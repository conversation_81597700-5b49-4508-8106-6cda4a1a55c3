<html xmlns:m="http://schemas.microsoft.com/office/2004/12/omml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Enter STO Fiber Trailer Receipt</title>
<style type="text/css">
.style11 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	background-color: #FFFFEA;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style12 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-weight: bold;
	background-color: #FFFFEA;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style28 {
	color: #9F112E;
}
.style29 {
	font-weight: bold;
	font-family: Arial, Helvetica, sans-serif;
}
.style30 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	text-align: right;
		font-family: Arial, Helvetica, sans-serif;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style31 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial, Helvetica, sans-serif;
		font-size: x-small;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style32 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-weight: bold;
		text-align: center;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style34 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style35 {
	font-weight: bold;
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style36 {
	border: 1px solid #000000;
	border-collapse: collapse;
	}
.style38 {
	font-weight: bold;
	font-family: Arial;
	font-size: x-small;
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style39 {
	font-size: x-small;
	font-family: Arial, Helvetica, sans-serif;
}
.auto-style1 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-weight: bold;
	text-align: center;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #EAF1FF;
}
.auto-style2 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #EAF1FF;
}
.auto-style3 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	text-align: right;
	font-family: Arial, Helvetica, sans-serif;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #EAF1FF;
}
.auto-style4 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #EAF1FF;
}
.auto-style5 {
	font-weight: bold;
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #EAF1FF;
}
.auto-style6 {
	font-weight: bold;
	font-family: Arial;
	font-size: x-small;
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #EAF1FF;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID, strLoad, MyConn, objMOC, rstFiber, strLocation, strSAP, rstTrailer , strTrailerWeight , strTractor   
    Dim strTrailer, strCarrier, strSQL3, MyConn3, strVID, strUOM, strPounds, strRail, strDate_shipped
    Dim strLoadNbr, strTrailerTID, strBales, stralert
    Dim strRECNbr
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strGenerator
    Dim strGenCity
    Dim strGenState, gAuditStatus
    Dim strOther, strR,  gSpecies, gPO, gSAP_Nbr, gVendor, gOID, strNet, gWeight_required, gTrailerStatus, strKCWeighed
    Dim MyRec5, strsql5, strCarID

    strLoad  = Trim(Request.QueryString("id")) 
    strpage = request.querystring("p")
	

 	set objGeneral = new ASP_CLS_General



if objGeneral.IsSubmit() Then

If request.form("Cancel") = "ON" then
Response.redirect ("SelectSTO.asp")
else
   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
	strLoad  = Trim(Request.QueryString("id")) 
 	strTrailer = Request.form("Trailer")
	strOther = Replace(Request.form("Other_Comments"), "'", "''") 
	strDateReceived = Request.form("Date_received")
	strSAPWeight = request.form("SAP_Weight")
	
	strCarrier = Request.form("Carrier")
	strLocation = Request.form("Location")

	strGenerator = Replace(Request.form("Generator"), "'", "''")
	
	if len(Request.form("Tons_Received")) > 1 then
	strPounds = Request.form("Tons_Received")
	strTonsReceived = round(strPounds/2000,3)
		strTrailerTID = 0
	
	else
	strTrailerTID = Request.form("Trailer_option")
		if strTrailerTID <> 0 then
   		 strSQL3 = "Select weight from tblTrailerOptions where TID = " & strTrailerTID

   			 Set MyConn3 = Server.CreateObject("ADODB.Recordset")
   			 MyConn3.Open strSQL3, Session("ConnectionString")
   	 			If not MyConn3.eof then
   	 			strTrailerWeight = MyConn3.fields("Weight")
   	 			strGrossWeight = Request.form("Gross")
   	 					if len(Request.form("Gross")) > 0 then 
   	 					strTonsReceived = round((Request.form("Gross") - strTrailerweight)/2000,3)
   	 					strPounds = round((Request.form("Gross") - strTrailerweight),3)
   	 	   	 				else
   	 					strTonsReceived = 0
   	 					end if

   		 		end if
   		 MyConn3.close
   
		end if
	end if

strNet = strTonsreceived
	

If isnull(strTrailer) or strTrailer = ""  or isnull(strCarrier) or strCarrier = ""  or len(strGenerator)<2 or isnull(strTonsReceived) or strTonsReceived = ""  then
	Session("Trailer") = strTrailer
	Session("Carrier") = strCarrier
	Session("Generator") = strGenerator
	Session("TrailerTID") = strTrailerTID
	Session("GrossWeight") = strGrossWeight
	Session("TonsReceived") = strTonsReceived
	Session("Pounds") = strPounds
	Session("SAPWeight") = strSAPWeight






	Response.redirect("EnterSTOReceipt.asp?id=" & strLoad & "&n=T")
	else
	strLoad = request.form("Load")
	
		strsql = "SELECT CID FROM tblCars WHERE STO_Number = " & strLoad & ""

   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
	If not MyRec.eof then
	strPOST = "NO"
	strPostCID = MyRec.fields("CID")
	else 
	strPOST = "OK"
	end if
	MyRec.close
	If strPOST = "OK" then

	

				Call SaveData() 
		If Request.form("Print_receipt") =  "ON" then
			
				Response.redirect("STO_Truck_receipt.asp?id=" & strCarID & "&p=" & strpounds)
				
		else
		Response.redirect ("SelectSTO.asp")
		end if
		

	else
	
	Response.redirect("EnterSTOReceipt.asp?id=" & strLoad & "&n=T1")
	end if	
		
	end if
else
Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to save this information.</font></br>")
MyRec.close
end if
end if
  
ELSE
   
	
	if len(strLoad) > 4 then
	strsql = "Select tblSTOInbound.* from tblSTOInbound where Load_nbr = " & strLoad & ""
		 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
  If not MyRec.eof then 
  strTrailer = MyRec.fields("Ship_From_State")
  strCarrier = MyRec.fields("Ship_From_City")
  strLoad = MyRec("Load_Nbr")
  strGenerator = MyRec("Ship_From_Name")
  strSAP = MyREc("SAP_nbr")
  strPO = MyRec("PO")
  strBOL = MyREc("BOL")
  end if
  MyRec.close
  else
  strSAP = request.querystring("s")
  end if
  strsql = "SELECT * from tblBrokeSap where SAP = '" & strSAP & "'"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not myrec.eof then
    strSpecies = MyREc("Type")
    end if
    MyRec.close

	
	strDateReceived = formatdatetime(Now(),2)
	
   	 
   	 

end if
%>



<body>


<% if Request.querystring("n") = "T" or request.querystring("n") = "T1" then 
strTrailer = Session("Trailer")
strCarrier = Session("Carrier")
strTrailerTID = Session("TrailerTID")
strGrossWeight = Session("GrossWeight")

strGenerator = Session("Generator")
strTonsReceived = Session("TonsReceived")
strPounds = formatnumber(Session("Pounds"),0)

 %>
<p align = center><font face = arial size = 4 color="teal"><b>

<font face = arial size = 3 color = red>
<span class="style28">

<% if isnull(Session("Trailer")) or len(Session("Trailer"))< 1 then %>
You must enter a Trailer number.<br>
<% end if %>
<% if strCarrier = "" then %>
You must enter a Carrier<br>
<% end if %>
<% if len(strGenerator) < 2 then %>
You must enter the KC Site Load is Shipped From<br>
<% end if %>
<% if len(strSapWeight) < 2 then %>
You must enter the Weight from the Shipping Ticket<br>
<% end if %>


<% if request.querystring("n") = "T1" then
	Response.write("<br><font face=arial size=3 color=red><br>Receipt Number " & strPostCID & " already has this load number.  It could be you clicked the Submit button twice.</font><br>")
	end if
	
end if %><br>
<table width = 100%><tr><td width = 33% style="height: 24px">
<font face="Arial" size="2"><b>Species: <%= strSpecies%>&nbsp;&nbsp; 

</td><td align = center style="height: 24px; width: 50%;"><b><font face="Arial" size="4">
Enter STO Fiber Trailer Receipt</font> </b></td>
	<td align = right width = 33% style="height: 24px"><b><font face = arial size = 2><a href="SelectSTO.asp">RETURN</a></font></b></td></tr></table>




<form name="form1" action="EnterSTOReceipt.asp?id=<%=strLoad%>&p=<%= strpage %>" method="post">
<input type="hidden" name="Species" value="<%= strSpecies %>">
<input type="hidden" name="SAP_Load" value="<%= strSap %>">
<input type="hidden" name="PO" value="<%= strPO %>">
<input type="hidden" name="BOL" value="<%= strBOL %>">
<div align="center">
<table cellspacing="0" bgcolor="#FFFFEA" style="width: 85%;" cellpadding="0" bordercolorlight="#C0C0C0" class="style36">
  <tr>
    <td  align = right style="height: 42px; width: 25%;" class="auto-style5" >
   <span class="style39">Material (SAP #)</span>:&nbsp; </td>
<td  align = left colspan="2" style="height: 42px" class="auto-style4">

		<font face="Arial" size="2"> <% if request.querystring("p") = "u" then %>
		<input type="text" name="SAP" value="" style="width: 135px">
		<% else %>
	<select name="SAP" style="font-weight: 700; width: 132px;" size="1" tabindex="1">
 	<option selected value="">  Select</option>
      <% strsql = "Select tblBrokeSAP.* from tblBrokeSAP where category = 'FIBER'"
      
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    while not MyRec.eof

       %>
        <option <% if strSAP = MyRec("SAP") then %> selected <% end if %> ><%= MyRec("SAP") %></option>
        <% MyRec.movenext
        wend
        MyRec.close %>
  
     </select><% end if %>&nbsp; (Required)</font></td>
<td  align = left width="29%" style="height: 42px" class="auto-style5"> 

<font face = arial size = 3 color = red>
<span class="style28">

<font face="Arial"><font size="2">Check to Print Receipt:</font><input type="checkbox" name="Print_receipt" value="ON" checked></td></tr>
  <tr>
    <td  align = right style="height: 32px; width: 25%;" class="auto-style5" >
   <font face="Arial" size="2">Trailer:&nbsp;</font></td>
<td  align = left colspan="3" style="height: 32px" class="auto-style4">

      <font face="Arial">

      <input name="Trailer" size="15" value = "<%= strTrailer%>" style="font-weight: 700" tabindex="1"><b>
		</b>&nbsp;&nbsp;</font></td>
	</tr>
  <tr>

      <td align = right style="height: 33px; width: 25%;" class="auto-style5">
	<font face="Arial" size="2">Select Carrier:</font></td>
<td  align = left colspan="3" style="height: 33px" class="auto-style4">

      <font face="Arial">   
		<select name="Carrier" style="font-weight: 700">
 	<option value="" selected>  Select Carrier (Required)</option>
 	<% strsql = "Select Carrier,C_Description, Weight from tblCarrier order by Carrier"
 	   	 Set MyRec4 = Server.CreateObject("ADODB.Recordset")
   	 MyRec4.Open strSQL, Session("ConnectionString")
   	 while not MyRec4.eof %>
   	 <option <% if strCarrier = MyRec4("Carrier") then %> selected <% end if %> value="<%= MyRec4("Carrier") %>"><%= MyRec4("Carrier") %>&nbsp;<%= MyRec4("C_Description") %>&nbsp;<%= MyRec4("Weight") %></option>"

 <%MyRec4.Movenext
 wend
 MyRec4.close %>
     </select></font><font face="Arial" size="2"><b>    

      &nbsp;</td>
</tr>



        <tr>  
			<td align = right class="auto-style4" style="height: 40px; width: 25%;"> <b>
			<font face="Arial" size="2">Combined Trailer/Tractor</font></b><font face="Arial" size="2"><b> Weight:&nbsp;</b></font></td>
<td align = left colspan="3" class="auto-style4" style="height: 40px">    <font face="Arial"> 
<select name="Trailer_option" style="font-weight: 700" size="1" tabindex="3">
 	<option selected value=0>  Select Trailer</option>
 	<% strsql2 = "SELECT  Trailer_Description, Audit_Weight, Trailer_Description + '-' + str([Audit_weight]) as toption, TID FROM tblTrailerOptions ORDER BY Trailer_description"
 	 	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL2, Session("ConnectionString")
while not MyRec.eof
%>
<option value=<%= MyRec("TID") %>><%= MyRec("Toption") %> </option>
 	
  <% MyRec.movenext
  wend
  MyRec.close %> 
     </select></td>
</tr>


<tr>
	<td class="auto-style5" style="height: 38px; width: 25%;">
	<p align="right"><font face="Arial" size="2">Gross Weight:</font></td>
    <td width="21%" class="auto-style4" style="height: 38px">    <font face="Arial">  
	<input name="Gross" size="15" value="<%= strGrossWeight%>" tabindex="4"></td>
    <td class="auto-style5" style="height: 38px">    
	<p align="right"> <font face="Arial" size="2">OR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	Pounds Received:</font></td>
    <td width="29%" class="auto-style4" style="height: 38px">    <font face="Arial">  
	<input name="Tons_Received" size="15" value = "<%= strPounds%>" style="float: left" tabindex="5"></td>
  </tr>

 

       <tr>
		<td  align = right style="height: 34px; width: 25%;" class="auto-style5" >
		<span class="style39">Shipping Ticket Weight</span>:&nbsp; </td>
<td align = left colspan="3" style="height: 34px" class="auto-style5"> 

<font face = arial size = 3 color = red>
<span class="style28">

<font face="Arial">  
	<input name="SAP_Weight" size="15" value="<%= strSAPWeight%>" tabindex="6"></td>
</tr>

       <tr>
		<td  align = right style="height: 14px; width: 25%;" class="auto-style6" >
		S<font size="2">TO Delivery Number: </font></td>
<td align = left colspan="3" style="height: 14px" class="auto-style5"> 

<font face = arial size = 3 color = red>
<span class="style28">

<font face="Arial">  
	<input name="Load" size="15" value="<%= strLoad %>" tabindex="6"></td>
</tr>


       <tr>
		<td  align = right style="height: 34px; width: 25%;" class="auto-style5" ><font face="Arial" size="2">Date Received:&nbsp;</font></td>
<td align = left colspan="3" style="height: 34px" class="auto-style5"> 

<font face = arial size = 3 color = red>
<span class="style28">

<font face="Arial"> 
<input name="Date_Received" size="15" value = "<%= strDateReceived%>" style="font-weight: 700" tabindex="7"></font></td>
</tr>
              <tr>
          <td  align = right style="width: 25%;" class="auto-style5" >
   <font face="Arial" size="2">Site Shipped From:&nbsp;</font></td>
<td align = left colspan="3" class="auto-style4">
      <font face="Arial">
      <input name="Generator" size="25" value = "<%= strGenerator %>" style="font-weight: 700" tabindex="8"></font></TD></tr>
         <tr>
          <td  align = right style="height: 37px; width: 25%;" class="auto-style5" >
  <font face="Arial" size="2">Comments:&nbsp;</font></td >
   <td align = left colspan="3" style="height: 37px" class="auto-style4">   <font face="Arial">   
	<input name="Other_Comments" size="25" value = "<%= strOther%>" style="font-weight: 700; width: 364px;" tabindex="9">&nbsp;&nbsp;&nbsp;</td></tr>
<tr>
    <td class="auto-style3" style="height: 26px; width: 25%;">
	<font face = arial size = 3>
	<span class="style29">

	<font size="2">Location:&nbsp;</font></td>

    <td colspan="3" class="auto-style2" style="height: 26px">YARD</td>
  </tr>

    
  <tr>
    <td width="17%" colspan="4" class="auto-style1">

<font face = arial size = 3 color = red>
<span class="style28">

	<font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></td>

  </tr>

    </table>

</div>

</form>

<%  

Function SaveData()



	
	Dim strRightNow, strAud
	strRightnow = Now()
	
	
   	
if len(request.form("Tons_Received")) > 0 then
		
		strPounds = Request.form("Tons_received")
	 if  strPounds > 0 then
		if strPounds = 42000 then
		strKCWeighed = "Yes"
		else
		strKCWeighed = ""
		end if 
	strTonsReceived = round(strPounds/2000,3)
	strTrailerTID = 0
	strTrailerweight = 0
	strGrossWeight = 0
	strTareWeight = 0
    strNet = strTonsReceived
	end if 
else
	
	strTrailerTID = request.form("Trailer_option")
   	 strSQL3 = "Select audit_weight from tblTrailerOptions where TID = " & strTrailerTID

   	 Set MyConn3 = Server.CreateObject("ADODB.Recordset")
   	 MyConn3.Open strSQL3, Session("ConnectionString")
   	 
   	 strTrailerWeight = MyConn3.fields("Audit_Weight")
   	 
   	 MyConn3.close
   	 
	strTonsReceived = round((Request.form("Gross") - strTrailerweight)/2000,3)
	strPounds = round((Request.form("Gross") - strTrailerweight),3)
		strTareWeight =  strTrailerweight
	strGrossWeight = Request.form("Gross")
	strNet = strTonsReceived
end if
	

	
	
	
	If strTrailerweight > 100 then
	strTrailerweight = (strTrailerweight - 18780) ' subtract off the weight of the conventional tractor
	end if 
	
	strSAPWeight = REquest.form("SAP_Weight")
 
	strSpecies = request.form("Species")
	If request.querystring("p") = "u" then
	strSpecies = "UNRESOLVED"
	end if

	strSapWeight = request.form("Sap_Weight")
	
	if len(strSAPWeight) > 0 then
	strTonsReceived = strSapWeight
	end if
	if strTonsReceived > 100 then
	strTonsReceived = round(strTonsReceived / 2000,3)
	end if
	
		strLoad = request.form("Load")
	if len(strLoad) > 0 then
	'do nothing
	else
	strLoad = 0
	end if

	
	strsql =  "INSERT INTO tblCars (  Trailer_weight, Trailer_TID, Tractor_weight,  Carrier, Species, Grade, SAP_Nbr, Vendor, "_
	&" Date_received, STO_Number, Location_unloaded, Location,  Generator,  Trailer, Other_Comments, Tons_Received, Net, Tons, Gross_weight, "_
	&" Tare_weight,   Entry_Time, Entry_BID, Entry_Page, Status, PO, BOL) "_
	&" SELECT   " & strTrailerWeight & ", " & strTrailerTID & ", 18780,  '" & strCarrier & "', '" & strSpecies & "', "_
	&" 'RF', '" & Request.form("SAP") & "', '" & strGenerator & "',  '" & strDateReceived & "', '" & strLoad & "', 'RF', "_
	&" 'YARD',  '" & strGenerator & "',   "_
	&" '" & strTrailer & "', '" & strOther & "',  " & strSAPWEight & ", " & strSAPWEight & ", " & strTonsReceived & ", "_
	&"  " & strGrossWeight & ", " & strTareWeight & ",    '" & strRightNow & "', '" & Session("EmployeeID") & "', "_
	&" 'EnterSTOReceipt', '" & strSAPWEight & "', '" & request.form("PO") & "', '" & request.form("BOL") & "'"
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
			strsql5 = "SELECT Max(tblCars.CID) AS MaxOfCID FROM tblCars WHERE tblCars.Trailer = '" & strTrailer & "'"
			
   	 Set MyRec5 = Server.CreateObject("ADODB.Recordset")
   	 MyRec5.Open strSQL5, Session("ConnectionString")
   	 strCarID = MyRec5.fields("MaxofCID")
   	 MyRec5.close

if request.querystring("p") = "u" then		
	strEmailTo = "<EMAIL>, <EMAIL>, <EMAIL>"
        	
            strEBCC = "<EMAIL>"
                 
			Set objMail = Server.CreateObject("CDO.Message")		
			objMail.From = "<EMAIL>"
			objMail.To = strEBCC
		
			
			objMail.Subject = "STO Fiber Truck Receipt for Trailer#: " & strTrailer & " Entered without Species "		
			
		
			objMail.HTMLBody = "<font face = arial size = 2>Truck # " & strTrailer & " was entered without Species. "
			' objMail.Send
			Set objMail = nothing

			
end if



End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->