<%Response.Buffer = False%>																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Edit Records</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->




<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate, strsql3

strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -60, strtdate)
If Session("EmployeeID") = "F00390" then
   Session("EmployeeID") = "C97338"
End If

    strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "' and User_type = 'A'"


    Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL3, Session("ConnectionString")


If not Myrec2.eof then


strsql = "SELECT tblCars.* FROM tblCars WHERE Date_received > '" & strdate & "' and (NFID = 0 or NFID is null ) "


If len(request.form("Release")) >3 then

strsql = strsql & " and Release_nbr = '" & request.form("Release") & "'"
end if

strsql = strsql & " order by CID desc"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<style type="text/css">
.style1 {
	text-align: center;
}
.style2 {
	font-size: x-small;
}
.style3 {
	font-size: x-small;
	font-weight: bold;
}
.auto-style1 {
	text-align: left;
}
</style>
</head>

<body>
<br>
<form name="form1" action="Sixty_Day_list.asp" method="post">
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD align = left><font size="2" face = arial>Release #: </font><input type="text" name="Release" value=""></td>
<td class="auto-style1"><font face="Arial"><b>Loads Received in Last Sixty Days</b></font></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br>
</form>
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1>  
	 <tr class="tableheader">
<td>&nbsp;</td>
	<td>  <font face="Arial" size="1">Print<br>Receipt</td>
		<td  > <p align="center">       <font face="Arial" size="1">Release<br> Number</font></td>
	
		<td  ><font face="Arial" size="1"><b>Trailer</b></font></td>
				<td  ><font face="Arial" size="1"><b>Transfer<br>Trailer</b></font></td>
		<td  ><font face="Arial" size="1"><b>Carrier</b></font></td>
		<td  ><font face="Arial" size="1">Species</font></td>
    
		<td  ><font face="Arial" size="1">Vendor</font></td>
		<td  >       <font face="Arial" size="1">PO Number</font></td>
	<td>       <font face="Arial" size="1">REC Number</font></td>

		<td  >       <p align="center">       <font face="Arial" size="1">Gross<br> Weight</font></td>
		<td  >        <p align="center">        <font face="Arial" size="1">Tare<br> Weight</font></td>
		<td  >        <p align="center">        <font face="Arial" size="1">Tons<br> Received</font></td>
	
		<td  >        <p align="center">        <font face="Arial" size="1">RF Bales</font></td>
		
	<td  >        <p align="center">        <font face="Arial" size="1">VF Bales</font></td>
		<td>        <font face="Arial" size="1">Date<br> Received</font></td>
		<td  >        <font face="Arial" size="1">Generator</font></td>
		<td  >        <font face="Arial" size="1">Generator<br> City</font></td>
		<td  >        <font face="Arial" size="1">Gen<br> State</font></td>
			<td class="style1"  >        <font face="Arial" size="1">Location</font></td>
		<td  >        <font size="1" face="Arial">Other</font></td>
	<td  >        <font size="1" face="Arial">Delete<br>Receipt</font></td>
<td><font size="1" face="arial">Rejected</font></font></td>
	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td> <font size="1" face="Arial"><a href="TruckReceiptYardEdit_sys.asp?id=<%= MyRec.fields("CID") %>">Edit</a></td>
<% if MyRec.fields("Trailer") = "UNKNOWN" Then %>
<td align = center class="style2"> <font face="Arial"><a href="Transfer_receipt.asp?id=<%= MyRec.fields("CID") %>"><%= MyRec.fields("CID")%></a></td>
	<% else %>
	
	<% if MyRec("Entry_Page") = "EnterSTOWaddingReceipt" then %>
	<td align = center class="style2"> <font face="Arial"><a href="STO_Wadding_Truck_receipt.asp?id=<%= MyRec.fields("CID") %>"><%= MyRec.fields("CID")%></a></td>
	<% elseif MyRec("Entry_Page") = "STO_Broke_Receipt" or MyRec("Entry_Page") = "EnterSTOReceipt" or MyRec("Entry_Page") = "STO_Receipt" then %>
	<td align = center class="style2"> <font face="Arial"><a href="STO_Truck_Receipt.asp?id=<%= MyRec.fields("CID") %>"><%= MyRec.fields("CID")%></a></td>
	
	<% else %>
	<td align = center class="style2"> <font face="Arial"><a href="Truck_receipt_print.asp?id=<%= MyRec.fields("CID") %>"><%= MyRec.fields("CID")%></a></td>
	<% end if %>
	
	<% end if %>
	<td class="style2"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Release_Nbr")%></span>&nbsp;</font></td>

	<td class="style3"  > <font face="Arial"><%= MyRec.fields("Trailer")%></font></td>
	
	<td  > <b> <font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Transfer_Trailer_Nbr")%></span>&nbsp;</font></b></td>
			<td  ><b><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Carrier")%></span>&nbsp;</font></b></td>


		<td class="style2"  ><font face="Arial"> <%= MyRec.fields("Species")%></font></td>
		<td><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Vendor")%></span>&nbsp;</font></td>
		<td  >        <font size="1" face="Arial">        <span class="style2">        <%= MyRec.fields("PO")%></span>&nbsp;</font></td>
		<% if isnull(MyRec.fields("REC_Number")) then %>
			<td class="style2"  >  <font face="Arial">        <%= MyRec.fields("CID")%></font></td>
			<% else %>
	<td class="style2"  >  <font face="Arial">        <%= MyRec.fields("REC_Number")%></font></td>
	<% end if %>
	
		<td align = right class="style2" >		 <font size="1" face="Arial">        
		<span class="style2">        <%= MyRec.fields("Gross_Weight")%></span>&nbsp;</font></td>
		<td align = right >		 <font size="1" face="Arial">        
		<span class="style2">        <%= MyRec.fields("Tare_Weight")%></span>&nbsp;</font></td>
		<td align = right <% if MyRec.fields("Tons_received") = 21  or MyRec.fields("Weigh_required") = "W" then %>bgcolor = pink <% end if %>  >
				 <font size="1" face="Arial" class="style2">        <%= MyRec.fields("Tons_received")%>&nbsp;<%= MyRec.fields("Weigh_required") %></font></td>
				 <td  ><font size="1" face="Arial" class="style2">&nbsp;<%= MyRec.fields("Bales_RF")%></font></td>

<td  >		 <font size="1" face="Arial">        <span class="style2">        <%= MyRec.fields("Bales_VF")%></span>&nbsp;</font></td>

		<td class="style2"  >		 <font face="Arial">        <%= MyRec.fields("Date_Received")%></font></td>
       <td  >		 <font size="1" face="Arial">        <span class="style2">        <%= MyRec.fields("Generator")%></span>&nbsp;</font></td>
		<td  >	 <font size="1" face="Arial">        <span class="style2">        <%= MyRec.fields("Gen_City")%></span>&nbsp;</font></td>
		<td  > <font size="1" face="Arial"> <span class="style2"> <%= MyRec.fields("Gen_State")%></span>&nbsp;</font></td>
		<td align="center"  > <font size="1" face="Arial"><span class="style2"> <a href="Change_location.asp?id=<%= MyRec("CID") %>"> <%= MyRec.fields("Location")%></a>&nbsp;</font></td>

		<td  >	 <font size="1" face="Arial">        <%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>
<td align = center >	 <font size="1" face="Arial"><a href="Maint_ReceiptDelete.asp?id=<%= Myrec.fields("CID")%>"> Delete</a></font></td>
<% 
if MyRec.fields("Rejected") = "YES" then %>
<td align = center >	 <font size="1" face="Arial">
<a href="Rejected_load.asp?id=<%= Myrec.fields("CID")%>">YES</a></font></td>
<% else %>
<td align = center >	 <font size="1" face="Arial">

<a href="Rejected_load.asp?id=<%= Myrec.fields("CID")%>">NO</a></font></td>
<% end if %>
	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>

<% Myrec2.close 
end if %><!--#include file="Fiberfooter.inc"-->