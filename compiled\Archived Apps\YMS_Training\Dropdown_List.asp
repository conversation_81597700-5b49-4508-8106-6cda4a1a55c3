<HTML>
<HEAD>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">
<TITLE>Dropdown List</TITLE>
</HEAD>
<!--#include file="classes/asp_cls_headerOYM.asp"-->
<!--#include file="classes/asp_cls_SessionStringOYM.asp"-->
 

<!--#include file="classes/asp_cls_DataAccessOYM.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureOYM.asp"-->
   
<body>
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 bgcolor="#FFFFDF">
  <tr><td colspan="4" bgcolor="white" class="subheader">&nbsp;</td></tr>

 <tr><TD><p align="left"><font size="3" face="Arial"><b>Drop Down List</b>&nbsp;&nbsp;</font></td>
</tr>
	    </table><br>
	    <%   
	      Dim SCRIPT_NAME
		Dim strSQL
		Dim BACK_TO_LIST_TEXT
            Dim objGeneral
		Dim objMOC, rstMill, strMill, strPD, strDescripion
 
            
		SCRIPT_NAME = Request.ServerVariables("SCRIPT_NAME")

		BACK_TO_LIST_TEXT = "<p>Click <a href=""" & SCRIPT_NAME & """>" _
    		    & "here</a> to go back to record list.</p>"
	      set objGeneral = new ASP_CLS_General
 	      Call GetData()
	    %>

	    <%
		Select Case LCase(Trim(Request.QueryString("action")))
		    Case "add"
		    %>
			<form action="Dropdown_list.asp?action=addsave" method="post">
	
		<table >
		<tr><td>  <font face = arial><b>Description: &nbsp;&nbsp; </b> </td>
		<td>  	<Input name="Description" size="40"  ></td></tr>
		<tr><td>  <font face = arial><b>Category: </b></td>
		<td><Input name="Category" size="34" ></td></tr>
			    	   
<tr><td colspan = 2>&nbsp;</td></tr>
<tr><td colspan = 2><Input name="Update" type="submit" Value="Update" ></td></tr>
</table>
				</form><br>
                    <%
	            Response.Write(BACK_TO_LIST_TEXT)

 		    Case "addsave"
			
			strSQL = "Insert into tblDropdown (PD_type, Description) Select '" & Request.Form("Category") & "', '" & Request.Form("Description") & "'"
		
		
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

			Response.redirect "Dropdown_list.asp"

		    Case "edit"
			iRecordId = Request.QueryString("id")
			strSQL = "Select * From tblDropdown Where ID = " & iRecordId

			Set MyRec = Server.CreateObject("ADODB.RecordSet")
			MyRec.Open strSQL, Session("ConnectionString")

			If Not MyRec.EOF Then
			    %>
				<form action="Dropdown_list.asp?action=editsave" method="post">
		
				   <input type="hidden" name="id" value="<%= MyRec.Fields("ID").Value %>" />
		<table >
		<tr><td>  <font face = arial><b>Description: &nbsp;&nbsp; </b> </td>
		<td>  	<Input name="Description" size="40" value = "<%= MyRec.Fields("Description") %>" ></td></tr>
		<tr><td>  <font face = arial><b>Category: </b></td>
		<td><Input name="Category" size="34" value = "<%= MyRec.Fields("PD_type") %>" ></td></tr>
			    	   
<tr><td colspan = 2>&nbsp;</td></tr>
<tr><td colspan = 2><Input name="Update" type="submit" Value="Update" ></td></tr>
</table>
				</form><br>
   			<% Response.Write(BACK_TO_LIST_TEXT)
			   
			End If

		    Case "editsave"
			iRecordId = Clng(Request.form("id"))
			strSQL = "Update tblDropdown Set Description = '" & Replace(Request.form("Description"), "'", "''")  & "', PD_type = '" & Replace(Request.form("Category"), "'", "''")  & "' Where ID = " & iRecordId

			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

			Response.redirect "Dropdown_list.asp"

		    Case "delete"
	        ' Get the id to delete
			iRecordId = Clng(Request.QueryString("id"))
			

			strSQL = "DELETE FROM tblDropdown WHERE ID = " & iRecordId 

			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

			Response.redirect "Dropdown_list.asp"


		    Case else   'Default view
		
			strSQL = "SELECT * from tblDropdown order by PD_Type, Description"
	     	

 			Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
			%>	<table><tr>  <td colspan="6" align="right"><font face="Arial"><a href="<%= SCRIPT_NAME %>?action=add">Add a new record</a></font></td>
				</tr></table>
				<table Border=1 bgcolor = #F0F0F0 BORDERCOLOR = #CCCCFF cellpadding = 3 width = 100%>
			<thead>
			    <tr>
   		
			      <th>
					<p align="left"><font face="arial" size="2">Description</font></th>
					      <th>
					<p align="left"><font face="arial" size="2">Added Description</font></th>
 					</font> 

    			 <font face="Arial" size="2">
 				<th> 
				<p align="left"><font face="Arial" size="2">Category</font></th>
                	
                		</font> 

    			 <font face="Arial">
                	
                		<th><font size = 2 font face = arial> Edit </th>
                       <th><font size = 2 font face = arial>  Delete </th>
              	    </tr>
			    </thead>
			    <tbody>
	
        		        <% While not MyRec.EOF %>
              			    <tr>	
				<td align = "left"><font size = 2 face = Arial><% = MyRec.fields("Description") %>&nbsp;</td>
				<td align = "left"><font size = 2 face = Arial><% = MyRec.fields("Added_Description") %>&nbsp;</td>
                		<td align = "left"><font size = 2 face = Arial><% = MyRec.fields("PD_type") %>&nbsp;</td>	
				<td align="center"><font face="Arial" size="2"><a href="<%= SCRIPT_NAME %>?action=edit&id=<%= MyRec.Fields("ID").Value %>">Edit</a></font></td>
              			<td align="center"><font face="Arial" size="2"><a href="<%= SCRIPT_NAME %>?action=delete&id=<%= MyRec.Fields("ID").Value %>">Delete</a></font></td>
 			        </tr>
                            
              	    <% 
				      MyRec.MoveNext
                 			WEND
                 			MyRec.Close
              	    %>

			    </tbody> </table>
		<%					
			End Select
			
   		 Function GetData()
       	' set objMOC = new ASP_CLS_MOC
       	         
	' set rstMill = objMOC.ReadMillList()
   		 End Function   %>
                             
      	</body>


<!--#include file="OYMfooter.inc"-->