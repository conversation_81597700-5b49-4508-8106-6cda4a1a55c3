																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Change Location</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->




<% Dim MyRec, strsql, strid, strLocation
  Dim strdate
  
strid = request.querystring("id")

strsql = "Select * from tblCars where CID = " & strid
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
strLocation = MyRec("Location")
strRelease = MyRec.fields("Release_Nbr")
strCID = MyRec.fields("CID")
strDate = MyRec.fields("Date_received")
strTrailer = MyRec.fields("Trailer")
strTTrailer = MyRec.fields("Transfer_Trailer_Nbr")
MyRec.close


  set objGeneral = new ASP_CLS_General
  if objGeneral.IsSubmit() Then	

strNow = formatdatetime(Now(),0)

  strLocation = request.form("Location")
  	
  
        	
  	
	strsql =  "Update tblCars set Location = '" & strLocation & "' where CID = " & strid

	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
			If strLocation = "YARD" then
			strsql =  "Update tblCars set Date_unloaded = Null, Inventory_Depletion_Date = null, Location_unloaded = Null where CID = " & strid
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close			
			end if
			
	    strsql = "INSERT INTO tblMovement ( CID, DDate, Tdate, From_location, To_location, BID, Comment ) "_
&" SELECT " & strid & ", '" & request.form("Date_received") & "', '" & strnow & "', '" & request.form("Original_Location") & "', "_
&" '" & strlocation & "', '" & Session("EmployeeID") & "', 'Change Location'"
  	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
Response.redirect("Sixty_day list.asp")		
end if
	
%>

<style type="text/css">
.style2 {
	font-family: arial;
	font-size: x-small;
}
.style4 {
	border-style: solid;
	border-width: 1px;
}
.style5 {
	font-family: arial;
	font-size: x-small;
	background-color: #FFFFD7;
}
.style6 {
	font-family: Arial, Helvetica, sans-serif;
}
</style>
</head>

<body>
<br>
	
<form name="form1" action="Change_Location.asp?id=<%= strid%>" method="post" ID="Form1">
<input type="hidden" name="Original_Location" value="<%= strLocation %>">
<input type="hidden" name="Date_received" value="<%= strDate %>">
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial">Change Location</font></td>
<td align = right><font face="Arial"><a href="Sixty_Day list.asp">Return</a>&nbsp;</td>


</tr>
	    </table>



	<p>&nbsp;</p>
	<table id="table1" style="width: 35%" class="style4" align="center">
		<tr bgcolor = #CCCCFF><td bgcolor="#FFFFD7" class="style5">Receipt ID</td>
		<td bgcolor="#FFFFD7" class="style5">Trailer</td>
			<td bgcolor="#FFFFD7" class="style5">Date received</td>

		<td bgcolor="#FFFFD7"><p align="center" class="style2">Release #</td>
			<td bgcolor="#FFFFD7"><p align="center" class="style2">Location</td>
	</tr>
	<tr>
		<td><span class="style6"><%= strCID %></span>&nbsp;</td>
		<td><span class="style6"><% if strTrailer = "UNKNOWN" Then %></span>
		<span class="style6">
		<%= strTTTrailer %></span> <span class="style6">
		<% else %></span> <span class="style6">
		
		<%= strTrailer %></span> <span class="style6">
		<% end if %></span>&nbsp;</td>
			<td class="style6"><%= strDate %></td>

		<td>
		<p align="center" class="style6"><%= strRelease %></td>
		<td>
		<p align="center"><font face = arial size = 1>
		<select name="Location">
		<% 
		
		strsql = "Select distinct location from tblCars where CID > 85490"
		 Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
   while not MyRec.eof %>
   <option <% if strLocation = MyRec("Location") then %> selected <% end if %>><%= MyRec("Location") %></option>
<% MyRec.movenext
wend
MyRec.close %>
		
		</select>
	</td>

	</tr>
</table>


<p align="center"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></p>
</form>
<!--#include file="Fiberfooter.inc"-->