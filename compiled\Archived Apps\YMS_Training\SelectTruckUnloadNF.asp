<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">


<TITLE>Move Non-Recovered Paper Trailer</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

 <%    Dim objDAC, strSQL, MyRec, objMOC, rstFiber, strCID, rstFiberTrans, rstNF, strNFCID

       Dim objGeneral, strDate, MyConn, strTCID

	strCID = ""

	
       set objGeneral = new ASP_CLS_General
  Call getData()   
if objGeneral.IsSubmit() Then


strNFID = Request.form("NFID")



	Response.redirect("Out_NF_Trailer.asp?id=" & strNFID)


end if


%>
<SCRIPT LANGUAGE="JavaScript">
function formHandler(Form1){
document.forms["Form1"].submit();
}
</SCRIPT>

<body>
<form name="form1" action="SelectTruckUnloadNF.asp" method="post" >

<p align="center"><font face="Arial"><b>CAR-OUT TRAILER&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</b></font></p>
<table border="1" cellpadding="0" class = "tablecolor1" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="60%" id="AutoNumber2" align = center>

       
<Td bgcolor = "#FFFFE8">
		<p align = right><a href="At_door.asp" target = "blank"><b>Trailers At Door</a>&nbsp;&nbsp;&nbsp;&nbsp;</b></p>
		<p align="center"> <font face="Arial" size="3"><b>&nbsp;&nbsp;Move 
		Non-Fiber Trailer:&nbsp;&nbsp;<br></b></font>
	&nbsp;&nbsp;&nbsp;<br>
     <font face="Arial">
     <select name="NFID">
 	<option value="" selected>Trailer Number</option>
 	<% strsql = "SELECT tblCars.CID, tblCars.Trailer, Location, Transfer_Trailer_nbr,  Trailer + ' - ' + Location as Tname FROM tblCars "_
&" WHERE (location = 'YARD' or location = 'AT DOOR') AND (Grade='NF' or Grade = 'VF' OR Grade = 'WADDING' or Species = 'WADDING' ) ORDER BY tblCars.Trailer"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
	MyRec.Open strSQL, Session("ConnectionString")
while not MyRec.eof
if MyRec("Trailer") = "UNKNOWN" then  %>
<option value="<%= MyRec("CID") %>"><%= MyRec("Transfer_trailer_nbr") %> - <%= MyRec("Location") %></option>

<% else %>
<option value="<%= MyRec("CID") %>"><%= MyRec("Tname") %></option>
<% end if %>
<% MyRec.movenext
wend
MyRec.close %>



     </select><font size="2"> </font></p>
		<p>&nbsp;</p> 
<p align="center"><input type="submit" value="Continue" id=submit1 name=submit1><br><br><br>
<br><br>   </TD></tr>

</table>
</form>


<%

 Function GetData()
        set objMOC = new ASP_CLS_FIBER

        set rstNF = objMOC.NFTrailers()
     

    End Function 
    

 %><!--#include file="Fiberfooter.inc"-->