<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 14.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Enter Broke Trailer Receipt</title>
<style type="text/css">
.style1 {
	font-size: x-small;
}
.style25 {
	border-style: solid;
	font-weight: bold;
	border-width: 1px;
	background-color: #FDE3E8;
}
.style26 {
	border-style: solid;
	border-width: 1px;
	background-color: #FDE3E8;
}
.style28 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
.style29 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.style30 {
	border-style: solid;
	border-width: 1px;
}
.style31 {
	font-weight: bold;
	border-style: solid;
	border-width: 1px;
}
.style32 {
	font-family: Arial;
	font-size: x-small;
}
.style33 {
	font-family: Arial, Helvetica, sans-serif;
}
.style34 {
	border-style: solid;
	border-width: 1px;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.style35 {
	border-style: solid;
	border-width: 1px;
	font-size: x-small;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID,  MyConn, objMOC, rstFiber, rstSpecies, strKCWeighed, strPounds  
    Dim strTrailer, strCarrier, strLocation, MyRec5, strsql5, stralert
    Dim rstTrailer , strTrailerWeight , strTractor, strTrailerTID, strSAP, strerror, strType, strPO, strBOL
 
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strOther, strsql3, strSpecies, strNet,  strR, objGeneral, strLoadID

strid = request.querystring("id")

strsql = "Select tblCars.* from tblCars where CID = " & strid
   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
   	 strSAP = MyRec("SAP_Nbr")
   	 strType = MyRec("Broke_Description")
   	 strTrailerWeight = MyRec("Trailer_Weight")
   	 strTrailerTID = MyRec("Trailer_TID")
   	 strCarrier = MyRec("Carrier")
   	 strSpecies = MyRec("Species")
   	 strGrade = MyRec("Grade")
   	 strTrailer = MyRec("Trailer")
   	 strOther = MyRec("Other_Comments")
   	 strTonsReceived = MyRec("Tons")
   	 strGrossWeight = MyRec("Gross_Weight")
   	 strTareWeight = MyRec("Tare_Weight")
   	 strLoad = MyRec("Sto_Number")
   	 strSAPWeight = MyRec("Net")
   	 strPounds = strTonsReceived * 2000
   	 strPO = MyRec("PO")
   	 strBOL = MyRec("BOL")
   	 MyRec.close
	
  call getdata()
  set objGeneral = new ASP_CLS_General
if objGeneral.IsSubmit() Then

   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
	
	

	strTrailer = Request.form("Trailer")
	strOther = Replace(Request.form("Other_Comments"), "'", "''") 
	strDateReceived = Request.form("Date_received")
	strGrossweight = Request.form("Gross_Weight")
	strPounds = Request.form("Tons_Received")
	strCarrier = Request.form("Carrier")
	strLocation = "YARD"
	strTrailerTID = request.form("Trailer_option")
	strSAP = request.form("SAP")
	


	Call SaveData() 

	
	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to save this information.</font></br>")
	MyRec.close
	end if ' if you don't have authorization
    
   
end if ' if they did not submit
%>

<% if Request.querystring("n") = "T" then 
strTrailer = Session("Trailer")
strCarrier = Session("Carrier")
strTrailerTID = Session("TrailerTID")
strGrossWeight = Session("GrossWeight")

strPounds = Session("Pounds")
strOther = Session("Other")
strSAP = Session("SAP")
end if
 %>


<body>
<table width = 100%><tr><td width = 33%>
&nbsp;</td><td align = center width = 34%><font face="Arial" size="4">
		<b>Edit STO&nbsp; Trailer Receipt </b></font> </b></td></tr></table>




<form name="form1" action="STO_Edit.asp?id=<%=strid%>" method="post">

<div align="center">
<table cellspacing="0" bordercolor="#C0C0C0" bgcolor="#FFFFEA" style="width: 85%;" cellpadding="0" class="style28">

<tr>
    <td bgcolor="#FFFFEA" align="right" class="style31" style="width: 20%">
	<span class="style32">Grade</span><font face="Arial" size="2">:</font></td>

    <td  bgcolor="#FFFFEA" class="style30" colspan="2">  
     <font face="Arial">
  	<span class="style1"><strong><select name="Grade">
  	<option <% if strGrade="BROKE" then %> selected <% end if %>>BROKE</option>
  		<option <% if strGrade="RF" then %> selected <% end if %>>RF</option>
  			<option <% if strGrade="WADDING" then %> selected<% end if %>>WADDING</option>
  		<option <% if strGrade="UNRESOLVED" then %> selected<% end if %>>UNRESOLVED</option>
  	</select></strong></span></td>

    <td  bgcolor="#FFFFEA" style="width: 44%" class="style30" colspan="2"> 
	&nbsp;</td>

  </tr>
  
    <tr>
    <td bgcolor="#FFFFEA" align="right" style="height: 23px; width: 20%;" class="style31"><font face="Arial" size="2">Material #:</td>

    <td  bgcolor="#FFFFEA" colspan="4" style="height: 23px" class="style31"> 
		<font face="Arial" size="2"> 
	<select name="SAP" style="font-weight: 700; width: 138px;" size="1" tabindex="1">
 	<option selected value="">  Select</option>
      <% strsql = "Select tblBrokeSAP.* from tblBrokeSAP order by SAP "
      
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    while not MyRec.eof

       %>
        <option <% if strSAP = MyRec("SAP") then %> selected <% end if %> value="<%= MyRec("SAP") %>"> <%= MyRec("SAP") %>-<%= MyRec("Type") %></option>
        <% MyRec.movenext
        wend
        MyRec.close %>
  
     </select>&nbsp;
		&nbsp;&nbsp;&nbsp; (Required)</font></td>
  </tr>
  
  <tr>
    <td  align = right bgcolor="#FFFFEA" height="29" class="style31" style="width: 20%" >
   <font face="Arial" size="2">Trailer:&nbsp;</font></td>
<td  align = left colspan="4" height="29" bgcolor="#FFFFEA" class="style30">

      <input type="text" name="Trailer" size="15" value="<%= strTrailer %>" tabindex="2" >&nbsp;
		<font face="Arial" size="2"><b>(Required)</b></font></td></tr>
  <tr>

      <td  bgcolor="#FFFFEA" align = right height="28" class="style31" style="width: 20%">
	<font face="Arial" size="2">Select Carrier: </font></td>
<td  align = left height="28" bgcolor="#FFFFEA" class="style30" colspan="2">

      <select name="Carrier" tabindex="3">
 	<option selected>  Select Carrier - Required</option>
      <%= objGeneral.OptionListAsString(rstFiber, "Carrier", "CDesc", strCarrier) %>
     </select><font face="Arial" size="2"><b>&nbsp; </b></font></td>
<td  align = left height="28" bgcolor="#FFFFEA" class="style30" colspan="2">

      &nbsp; </td></tr>
<tr>
    <td  bgcolor="#FFFFEA" class="style31" style="width: 20%">  
	<p align="right">  <font face="Arial" size="2">&nbsp;</font></td>
    <td bgcolor="#FFFFEA" colspan="4" class="style30">   &nbsp;</td>
  </tr>

    <tr>
		<td align = right class="style26" style="width: 20%"> 
		<p align="center"> <b>
			<font face="Arial" size="2">Combined Trailer/Tractor</font></b><font face="Arial" size="2"><b> 
		Weight:&nbsp;</b></font></td>
<td align = left colspan="4" class="style26">    <font face="Arial"> 
<select name="Trailer_option" style="font-weight: 700" size="1" tabindex="4">
 	<option selected>  Select Trailer</option>
      <%= objGeneral.OptionListAsString(rstTrailer, "TID", "Toption", strTrailerTID) %>
        
     </select><%= strTrailerTID %></td>
</tr>
<tr>
	<td height="22" align="right" class="style26" style="width: 20%">
	&nbsp;</td>
    <td colspan="4" height="22" class="style26">         
	&nbsp;</td>
  </tr>
<tr>
	<td height="22" class="style25" style="width: 20%">
	<p align="right"><font face="Arial" size="2">Gross Weight:</font></td>
    <td height="22" width="21%" class="style26" colspan="2">      
	
      <input type="text" name="Gross_Weight" size="15" style="height: 22px" value="<%= strGrossweight %>" tabindex="5" ></td>
    <td height="22" style="width: 44%" class="style25" colspan="2">    
	 <font face="Arial" size="2">OR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
		Pounds Received:&nbsp;&nbsp; 
		<input name="Tons_Received" size="15" value = "<%= strPounds%>" tabindex="6" ></font></td>
  </tr>
<tr>
	<td  bgcolor="#FFFFEA" height="22" class="style30" style="width: 20%">&nbsp;</td>
    <td  bgcolor="#FFFFEA" colspan="4" height="22" class="style30">&nbsp;</td>
  </tr>

       <tr>
		<td  align = right bgcolor="#FFFFEA" class="style31" style="width: 20%" ><font face="Arial" size="2">Shipping Ticket Weight:</font></td>
<td align = left colspan="4" bgcolor="#FFFFEA" class="style30"> 
<input type="text" name="Sap_Weight" size="15" value = "<%= strSAPWeight%>" style="width: 82px" tabindex="7"></td></tr>
             
       <tr>
		<td  align = right bgcolor="#FFFFEA" class="style30" style="width: 20%" ><strong>
		<span class="style29">STO Delivery Number</span>:</strong></td>
<td align = left bgcolor="#FFFFEA" class="style30"> 
<input type="text" name="Load" size="15" value = "<%= strLoad %>" style="width: 81px" tabindex="8"></td>
<td align = left colspan="2" bgcolor="#FFFFEA" class="style35"> 
<span class="style33"><span class="style1"><strong>PO#&nbsp; 
<input type="text" name="PO" size="15" value = "<%= strPO %>" style="width: 81px" tabindex="8"></strong></span></span><strong>
</strong></td>
<td align = left bgcolor="#FFFFEA" class="style34"> 
<strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
BOL# 
<input type="text" name="BOL" size="15" value = "<%= strBOL%>" style="width: 81px" tabindex="8"></strong></td>
</tr>
             
       <tr>
		<td  align = right bgcolor="#FFFFEA" class="style30" style="width: 20%" >
		&nbsp;</td>
<td align = left colspan="4" bgcolor="#FFFFEA" class="style30"> 
&nbsp;</td></tr>
             
       <tr>
		<td  align = right bgcolor="#FFFFEA" class="style31" style="width: 20%" ><font face="Arial" size="2">Date Received:&nbsp;</font></td>
<td align = left colspan="4" bgcolor="#FFFFEA" class="style30"> 
<input type="text" name="Date_Received" size="15" value = <%= formatdatetime(Now(),2)%> style="width: 136px" tabindex="9"></td></tr>
             
         <tr>
          <td  align = right bgcolor="#FFFFEA" class="style31" style="width: 20%" >
  <font face="Arial" size="2">Comments:&nbsp;</font></td >
   <td align = left colspan="4" bgcolor="#FFFFEA" class="style30">   
	<input type="text" name="Other_Comments" size="25" style="width: 278px" value="<%= strOther %>" tabindex="10">
	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font face="Arial"><font size="2">&nbsp;Location: </font>   
	<span class="style1">YARD</span></font><span class="style1">&nbsp;&nbsp;&nbsp;</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	</td></tr>
	
	

<tr>
    <td  bgcolor="#FFFFEA" class="style30" style="width: 20%">&nbsp;</td>

    <td bgcolor="#FFFFEA" colspan="4" class="style30"> 
	<Input name="Update" type="submit" Value="Submit"  ></td>
  </tr>


</table>

</div>

</form>
</body>
<% Function GetData()
  set objMOC = new ASP_CLS_FIBER
        set rstFiber = objMOC.FiberCarrier()
    
        set rstTrailer = objMOC.TrailerOptions()
           
End Function

Function SaveData()
dim strbody, strCID, strECarrier, strETrailer, strELoad, strEDoc, strEDC, strEDCName, strEAdd, strECity, strEState, MyConn2, strsql4
dim strECC, strEBCC
	

	if len(request.form("Tons_Received")) > 0 then
		
		strPounds = Request.form("Tons_received")
	strTonsReceived = round(strPounds/2000,3)



	strTrailerTID = 0
	strTrailerweight = 0
	strGrossWeight = 0
	strTareWeight = 0
    strNet = strTonsReceived
	end if 


	
	If len(request.form("Gross_Weight")) > 1 then
	strTrailerTID = request.form("Trailer_option")
   	 strSQL3 = "Select weight from tblTrailerOptions where TID = " & strTrailerTID

   	 Set MyConn3 = Server.CreateObject("ADODB.Recordset")
   	 MyConn3.Open strSQL3, Session("ConnectionString")
   	 
   	 strTrailerWeight = MyConn3.fields("Weight")
   	 
   	 MyConn3.close
   	 
   	   	 
	strTonsReceived = round((Request.form("Gross_Weight") - strTrailerweight)/2000,3)
	strPounds = round((Request.form("Gross_Weight") - strTrailerweight),3)
	strTareWeight =  strTrailerweight
   	 

	strGrossWeight = Request.form("Gross_Weight")
		strNet = strTonsReceived
	end if
	

	
If strTrailerweight > 100 then
	strTrailerweight = (strTrailerweight - 18780) ' subtract off the weight of the conventional tractor
	end if 
	

	
	Dim strRightNow
	strRightnow = now()
	
	


	strSapWeight = request.form("Sap_Weight")
	if len(strSAPWeight) > 0 then
	strTonsReceived = strSapWeight
	end if
		strLoad = request.form("Load")
	if len(strLoad) > 0 then
	'do nothing
	else
	strLoad = 0
	end if
	
strSAP = request.form("SAP")
	strsql = "Select Type from tblBrokeSAP where SAP = '" & strSAP & "'"
	    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
   if not MyRec.eof then
   strSpecies = MyRec("Type")
   end if
   MyRec.close

if Request.form("Grade") = "WADDING" THen 
strsql =  "Update tblCars set SAP_Nbr = '" & strSAP & "', "_
&"   Carrier = '" & strCarrier & "', Species = '" & strSpecies & "', "_
&" Grade = '" & request.form("Grade") & "', Trailer = '" & strTrailer & "', Other_Comments = '" & strOther & "', "_
&"  Tons_Received = " & strTonsReceived & ", Net = " & strTonsReceived & ",  "_
&"   STO_Number = " & strLoad & ", PO = '" & request.form("PO") & "', BOL = '" & request.form("BOL") & "' where CID = " & strid

else
	
strsql =  "Update tblCars set SAP_Nbr = '" & strSAP & "', Broke_Description = '" & strType & "', Trailer_weight = " & strTrailerWeight & ","_
&"  Trailer_TID = " & strTrailerTID & ",  Carrier = '" & strCarrier & "', Species = '" & strSpecies & "', "_
&" Grade = '" & request.form("Grade") & "', Trailer = '" & strTrailer & "', Other_Comments = '" & strOther & "', "_
&"  Tons_Received = " & strTonsReceived & ", Net = " & strTonsReceived & ", Gross_weight = " & strGrossWeight & ", "_
&" Tare_weight = " & strTareWeight & ", "_
&"   STO_Number = " & strLoad & ", PO = '" & request.form("PO") & "', BOL = '" & request.form("BOL") & "' where CID = " & strid
end if	

	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
				
	
   	 
   	 if len(strNet) > 0 then
   	 strsql = "Update tblcars set Tons = " & strnet & " where CID = " & strID
   	 	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

end if



	
		Response.redirect ("Resolve_STO_Exceptions.asp")


End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->