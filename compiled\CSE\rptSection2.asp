<html>
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 6.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>This assessment is intended to document a summary of&nbsp; hazards and 
control measures required to permit safe entry</title>
</head>
<% dim strAirExchange, strVolume, strFtoM, strNaturalAir, strMechanicalAir, strglassesWSS, strInternalConfig, strConfigReduced
dim strChemicalGOG, strDustGOG, strOtherGoggles, strfaceshield, strWeldingHelmet
dim strOtherEyePort, strregularshoes, strChemicalBoots, strNonConductive, strAirlineResp
dim strdustResp, strescapeResp, strescapeRespWAttendant, strhalfResp, strfullResp,strPAPResp
dim strScottAirPack, strOtherResp, strhearing, strHardHat, strcottonleather, strhotGlove
dim strchemicalGlove, strchemicalRubber, strchemicalNitrile, strCutResistant, strOtherGlove
dim strpapercoveralls, strchemicalCloth, strnonsparking, strcoolvest, strkneepads, strOtherClothing
dim strATNonEntryLocation1, strATNonEntryLocation2, strATNonEntryLocation3, strATNonEntryLocation4
dim strATNE1Tech, strATNE2Tech, strATNE3Tech, strATNE4Tech, strCompartName
Dim strSpaceID, strLocation, strSDescription, strcid, strSpecialAlarm, strListAlarm, strAir1
Dim strReducdedList1, strReducedList2, strIllumIssues, strLowVolt, strGFIRequired, strOtherHazImpairRescue, strRescueControl
Dim strOtherHazIDLH, strOtherHazDrop, strDropHazControl, strimpactGOG, strImmediate_danger, strOtherGogglesChk
Dim strotherEyeProt, strOtherShoes, strAnyNonEntryAirTest, strHid, strVentSpReq, strPPEspreq

strid = Request.querystring("id")
strcid = Request.querystring("cid")

strsql = "SELECT tblHA.*, tblSOP.LOCATION, tblSOP.SDescription FROM tblHA INNER JOIN tblSOP ON tblHA.SpaceID = tblSOP.SOP_NO "_
  &" where SpaceID = '" & strid & "'"
Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
  If not MyConn.eof then
 strSpaceid = strid
 strLocation = MyConn.fields("Location")
 strSdescription = MyConn.fields("SDescription")
 strHid = MyConn.fields("IDHazAssess")
 end if 
 MyConn.close 
  
 strsql = "SELECT tblCompartment.* from tblCompartment where ID = " & strcid & ""

Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
  If not MyConn.eof then 
  
  strPPPspreq = MyConn.fields("PPE_SpReq")
 
strVentSpReq = MyConn.fields("Vent_spReq")
strAirExchange = MyConn.fields("airEexchanges")
strVolume = MyConn.fields("Volume")
strFtoM = MyConn.fields("FtoM")
strNaturalAir = MyConn.fields("NaturalAir")
strMechanicalAir = MyConn.fields("MechanicalAir")
strglassesWSS = MyConn.fields("glassesWSS")
strChemicalGOG = MyConn.fields("ChemicalGOG")
strDustGOG = MyConn.fields("DustGOG")
strOtherGoggles = MyConn.fields("OtherGoggles")
strfaceshield = MyConn.fields("faceshield")
strWeldingHelmet = MyConn.fields("WeldingHelmet")
strotherEyeProt = MyConn.fields("OtherEyeProt")
strregularshoes = MyConn.fields("regularshoes")
strChemicalBoots = MyConn.fields("ChemicalBoots")
strNonConductive = MyConn.fields("NonConductive")
strAirlineResp = MyConn.fields("AirlineResp")
strdustResp = MyConn.fields("dustResp")
strescapeResp = MyConn.fields("escapeResp")
strescapeRespWAttendant = MyConn.fields("escapeRespWAttendant")
strhalfResp = MyConn.fields("halfResp")
strfullResp = MyConn.fields("fullResp")
strPAPResp = MyConn.fields("PAPResp")
strScottAirPack = MyConn.fields("ScottAirPack")
strOtherResp = MyConn.fields("OtherResp")
strhearing = MyConn.fields("hearing")
strHardHat = MyConn.fields("HardHat")
strcottonleather = MyConn.fields("cottonleather")
strhotGlove = MyConn.fields("hotGlove")
strchemicalGlove = MyConn.fields("chemicalGlove")
strchemicalRubber = MyConn.fields("chemicalRubber")
strchemicalNitrile = MyConn.fields("chemicalNitrile")
strCutResistant = MyConn.fields("CutResistant")
strOtherGlove = MyConn.fields("OtherGlove")
strpapercoveralls = MyConn.fields("papercoveralls")
strchemicalCloth = MyConn.fields("chemicalCloth")
strnonsparking = MyConn.fields("nonsparking")
strcoolvest = MyConn.fields("coolvest")
strkneepads = MyConn.fields("kneepads")
strATNonEntryLocation1 = MyConn.fields("ATNonEntryLocation1")
strATNonEntryLocation2 = MyConn.fields("ATNonEntryLocation2")
strATNonEntryLocation3 = MyConn.fields("ATNonEntryLocation3")
strATNonEntryLocation4 = MyConn.fields("ATNonEntryLocation4")
strATNE1Tech = MyConn.fields("ATNE1Tech")
strATNE2Tech = MyConn.fields("ATNE2Tech")
strATNE3Tech = MyConn.fields("ATNE3Tech")
strATNE4Tech = MyConn.fields("ATNE4Tech")

strInternalConfig = MyConn.fields("InternalConfig")
strCompartName = MyConn.fields("CompartName")
strconfigreduced = MyConn.fields("Config_Reduced")
strReducedList1 = MyConn.fields("ReducedList1")
strReducedList2 = MyConn.fields("ReducedList2")

strOtherHazImpairRescue = Myconn.fields("OtherHazImpairRescue")
strRescueControl = MyConn.fields("RescueControl")
strOtherHazIDLH = MyConn.fields("OtherHazIDLH")
strOtherHazDrop = MyConn.fields("OtherHazDrop")
strDropHazControl = MyConn.fields("DropHazControl")
strimpactGOG = MyConn.fields("impactGOG")
strOtherGoggleschk = Myconn.fields("othergoggleschk")
strImmediate_danger = Myconn.fields("Immediate_danger")
strOtherShoes = Myconn.fields("OtherShoes")
strOtherClothing = Myconn.fields("OtherClothing")
strAnyNonEntryAirTest = MyConn.fields("AnyNonEntryAirTest")
end if
MyConn.close 


 		 %>


<body>
<table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" height="26">
  <tr>
    <td  bgcolor="#FFFFFF" bordercolor="#FFFFFF"><b>
	<font size="2" face="Arial">Compartment Analysis&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </font>
	<font face="Arial">&nbsp;Section 2:&nbsp; Compartment Review</font></b></td>
  </tr></table>



  <table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="100%">
  <tr>
    <td width="4%" height="19" bgcolor="#FFFFDD">
	<p align="center"><font size="2" face="Arial">
	&nbsp;Space ID</font></td>
    <td width="13%" height="19" bgcolor="#FFFFDD" align="center">
	<font face = Arial size = 2>Space Name</td>
    <td width="12%" height="19" bgcolor="#FFFFDD" align="center">
	<font face="Arial" size="2">Compartment Name</font></td>
    <td width="28%" height="19" bgcolor="#FFFFDD" align="center"><font face="Arial" size="2">
	Location</font></td>

    <td width="21%" bgcolor="#FFFFDD" height="19">
	<p align="center"><font face="Arial" size="2">Entry Portals</font></td>
   
  </tr>
  <tr>
    <td width="4%" align = center height="19" bgcolor="#FFFFFF" > 
	<font face = Arial size = 2><%= strSpaceID %></td>
    <td width="13%" height="40" bgcolor="#FFFFFF"  ><font face="Arial" size="2">
	<%= strSdescription %></font></td>
    <td width="12%" height="40" bgcolor="#FFFFFF"  >	<font face="Arial" size="2"><b>
	
	
	<%= strCompartName %></b>&nbsp;</td>
	<td bgcolor="#FFFFFF"><font face="Arial" size="2"><%= strLocation %></td>

      <td  bgcolor="#FFFFFF" height="19"><font face="Arial" >
    	<font size="2">
<% Dim strsqlC, MyConnC
    strsqlC = "SELECT tblPortals.* from tblPortals where Compartment_ID= " &  strcid
    Set MyConnC = Server.CreateObject("ADODB.Recordset") 
   		MyConnC.Open strSQLC, Session("ConnectionString")
  If not MyConnC.eof then %>
  
  	<% 
      Dim ii
       ii = 0
       while not MyConnC.Eof %>
       <font face = arial size = 1>&nbsp;<%= MyConnC.fields("NameLocPortal")%>&nbsp;</font>
 <br>
    <%
       ii = ii + 1
       MyConnC.MoveNext
     Wend
     End if
     MyConnc.close
    %>

 
  
  </tr>
</table>
<table width = 100%><tr><td bgcolor="#D9E1F9" height="25">
	<b><font face="Arial" size="2">Ventilation:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Identify the volume of the compartment (length x width x height)</font></b></td>
   </tr></table>
   <table>


 
  <tr>
    <td height="25" bgcolor="#FFFFFF" ><font size="2" face="Arial">GUIDELINE: Less than 3000 cubic 
	feet or 85 cubic meters = 20 air exchanges p/h or greater than 3000 cubic 
	feet or 85 cubic meters = 10 air changes p/h. &nbsp;&nbsp; </font></TD>
    


  </tr>
  <tr>
    <td height="44" bgcolor="#FFFFFF" ><font face="Arial" size="2">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	Volume:&nbsp;&nbsp; </font><font face="Arial">
	<font size="2">
	<%= strVolume%>&nbsp;	<%= strFtoM%>&nbsp;&nbsp;
	</font><font face="Arial" size="2">Recommended Air Exchanges&nbsp; <%= strAirExchange%>&nbsp; 
	p/h </font>
	<br><font size="2" face="Arial">&nbsp;&nbsp; In this compartment, 
		will you use natural ventilation or mechanical ventilation during the 
		work phase?<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </font>
		<font face="Arial">
		<input type="checkbox" name="NaturalVentilation" <% if strNaturalAir = -1 then %>checked <% end if %> value="ON"></font><font face="Arial" size="2">&nbsp; 
		Natural Ventilation (2 openings recommended) 
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </font>
		<font face="Arial">
		<input type="checkbox" name="MechanicalVentilation" <% if strMechanicalAir = -1 then %>checked <% end if %> value="ON"></font><font face="Arial" size="2">&nbsp; 
		Mechanical Ventilation</font></p>
	
	</TD>
 


  </tr>
  
    <tr>
    <td bgcolor="#FFFFFF" ><font size="2" face="Arial">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	List any Specific Requirements:&nbsp;<%= strVentSpReq%>&nbsp;&nbsp; </font>
		</TD>
    
  
   

  </tr>
  </table>
  
    <table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#D9E1F9">
 

  <tr>
    <td bgcolor="#D9E1F9" height="25" ><b><font face="Arial" size="2">Internal 
	Configuration&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	</font></b></td>
   </tr></table>

   <table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#D9E1F9">
 
  <tr>
    <td bgcolor="#FFFFFF" ><font face="Arial" size="2">
	Does this compartment have an internal configuration that could allow someone 
	entering to be trapped or asphyxiated by inward converging walls or by a 
	floor, with slopes downward and tapers to a smaller cross-section.&nbsp;&nbsp;&nbsp;
	</font><font face="Arial">
	 <% if strInternalConfig = -1 then %> <font size="2">Yes <% else %> No 
	</font> <% end if %>
</font></td>
    
  </tr></table>
   <table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#D9E1F9">
 
  <tr>
    <td width="100%" bgcolor="#FFFFFF" height="33" ><font face="Arial" size="2">&nbsp;&nbsp;&nbsp; Can it be 
	addressed to reduce the potential for harm?&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;
	&nbsp;&nbsp; If Yes, list what is needed to control 
	hazard: (platform, blocked, etc.)
	</font></td>
  </tr> 
  
  <tr>
    <td width="100%" bgcolor="#FFFFFF" height="20" ><font face="Arial" size="2">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	1.&nbsp; &nbsp;&nbsp;<%= strReducedList1%> </font></td>
  </tr> 

 <tr>
    <td width="100%" bgcolor="#FFFFFF" ><font face="Arial" size="2">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	2.&nbsp; &nbsp;&nbsp;<%= strReducedList2%>
	</font></td>
  </tr> 

 </table>
  <table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#D9E1F9">
 

  <tr>
    <td width="100%" bgcolor="#D9E1F9" height="25"><b>
	<font face="Arial" size="2">Other 
	Recognized Safety or Health Hazards:&nbsp; Did we miss anything?</font></b></td>
  </tr>
  <tr>
    <td width="100%" bgcolor="#FFFFFF"><font face="Arial" size="2">Does this compartment contain any other 
	recognized safety or health hazards that could either:</font></td>
  </tr>
 
</table>
    <table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#D9E1F9">
  <tr>
    <td height="22" bgcolor="#FFFFFF"><font face="Arial" size="2">&nbsp;&nbsp; </font>
		<font face="Arial">
		<input type="checkbox" name="OtherHazImpairRescue" <% if strOtherHazImpairRescue = -1 then %> checked <% end if %> value="ON"><font size="2">
	</font></font>
	<font face="Arial" size="2">Impair the ability to self rescue&nbsp;&nbsp;If so, list control:&nbsp; 
	<%= strRescueControl%></font></td>
 
   
  </tr>
  <tr>
    <td bgcolor="#FFFFFF"><font face="Arial" size="2">&nbsp;&nbsp; </font>
		<font face="Arial">
		<input type="checkbox" name="OtherHazIDLH" <% if strOtherHazIDLH = -1 then %> checked <% end if %>value="ON"><font size="2">
	</font></font>
	<font face="Arial" size="2">Result in a situation that presents an immediate 
	danger to life or health?&nbsp; List hazard and control: </font>
	<font face="Arial">
	<%= strImmediate_danger%></font></td>
    <tr><td bgcolor="#FFFFFF">
		<font face="Arial"><font size="2">&nbsp;&nbsp;
		</font>
		<input type="checkbox" name="OtherHazDrop"  <% if strOtherHazDrop = -1 then %> checked <% end if %> value="ON"><font size="2">
		</font></font>
	<font face="Arial" size="2">Can anything from above drop into the space?&nbsp; 
	If it can be controlled, list what is needed to control hazard:&nbsp; 
		
	<%= strDropHazControl%>
		</td>
   
  </tr></table>
  
<br>
    <table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#D9E1F9">
 
  <tr><td bgcolor="#D9E1F9" height="25"><font face="Arial" size="2"><b>Non-entry Ports&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </b></font></td> </tr>
    </table>

<table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#D9E1F9">
 	   <tr height = 40><td bgcolor="#FFFFFF"><font face="Arial" size="2">Are any air 
		tests done at NON-ENTRY ports in this compartment?
	 <% if strAnyNonEntryAirTest = -1 then %> Yes <% else %> No <% end if %>
&nbsp; If Yes, list all air test locations and 
		note the proper test technique.</font></font></td></tr>
	
	<tr><td bgcolor="#FFFFFF" align="left">
	
		<font face="Arial" size="2">&nbsp;&nbsp;&nbsp;&nbsp; Location 1:&nbsp; 
		<%= strATNonEntryLocation1 %>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;
		Test Technique:
	&nbsp;  <% if strATNE1Tech = "Vertical" then %>Vertical <% elseif strATNE1Tech = "Horizontal" then %> Horizontal <% else %> &nbsp<% end if %>
</tD>
		</tr>
	<tr><td bgcolor="#FFFFFF" align="left" height="18">
	<font face="Arial" size="2">&nbsp;&nbsp;&nbsp; 
		&nbsp;Location 2:&nbsp;<% = strATNonEntryLocation2 %>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	Test Technique:
		&nbsp;  <% if strATNE2Tech = "Vertical" then %>Vertical <% elseif strATNE2Tech = "Horizontal" then %> Horizontal <% else %> &nbsp<% end if %>
</tD>	</tr>
	
	<tr><td bgcolor="#FFFFFF" align="left">
	<font face="Arial" size="2">&nbsp;&nbsp;&nbsp; 
		&nbsp;Location 3:&nbsp;<% = strATNonEntryLocation3 %>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;
	Test Technique:
	
		&nbsp;  <% if strATNE3Tech = "Vertical" then %>Vertical <% elseif strATNE3Tech = "Horizontal" then %> Horizontal <% else %> &nbsp<% end if %>
</tD>
		</tr>
		<tr><td bgcolor="#FFFFFF" align="left">
	<font face="Arial" size="2">&nbsp;&nbsp;&nbsp; 
		&nbsp;Location 4:&nbsp;<% = strATNonEntryLocation4 %>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;
	Test Technique:
	 
		&nbsp;  <% if strATNE4Tech = "Vertical" then %>Vertical <% elseif strATNE4Tech = "Horizontal" then %> Horizontal <% else %> &nbsp<% end if %>
</tD>
		</tr>
</table>
</body>

