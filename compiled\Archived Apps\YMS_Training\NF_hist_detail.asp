																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Recovered Paper Yard Inventory Report</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->



<% Dim MyRec, strsql, strid
strid = Request.querystring("id")



strsql = "SELECT tblCars.Carrier, tblCars.Trailer, tblMovement.DDate, tblMovement.CID, tblMovement.Tdate, "_
&" tblMovement.From_location, tblMovement.To_location, tblMovement.BID, tblMovement.Comment, tblMovement.From_door, "_
&" tblMovement.To_door FROM tblCars INNER JOIN tblMovement ON tblCars.CID = tblMovement.CID where tblCars.CID = " & strid & ""
   
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=0>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><b>
<font face="arial" size="4" >Trailer History Report for <%= MyRec.fields("Trailer")%>&nbsp;&nbsp;&nbsp;&nbsp;Carrier:&nbsp<%= MyRec.fields("Carrier")%></font></b></span></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br><br>

	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">

	<td  ><b><font face="Arial" size="2">Movement Date</font></b></td>
		<td align = left><b><font face="Arial" size="2">Transaction Date</font></b></td>
    	<td align = left ><font face="Arial" size="2">
		<b><font face="Arial" size="2">From<br> Location</font></b></td>
		<td ><b><font face="Arial" size="2">To<br> Location</font></b></td>
	<td ><b><font face="Arial" size="2">From<br> Door</font></b></td>
	<td ><b><font face="Arial" size="2">To<br> Door</font></b></td>
<td width="69" ><b><font face="Arial" size="2">BID</font></b></td>
<td width="69" ><b><font face="Arial" size="2">Comments</font></b></td>
	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    


		
 
   <td  >  <font size="2" face="Arial">        <%= MyRec.fields("DDate")%></font></td>
    <td  >  <font size="2" face="Arial">        <%= MyRec.fields("TDate")%></font></td>
 <td  >  <font size="2" face="Arial">        <%= MyRec.fields("From_location")%></font></td>
 <td  >  <font size="2" face="Arial">        <%= MyRec.fields("To_location")%></font></td>
 <td  >  <font size="2" face="Arial">        <%= MyRec.fields("From_door")%></font></td>
 <td  >  <font size="2" face="Arial">        <%= MyRec.fields("To_door")%></font></td>	
  <td  >  <font size="2" face="Arial">        <%= MyRec.fields("BID")%></font></td>	
	<td><font size="2" face="Arial"><%= MyRec.fields("Comment")%>&nbsp;</font></td>

	</tr>

 <%    ii = ii + 1
       MyRec.MoveNext
    
   Wend %>