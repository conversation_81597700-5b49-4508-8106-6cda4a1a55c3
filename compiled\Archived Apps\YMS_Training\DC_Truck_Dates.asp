																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>DC Warehouse Trucks</TITLE>
<style type="text/css">
.style1 {
	border: 1px solid #C0C0C0;
}
.style2 {
	font-family: Arial;
	font-size: small;
	text-align: center;
}
.style3 {
	font-size: small;
	text-align: center;
}
.style4 {
	font-size: small;
	text-align: center;
	background-color: #FFFFBF;
}
.style5 {
	font-family: Arial;
	font-size: small;
	text-align: center;
	background-color: #FFFFBF;
}
.style6 {
	text-align: center;
}
</style>
</head>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->
<%   Dim strStart, strEnd

set objGeneral = new ASP_CLS_General
  if objGeneral.IsSubmit() Then	
  strStart = Request.form("Start")
  strEnd = Request.form("End")
  Response.redirect("DC_Truck_list.asp?s=" & strStart & "&e=" & strEnd)
  end if
  %>
<body>
<div class="style6">
<br>
<form name="form1" action="DC_Truck_Dates.asp" method="post" ID="Form1">	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>DC Warehouse Trucks<br>
<br>
Enter Date Range for Loads you wish to View</b></font></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br>
	
	
	<TABLE cellSpacing=0 cellPadding=0 class = "style1" style="width: 25%" align="center">  
	 <tr class="tableheader">
			<td class="style5"  > <p align="center" class="style2">       Start 
			Date</td>
		<td class="style4"  ><font face="Arial">End Date</font></td>

	</tr>
		 <tr class="tableheader">
			<td class="style2"  > <p align="center" class="style2">     <font face = arial size = 1>
		<input type="text" name="Start" size="20" style="width: 103px" ></td>
		<td class="style3"  ><font face = arial size = 1>
		<input type="text" name="End" size="20" style="width: 94px" ></td>

	</tr>

</table>
<br><font face="Arial">
	<Input name="Update" type="submit" Value="Continue" style="font-weight: 700" ></font></div>
</form>
<br><br><br>

<!--#include file="Fiberfooter.inc"-->