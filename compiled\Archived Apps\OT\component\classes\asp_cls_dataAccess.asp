﻿<!--#INCLUDE FILE="DBConnectionString.inc"-->

<%
 Public Const adCmdSPStoredProc = 4
 Public Const adParamReturnValue = 4
 Public Const adParaminput = 1
 Public Const adParamOutput = 2
 Public Const adInteger = 3
 Public Const adVarChar = 200
 Public Const adUseClient = 3
 Public Const adOpenForwardOnly = 0
 Public Const adExecuteNoRecords = &*********
 Public Const adLockReadOnly = 1

	
  Class ASP_CLS_DataAccess 

    Public Function ExecuteSp(spName, vntParameters)
    Set ExecuteSp = PrivateExecuteSp(spName, vntParameters, True)
    End Function

    Public Sub ExecuteSp1(spName, vntParameters)
    Call PrivateExecuteSp(spName, vntParameters, False)
    End Sub

    Public Function ExecuteSql(strSql)
       Dim objConnection

       'Response.Write strSql
		'Application("ConnectionString")=kConnectionString

       set objConnection = Server.CreateObject("ADODB.Connection")
       objConnection.ConnectionString = Application("ConnectionString")
       objConnection.Open
       objConnection.execute strSql
       objConnection.Close
       set objConnection = Nothing


    End Function

    Public Function ExecuteSql1(strSql)
       Dim objConnection
		'Application("ConnectionString")=kConnectionString

       set objConnection = Server.CreateObject("ADODB.Connection")
       objConnection.ConnectionString = Application("ConnectionString")
       objConnection.Open
       'objConnection.execute strSql
       'objConnection.Close
       
       set ExecuteSql1 = server.createObject("adodb.recordset")

  
        ExecuteSql1.lockType = adLockReadOnly
        ExecuteSql1.cursorType = adOpenForwardOnly 
   
        ExecuteSql1.open strSql, objConnection

        set objConnection = Nothing

    End Function


    Private Function PrivateExecuteSp(spName, vntParameters, blnReturn)

    
       Dim objConnection, objCommand
       Dim rstOut

 		'Application("ConnectionString")=kConnectionString
     
       set objCommand = Server.CreateObject("ADODB.Command")
       objCommand.CommandType = adCmdSPStoredProc 
       objCommand.CommandText = spName
       objCommand.ActiveConnection = Application("ConnectionString")

 

       if ( IsNull(vntParameters) = False) Then
        Call PrivateBuildParametersFromVariant(objCommand, vntParameters)
       end if



       if blnReturn = False Then
         objCommand.Execute , , adExecuteNoRecords
       else

 
         set rstOut = Server.CreateObject("ADODB.Recordset")
         rstOut.CursorLocation = adUseClient
         rstOut.Open objCommand, , adOpenForwardOnly, adLockReadOnly

         Set rstOut.ActiveConnection = Nothing
         set PrivateExecuteSp = rstOut
         set rstOut = Nothing
       end if

       set objCommand = Nothing
       'set objConnection = Nothing

    End Function

    Private Sub PrivateBuildParametersFromVariant(cmdCommand, vntParameters)
       
       Dim i
       For i = 0 to uBound(vntParameters)
         'Response.Write i & vntParameters(i) & "*<br>"
         if (vntParameters(i) = "") Then
            cmdCommand.Parameters.Append _
               cmdCommand.CreateParameter(, adVarChar, adParamInput, 1, null)
         else
            cmdCommand.Parameters.Append cmdCommand.CreateParameter(, adVarChar, adParamInput, len(vntParameters(i)), vntParameters(i))   
         end if
       Next

    End Sub

  End Class
%>