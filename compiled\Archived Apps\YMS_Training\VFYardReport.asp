																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Virgin Fiber Yard Inventory Report</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->



<% Dim MyRec, strsql, strDays, strDate, gSpecies, gCount, gTCount, strsql3, MyRec3, gBales, gWeight

strdate = formatdatetime(now(),2)
gcount = 0
gbales = 0
gWeight = 0
gSpecies = ""

strsql = "SELECT tblCars.*,  [date_received] AS Sort_Date FROM tblCars WHERE Date_received Is Not Null AND Location='Yard'  and Grade = 'VF' ORDER BY Species, Sort_Date "   
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=0>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><b>
<font face="arial" size="4" >Virgin Fiber Yard Inventory Report for <%= strDate%></font></b></span></td>
<td align = center>&nbsp;</td>


</tr>
	    </table>
<p align="right"><font face="Arial">Yard Physically Checked by 
_______________________<br>Signature&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </font>&nbsp; </p>

	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">

	<td  ><font face="Arial" size="1"><b>Species</b></font></td>
		<td align = center  ><font face="Arial" size="1"><b>Date <br>Received</b></b></font></td>
    	<td ><font face="Arial" size="1"><b>&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; Trailer</b></font></td>
		<td ><font face="Arial" size="1"><b>&nbsp;&nbsp;&nbsp;  Carrier</b></font></td>

	<td ><font face="Arial" size="1"><b>REC Nbr</b></font></td>
	<td ><font face="Arial" size="1"><b>PO</b></font></td>
<td ><font face="Arial" size="1"><b>Vendor</b></font></td>
<td ><font face="Arial" size="1"><b>Qty</b></font></td>
<td ><font face="Arial" size="1"><b>Weight</b></font></td>
<td align=center><font face="Arial" size="1"><b>To Date<br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="1"><b>Days <br>in Yard</b></font></td>

<td align="center" ><font face="Arial" size="1"><b>Other</b></font></td>

	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    
    <% if  MyRec.fields("Species") = gSpecies or gcount = 0 then %>


 
	      
        <% if isnull(MyRec.fields("Transfer_date")) then %>
   <td>       <font size="2" face="Arial">       <%= MyRec.fields("Species")%></font></td>
   	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>

		<td  > <font size="2" face="Arial"> &nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;<%= MyRec.fields("Trailer")%></font></td>
		<% else %>
		   <td>       <font size="2" face="Arial">       <%= MyRec.fields("Species")%>       &nbsp;&nbsp:SHUTTLE</font></td>
   	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Transfer_Date")%></font></td>

			<td  > <font size="2" face="Arial"> &nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;<%= MyRec.fields("Transfer_trailer_nbr")%></font></td>
		
		<% end if %>
	  <% if isnull(MyRec.fields("Transfer_date")) then %>

<td  > <font size="2" face="Arial">&nbsp;&nbsp;&nbsp; <%= MyRec.fields("Carrier")%></font></td>
<% else %>
<td  > <font size="2" face="Arial">&nbsp;&nbsp;&nbsp; <%= MyRec.fields("Trans_Carrier")%></font></td>
    <% end if %> 
    
    	  <% if isnull(MyRec.fields("Transfer_date")) then %>
 <td  >  <font size="2" face="Arial">        <%= MyRec.fields("REC_number")%>&nbsp;</font></td>
 <% else %>

    <% if isnull(MyRec.fields("Rec_number")) then %>
		 <td  >  <font size="2" face="Arial">        <%= MyRec.fields("CID")%>&nbsp;</font></td>
		 <% else %>
	 <td  >  <font size="2" face="Arial">        <%= MyRec.fields("REC_number")%>&nbsp;</font></td>
	 <% end if %>
	
 <% end if %>

		  <% if isnull(MyRec.fields("Transfer_date")) then %>	
 
   <td  >  <font size="2" face="Arial">        <%= MyRec.fields("PO")%></font></td>
   <% else %>
   		   <td  >  <font size="2" face="Arial">        <%= MyRec.fields("PMO_nbr")%>&nbsp;</font></td>
<% end if %>
    <td  >  <font size="2" face="Arial">        <%= MyRec.fields("Vendor")%></font></td>
    <td  >  <font size="2" face="Arial">        <%= MyRec.fields("Bales_VF")%></font></td>
        <td  >  <font size="2" face="Arial">        <%= MyRec.fields("Tons_received")%>&nbsp;<%= MyRec.fields("UOM")%></font></td>
          
         <%  if isnull(MyRec.fields("Transfer_date")) then
             strDays = round(Now()- cdate(MyRec.fields("Date_received")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

             else
             strDays = round(Now()- cdate(MyRec.fields("Transfer_date")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Trans_Carrier") & "' "
             end if  

            

          
 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    
       If left(MyRec("Trailer"),4) = "GACX" then
    strFee = 0
    end if
    if strDays  > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(strDays) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"><%= strDays%></font></td>	
	
	<td><font size="2" face="Arial"><%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>

	</tr>

 <%    gcount = gcount + 1
 		gbales = gbales + MyRec.fields("Bales_VF")
 		gweight = gweight + MyRec.fields("Tons_received")
       gTcount = gTcount + 1
 		gSpecies = MyRec.fields("Species")
       ii = ii + 1
       MyRec.MoveNext
    
    %>
<% else %>
<td colspan = 7><font face = arial size = 2><b></b>Total:&nbsp;<%= gcount%></b></font></td>
<td ><font face = arial size = 2><b></b><%= gbales%></b></font></td>
<td colspan = 4><font face = arial size = 2><b></b><%= gweight%></b></font></td>


	</tr>
	<TR><td colspan = 11>&nbsp;</td>


	</tr>
	
	  <tr class=tablecolor2>
        
           <% if isnull(MyRec.fields("Transfer_date")) then %>
   <td>       <font size="2" face="Arial">       <%= MyRec.fields("Species")%></font></td>
   	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>

		<td  > <font size="2" face="Arial"> &nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;<%= MyRec.fields("Trailer")%></font></td>
		<% else %>
		   <td>       <font size="2" face="Arial">       <%= MyRec.fields("Species")%>       &nbsp;&nbsp:SHUTTLE</font></td>
   	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Transfer_Date")%></font></td>

			<td  > <font size="2" face="Arial"> &nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;<%= MyRec.fields("Transfer_trailer_nbr")%></font></td>
		
		<% end if %>
	  <% if isnull(MyRec.fields("Transfer_date")) then %>

<td  > <font size="2" face="Arial">&nbsp;&nbsp;&nbsp; <%= MyRec.fields("Carrier")%></font></td>
<% else %>
<td  > <font size="2" face="Arial">&nbsp;&nbsp;&nbsp; <%= MyRec.fields("Trans_Carrier")%></font></td>
    <% end if %>    
        
    	  <% if isnull(MyRec.fields("Transfer_date")) then %>
 <td  >  <font size="2" face="Arial">        <%= MyRec.fields("REC_number")%>&nbsp;</font></td>
 <% else %>

    <% if isnull(MyRec.fields("Rec_number")) then %>
		 <td  >  <font size="2" face="Arial">        <%= MyRec.fields("CID")%>&nbsp;</font></td>
		 <% else %>
	 <td  >  <font size="2" face="Arial">        <%= MyRec.fields("REC_number")%>&nbsp;</font></td>
	 <% end if %>
	
 <% end if %>
	  <% if isnull(MyRec.fields("Transfer_date")) then %>	
 
   <td  >  <font size="2" face="Arial">        <%= MyRec.fields("PO")%></font></td>
   <% else %>
   		   <td  >  <font size="2" face="Arial">        <%= MyRec.fields("PMO_nbr")%>&nbsp;</font></td>
<% end if %>
        <td  >
        <font size="2" face="Arial">
        <%= MyRec.fields("Vendor")%></font></td>
    <td  >  <font size="2" face="Arial">        <%= MyRec.fields("Bales_VF")%></font></td>
        <td  >  <font size="2" face="Arial">        <%= MyRec.fields("Tons_received")%>&nbsp;<%= Myrec.fields("UOM")%></font></td>

 <% if isnull(MyRec.fields("Transfer_date")) then
             strDays = round(Now()- cdate(MyRec.fields("Date_received")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

             else
             strDays = round(Now()- cdate(MyRec.fields("Transfer_date")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Trans_Carrier") & "' "
             end if  

 
 

           
          
 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    
       If left(MyRec("Trailer"),4) = "GACX" then
    strFee = 0
    end if
    if strDays  > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(strDays) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"><%= strDays%></font></td>

	

	<td><font size="2" face="Arial"><%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>

	</tr>
		
	<% gcount = 1
	gbales = 0
	gweight = 0
	 gTcount = gTcount + 1
	 	gbales = gbales + MyRec.fields("Bales_VF")
 		gweight = gweight + MyRec.fields("Tons_received")
	 gSpecies = MyRec.fields("Species")
       ii = ii + 1
       MyRec.MoveNext
    
	 end if %>

<%  Wend %>
	  <tr class=tablecolor2>
<td colspan = 7><font face = arial size = 2><b></b>Total:&nbsp;<%= gcount%></b></font></td>
<td ><font face = arial size = 2><b></b><%= gbales%></b></font></td>
<td colspan = 4><font face = arial size = 2><b></b><%= gweight%></b></font></td>


	</tr>
	<TR><td colspan = 11>&nbsp;</td>



	</tr>
<TR><td colspan = 11><font face = arial size = 2><b></b>Grand Total:&nbsp;<%= gTcount%></b></font></td>



	</tr>

</table>