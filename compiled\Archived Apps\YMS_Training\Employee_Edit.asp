<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<head>

<TITLE>Modify Employee</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
</head>
<style type="text/css">
.style1 {
	font-family: Arial;
	text-align: center;
}
.style3 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
.style4 {
	text-align: left;
}
.style5 {
	font-family: Arial;
	font-size: medium;
}
.style6 {
	text-align: center;
}
</style>

<% dim strCode, strDescription, strid, strsql, MyRec, strFirstName, strLastName, strUID
strid = request.querystring("id")


strsql = "SELECT tblEmployees.* from tblEmployees where ID = " & strid

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
If not MyRec.eof then
strFirstname = MyRec.fields("First_name")
strLastname = MyRec.fields("Last_name")
strUID= MyRec.fields("UID")
strInactive = MyRec.fields("Inactive")
end if
MyRec.close

 set objGeneral = new ASP_CLS_General


if objGeneral.IsSubmit() Then


	Call SaveData() 

End if %>
<body><form name="form1" action="Employee_edit.asp?id=<%= strid%>" method="post">
 
<table border="0" cellpadding="0" cellspacing="0" width = 60% style="border-collapse: collapse" bordercolor="000000" height="25" align = center >
  <tr>
    <td  bgcolor="#FFFFFF" bordercolor="#CCCCFF" height="25" >
    <p align="center"><font face="Arial" size="4">Modify </font>
	<span class="style5">Employee </span></td>
    <td align = center height="25"><font face="Arial"><b><a href="Employees.asp">RETURN</a></b></font></td>

    <td align = center bgcolor="#FFFFFF" height="25">
  <Input name="Submit" type="submit" Value="Submit" style="float: right" ></td>
  </tr>
</table><br><br>
<div align="center">
<table cellpadding="0" cellspacing="0" style="width: 55%;" bordercolor="#808080"  height="10" class="style3">
    <tr>
<td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" align="left" style="height: 39px" ><b>
	<font face="Arial">First Name </font></b></td>
   <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" align="left" style="height: 39px" ><b>
	<font face="Arial">Last Name </font></b></td>

<td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" class="style1" style="height: 39px" >
	<strong>Employee ID</strong></td>
   
   
<td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" class="style1" style="height: 39px" >
	<strong>Inactive</strong></td>
   
   
  </tr>
  
    <tr>
    <td  bordercolor="#CCCCFF" height="47"  align="center" class="style4" >   <font face="Arial">	
		<input type="text" name="First_name" size="35" value="<%= strFirstName %>" style="width: 177px" tabindex="1">
</font></td>
    	

    <td  bordercolor="#CCCCFF" height="47"  align="center" class="style4" >  	
     <font face="Arial">	
		<input type="text" name="Last_name" size="35" value="<%= strLastName %>" style="width: 178px" tabindex="2"></font></td>
    	
   <td  bordercolor="#CCCCFF" height="47"  align="center" class="style6" >  	
     <font face="Arial">	
		<input type="text" name="UID" size="35" value="<%= strUID %>" style="width: 79px; height: 22px;" tabindex="2"></font></td> 

   <td  bordercolor="#CCCCFF" height="47"  align="center" class="style6" >  	
	<input type="checkbox" name="Inactive" value="Y" <% if strInactive = True then %> checked <% end if %>></td> 

  </tr>
 
  </table>
</div>



</form>
   
  

</body>

</html> <%

  
  Function SaveData()

strFirstName = Replace(Request.form("First_name"), "'", "''")   
strLastName = Replace(Request.form("Last_name"), "'", "''") 
strUID = Request.form("UID") 
If request.form("Inactive") = "Y" then
strInactive = 1
else
strInactive = 0
end if

  
  strsql = "Update tblEmployees set Inactive = " & strInactive & ", First_name = '" & strFirstName & "', Last_name = '" & strLastName & "', UID = '" & strUID & "' where ID = " & strid 
  	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
          Response.redirect("Employees.asp")
  End Function
  
   %>