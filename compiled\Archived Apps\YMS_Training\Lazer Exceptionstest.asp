																

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>YMS Yard Exception Report</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->





<%
Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate, strsql3

strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -60, strtdate)

    	
strsql = "Select ID from tblMIssed where Log_Date = '" & strtdate & "'"
    Set MyConn2 = Server.CreateObject("ADODB.Recordset")
    MyConn2.Open strSQL, Session("ConnectionString")
    If not MyConn2.eof then 
    strMissedid = Myconn2("ID")
    else
    strMissedID = 99
    end if
    
   

Function StripOut(From, What) 

    Dim i 
	 
    StripOut = From
    for i = 1 to len(What)
	StripOut = Replace(StripOut, mid(What, i, 1), "")
    next 
	 
	End Function
	
	What = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"



strsql = "SELECT Last_Update from tblLazer "

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    strLastUpdate = MyRec("Last_Update")
    MyRec.close  

strsql = "SELECT max(Audit_date) as Last_Date from tblLazer"
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    strLastDate = MyRec("Last_Date")
    MyRec.close
  strTodate = strTdate & " 12:01:00 AM"  
   
 sdiff = datediff("h", strTodate, strLastUpdate)
  If strMissedid = 99 and  sDiff > 0 then
  Response.write("GOt it ")
  end if
 %>

strToDate: <%= strTodate %>      strLastUpdate:   <%= strLastUpdate %>   strMissedID : <%= strMIssedid %>   strdiff <%= sDiff %>