																

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>YMS Yard Exception Report</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->

  
	<style type="text/css">
.style1 {
	border: 1px solid #000000;
}
.style2 {
	font-family: Arial, Helvetica, sans-serif;
}
.style5 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.style8 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #FFFFFF;
}
.style10 {
	font-size: small;
	font-family: Arial, Helvetica, sans-serif;
}
.style11 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #FFFFFF;
	text-align: center;
}
.style12 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	text-align: center;
}
.style13 {
	color: #FF0000;
}
</style>

<%
Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate, strsql3

strtdate = formatdatetime(Now(),2)
  strtoday = formatdatetime(now(),0)
 strdate = formatdatetime(now(),2)
 
 
strCountLonger = 0
 strsql = "SELECT tblRentedTrailer.Trailer_Nbr, tblLazer.Trailer "_
&" FROM tblRentedTrailer inner JOIN tblLazer ON tblRentedTrailer.Trailer_Nbr = tblLazer.Trailer WHERE tblLazer.Status = 'EMPTY' and datediff(d, Audit_Date,'" & strtoday & "') > 0"
   Set MyConn2 = Server.CreateObject("ADODB.Recordset")
    MyConn2.Open strSQL, Session("ConnectionString")
    If not MyConn2.eof then 
    while not MyConn2.eof
strCountLonger = strCountLonger + 1
 MyConn2.movenext 
wend
end if
MyConn2.close 

strTransTrailer = ""
strCountNU = 0
strCountRF = 0

 strsql = "SELECT  Species, Date_received, trans_Carrier, species, PMO_Nbr,  transfer_trailer_nbr  FROM tblCars where datediff(d, date_received, '" & strtoday & "') = 1 and len(PMO_Nbr) > 1"_
 &" and Transfer_trailer_nbr Not like '%8834681' and Transfer_trailer_nbr Not like '%7004838' and Transfer_trailer_nbr Not like '%CONT%' and (grade = 'RF' or grade = 'VF')"
    Set MyConn2 = Server.CreateObject("ADODB.Recordset")
    MyConn2.Open strSQL, Session("ConnectionString")
    If not MyConn2.eof then 
    while not MyConn2.eof 
    if MyCOnn2("PMO_Nbr") = "RAIL CAR" then
    'skip
    else
    strTransTrailer = MyConn2("Transfer_Trailer_nbr")
    Response.write ("<br>Trailer " & strTransTrailer)
    
    strsql2 ="Select trailer_nbr from tblRentedTrailer where Trailer_nbr = '" & strTransTrailer & "'"
      Set MyC = Server.CreateObject("ADODB.Recordset")
    	MyC.Open strSQL2, Session("ConnectionString")
    	If not MyC.eof then
    	strCountRF = strCountRF + 1
    	else
    	strCountNU = strCountNU + 1
    	end if
    	MyC.close
    	end if
    	MyConn2.movenext
    	wend
    	end if
    	MyConn2.close
Dim strBody2

if strcountLonger < strCountNU then
strMissed = strcountlonger
else
strMIssed = strCountNU
end if

    	
strsql = "Select ID from tblMIssed where Log_Date = '" & strdate & "'"
    Set MyConn2 = Server.CreateObject("ADODB.Recordset")
    MyConn2.Open strSQL, Session("ConnectionString")
    If not MyConn2.eof then 
'skip
else
strsql2 = "Insert into tblMissed (Log_date, Missed) select '" & strdate & "', " & strMissed & ""
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL2
			MyConn.Close

end if
MyConn2.close
strMTD = 0

strsql = "Select sum(Missed) as MTD_Missed from tblMissed where datepart(m, Log_Date) = datepart(m, '" & strdate & "') and datepart(yyyy, Log_Date) = datepart(yyyy, '" & strdate & "')"
    Set MyConn2 = Server.CreateObject("ADODB.Recordset")
    MyConn2.Open strSQL, Session("ConnectionString")
    If not MyConn2.eof then 
strMTD = MyConn2("MTD_Missed")
else
strMTD = 0
end if
MyConn2.close
 
    'strEmailTo = "<EMAIL>"
       '  strEmailTo = "<EMAIL>"          
              
			Set objMail = Server.CreateObject("CDO.Message")		
			objMail.From = "<EMAIL>"
			objMail.To = "<EMAIL>"
			
			'objMail.BCC = strEmailCC
 
			objMail.Subject = "Rental Trailer Report"
			 
		    strbody2 =   "<TABLE cellSpacing=0 cellPadding=0 width=100%  border=0><tr><TD align = left ><strong><font face=arial>Trailers from Yesterday</strong></td> </tr>"
 			strbody2 = strbody2 & "<tr><TD align = left ><font face=arial color=black size=2>Non-RF: " & strCountNU & "</td> </tr>"
 			strbody2 = strbody2 & "<tr><TD align = left ><font face=arial color=black size=2>RF:" &  strCountRF & "</td> </tr></table><Br>"
 			strbody2 = strbody2 & "<TABLE   cellSpacing=0 cellPadding=0 width=100%  border=0>"
 			strbody2 = strbody2 & "<tr> <TD align = left ><strong><font face=arial color=red >Missed Trailer Rental Opportunity Yesterday: " &  strMissed & "</span></strong></td> </tr>"
 			strbody2 = strbody2 & "<tr> <TD align = left ><strong><span class=style13>&nbsp;</span></strong></td> </tr>"
 			strbody2 = strbody2 & "<tr> <TD align = left ><strong><font face= Arial>Missed Trailer Rental Opportunity MTD: " & strMTD & "</strong></td> </tr>"
 			strbody2 = strbody2 & "<tr> <TD align = left ><br><strong><font face= Arial>Rental Trailers sitting empty longer than one day: " & strCountLonger & "</strong> </td> </tr></table><br>"
			strbody2 = strbody2 & "<TABLE border=1 cellSpacing=0 cellPadding=0 width=15% > <tr><td> <font face= Arial> Trailer</td><td> <font face= Arial> Audit Date</td></tr>"


 strsql = "SELECT tblRentedTrailer.Trailer_Nbr, tblLazer.Trailer, Audit_Date "_
&" FROM tblRentedTrailer inner JOIN tblLazer ON tblRentedTrailer.Trailer_Nbr = tblLazer.Trailer WHERE tblLazer.Status = 'EMPTY' and datediff(d, Audit_Date,'" & strtoday & "') > 0"
   Set MyConn2 = Server.CreateObject("ADODB.Recordset")
    MyConn2.Open strSQL, Session("ConnectionString")
    If not MyConn2.eof then 
    while not MyConn2.eof
    strTrailer2 = MyCOnn2("Trailer_Nbr")
    strAD = MyConn2("Audit_Date")
    strbody2 = strbody2 & "<tr><td><font size=2 face=arial>" & strTrailer2 & "&nbsp;</font></td>"
        strbody2 = strbody2 & "<td><font size=2 face=arial>" & strAD & "&nbsp;</font></td></tr>"

MyConn2.movenext 
wend
end if
MyConn2.close

			strbody2 = strbody2 & "  </table>	<br>"
			strbody2 = strbody2 & "  <TABLE cellSpacing=0 cellPadding=0 width=100%  border=0><tr> <TD align = left><font face= Arial><strong>Rented Trailers not on SIX Report</strong></td> </tr></table><br>"
			strbody2 = strbody2 & " <TABLE cellSpacing=0 cellPadding=0 width=25% border=1><tr><td><font face= Arial>Trailer</td><td><font face= Arial>Last YMS Transaction</td></tr>  "

strsql = "SELECT tblRentedTrailer.Trailer_Nbr, tblLazer.Trailer "_
&" FROM tblRentedTrailer LEFT JOIN tblLazer ON tblRentedTrailer.Trailer_Nbr = tblLazer.Trailer WHERE tblLazer.Trailer Is Null and OOS is null"
   Set MyConn2 = Server.CreateObject("ADODB.Recordset")
    MyConn2.Open strSQL, Session("ConnectionString")
    If not MyConn2.eof then 
    while not MyConn2.eof
    strTrailer = MyConn2("Trailer_Nbr")
	strbody2 = strbody2 & "<tr><td><font face=arial>" & strTrailer & "</font></td>"
	 strsql2 = "Select Max(Date_unloaded) as MDR from tblCars where right(Trailer,5) = right('" & strTrailer & "',5) "
      Set MyConn = Server.CreateObject("ADODB.Recordset")
  MyConn.Open  strsql2, Session("ConnectionString")
    If not MyConn.eof then    
    strDateUnloaded = MyConn("MDR")
	end if 
	MyConn.close
	
   strsql2 = "Select Max(Trans_Unload_Date) as MDT from tblCars where  right(Transfer_Trailer_nbr,5) = right('" & strTrailer & "',5)"
      Set MyConn = Server.CreateObject("ADODB.Recordset")
  MyConn.Open  strsql2, Session("ConnectionString")
    If not MyConn.eof then    
    strDateUnloadedT = MyConn("MDT")
	end if 
	MyConn.close
		if strDateUnloadedT > strDateUnloaded then
	strUseDate = strDateUnloadedT
	else
	strUseDate = strDateUnloaded
	end if
	strbody2 = strbody2 & " <td><font face=arial>" & strUseDate & "&nbsp;</td></tr>"
	MyConn2.movenext 
wend
end if
MyConn2.close 
	strbody2 = strbody2 & "</table>"
	strbody2 = strbody2 & "<TABLE cellSpacing=0 cellPadding=0 width=100% ><tr> <TD align = left><br><strong><font face=arial>Transfers from Yesterday</strong></td> </tr></table><br>"
	strbody2 = strbody2 & "<TABLE cellSpacing=0 cellPadding=0 width=45% border=1><tr><td><font face=arial>Trailer</td><td><font face=arial>Date</td><td><font face=arial>From Location</td><td><font face=arial>To Location</td><td><font face=arial>Carrier</td><td><font face=arial>Species</td></tr>"
	
 strsql = "SELECT  Species, Date_received, trans_Carrier, species, Location, PMO_Nbr,  transfer_trailer_nbr  FROM tblCars where datediff(d, date_received, '" & strtoday & "') = 1 and len(PMO_Nbr) > 1 and (grade = 'RF' or grade = 'VF') "
    Set MyConn2 = Server.CreateObject("ADODB.Recordset")
    MyConn2.Open strSQL, Session("ConnectionString")
    If not MyConn2.eof then 
    while not MyConn2.eof
	
	strTransTrailer = MyConn2("Transfer_Trailer_nbr") 
	strDR = MyConn2("Date_Received")
	strPMO = MyConn2("PMO_Nbr")
	strLocation = MyConn2("Location")
	strCarrier = MyConn2("Trans_Carrier")
	strSpecies = MyConn2("Species")
	
	
	strbody2 = strbody2 & "<tr><td><font face=arial>" & strTransTrailer & "</td><td><font face=arial>" & strDR & "</td><td><font face=arial>" & strPMO & "</td><td><font face=arial>" & strLocation & "</td>"
	strbody2 = strbody2 & "<td><font face=arial>" & strCarrier & "</td><td><font face=arial>" & strSpecies& "</td></tr>"
	MyConn2.movenext 
wend
end if
MyConn2.close
 strbody2 = strbody2 & "</table>"
			objMail.HTMLBody =  "<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=0><tr><TD align = left><strong><font face= Arial>Rental Trailer Report: " & strdate & "</strong></td> </tr></table><br>" & strBody2
 
			objMail.Send
			Set objMail = nothing
' Response.redirect("Lazer Exceptions.asp")	

 %>


 
   


 
