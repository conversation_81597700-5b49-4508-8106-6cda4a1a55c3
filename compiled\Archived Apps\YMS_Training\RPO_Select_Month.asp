
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Recovered Paper Orders</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

<%  
	Dim strsQL, rstEquip, rstMonth, objNew, strMonth, objGeneral
   	

   set objGeneral = new ASP_CLS_General

  if objGeneral.IsSubmit() Then
 	strMonth = request.form("Month")
 	If request.form("Version") = "Web" then
  Response.redirect("rptMobileorders.asp?id=" & strMonth)
  else
    Response.redirect("rptMobileorders_excel.asp?id=" & strMonth)
 
   end if 
  end if
Call GetData()
%>



<style type="text/css">
.style1 {
	border-style: solid;
	border-width: 1px;
}
.auto-style2 {
	font-weight: bold;
	border: 1px solid #C0C0C0;
	background-color: #E7EBFE;
}
.auto-style3 {
	border: 1px solid #000000;
}
</style>
</head>


<form name="form1" action="RPO_Select_Month.asp" method="post">
	<div align="center">
<TABLE borderColor=#E7D1C2 cellSpacing=0 cellPadding=0 width="100%" border=0>  
  <tr><td colspan="5" size = "2" bordercolor="#FFFFFF"><b>
	<font face="Arial">Recovered Paper Orders&nbsp;&nbsp;</font></td>
	</tr></table>
	<TABLE cellSpacing=0 cellPadding=0 style="width: 50%" class="auto-style3"> 
  <TR>
    <TD align="center" style="height: 46" class="auto-style2"  ><font face="Arial" size="2">Select Month</font></TD>

    <TD align="center" style="height: 46" class="auto-style2"  >
	<font face="Arial" size="2">Version</font></TD>

 </TR>

  <TR>
         <TD bordercolor="#FFFFFF" align="center" style="height: 72px">
    <font face="Arial">
   <select size="1" name="Month">

     <option value="">--- Select ---</option>
       <%= objGeneral.OptionListAsString(rstMonth, "Import_month", "Import_month", strMonth) %>
     </select></font></TD>
         <TD bordercolor="#FFFFFF" align="center" style="height: 72px">
    <font face="Arial">
   <select size="1" name="Version">

     <option value="Web">Web Page</option>
     <option >Excel</option>
     </select></font></TD></tr>


     <TR>
    <TD align="center" colspan="2" class="auto-style2"  ><font face="Arial" size="2">
	<input type="submit"  value="View Report"  style="float: center"></font></TD>




 </TR>


  </TABLE></div>
</form>



 <% Function GetData()

   set objNew = new ASP_Cls_Fiber
          

	set rstMonth= objNew.FiberMonth()



End Function

 %><!--#include file="Fiberfooter.inc"-->