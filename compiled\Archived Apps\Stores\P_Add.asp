<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<head>

<TITLE>Add Personnel</TITLE>
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_Session.asp"-->
<!--#include file="classes/asp_cls_DataAccess.asp"-->
</head>
<style type="text/css">
.style1 {
	font-family: Arial;
	text-align: center;
}
.style3 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
.style4 {
	text-align: left;
}
.style5 {
	font-family: Arial;
	font-size: medium;
}
.style6 {
	text-align: center;
}
.auto-style1 {
	border-style: solid;
	border-color: #C0C0C0;
	font-family: Arial;
	text-align: center;
	font-size: x-small;
	background-color: #E7EBFE;
}
.auto-style2 {
	font-weight: bold;
	font-size: x-small;
	border-style: solid;
	border-color: #C0C0C0;
	background-color: #E7EBFE;
}
</style>
<% 


 set objGeneral = new ASP_CLS_General


if objGeneral.IsSubmit() Then

	Call SaveData() 


End if %>
<body><form name="form1" action="P_Add.asp" method="post">
 
<table border="0" cellpadding="0" cellspacing="0" width = 60% style="border-collapse: collapse" bordercolor="000000" height="25" align = center >
  <tr>
    <td  bgcolor="#FFFFFF" bordercolor="#CCCCFF" height="25" >
    <p align="center"><font face="Arial" size="4">Add</font>
	<span class="style5">Personnel</span></td>
    <td align = center height="25"><font face="Arial"><b><a href="P_List.asp">RETURN</a></b></font></td>

    <td align = center bgcolor="#FFFFFF" height="25">
  <Input name="Submit" type="submit" Value="Submit" style="float: right" ></td>
  </tr>
</table><br><br>
<div align="center">
<table cellpadding="0" cellspacing="0" style="width: 55%;" bordercolor="#808080"  height="10" class="style3">
    <tr>
<td align="left" style="height: 39" class="auto-style2" >
	<font face="Arial">Name   </font></td>

<td align="left" style="height: 39" class="auto-style2" >
	<font face="Arial">Job Title</font></td>

<td class="auto-style1" style="height: 39" >
	<strong>Logon ID</strong></td>   
   <td class="auto-style1" style="height: 39" >
	<strong>Badge</strong></td>   

   <td class="auto-style1" style="height: 39" >
	<strong>Delete</strong></td>   

  </tr>
  
    <tr>
    <td  bordercolor="#CCCCFF" height="47"  align="center" class="style4" >   <font face="Arial">	
		<input type="text" name="Name" size="35" value="<%= strName %>" style="width: 231px" tabindex="1">
</font></td>
    	  	
    <td  bordercolor="#CCCCFF" height="47"  align="center" class="style4" >   <font face="Arial">	
		<input type="text" name="Job" size="35" value="<%= strJob %>" style="width: 231px" tabindex="2"></font></td>
    	  	
   <td  bordercolor="#CCCCFF" height="47"  align="center" class="style6" >  	
     <font face="Arial">	
		<input type="text" name="BID" size="35" value="<%= strLogin %>" style="width: 79px; height: 22px;" tabindex="3"></font></td> 

   <td  bordercolor="#CCCCFF" height="47"  align="center" class="style6" >  	
     <font face="Arial">	
		<input type="text" name="Badge" size="35" value="<%= strBadge %>" style="width: 79px; height: 22px;" tabindex="3"></font></td> 

   <td  bordercolor="#CCCCFF" height="47"  align="center" class="style6" >  <input type="checkbox" name="Delete" value="ON">
     &nbsp;</td> 

  </tr>
 
  </table>
</div>



</form>
   
  

</body>
 <%

  
  Function SaveData()

strName = Replace(Request.form("Name"), "'", "''")   
strLogin = Request.form("BID") 
strBadge = request.form("Badge")
strJob = request.form("Job")

 
  
  strsql = "Insert into tbl_Stores_People  (HR_Name, Login_ID, Badge, Job_title) values '" & strName & "',  '" & strLogin & "',   '" & strbadge & "',   '" & strJob & "')" 
  	 set MyRec = new ASP_CLS_DataAccess
        MyRec.ExecuteSql strSql 
         Response.write ("strsql" & strsql)
         
          Response.redirect("P_List.asp")
  End Function
  
   %><!--#include file="footer.inc"-->