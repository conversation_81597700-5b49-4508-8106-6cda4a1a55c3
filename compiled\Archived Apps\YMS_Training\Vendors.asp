																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Vendor List </TITLE>

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->



<% Dim MyRec, strsql, strstartdate
strStartdate = formatdatetime(now(),2)



strsql = "SELECT * from tblVendors order by Company_name"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<style type="text/css">
.style1 {
	font-family: Arial;
	font-size: x-small;
}
</style>
</head>

<body>
B96138
<% if Session("EmployeeID") = "C97338"  or Session("EmployeeID") = "B96138" or Session("EmployeeID") = "B55548" or Session("EmployeeID") = "B38763"  or Session("EmployeeID") = "B53909"  or Session("EmployeeID") = "U04211"  then %>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>


<td align = left><b><font face="Arial">Vendor List</font></b></td>



</tr>
	    </table>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1 align = center>  
	 <tr class="tableheader">
<td>&nbsp;</td>

				<td  align = left width="25%">     <font face="Arial" size="2">	Vendor Name</font></td>
		<td class="style1"  > <font face="Arial" size="2">Email Address</font></td>
	

	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td> <font size="2" face="Arial">

<a href="Vendor_Edit.asp?id=<%= MyRec.fields("ID") %>">

Edit</a></td>

	<td  ><font size="2" face="Arial"><%= MyRec.fields("Company_name")%></font></td>
	
<td  ><font size="2" face="Arial"><%= MyRec.fields("Email_address")%>&nbsp;</font></td>
	
</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>
<% else %>
<p align="center"><font face="arial" size="3"><b>You do not have authorization to view this page</b></font></p>
<% end if %><!--#include file="Fiberfooter.inc"-->