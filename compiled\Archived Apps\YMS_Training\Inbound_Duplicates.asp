																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Duplicates</TITLE>


<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->


<% Dim MyRec, strsql, MyConn, strUserType, strDate
strdate = dateadd("d", -30, Date())



strsql = "SELECT tblDuplicates.* from tblDuplicates where Delivery_date > '" & strdate & "' ORDER by release" 
    Set MyRec = Server.CreateObject("ADODB.Recordset")
       MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>


<td align = center><b>
<font face="arial" size="4" >Duplicate Inbound Shipment Release Numbers</font></b></span></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=80% class = "tablecolor1" border=1 align = center>  
	 <tr class="tableheader">
	 

	<td>  <font face="Arial" size="2">Release Number</font></td>
<td>  <font face="Arial" size="2">Trailer</font></td>
<td>  <font face="Arial" size="2">Load_number</font></td>
	
		<td  >  <font face="Arial" size="2">Vendor</font></td>
<td  >  <font face="Arial" size="2">Delivery Date</font></td>

	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>

	<td  >      <font size="2" face="Arial">        <%= MyRec.fields("Release")%>&nbsp;</font></td>
		<td  >      <font size="2" face="Arial">        <%= MyRec.fields("Trailer")%>&nbsp;</font></td>
      	<td  >      <font size="2" face="Arial">        <%= MyRec.fields("Load_nbr")%>&nbsp;</font></td>
      		<td  >        <font size="2" face="Arial">        <%= MyRec.fields("Vendor")%>&nbsp;</font></td>
  
<td  >        <font size="2" face="Arial">        <%= MyRec.fields("Delivery_Date")%>&nbsp;</font></td>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
     MyRec.close
    %>
</table>
<!--#include file="Fiberfooter.inc"-->