
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">


<TITLE>Transfer from Rail Car to Truck</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

 <%    Dim objDAC, strSQL, MyRec, objMOC, rstFiber, strCID, rstFiberTrans

       Dim objGeneral, strDate, MyConn, strTCID

	
	
       set objGeneral = new ASP_CLS_General
  Call getData()   
if objGeneral.IsSubmit() Then

strCID = Request.form("CID")



	Response.redirect("TransRtoY.asp?id=" & strCID)
	Session("Trailer") = ""
	


End if
%>
<SCRIPT LANGUAGE="JavaScript">
function formHandler(Form1){
document.forms["Form1"].submit();
}
</SCRIPT>

<style type="text/css">
.style1 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
}
</style>
</head>

<body>
<form name="Form1" action="SelectTruckfromRail.asp" method="post" >

<p>&nbsp;</p>
<div align="center">
<table cellpadding="0"  cellspacing="0" bordercolor="#111111" width="60%" id="AutoNumber2" class="style1">

       <TD align = center>  <p>&nbsp;</p> <font face="Arial" size="3"><b>Select Rail Car to Transfer from :&nbsp;&nbsp;</b></font>
	&nbsp;&nbsp;&nbsp;
     <font face="Arial">
     <select name="CID">
 	<option value="" selected>Rail Car</option>
   				<% 	Do While Not rstFiber.EOF
					%>
            			
						<option VALUE="<%= rstFiber.fields("CID")%> ">
                				<%=rstFiber.fields("Trailer")%> - <%= rstFiber("Species") %></option>

					<% 
        					rstFiber.MoveNext 
        					Loop         				
        					rstFiber.close
					%>

     </select><font size="2"> </font></font>
		<br>
		<br>
     <font face="Arial">
     	<input type="submit" value="Continue" id=submit1 name=submit1></font><p>
		&nbsp;</p>
</tr>

</table>
</form>


<%

 Function GetData()
        set objMOC = new ASP_CLS_FIBER
    
    Set rstFiber = Server.CreateObject("ADODB.Recordset")
strsql2 = "SELECT CID, Trailer, Species from tblCars where Location = 'YARD' and Carrier = 'RAIL' order by Trailer"
     
    rstFiber.Open strSQL2, Session("ConnectionString"), adOpenDynamic

    End Function 
    


 %><!--#include file="Fiberfooter.inc"-->