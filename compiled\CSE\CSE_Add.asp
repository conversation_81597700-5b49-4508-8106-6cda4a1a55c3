<html>
<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_Procedure.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Add Energy Control Procedure</title>
<style>
<!--
table.MsoTableGrid
	{border:1.0pt solid windowtext;
	font-size:10.0pt;
	font-family:"Times New Roman";
	}
.style2 {
	text-align: center;
}
.auto-style1 {
	text-align: left;
}
.auto-style2 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
	background-color: #EDF7FE;
}
.auto-style3 {
	background-color: #EDF7FE;
}
.auto-style4 {
	text-align: left;
	background-color: #EDF7FE;
}
-->
</style>
</head>
<% dim strsql, MyRec, strid, strecp, strTask, objGeneral, strDescription, strLocation, strComments, strDate, strTeam, strAsset
Dim objEPS, rstTeam, rstWA, strArea, strWorkArea,  strSOP, strFunctional, strListone, strListtwo, strListthree, strType

			
Dim strBID, strE_name, strP_Date, strSpaceType
			
strBID = Session("EmployeeID")
strE_name = Session("Ename")

strSpaceType = "Space Evaluation"
strnow = dateadd("h", -5, now())
strDate = formatdatetime(strnow,2)
strP_date = formatdatetime(strnow,2)
 

set objGeneral = new ASP_CLS_General
  		 if objGeneral.IsSubmit = True Then
  		
 	
if isnull(Request.form("Description")) then
strDescription = ""
else
strDescription = Replace(Request.form("Description"), "'", "''") 
end IF

if isnull(Request.form("Location")) then
strLocation = ""
else
strLocation= Replace(Request.form("Location"), "'", "''") 
end if

if isnull(Request.form("Comments")) then
strComments = ""
else
strComments = Replace(Request.form("Comments"), "'", "''") 
end if

if isnull(Request.form("Functional_location")) then
strFunctional = ""
else
strFunctional= Replace(Request.form("Functional_location"), "'", "''") 
end if

if isnull(Request.form("Asset")) then
strAsset = ""
else
strAsset = Replace(Request.form("Asset"), "'", "''") 
end if

strWorkArea = Request.form("Workarea")

 strsql = "SELECT [WorkArea],[WA_Abb] FROM [WorkAreas] where WA_Abb = '" & strWorkArea & "'"		
    Set MyRec = Server.CreateObject("ADODB.Recordset") 
   	MyRec.Open strSQL, Session("ConnectionString")
 	
strWorkA = MyRec("Workarea")
MyRec.close

strT = request.form("Team")

strsql = "SELECT [VA_Team_Name]  FROM  [VA_Team_Names] where Team_Abb = '" & strT & "'"
    Set MyRec = Server.CreateObject("ADODB.Recordset") 
   	MyRec.Open strSQL, Session("ConnectionString")
   	strTeam = MyRec("VA_Team_Name")
   	MyREc.close



strType = "CSE"

 strsql =  "INSERT INTO tblSOP (Location_type,  Area,  AssignedAsset, Sdate, TeamName, WorkArea, Sdescription, Location, Comment, [Functional_location]) "_
 &" SELECT '" & strType & "',  '" & strArea & "', '" & strAsset & "',  "_
 &" '" & Request.form("Date") & "', '" & strTeam & "', '" & strWorkA & "', '" & strDescription & "', "_
 &" '" & strLocation & "', '" & strComments & "', '" & strFunctional & "'"
 Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 
			
			

strsql = "SELECT Max(tblSOP.SID) AS MaxOfSID FROM tblSOP "
Set MyRec = Server.CreateObject("ADODB.Recordset") 
MyRec.Open strSQL, Session("ConnectionString")
strid = MyRec.fields("MaxOfSID")
MyRec.close

'strSOP =  Replace(Request.form("SOP_NO"), "'", "''")

 'strNewID = Get the last 3 digits from SOP_NO with same Team and Work area and increase the number by 1.  Preceed with zeroes to always have 3 numbers after the dash.

'1.	Team/Product System = Fiber Utilities
'2.	Work Area/Machine = CHP Plant (suggestion:  remove plant from CHP in confined space system)
'3.	# (start with 001)  

strSOP =  Request.form("Team") & "-" & Request.form("Workarea") & "-" 
strsql = "select IsNULL(MAX(SOP_NO),0) as 'MaxSOPNO' FROM [cse0nedb].[dbo].[tblSOP] where Left(SOP_NO," & Len(strSOP) & ") = '" & strSOP & "'"
'Response.Write strsql & "<BR>"
MyRec.Open strSQL, Session("ConnectionString")
If NOT MyRec.EOF Then
    strSOPID = Replace(MyRec.fields("MaxSOPNO"),strSOP,"")
	strSOPID = CInt(strSOPID) + 1
End If
MyRec.Close
Set MyRec = Nothing
'Response.Write strSOP & Right("00" & strSOPID,3)
'Response.End
strSOP = strSOP & Right("00" & strSOPID,3)

strsql = "Update tblSOP set SOP_NO = '" & strSOP & "', SpaceStatus = 'In Use' where SID = " & strid & ""
Set MyRec = Server.CreateObject("ADODB.Connection")
MyRec.Open Session("ConnectionString")
MyRec.Execute strSQL
MyRec.Close 

strsql = "Insert into tblActionLog (SOP_ID, Type, BID, E_name, P_date) Select " & strid & ", "_
	&" '" & strSpaceType & "', '" & strBID & "', '" & strE_name & "', '" & strP_Date & "'"
Set MyRec = Server.CreateObject("ADODB.Connection")
MyRec.Open Session("ConnectionString")
MyRec.Execute strSQL
MyRec.Close 
 

Response.redirect("CSE_Add_Two.asp?id=" & strid)			

end if 

 %><body>
 <p align="left"><b><font face = arial size = 3>Add New Confined Space:</font></b><font face="Arial">
 </font>
 </p>
	<form name="form1" action="CSE_Add.asp"  method="post" ID="Form1"  >
 
 <table cellpadding="0" style="width: 50%;" bordercolor="#111111" height="60"   class="auto-style2">
   
  <tr>
    <td bordercolordark="#000000" bordercolor="#FFFFDD" class="auto-style4">
	<font face="Arial" size="2">Date</font></td>

   
    <td bordercolordark="#000000" bordercolor="#FFFFDD" class="auto-style4">
	<font face="Arial" size="2">Team</font></td>

   
    <td bordercolordark="#000000" bordercolor="#FFFFDD" class="auto-style4">
	<font face="Arial" size="2">Work Area</font></td>

 

   
  </tr>
  <tr>
    <td bordercolor="#FFFFDD" class="auto-style3"  >
  <font face = arial size = 2>
	<input type="text" name="Date" size="10" value="<%= strDate %>" ></td>

<%	strsql = "SELECT [VA_Team_Name],[Team_Abb] FROM [cse0nedb].[dbo].[VA_Team_Names] order by VA_Team_Name"
    Set MyRec = Server.CreateObject("ADODB.Recordset") 
   	MyRec.Open strSQL, Session("ConnectionString")
%>

    <td bordercolor="#FFFFDD" class="auto-style3"  >
	<p class="auto-style1">  <font face="Arial">    <select name="Team" tabindex="1">
       <option value="">--- All ---</option>
	   <%While NOT MyRec.EOF%>
       <option value="<%=MyRec.fields("Team_Abb")%>"><%=MyRec.fields("VA_Team_Name")%></option>
<%          MyRec.MoveNext
         Wend
         MyRec.Close
		 Set MyRec = Nothing
%>


     </select></font></td>
	
    <td bordercolor="#FFFFDD" class="auto-style3"  >
	<p class="auto-style1">  
		<font face="Arial">  
<%strsql = "SELECT [WorkArea],[WA_Abb] FROM [cse0nedb].[dbo].[WorkAreas] order by WorkArea"		
    Set MyRec = Server.CreateObject("ADODB.Recordset") 
   	MyRec.Open strSQL, Session("ConnectionString")
%>		
		<select name="Workarea" tabindex="2">
       <option value="">--- All ---</option>
	   <%While NOT MyRec.EOF%>
       <option value="<%=MyRec.fields("WA_Abb")%>"><%=MyRec.fields("WorkArea")%></option>
<%          MyRec.MoveNext
         Wend
         MyRec.Close
		 Set MyRec = Nothing
%>     </select></font></td>
 
 
   
  <tr>
    <td bordercolor="#FFFFDD" class="auto-style4" colspan="3">
	<font face="arial" size="2">Assigned Asset</font></td>

   
  </tr>
   
  <tr>
    <td bordercolor="#FFFFDD" class="auto-style4" colspan="3" style="height: 41px">
		<font face = arial size = 2>
	<input name="Asset" size="42" style="float: left" value="<%= strAsset %>" tabindex="6" ></td>

   
  </tr>
   
  <tr>
    <td bordercolor="#FFFFDD" class="auto-style4" colspan="3">
	<font face="Arial" size="2">Functional Location</font></td>

   
  </tr>
   
  <tr>
    <td align="center" bordercolor="#FFFFDD" class="auto-style3" colspan="3" style="height: 34px">
		<font face = arial size = 2>
	<input name="Functional_location" size="42" style="float: left" value="<%= strFunctional %>" tabindex="4" ></td>

   
  </tr>
   
  <tr>
    <td align="center" bordercolor="#FFFFDD" class="auto-style3" colspan="3">
	<p align="left">
	<font face="arial" size="2">Name of New Confined Space</font>&nbsp;</td>

   
  </tr>
  <tr>
    <td bordercolor="#FFFFDD" class="auto-style3" colspan="3"  >
	<p align="center" style="height: 29px"><font face="Arial" size="2">
	<input name="Description" size="87" style="float: left" value="<%= strDescription %>" tabindex="5" ></font>&nbsp;</td>
	
      
  <tr>
    <td align="left" bordercolor="#FFFFDD" class="auto-style3" colspan="3" >
	
	<font face="arial" size="2">Space Location</font></td>

   
  </tr>
  <tr>
    <td height="26" bordercolor="#FFFFDD" class="auto-style3" colspan="3"  >
	<font face="Arial" size="2">
	<input name="Location" size="87" style="float: left; height: 33px;" value="<%= strLocation %>" tabindex="7" ></font></td>
	
</tr>
  
  <tr>
    <td align="left" colspan = 3 bordercolor="#FFFFDD" class="auto-style3">
	
	<font face="arial" size="2">Comments</font></td>

   
  </tr>
  <tr>
    <td  bordercolorlight="#D9E1F9" colspan = 3 bordercolor="#FFFFDD" class="auto-style3"  >
	<font face="Arial" size="2">
	<input name="Comments" size="139" style="float: left; width: 756px; height: 38px;" value="<%= strComments %>" tabindex="8" ></font></td>
	    
  </table>
 

	<p>&nbsp;</p>
	


 <p><INPUT TYPE="submit" value="Continue"></p>
 </form>
<!--#include file="footer.inc"-->