																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META First_Name="robots" content="index,nofollow">
<META First_Name="Generator" CONTENT="Microsoft FrontPage 6.0">

<TITLE>EMS List</TITLE>
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_SessionStringBadge.asp"-->


<% Dim MyRec, strsql, MyConn, strsql2


strsql = "SELECT tblEMS.* FROM tblEMS order by display_name"
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionStringBadge")

%>

<style type="text/css">
.style1 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	text-align: left;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style3 {
	border: 1px solid #C0C0C0;
}
.style4 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style5 {
	font-weight: bold;
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
</style>
</head>

<body>
<br>

<% if Session("EMployeeID") = "C97338" or Session("EmployeeID") = "B54758" or Session("EmployeeID") = "B55556" or Session("EmployeeID") = "B44792" or Session("EmployeeID") = "B54577" then %>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>

<td align = left width =20%><font face="Arial"><a href="EMS_add.asp"<font face="arial" size="2" >Add New </a></font>&nbsp;</td>
<td align = center><b>
<font face="arial" size="4" >EMS / Fire Brigade List</font></b></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </td>



</tr>
<tr><td align = left colspan="2">&nbsp;</td>
</tr>
	    </table><br>
	
	
	<TABLE cellSpacing=0 cellPadding=0 class = "style3" align = center style="width: 50%">  
	 <tr class="tableheader">


	
	<td class="style5"  >  <font face="Arial">BID</font></td>
		<td class="style5"  >  <font face="Arial">Security ID</font></td>
		<td class="style5"  >  <font face="Arial">Name</font></td>
		<td class="style5"  >  <font face="Arial">Category</font></td>
			<td class="style5"  >  <font face="Arial">&nbsp;</font></td>	

	<td class="style5"  >  <font face="Arial">&nbsp;</font></td>	
		


	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr bgcolor="#D7D2B9">
    <% end if %>
      
	

     	<td class="style4"  > <font size="2" face="Arial"> <%= MyRec.fields("EID")%>&nbsp;</font></td>
 <td class="style4"  > <font size="2" face="Arial"> <%= MyRec.fields("S_EID")%>&nbsp;</font></td>

        		
	
        			<td class="style4"  >  <font size="2" face="Arial"><%= MyRec.fields("Display_Name")%>&nbsp;</font></td>	
        		
        <td class="style4"  >  <font size="2" face="Arial"><%= MyRec.fields("Category")%>&nbsp;</font></td>	

       	<td align="center" class="style4"> <font size="2" face="Arial">&nbsp;<a href="Ems_Edit.asp?id=<%= MyRec.fields("ID") %>">Edit</a>&nbsp;</td>	
 

	<td align="center" class="style4"> <font size="2" face="Arial">&nbsp;<a href="Ems_Delete.asp?id=<%= MyRec.fields("ID") %>">Delete</a>&nbsp;</td>	

	
	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
     MyRec.close
    %>
</table>
<% else %>
<p align="center"><font face="arial" size="3" color="red"><b>You do not have authorization to view this page.</b></font></p>
<% end if %><!--#include file="footer.inc"-->