<html xmlns:m="http://schemas.microsoft.com/office/2004/12/omml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 

 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Enter Trailer Receipt</title>
<style type="text/css">
.style1 {
	text-align: left;
	font-family: Arial;
	font-size: small;
	border-width: 1px;
	background-color: #E3EEFD;
}
.style3 {
	font-family: Arial;
	font-size: small;
	border-width: 1px;
	background-color: #E3EEFD;
}
.style8 {
	font-size: x-small;
}
.style10 {
	font-weight: bold;
	border-width: 1px;
	background-color: #E3EEFD;
}
.style18 {
	color: #000000;
}
.style28 {
	color: #9F112E;
}
.style31 {
	font-family: Arial;
	font-size: x-small;
	color: #008080;
	background-color: #E3EEFD;
}
.style32 {
	background-color: #E3EEFD;
}
.style33 {
	font-weight: bold;
	background-color: #E3EEFD;
}
.style35 {
	font-weight: bold;
	background-color: #FFFFFF;
}
.style36 {
	border-width: 1px;
	background-color: #E3EEFD;
}
.style37 {
	font-weight: bold;
	background-color: #FFFFFF;
	font-size: x-small;
}
.style38 {
	background-color: #E3EEFD;
	text-align: right;
}
.style39 {
	font-weight: bold;
}
</style>
</head>
<%  strid = request.querystring("id")
strPage = request.querystring("p")
 


 	set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then
 
   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
	
  
 

	

				Call SaveData() 
				if request.querystring("p") = "vw" then 
				  Response.redirect ("Vendor_Weight_Exceptions.asp")
				else
		 
  Response.redirect ("Edit_Yard_Trucks.asp")
  end if
	 	
 
else
Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to save this information.</font></br>")
MyRec.close
end if
 
  
ELSE
  
	 
 strsql = "Select tblCars.* from tblCars where CID = " & strid
 
 	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
   	 if not MyRec.eof then

   	 
    strReleaseNbr  = MyRec("Release_nbr")
	strSpecies = MyRec("Species")
	strTrailer = MyRec("Trailer")
	strCarrier = MyRec("Carrier")
	strbalesRF = MyRec("Bales_RF")
	strDateReceived = Myrec("Date_Received")
	strSAP = MyREc("SAP_DOC_ID")
	strTrailerTID = MyREc("Trailer_TID")
	strGenerator = MyRec("Generator")
	strGenCity = MyRec("Gen_City")
	strGenState = MyREc("Gen_State")
	strOther = MyRec("Other_Comments")
	strTonsReceived = MyRec("Tons_Received")
	strGrossWeight = MyRec("Gross_Weight")
	strTareWeight = MyRec("Tare_weight")
	strCTPounds = strTareWeight
	gSpecies = MyRec("Species")
	gSAPNbr = MYRec("SAP_Nbr")
	strDOT = MyRec("Audit_Tons")
	strFormat = MyRec("Format_PKG")
	end if
	MyRec.Close
	
	strTrailerWeight = 14460

   	 strSQL3 = "Select weight from tblCarrier   where Carrier = '" & strCarrier & "'"

   	 Set MyConn3 = Server.CreateObject("ADODB.Recordset")
   	 MyConn3.Open strSQL3, Session("ConnectionString")
   	 if not MyConn3.eof then
   	 
   	 strTrailerWeight = MyConn3.fields("Weight")
   	 end if
   	 MyConn3.close
   	 
   	 if len(strTrailerweight) > 3 then
   	 ' do nothing
   	 else
   	 strTrailerWeight = 14460
   	 end if
   	 
   	 if strGrossweight > 0 and strTrailerWeight > 0 and strTonsReceived > 0 then 
   strCpounds = round(strGrossWeight - strTrailerweight - (strTonsReceived*2000),0)
   end if
 



end if

 set objMOC = new ASP_CLS_FIBER
        set rstFiber = objMOC.FiberCarrier()
        set rstTrailer = objMOC.TrailerOptions()

%>



<body>

 
<p align = center><font face = arial size = 4 color="teal"><b>

<font face = arial size = 3 color = red>
<span class="style28">
 
 

 
<table width = 100%><tr><td width = 33% style="height: 24px"> <font face="Arial" size="2"><b>Species: <%=gSpecies%>&nbsp;&nbsp; SAP Nbr: <%= gSAP_Nbr%></b></td>
<td align = center width = 34% style="height: 24px"><b><font face="Arial" size="4">
Enter Trailer/Rail Receipt for Release Number:&nbsp;<b><%= strReleaseNbr%></b></font> </b></td>
	<td align = right width = 33% style="height: 24px"> <a href="javascript:history.go(-1);"><b><font face = arial size = 2>RETURN</a></font></b></td></tr></table>




<form name="form1" action="Receipt_Release_Edit.asp?id=<%= strid %>&p=<%=strPage %>" method="post">
<input type = hidden name = "Bales" value = <%= strBales %>>
 
<input type = hidden name = "UOM" value = <%= strUOM %>>
 

<div align="center">
<table border="1" cellspacing="0" width="70%" bgcolor="#FFFFEA" style="border-collapse: collapse" cellpadding="0" bordercolorlight="#C0C0C0">
<tr>
    <td width="17%" class="style32">&nbsp;</td>

    <td colspan="4" class="style32">&nbsp;</td>
  </tr>
  <tr>
    <td  align = right width="17%" class="style33" >
   <font face="Arial" size="2">Trailer/Rail Car #:&nbsp;</font></td>
<td  align = left colspan="3" class="style32">

      <font face="Arial">

      <input name="Trailer" size="15" value = "<%= strTrailer%>" style="font-weight: 700"><b>
		</b>&nbsp;&nbsp;</font></td>
<td  align = left width="29%" class="style32"> &nbsp;</td></tr>

  <tr>

      <td align = right width="17%" class="style36">
	<span class="style34"><strong><span class="style8">Vendor Weight:</span> </strong></span>&nbsp;</td>
<td  align = left colspan="4" class="style36">

      <font face="Arial" size="4" color="teal">

      <input name="DOT" size="15" value = "<%= strDOT%>" style="font-weight: 700" tabindex="2">&nbsp;<span class="style37"><strong>(lbs) </strong></span>
		&nbsp;<span class="style13"><span class="style33"><span class="style8">
		</span> </span></span>
		<span class="style32"><strong><span class="style13">
		<span class="style8">Required&nbsp;
		</span>
		</span></strong></span></font></td>
</tr>

  <tr>

      <td align = right width="17%" class="style33">
	<font face="Arial" size="2">Select Carrier:</font></td>
<td  align = left colspan="4" class="style32">

 <select name="Carrier" style="font-weight: 700">
 	<option value="" selected>  Select Carrier (Required)</option>
 	<% strsql = "Select Carrier,C_Description, Weight from tblCarrier order by Carrier"
 	   	 Set MyRec4 = Server.CreateObject("ADODB.Recordset")
   	 MyRec4.Open strSQL, Session("ConnectionString")
   	 while not MyRec4.eof %>
   	 <option <% if strCarrier = MyRec4("Carrier") then %> selected <% end if %> value="<%= MyRec4("Carrier") %>"><%= MyRec4("Carrier") %>&nbsp;<%= MyRec4("C_Description") %>&nbsp;<%= MyRec4("Weight") %></option>"

 <%MyRec4.Movenext
 wend
 MyRec4.close %>
     </select> </td>
</tr>
 
  <tr>

      <td align = right width="17%" class="style31">
	<span class="style18"><strong>Format:</strong></span>:</td>
 
<td  align = left colspan="4" class="style32">
    <select name="Format" style="font-weight: 700" size="1">

	<option selected value="">  Select</option>
 	<option  <% if strFormat = "Bales" then %> selected <% end if %>>  Bales</option>
<option  <% if strFormat = "Rolls" then %> selected <% end if %>>Rolls</option>
<option <% if strFormat = "Gaylords" then %> selected <% end if %>>Gaylords</option>

 	</select>

		&nbsp;</td>
</tr>
 
  <tr>

      <td align = right width="17%" class="style31">
	<span class="style18"><strong>Bales:</strong></span>:</td>
 
<td  align = left colspan="4" class="style32">

		<font face="Arial" size="4" color="teal">  
	<input name="BalesRF" size="15" value = "<%= strBalesRF%>" style="float: left; width: 55px;"></td>
</tr>
<tr>
    <td width="17%" class="style3">  
	<strong><span class="style8">Trailer Weight Entry</span></strong></td>
    <td colspan="4" class="style1">   </td>
  </tr>

 


<tr>
	<td height="22" width="17%" class="style10">
	<p align="right"><font face="Arial" size="2">Scale Gross:</font></td>
    <td height="22" class="style32" style="width: 19%">    <font face="Arial">  
	<input name="Gross" size="15" value="<%= strGrossWeight%>" style="width: 105px"> 
	<span class="style8"><strong>lbs</strong></span></td>
    <td colspan="3" height="22" class="style33">    
   <font face="Arial" size="2"  >  Scale out (Cab Only):&nbsp;
	<input name="C_Pounds" size="15" value = "<%= strCPounds%>" style="width: 75px"  >lbs&nbsp;&nbsp;&nbsp; OR&nbsp;&nbsp;&nbsp;&nbsp; 
Scale out (Cab and Trailer):&nbsp; 
 
	<input name="CT_Pounds" size="15" value = "<%= strCTPounds%>" style="width: 78px"  >lbs</td>
  </tr>

 
       <tr><td  align = right width="17%" class="style33" ><font face="Arial" size="2">Date Received:&nbsp;</font></td>
<td align = left colspan="2" class="style32"> <font face="Arial"> 
<input name="Date_Received" size="15" value = "<%= strDateReceived%>" style="font-weight: 700"></font></td>
<td align = left width="18%" class="style32"> 
<p align="right"><font face="Arial" size="2">
<b>SAP DOC ID:</b>

      </td>
<td align = left width="29%" class="style32">    <font face="Arial" size="2">

      <input name="SAP_DOC_ID" size="16" value = "<%= strSAP %>" style="font-weight: 700"></font></td></tr>
              <tr>
          <td  align = right width="17%" class="style33" >
   <font face="Arial" size="2">Generator:&nbsp;</font></td>
<td align = left colspan="4" class="style32">
      <font face="Arial">
      <input name="Generator" size="25" value = "<%= strGenerator %>" style="font-weight: 700"></font></TD></tr>
        <tr>
          <td  align = right height="28" width="17%" class="style33" >
    <font face="Arial" size="2">Generator City:&nbsp;</font></td>
<td align = left colspan="4" height="28" class="style32">
      <font face="Arial">
      <input name="Gen_City" size="15" value ="<%= strGenCity%>" style="font-weight: 700; width: 201px;"></font></td></tr>

           <tr>
          <td align = right width="17%" class="style33" >
   <font face="Arial" size="2">Generator State:&nbsp;</font></td>
<td align = left colspan="4" class="style32">
      <font face="Arial">
      <input name="Gen_State" size="15" value = "<%= strGenState%>" style="font-weight: 700"></font></td></tr>
         <tr>
          <td  align = right width="17%" class="style33" >
  <font face="Arial" size="2">Other:&nbsp;</font></td >
   <td align = left colspan="4" class="style32">   <font face="Arial">   
	<input name="Other_Comments" size="25" value = "<%= strOther%>" style="font-weight: 700">&nbsp;&nbsp;&nbsp;
	<font size="2">&nbsp;Location: </font>   
	<select name="Location" style="font-weight: 700" size="1">

      <option selected>YARD</option>
	
 
     </select></font></td></tr>
     <% if request.querystring("p") = "vw" then %>
     <tr>
    <td width="17%" class="style32"></td>

    <td colspan="4" class="style36"></td>
  </tr>

<tr>
    <td width="17%" class="style38">
  <font face="Arial" size="2">
<span class="style39">
 
 

 
	Secondary Weight:</font></td>

    <td colspan="4" class="style36"> 
 

      <input name="Secondary" size="15" value = "<%= strSecondary %>" style="font-weight: 700" tabindex="2"> </td>
  </tr>
<% end if %>
<tr>
    <td width="17%" class="style38">
  <font face="Arial" size="2">
<span class="style39">
 
 

 
	Floor Scale Weight:</font></td>

    <td colspan="4" class="style36"> 
 

      <input name="Floor" size="15" value = "<%= strFloor%>" style="font-weight: 700" tabindex="2"> </td>
  </tr>

 
    
  <tr>
  

    <td width="17%" class="style36">&nbsp;</td>

    <td align = left colspan="4" class="style36"><font face="Arial">
	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" >&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </font></td>
  </tr>
</table>

 

</form>
 
<%  

Function SaveData()
 
 
If len(request.form("CT_Pounds")) > 2 then
strCTPounds = request.form("CT_Pounds")
	strGrossWeight = Request.form("Gross")

	strTonsReceived = round((strGrossWeight - strCTPounds)/2000,3)

	strPounds = round((strGrossWeight - strCTPounds),3)
		strTareWeight =  strCTPounds
 
	strNet = strTonsReceived
	strTrailerTID = 0

	elseif 	len(request.form("C_Pounds")) > 2 then
	strCPounds = Request.form("C_Pounds")
	
	strTrailerWeight = 14460

   	 strSQL3 = "Select weight from tblCarrier   where Carrier = '" & strCarrier & "'"

   	 Set MyConn3 = Server.CreateObject("ADODB.Recordset")
   	 MyConn3.Open strSQL3, Session("ConnectionString")
   	 if not MyConn3.eof then
   	 
   	 strTrailerWeight = MyConn3.fields("Weight")
   	 end if
   	 MyConn3.close
   	 
   	 if len(strTrailerweight) > 3 then
   	 ' do nothing
   	 else
   	 strTrailerWeight = 14460
   	 end if
   	 
   	 
	strTonsReceived = round((Request.form("Gross") - strCPounds - strTrailerweight)/2000,3)
	strPounds = round((Request.form("Gross") - strCpounds - strTrailerweight),3)
		strTareWeight =  strTrailerweight + strCpounds
	strGrossWeight = Request.form("Gross")
	strNet = strTonsReceived
	 end if
	 
	   	 	strTrailerTID = 0
 
 
 	strTrailer = Request.form("Trailer")
	strOther = Replace(Request.form("Other_Comments"), "'", "''") 
	strDateReceived = Request.form("Date_received")
	
	strCarrier = Request.form("Carrier")
	strLocation = Request.form("Location")

	strGenerator = Replace(Request.form("Generator"), "'", "''")
	strGenCity = Replace(Request.form("Gen_City"), "'", "''")
	strGenState = Request.form("Gen_State")
	 
	 
		strCTPounds = request.form("CT_Pounds")


	
	

	
	if len(request.form("SAP_DOC_ID")) > 2 then
	strSAP = Request.form("SAP_DOC_ID")
	else
	strSAP = ""
	end if
 
	
 
	
		If len(request.form("BalesRF")) > 0 then
	strBalesRF = request.form("BalesRF")
	else
	strBalesRF = 0
	end if

	 
	
	 
 strsql = "Update tblCars set Date_Received = '" & strDateReceived & "', Bales_RF = " & strBalesRF & ",   Trailer_TID = " & strTrailerTID & ",   SAP_DOC_ID = '" & strSAP & "', "_
 &"  Carrier = '" & strCarrier & "',  Generator = '" & strGenerator & "', Gen_City = '" & strGencity & "', "_
 &" Gen_State = '" & strGenstate & "',Trailer = '" & strTrailer & "', Other_Comments = '" & strOther & "', Tons_Received = " & strTonsReceived & ", "_
 &"  Net = " & strTonsReceived & ", Gross_weight = " & strGrossWeight & ", Tare_weight = " & strTareWeight & ", Format_Pkg = '" & request.form("Format") & "' where CID = " & strid
	
 
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			
 'Response.write ("strsql  " & strsql)
		 	MyConn.Execute strSQL
			MyConn.Close
			
			 
 		if len(Request.form("DOT")) > 0 then
		strDOT = request.form("DOT")
				strsql = "Update tblCars set Audit_Tons = " & strDOT & "  where CID = " & strid
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
	end if


 		if len(Request.form("Floor")) > 0 then
		strFloor = request.form("DOT")
				strsql = "Update tblCars set Floor_Scale_Weight = " & strFloor & "  where CID = " & strid
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
	end if

  		if len(Request.form("Secondary")) > 0 then
		strSEcondary = request.form("Secondary")
				strsql = "Update tblCars set Secondary_Weight = " & strSecondary & "  where CID = " & strid
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
	end if




End Function %><!--#include file="Fiberfooter.inc"-->