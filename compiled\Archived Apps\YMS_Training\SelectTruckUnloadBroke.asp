
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">


<TITLE>Move Trailer of BROKE</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 

 <%    Dim objDAC, strSQL, MyRec, objMOC, rstFiber, strCID, rstFiberTrans, rstNF, strNFCID

       Dim objGeneral, strDate, MyConn, strTCID

	strCID = ""
	strNFID = ""
i = 0
	
       set objGeneral = new ASP_CLS_General

if objGeneral.IsSubmit() Then
dim i
i = 0
if len(Request.form("KCOP")) > 1 then
i = i + 1
strnfid = Request.form("KCOP")
end if

if len(request.form("Shuttle")) > 1 then
i = i + 1
strNFID = Request.form("Shuttle")
end if



if i > 1 then
Response.write ("<br><font face = arial size = 3 color = red><b>Please select only one Trailer.</b></font>")
elseif i = 0 then
Response.write ("<br><font face = arial size = 3 color = red><b>Please select a Trailer.</b></font>")
else

	Response.redirect("Out_Broke_Trailer.asp?id=" & strNFID)
end if

end if


%>
<SCRIPT LANGUAGE="JavaScript">
function formHandler(Form1){
document.forms["Form1"].submit();
}
</SCRIPT>

<style type="text/css">
.style3 {
	border: 1px solid #800000;
	border-collapse: collapse;
}
.style4 {
	text-align: center;
}
.style2 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	text-align: center;
}
.style5 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	background-color: #EFECFF;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	text-align: center;
}
</style>
</head>

<body>
<form name="form1" action="SelectTruckUnloadBroke.asp" method="post" >
<br><br>
<p align="center"><font face="Arial"><b>MOVE TRAILER
OF BROKE</b></font></p><br><br>
<table cellpadding="2" class = "style3" style="width: 75%;" align="center">

       <TD align = center align="center" bgcolor="#FFFFE8" >  <font face="Arial" size="3"><b>Select Inbound Trailer&nbsp;</b></font></td>
	<td  bgcolor="#FFFFE8" class="style4"> <font face="Arial" size="3"><b>&nbsp;Select Shuttle 
	to Yard</b></font></td>
	<td  bgcolor="#FFFFE8" class="style4"> &nbsp;</td></tr>
	<td class="style5">		
       
      <select name="KCOP">
 	<option value="" selected>Trailer Number</option>
    <% strsql = "Select CID, Trailer from tblCars where Grade = 'BROKE' and Location = 'YARD' and Trailer <> 'UNKNOWN'"
            Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
			If not MyRec.eof then
			while not MyRec.eof 
			%>
			<option value="<%= MyRec.fields("CID") %>"><%= MyRec.fields("Trailer") %></option>
<% MyRec.movenext
wend
end if
MyRec.close %>
     </select>&nbsp;&nbsp;
		</td>
		<Td bgcolor = "#EFECFF" class="style2">		
       
     <select name="Shuttle" size="1">
 	
 	<option value="" selected>Trailer Number</option>
    <% strsql = "Select CID, Transfer_Trailer_Nbr from tblCars where Species = 'BROKE' and Location = 'YARD'  and Trailer = 'UNKNOWN'"
            Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
			If not MyRec.eof then
			while not MyRec.eof 
			%>
			<option value="<%= MyRec.fields("CID") %>"><%= MyRec.fields("Transfer_Trailer_Nbr") %></option>
<% MyRec.movenext
wend
end if
MyRec.close %>
     </select>&nbsp;&nbsp;</b></font></td>
		<Td bgcolor = "#EFECFF" class="style2">		
       
     &nbsp;</td></tr>
        <tr><td class="style5">
   <b><font face="Arial" size="2">&nbsp;</font></b><font face="Arial"><input type="submit" value="Continue" id=submit2 name=submit2></td>


<Td class="style5">
<input type="submit" value="Continue" id=submit3 name=submit3></Td>


<Td class="style5">
&nbsp;</Td>

</tr>

</table>
<br><br><br>
</form>


<!--#include file="Fiberfooter.inc"-->
