																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>RF Vendors</TITLE>

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->



<% Dim MyRec, strsql, strVendor, strSource, strCity, strSTate, strGrade, strTier, strStatus
strVendor = request.form("Vendor")
strSource = request.form("Source")
strCity = request.form("City")
strState = request.form("State")
strSite = request.form("Site")
strGrade = request.form("Grade")
strStatus = request.form("Status")
 


' <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>("EmployeeID") = "B02867"  or session("EmployeeID") = "B28054" or Session("EmployeeID") = "U04211"  or Session("EmployeeID") =  "B96138" or Session("EmployeeID") = "C28802" then
strAdmin = "OK"
end if

 ' Andres, Stephen, Debbie Morris, Sandra Wallace
If Session("EmployeeID") = "B53909" or Session("EmployeeID") = "B88851" or Session("EmployeeID") = "C97338" or Session("EmployeeID") = "B65156" or Session("EmployeeID") = "B54396" then
strAdmin = "OK"
end if

 

%>

<style type="text/css">
.style2 {
	border-style: solid;
	border-width: 1px;
}
.style3 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 2px;
	background-color: #FFFFDD;
}
.style6 {
	border: 1px solid #808080;
	font-family: arial;
	font-size: x-small;
}
.style4 {
	border: 1px solid #808080;
}
.style9 {
	border: 1px solid #808080;
	text-align: center;
}
.style12 {
	text-align: center;
}
.style13 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.auto-style1 {
	border: 1px solid #808080;
	font-family: arial;
	font-size: x-small;
	background-color: #E2F3FE;
}
.auto-style2 {
	border: 1px solid #C0C0C0;
}
.auto-style3 {
	border: 1px solid #C0C0C0;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.auto-style4 {
	text-align: center;
	border: 1px solid #C0C0C0;
}
.auto-style5 {
	border: 1px solid #C0C0C0;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	text-align: center;
}
.auto-style6 {
	border: 1px solid #C0C0C0;
	text-align: left;
}
</style>
</head>

<body>

<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>
<tr>

<td align = left><b><font face="Arial">Vendor Tier Rating</font></b></td>
<% if strAdmin = "OK" then %>
<td align = right><a href="RF_Vendor_Excel.asp"><font face="Arial">Export to Excel</a></td>
<td align = right><font face="Arial"><a href="RF_Vendor_add.asp"><b>Add New</b></a>&nbsp;</td>
<% end if %>
 

</tr>
	    </table>

 <form name="form1" action="RF_Vendor.asp"  method="post" ID="Form1"  > 
 
 <table cellpadding="0" bordercolor="#111111" align="center" class="style3" style="width: 100%">
   
  <tr>

    <td align="center" class="auto-style1" style="height: 22">
	Vendor</td>

    <td align="center" class="auto-style1" style="height: 22">
	Source</td>

    <td align="center" class="auto-style1" style="height: 22">
	City</td>

   
    <td align="center" class="auto-style1" style="height: 22">
	State</td>

   
    <td align="center" class="auto-style1" style="height: 22">
	Grade</td>

   
    <td align="center" class="auto-style1" style="height: 22">
	Site</td>
	
    <td align="center" class="auto-style1" style="height: 22">
	Status</td>
   
  <td align="center" class="auto-style1" style="height: 22">
	 </td>

   
  </tr>
  <tr>
 
    <td  bgcolor="#FFFFFF" style="height: 25px" class="style4" align="center"  >
    <select name="Vendor">
    <option value="">--Select--</option>

      <% strsql = "SELECT Distinct Vendor from tblTier order by Vendor"
	Set MyRec2 = Server.CreateObject("ADODB.RecordSet")
	MyRec2.Open strSQL,   Session("ConnectionString")
	While not MyRec2.eof
 %>
 <option <% If strVendor = MyRec2("Vendor") then %> selected <% end if %> ><%= MyRec2("Vendor") %></option>
 <% MyRec2.movenext
 wend
 MyRec2.close %>
</select>
	&nbsp;</td>
 
    <td  bgcolor="#FFFFFF" style="height: 25px" class="style4" align="center">  <font face="Arial" size="2">
       <select name="Source">
       <option value="">--Select--</option>

      <% strsql = "SELECT Distinct Generator from tblTier order by Generator"
	Set MyRec2 = Server.CreateObject("ADODB.RecordSet")
	MyRec2.Open strSQL,   Session("ConnectionString")
	While not MyRec2.eof
 %>
 <option <% If strSource = MyRec2("Generator") then %> selected <% end if %> ><%= MyRec2("Generator") %></option>
 <% MyRec2.movenext
 wend
 MyRec2.close %>
</select>
	&nbsp;</td>
 
    <td  bgcolor="#FFFFFF" style="height: 25px" class="style9"  >
	<p align="center" class="style12"><font face="Arial" size="2">
<select name="City">
<option value="">--Select--</option>
      <% strsql = "SELECT Distinct City from tblTier order by City"
	Set MyRec2 = Server.CreateObject("ADODB.RecordSet")
	MyRec2.Open strSQL,   Session("ConnectionString")
	While not MyRec2.eof
 %>
 <option <% If strCity = MyRec2("City") then %> selected <% end if %> ><%= MyRec2("City") %></option>
 <% MyRec2.movenext
 wend
 MyRec2.close %>
</select>
	&nbsp;</td>
	
    	
    <td  bgcolor="#FFFFFF" style="height: 25px" class="style9"  >
	<font face="Arial" size="2">
<select name="State">
<option value="">--Select--</option>
      <% strsql = "SELECT Distinct State from tblTier order by State"
	Set MyRec2 = Server.CreateObject("ADODB.RecordSet")
	MyRec2.Open strSQL,   Session("ConnectionString")
	While not MyRec2.eof
 %>
 <option <% If strState = MyRec2("State") then %> selected <% end if %> ><%= MyRec2("State") %></option>
 <% MyRec2.movenext
 wend
 MyRec2.close %>
</select>
	&nbsp;</td>
	

    
    <td  bgcolor="#FFFFFF" style="height: 25px" class="style9"  >	<font face="Arial" size="2">
	<select name="Grade">
	<option value="">--Select--</option>

      <% strsql = "SELECT Distinct Grade from tblTier order by Grade"
	Set MyRec2 = Server.CreateObject("ADODB.RecordSet")
	MyRec2.Open strSQL,   Session("ConnectionString")
	While not MyRec2.eof
 %>
 <option <% If strGrade = MyRec2("Grade") then %> selected <% end if %> ><%= MyRec2("Grade") %></option>
 <% MyRec2.movenext
 wend
 MyRec2.close %>
</select>
	&nbsp;</td>
	
    	
    
    <td  bgcolor="#FFFFFF" style="height: 25px" class="style9"  >	
	<select name="Site">
	<option value=""> Select </option>
<option <% if strSite = "LDN" then %> selected <% End if %>>LDN</option>
<option <% if strSite = "MOB" then %> selected <% End if %>>MOB</option>
<option <% if strSite = "OWB" then %> selected <% End if %>>OWB</option>
</select>
	&nbsp;</td>
	
    <td  bgcolor="#FFFFFF" style="height: 25px" class="style9"  >	
	<select name="Status">
 	  <option <% If strStatus = "Y" then %> selected <% end if %> value="Y" >Active</option>
	  <option <% If strStatus = "N" then %> selected <% end if %> value="N">Inactive</option>
	</select>
	&nbsp;</td>
    	
    
    <td  bgcolor="#FFFFFF" style="height: 25px" class="style9"  >	<INPUT TYPE="submit" value="Submit"></td>
	
    	
    
    </table> 
    </form>
    
    
    
    <%
set objGeneral = new ASP_CLS_General
  		 if objGeneral.IsSubmit = True Then
strVendor = request.form("Vendor")
strSource = request.form("Source")
strCity = request.form("City")
strState = request.form("State")
strGrade = request.form("Grade")
strStatus = request.form("Status")

strsql = "SELECT tblTier.* from tblTier where ID > 0 and Active = '" & strStatus & "'"

if len(strVendor) > 1 then
strsql = strsql & " and Vendor = '" & strVendor & "' "
end if

if len(strSource) > 1 then
strsql = strsql & " and Generator = '" & strSource & "' "
end if

if len(strCity) > 1 then
strsql = strsql & " and city= '" & strcity & "' "
end if

if len(strState) > 1 then
strsql = strsql & " and State = '" & strState & "' "
end if

if len(strGrade) > 1 then
strsql = strsql & " and Grade = '" & strGrade & "' "
end if

if Request.form("Site") = "LDN" then
strsql = strsql & " and LDN = 'Y' "
end if

if Request.form("Site") = "MOB" then
strsql = strsql & " and MOB = 'Y' "
end if

if Request.form("Site") = "OWB" then
strsql = strsql & " and OWB = 'Y' "
end if


strsql = strsql & " order by Vendor, Generator, Grade"


    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>
&nbsp;<TABLE borderColor=white cellSpacing=0 cellPadding=0 class = "style2" align = center style="width: 100%">  
	 <tr class="tableheader">
<td>&nbsp;</td>

				<td  align = left class="auto-style2">     <font face="Arial" size="2">	Vendor</font></td>
				<td  align = left class="auto-style2">     <font face="Arial" size="2">	Source</font></td>
				<td  align = left class="auto-style2">     <font face="Arial" size="2">	City</font></td>
				<td  align = left class="auto-style2">     <font face="Arial" size="2">	State</font></td>
				<td  align = left class="auto-style3">     Contact</td>
				<td  align = left class="auto-style3">     Contact Phone</td>
				<td  align = left class="auto-style3">     Contact Email</td>
				<td class="auto-style5">     Ldn</td>
				<td class="auto-style5">     Mob</td>
				<td class="auto-style5">     Own</td>
				<td  align = left class="auto-style2">     <font face="Arial" size="2">	Grade</font></td>
					<td  align = center class="auto-style2">     <font face="Arial" size="2">	Tier</font></td>
					<td  align = center class="auto-style3">     Test Required</td>
			<td  align = center class="auto-style2">     <font face="Arial" size="2">	Active</font></td>
	<td  align = left class="auto-style2">     <font face="Arial" size="2">	Comments</font></td>
	<td class="auto-style5">  <font face="Arial" size="2">	   Contract</td>
	<td class="auto-style5">  <font face="Arial" size="2">	  Yard Access</td>
<td class="auto-style5">  <font face="Arial" size="2">Release Submission</td>
	<td class="auto-style4">     <font face="Arial" size="2">	Monthly Volume</font></td>
	<td class="auto-style4">     <font face="Arial" size="2">UOM</font></td>

	<td  align = left class="auto-style2">     <font face="Arial" size="2">	Freight</font></td>

	<td class="auto-style4">  <font face="Arial" size="2">Do not Push</font></td>
		<td align = right>&nbsp;</td>
	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td> <font size="2" face="Arial">
<% if strAdmin = "OK" then %>
 

<a href="RF_Vendor_Edit.asp?id=<%= MyRec.fields("ID") %>">Edit</a>
 
<% end if %>
&nbsp;</td>

	<td class="auto-style2"  ><font size="2" face="Arial"><%= Trim(MyRec.fields("Vendor"))%></font></td>
		<td class="auto-style2"  ><font size="2" face="Arial"><%= Trim(MyRec.fields("Generator")) %></font></td>
			<td class="auto-style2"  ><font size="2" face="Arial"><%= MyRec.fields("City")%></font></td>
				<td class="auto-style2"  ><font size="2" face="Arial"><%= MyRec.fields("State")%></font></td>
				<td class="auto-style6"  ><font size="2" face="Arial"><%= MyRec.fields("Contact")%></font>&nbsp;</td>
						<td class="auto-style6" ><font size="2" face="Arial"><%= MyRec.fields("Contact_Phone")%></font>&nbsp;</td>
						<td class="auto-style6" ><font size="2" face="Arial"><%= MyRec.fields("Contact_Email")%>&nbsp;</font></td>
					<td class="auto-style4"  ><font size="2" face="Arial"><%= MyRec.fields("LDN")%></font>&nbsp;</td>

	<td class="auto-style4"  ><font size="2" face="Arial"><%= MyRec.fields("MOB")%></font>&nbsp;</td>

				<td class="auto-style4"  ><font size="2" face="Arial"><%= MyRec.fields("OWB")%></font>&nbsp;</td>

					<td class="auto-style2"  ><font size="2" face="Arial"><%= MyRec.fields("Grade")%></font>&nbsp;</td>
						<td align="center" class="auto-style2" ><font size="2" face="Arial"><%= MyRec.fields("Tier")%></font>&nbsp;</td>
						<td align="center" class="auto-style2" ><font size="2" face="Arial"><%= MyRec.fields("Test")%>&nbsp;</font></td>
						<td  align="center" class="auto-style2" ><font size="2" face="Arial"><%= MyRec.fields("Active")%></font></td>
								<td class="auto-style2"  ><font size="2" face="Arial"><%= MyRec.fields("Comments")%></font>&nbsp;</td>
								<td class="auto-style4"  ><font size="2" face="Arial"><%= MyRec.fields("Contract")%></font>&nbsp;</td>

								<td class="auto-style4"  ><font size="2" face="Arial"><%= MyRec.fields("Yard_Access")%></font>&nbsp;</td>

								<td class="auto-style4"  ><font size="2" face="Arial"><%= MyRec.fields("Release_Submission")%></font>&nbsp;</td>


										<td  align="center" class="auto-style2" ><font size="2" face="Arial"><%= MyRec.fields("Volume")%></font>&nbsp;</td>
										
										<td  align="center" class="auto-style2" ><font size="2" face="Arial"><%= MyRec.fields("UOM")%></font>&nbsp;</td>

								<td class="auto-style2"  ><font size="2" face="Arial"><%= MyRec.fields("Freight")%></font>&nbsp;</td>


	<td class="auto-style4"  ><font size="2" face="Arial"><%= MyRec.fields("Push")%></font>&nbsp;</td>


</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>
<% end if %><!--#include file="Fiberfooter.inc"-->