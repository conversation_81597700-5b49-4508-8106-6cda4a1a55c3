<html>
<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_Procedure.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>CSE Permit</title>

</head>
<% dim strsql, MyConn, strid, objGeneral, strsql2, strPurpose,  strBID, strE_name
Dim strDateIssued, strDateExpired, strTimeIssued, strTimeExpired, strPID, strsql3, MyRec3

strid = Request.querystring("id")
strpid = request.querystring("pid")


strsql = "Select tblPermit.* from tblPermit where PID = " & strpid  & ""

Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
   		
strPurposeofEntry = MyConn.fields("Purpose")
strDateIssued = MyConn.fields("Date_issued")
strTimeIssued = MyConn.fields("Time_issued")
strDateExpired = MyConn.fields("Date_expired")
strTimeExpired = MyConn.fields("Time_expired")
strPID = MyConn.fields("PID")
strPurpose = MyConn.fields("Purpose")
MyConn.close


strsql = "SELECT SDescription, SOP_NO FROM tblSOP  where SOP_NO = '" & strid & "'"

Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
		

  		 
  		
 %><body>
 <p align="left"><b><font face="arial">Confined Space Permit for Space:&nbsp;<%= Myconn.fields("SOP_NO")%> - <%= MyConn.fields("Sdescription")%>
</font></b>
 
 </p><% MyConn.close %>

 
 <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#D9E1F9" height="60">
   
  <tr>
    <td bgcolor="D9E1F2" align="center" bordercolordark="#000000" bordercolor="#FFFFDD">
	<font face="Arial" size="2">Date Issued</font></td>

      
    <td bgcolor="D9E1F2" align="center" bordercolordark="#000000" bordercolor="#FFFFDD">
	<font face="Arial" size="2">Time Issued</font></td>

   
    <td bgcolor="D9E1F2" align="left"  bordercolordark="#000000" bordercolor="#FFFFDD" align="center">
	<font face="Arial" size="2">Permit Expiration Time</font></td>

   
  </tr>
  <tr align = center>
    <td  bgcolor="D9E1F2" bordercolor="#FFFFDD"  >
     <font face = arial size = 2>	
	<%= strDateIssued %></td>
	

    <td  bgcolor="D9E1F2" bordercolor="#FFFFDD"  >
		<font face = arial size = 2>	
	<%= strTimeIssued%></td>
	
    <td  bgcolor="D9E1F2" align="left" bordercolor="#FFFFDD" align="center" >
	
		<font face = arial size = 2>	
	<%= strTimeexpired%></td></tr>
	
  </table> 

 

 
  
 <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#808080" width="100%" bgcolor="#D9E1F9" id="table1" height="95">
   
  <tr align = center>
    <td bgcolor="D9E1F2" bordercolor="#FFFFDD">
	
	<font face="arial" size="2">&nbsp;</font>
	<font face="arial" size="2"><br>Description of Work to be Performed</font>
	&nbsp;</td>

   
  </tr>
  <tr align = center>
    <td  bgcolor="D9E1F2" bordercolor="#FFFFDD"  >
	<b>
	<font face="Arial" size="2">
<%= strPurpose %></font><p></b>
	&nbsp;</td></tr></table>
	 <% If len(request.querystring("p")) > 0 then  %>
  <p><b><font face = arial size = 3 color="#FF0000">VIP has been deleted</font></b></p>     
  <% end if %>  
			<%	Dim strHazID
  			
  			strsql = "SELECT IDHazAssess  FROM tblHA where  SpaceID = '" & strid & "'"
  			 Set MyConnC = Server.CreateObject("ADODB.Recordset") 
   		MyConnC.Open strSQL, Session("ConnectionString")
   		strHazID = MyConnc.fields("IDHazAssess")
   		MyConnc.close
  	strSpaceid = strid		
  			
	strsql = "Select ECP_ID, EID,  haz_id, Task, EID from tblECP where Haz_Id = " & strHazID & ""

    Set MyConnC = Server.CreateObject("ADODB.Recordset") 
   		MyConnC.Open strSQL, Session("ConnectionString")
 while not MyConnC.Eof 
  strid = MyConnC.fields("EID")


	strsql3 = "Select EID, Haz_id, Space_id, PID from tblPermitECPSOP where EID = '" & strid & "' and PID = " & strpid & " "
 	Set MyRec3 = Server.CreateObject("ADODB.Recordset") 
 
			MyRec3.Open strSQL, Session("ConnectionString")			
			If not MyRec3.eof then

		If request.querystring("del") = "D" then
		' do nothing
		else
	strsql2 = "Insert into tblPermitECPSOP (EID, Haz_id,  Space_Id, PID) Select '" & strid & "', " & strHazID & ",  '" & strSpaceid & "', " & strPID & ""
			Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL2
		    MyRec.Close 
end if
	end if
	MyRec3.close
	  MyConnC.MoveNext
     Wend 
     MyConnc.close %>



   <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#808080" width="100%"  id="table1" bgcolor="D9E1F2" height = 137>
  <tr><td colspan = 2 bordercolor="#FFFFDD">
  <font face="arial" size="2"><b>VIP(s) for this Space </b></font></td></tr>
  <%
  	  	strsql = "SELECT tblPermitECPSOP.PID, tblECP.HAZ_ID, tblECP.ECP_ID, tblECP.Task, tblECP.EID "_
			&" FROM tblPermitECPSOP INNER JOIN tblECP ON tblPermitECPSOP.EID = tblECP.EID "_
			&" where tblPermitECPSOP.PID = " & strPID & ""	
     Set MyConnC = Server.CreateObject("ADODB.Recordset") 
   		MyConnC.Open strSQL, Session("ConnectionString")

          while not MyConnC.Eof %>
    <tr><td width = 15% bordercolor="#FFFFDD">  <font face = arial size = 2><b>
<a href="Add_ECP_to_Permit.asp?id=<%= MyConnC.fields("EID")%>&hid=<%= strHazid %>&pid=<%= strPID %>&ti=<%= strTimeIssued %>&s=<%= strSpaceid%>">Delete from Permit</a>&nbsp;</td>
		<td bordercolor="#FFFFDD"><font face = arial size = 2>
<%= MyConnC.fields("ECP_ID")%>&nbsp;&nbsp;<%= MyConnc.fields("Task")%>
</font>
 </td></tr>
    <%
       ii = ii + 1
       MyConnC.MoveNext
     Wend %>
     <tr><td colspan = 2>&nbsp;</td></tr>
     </table>
     </div>
     <%
  
     MyConnc.close%>
     
     
 
	<% strsql = "Select SID, Task, Space_ID from tblECP_SOP where Haz_Id = " & strHazID & ""

    Set MyConnC = Server.CreateObject("ADODB.Recordset") 
   		MyConnC.Open strSQL, Session("ConnectionString")
 while not MyConnC.Eof 
   strid = MyConnC.fields("SID")


	strsql3 = "Select SID, Haz_id, Space_id, PID from tblPermitECPSOP where SID = '" & strid & "' and PID = " & strpid & " "
 	Set MyRec3 = Server.CreateObject("ADODB.Recordset") 
 
			MyRec3.Open strSQL, Session("ConnectionString")			
			If not MyRec3.eof then

		If request.querystring("del") = "D" then
		' do nothing
		else
	strsql2 = "Insert into tblPermitECPSOP (SID, Haz_id,  Space_Id, PID) Select '" & strid & "', " & strHazID & ",  '" & strSpaceid & "', " & strPID & ""
			Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL2
		    MyRec.Close 
end if
	end if
	MyRec3.close
	  MyConnC.MoveNext
     Wend 
     MyConnc.close %>
     	 <% If len(request.querystring("sop")) > 0 then  %>
  <p><b><font face = arial size = 3 color="#FF0000">SWP has been deleted</font></b></p>     
  <% end if %>  

    <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#808080" width="100%"   id="table1" bgcolor="D9E1F2" height="137" >
  <tr><td colspan = 2 bgcolor="D9E1F2" bordercolor="#FFFFDD">
<font face="arial" size="2"><b>SWP(s) for this Space</b></font></td></tr>  
     
  		<% strsql = "SELECT tblPermitECPSOP.PID, tblECP_SOP.SID, tblECP_SOP.Task, tblECP_SOP.Space_ID"_
	&" FROM tblPermitECPSOP INNER JOIN tblECP_SOP ON tblPermitECPSOP.SID = tblECP_SOP.SID "_
	&" where tblPermitECPSOP.PID = " & strPID & ""	
     Set MyConnC = Server.CreateObject("ADODB.Recordset") 
   		MyConnC.Open strSQL, Session("ConnectionString")

          while not MyConnC.Eof %>

    
   
         <tr><td width = 15% bgcolor="D9E1F2" bordercolor="#FFFFDD">  
			<font face = arial size = 2><b>
      
			<a href="Add_SOP_to_Permit.asp?id=<%= MyConnc.fields("SId") %>&hid=<%= strHazid%>&pid=<%= strPID %>&ti=<%= strTimeIssued %>&s=<%= strSpaceid%>">
			Delete from Permit</a>&nbsp;
</td>
<td bgcolor="D9E1F2" bordercolor="#FFFFDD"><font face = arial size = 2>
<%= MyConnC.fields("Space_ID")%>&nbsp;&nbsp;<%= MyConnc.fields("Task")%>
</font>
 </td></tr>

     <%
       ii = ii + 1
       MyConnC.MoveNext
     Wend %>
          <tr><td colspan = 2>&nbsp;</td></tr>
     </table>
<p align = center>
<b><a href="CSE_Permit_New_print.asp?id=<%= strSpaceid%>&pid=<%= strpid%>"><font face = arial size = 3>VIEW PERMIT</a></P></B>


    <!--#include file="footer.inc"-->