

<!--#INCLUDE file= "ADUserQuery.inc"-->
   <%
        Dim strEID, x, y, OUser, strUserName, oNS, OUser1, strUserLocation
         

   			x = Request.Servervariables("LOGON_USER")
			strEID = ""
			If InStr(x,"\") then
                  y = SPLIT(x,"\")
                  strEID = y(1)
                  Else
                  strEID = x
                  End If

     	 	'If left(strEID,4) = "USEV" Then
     	 	If len(Session("Login_ID")) = 6 then
     	 	strEID =  Session("Login_ID")
     	 	end if
     	 	'end if

              Session("EmployeeID") = strEID

     

         Session("Mill") = ReturnMill(StrEID)
         Session("Owner") = ReturnUser(StrEID)
         Session("Ename") = ReturnEname(StrEID)
	
		session.timeout=75


Function ReturnUser(strContactBID)	
	Dim objADUserQuery,objADU
	Dim strFirstName, strLastName
    	Set objADUserQuery = New ActiveDirectoryUserQuery
	objADUserQuery.targetDomain = "kcc.com"
      	objADUserQuery.addFilter "cn",strContactBID
	objADUserQuery.executeQuery
	If(objADUserQuery.resultsCount = 0) Then
		ReturnUser=""
	ElseIf(objADUserQuery.resultsCount = 1) Then
		set objADU=objADUserQuery.results(0)
		'ReturnUser=objADU.sn & ", " & objADU.givenname
	ReturnUser=objADU.displayName
	Else
		ReturnUser=""
	end if
	 Set objADUserQuery = Nothing
	 Set objADU=Nothing
End Function

Function ReturnEname(strContactBID)	
	Dim objADUserQuery,objADU
	Dim strFirstName, strLastName
    	Set objADUserQuery = New ActiveDirectoryUserQuery
	objADUserQuery.targetDomain = "kcc.com"
      	objADUserQuery.addFilter "cn",strContactBID
	objADUserQuery.executeQuery
	If(objADUserQuery.resultsCount = 0) Then
		ReturnEName=""
	ElseIf(objADUserQuery.resultsCount = 1) Then
		set objADU=objADUserQuery.results(0)
		ReturnEName=objADU.givenname & " " &  objADU.sn
	Else
		ReturnEName=""
	end if
	 Set objADUserQuery = Nothing
	 Set objADU=Nothing
End Function	

Function ReturnMill(strContactBID)	
	Dim objADUserQuery,objADU
		Set objADUserQuery = New ActiveDirectoryUserQuery
		objADUserQuery.targetDomain = "kcc.com"
			objADUserQuery.addFilter "cn",strContactBID
		objADUserQuery.executeQuery
		If(objADUserQuery.resultsCount = 0) Then
			ReturnMill=""
		ElseIf(objADUserQuery.resultsCount = 1) Then
			set objADU=objADUserQuery.results(0)
			ReturnMill=objADU.physicalDeliveryOfficeName
		Else
			ReturnMill=""
		end if
		Set objADUserQuery = Nothing
		Set objADU=Nothing   	
End Function

Function ReturnEmail(strContactBID)	
	Dim objADUserQuery,objADU
		Dim strFirstName, strLastName
		Set objADUserQuery = New ActiveDirectoryUserQuery
		objADUserQuery.targetDomain = "kcc.com"
			objADUserQuery.addFilter "cn",strContactBID
		objADUserQuery.executeQuery
		If(objADUserQuery.resultsCount = 0) Then
			ReturnEmail=""
		ElseIf(objADUserQuery.resultsCount = 1) Then
			set objADU=objADUserQuery.results(0)
			ReturnEmail=objADU.mail
		Else
			ReturnEmail=""
		end if
		Set objADUserQuery = Nothing
		Set objADU=Nothing
End Function	
%>
