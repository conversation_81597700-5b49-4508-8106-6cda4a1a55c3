﻿<html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns="http://www.w3.org/TR/REC-html40">
<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_Procedure.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows1252">

<style type="text/css">
.style2 {
	font-family: Arial;
	font-size: x-small;
}
.style3 {
	border-width: 1px;
}
.style6 {
	font-family: Arial;
}
.style10 {
	font-size: xx-small;
}
.style11 {
	border-width: 1px;
	font-size: xx-small;
	font-family: Verdana;
}
.style15 {
	font-weight: bold;
	border-width: 1px;
	font-size: xx-small;
	background-color: #EFEFF8;
}
.style16 {
	border-color: #C0C0C0;
	border-width: 1px;
}
.style18 {
	border-width: 1px;
	background-color: #EFEFF8;
}
.style19 {
	font-weight: bold;
	border-style: solid;
	border-color: #EFEFF8;
	background-color: #FFFFD9;
}
.style20 {
	border-width: 1px;
	font-size: xx-small;
	background-color: #EFEFF8;
}
.style21 {
	border-color: #C0C0C0;
	border-width: 1px;
	background-color: #EFEFF8;
}
.style23 {
	border-color: #C0C0C0;
	background-color: #EFEFF8;
}
.style25 {
	font-family: Verdana;
}
.style27 {
	border-width: 1px;
	font-size: xx-small;
	background-color: #EFEFF8;
	font-family: Verdana;
}
.style28 {
	font-size: xx-small;
	font-family: Verdana;
}
.style29 {
	font-weight: bold;
	border-width: 1px;
	font-size: xx-small;
	background-color: #EFEFF8;
	font-family: Verdana;
}
.style30 {
	font-family: Verdana;
	font-weight: normal;
}
.style31 {
	font-family: Arial;
	font-size: xx-small;
}
.style32 {
	font-weight: bold;
}
.style36 {
	font-weight: normal;
	border-width: 1px;
	font-size: xx-small;
	background-color: #EFEFF8;
	font-family: Verdana;
}
.style37 {
	font-weight: normal;
	border-width: 1px;
	font-size: xx-small;
	background-color: #EFEFF8;
}
.style38 {
	font-weight: normal;
}
.style39 {
	border-style: solid;
	border-color: #EFEFF8;
	background-color: #FFFFD9;
}
</style>

</head>

<% dim strsql, MyConn, strid, strsql1, MyRec, strApproval_Date, strApprover_One, strApprover_Two
Dim strSpace_ID, strERT_Map, strPortal_ID, strLock_Box_Location, strSpace_Category_1, strSpace_Category_2
Dim strAccess_Elev_Yes, strAccess_Elev_No, strAccess_Restricted_Yes, strAccess_Restricted_No, strRaise_Lower_3_1
Dim strRaise_Lower_4_1, strTripod, strMain_Line_Red, strBelay_Line_Blue, strGear_Bag_Black, strYellow_bag, strSuggested_Anchors
Dim strPrerigging, strPre_Fab_Anchor, strBeam, strHandrail, strStairwell, strStrut, strSteel_Pipe, strSupport_Column
Dim  strRescue_External, strRescue_Congested, strRescue_Lowering, strC_Collar, strTrauma_Kit, strHalf_Back
Dim strSked, strCSR_One, strCSR_Two, strCSR_Three, strTrauma_One, strTrauma_Two, strTrauma_Three, strAdd_PPE
Dim strEntrant_1, strAttendant, strEntrant_2, strSafety, strMain_Line, strBelay_Line, strBackup, strTester
Dim strRecommend_Evac_1, strRecommend_Evac_2, strRecommend_Evac_3, strEntry_Super, strSuper_Phone, strCompleted_By
Dim strNotes, strSup_Date, strSpace_Desc, strOther_Com
Dim strSummon_Rescue, strRescue_Req, strRescue_Req_Comment, strSuggested_Anchors_Com, strPrerigging_Com
Dim strSlocation, strSdescription, strSid, strPortalName, strPortalLocation, strAccessEgress, strOther_ae, strAtt_call, strSCBAInPortal
Dim strMechanicalDevice, strBodyHarnesswithLine, strharnessAndRetrieval, strRescueEquip1, strRescueEquip2
Dim rstCSR, rstTrauma, strCSR1, strCSR2, strCSR3, strT1, strT2, strT3, strAttachment, strPhoto


strid = Request.querystring("id")
strSid = request.querystring("sid")
strpid = request.querystring("p")

strsql = "SELECT LOCATION, SDescription FROM tblSOP where SOP_NO = '" & strSid & "'"
Set MyConn = Server.CreateObject("ADODB.Recordset") 
  MyConn.Open strSQL, Session("ConnectionString")
  If not MyConn.eof then
strSlocation = MyConn.fields("Location")
strSDescription = MyConn.fields("SDescription")
end if
MyConn.close

strsql = "Select Att_call from tblHA where SpaceID = '" & strSID & "'"
  MyConn.Open strSQL, Session("ConnectionString")
  If not MyConn.eof then

strAtt_Call = Myconn.fields("Att_call")
end if 
MyConn.close
strnow = dateadd("h", -5, now())
strApproval_Date = formatdatetime(strnow,2)

strsql = "Select tblPortals.*  from tblPortals where ID = " & strpid
Set MyConn = Server.CreateObject("ADODB.Recordset") 
  MyConn.Open strSQL, Session("ConnectionString")
  If not MyConn.eof then
strPortalName = Myconn.fields("NameLocPortal")
strAccessEgress = MyConn.fields("AccessEgress")
strOther_ae = Myconn.fields("Other_ae")
strSCBAInPortal = MyConn.fields("SCBAInPortal")
strNonEntryRescue = MyConn.fields("NonEntryRescue")
strMechanicalDevice = MyConn.fields("MechanicalDevice")
strBodyHarnesswithLine = Myconn.fields("BodyHarnesswithLine")
strharnessAndRetrieval = MyConn.fields("harnessAndRetrieval")
strRescueEquip1 = MyConn.fields("RescueEquip1")
strRescueEquip2 = MyConn.fields("RescueEquip2")


end if
MyConn.close


strsql = "Select tblRescue.* from tblRescue where ID = " & strID 

Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
   		
If not MyConn.eof then
   		
strID = MyConn.fields("ID")	
strPhoto = MyConn.fields("Photo")
strAttachment = MyConn.fields("Attachment")
strApproval_Date = MyCOnn.fields("Approval_Date")
strApprover_One = MyConn.fields("Approver_One")
strApprover_Two = MyConn.fields("Approver_Two")
strSpace_ID = MyConn.fields("Space_ID")
strERT_Map = MyConn.fields("ERT_Map")
strPortal_ID = MyConn.fields("Portal_ID")
strLock_Box_Location = MyConn.fields("Lock_Box_Location")
strSpace_Category_1 = MyConn.fields("Space_Category_1")
strSpace_Category_2 = MyConn.fields("Space_Category_2")
strAccess_Elev_Yes = MyConn.fields("Access_Elev_Yes")
strAccess_Elev_No = Myconn.fields("Access_Elev_No")
strAccess_Restricted_Yes = Myconn.fields("Access_Restricted_Yes")
strRaise_Lower_3_1 =  Myconn.fields("Raise_Lower_3_1")
strRaise_Lower_4_1 = MyConn.fields("Raise_Lower_4_1")
strTripod = Myconn.fields("Tripod")
strMain_Line_Red = Myconn.fields("Main_Line_Red")
strBelay_Line_Blue = Myconn.fields("Belay_Line_Blue")
strGear_Bag_Black = Myconn.fields("Gear_Bag_Black")
strYellow_Bag = Myconn.fields("Yellow_Bag")
strSuggsted_Anchors = Myconn.fields("Suggested_Anchors")
strSuggsted_Anchors_Com = Myconn.fields("Suggested_Anchors_Com")
strPrerigging = Myconn.fields("Prerigging")
strPrerigging_Com = Myconn.fields("Prerigging_Com")
strPrefab_Anchor = Myconn.fields("Prefab_Anchor")
strBeam = Myconn.fields("Beam")
strHandrail = Myconn.fields("Handrail")
strStairwell = Myconn.fields("Stairwell")
strStrut = Myconn.fields("Strut")
strSteel_Pipe = Myconn.fields("Steel_Pipe")
strSupport_Column = Myconn.fields("Support_Column")

strOther_Com = Myconn.fields("Other_Com")
strRescue_External = Myconn.fields("Rescue_External")
strRescue_Congested = Myconn.fields("Rescue_Congested")
strRescue_Lowering = Myconn.fields("Rescue_Lowering")
strC_Collar = Myconn.fields("C_Collar")
strTrauma_Kit = Myconn.fields("Trauma_Kit")
strHalf_Back = Myconn.fields("Half_Back")
strSked = Myconn.fields("Sked")
strCSR_One = Myconn.fields("CSR_One")
strCSR_Two = Myconn.fields("CSR_Two")
strCSR_Three = Myconn.fields("CSR_Three")
strTrauma_One = Myconn.fields("Trauma_One")
strTrauma_Two = Myconn.fields("Trauma_Two")
stTrauma_Three = Myconn.fields("Trauma_Three")
stAdd_PPE = Myconn.fields("Add_PPE")
strEntrant_1  = Myconn.fields("Entrant_1")
strAttendant = Myconn.fields("Attendant")
strEntrant_2 = Myconn.fields("Entrant_2")
strSafety = Myconn.fields("Safety")
strMain_Line = Myconn.fields("Main_Line")
strBelay_Line = Myconn.fields("Belay_Line")
strBackup = Myconn.fields("Back_Up")
strTester = Myconn.fields("Tester")
strRecommend_Evac_1 = Myconn.fields("Recommend_Evac_1")
strRecommend_Evac_2 = Myconn.fields("Recommend_Evac_2")
strRecommend_Evac_3 = Myconn.fields("Recommend_Evac_3")
strEntry_Super = Myconn.fields("Entry_Super")
strSuper_Phone = Myconn.fields("Super_Phone")
strCompleted_By = Myconn.fields("Completed_By")
strNotes = Myconn.fields("Notes")
strSup_Date = Myconn.fields("Sup_Date")
strSpace_Desc = Myconn.fields("Space_Desc")
strSummon_Rescue = Myconn.fields("Summon_Rescue")
strRescue_Req = Myconn.fields("Rescue_Req")
strRescue_Req_Comment = Myconn.fields("Rescue_Req_Comment")
strPortalLocation = MyConn.fields("Portal_Location")

end if
MyConn.close
set objGeneral = new ASP_CLS_General
  if objGeneral.IsSubmit = True Then
  call savedata()
  end if
  Call getdata()
%>


<body ><br>


<form name="form1" action="Rescue.asp?id=<%= strID%>&sid=<%= strSID%>&p=<%= strpid%>"  method="post" ID="Form1"  >

<table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" align = CENTER>

  <tr align = CENTER>
    <td class="style19">
	<font face="Arial">CONFINED SPACE RESCUE PREPLAN FOR PORTAL ID# <%= strpid%> </TD>
	<TD align="CENTER" class="style39">
	 <INPUT TYPE="submit" value="Submit"></font></td>

  </tr></table><br>
  <table border="1" cellpadding="0" cellspacing="0" width = 100% style="border-collapse: collapse" bordercolor="#111111" id="table1" class="style2">

  <tr>

 <td   align = left class="style27" style="height: 22px">			
		<span class="style28">APPROVAL	DATE:&nbsp;</span><span class="style6"><font size = 2>
		<span class="style25"><span class="style10">
<input type = text value = "<%= strApproval_Date %>" name = "Approval_Date" size="18" tabindex="1" style="width: 112px"></span></span></font></span><span class="style28"></font></span></td>
    <td   align = left style="height: 22px" class="style18">	
 
         <font face="Verdana"> 
	<span class="style31">
 
	APPROVER 1&nbsp;&nbsp;&nbsp;&nbsp; 	</span> </font>
        <font face="Verdana" size="2">
        <span class="style6">
        <span class="style25"><span class="style10">
        <input type = text value = "<%= strApprover_One %>" name = "Approver_One" size="27" tabindex="2"></span></span></TD>
	 <td   align = left style="height: 22px" class="style18">	<font size = 2>
 
         <font face="Verdana" size = 1>
	<span class="style6">
 
         APPROVER 2:&nbsp;&nbsp; </span></font>
        <input type = text value = "<%= strApprover_Two %>" name = "Approver_Two" size="20" tabindex="3"></span></font></td>
      
    
  
  </tr>
 <tr>
 
    
   <td   align = left style="height: 24px" class="style29">	
	<span class="style30">SPACE ID:&nbsp;&nbsp;&nbsp;<b><%= strSid %></b></TD>
	<td   align = left style="height: 24px" class="style15">	
	<span class="style30">SPACE NAME:&nbsp;<b><%= strSDescription %></b></span></TD>
	<td   align = left style="height: 24px" class="style20">	
	<span class="style30"> LOCATION:&nbsp;<b><%= strSLocation %></b></span></td>
  
  </tr>

 <tr>    
   <td   align = left  style="height: 22px" class="style27" >	
	<span class="style10">	
	ERT MAP&nbsp;&nbsp;
        <span class="style25">
        <input type = text value = "<%= strERT_Map %>" name = "ERT_Map" size="20" tabindex="8"></span></span><span class="style28">
	</span>
	</td>
  
  
   <td   align = left  style="height: 22px" class="style27" >	
	PORTAL NAME:&nbsp;<b><%= strPortalName %></b></td>
    <td   align = left  style="height: 22px" class="style18" >	
	<span class="style10">	
	Portal Location&nbsp;&nbsp;
        <input type = text value = "<%= strPortalLocation %>" name = "Portal_Location" size="20" tabindex="8" style="width: 563px"></span>
	</td>

  </tr>

 <tr>

    
   <td   align = left  class="style27" >	LOCK BOX LOCATION</td>
     <td bordercolor="#000000" colspan="2" class="style18" >
        <font face="Verdana">
        <span class="style25"><span class="style10">
        <input type = text value = "<%= strLock_Box_Location %>" name = "Lock_Box_Location" size="108" tabindex="8" style="width: 941px"></span></span></font></td>
  
  </tr>

 <tr> 
    
   <td   align = left  style="height: 20px; " class="style27">	SPACE CATEGORY</td>
     <td bordercolor="#000000" colspan="2" style="height: 20px" class="style18" >
	<font face="Verdana" size="1"><span class="style10">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	</span>
		<span class="style6">
	<span class="style25"><span class="style10">
	<input type="checkbox" name="Space_Category_1" <% if strSpace_Category_1 = 1 then %>checked <% end if %> value="ON"></span></span></span><span class="style10">&nbsp; Category 1- Rescue Available (RA)</span></font><span class="style28">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	</span>
	<font face="Verdana" size="1">
	<span class="style25"><span class="style10">
	<input type="checkbox" name="Space_Category_2" <% if strSpace_Category_2 = 1 then %>checked <% end if %> value="ON"></span></span><span class="style10">&nbsp; Category II- Rescue Stand-by (RS) 
	</span> </font>
	</td>
  
  </tr>
 <tr>
    <td  class="style36">	<font class="style25">LOCATION OF ACCESS/EGRESS PORTAL</font></td>
    <td colspan="2" class="style18"><span class="style25"><span class="style10">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Elevated:&nbsp;</span></span>
	<span class="style25"><span class="style10">
    <input type="checkbox" name="Access_Elev_Yes" <% if strAccess_Elev_Yes = 1 then %>checked <% end if %> value="ON"></span></span>
    <span class="style28">Y</span><span class="style25"><span class="style10"> &nbsp;
    <input type="checkbox" name="Access_Elev_No" <% if strAccess_Elev_No = 1 then %>checked <% end if %> value="ON"></span></span>
    <font face="Verdana" size="2"><span class="style10">N&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	</span>
	<font face="Verdana" size="1"><span class="style10">Restricted:
	</span><span class="style25"><span class="style10">
	<input type="checkbox" name="Access_Restricted_Yes" <% if strAccess_Restricted_Yes = 1 then %>checked <% end if %> value="ON"></span></span><span class="style10">Y
	&nbsp;
	</span><span class="style25"><span class="style10">
	<input type="checkbox" name="Access_Restricted_No" <% if strAccess_Restricted_No = 1 then %>checked <% end if %> value="ON"></span></span><span class="style10">N
	&nbsp;&nbsp;&nbsp;&nbsp;<b><% = strAccessEgress %>&nbsp;&nbsp;&nbsp;&nbsp;<%=	strOther_ae%></b></span></font></td></tr>

 <tr class="style25" >
    <td style="height: 22px; " class="style20">MEANS TO SUMMON RESCUE SERVICE</td>
     <td colspan="2" style="height: 22px" class="style37"><b><%= strAtt_call %></b>&nbsp;</td>
  

  </tr>

 <tr>
    <td  class="style27">
	RESCUE REQUIREMENTS:</td>
<td colspan="2" class="style18">
	<font face="Verdana" size="1">
	<span class="style25"><span class="style10">
	<input type="checkbox" name="SCBA" <% if strSCBAInPortal = 1 then %>checked <% end if %> value="ON"></span></span></font>
	<span class="style25"><span class="style10">
	&nbsp; SCBA will not fit through portal <br><span class="style30">
	<% if strNonEntryRescue = 1 then %><strong>
	 Non-entry Rescue: aided assistance in exiting the confined space not requiring entry  (body or wrist harness with a retrieval line attached unless either would endanger the entrant or not assist in a rescue).
	<% end if %></strong> <strong>
	<br>
	<% IF strharnessAndRetrieval = 1 THEN %>
	Body or wrist harness&nbsp;&nbsp;
	<% END IF %></strong> <strong>
	<% if strBodyHarnessWithLine = 1 then %>
	Body or wrist harness with retrieval line&nbsp;&nbsp;<% end if %></strong>
	<strong>
	<% if strMechanicalDevice = 1 then %>
	Mechanical device/tripod&nbsp;&nbsp;
	<% end if %></strong> <strong>
	
<%= strRescueEquip1%></strong>&nbsp; <%= strRescueEquip2%>
	<br>
	COMMENTS</span></span>

        <font face="Verdana">
        <span class="style25"><span class="style10">
        <input type = text value = "<%= strRescue_Req_Comment %>" name = "Rescue_Req_Comment" size="108" tabindex="8"></span></span></font></td>
  
  </tr>

 <tr>
    <td bordercolor="#000000" class="style27">
	SUGGESTED RAISING/LOWERING SYSTEM</td>
     <td bordercolor="#000000" colspan="2" class="style18" >
        <font face="Verdana">
        <span class="style6"><span class="style10">
        <span class="style25">
        <input type="checkbox" name="Raise_Lower_3_1" <% if strRaise_Lower_3_1 = 1 then %>checked <% end if %> value="ON"></span></span></span></font><span class="style10"><span class="style25"></font>&nbsp; 
		31&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
		</span> 
		</span><span class="style6"><span class="style10"> 
		<span class="style25"> <input type="checkbox" name="Raise_Lower_4_1" <% if strRaise_Lower_4_1 = 1 then %>checked <% end if %> value="ON"></span>
		</span></span><span class="style10"><span class="style25"></font></font>&nbsp; 41  </font>&nbsp;&nbsp;&nbsp;</span></span><span class="style25"><span class="style10">&nbsp;&nbsp;&nbsp; 
		<input type="checkbox" name="Tripod" <% if strTripod then %>checked <% end if %> value="ON">&nbsp;
		</span></span>
		<font face="Verdana"><span class="style10">Tripod </span> </font> 
		<span class="style25"><span class="style10"> <br>
  
		RESCUE ROPE BAGS NEEDED&nbsp;&nbsp;&nbsp;
        <input type="checkbox" name="Main_Line_Red" <% if strMain_Line_Red then %>checked <% end if %> value="ON">&nbsp; 
		</span></span> 
		<font face="Verdana"><span class="style10">Main Line/Red Bag </span> </font>
		<span class="style25"><span class="style10">&nbsp;&nbsp; <input type="checkbox" name="Belay_Line_Blue" <% if strBelay_Line_Blue then %>checked <% end if %> value="ON"></font></font>&nbsp; 
		</span></span> 
		<font face="Verdana" size="1"><span class="style10">Belay Line/Blue Bag&nbsp;&nbsp; 
		</span><span class="style25"><span class="style10"> <input type="checkbox" name="Gear_Bag_Black" <% if strGear_Bag_Black then %>checked <% end if %> value="ON"></span></span></font><span class="style25"><span class="style10"></font>&nbsp; 
		</span></span> 
		<font face="Verdana"><span class="style10">Gear Bag/Black Bag </span> </font>
		<span class="style25"><span class="style10">&nbsp;&nbsp;&nbsp; <input type="checkbox" name="Yellow_Bag" <% if strYellow_Bag then %>checked <% end if %> value="ON"></font></font>&nbsp; 
		</span></span> 
		<font face="Verdana"><span class="style10">41 /Yellow Bag</span></font><span class="style28"><br>
			</span>
			<span class="style6"><span class="style25"><span class="style10"><input type="checkbox" name="Suggested_Anchors" <% if strSuggested_Anchors then %>checked <% end if %> value="ON"></span></span></span><span class="style25"><span class="style10">&nbsp; 
		Suggested Anchors </span></span>&nbsp;<span class="style25"><span class="style10"><input type = text value = "<%= strSuggested_Anchors_Com %>" name = "Suggested_Anchors_Com" size="100" tabindex="8"><br>
		</span></span>
<span class="style6"><span class="style25"><span class="style10"><input type="checkbox" name="Prerigging" <% if strPrerigging then %>checked <% end if %> value="ON"></span></span></span><span class="style25"><span class="style10">&nbsp; 
		Pre-rigging 

        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;

        &nbsp;<input type = text value = "<%= strPrerigging_Com %>" name = "Prerigging_Com" size="100" tabindex="8"><br>
   		ANCHORAGE  &nbsp;
        </span></span>
        <span class="style6"><span class="style25"><span class="style10"><input type="checkbox" name="Prefab_Anchor" <% if strPrefab_Anchor = 1 then %>checked <% end if %> value="ON"></span></span></span><span class="style28">&nbsp; 
		</span> 
		<font face="Verdana" size="1"> 
		<span class="style10">Pre-Fabricated Anchor&nbsp;&nbsp; </span> 
		<b> 
		<span class="style6"><span class="style25"><span class="style10"> <input type="checkbox" name="Beam" <% if strBeam then %>checked <% end if %> value="ON"></span></span></span></b></font><span class="style25"><span class="style10"></font><b>&nbsp; 
		</b> 
		</span></span> 
		<font face="Verdana" size="1"> 
		<span class="style10">Beam&nbsp;<b>&nbsp; 
		</b> 
		</span> 
		<b> 
		<span class="style6"><span class="style25"><span class="style10"> 
		<input type="checkbox" name="Handrail" <% if strHandrail then %>checked <% end if %> value="ON" class="style32"></span></span></span></font><span class="style25"><span class="style10">&nbsp; 
		<span class="style38">Welded Steel 
		Handrail</span>&nbsp;&nbsp;&nbsp;&nbsp; </span></span>
		<font face="Verdana" size="1"> 
		<span class="style10">&nbsp; </span> 
		<span class="style6"><span class="style25"><span class="style10"> 
		<input type="checkbox" name="Strut" <% if strStrut then %>checked <% end if %> value="ON" class="style32"></span></span></span></font><span class="style28">&nbsp; 
		</span> 
		</b> 
		<font face="Verdana" size="1"> <span class="style10">Support Strut<br>
 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="style25"><span class="style10"><input type="checkbox" name="Stairwell" <% if strStairwell then %>checked <% end if %> value="ON" class="style32"></span></span><span class="style10"> Stairwell&nbsp;&nbsp; 
		</span>
	<span class="style6"><span class="style25"><span class="style10"> 
		<input type="checkbox" name="Steel_Pipe" <% if strSteel_Pipe then %>checked <% end if %> value="ON" class="style32"></span></span></span></font><span class="style28">&nbsp; 
		</span> 
		<font face="Verdana" size="1"><span class="style10">Anchored Steel Pipe&nbsp;&nbsp;&nbsp;</span><b><span class="style6"><span class="style25"><span class="style10"><input type="checkbox" name="Support_Column" <% if strSupport_Column then %>checked <% end if %> value="ON"></span></span></span></b></font><span class="style28">&nbsp; 
		</span> 
		<font face="Verdana"><span class="style10">Support Column&nbsp;&nbsp;&nbsp;</span></font><span class="style28"> Other  </span>  <font size="1" face="Arial">  
		<span class="style25"><span class="style10">  
		<input type = text value = "<%= strOther_Com %>" name = "Other_Com" size="50" style="width: 279px" ></span></span></font></td>
  
  </tr>

 <tr>
    <td  class="style27">
	METHODS OF RESCUE</td>
     <td bordercolor="#000000" height="22" colspan="2" class="style18" >
        <span class="style25"><span class="style10">
        <input type="checkbox" name="Rescue_External" <% if strRescue_External then %>checked <% end if %> value="ON">&nbsp; 
		</span></span> 
		<font face="Verdana"><span class="style10">External (Retrieval) </span> </font>
		<span class="style25"><span class="style10">&nbsp;&nbsp; <input type="checkbox" name="Rescue_Congested" <% if strRescue_Congested then %>checked <% end if %> value="ON"></font></font>&nbsp; 
		</span></span> 
		<font face="Verdana" size="1"><span class="style10">Congested Work Area Bag&nbsp;&nbsp; 
		</span><span class="style25"><span class="style10"> <input type="checkbox" name="Rescue_Lowering" <% if strRescue_Lowering then %>checked <% end if %> value="ON"></span></span></font><span class="style25"><span class="style10"></font>&nbsp; 
		</span></span> 
		<font face="Verdana"><span class="style10">Victim-Lowering System Required/Lowering Area 
		</span> </font></td>
  
  </tr>

 <tr>
    <td class="style27">
	MEDICAL&nbsp; &amp; PACKING EQUIPMENT NEEDED</td>
     <td bordercolor="#000000" height="22" colspan="2" class="style18" >
        <span class="style25"><span class="style10">
        <input type="checkbox" name="C_Collar" <% if strC_Collar then %>checked <% end if %> value="ON">&nbsp; 
		</span></span> 
		<font face="Verdana"><span class="style10">C-Collar </span> </font>
		<span class="style25"><span class="style10">&nbsp;&nbsp; <input type="checkbox" name="Trauma_Kit" <% if strTrauma_Kit then %>checked <% end if %> value="ON"></font></font>&nbsp; 
		</span></span> 
		<font face="Verdana" size="1"><span class="style10">Trauma Kit Bag&nbsp;&nbsp; 
		</span><span class="style25"><span class="style10"> <input type="checkbox" name="Half_Back" <% if strHalf_Back then %>checked <% end if %> value="ON"></span></span></font><span class="style25"><span class="style10"></font>&nbsp; 
		</span></span> 
		<font face="Verdana"><span class="style10">Half-Back </span> </font>
		<span class="style25"><span class="style10">&nbsp;&nbsp;&nbsp; <input type="checkbox" name="Sked" <% if strSked then %>checked <% end if %> value="ON"></font></font>&nbsp; 
		</span></span> 
		<font face="Verdana"><span class="style10">SKED </span> </font></td>
  
  </tr>

 <tr>
    <td class="style27" >
	NEAREST CSR STATION LOCATIONS</td>
     <td bordercolor="#000000" height="22" colspan="2" class="style20" >
        <font size="1" class="style10"> 1. <select name="CSR_1">
       <option value="">--- All ---</option>
       <%= objGeneral.OptionListAsString(rstCSR, "Station_nbr", "Station", strCSR_one) %>
     </select><br>
     2. <select name="CSR_2">
       <option value="">--- All ---</option>
       <%= objGeneral.OptionListAsString(rstCSR, "Station_nbr", "Station", strCSR_two) %>
     </select><br>
3. <select name="CSR_3">
       <option value="">--- All ---</option>
       <%= objGeneral.OptionListAsString(rstCSR, "Station_nbr", "Station", strCSR_three) %>
     </select></font></td>
  
  </tr>

 <tr>
    <td class="style27" >
        NEAREST TRAUMA STATION LOCATIONS</td>
     <td bordercolor="#000000" height="22" colspan="2" class="style18" >
        <font size="1" class="style10"> 1. <select name="Trauma_1">
       <option value="">--- All ---</option>
       <%= objGeneral.OptionListAsString(rstTrauma, "Station_nbr", "Station", strTrauma_One) %>
      </select><br>
2.
     <select name="Trauma_2">
       <option value="">--- All ---</option>
       <%= objGeneral.OptionListAsString(rstTrauma, "Station_nbr", "Station", strTrauma_Two) %>
   </select><br>
3.
       <select name="Trauma_3">
       <option value="">--- All ---</option>
       <%= objGeneral.OptionListAsString(rstTrauma, "Station_nbr", "Station", strTrauma_Three) %>
     </select></font></td>
  
  </tr>

 <tr>
    <td class="style27" >
	ADDITIONAL PPE: (See Permit/MSDS)</td>
     <td bordercolor="#000000" colspan="2" class="style18" >
        <font face="Verdana">
        <span class="style25"><span class="style10">
        <input type = text value = "<%= strAdd_PPE %>" name = "Add_PPE" size="108" tabindex="8" style="width: 916px"></span></span></font></td>
  
  </tr>

 <tr bgcolor="#EFEFF8">
    <td colspan="3" class="style11" style="height: 22px">
	DESIGNATION OF RESCUE PERSONNEL&nbsp; (Last Name, First Initial)</td>
  
  </tr>
</TABLE>
<table border="1" cellpadding="0" cellspacing="0" width = 100% style="border-collapse: collapse" bordercolor="#111111" id="table1" class="style2">

 <tr>
    <td class="style21" >
	<font size="1">Entrant (1)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <input type = text value = "<%= strEntrant_1 %>" name = "Entrant_1" size="25" tabindex="8"></font></td>
    <td class="style21" >
	<font size="1">Safety&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <input type = text value = "<%= strSafety %>" name = "Safety" size="25" tabindex="8"></font></td>
  
  </tr>

 <tr>
    <td class="style21" >
	<font size="1">Entrant (2)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <input type = text value = "<%= strEntrant_2 %>" name = "Entrant_2" size="25" tabindex="8"></font></td>
    <td class="style21" >
	<font size="1">Attendant&nbsp;&nbsp;&nbsp;&nbsp; <input type = text value = "<%= strAttendant %>" name = "Attendant" size="25" tabindex="8"></font></td>
  
  </tr>

 <tr>
    <td class="style21" >
	<font size="1">Main Line&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <input type = text value = "<%= strMain_Line %>" name = "Main_Line" size="25" tabindex="8"></font></td>
    <td class="style21" >
	<font size="1">Belay Line&nbsp;&nbsp; <input type = text value = "<%= strBelay_Line %>" name = "Belay_Line" size="25" tabindex="8"></font></td>
  
  </tr>

 <tr>
    <td class="style21" >
	<font size="1">Back-Up Rescuer <input type = text value = "<%= strBackup %>" name = "Backup" size="25" tabindex="8"></font></td>
    <td class="style21">
	<font size="1">Air Tester&nbsp;&nbsp;&nbsp;&nbsp; <input type = text value = "<%= strTester %>" name = "Tester" size="25" tabindex="8"></font></td>
  
  </tr>

 <tr bgcolor="#EFEFF8">
    <td colspan="3" style="height: 22px" class="style3">
	<font size="1">RECOMMENDED EVACUATION STRATEGY FOR PATIENT(S):</font></td>
  
  </tr>

 <tr>
    <td colspan="3" class="style21">
	<font size="1">1. 
	<input type = text value = "<%= strRecommend_Evac_1 %>" name = "Recommend_Evac_1"  tabindex="8" size="140" style="width: 1092px"></font></td>
  
  </tr>

 <tr>
    <td colspan="3" class="style21">
	<font size="1">2. 
	<input type = text value = "<%= strRecommend_Evac_2 %>" name = "Recommend_Evac_2"  tabindex="8" size="140" style="width: 1092px"></font></td>
  
  </tr>

 <tr>
    <td colspan="3" class="style21">
	<font size="1">3. 
	<input type = text value = "<%= strRecommend_Evac_3 %>" name = "Recommend_Evac_3"  tabindex="8" size="140" style="width: 1092px"></font></td>
  
  </tr>
</TABLE>
<table border="1" cellpadding="0" cellspacing="0" width = 100% style="border-collapse: collapse" bordercolor="#111111" id="table1" class="style2">

 <tr>
    <td colspan="3" class="style21">
	<font size="1">SPACE DESCRIPTION:&nbsp;<input type = text value = "<%= strSpace_Desc %>" name = "Space_Desc"  tabindex="8" style="width: 1000px"></td>
  
  </tr>
 <tr bgcolor="#EFEFF8">    
	<td colspan="3" style="height: 22px; " class="style16">
	<font size="1">SKETCH OR DIAGRAM OF SPACE:&nbsp;&nbsp; Attachment?&nbsp;&nbsp; <font face = arial size = 1>

<select name="Attachment" >
		<option value="No" <% if strAttachment = "No" then %> Selected<% end if %>>No</option>
			<option value="Yes" <% if strAttachment = "Yes" then %> Selected<% end if %>>Yes</option>
			
					</select></font>
					
		<% if strAttachment = "Yes" then %><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<a target = "_blank" href="\\USEVFN01\Share\Safety\Haz Track\Safety Log Attachments\<%= strid %>">View</a></b><% end if %>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<a href="AttachHelp.asp" target = "_blank"><img border=0 width=60 height=29 src="images/help.gif"  alt="How to Reference Attachment"  ></a>			
					
					
					
					</td>
  </tr>

 <tr>
    <td class="style23"><font size="1">ENTRY SUPERVISOR:&nbsp; <input type = text value = "<%= strEntry_Super %>" name = "Entry_Super"  tabindex="8"></font></td>
    <td  class="style23"><font size="1">&nbsp;PHONE #&nbsp;&nbsp;&nbsp; <input type = text value = "<%= strSuper_Phone %>" name = "Super_Phone"  tabindex="8"></td>
	<td  class="style23"><font size="1">&nbsp;DATE:&nbsp;&nbsp; 	<input type = text value = "<%= strSup_Date %>" name = "Sup_Date"  tabindex="8" style="width: 119px"></font></td>
  
  
  </tr>

 <tr>
    <td colspan="6" style="height: 26px" class="style23">
	<font size="1">REPORT COMPLETED BY: <%= Session("Ename") %></font></td>
  
  </tr>

 <tr>
    <td colspan="6" class="style23">
	&nbsp;</td>
  
  </tr>

 <tr bgcolor="#EFEFF8">
    <td colspan="3" style="height: 22px; " class="style16">
	<font size="1">PHOTOS ASSOCIATED WITH THE ROPE TECHNIQUES FOR THIS CONFINED 
	SPACE:&nbsp;&nbsp;&nbsp;Attachment?&nbsp;&nbsp; <font face = arial size = 1>

<select name="Photo" >
		<option value="No" <% if strPhoto = "No" then %> Selected<% end if %>>No</option>
			<option value="Yes" <% if strPhoto = "Yes" then %> Selected<% end if %>>Yes</option>
			
					</select></font>
					
		<% if strPhoto = "Yes" then %><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<a target = "_blank" href="\\USEVFN01\Share\Safety\Haz Track\Safety Log Attachments\<%= strid %>">View</a></b><% end if %>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<a href="AttachHelp.asp" target = "_blank"><img border=0 width=60 height=29 src="images/help.gif"  alt="How to Reference Attachment"  ></a>			
					
					
					
					</font></td>
	  <td colspan="3" style="height: 22px" class="style16">&nbsp;</td>
  
  </tr>

 <tr>
    <td colspan="6" class="style23">
	<font size="1">NOTES:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;  
	<textarea name="Notes" id="Notes" width: 1078px; height: 44px;" style="width: 998px"><%= strNotes %></textarea></font></td>
  
  </tr>

 <tr>
    <td colspan="6" class="style23">
	&nbsp;</td>
  
  </tr>

</table>
</form>
<% Function Savedata()

strSpace_ID = request.querystring("sid")
   If len(strApproval_Date) > 1 then 
   ' skip
   else
   strnow = dateadd("h", -5, now())
   strApproval_Date = formatdatetime(strnow,0)
   end if
   
   If len(Request.form("Approver_One")) > 0 then
   strApprover_One = Replace(Request.form("Approver_One"), "'", "''")
   else
   strApprover_One = ""
   end if
   
   If len(Request.form("Approver_Two")) > 0 then
   strApprover_Two = Replace(Request.form("Approver_Two"), "'", "''")
   else
   strApprover_Two = ""
   end if
   
   If len(Request.form("ERT_Map")) > 0 then
   strERT_Map = Replace(Request.form("ERT_Map"), "'", "''")
   else
   strERT_Map = ""
   end if
   
   If len(Request.form("Lock_Box_Location")) > 0 then
   strLock_Box_Location = Replace(Request.form("Lock_Box_Location"), "'", "''")
   else
   strLock_Box_Location = ""
   end if
   
   If len(Request.form("Suggested_Anchors_Com")) > 0 then
   strSuggested_Anchors_Com = Replace(Request.form("Suggested_Anchors_Com"), "'", "''")
   else
   strSuggested_Anchors_Com = ""
   end if
   
   If len(Request.form("Add_PPE")) > 0 then
   strAdd_PPE = Replace(Request.form("Add_PPE"), "'", "''")
   else
   strAdd_PPE = ""
   end if
   
   If len(Request.form("Prerigging_Com")) > 0 then
   strPrerigging_Com = Replace(Request.form("Prerigging_Com"), "'", "''")
   else
   strPrerigging_Com = ""
   end if
   
   If len(Request.form("Entrant_1")) > 0 then
   strEntrant_1 = Replace(Request.form("Entrant_1"), "'", "''")
   else
   strEntrant_1 = ""
   end if
   
   If len(Request.form("Entrant_2")) > 0 then
   strEntrant_2 = Replace(Request.form("Entrant_2"), "'", "''")
   else
   strEntrant_2 = ""
   end if
   
   If len(Request.form("Attendant")) > 0 then
   strAttendant = Replace(Request.form("Attendant"), "'", "''")
   else
   strAttendant = ""
   end if
   
   If len(Request.form("Safety")) > 0 then
   strSafety = Replace(Request.form("Safety"), "'", "''")
   else
   strSafety = ""
   end if
   
   If len(Request.form("Main_Line")) > 0 then
   strMain_Line = Replace(Request.form("Main_Line"), "'", "''")
   else
   strMain_Line = ""
   end if
   
   If len(Request.form("Belay_Line")) > 0 then
   strBelay_Line = Replace(Request.form("Belay_Line"), "'", "''")
   else
   strBelay_Line = ""
   end if
   
   If len(Request.form("Backup")) > 0 then
   strBackup = Replace(Request.form("Backup"), "'", "''")
   else
   strBackup = ""
   end if
   
   If len(Request.form("Tester")) > 0 then
   strTester = Replace(Request.form("Tester"), "'", "''")
   else
   strTester = ""
   end if
   
   If len(Request.form("Recommend_Evac_1")) > 0 then
   strRecommend_Evac_1 = Replace(Request.form("Recommend_Evac_1"), "'", "''")
   else
   strRecommend_Evac_1 = ""
   end if
   
   If len(Request.form("Recommend_Evac_2")) > 0 then
   strRecommend_Evac_2 = Replace(Request.form("Recommend_Evac_2"), "'", "''")
   else
   strRecommend_Evac_2 = ""
   end if
   
   If len(Request.form("Recommend_Evac_3")) > 0 then
   strRecommend_Evac_3 = Replace(Request.form("Recommend_Evac_3"), "'", "''")
   else
   strRecommend_Evac_3 = ""
   end if
   
   If len(Request.form("Space_Desc")) > 0 then
   strSpace_Desc = Replace(Request.form("Space_Desc"), "'", "''")
   else
   strSpace_Desc = ""
   end if
   
   If len(Request.form("Entry_Super")) > 0 then
   strEntry_Super = Replace(Request.form("Entry_Super"), "'", "''")
   else
   strEntry_Super = ""
   end if
   
   If len(Request.form("Super_Phone")) > 0 then
   strSuper_Phone = Replace(Request.form("Super_Phone"), "'", "''")
   else
   strSuper_Phone = ""
   end if
   
   If len(Request.form("Sup_Date")) > 0 then
   strSup_Date = Replace(Request.form("Sup_Date"), "'", "''")
   else
   strSup_Date = ""
   end if
   
   
   If len(Request.form("Notes")) > 0 then
   strNotes = Replace(Request.form("Notes"), "'", "''")
   else
   strNotes = ""
   end if
   
   If len(Request.form("Other_Com")) > 0 then
   strOther_Com = Replace(Request.form("Other_Com"), "'", "''")
   else
   strOther_Com = ""
   end if
   
      If len(Request.form("Portal_Location")) > 0 then
   strPortalLocation = Replace(Request.form("Portal_Location"), "'", "''")
   else
    strPortalLocation = ""
   end if
   
   If len(Request.form("Rescue_Req_Comment")) > 0 then
  strRescue_Req_Comment =  Replace(Request.form("Rescue_Req_Comment"), "'", "''")
  else
  strRescue_Req_Comment = ""
  end if 

   
   If Request.Form("Space_Category_1") = "ON"  then
   strSpace_Category_1 = 1
   else
   strSpace_Category_1 = 0
   end if
   
   If Request.Form("Space_Category_2") = "ON"  then
   strSpace_Category_2 = 1
   else
   strSpace_Category_2 = 0
   end if
   
   If Request.Form("Access_Elev_Yes") = "ON"  then
   strAccess_Elev_Yes = 1
   else
   strAccess_Elev_Yes = 0
   end if
   
   If Request.Form("Access_Elev_No") = "ON"  then
   strAccess_Elev_No = 1
   else
   strAccess_Elev_No = 0
   end if
   
   If Request.Form("Access_Restricted_Yes") = "ON"  then
   strAccess_Restricted_Yes = 1
   else
   strAccess_Restricted_Yes = 0
   end if
   
   If Request.Form("Access_Restricted_No") = "ON"  then
   strAccess_Restricted_No = 1
   else
   strAccess_Restricted_No = 0
   end if
   
   If Request.Form("Rescue_Req") = "ON"  then
   strRescue_Req = 1
   else
   strRescue_Req = 0
   end if
   
   If Request.Form("Raise_Lower_3_1") = "ON"  then
   strRaise_Lower_3_1 = 1
   else
   strRaise_Lower_3_1 = 0
   end if
   
   If Request.Form("Raise_Lower_4_1") = "ON"  then
   strRaise_Lower_4_1 = 1
   else
   strRaise_Lower_4_1 = 0
   end if
   
   If Request.Form("Tripod") = "ON"  then
   strTripod = 1
   else
   strTripod = 0
   end if
   
   If Request.Form("Main_Line_Red") = "ON"  then
   strMain_Line_Red = 1
   else
   strMain_Line_Red = 0
   end if
   
   If Request.Form("Belay_Line_Blue") = "ON"  then
   strBelay_Line_Blue = 1
   else
   strBelay_Line_Blue = 0
   end if
   
   If Request.Form("Gear_Bag_Black") = "ON"  then
   strGear_Bag_Black = 1
   else
   strGear_Bag_Black = 0
   end if
   
   If Request.Form("Yellow_Bag") = "ON"  then
   strYellow_Bag = 1
   else
   strYellow_Bag = 0
   end if
   
   If Request.Form("Suggested_Anchors") = "ON"  then
   strSuggested_Anchors = 1
   else
   strSuggested_Anchors = 0
   end if
   
   If Request.Form("Prerigging") = "ON"  then
   strPrerigging = 1
   else
   strPrerigging = 0
   end if
   
   If Request.Form("Prefab_Anchor") = "ON"  then
   strPreFab_Anchor = 1
   else
   strPreFab_Anchor = 0
   end if
   
   If Request.Form("Beam") = "ON"  then
   strBeam = 1
   else
   strBeam = 0
   end if
   
   If Request.Form("Handrail") = "ON"  then
   strHandrail = 1
   else
   strHandrail = 0
   end if
   
   If Request.Form("Strut") = "ON"  then
   strStrut = 1
   else
   strStrut = 0
   end if
   
   If Request.Form("Stairwell") = "ON"  then
   strStairwell = 1
   else
   strStairwell = 0
   end if
   
   If Request.Form("Steel_Pipe") = "ON"  then
   strSteel_Pipe = 1
   else
   strSteel_Pipe = 0
   end if
   
   If Request.Form("Support_Column") = "ON"  then
   strSupport_Column = 1
   else
   strSupport_Column = 0
   end if
   
   
   If Request.Form("Rescue_External") = "ON"  then
   strRescue_External = 1
   else
   strRescue_External = 0
   end if
   
   If Request.Form("Rescue_Congested") = "ON"  then
   strRescue_Congested = 1
   else
   strRescue_Congested = 0
   end if
   
   If Request.Form("Rescue_Lowering") = "ON"  then
   strRescue_Lowering = 1
   else
   strRescue_Lowering = 0
   end if
   
   If Request.Form("C_Collar") = "ON"  then
   strC_Collar = 1
   else
   strC_Collar = 0
   end if
   
   If Request.Form("Trauma_Kit") = "ON"  then
   strTrauma_Kit = 1
   else
   strTrauma_Kit = 0
   end if
   
   If Request.Form("Half_Back") = "ON"  then
   strHalf_Back = 1
   else
   strHalf_Back = 0
   end if
   
   If Request.Form("Sked") = "ON"  then
   strSked = 1
   else
   strSked = 0
   end if
   
   If request.form("SCBA") = "ON" Then
   strSCBAInPortal = 1
   else
   strSCBAInPortal = 0
   end if
   
   strCSR_One = Trim(request.form("CSR_1"))
    strCSR_Two = Trim(request.form("CSR_2"))
     strCSR_Three = Trim(request.form("CSR_3"))
     
     strTrauma_One = Trim(Request.form("Trauma_1"))
    strTrauma_Two = Trim(Request.form("Trauma_2"))
     strTrauma_Three = Trim(Request.form("Trauma_3"))
     
   strPhoto = Request.form("Photo")
   strAttachment = Request.form("Attachment")
   

   
   strsql =  "INSERT INTO tblRescue (Approval_Date, Approver_One, Approver_Two, ERT_Map, Lock_Box_Location, Space_Category_1, Space_Category_2,  "_
   &" Access_Elev_Yes, Access_Elev_No, Access_Restricted_Yes, Access_Restricted_No, Raise_Lower_3_1, "_
   &" Raise_Lower_4_1, Tripod, Main_Line_Red, Belay_Line_Blue, Gear_Bag_Black, Yellow_bag, Suggested_Anchors, "_
   &" Prerigging, PreFab_Anchor, Beam, Handrail, Stairwell, Strut, Steel_Pipe, Support_Column, Sked, "_
   &" Rescue_External, Rescue_Congested, Rescue_Lowering, C_Collar, Trauma_Kit, Half_Back, "_
      &" CSR_One, CSR_Two, CSR_Three, Trauma_One, Trauma_Two, Trauma_Three, Add_PPE, "_
        &" Entrant_1, Attendant, Entrant_2, Safety, Main_Line, Belay_Line, Back_up, Tester,  "_
           &" Recommend_Evac_1, Recommend_Evac_2, Recommend_Evac_3, Entry_Super, Super_Phone, Completed_By,  "_
              &" Notes,  Sup_Date, Space_Desc, Other_Com, "_
               &" Summon_Rescue, Rescue_Req, Rescue_Req_Comment, Suggested_Anchors_Com, Prerigging_Com, Space_ID, Photo, Attachment, Portal_Location, SCBAInPortal)"_
   &" SELECT '" & strApproval_Date & "', '" & strApprover_One & "', '" & strApprover_Two & "', '" & strERT_Map & "', "_
   &" '" & strLock_Box_Location & "', " & strSpace_Category_1 & ", " & strSpace_Category_2 & ", "_
   &" " & strAccess_Elev_Yes & ", " & strAccess_Elev_No & ", " & strAccess_Restricted_Yes & ", " & strAccess_Restricted_No & ", " & strRaise_Lower_3_1 & ", "_
   &" " & strRaise_Lower_4_1 & ", " & strTripod & ", " & strMain_Line_Red & ", " & strBelay_Line_Blue & ", " & strGear_Bag_Black & ", "_
   &" " & strYellow_bag & ", " & strSuggested_Anchors & ", "_
   &" " & strPrerigging & ", " & strPreFab_Anchor & ", " & strBeam & ", " & strHandrail & ", " & strStairwell & ", "_
   &" " & strStrut & ", " & strSteel_Pipe & ", " & strSupport_Column & ", "_
   &" " & strRescue_External & ", " & strRescue_Congested & ", " & strRescue_Lowering & ", " & strC_Collar & ", "_
   &" " & strTrauma_Kit & ", " & strHalf_Back & ", " & strSked & ", "_
   &" '" & strCSR_One & "', '" & strCSR_Two & "', '" & strCSR_Three & "', '" & strTrauma_One & "', '" & strTrauma_Two & "', '" & strTrauma_Three & "', '" & strAdd_PPE & "', "_
   &"  '" & strEntrant_1 & "', '" & strAttendant & "', '" & strEntrant_2 & "', '" & strSafety & "', '" & strMain_Line & "', "_
   &" '" & strBelay_Line & "', '" & strBackup & "', '" & strTester & "', "_
   &" '" & strRecommend_Evac_1 & "', '" & strRecommend_Evac_2 & "', '" & strRecommend_Evac_3 & "', '" & strEntry_Super & "', "_
   &" '" & strSuper_Phone & "', '" & Session("Ename") & "', '" & strNotes & "', '" & strSup_Date & "', '" & strSpace_Desc & "', "_
   &" '" & strOther_Com & "', "_
   &"  '" & strSummon_Rescue & "', " & strRescue_Req & ", '" & strRescue_Req_Comment & "', '" & strSuggested_Anchors_Com & "', '" & strPrerigging_Com & "', "_
   &" '" & strSpace_ID & "', '" & strPhoto & "', '" & strAttachment & "', '" & strPortalLocation & "', " & strSCBAInPortal & ""

   
   		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
			End Function
  Function GetData()
  Dim strtype
        set objEPS = new ASP_CLS_ProcedureESL
        strType = "T"
        set rstTrauma = objEPS.Stations(strType)
        strType = "C"
 	set rstCSR = objEPS.stations(strType)
 
	

    End Function

  %> 
<br>


</body>

</html>
<!--#include file="footer.inc"-->