																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<!DOCTYPE html><html><head>

<script type="text/javascript">
    function StripFirstChars(TrlrNumb) {
        var StripStr = TrlrNumb.match(/\d+\.?\d*/);
        return TrlrNumb.substring(TrlrNumb.indexOf(StripStr));
    }
</script>
<% Sub StripLetters()
Response.write("<script language='JavaScript'>var StripStr = TrlrNumb.match(/\d+\.?\d*/); return TrlrNumb.substring(TrlrNumb.indexOf(StripStr)); </script>")
End Sub
%>


<% language="javascript" 

   function StripFirstChars(TrlrNumb)
    {
        var StripStr = TrlrNumb.match(/\d+\.?\d*/);
        return TrlrNumb.substring(TrlrNumb.indexOf(StripStr));
    }
%>

<script>
var originalString;
var strippedString;
function stripNumbers()

{
strippedString = originalString.replace(/[a-z]/g, "");
strippedString = strippedString.replace(/[A-Z]/g, "");

document.getElementById("strippedstring").innerHTML=strippedString;
}
window.onload=function()
{originalString = "JavaScript123 Tutorials123!";
document.getElementById("originalstring").innerHTML=originalString;
}
</script>
</head>
<body>

Original String is: <span id="originalstring"></span>
</br>Stripped String is: <span id="strippedstring"></span></br>
<button onClick="stripNumbers();">Click Here!</button></body></html>

<% strTest = "JA1234"
strTrailer = StripFirstChars(strTest) %><%= strTrailer %>