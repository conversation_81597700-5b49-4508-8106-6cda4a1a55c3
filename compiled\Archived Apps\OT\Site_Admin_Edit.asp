<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<head>

<TITLE>Modify Administrator</TITLE>
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_Session.asp"-->
<!--#include file="classes/asp_cls_DataAccess.asp"-->
</head>
<style type="text/css">
.style1 {
	font-family: Arial;
	text-align: center;
}
.style3 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
.style4 {
	text-align: left;
}
.style5 {
	font-family: Arial;
	font-size: medium;
}
.style6 {
	text-align: center;
}
</style>
<% 
 dim strCode, strDescription, strid, strsql, MyRec, strName, strBID, strMill, strType
strid = request.querystring("id")


strsql = "SELECT tblOT_Admin.* from tblOT_Admin where ID = " & strid

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
If not MyRec.eof then
strname = MyRec.fields("Emp_Name")
strBID= MyRec.fields("EMP_ID")
 

end if
MyRec.close

 set objGeneral = new ASP_CLS_General


if objGeneral.IsSubmit() Then


	Call SaveData() 

End if %>
<body><form name="form1" action="Site_Admin_edit.asp?id=<%= strid%>" method="post">
 
<table border="0" cellpadding="0" cellspacing="0" width = 60% style="border-collapse: collapse" bordercolor="000000" height="25" align = center >
  <tr>
    <td  bgcolor="#FFFFFF" bordercolor="#CCCCFF" height="25" >
    <p align="center"><font face="Arial" size="4">Modify </font>
	<span class="style5">Administrator </span></td>
    <td align = center height="25"><font face="Arial"><b><a href="Site_Admins.asp">RETURN</a></b></font></td>

    <td align = center bgcolor="#FFFFFF" height="25">
  <Input name="Submit" type="submit" Value="Submit" style="float: right" ></td>
  </tr>
</table><br><br>
<div align="center">
<table cellpadding="0" cellspacing="0" style="width: 35%;" bordercolor="#808080"  height="10" class="style3">
    <tr>
<td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" align="left" style="height: 39px" ><b>
	<font face="Arial">Name (Last, First) </font></b></td>

<td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" class="style1" style="height: 39px" >
	<strong>Employee ID</strong></td>   
   
  </tr>
  
    <tr>
    <td  bordercolor="#CCCCFF" height="47"  align="center" class="style4" >   <font face="Arial">	
		<input type="text" name="Name" size="35" value="<%= strName %>" style="width: 177px" tabindex="1">
</font></td>
    	  	
   <td  bordercolor="#CCCCFF" height="47"  align="center" class="style6" >  	
     <font face="Arial">	
		<input type="text" name="BID" size="35" value="<%= strBID %>" style="width: 79px; height: 22px;" tabindex="3"></font></td> 


  </tr>
 
  </table>
</div>



</form>
   
  

</body>
 <%

  
  Function SaveData()

strName = Replace(Request.form("Name"), "'", "''")   
strBID = Request.form("BID") 
 
  
  strsql = "Update tblOT_Admin set Emp_Name = '" & strName & "', Emp_ID = '" & strBID & "' where ID = " & strid 
  	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
          Response.redirect("Site_Admins.asp")
  End Function
  
   %><!--#include file="footer.inc"-->