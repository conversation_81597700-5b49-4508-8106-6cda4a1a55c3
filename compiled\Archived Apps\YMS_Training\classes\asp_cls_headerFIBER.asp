﻿
<html>
<head>
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">

<meta name="ProgId" content="FrontPage.Editor.Document">

<title>Kimberly<PERSON>Clark Mobile Fiber Logistics Management</title>
<SCRIPT LANGUAGE="JavaScript1.2" SRC="scripts/menubar.js"></SCRIPT>
<link rel="stylesheet" href="scripts/KCStyle.css" type="text/css">

</head>


<body>
<b>    <font face=Arial>              Mobile         |      Fiber Logistics Management </b>


</font> <br>
<div class="menuBar">
<img border="0" align="left" src="images/internal.gif" alt="Internal Security Level"> &nbsp;&nbsp;&nbsp;&nbsp;
<a class="menuButton" href="FiberIndex.asp">HOME</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<a class="menuButton" href="" onclick="return buttonClick(event, 'menu1');" onmouseover="buttonMouseover(event, 'menu1);">&nbsp;&nbsp;Fiber Receipts/Transfers</a>

&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;

<a class="menuButton" href="" onclick="return buttonClick(event, 'menu6');" onmouseover="buttonMouseover(event, 'menu6);">All-Other Receipt</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;

<a class="menuButton" href="" onclick="return buttonClick(event, 'menu8');" onmouseover="buttonMouseover(event, 'menu8);">&nbsp;&nbsp;Car-Out Trailer</a>

&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;

<a class="menuButton" href="" onclick="return buttonClick(event, 'menu3');" onmouseover="buttonMouseover(event, 'menu3);">&nbsp;&nbsp;Unload/Grading</a>

&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;


<a class="menuButton" href="" onclick="return buttonClick(event, 'menu2');" onmouseover="buttonMouseover(event, 'menu2);">&nbsp;&nbsp;Reports</a>

&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;

<a class="menuButton" href="" onclick="return buttonClick(event, 'menu9');" onmouseover="buttonMouseover(event, 'menu9);">Guard Activities</a>
&nbsp;&nbsp;&nbsp;

<a class="menuButton" href="" onclick="return buttonClick(event, 'menu5');" onmouseover="buttonMouseover(event, 'menu5);">&nbsp;&nbsp;System Maint</a>
<a class="menuButton" href="" onclick="return buttonClick(event, 'menu10');" onmouseover="buttonMouseover(event, 'menu10);">&nbsp;RF Buyer</a>&nbsp;&nbsp;&nbsp;
</div>

<!-- Main menus. -->


<div id="menu1" class="menu" onmouseover="menuMouseover(event)">
<a class="menuItem" href="SelectRelease.asp">Enter Truck Receipt</a>
<a class="menuItem" href="SelectSTO.asp">Enter STO Receipt</a>
<a class="menuItem" href="Boise_receipt.asp">Enter Boise Cascade Receipt</a>
<a class="menuItem" onclick="return false;" onmouseover="menuItemMouseover(event, 'menu1_1');"><span class="menuItemText"><b>Transfer From Yard</b></span></a>
<a class="menuItem" onclick="return false;" onmouseover="menuItemMouseover(event, 'menu1_2');"><span class="menuItemText"><b>Transfer From Warehouse</b></span></a>

<a class="menuItem" href="Transfer_From_DF.asp">Transfer Load from Dry Fiber</a>
<a class="menuItem" href="SelectTruckfromRail.asp">Transfer from Rail Car</a>
<a class="menuItem" href="AssignActual.asp">Trucks to be Re-Weighed</a>
<a class="menuItem" onclick="return false;" onmouseover="menuItemMouseover(event, 'menu1_4');"><span class="menuItemText"><b>Edit Receipts</b></span></a>

<a class="menuItem" onclick="return false;" onmouseover="menuItemMouseover(event, 'menu1_3');"><span class="menuItemText"><b>Edit Transfers</b></span></a>



</div>

<div id="menu1_1" class="menu" onmouseover="menuMouseover(event)">
<a class="menuItem" href="SelectTruckYtoDC.asp">Transfer Truck From Yard to DC WHSE</a>
<a class="menuItem" href="SelectTruckYtoM.asp">Transfer Truck From Yard to Merchants</a>
<a class="menuItem" href="SelectTruckYtoCotton.asp">Transfer Truck From Yard to Cotton Street</a>
<a class="menuItem" href="SelectTruckYtoPJ.asp">Transfer Truck From Yard to PJ WHSE</a>
<a class="menuItem" href="SelectTruckYtoMeyer.asp">Transfer Truck From Yard to Meyer Building</a>
</div>

<div id="menu1_2" class="menu" onmouseover="menuMouseover(event)">
<a class="menuItem" href="Trans_DC_WH_Select.asp">Transfer Load from DC WHSE</a>
<a class="menuItem" href="Trans_MWH_Select.asp">Transfer Load from Merchants</a>
<a class="menuItem" href="Trans_Cotton_WH_Select.asp">Transfer Load from Cotton Street</a>
<a class="menuItem" href="Trans_PJ_Select.asp">Transfer Load from PJ WHSE</a>
<a class="menuItem" href="Trans_Meyer_Select.asp">Transfer Load from Meyer Building</a>
</div>


<div id="menu1_3" class="menu" onmouseover="menuMouseover(event)">
<a class="menuItem" href="DC_Trucks_Edit.asp">Yard to DC WHSE</a>
<a class="menuItem" href="Merchants_Trucks_Edit.asp">Yard to Merchants</a>
<a class="menuItem" href="Cotton_Trucks_Edit.asp">Yard to Cotton Street</a>
<a class="menuItem" href="PJ_Trucks_Edit.asp">Yard to PJ Warehouse</a>
<a class="menuItem" href="Meyer_Trucks_Edit.asp">Yard to Meyer Building</a>

<a class="menuItem" href="Edit_Shuttle.asp"> From DC Warehouse </a>
<a class="menuItem" href="Edit_Merchants_Shuttle.asp">From Merchants </a>
<a class="menuItem" href="Edit_Cotton_Shuttle.asp">From Cotton Street </a>
<a class="menuItem" href="Edit_PJ_Shuttle.asp">From PJ Warehouse </a>
<a class="menuItem" href="Edit_Meyer_Shuttle.asp">From Meyer Building </a>


</div>

<div id="menu1_4" class="menu" onmouseover="menuMouseover(event)">
<a class="menuItem" href="Edit_Yard_Trucks.asp">Edit Trucks in Yard</a>
<a class="menuItem" href="Edit_STO_Trucks.asp">Edit STO Receipt</a>
<a class="menuItem" href="Edit_DF_Shuttle.asp">Edit Transfers From Dry Fiber </a>
</div>


<div id="menu2" class="menu" onmouseover="menuMouseover(event)">

<a class="menuItem" onclick="return false;" onmouseover="menuItemMouseover(event, 'menu2_2');"><span class="menuItemText"><b>Yard Inventory Reports</b></span><span class="menuItemArrow">&#9654;</span></a>
<a class="menuItem" onclick="return false;" onmouseover="menuItemMouseover(event, 'menu2_3');"><span class="menuItemText"><b>Inbound Reports</b></span><span class="menuItemArrow">&#9654;</span></a>
<a class="menuItem" onclick="return false;" onmouseover="menuItemMouseover(event, 'menu2_4');"><span class="menuItemText"><b>Inventory Consumption</b></span><span class="menuItemArrow">&#9654;</span></a>
<a class="menuItem" onclick="return false;" onmouseover="menuItemMouseover(event, 'menu2_5');"><span class="menuItemText"><b>RF Orders/Arrivals/Fill Rates</b></span><span class="menuItemArrow">&#9654;</span></a>
<a class="menuItem" onclick="return false;" onmouseover="menuItemMouseover(event, 'menu2_6');"><span class="menuItemText"><b>Non-Fiber Search</b></span><span class="menuItemArrow">&#9654;</span></a>
<a class="menuItem" onclick="return false;" onmouseover="menuItemMouseover(event, 'menu2_7');"><span class="menuItemText"><b>Research Tools</b></span><span class="menuItemArrow">&#9654;</span></a>
<a class="menuItem" onclick="return false;" onmouseover="menuItemMouseover(event, 'menu2_8');"><span class="menuItemText"><b>Warehouse Activity</b></span><span class="menuItemArrow">&#9654;</span></a>

 
<a class="menuItem" href="Daily_inventory_rpt2019Rail.asp">Daily Inventory Report 2019</a>
<a class="menuItem" href="Daily_inventory_rpt_RF.asp">Daily Inventory Report RF</a>
<a class="menuItem" href="Daily_inventory_rpt_OCC.asp">Daily Inventory Report OCC</a>
<a class="menuItem" href="Daily_inventory_rpt_Broke.asp">Broke Daily Inventory Report</a>
<a class="menuItem" href="Grade_report_view.asp">Grading Reports</a>
<a class="menuItem" href="Grade_report_Low_Quality.asp">Low Quality Grading Reports</a>
<a class="menuItem" href="Vendor_Weight_Exceptions.asp">Vendor weights</a>

</div>


<div id="menu2_2" class="menu" onmouseover="menuMouseover(event)">
<a class="menuItem" href="RPYardReportnoOCC.asp">RF Yard Inventory Report</a>
<a class="menuItem" href="RPYardReportOCC.asp">OCC Yard Inventory Report</a>
<a class="menuItem" href="YardReportBroke.asp">Broke Yard Inventory Report</a>
<a class="menuItem" href="YardReportBoise.asp">Bosie Cascade Yard Inventory Report</a>
<a class="menuItem" href="VFYardReport.asp">VF Yard Inventory Rpt</a>

<a class="menuItem" href="NFYardReport.asp">KDF Yard Inventory Rpt</a>

<a class="menuItem" href="WADYardReport.asp">Wadding Yard Inventory Rpt</a>

<a class="menuItem" href="RPYardReport.asp">Yard Inventory Report</a>

<a class="menuItem" href="RPYardReportbycarrier.asp">Yard Inventory Report by Carrier</a>

<a class="menuItem" href="Unresolved.asp">Unresolved Yard Inventory Report</a>
</div>

<div id="menu2_3" class="menu" onmouseover="menuMouseover(event)">
<a class="menuItem" href="Inbound.asp">Inbound</a>
<a class="menuItem" href="Inbound_load_by_species.asp">Inbound Load Totals</a>
<a class="menuItem" href="Inbound_Duplicates.asp">Inbound Duplicates</a>

</div>


<div id="menu2_4" class="menu" onmouseover="menuMouseover(event)">

<a class="menuItem" href="Inventory_Reduction_Summary.asp">Fibers Waste Paper Consumption</a>
<a class="menuItem" href="Inventory_reduction_rpt.asp">Inventory Reduction Report</a>
<a class="menuItem" href="Trailer_Arrival_Summary.asp">Comsumption/Arrival Summary</a>

</div>

<div id="menu2_5" class="menu" onmouseover="menuMouseover(event)">
<a class="menuItem" href="RP_Search.asp">Recovered Paper Orders</a>
<a class="menuItem" href="Trailer_Arrival_Search.asp">Recovered Paper Arrivals</a>
<a class="menuItem" href="Trailer_receiving_report.asp">Trailer Receiving Report</a>
<a class="menuItem" href="Trailer_unload_report.asp">Trailer Unload Report</a>
<a class="menuItem" href="Broke_unload_report.asp">Broke Unload Report</a>
<a class="menuItem" href="Vendor_rating.asp">Vendor Quality Rating</a>
<a class="menuItem" href="RPO_Select_month.asp">Vendor Fill Rates</a>
<a class="menuItem" href="MOB_Select_Dates.asp">Vendor Fill Rates by Species</a>
<a class="menuItem" href="Rail_Cars_Received.asp">Rail Cars Received</a>
</div>


<div id="menu2_6" class="menu" onmouseover="menuMouseover(event)">
<a class="menuItem" href="OP_material_search.asp">Non-Fiber Material Search</a>
<a class="menuItem" href="NF_Trailer_History.asp">Non-Fiber Trailer History</a>

</div>

<div id="menu2_7" class="menu" onmouseover="menuMouseover(event)">

<a class="menuItem" href="Trailer_history.asp">Movement History</a>
<a class="menuItem" href="Deleted_records.asp">Deleted Records</a>
<a class="menuItem" href="Duplicates.asp">Duplicate Receipts</a>
<a class="menuItem" href="Lazer Exceptions.asp">Yard Inventory Exceptions</a>
<a class="menuItem" href="Rented_trailer_history.asp">Rented Trailer History</a>
<a class="menuItem" href="Mobile_Rail_report.asp">Rail Car Report</a>
</div>




<div id="menu2_8" class="menu" onmouseover="menuMouseover(event)">

<a class="menuItem" href="Baldwin_DC_rpt.asp">Activity From Warehouse</a>

<a class="menuItem" href="To_Baldwin_DC_rpt.asp">Activity To Warehouse</a>

<a class="menuItem" href="Warehouse_Rpt.asp">Warehouse Movement Summary</a>

</div>

<div id="menu3" class="menu" onmouseover="menuMouseover(event)">

<a class="menuItem" href="Grading_list.asp">Edit BWIF Grading Form</a>
<a class="menuItem" href="RF_Grading_list.asp">Edit RF Grading Form</a>


</div>

<div id="menu3" class="menu" onmouseover="menuMouseover(event)">

<a class="menuItem" href="Grading_list.asp">Edit BWIF Grading Form</a>
<a class="menuItem" href="RF_Grading_list.asp">Edit RF Grading Form</a>


</div>
<div id="menu6" class="menu" onmouseover="menuMouseover(event)">
<a class="menuItem" href="Select_Rail.asp">Enter Rail RF/VF Receipt</a>
<a class="menuItem" href="Generic_Receipt_VF.asp">Enter Truck RF Unresolved Species Receipt</a>
<a class="menuItem" href="SelectWeyInbound.asp">Enter KDF Receipt</a>
<a class="menuItem" href="KDF_Edit_received.asp">Edit KDF Receipt</a>
<a class="menuItem" href="Edit_NF_Yard_Trucks.asp">Edit Trucks in Yard</a>
<a class="menuItem" onclick="return false;" onmouseover="menuItemMouseover(event, 'menu6_1');"><span class="menuItemText"><b>Wadding</b></span><span class="menuItemArrow">&#9654;</span></a>
<a class="menuItem" href="Unclassified_Broke.asp">Classify BROKE </a>
</div>

<div id="menu6_1" class="menu" onmouseover="menuMouseover(event)">
<a class="menuItem" href="Wad_Tsf_Merchants.asp">Transfer From Merchants </a>
<a class="menuItem" href="Edit_Merchants_WAD_Shuttle.asp">Edit Transfers From Merchants </a>
<a class="menuItem" href="Wad_Tsf_Fayard.asp">Transfer From Fayard </a>
<a class="menuItem" href="Edit_Fayard_WAD_Shuttle.asp">Edit Transfers From Fayard </a>
</div>

<div id="menu8" class="menu" onmouseover="menuMouseover(event)">
<a class="menuItem" href="SelectTruckUnload.asp">Recovered Paper</a>
<a class="menuItem" href="SelectTruckUnloadNF.asp">Non-Recovered Paper</a>
<a class="menuItem" href="SelectTruckUnloadBroke.asp">Broke</a>
<a class="menuItem" href="SelectTruckUnloadBoise.asp">Boise Cascade</a>
</div>

<div id="menu9" class="menu" onmouseover="menuMouseover(event)">
<a class="menuItem" href="Send_yard_email.asp">Yard Report Email</a>


</div>

<div id="menu5" class="menu" onmouseover="menuMouseover(event)">
<a class="menuItem" onclick="return false;" onmouseover="menuItemMouseover(event, 'menu5_3');"><span class="menuItemText"><b>Edit Records</b></span><span class="menuItemArrow">&#9654;</span></a>
<a class="menuItem" href="Rejected_Load_list.asp">Rejected Loads</a>

<a class="menuItem" href="Select_Receipt_Adj.asp">Adjust Receipt Weight</a>

<a class="menuItem" href="Incoming_KDF.asp">Edit Inbound KDF records</a>
<a class="menuItem" href="Request_release.asp">Edit Broke Type</a>
<a class="menuItem" href="SAPISM_Species.asp">Edit STO Type</a>
<a class="menuItem" href="Plant.asp">Edit STO Plant Options</a>
<a class="menuItem" href="Rented_trailers.asp">Edit Rented Trailers</a>
<a class="menuItem" href="VF_Inbound.asp">In-Transit Loads</a>
<a class="menuItem" onclick="return false;" onmouseover="menuItemMouseover(event, 'menu5_1');"><span class="menuItemText"><b>Projections</b></span><span class="menuItemArrow">&#9654;</span></a>

<a class="menuItem" href="VF_Species.asp">Species Options</a>
<a class="menuItem" href="VF_Vendor.asp">VF Vendor Options</a>
<a class="menuItem" href="Carrier.asp">Maintain Carriers</a>
<a class="menuItem" href="Vendors.asp">RF Vendor List</a>

<a class="menuItem" href="Receipt_Delete_Report.asp">RF Receipt Deletion Report</a>
<a class="menuItem" href="RPC_Select_Month.asp">RF Consumption</a>
<a class="menuItem" href="RF_Vendor.asp">Vendor Tier Management</a>
<a class="menuItem" onclick="return false;" onmouseover="menuItemMouseover(event, 'menu5_2');"><span class="menuItemText"><b>Resolve Exceptions</b></span><span class="menuItemArrow">&#9654;</span></a>
<a class="menuItem" href="Invalid_Admin.asp">Invalid Admin Records</a>
<a class="menuItem" href="Select_Loads_To_Verify.asp">Identify Loads to Verify Weight</a>
<a class="menuItem" href="YardReportBrokeOption.asp">Broke Loads</a>
<a class="menuItem" href="YardReportOCCoption.asp">OCC Loads</a>
<a class="menuItem" onclick="return false;" onmouseover="menuItemMouseover(event, 'menu5_4');"><span class="menuItemText"><b>Trailer Designation</b></span><span class="menuItemArrow">&#9654;</span></a>

<a class="menuItem" href="Documents/Grades and Species.docx">Reference Documentation</a>
</div>

<div id="menu5_1" class="menu" onmouseover="menuMouseover(event)">
<a class="menuItem" href="Facility.asp">Consumption Projections</a>
<a class="menuItem" href="Facility_New.asp">Consumption Projections 2020</a>
<a class="menuItem" href="BC.asp">Broke Consumption Projections</a>
<a class="menuItem" href="RC_Projections.asp">Rail Car Target</a>


</div>


<div id="menu5_2" class="menu" onmouseover="menuMouseover(event)">
<a class="menuItem" href="Resolve_Exceptions.asp">RF Trailers</a>
<a class="menuItem" href="Resolve_Exceptions_VF.asp">VF Trailers</a>
<a class="menuItem" href="Resolve_Exceptions_Rail.asp">Rail</a>
<a class="menuItem" href="Resolve_STO_Exceptions.asp">STO</a>
</div>

<div id="menu5_3" class="menu" onmouseover="menuMouseover(event)">

<a class="menuItem" href="Sixty_day_list_Shuttles.asp">Shuttles</a>
<a class="menuItem" href="Sixty_day_list_rail.asp">Rail Cars</a>
<a class="menuItem" href="NF_Sixty_day_list.asp">KDF</a>
<a class="menuItem" href="Sixty_day_list_wadding.asp">Wadding</a>
<a class="menuItem" href="Sixty_day list.asp">All Others</a>
</div>

<div id="menu5_4" class="menu" onmouseover="menuMouseover(event)">

<a class="menuItem" href="Edit_SHRED_Trucks.asp">RF Trucks in Yard</a>
<a class="menuItem" href="Edit_RF_Trucks.asp">OCC Trucks in Yard</a>
<a class="menuItem" href="Edit_SHRED_Inbound.asp">RF Inbound Trucks</a>
<a class="menuItem" href="Edit_OCC_Inbound.asp">OCC Inbound Trucks</a>

</div>

<div id="menu10" class="menu" onmouseover="menuMouseover(event)">
<a class="menuItem" onclick="return false;" onmouseover="menuItemMouseover(event, 'menu10_1');"><span class="menuItemText"><b>Maintenance</b></span><span class="menuItemArrow">&#9654;</span></a>
<a class="menuItem" onclick="return false;" onmouseover="menuItemMouseover(event, 'menu10_2');"><span class="menuItemText"><b>Reports</b></span><span class="menuItemArrow">&#9654;</span></a>
</div>

<div id="menu10_1" class="menu" onmouseover="menuMouseover(event)">
<a class="menuItem" href="http://www.yms.app.kcc.com/aig/Mobile_pricing_to_Confirm.asp" target="blank">Confirm Pricing</a>
<a class="menuItem" href="Maintain_orders.asp">Edit Orders</a>
<a class="menuItem" href="Grading_Deduction_list.asp">Grading Deductions</a>
<a class="menuItem" href="Rejected_Load_list.asp">Rejected Loads</a>
<a class="menuItem" href="Vendors.asp">RF Vendor List</a>
</div>

<div id="menu10_2" class="menu" onmouseover="menuMouseover(event)">

<a class="menuItem" href="Daily_inventory_rpt2019Rail.asp">Daily Inventory Report 2019</a>
<a class="menuItem" href="Grade_report_view.asp">Grading Reports</a>
<a class="menuItem" href="Inbound.asp">Inbound</a>
<a class="menuItem" onclick="return false;" onmouseover="menuItemMouseover(event, 'menu10_2_1');"><span class="menuItemText"><b>Inbound Duplicates</b></span><span class="menuItemArrow">&#9654;</span></a>
<a class="menuItem" href="RP_Search.asp">Recovered Paper Orders</a>
<a class="menuItem" href="Trailer_history.asp">Trailer Movement </a>
<a class="menuItem" href="Trailer_receiving_report.asp">Trailer Receiving Report</a>

<a class="menuItem" href="Vendor_rating.asp">Vendor Quality Rating</a>
<a class="menuItem" href="RPYardReportnoOCC.asp">RF Yard Inventory Report</a>
<a class="menuItem" href="RPYardReportOCC.asp">OCC yard Inventory Report</a>
<a class="menuItem" href="Receipt_Delete_Report.asp">RF Receipt Deletion Report</a>
<a class="menuItem" href="Damage_list.asp">Damaged Trailer History</a>
</div>

<div id="menu10_2_1" class="menu" onmouseover="menuMouseover(event)">
<a class="menuItem" href="http://www.yms.app.kcc.com/aig/Vendor_duplicates.asp" target="_blank">Current</a>
<a class="menuItem" href="Inbound_duplicates.asp">History</a>
</div>