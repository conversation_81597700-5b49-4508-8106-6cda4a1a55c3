																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Edit Release for Order </TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->


<% Dim MyRec, strsql, MyRec2, strsql2,  MyConn3, strsql3,  strRelease, strVendor, strGenerator, strState, strCity, strBD, strPO

 

  set objGeneral = new ASP_CLS_General
  strid = Request.querystring("id")
  
  strsql = "Select * from tblOrder where OID = " & strid
  
      Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

  

  if objGeneral.IsSubmit() Then	

  

  	
  	If len(request.form("Broke_Description")) > 0 then  
     strBD= Replace(request.Form("Broke_Description"), "'", "''")    
     else
     strBD = ""
     end if	

	strsql =  "Update tblOrder  set  Broke_Description = '" & strBD & "' where OID = " & strid
	
	
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

If len(request.form("po_nbr")) > 1 then

strPO = request.form("po_nbr")	
	
Response.redirect("Order_Edit_list.asp?po=" & strPO)	
	
elseif len(request.form("release")) > 1 then

strRelease = request.form("release")
Response.redirect("Order_Edit_list.asp?r=" & strRelease)	
end if

end if	
%>

<style type="text/css">
.style3 {
	border: 1px solid #000000;
}
.style4 {
	font-family: arial;
	font-size: x-small;
	text-align: left;
}
.style6 {
	font-family: arial;
	font-size: x-small;
	background-color: #FFFFD7;
}
.style7 {
	text-align: left;
}
</style>
</head>

<body>
<br>
	
<form name="form1" action="Broke_type_Edit.asp?id=<%= strid%>" method="post" ID="Form1">

<input type="hidden" name="release" value="<%= request.querystring("r") %>">
<input type="hidden" name="po_nbr" value="<%= request.querystring("o") %>">

<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Edit Broke Type</b></font></td>
<td align = right><font face="Arial"><a href="javascript:history.go(-1);">
<strong>RETURN</strong></a><strong>&nbsp;</strong></td>


</tr>
	    </table>



	<p>&nbsp;</p>
	<table id="table1" style="width: 95%" align="center" class="style3">
<tr bgcolor = #CCCCFF>
		<td bgcolor="#FFFFD7" class="style6" style="height: 48px">PO #</td>
		<td bgcolor="#FFFFD7" class="style6" style="height: 48px">Release #</td>
		<td bgcolor="#FFFFD7" class="style6" style="height: 48px">Vendor</td>
		<td bgcolor="#FFFFD7" class="style6" style="height: 48px">Generator</td>
		<td bgcolor="#FFFFD7" style="height: 48px">

<p class="style4">City</td>
		<td bgcolor="#FFFFD7" class="style6" style="height: 48px">State</td>
	</tr>
	<tr>
		<td><font face = arial size = 2>
		<%= MyRec.fields("PO") %></td>
		<td><font face = arial size = 2>
	<%= MyRec.fields("Release") %></td>
		<td><font face = arial size = 2>
	<%= MyRec.fields("Vendor") %></td>
		<td><font face = arial size = 2>
	<%= MyRec.fields("Generator") %></td>
		<td>
		<p align="center" class="style7"><font face = arial size = 2>
<%= MyRec.fields("City") %></td>
		<td>
		<font face = arial size = 2>
<%= MyRec.fields("State") %></td>
	</tr>
	<tr bgcolor = #CCCCFF>
		<td bgcolor="#FFFFD7" class="style6" style="height: 46px">

SAP #</td>
		<td bgcolor="#FFFFD7" class="style6" style="height: 46px">

Species</td>
		<td bgcolor="#FFFFD7" class="style6" style="height: 46px">

Grade</td>
		<td bgcolor="#FFFFD7" class="style6" style="height: 46px">

Broke Type</td>
		<td bgcolor="#FFFFD7" style="height: 46px">

<p class="style4">Import Month</td>
		<td bgcolor="#FFFFD7" class="style6" style="height: 46px">Weigh Trailer</td>
	</tr>
	<tr>
		<td style="height: 32px"><font face = arial size = 2><%= MyRec.fields("SAP_Nbr") %>	&nbsp;</td>
		<td style="height: 32px"><font face = arial size = 2><%= MyRec.fields("Species") %></td>
		<td style="height: 32px"><font face = arial size = 2><%= MyRec.fields("Grade") %></td>
		<td style="height: 32px"><font face = arial size = 2>
	<input type="text" name="Broke_Description" size="30" style="width: 311px" value="<%= MyRec.fields("Broke_Description") %>" tabindex="3"></td>
		<td style="height: 32px" align="center" class="style7">		<font face = arial size = 2>
<%= MyRec.fields("Import_Month") %></td>
		<td style="height: 32px"><font face = arial size = 2><%= MyRec.fields("Weigh_Trailer") %></td>
	</tr>

</table>
<br>
		

<p align="center"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></p>
</form>
<% MyRec.close %><!--#include file="Fiberfooter.inc"-->