<%
'-----------------------------------------------------------------------------------------------
'-- DECLARATIONS -------------------------------------------------------------------------------
'-----------------------------------------------------------------------------------------------

DIM intXPMonth			'-- INT:  Standard expiration period, in months.
DIM strFileName			'-- CHAR: The name of file
DIM strFilePath			'-- CHAR: The system path of the file
DIM objFSObject			'-- OBJ:  File system object
DIM objTheFile			'-- OBJ:  The file
DIM strDateModified	    	'-- CHAR: Last modified date of the document.
DIM strTimeModified		'-- CHAR: Last modified Time of the document.
DIM strDateExpires		'-- CHAR: Expiration date of the document.
		
'-----------------------------------------------------------------------------------------------
'-- INITIALIZATIONS ----------------------------------------------------------------------------
'-----------------------------------------------------------------------------------------------

intXPMonth   = 36 '	xpMonth is the generic expiration date of this document set, in months.
strFileName  = request.querystring("file")
strFilePath  = request.servervariables("PATH_TRANSLATED")
	
Set objFSObject = CreateObject("Scripting.FileSystemObject")
Set objTheFile  = objFSObject.GetFile(strFilePath)
strDateModified = CStr(FormatDateTime(CDate(objTheFile.DateLastModified),vbLongDate))
strTimeModified = CStr(FormatDateTime(CDate(objTheFile.DateLastModified),vbLongTime))
strDateExpires  = CStr(FormatDateTime(DateAdd("m", intXPMonth,CDate(objTheFile.DateLastModified)),vbLongDate))

'-----------------------------------------------------------------------------------------------
'-- FUNCTIONS ----------------------------------------------------------------------------------
'-----------------------------------------------------------------------------------------------

response.write("&nbsp;<hr width=100% color=darkblue>") 					
response.write("<font size='-1'><center><b> Kimberly-Clark</b> Corporation - Mobile")  	
response.write("<br>")
response.write("Owner: <b>Dennis Helms</b>&nbsp;&nbsp;&nbsp;Author &amp; Publisher:  <a href='mailto:Bryant,%20Darla'>Darla Bryant</a>")
response.write("</font><br>")		
response.write("<font size=""-2"">Last Modified: " & strDateModified & " " & strTimeModified & "&nbsp;&nbsp;&nbsp;")
response.write("Expires: " & strDateExpires & "<br>&nbsp;</font></center>")
   
%>