<html>
<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_Procedure.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>CSE Permit</title>

</head>
<% dim strsql, MyConn, strid, objGeneral, strsql2, strPurpose, strDateIssued, strDateExpired, strBID, strE_name, strTimeIssued
Dim strTimeexpired, strOrigExpired
 If len(Session("EmployeeID")) = 6 or len(Session("Login_ID")) = 6   then




strid = Request.querystring("id")

strNow = Dateadd("h", -5, now())
strDateIssued = formatdatetime(dateadd("h", -2, strnow),2)

strBID = Session("EmployeeID")
strE_name = Session("Ename")
strTimeIssued = formatdatetime(dateadd("h", -2, strnow),3)
strTimeExpired = strTimeIssued
strOrgExpired = strTimeExpired



strsql = "SELECT SDescription, SOP_NO FROM tblSOP  where SOP_NO = '" & strid & "'"

Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
		
set objGeneral = new ASP_CLS_General
  if objGeneral.IsSubmit = True Then
 
  if isnull(Request.form("Purpose")) then
strPurpose = ""
else
strPurpose = Replace(Request.form("Purpose"), "'", "''") 
end if 		

strdateissued = request.form("Date_issued")
strdateexpired = request.form("Date_expired")
strTimeissued = request.form("Hour") 
strTimeexpired = request.form("Ex_hour")
 

  
  If isdate(request.form("Date_expired")) = False then
  	Response.write ("<br><br><font face = arial size = 4 color = red><i><p align = center><b>You must enter a valid Date Expired</font></b></i></p>")
  	elseif strTimeexpired = "" then
  		Response.write ("<br><br><font face = arial size = 4 color = red><i><p align = center><b>You must select a Time Expired</font></b></i></p>")
  
  	elseif len(strPurpose) < 2 then
  	 	Response.write ("<br><br><font face = arial size = 4 color = red><i><p align = center><b>You must enter a Purpose</font></b></i></p>")

  	else	 


		
	strsql2 = "Insert into tblPermit (Space_ID, BID, Author, Date_issued, Date_expired, Time_issued, Time_expired, Purpose) Select '" & strid & "', "_
	&"  '" & strBID & "', '" & strE_name & "', '" & strDateIssued &"', '" & strDateexpired & "', "_
	&" '" & strTimeIssued & "', '" & strTimeExpired & "', '" & strPurpose & "'"
			Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL2
			MyRec.Close 
  		 
  		strsql = "Select max(PID) as Max from tblPermit"
				Set MyRec = Server.CreateObject("ADODB.RecordSet")
			MyRec.Open strSQL, Session("ConnectionString")
			
			dim strpid
			
			strPID = MyRec.fields("max")
			MyRec.close
			
				Dim strdate, strBName
	If len(Session("Ename")) > 2 then
	strBName = Session("Ename")
	else
	strBName = ""
	end if
	strdate = formatdatetime(strnow,0)	
	
strsql = "Insert into tblPermitHistory	(PID, BID, Activity_Date, Status, BID_Name) "_
&" Select " & strpid & ", '" & Session("EmployeeID") & "', '" & strdate & "', 'NEW', '" & strBName & "'"	
		 Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 

			
			
			
  		Response.redirect("CSE_Start_two.asp?id=" & strid & "&pid=" & strpid )
  		
  	end if
  		 end if
 %>
 
 <script language="javascript">

function openWindow(pURL)
{
	myWindow = window.open(pURL, "myLittleCalendar", 'toolbar=no,location=no,directories=no,status=no,menubar=no,scrollbars=no,resizable=no,width=170,height=270');
}


</script><body>
 <p align="left"><b><font face="arial">Confined Space Permit for Space:&nbsp;<%= Myconn.fields("SOP_NO")%> - <%= MyConn.fields("Sdescription")%>
</font></b>
 
 </p><% MyConn.close %>
	<form name="form1" action="CSE_Start.asp?id=<%= strid%>&t=<%= strTimeIssued%>"  method="post" ID="Form1"  >
 
 <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" height="60">
   
  <tr>
    <td bgcolor="#FFFFDD" align="center" bordercolordark="#000000" bordercolor="#FFFFDD">
	<font face="Arial" size="2">Date Issued</font></td>

   
    <td bgcolor="#FFFFDD" align="center" bordercolordark="#000000" bordercolor="#FFFFDD">
	<font face="Arial" size="2">Date Expired (REQUIRED)</font></td>

   
    <td bgcolor="#FFFFDD" align="center" bordercolordark="#000000" bordercolor="#FFFFDD" valign="top">
	<font face="Arial" size="2"><br>Time Issued<br><br></font></td>

   
    <td bgcolor="#FFFFDD" align="center"  bordercolordark="#000000" bordercolor="#FFFFDD" valign="top">
	<font face="Arial" size="2"><br>Time Expired<br><br></font></td>

   
  </tr>
  <tr align = center>
    <td  bgcolor="#FFFFDD" bordercolor="#FFFFDD"  >
     <font face = arial size = 2>	
		<input type="text" name="Date_issued" size="10" value="<%= strDateIssued %>" tabindex="1" ><br>
		</font><font face = "arial" size = "1">
		<INPUT TYPE="image" img border="0" src="calendar.gif" width="79" height="76" VALUE="..." STYLE="font-family: MS Sans Serif,Arial; color: #885B3B; font-size: 12px;" onclick="openWindow('mlcpopup.asp?elt=Date_issued'); return false;" name="I3">
		<br>Click calendar icon to select different Date Issued</td>
	
    <td  bgcolor="#FFFFDD" bordercolor="#FFFFC1"  >
	 <font face = arial size = 2>	
		<input type="text" name="Date_expired" size="10" value="<%= strDateExpired%>" tabindex="2" ><br>
		</font><font face = "arial" size = "1">
		<INPUT TYPE="image" img border="0" src="calendar.gif" width="79" height="76" VALUE="..." STYLE="font-family: MS Sans Serif,Arial; color: #885B3B; font-size: 12px;" onclick="openWindow('mlcpopup.asp?elt=Date_expired'); return false;" name="I4"><br>
		Click Calendar Icon to Select Date Expired</td>
		<% dim strhour, strminute, strAM
		strminute = datepart("n", strNow)
		strhour = datepart("h", strNow)
		strhour = strhour - 2
		
		 %>
    <td  bgcolor="#FFFFDD" bordercolor="#FFFFDD" valign="top"  >
		<font face = arial size = 2>	
		<select size="1" name="Hour" tabindex="3">
	<option <% if strhour = 1 then %> selected <% end if %> value = "01:00:00 AM">1 AM</option>
		<option <% if strhour = 2 then %>selected  <% end if %> value = "02:00:00 AM">2 AM</option>
		<option <% if strhour = 3 then %>selected  <% end if %> value = "03:00:00 AM">3 AM</option>
		<option <% if strhour = 4 then %>selected  <% end if %> value = "04:00:00 AM">4 AM</option>
		<option <% if strhour = 5 then %>selected  <% end if %> value = "05:00:00 AM">5 AM</option>
		<option <% if strhour = 6 then %>selected  <% end if %> value = "06:00:00 AM">6 AM</option>
		<option <% if strhour = 7 then %>selected  <% end if %> value = "07:00:00 AM">7 AM</option>
		<option <% if strhour = 8 then %>selected  <% end if %> value = "08:00:00 AM">8 AM</option>
		<option <% if strhour = 9 then %>selected  <% end if %> value = "09:00:00 AM">9 AM</option>
		<option <% if strhour = 10 then %>selected  <% end if %> value = "10:00:00 AM">10 AM</option>
		<option <% if strhour = 11 then %> selected <% end if %> value = "11:00:00 AM">11 AM</option>
		<option <% if strhour = 12 then %>selected  <% end if %> value = "12:00:00 AM">12 PM</option>
		<option <% if strhour = 13 then %> selected <% end if %> value = "01:00:00 PM">1 PM</option>
		<option <% if strhour = 14 then %>selected  <% end if %> value = "02:00:00 PM">2 PM</option>
		<option <% if strhour = 15 then %>selected  <% end if %> value = "03:00:00 PM">3 PM</option>
		<option <% if strhour = 16 then %>selected  <% end if %> value = "04:00:00 PM">4 PM</option>
		<option <% if strhour = 17 then %>selected  <% end if %> value = "05:00:00 PM">5 PM</option>
		<option <% if strhour = 18 then %>selected  <% end if %> value = "06:00:00 PM">6 PM</option>
		<option <% if strhour = 19 then %>selected  <% end if %> value = "07:00:00 PM">7 PM</option>
		<option <% if strhour = 20 then %>selected  <% end if %> value = "08:00:00 PM">8 PM</option>
		<option <% if strhour = 21 then %>selected  <% end if %> value = "09:00:00 PM">9 PM</option>
		<option <% if strhour = 22 then %>selected  <% end if %> value = "10:00:00 PM">10 PM</option>
		<option <% if strhour = 23 then %>selected  <% end if %> value = "11:00:00 PM">11 PM</option>
		<option <% if strhour = 24 then %>selected  <% end if %> value = "12:00:00 PM">12 AM</option>
		</select>
		&nbsp;
		&nbsp;</td>
		
	
	
    <td  bgcolor="#FFFFDD" bordercolor="#FFFFDD" valign="top"  >
	
		<select size="1" name="Ex_hour" tabindex="4">
	<option value = "" selected>Select Time</option>
	<option <% if strtimeexpired = "01:00:00 AM" then %> selected <% end if %> value = "01:00:00 AM">1 AM</option>
		<option <% if strtimeexpired = "02:00:00 AM" then %>selected  <% end if %> value = "02:00:00 AM">2 AM</option>
		<option <% if strtimeexpired = "03:00:00 AM" then %>selected  <% end if %> value = "03:00:00 AM">3 AM</option>
		<option <% if strtimeexpired = "04:00:00 AM" then %>selected  <% end if %> value = "04:00:00 AM">4 AM</option>
		<option <% if strtimeexpired = "05:00:00 AM" then %>selected  <% end if %> value = "05:00:00 AM">5 AM</option>
		<option <% if strtimeexpired = "06:00:00 AM" then %>selected  <% end if %> value = "06:00:00 AM">6 AM</option>
		<option <% if strtimeexpired = "07:00:00 AM" then %>selected  <% end if %> value = "07:00:00 AM">7 AM</option>
		<option <% if strtimeexpired = "08:00:00 AM" then %>selected  <% end if %> value = "08:00:00 AM">8 AM</option>
		<option <% if strtimeexpired = "09:00:00 AM" then %>selected  <% end if %> value = "09:00:00 AM">9 AM</option>
		<option <% if strtimeexpired = "10:00:00 AM" then %>selected  <% end if %> value = "10:00:00 AM">10 AM</option>
		<option <% if strtimeexpired = "11:00:00 AM" then %> selected <% end if %> value = "11:00:00 AM">11 AM</option>
		<option <% if strtimeexpired = "12:00:00 PM" then %>selected  <% end if %> value = "12:00:00 AM">12 PM</option>
		<option <% if strtimeexpired = "01:00:00 PM" then %> selected <% end if %> value = "01:00:00 PM">1 PM</option>
		<option <% if strtimeexpired = "02:00:00 PM" then %>selected  <% end if %> value = "02:00:00 PM">2 PM</option>
		<option <% if strtimeexpired = "03:00:00 PM" then %>selected  <% end if %> value = "03:00:00 PM">3 PM</option>
		<option <% if strtimeexpired = "04:00:00 PM" then %>selected  <% end if %> value = "04:00:00 PM">4 PM</option>
		<option <% if strtimeexpired = "05:00:00 PM" then %>selected  <% end if %> value = "05:00:00 PM">5 PM</option>
		<option <% if strtimeexpired = "06:00:00 PM" then %>selected  <% end if %> value = "06:00:00 PM">6 PM</option>
		<option <% if strtimeexpired = "07:00:00 PM" then %>selected  <% end if %> value = "07:00:00 PM">7 PM</option>
		<option <% if strtimeexpired = "08:00:00 PM" then %>selected  <% end if %> value = "08:00:00 PM">8 PM</option>
		<option <% if strtimeexpired = "09:00:00 PM" then %>selected  <% end if %> value = "09:00:00 PM">9 PM</option>
		<option <% if strtimeexpired = "10:00:00 PM" then %>selected  <% end if %> value = "10:00:00 PM">10 PM</option>
		<option <% if strtimeexpired = "11:00:00 PM" then %>selected  <% end if %> value = "11:00:00 PM">11 PM</option>
		<option <% if strtimeexpired = "12:00:00 AM" then %>selected  <% end if %> value = "12:00:00 PM">12 AM</option>
		</select>
		&nbsp;
	</td>
	
  </table> 

 

 
  
 <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#808080" width="100%" bgcolor="#D9E1F9" id="table1" height="137">
   
  <tr align = center>
    <td bgcolor="#FFFFDD" bordercolor="#FFFFDD">
	
	<font face="arial" size="2">&nbsp;</font><br>
	<font face="arial" size="2"><br>Purpose of Entry</font>
	&nbsp;</td>

   
  </tr>
  <tr align = center>
    <td  bgcolor="#FFFFDD" bordercolor="#FFFFDD"  >
	<font face="Arial" size="2">
	<input name="Purpose" size="98" value="<%= strPurpose %>" tabindex="5" ></font><p>
	<INPUT TYPE="submit" value="Continue" tabindex="6"></td></tr></table>
 

	</form>
	
	<% else %>
	
	Response.redirect("Sign_in.asp")
	<% end if %><!--#include file="footer.inc"-->