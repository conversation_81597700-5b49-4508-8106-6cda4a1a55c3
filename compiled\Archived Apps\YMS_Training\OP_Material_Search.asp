
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>All-Other Inbound Material Lookup</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

<%  
	Dim strsQL, rstEquip

   	Dim objEquipSearch, rstTotals, strCount, strDed, strNet, strTons, rstTotalReceived, strTR, objNew, rstSpecies
  	Dim  strVendor, strBegDate, strEndDate, strSpecies, strMonth, strDateReceived, strDateAdded, rstMonth
 	Dim  rstVendor, rstNF
  	Dim objGeneral, strPO, gcount, strDays
  	
  	Dim strPS, strAT, strStype, objMOC
   	
   	Dim intDirection
  	Dim intPageNumber
   	Dim strPageNav

   intPageNumber = 1

   set objGeneral = new ASP_CLS_General

  if objGeneral.IsSubmit() Then
    Call GetFormData()
    Select Case intDirection
      Case 10: 'goto mocinfo.asp
  
      Case Else:
        Call LoadSearchResults()
    End Select
  else
    intDirection = 0
  end if
Call GetData()
%>

<script language="javascript">
 
  function GotoMOC(MOCID)
  {
    document.forms["form1"].elements["tbl_ID"].value = MOCID;
    document.forms["form1"].elements["Direction"].value = 10;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }

  function Search()
  {
    
    document.forms["form1"].elements["Direction"].value = 1;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }

  function NextPage()
  {
    document.forms["form1"].elements["Direction"].value = 5;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }
  function PrevPage()
  {
    document.forms["form1"].elements["Direction"].value = 6;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }
</script>


<style type="text/css">
.auto-style1 {
	border: 1px solid #000000;
}
</style>
</head>


<form name="form1" action="OP_Material_Search.asp" method="post">
 <input type="hidden" name="Direction" value="1" >
 <input type="hidden" name="tbl_ID" value="" >
 <input type="hidden" name=PageNumber value="<%=intPageNumber%>" >

  <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=90%  border=1 align = CENTER>  <tr>
    <TD><b><font face="Arial" size="2">Product System</font></b></TD>
    <TD>
   <font face="Arial">
	  <select size="1" name="PS">
	<option value = "" <% if strPS = "" then %>selected <% end if %>>All</option>
	<option <% if strPS = "FC" then %>selected <% end if %>>FC</option>
	<option <% if strPS = "Fiber" then %>selected <% end if %>>Fiber</option>
	<option <% if strPS = "KCP Tissue" then %>selected <% end if %>>KCP Tissue</option>
	<option <% if strPS = "KCP Towel" then %>selected <% end if %>>KCP Towel</option>
	<option <% if strPS = "Shared" then %>selected <% end if %>>Shared</option>
	</select>&nbsp;</TD>    <TD><b><font face="Arial" size="2">Asset Team</font></b></TD>
        <TD>
   <font face="Arial">
	  <select size="1" name="AT">
	<option value = "" <% if strAT = "" then %>selected <% end if %>>All</option>
	<option <% if strAT = "#5 TM" then %>selected <% end if %>>#5 TM</option>
	<option <% if strAT = "#6 TM" then %>selected <% end if %>>#6 TM</option>
	<option <% if strAT = "#7 TM" then %>selected <% end if %>>#7 TM</option>
	<option <% if strAT = "#8 TM" then %>selected <% end if %>>#8 TM</option>
	<option <% if strAT = "FC #11TM" then %>selected <% end if %>>FC #11TM</option>
	<option <% if strAT = "FC Bath" then %>selected <% end if %>>FC Bath</option>
	<option <% if strAT = "FC Co-Pack" then %>selected <% end if %>>FC Co-Pack</option>
	<option <% if strAT = "FC Napkin" then %>selected <% end if %>>FC Napkin</option>
	<option <% if strAT = "IMPORT" then %>selected <% end if %>>IMPORT</option>
	<option <% if strAT = "KCP Fld" then %>selected <% end if %>>KCP Fld</option>
	<option <% if strAT = "KCP JRT" then %>selected <% end if %>>KCP JRT</option>
	<option <% if strAT = "KCP SRB" then %>selected <% end if %>>KCP SRB</option>
	<option <% if strAT = "KCP Tiss Co-Pack" then %>selected <% end if %>>KCP Tiss Co-Pack</option>
	<option <% if strAT = "KCP Twl Co-Pack" then %>selected <% end if %>>KCP Twl Co-Pack</option>
	<option <% if strAT = "KCP Twls" then %>selected <% end if %>>KCP Twls</option>
	
	<option <% if strAT = "Shared" then %>selected <% end if %>>Shared</option>
	</select>&nbsp;</TD>  <Td>

<b><font face="Arial" size="2">Commodity</font></b></td>
 <TD>
   <font face="Arial">    <select name="Stype">
 	<option value="" selected>-- All --</option>
      <%= objGeneral.OptionListAsString(rstNF, "Stype", "Stype", strStype) %>
     </select>
   

	</TD><Td colspan = 2 align = right><input type="button" onClick="javascript:Search()" value="Search" caption="Search"></TD>
   
   &nbsp;</td><td>

&nbsp;</td><td>

&nbsp;</td>


 </TR>


  </TABLE></form><font face = arial>
  <table width = 75% bgcolor="#FFFFDD" align = center class="auto-style1"><tr><td>
	<b>
	<font face="Arial" size="2">On-Yard</font></b></td><td width="641">	
	<font face="Arial" size="2">This load has arrived and is on the yard.  Call Railserve Spotter at 604-3360, Have the OLDEST trailer spotted.</font></td></tr>						
<tr><td><font face="Arial" size="2"><b>In-Transit</b></font></td>
	<td width="641">	
	<font face="Arial" size="2">Material has been produced and the load is on the way to us.  The carrier listed has possession of the load.						
	Click Here for Contact List of Carriers to Call to rush the load or find ETA	</font></td></tr>						
<tr><td><b><font face="Arial" size="2">On-Order</font></b></td><td width="641">	
	<font face="Arial" size="2">This has been ordered but not yet produced by the vendor.  Call your Planner: Click here for Contact list
	</font>						
</td></tr></table>
  
    <% if objGeneral.IsSubmit() Then 

%>

&nbsp;<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=96% class="tablecolor1" border=1 align = center>
    <tr>
		<td colspan="16" bgcolor="white"><font face="Arial" size="2"><b>Search Results</b> - Page <%=intPageNumber%></font></td>
 
    </tr>
    <tr><td colspan="16" bgcolor="white" align="right"><%=strPageNav%>&nbsp;</td></tr>
      <tr class="tableheader">
         <td  align="center"><font face="Arial" size="1">PS</font></td>
      <td  align="center"><font face="Arial" size="1">Comm.</font></td>
 	<td  align="center"><font face="Arial" size="1">Brand</font></td>
	<td  align="center"><font face="Arial" size = 1>SAP Number</font></td>
	<td  align="center"><font face="Arial" size="1">Spec</font></td>
	<td  align="center"><font face="Arial" size="1">Status</font></td>
		<td  align="center"><font face="Arial" size="1">Location</font></td>
	<td  align="center"><font face="Arial" size = 1>Date<br> In or Due</font></td>
	<td  align="center"><font face="Arial" size="1">Carrier</font></td>
	<td  align="center"><font face="Arial" size="1">Trailer</font></td>
	<td  align="center"><font face="Arial" size="1">Quantity</font></td>
	<td  align="center"><font face="Arial" size="1">KC PO#</font></td>
	
	<td  align="center"><font face="Arial" size="1">Comments</font></td>
	<td  align="center"><font face="Arial" size="1">Days<br> in Yard</font></td>
	<td  align="center"><font face="Arial" size="1">Detention<br>$</font></td>


      
  	<% 
      Dim ii
       ii = 0
       while not rstEquip.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr class=tablecolor1 height = 30>
    <% else %>
       <tr class=tablecolor2 height = 30>
    <% end if %>
    <td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("PS")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Stype")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "2"><%=rstEquip.fields("Brand")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "2"><%=rstEquip.fields("Material")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "2"><%=rstEquip.fields("Spec")%>&nbsp;</td>
<td  align="left" ><font face = "arial" size = "2">
<% if len(rstEquip.fields("Date_received"))>1 then %>On-Yard
<% elseif rstEquip.fields("CTrailer") = "Unknown"  or isnull(rstEquip.fields("Ctrailer")) then %>On Order 

<% else %>In Transit <% end if %>
&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Location")%><br><%= rstEquip.fields("Door")%></td>
<td  align="center"><font face = "arial" size = "2">
<% if not isnull(rstEquip.fields("Date_received")) then %> <%= rstEquip.fields("Date_received")%>
<% else %>
<%=rstEquip.fields("Delivdate")%>
<% end if %>&nbsp;</td>
<td  align="center"><font face = "arial" size = "2">
<% if len(rstEquip.fields("trailer"))>1 then %>
<%= rstEquip.fields("Carrier") %>
<% elseif  rstEquip.fields("Vendor_name") = "WEYERHAEUSER COMPANY" then %> BWIF 
<% else %>&nbsp
<% end if %></td>
<td  align="center"><font face = "arial" size = "2">
<% if len(rstEquip.fields("trailer"))>1 then %><%= rstEquip.fields("Trailer")%>
<% elseif rstEquip.fields("CTrailer") <> "Unknown" then %><%= rstEquip.fields("CTrailer")%>
<% end if %>
&nbsp;</td>
<% if not isnull(rstEquip.fields("Date_received")) then %> 
<td  align="right"><font face = "arial" size = "2"><%=rstEquip.fields("tons_received")%>&nbsp;</td>
<% else %>
<td  align="right"><font face = "arial" size = "2"><%=rstEquip.fields("Loaded_qty")%>&nbsp;</td>
<% end if %>
<td  align="center" ><font face = "arial" size = "2"><%=rstEquip.fields("PurchDoc")%>&nbsp;</td>

<td  align="center" width = 6%><font face = "arial" size = "1" >
<% if len(rstEquip.fields("trailer"))>1 then %>
<%= rstEquip.fields("Other_comments")%>
<% else %>
<%= rstEquip.fields("Comments")%>
<% end if %>&nbsp;</td>
<td  align="right"><font face="Arial" size = 2>
<% if len(rstEquip.fields("trailer"))>1 then 
		 if isnull(rstEquip.fields("date_unloaded")) then
		 strdays = DateDiff("d", rstEquip.fields("date_received"), Now())
		 else
		 strdays =  DateDiff("d", rstEquip.fields("date_received"), rstEquip.fields("date_unloaded"))
		 end if
 %>
<%=  strdays %>&nbsp;	
<% else %>&nbsp;
<% end if %></font></td>
<td  align="right"><font face="Arial" size = 2>

<% if len(rstEquip.fields("trailer"))>1 then %>
	<% if strdays < 3 then %>
	0&nbsp;
	<% elseif strdays => 3 and rstEquip.fields("Carrier")  = "BWIF" then %>
	<%= (strdays * 35) - 70 %>
	<% elseif strdays => 3 and rstEquip.Fields("Carrier")  = "SWFT" then %>
	<%= strdays * 25 %>
	<% elseif strdays => 4 and rstEquip.Fields("Carrier")  = "HAEI" then %>
	<%= strdays * 25 %>
	<% elseif strdays =3  and rstEquip.Fields("Carrier")  = "HAEI" then %>
	0&nbsp;
	<% elseif strdays >= 3 then %>
	<%= strdays * 50 %>
	<% end if  %>
<% else %>	
&nbsp;
<% end if %></font></td>
	
  
   </tr>
    <% 
       ii = ii + 1
       rstEquip.MoveNext
     Wend
    %>
   </table>
<table>    <tr><td colspan="7" bgcolor="white" align="right"><%=strPageNav%>&nbsp;</td></tr></table>

<% end if %><% Function GetData()

   set objNew = new ASP_Cls_Fiber
          




End Function
    Function GetFormData()
      intDirection = cint(Request.Form("Direction"))
       intPageNumber = cint(Request.Form("PageNumber"))
     
    
	strPS = request.form("PS")
	strAT = Request.form("AT")
	strSType = request.form("SType")   
	
End Function
 Function GetData()   
  
        set objMOC = new ASP_CLS_FIBER
        set rstNF = objMOC.NFCommodity()
     

    End Function 
    

    Function LoadSearchResults()
      Dim objEquipSearch, objTotals

      if intDirection =1 Then
         intPageNumber = 1
      end if

      if intDirection = 5 Then
         intPageNumber = intPageNumber + 1
      end if
      if intDirection = 6 Then
         intPageNumber = intPageNumber - 1
      end if

   strPS = request.form("PS")
   strAT = request.form("AT")
   strSType = request.form("SType")


      set objEquipSearch = new ASP_Cls_Fiber
    
 
 set rstEquip = objEquipSearch.NFSearch(strPS, intPageNumber, strAT, strStype)


     if ( not rstEquip.Eof) Then
      if ( intPageNumber < rstEquip.fields("TotalPage").value ) Then
        strPageNav = "<a href=javascript:NextPage()><B>Next Page</b></a>"
      end if
      if ( intPageNumber > 1 ) Then
         strPageNav = "<a href=javascript:PrevPage()><b>Previous Page</b></a>&nbsp;&nbsp;&nbsp;&nbsp;" & strPageNav
      end if
     end if

     
    End Function
 %><!--#include file="Fiberfooter.inc"-->