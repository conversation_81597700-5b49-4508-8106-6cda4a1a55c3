																

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Loudon Pricing - Purchase Orders to Confirm</TITLE>

	<!--#include file="classes/asp_cls_DataAccessAIG.asp"-->
<!--#include file="classes/asp_cls_headerAIG.asp"-->
<!--#include file="classes/asp_cls_SessionStringAIG.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_SessionLDN.asp"-->

<style type="text/css">
.auto-style1 {
	font-family: Calibri;
	font-size: small;
}
.auto-style2 {
	text-align: center;
}
.auto-style3 {
	border-style: solid;
	border-width: 1px;
}
</style>
</head>




<% Dim <PERSON>, strsql, MyConn, strUserType

  set objGeneral = new ASP_CLS_General
  
  
if objGeneral.IsSubmit() Then

strPO = request.form("PO")
strVendor = request.form("Vendor")

If len(strPO) > 3  or len(strVendor) > 3 then 
Response.redirect("Loudon_pricing_to_confirm.asp?p=" & strPO & "&v=" & strVendor )
else
Response.write("<font color=red face=calibri size=3><b>You must select either a PO number to start with, or a Vendor</b></font>")
end if
end if

    strUserType = ""
 strsql = "SELECT tblAIGUserType.* FROM tblAIGUserType where BID = '" & Session("EmployeeID") & "' and User_type = 'B'" 
    Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
    If not MyConn.eof  then
 strUserType = "B"
    end if

     If Session("EmployeeID") = "C97338" then
    strUserType = "B"
    end if
    If strUserType = "" then

    Response.write ("<br><br><font face = arial size = 3><b>You do not have authorization to view this page</b></font>")
   else
    BuildScreen()
    end if 

    %>
    <%Sub Buildscreen


%>

<body>
<br>
 <form name="form1" action="Loudon_pricing_to_confirm_Select.asp" method="post" >		
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=40%  border=1 align = center>


<td align =left><b>
<font face="arial" size="4" >Loudon Recovered Paper Purchase Orders to Confirm</font></b><br></td>


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 class = "auto-style3" align = center style="width: 40%">  
	 <tr class="tableheader">
	<td style="height: 40px"><span class="auto-style1">Enter PO# to Start with: &nbsp; </span> 
	<input type="text" name="PO" value="" class="auto-style1" style="width: 128px"><span class="auto-style1">&nbsp;&nbsp;&nbsp;&nbsp;</span></td>

	</tr>
	 <tr class="tableheader">
	<td>&nbsp;</td>

	</tr>
	 <tr class="tableheader">
	<td class="auto-style1" style="height: 27px">&nbsp; 
	<strong>OR</strong></td>

	</tr>
	 <tr class="tableheader">
	<td style="height: 27px">&nbsp;</td>

	</tr>
	 <tr class="tableheader">
	<td style="height: 27px">  <font face="Arial" size="2"><span class="auto-style1">Select Vendor:&nbsp;&nbsp;
	</span></font>
	<select name="Vendor">
	<option value="">--Select--</option>
	<% 
 strsql = "SELECT SAP_Vendor FROM tblPricing "_
&" WHERE (((tblPricing.Site)='LOUDON') AND ((tblPricing.Price) Is Not Null) AND ((tblPricing.Confirmed)='No') AND ((tblPricing.Do_not_invoice) Is Null)) "_
&" GROUP BY tblPricing.SAP_Vendor ORDER BY tblPricing.SAP_Vendor"
     Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    while not MyRec.eof
    %>
<option  value="<%=MyRec("SAP_Vendor")%>" ><%=MyRec("SAP_Vendor")%></option>	
<%  MyRec.movenext
wend 
MyRec.Close
%>
</select></td>

	</tr>
	</table>
<br><br>
 
<p class="auto-style2">  <font face="Arial" size="2">
<input type="submit" name="btnSubmit" value="Submit" class="auto-style11"></font></p>
</form>
 <p></p>
 
<% End Sub %><!--#include file="AIGfooter.inc"-->