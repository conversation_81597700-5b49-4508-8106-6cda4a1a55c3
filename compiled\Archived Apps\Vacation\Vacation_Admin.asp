﻿<html xmlns:m="http://schemas.microsoft.com/office/2004/12/omml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<!--#include file="classes/asp_cls_sessionPLI.asp"--> 
<!--#include file="classes/ASP_CLS_General.asp"--> 

<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 14.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Vacation</title>

<% 


 set objGeneral = new ASP_CLS_General

 if Request("btnSubmitRequest") = "Submit Request(s)" Then
    For Each Item IN Request.Form
        If Left(Item,10) = "cbxRequest" Then
           ary = Split(Request.Form(Item),"|")
		   strsql = "Insert INTO tblScheduledTimeOff (TechnicianID,TimeoffDate,VacAreaID) Values (" & ary(0) & ",'" & ary(1) & "'," & ary(2) & ")"
		   'Response.Write strsql & "<BR>"
		   Set MyInsert = Server.CreateObject("ADODB.Connection")
		   MyInsert.Open Session("ConnectionPolaris")
		   MyInsert.Execute strSQL
		   MyInsert.Close 
		   Set MyInsert = Nothing
		End If
    Next
 end if


   
   %>
   <style type="text/css">
.style5 {
	border-width: 1px;
	background-color: #FFFFFF;
	text-align: right;
	font-family: Calibri;
	font-size: medium;
}
.style6 {
	font-family: Arial;
	font-size: xx-small;
}

.style26{
	font-family: Arial;
	font-size: xx-small;
	background-color: #000000;
	color: #FFFFFF;
}

.style27{
	font-family: Arial;
	font-size: xx-small;
	background-color: #FFFFFF;
	color: #000000
}


.style7 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: xx-small;
	background-color: #EAF1FF;
}
.style9 {
	border-width: 1px;
	background-color: #FFFFFF;
	text-align: center;
}
.style10 {
	font-family: Calibri;
	font-size: x-small;
}
.style11 {
	border-width: 1px;
	text-align: left;
	font-family: Calibri;
	font-size: x-small;
	background-color: #EAF1FF;
}
.style12 {
	font-family: Calibri;
}
.style13 {
	font-size: x-small;
}
.style16 {
	text-align: left;
}
.style17 {
	border: 1px solid #808080;
}
.style28 {
	background-color: #EAF1FF;
}
.style29 {
	font-family: Calibri;
	font-size: x-small;
	background-color: #EAF1FF;
}
.style30 {
	font-family: Arial;
	font-size: small;
	background-color: #EAF1FF;
}
.style31 {
	font-family: Arial, Helvetica, sans-serif;
}
.style33 {
	color: #FFFFFF;
}
.style34 {
	border-color: #000000;
	border-width: 1px;
	color: #FFFFFF;
}
.style35 {
	border-color: #000000;
	border-width: 1px;
	font-family: Arial, Helvetica, sans-serif;
	text-align: center;
}

div.wrapper {
    overflow:hidden;
    overflow-y: scroll;
    height: 600px; // change this to desired height
}
</style>

<form name="form1" action="Vacation.asp" method="post">

<table>
<tr class="style35">
<td align="left">
   Select KC ID:&nbsp;&nbsp;
   </td>
<td align="left">   
   <select name="selTechnician" size="1" tabindex="1" style="height: 22px">
       <option value = "">--- Select---</option>
   
  <% 
 
    strsql = "SELECT Distinct KCID, Technician from tblTechnician ORDER BY TECHNICIAN"
  
     Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionPolaris")
while not MyConn.eof %>
 
<option <% if Request("selTechnician") = MyConn("KCID") then %> selected <% end if %> value="<%=MyConn("KCID") %>"><%= MyConn("Technician") %>&nbsp;(<%= MyConn("KCID") %>)</option>
 
<% MyConn.movenext
wend
MyConn.close %>
     </select>
	 </td>
	 </tr>
<tr class="style35">
<td align="left">
	 Start Date:&nbsp:&nbsp;</td>
<td align="left">
	 <input type="text" name="txtStartDate" value="<%=Request("txtStartDate")%>">
</td>
</tr>
<tr class="style35">
<td align="left">
	 End Date:&nbsp:&nbsp;
	 </td>
<td align="left">
<input type="text" name="txtEndDate" value="<%=Request("txtEndDate")%>">
</td>
</tr>
</table>
<BR>
<input type="submit" name="btnSubmit" value="Submit">
</span>
<BR><BR>

<%If Request("btnSubmit") = "Submit" OR Request("btnSubmitRequest") = "Submit Request(s)" Then
     Dim aryActualTotal(99)
     strsql = "SELECT * from tblTechnician WHERE KCID = '" & Request("selTechnician") & "'"
     Set MyConn = Server.CreateObject("ADODB.Recordset")
     MyConn.Open strSQL, Session("ConnectionPolaris")
	 If NOT MyConn.EOF Then
	    strID = MyConn("ID")
	    strKCID = MyConn("KCID")
		strTechnician = MyConn("Technician")
        strArea = MyConn("Area")
        strWSR = MyConn("WSR")
		strTeamLeader = MyConn("TeamLeader")
		strsql = "select VacArea, IsNull(MaxOffPerDay,0) as 'MaxOffPerDay', tblAreaVacArea.VacAreaID  from tblArea " _
					& " INNER JOIN tblAreaVacArea ON tblArea.ID = tblAreaVacArea.AreaID " _
					& " INNER JOIN tblVacArea ON tblVacArea.ID = tblAreaVacArea.VacAreaID " _
					& " where Area = '" & strArea & "'"
		Set MyConn2 = Server.CreateObject("ADODB.Recordset")
        MyConn2.Open strSQL, Session("ConnectionPolaris")
        If not MyConn2.eof Then
           strVacArea = MyConn2("VacArea")
		   intMaxOffPerDay = MyConn2("MaxOffPerDay")
		   intVacAreaID = MyConn2("VacAreaID")
		End If
		MyConn2.Close
		Set MyConn2 = Nothing
     End If
	 MyConn.Close
	 Set MyConn = Nothing%>
<BR>
<table>
<tr class="style35"><td align="left">Vacation Area:&nbsp;&nbsp;</td><td align="left"><%=strVacArea%></td></tr>
<tr class="style35"><td align="left">Maximum Allowed off/day:&nbsp;&nbsp;</td><td align="left"><%=intMaxOffPerDay%></td></tr>
</table>
<br>
<table border="1">
<tr class="style35"><td>&nbsp;</td>
<%
  dtStartDate = Request("txtStartDate")
  For i = 0 To 13
     dtCurrentDate = dateAdd("d",i,dtStartDate)
	 Response.Write "<td align=""center"">" & WeekDayName(WeekDay(dtCurrentDate)) & "</td>"

  Next
%>
</tr>
<tr class="style35"><td>&nbsp;</td>
<%
  dtStartDate = Request("txtStartDate")
  For i = 0 To 13
     dtCurrentDate = dateAdd("d",i,dtStartDate)
	 Response.Write "<td>" & dtCurrentDate & "</td>"

  Next
%>
</tr>

<tr class="style35"><td>Actual</td>
<%
  Dim aryTechnicianScheduled(99)
  FOR i = 0 To 98
     aryTechnicianScheduled(i) = 0
  Next
  dtStartDate = Request("txtStartDate")
  For i = 0 To 13
     dtCurrentDate = dateAdd("d",i,dtStartDate)

     strsql = "SELECT count(*) as 'ActualTotal', IsNULL(SUM(CASE TechnicianID WHEN " & strID & " Then 1 Else 0 END),0) as 'TechnicianScheduled'   FroM tblScheduledTimeOff where DateDIFF(d, TimeOffDate, '" & dtCurrentDate & "') = 0 AND VacAreaID = " & intVacAreaID
	 'Response.write strsql
	 Set MyConn2 = Server.CreateObject("ADODB.Recordset")
     MyConn2.Open strSQL, Session("ConnectionPolaris")
     If not MyConn2.eof Then
        aryActualTotal(i) = MyConn2("ActualTotal")
		aryTechnicianScheduled(i) = MyConn2("TechnicianScheduled")
	End If
	MyConn2.Close
	Set MyConn2 = Nothing

	 Response.Write "<td align=""center"">" & aryActualTotal(i) & "</td>"

  Next
%>
</tr>

<tr class="style35"><td>Available</td>
<%
  dtStartDate = Request("txtStartDate")
  For i = 0 To 13
      Response.Write "<td align=""center"">" & intMaxOffPerDay - aryActualTotal(i) & "</td>"
  Next
%>
</tr>
<tr class="style35"><td>Check To Request</td>
<%
  dtStartDate = Request("txtStartDate")
  For i = 0 To 13
     dtCurrentDate = dateAdd("d",i,dtStartDate)
  
	 Response.Write "<td align=""center"">"
	 If intMaxOffPerDay > aryActualTotal(i) And aryTechnicianScheduled(i) = 0 Then
	    Response.Write "<input type=""checkbox"" name=""cbxRequest" & i & """ value=""" & strID & "|" & dtCurrentDate & "|" & intVacAreaID & """>"
	 Else
	    Response.Write "&nbsp;"
	 End If	 
	 Response.Write "</td>"

  Next
%>
</tr>
</table>
<input type="submit" name="btnSubmitRequest" value="Submit Request(s)">

<%End If%>
 
<TABLE cellSpacing=1 cellPadding=2 class = "style17" style="width: 95%" align="center">  



</TABLE>
</form>