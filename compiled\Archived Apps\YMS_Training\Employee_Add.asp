
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Add New Employee</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
</head>
<style type="text/css">
.style1 {
	font-family: Arial;
}
.style2 {
	font-family: Arial;
	font-size: medium;
}
.style3 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
</style>

<% Dim strFirstName, strLastNmae, strUID

 set objGeneral = new ASP_CLS_General


if objGeneral.IsSubmit() Then


	Call SaveData() 

End if %>

<body><form name="form1" action="Employee_add.asp" method="post">
 
<table border="0" cellpadding="0" cellspacing="0" width = 60% style="border-collapse: collapse" bordercolor="000000" height="25" align = center >
  <tr>
    <td  bgcolor="#FFFFFF" bordercolor="#CCCCFF" height="25" >
    <p align="center"><font face="Arial" size="4">Add New Employee</font></td>
    <td align = center height="25"><font face="Arial"><b><a href="Employees.asp">RETURN</a></b></font></td>

    <td align = center bgcolor="#FFFFFF" height="25">
  <Input name="Submit" type="submit" Value="Submit" style="float: right" ></td>
  </tr>
</table><br><br>
<div align="center">
<table cellpadding="0" cellspacing="0" width="50%" bordercolor="#808080"  height="10" class="style3">
    <tr>
<td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="22" align="center" ><b>
	<font face="Arial">First Name </font></b></td>
   
<td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="22" align="center" class="style1" >
	<strong>Last Name</strong></td>
   
  <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="22" align="center" class="style1" >
	<strong>UID</strong></td>
 
  </tr>
  
    <tr>
    <td  bordercolor="#CCCCFF" height="47"  align="center" >   <font face="Arial">	
		<input type="text" name="First_name" size="35" value="" style="width: 177px" tabindex="1">
</font></td>
    	

    <td  bordercolor="#CCCCFF" height="47"  align="center" >  	
     <font face="Arial">	
		<input type="text" name="Last_name" size="35" value="" style="width: 178px" tabindex="2"></font></td>
    	
   <td  bordercolor="#CCCCFF" height="47"  align="center" >  	
     <font face="Arial">	
		<input type="text" name="UID" size="35" value="" style="width: 92px" tabindex="2"></font></td> 

  </tr>
 
  </table>
</div>



</form>
   
  

</body>
 <%

  
  Function SaveData()



strFirstName = Replace(Request.form("First_name"), "'", "''")   
strLastName = Replace(Request.form("Last_name"), "'", "''") 
strUID = Request.form("UID")  

  
  strsql = "Insert into tblEmployees (First_name, Last_Name, UID) "_
  &" Values('" & strFirstName & "',  '" & strLastName & "', '" & strUID & "')"

   
  	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
          Response.redirect("Employees.asp")
  End Function
  
   %>