<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 14.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Edit KDF Inbound Load</title>
</head>
<%
    Dim strSQL, MyRec, strID
      
    Dim strTrailer, strLN, strPO, strML, strsql3, Myconn, strmaterial, strMD, strStatus, strLocation


       strId  = Trim(Request.QueryString("id")) 


 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
 
		Call SaveData() 

	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to delete a Receipt.</font></br>")
	MyRec.close
	end if

  
ELSE


strsql = "SELECT tblSapOpenPO.* from tblSapOpenPO where OID = " & strid

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


    strTrailer = MyRec.fields("Trailer")
    strML = MyRec.fields("Multi_load")
    strPO = MyRec.fields("Purchdoc")
    strLN = MyRec.fields("Item")
    strStatus = MyRec.fields("Status")
    strLocation = MyRec.fields("Report_location")
    strSAP=MyRec.fields("Material")
    strBrand = MyRec("Brand")
 
    

MyRec.close

	end if

%>



<body><br>
<table width = 100%><tr><td width = 33%></td><td align = center width = 34%><b><font face="Arial" size="4">
Edit Inbound KDF Trailer</font> </b></td><td align = right width = 33%><a href="Incoming_KDF.asp"><b><font face = arial size = 2>RETURN</a></font></b></td></tr></table>



<form name="form1" action="KDF_Inbound_Edit.asp?id=<%=strid%>" method="post">
<div align="center">
<table border="1" cellspacing="0" bordercolor="#C0C0C0" width="60%" bgcolor="#FFFFFF" style="border-collapse: collapse" cellpadding="0">
<tr>
    <td bgcolor="#FFFFE6" style="height: 7px">
   <b>
   <font face="Arial" size="2">Trailer:</font></b></td>

    <td  bgcolor="#FFFFE6" style="height: 7px"><font face="Arial" size="2"><b>Multi-Item Load:&nbsp;&nbsp; </b>
	(Enter X if Multi-Item Load)</font></td>

    <td  bgcolor="#FFFFE6" style="height: 7px"><font face="Arial" size="2"><strong>SAP #</strong></font></td>

    <td  bgcolor="#FFFFE6" style="height: 7px"><font face="Arial" size="2"><strong>
	Brand</strong></font></td>
  </tr>
  <tr>
    <td  align = right bgcolor="#FFFFFF" >
   <font face="Arial">

      <input name="Trailer" size="15" value = "<%= strTrailer%>" style="float: left" tabindex="1"></font><b><font face="Arial" size="2">&nbsp;</font></b></td>
<td  align = left>

      <font face="Arial">

      <input type="text" name="ML" size="4" value = "<%= strML%>" tabindex="2"></font></td>
<td  align = left>

   <font face="Arial">

      <input name="SAP" size="15" value = '<%= strSAP %>' style="float: left" tabindex="3"></font></td>
<td  align = left>

   <font face="Arial">

      <input name="Brand" size="15" value = "<%= strBrand %>" style="float: left" tabindex="4"></font></td></tr>

  </tr>
<tr>
    <td bgcolor="#FFFFE6">
   <b><font face="Arial" size="2">PO#</font></b></td>

    <td  bgcolor="#FFFFE6"><b><font face="Arial" size="2">Line Number</font></b></td>

    <td  bgcolor="#FFFFE6"><b><font face="Arial" size="2">Status</font></b></td>

    <td  bgcolor="#FFFFE6">&nbsp;</td>


  </tr>

  <tr>
    <td  align = left bgcolor="#FFFFFF" >
<font face="Arial">
<input name="PO" size="15" value = "<%= strPO%>" style="float: left" tabindex="5"></font></font></td>
<td  align = left>  <font face="Arial">
<input name="LN" size="15" value = "<%= strLN%>" style="float: left" tabindex="6"></font></td>
<td  align = left>  <select size="1" name="Status" tabindex="7">
<option value = "">Inbound</option>
<option <% if strStatus = "R" then %> selected <% end if %> value="R">Received</option>
<option <% if strStatus = "REMOVED" then %> selected <% end if %> value="REMOVED">Removed</option>
</select></td>
<td  align = left>  <select size="1" name="Location" tabindex="8">
<option <% if strlocation = "A" then %> selected <% end if %> value="A">At Door</option>
<option <% if strlocation = "B" then %> selected <% end if %> value="B">On Yard</option>
<option <% if strlocation = "C" then %> selected <% end if %> value="C">In Transit</option>
<option <% if strlocation = "D" then %> selected <% end if %> value="D">On Order</option>
</select></td>
</tr>

 
 

  <tr>
    <td bgcolor="#FFFFE6" colspan="4">
	<p align="center">
    <Input name="Update" type="submit" Value="Submit" tabindex="9" ></td>

  </tr>
</table>

<p>&nbsp;</div>

</form>
</body>
<%
 


 Function SaveData()
 strid = request.querystring("id")
strTrailer = Request.form("Trailer")
strPO = request.form("PO")
strLN = request.form("LN")
strSAP = request.form("SAP")
strBrand = request.form("Brand")
If len(request.form("ML")) > 0 then
strML = Request.form("ML")
else
strML = ""
end if 

If len(request.form("SAP")) > 0 Then

  strSql = "Update tblSAPOpenPO set  Material= " & strSAP & " where OID =  " & strid & ""
  else
    strSql = "Update tblSAPOpenPO set  Material= Null where OID =  " & strid & ""
  end if
Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close


If len(request.form("Brand")) > 0 Then

  strSql = "Update tblSAPOpenPO set  Brand = '" & strBrand & "' where OID =  " & strid & ""
  else
    strSql = "Update tblSAPOpenPO set  Brand = Null where OID =  " & strid & ""
  end if
Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close





If strML = "" then

         strSql = "Update tblSAPOpenPO set Purchdoc = '" & strPO & "', PO_Nbr = " & strPO & ", Line_nbr = " & strLN & ", "_
         &" Item = '" & strLN & "', Trailer = '" & strTrailer & "', Multi_load =  NULL, , "_
         &" Report_location = '" & request.form("Location") & "', Status = '" & Request.form("Status") & "' where OID =  " & strid & ""

else

  strSql = "Update tblSAPOpenPO set Purchdoc = '" & strPO & "', PO_Nbr = " & strPO & ", Line_nbr = " & strLN & ", "_
         &" Item = '" & strLN & "', Trailer = '" & strTrailer & "', Multi_load =  '" & strML & "',  "_
         &" Report_location = '" & request.form("Location") & "', Status = '" & Request.form("Status") & "' where OID =  " & strid & ""



end if

If request.form("Status") = "" then
If strML = "" then

     strSql = "Update tblSAPOpenPO set Purchdoc = '" & strPO & "', PO_Nbr = " & strPO & ", Line_nbr = " & strLN & ", "_
         &" Item = '" & strLN & "', Trailer = '" & strTrailer & "', Multi_load =  NULL,  "_
         &" Report_location = '" & request.form("Location") & "', Status = Null where OID =  " & strid & ""
else

 strSql = "Update tblSAPOpenPO set Purchdoc = '" & strPO & "', PO_Nbr = " & strPO & ", Line_nbr = " & strLN & ", "_
         &" Item = '" & strLN & "', Trailer = '" & strTrailer & "', Multi_load =  '" & strML & "',  "_
         &" Report_location = '" & request.form("Location") & "', Status = Null where OID =  " & strid & ""

end if 
         
	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
	end if			

	
Response.redirect ("Incoming_KDF.asp")



End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->