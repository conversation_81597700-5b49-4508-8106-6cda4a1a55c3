<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">


<TITLE>Select Release Number</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

 <%    Dim objDAC, strSQL, MyRec, objMOC, rstFiber, strPO, strR, strID
	Dim gSpecies, gSap_Nbr, gVendor, gPO, gRelease, gOID, gGenerator, gCity, gState
       Dim objGeneral, strDate, MyConn
strDate = formatdatetime(Now(),2)
	
	
  set objGeneral = new ASP_CLS_General
  Call getData()   
if objGeneral.IsSubmit() Then
strR = Request.form("Release")




	Response.redirect("EnterNFReceipt.asp?id=" & strR)
	


End if
%>
<SCRIPT LANGUAGE="JavaScript">
function formHandler(Form1){
document.forms["Form1"].submit();
}
</SCRIPT>

<body>
<form name="form1" action="SelectNFRelease.asp" method="post" >

<p>&nbsp;</p>
<table border="1" cellpadding="0" class = "tablecolor1" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="75%" id="AutoNumber2" align = center>

       <TD align = center>  <p>&nbsp;</p> <font face="Arial" size="3"><b>Select Non-Fiber Order Number</b></font>
	&nbsp;&nbsp;&nbsp;<br><br>
     <font face="Arial">
     	<font size="2"> &nbsp;</font><select name="Release">
 	<option value="" selected>Order Nbr - Line # - Trailer - Vendor</option>
      <%= objGeneral.OptionListAsString(rstFiber, "OID", "PO", strPO) %>
     </select><p>&nbsp;</p> 
<p align="center"><input type="submit" value="Continue" id=submit1 name=submit1><br>&nbsp;</TD>

       <TD align = center>  


<p align="center">
     <font face="Arial">
     <b><a href="EnterNF_Generic_Receipt.asp">If Load's Order Number is not an option, 
Click Here</a></b></p>



		<p>&nbsp;</TD></tr>

</table>
</form>


<p align="center">
     &nbsp;</p>



<%

 Function GetData()
        set objMOC = new ASP_CLS_FIBER
        set rstFiber = objMOC.FiberNFRelease()
     

    End Function  %><!--#include file="Fiberfooter.inc"-->