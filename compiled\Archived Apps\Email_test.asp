<html>
 


<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Send emails</title>
<style type="text/css">
.style2 {
	font-size: x-small;
}
.style4 {
	border: 1px solid #000000;
}
.style5 {
	border: 1px solid #808080;
}
.style6 {
	border: 1px solid #808080;
	font-size: x-small;
}
.style7 {
	border: 1px solid #808080;
	font-size: x-small;
	font-weight: bold;
	background-color: #E6E6FF;
}
.style8 {
	border: 1px solid #808080;
	font-size: x-small;
	font-weight: bold;
	background-color: #E6E6FF;
	text-align: center;
}
.style9 {
	font-family: Arial, Helvetica, sans-serif;
}
.style10 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.style11 {
	border: 1px solid #808080;
	text-align: center;
}
</style>

<%	strtoday = formatdatetime(Now(),2)
str30day = dateadd("d", -30, strtoday)
strcount = 0

	Set objMail = Server.CreateObject("CDO.Message")		
			objMail.From = "<EMAIL>"	 
 
			objMail.to = "<EMAIL>"	
		
			objMail.Subject = "Test for DEV NEW FARM"    
			strBody =  "<table width=100% cellpadding=0 cellspacing=0 border =1 class=style4 >"
			strbody = strBody & "<tr>	<td align = center font face = arial class=style7 border=1>Mill</td><td align = center font face = arial class=style7 border=1>KC Dryer #</td><td align = center font face = arial class=style7 border=1>Date</td>"
			strbody = strbody & "<td align = center font face = arial class=style7 border=1>Date Due</td><td align = center font face = arial class=style7 border=1>Source</td>"
			strbody = strbody & "<td align = center font face = arial class=style7 border=1>Description</td></tr>"
 
			objMail.HTMLBody = "The following event(s) are past due for completion.<br><br> " &  strbody
 
			objMail.Send
 
			Set objMail = nothing
		
 
  
   %>