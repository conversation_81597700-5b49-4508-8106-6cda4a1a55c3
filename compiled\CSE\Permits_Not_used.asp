<html>
<title>Permits Not Used</title>

<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_Procedure.asp"-->

<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->

<head>
<% dim strdate, strWA, MyConn
strnow = dateadd("h", -5, now())
strDate = formatdatetime(strnow,2)
strWA = Request.querystring("id")
if left(strWA, 4) = "SF B" then
strWA = "SF Buildings & Grounds"
end if 
 %>

<style>
<!--
 p.<PERSON>oNormal
	{mso-style-parent:"";
	margin-bottom:.0001pt;
	font-size:12.0pt;
	font-family:"Times New Roman";
	margin-left:0in; margin-right:0in; margin-top:0in}
-->
</style>
</head>

<body leftmargin="0" topmargin="5" marginwidth="0" marginheight="0">

<table width="100%"  border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td  width="160" valign="top" bgcolor="#F4F4F4">
	&nbsp;</td>


    <td width="801" valign="top">
	<img border="0" src="images/bg_blueProducts.gif" width="801" height="32"><br>
      <table width="801" border="0" cellspacing="0" cellpadding="1">
        <tr>
          <td width="73%" valign="top">
		  	<table width="98%"  border="0" cellspacing="0" cellpadding="1" align="center">
            <tr>
              <td bgcolor="#FFFFFF" align = center><font size="4"><b><font color = black face="Arial, Helvetica, sans-serif">
		<%= strWA%></b>	</font><br>
          
                  <br>
            <table border = 1 cellspacing = 0 cellpadding = 0 width = 95% bordercolor="#F4F4F4"><tr>
             <td align="center" ><font face="Arial" size="1">Space ID</font></td>
            <td align="center" ><font face="Arial" size="1">Space Name</font></td>
			<td align="center" > <font face="Arial" size="1">Date of <br>Issue</font></td>
			<td align="center" ><font face="Arial" size="1">Date<br> Expires</font></td>
			<td align="center" ><font face="Arial" size="1">Permit <br>Not Used</font></td>
			<td align="center">&nbsp;</td></tr>
            
      <% strsql = "SELECT tblSOP.SOP_NO, tblSOP.SDescription, tblPermit.Date_issued, tblPermit.Date_expired, tblSOP.WorkArea, "_
      &" tblPermit.Permit_status, tblPermit.Status_date,  tblPermit.Safety_Date, tblPermit.PID "_
		&" FROM tblPermit INNER JOIN tblSOP ON tblPermit.Space_ID = tblSOP.SOP_NO "_
		&" WHERE tblSOP.WorkArea='" & strWA& "'   and (Permit_status =  'Permit Not Used') order by Date_Expired desc "
       Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly 
			While not MyRec.eof %>
           <tr>
            
            <td align="center" ><font face="Arial" size="1"><%= MyRec.fields("SOP_NO") %></font></td>
			<td align="left" > 	<font face="Arial" size="1"><%= MyRec.fields("Sdescription") %></td>
			<td align="center"><font face="Arial" size="1">&nbsp;<%= MyRec.fields("Date_issued") %>&nbsp;</td>
			<td align="center" ><font face="Arial" size="1">&nbsp;<%= MyRec.fields("Date_expired") %>&nbsp;</td>
			<td align="center" ><font face="Arial" size="1">&nbsp;<%= MyRec.fields("Status_date") %></font></td>			
			<td align="center" ><font face="Arial" size="1"><a href="Permit_Status_edit.asp?id=<%= MyRec.fields("PID") %>&wa=<%= strWA%> ">Edit</a></font></td>
			</tr>
 <%   
       MyRec.MoveNext
     Wend
     MyRec.close %>
            
            </table>
<br>
<br>



</b></i>
			
                  </font></font></td>
            </tr>
          </table></td>
          <td width="26%" valign="top" bgcolor="#D6D6F5" align = center><font size="2" face="Arial, Helvetica, sans-serif"></td>


        </tr>
     
      </table></td>
      <td  bgcolor="#C5C5C5">&nbsp;</td>


</tr></table>


</body>

</html>

<!--#include file="footer.inc"-->
                                       