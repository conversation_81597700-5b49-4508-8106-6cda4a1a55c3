<html>
<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_Procedure.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 6.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Update Permit Status</title>
</head>
<% dim strsql, MyRec, strid, strwa, strSpaceID, strTitle, strStatus, strDate, strok

strid = Request.querystring("id")

strok = "N"
		

	strSQL = "SELECT tblAuthorizers.P_Type from tblAuthorizers where P_BID = '" & session("EmployeeID") & "' and P_Type = 'M'"
		       
		       
			Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly 
			If not MyRec.EOF then
			strOK = MyRec.fields("P_Type")
			end if 
			MyRec.close
if strok = "N" then
Response.write("<b><font face = arial size = 3> <br><br>You do not have authorization to close out this permit</font></b><br><br>")
else




      strsql = "SELECT tblSOP.SOP_NO, tblSOP.SDescription, tblPermit.Date_issued, tblPermit.Date_expired, tblSOP.WorkArea, "_
      &" tblPermit.Permit_status, tblPermit.Status_date,  tblPermit.Safety_Date, tblPermit.PID "_
		&" FROM tblPermit INNER JOIN tblSOP ON tblPermit.Space_ID = tblSOP.SOP_NO "_
		&" WHERE tblPermit.PID = " & strid
       Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly 
					
			strTitle = MyRec.fields("SDescription")
			strSpaceID = MyRec.fields("SOP_NO")
			strstatus = MyRec.fields("Permit_status")
			MyRec.close


set objGeneral = new ASP_CLS_General
  		 if objGeneral.IsSubmit = True Then
  		strid = request.querystring("id") 
  		strnow = dateadd("h", -5, now())
strDate = formatdatetime(strnow,2)
  		strSpaceid = request.querystring("spid")
 
  		
  	
 	
  	
 strsql =  "Update tblPermit set  safety_date = '" & strdate & "', Safety_BID = '" & Session("EmployeeID") & "' where PID = " & strid
 
 Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 


Response.redirect("Summary_Details_edit.asp?id=" & strspaceid)
end if 

 %><body><table width = 100%><tr><td align="left"><b><font face="arial">Safety 
	Department Close-out:</font></b>
 </td><td align = right><font face = arial size = 3><a href="javascript:history.go(-1);"><b>Return</b></a></td></tr></table>
 <br><br>
 </p>
 <form name="form1" action="SD_Closeout.asp?id=<%= strID%>&spid=<%= strspaceid%>"  method="post" ID="Form1"  >
 
 <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="75%"  align = center bgcolor="#D9E1F9">
   
  <tr>
    <td   bgcolor="#FFFFDD" >
	<p align="center"><font face="Arial" size="2">Space ID</font></td>
    <td bgcolor="#FFFFDD" align="center" width="413">
	<font face="arial" size="2">Space Name</font></td>

   
    <td bgcolor="#FFFFDD" align="center">
	<font face="Arial" size="2">Permit Status</font></td>

   
  </tr>
  <tr>
    <td align = center bgcolor="#FFFFFF" ><font face="Arial" size="2"> <%= strSpaceID%>
	&nbsp;</td>
    <td  bgcolor="#FFFFFF" width="413"  >
	<p align="center">&nbsp;<font face="Arial" size="2"><%= strTitle %></td>
	
    <td  bgcolor="#FFFFFF"  >
	<p align="center">&nbsp;<font face="Arial" size="2"><%= strstatus %></td>
	
  </table> 
	<p align = center>&nbsp;<INPUT TYPE="submit" value="Close out Permit" name="Submit"></p>
	</form>
	<% end if %><!--#include file="footer.inc"-->