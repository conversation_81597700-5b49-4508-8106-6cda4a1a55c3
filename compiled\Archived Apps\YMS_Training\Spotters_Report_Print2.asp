<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">


<TITLE>Spotters Report</TITLE>
<!--#include file="classes/asp_cls_SessionStringLDN.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<style type="text/css">
.style1 {
	border-width: 1px;
	background-color: #EAF1FF;
}
.style2 {
	text-align: center;
}
.auto-style5 {
	border: 1px solid #000000;
}
.auto-style6 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Calibri;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #F4EADF;
}
.auto-style7 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: <PERSON>ibri;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.auto-style8 {
	font-family: Arial, Helvetica, sans-serif;
}

</style>
</head>

<body>
 
	<div class="style2">
	    <span class="auto-style8">Spotter's Report</span>&nbsp;&nbsp;&nbsp; <%= Dateadd("h", 1, formatdatetime(Now(),0)) %></div>

 
<input type="hidden" name="hidReleaseNum" value="">

 <table width="100%">
 <tr>
 <td valign="top">
 <table align="center" class="auto-style5" style="width: 75%" cellspacing="0">
<tr>
        <td class="auto-style6" style="text-align:center">Carrier</td>
	 <td class="auto-style6" style="text-align:center">Yard Status</td>
	 <td class="auto-style6" style="text-align:center">Trailer # In</td>
 
	 <td class="auto-style6" style="text-align:center">Move From</td>
	 <td class="auto-style6" style="text-align:center">Move To</td>

</tr>

 
 <%

 strsql = "SELECT ReleaseNum, MoveStatus, CurrentLocation,CARRIER,MoveTo,[TRAILER# IN],[YARD STATUS], DockSort FROM Logistics.dbo.master WHERE MoveStatus IN (-1,1) Order by ReleaseNum"

 Set MyRec = Server.CreateObject("ADODB.Recordset")
 MyRec.Open strSQL, Session("ConnectionStringLDN")
 counter = 0
 while not MyRec.eof
    counter = counter + 1
If counter Mod 2 = 0 Then%>
     <tr style="background-color:#d8ecf3">
 <%Else%>
     <tr style="background-color:white">
<%End If%>
      <td class="auto-style7" style="text-align:center"><%= MyRec("Carrier") %>&nbsp;</td>

<td class="auto-style7" style="text-align:center">&nbsp;<%= MyRec("YARD STATUS")%>&nbsp;</td>
  <td class="auto-style7" style="text-align:center"><%= MyRec("TRAILER# IN")%>&nbsp;</td>
    <td class="auto-style7" style="text-align:center"><%= MyRec("CurrentLocation")%>&nbsp;</td>

<td class="auto-style7" style="text-align:center"><%= MyRec("MoveTo")%>&nbsp;</td>

</tr>
<% MyRec.movenext
wend
MyRec.close %>

 </table>
 
<script language="javascript">
window.print();
</script>