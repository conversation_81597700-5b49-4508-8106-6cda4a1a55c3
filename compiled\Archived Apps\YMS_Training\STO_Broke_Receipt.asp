<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Enter Broke Trailer Receipt</title>
<style type="text/css">
.style1 {
	font-size: x-small;
}
.style25 {
	border-style: solid;
	font-weight: bold;
	border-width: 1px;
	background-color: #FDE3E8;
}
.style26 {
	border-style: solid;
	border-width: 1px;
	background-color: #FDE3E8;
}
.style28 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
.style29 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.style30 {
	border-style: solid;
	border-width: 1px;
}
.style31 {
	font-weight: bold;
	border-style: solid;
	border-width: 1px;
}
.auto-style1 {
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
}
.auto-style2 {
	font-weight: bold;
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
}
.auto-style3 {
	font-weight: bold;
	border-style: solid;
	border-width: 1px;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	text-align: right;
	background-color: #EAF1FF;
}
.auto-style5 {
	border-style: solid;
	border-width: 1px;

	background-color: #EAF1FF;
}
.auto-style6 {
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
	text-align: right;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.auto-style7 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	color: #000000;
}
.auto-style8 {
	color: #000000;
}
.auto-style9 {
	color: #000000;
	font-size: x-small;
}
.auto-style10 {
	text-align: center;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID,  MyConn, objMOC, rstFiber, rstSpecies, strKCWeighed, strPounds  
    Dim strTrailer, strCarrier, strLocation, MyRec5, strsql5, stralert
    Dim rstTrailer , strTrailerWeight , strTractor, strTrailerTID, strSAP, strerror, strType, strPO, strBOL
 
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strOther, strsql3, strSpecies, strNet,  strR, objGeneral, strLoadID

		strother = ""
		If len(request.querystring("id")) > 4 then
	strLoadID = request.querystring("id")
	strLoad = strLoadID
	
strsql = "Select tblSTOInbound.* from tblSTOInbound where load_nbr = " & strLoadID & ""
   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")


	If not Myrec.eof then
	strSAP = MyRec.fields("SAP_Nbr")
	strTrailer = MyRec.fields("Ship_From_State")
	strCarrier = MyRec.fields("Ship_From_City")
	strPO = MyREc("PO")
	strBOL = MyREc("BOL")
	end if
	MyRec.close
else
strSAP = request.querystring("s")
strTrailer = ""
strCarrier = ""
strPO = ""
strBOL = ""
end if	
	
 
  set objGeneral = new ASP_CLS_General
if objGeneral.IsSubmit() Then
strerror = 0

   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
	
	

	strSpecies ="BROKE"
	
 	strTrailer = Request.form("Trailer")
	strOther = Replace(Request.form("Other_Comments"), "'", "''") 
	strDateReceived = Request.form("Date_received")
	strGrossweight = Request.form("Gross_Weight")
	strPounds = Request.form("Tons_Received")
	strCarrier = Request.form("Carrier")
	strLocation = "YARD"
	strTrailerTID = request.form("Trailer_option")
	strSAP = request.form("SAP")
	
	 if  len(strTrailer)< 1 then
	 strerror = 1 %>
	 <font face="arial" size="3" color="red"><b>You must enter a Trailer number.</b></font><br>
<% end if %>
<% if strCarrier = "" then
strerror = 1 %>
 <font face="arial" size="3" color="red"><b>You must enter a Carrier</b></font><br>
<% end if %>
<% if strSAP= "" then
strerror = 1  %>
 <font face="arial" size="3" color="red"><b>You must select a SAP #</b></font><br>
<% end if

	if strerror = 0 then
	Call SaveData() 
	end if
	
	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to save this information.</font></br>")
	MyRec.close
	end if ' if you don't have authorization
    
   
end if ' if they did not submit
%>

<% if Request.querystring("n") = "T" then 
strTrailer = Session("Trailer")
strCarrier = Session("Carrier")
strTrailerTID = Session("TrailerTID")
strGrossWeight = Session("GrossWeight")

strPounds = Session("Pounds")
strOther = Session("Other")
strSAP = Session("SAP")
 %>
<p align = center><font face = arial size = 3 color = red><b>



<% if isnull(Session("Trailer")) or len(Session("Trailer"))< 1 then %>
You must enter a Trailer number.<br>
<% end if %>
<% if strCarrier = "" then %>
You must enter a Carrier</span><br>
<% end if %>
<% if strSAP= "" then %>
You must select a SAP #</span><br>
<% end if %>

</p></b></font>
<% else %>
&nbsp;
<% end if %>


<body>
<table width = 100%><tr><td width = 33% class="auto-style10" style="width: 67%">
	<font face="Arial" size="4">
	<b>Enter STO Broke Trailer Receipt </b></font> </b></td></tr></table>




<form name="form1" action="STO_Broke_Receipt.asp?id=<%=strid%>" method="post">
<input type="hidden" name="PO" value="<%= strPO %>">


<div align="center">
<table cellspacing="0" bordercolor="#C0C0C0" bgcolor="#FFFFEA" style="width: 85%;" cellpadding="0" class="style28">

<tr>
    <td align="right" class="auto-style2" style="width: 20%"><font face="Arial" size="2">Species:</font></td>

    <td class="auto-style1">  
     <font face="Arial">
  	<span class="style1"><strong>&nbsp;BROKE</strong></span></td>

    <td style="width: 44%" class="auto-style1"> 
	<font face="Arial"><font size="2" face="Arial"><b>Check 
	to Print Receipt:&nbsp;
</b></font> <input type="checkbox" name="Print_receipt" value="ON" checked></td>

  </tr>
  
    <tr>
    <td align="right" style="height: 23px; width: 20%;" class="auto-style2"><font face="Arial" size="2">Material #:</td>

    <td colspan="2" style="height: 23px" class="auto-style2"> 
		<font face="Arial" size="2"> 
	<select name="SAP" style="font-weight: 700; width: 132px;" size="1" tabindex="1">
 	<option selected value="">  Select</option>
      <% strsql = "Select tblBrokeSAP.* from tblBrokeSAP where category = 'BROKE'"
      
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    while not MyRec.eof

       %>
        <option <% if strSAP = MyRec("SAP") then %> selected <% end if %> ><%= MyRec("SAP") %></option>
        <% MyRec.movenext
        wend
        MyRec.close %>
  
     </select>&nbsp;
		&nbsp;&nbsp;&nbsp; (Required)</font></td>
  </tr>
  
  <tr>
    <td  align = right height="29" class="auto-style2" style="width: 20%" >
   <font face="Arial" size="2">Trailer:&nbsp;</font></td>
<td  align = left colspan="2" height="29" class="auto-style1">

      <input type="text" name="Trailer" size="15" value="<%= strTrailer %>" tabindex="2" >&nbsp;
		<font face="Arial" size="2"><b>(Required)</b></font></td></tr>
  <tr>

      <td align = right height="28" class="auto-style2" style="width: 20%">
	<font face="Arial" size="2">Select Carrier: </font></td>
<td  align = left height="28" class="auto-style1" colspan="2">

      <font face="Arial" size="2"><b>

      <font face="Arial" color="teal" size="4">   
	<select name="Carrier" style="font-weight: 700">
 	<option value="" selected>  Select Carrier (Required)</option>
 	<% strsql = "Select Carrier,C_Description, Weight from tblCarrier order by Carrier"
 	   	 Set MyRec4 = Server.CreateObject("ADODB.Recordset")
   	 MyRec4.Open strSQL, Session("ConnectionString")
   	 while not MyRec4.eof %>
   	 <option <% if strCarrier = MyRec4("Carrier") then %> selected <% end if %> value="<%= MyRec4("Carrier") %>"><%= MyRec4("Carrier") %>&nbsp;<%= MyRec4("C_Description") %>&nbsp;<%= MyRec4("Weight") %></option>"

 <%MyRec4.Movenext
 wend
 MyRec4.close %>
     </select></font>&nbsp; </b></font>&nbsp; </td>
	</tr>
<tr>
    <td class="auto-style3" style="width: 20%">  
	Format:</td>
    <td colspan="2" class="auto-style1">   

      <font face = arial size = 4 color="teal">
    <select name="Format" style="font-weight: 700" size="1" class="style29">
	<option selected value="">  Select</option>
 	<option   >Bales</option>
<option   >Rolls</option>
<option  >Gaylords</option>

 	</select>  <strong>&nbsp;<span class="auto-style9">Bales:&nbsp;</span> <input name="BalesRF" size="15" value = "<%= strBalesRF%>" style="width: 41px" tabindex="6" class="style29"  ></strong> </td>
  </tr>

<tr>
    <td class="auto-style2" style="width: 20%; height: 15px;">  
	<p align="right">  <font face="Arial" size="2">&nbsp;</font></td>
    <td colspan="2" class="auto-style1" style="height: 15px">   </td>
  </tr>
  <tr>
<td width="17%" class="auto-style6"><strong>Trailer Weight Entry</strong></td>
    <td colspan="2" class="auto-style5">   </td>
  </tr>

<tr>
	<td height="22" width="17%" class="auto-style5" align="right" rowspan="2"><font face="Arial" size="2">Scale Gross:</font></td>
    <td height="22" class="auto-style5" rowspan="2"   >    <font face="Arial">  
	<input name="Gross" size="15" value="<%= strGrossWeight%>" ><span class="style1"> lbs</span></td>
    <td height="22" class="auto-style5" style="height: 11">     <font face="Arial" size="2"   >   
 Scale out (Cab Only):&nbsp;
	<input name="C_Pounds" size="15" value = "<%= strCPounds%>"   >   lbs&nbsp;&nbsp;&nbsp;<span class="style43"> 
	(Trailer weight will be picked up from selected carrier)</span> OR </td>
  </tr>


<tr>
    <td height="22" class="auto-style5" style="height: 11">    
   <font face="Arial" size="2"   >   Scale out (Cab and Trailer): &nbsp; 
 
	<input name="CT_Pounds" size="15" value = "<%= strCTPounds%>"   >   lbs</td>
  </tr>
<tr>
	<td height="22" width="17%" class="auto-style5">
	&nbsp;</td>
    <td height="22" class="auto-style5" colspan="2">    <font face = arial size = 4 color="teal">
	<input type="checkbox" name="Axle" value="Y" class="style29"  > 
	<span class="auto-style7"> <span class="auto-style8"> <span class="style18"> 
	<span class="style13"><span class="style8">Check here if total Truck &amp; Trailer did not fit on scale at same time  </span>
	</span></span></span></span></span>&nbsp;</td>
  </tr>
<tr>
	<td height="22" class="auto-style1" style="width: 20%">&nbsp;</td>
    <td colspan="2" height="22" class="auto-style1">&nbsp;</td>
  </tr>

       <tr>
		<td  align = right class="auto-style2" style="width: 20%" ><font face="Arial" size="2">Shipping Ticket Weight:</font></td>
<td align = left colspan="2" class="auto-style1"> 
<input type="text" name="Sap_Weight" size="15" value = "<%= strSAPWeight%>" style="width: 82px" tabindex="7"></td></tr>
             
       <tr>
		<td  align = right class="auto-style1" style="width: 20%" ><strong>
		<span class="style29">STO Delivery Number</span>:</strong></td>
<td align = left class="auto-style1"> 
<input type="text" name="Load" size="15" value = "<%= strLoad %>" style="width: 81px" tabindex="8"></td>
<td align = left class="auto-style1"> 
<span class="style29"><strong>BOL#</strong></span>:&nbsp; 
<input type="text" name="BOL" size="17" value = "<%= strBOL %>" style="width: 120px" tabindex="9"></td>
</tr>
             
       <tr>
		<td  align = right class="auto-style1" style="width: 20%" >
		&nbsp;</td>
<td align = left colspan="2" class="auto-style1"> 
&nbsp;</td></tr>
             
       <tr>
		<td  align = right class="auto-style2" style="width: 20%" ><font face="Arial" size="2">Date Received:&nbsp;</font></td>
<td align = left colspan="2" class="auto-style1"> 
<input type="text" name="Date_Received" size="15" value = <%= formatdatetime(Now(),2)%> style="width: 136px" tabindex="10"></td></tr>
             
         <tr>
          <td  align = right class="auto-style2" style="width: 20%" >
  <font face="Arial" size="2">Comments:&nbsp;</font></td >
   <td align = left colspan="2" class="auto-style1">   
	<input type="text" name="Other_Comments" size="25" style="width: 278px" value="<%= strOther %>" tabindex="11">
	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font face="Arial"><font size="2">&nbsp;Location: </font>   
	<span class="style1">YARD</span></font><span class="style1">&nbsp;&nbsp;&nbsp;</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	</td></tr>
	
	

<tr>
    <td class="auto-style1" style="width: 20%">&nbsp;</td>

    <td colspan="2" class="auto-style1"> 
	<Input name="Update" type="submit" Value="Submit"  ></td>
  </tr>


</table>

</div>

</form>
</body>
<%  

Function SaveData()
dim strbody, strCID, strECarrier, strETrailer, strELoad, strEDoc, strEDC, strEDCName, strEAdd, strECity, strEState, MyConn2, strsql4
dim strECC, strEBCC
	

strTrailerWeight = 14460

   	 strSQL3 = "Select weight from tblCarrier   where Carrier = '" & strCarrier & "'"

   	 Set MyConn3 = Server.CreateObject("ADODB.Recordset")
   	 MyConn3.Open strSQL3, Session("ConnectionString")
   	 if not MyConn3.eof then
   	 
   	 strTrailerWeight = MyConn3.fields("Weight")
   	 end if
   	 MyConn3.close
  
   	 
   	 if len(strTrailerweight) > 3 then
   	 ' do nothing
   	 else
   	 strTrailerWeight = 14460
   	 end if

	strGrossWeight = Request.form("Gross") 	
	
   	 strTareWeight = 0

If len(request.form("CT_Pounds")) > 2 then
strCTPounds = request.form("CT_Pounds")
	strGrossWeight = Request.form("Gross")

	strTonsReceived = round((strGrossWeight - strCTPounds)/2000,3)

	strPounds = round((strGrossWeight - strCTPounds),3)
		strTareWeight =  strCTPounds
 
	strNet = strTonsReceived
	strTrailerTID = 0

	elseif 	len(request.form("C_Pounds")) > 2 then
	strCPounds = Request.form("C_Pounds")
	
   	 
   	 
	strTonsReceived = round((Request.form("Gross") - strCPounds - strTrailerweight)/2000,3)
	strPounds = round((Request.form("Gross") - strCpounds - strTrailerweight),3)
		strTareWeight =  strTrailerweight + strCpounds
	strGrossWeight = Request.form("Gross")
	strNet = strTonsReceived
	 end if
	 
	   	 	strTrailerTID = 0
	   	 	
 

	
		
		If len(request.form("BalesRF")) > 0 then
	strBalesRF = request.form("BalesRF")
	else
	strBalesRF = 0
	end if

	
	Dim strRightNow
	strRightnow = now()
	
	strSAP = request.form("SAP")
	strsql = "Select Type from tblBrokeSAP where SAP = '" & strSAP & "'"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
   	StrType = MyRec("Type")
	MyRec.close


	strSapWeight = request.form("Sap_Weight")
	if len(strSAPWeight) > 0 then
	strTonsReceived = strSapWeight
	strNet = strTonsReceived
	end if
	if strTonsReceived > 100 then
	strTonsReceived = round(strTonsReceived / 2000,3)
	end if
		strLoad = request.form("Load")
	if len(strLoad) > 0 then
	'do nothing
	else
	strLoad = 0
	end if
	
	if len(strGrossWeight) > 0 then 
	'do nothing
	else
	strGrossWeight = 0
	end if
	

	
	
strsql =  "INSERT INTO tblCars ( SAP_Nbr, Broke_Description, Trailer_weight, Trailer_TID, Carrier, Species, Grade,  "_
&"   Date_received, Location,  Trailer, Other_Comments,  Tons_Received, Net, Gross_weight, Tare_weight, "_
&"  Entry_Time, Entry_BID, Entry_Page, Status, STO_Number, PO, BOL, Format_pkg, Bales_RF ) "_
	&" SELECT '" & strSAP & "', '" & strType & "',  " & strTrailerWeight & ", 0,  "_
	&"  '" & strCarrier & "', 'BROKE', 'BROKE', "_
	&"   '" & strDateReceived & "',   'YARD',  "_
	&" '" & strTrailer & "', '" & strOther & "',  " & strTonsReceived & ", " & strTonsReceived & ","_
	&"  " & strGrossWeight & ", " & strTareWeight & ", "_
	&" '" & strRightNow & "', '" & Session("EmployeeID") & "', 'STO_Broke_Receipt', " & strTonsReceived & ", " & strLoad & ", "_
	&" '" & request.form("PO") & "', '" & request.form("BOL") & "', '" & request.form("Format") & "', " & strBalesRF & " "

	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
				
				strsql5 = "SELECT Max(tblCars.CID) AS MaxOfCID FROM tblCars WHERE tblCars.Trailer = '" & strTrailer & "'"
			
   	 Set MyRec5 = Server.CreateObject("ADODB.Recordset")
   	 MyRec5.Open strSQL5, Session("ConnectionString")
   	 strcarID = MyRec5.fields("MaxofCID")
   	 MyRec5.close
   	 
   	 if len(strNet) > 0 and strGrossweight > 0 then
   	 strsql = "Update tblcars set Tons = " & strnet & " where CID = " & strcarID
   	 	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

end if



		If Request.form("Print_receipt") =  "ON" then
		
	
	
	
				Response.redirect("STO_Truck_receipt.asp?id=" & strCarID & "&p=" & strpounds)
			

	
		else
		Response.redirect ("SelectSTO.asp")
		end if

End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->