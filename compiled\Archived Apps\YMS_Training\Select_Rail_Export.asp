																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>In Transit Rail Cars </TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

 
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->


<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate
Response.ContentType = "application/vnd.ms-excel"
strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -7, strtdate)

strsql = "SELECT * from tblVirginFiber where status = 'Inbound'  and Trailer is not null and vendor = 'Corporate' order by Trailer"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<style type="text/css">
.style2 {
	text-align: right;
}
.style3 {
	font-size: xx-small;
	font-family: Arial, Helvetica, sans-serif;
}
.style4 {
	font-size: x-small;
	font-weight: bold;
}
</style>
</head>

<body>
<br>
	
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  >
<tr>  
<td align = left><font face="Arial" size="2">RF Rail Car Load to Receive </font></td></tr>    </table>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1 align = center>  
	 <tr class="tableheader">
 

				<td  align = left  >     <font face="Arial" size="1">	Trailer/Car</font></td>
		<td   ><b><font face="Arial" size="1">Date Shipped</font></b></td>
	
		<td  align = left  >     <font face="Arial" size="1">ETA</font></td>
		<td  align = left class="style3"  >     Release</td>
	
		<td  align = left  >     <font face="Arial" size="1">Vendor</font></td>
			<td  align = left class="style3"  >City</td>
		<td   ><b><font face="Arial" size="1">Item Description</font></b></td>
			<td   ><b><font face="Arial" size="1">SAP</font></b></td>
			<td   ><b><font face="Arial" size="1">PO</font></b></td>
		<td   ><b><font face="Arial" size="1">Units</font></b></td>
		<td  align = left  >     <font face="Arial" size="1">	Tons</font></td>
		<td   ><b><font face="Arial" size="1">UOM</font></b></td>
			<td    ><b><font face="Arial" size="1">Comments</font></b></td>
		
	</tr>

 <%  Dim ii
       ii = 0
       while not MyRec.Eof

 
 
 strRelease = MyRec("Release")

strsql2 = "Select Release_nbr from tblCars where Release_nbr  = '" & strRelease & "'"
  Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL2, Session("ConnectionString")
    If not MyRec2.eof then 
    'skip record
    else
 
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
 

	<td  ><font size="1" face="Arial"><%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	
<td  ><font size="1" face="Arial"><%= MyRec.fields("Date_shipped")%>&nbsp;</font></td>
<% if datediff("d", MyRec.fields("ETA"), strTdate) > 0 then %>	
<td bgcolor="#FEDAD6"  >
<% else %>
<td>
<% end if %><font size="1" face="Arial"><%= MyRec.fields("ETA")%>&nbsp;</font></td>
<td  ><font size="1" face="Arial"><%= MyRec.fields("Release")%>&nbsp;</font></td>

 

	<td  ><font size="1" face="Arial"> 
	
		
	<%  strV = ""
	strG = ""
	strCity = ""
	
 if len(strRelease) > 3 then
	
	strsql = "SELECT Release, Vendor, Generator, City from tblOrder where   tblOrder.Release='" & strRelease & "'"
 Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
    if  not MyConn.eof then
    strV = MyCOnn("Vendor")
    strG = MyConn("Generator")
    strCity = MyConn("City")
    end if
    MyConn.close
    end if %>

	
	
	
	<%= strV %> - <%= strG %></font></td>
 
<td  ><font size="1" face="Arial"><%= strCity %>&nbsp;</font></td>
	
<td  ><font size="1" face="Arial"><%= MyRec.fields("Species")%>&nbsp;</font></td>
<td  ><font size="1" face="Arial"><%= MyRec.fields("SAP_NBR")%>&nbsp;</font></td>
<td  ><font size="1" face="Arial"><%= MyRec.fields("PO")%>&nbsp;</font></td>
	<td  ><font size="1" face="Arial"><%= MyRec.fields("Bales_VF")%> </font></td>
	
<td  ><font size="1" face="Arial"><%= MyRec.fields("Tons")%> </font></td>
	<td  ><font size="1" face="Arial"><%= MyRec.fields("UOM")%>&nbsp;</font></td>
		<td  ><font size="1" face="Arial"><%= MyRec.fields("Other_comment")%>&nbsp;</font></td>
</tr>

 <%   end if
       ii = ii + 1
       MyRec.MoveNext
     Wend
     MyRec2.close
    %>
</table>
<%

strsql = "SELECT * from tblVirginFiber where status = 'Inbound'  and Trailer is not null and vendor <> 'Corporate' order by Trailer"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>
<tr>  
<td align = center>
 <font face="Arial">Virgin Fiber Rail Car Load to Receive</font></td>

 

</tr>
	    </table>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1 align = center>  
	 <tr class="tableheader">
 

				<td  align = left>     <font face="Arial" size="1">	Trailer/Car</font></td>
		<td  ><b><font face="Arial" size="1">Date Shipped</font></b></td>
	
		<td  align = left>     <font face="Arial" size="1">ETA</font></td>
		
		<td  align = left>     <font face="Arial" size="1">Vendor</font></td>
		<td  ><b><font face="Arial" size="1">Item Description</font></b></td>
			<td  ><b><font face="Arial" size="1">SAP</font></b></td>
				<td  ><b><font face="Arial" size="1">PO</font></b></td>
		<td  ><b><font face="Arial" size="1">Units</font></b></td>
		<td  align = left>     <font face="Arial" size="1">	Tons</font></td>
		<td  ><b><font face="Arial" size="1">UOM</font></b></td>
			<td  ><b><font face="Arial" size="1">Comments</font></b></td>
		
	</tr>

 <% 
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
 

	<td  ><font size="1" face="Arial"><%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	
<td  ><font size="1" face="Arial"><%= MyRec.fields("Date_shipped")%>&nbsp;</font></td>
<% if datediff("d", MyRec.fields("ETA"), strTdate) > 0 then %>	
<td bgcolor="#FEDAD6"  >
<% else %>
<td>
<% end if %><font size="1" face="Arial"><%= MyRec.fields("ETA")%>&nbsp;</font></td>
	<td  ><font size="1" face="Arial"><%= MyRec.fields("Vendor")%>&nbsp;</font></td>
	
<td  ><font size="1" face="Arial"><%= MyRec.fields("Species")%>&nbsp;</font></td>
<td  ><font size="1" face="Arial"><%= MyRec.fields("SAP_NBR")%>&nbsp;</font></td>
<td  ><font size="1" face="Arial"><%= MyRec.fields("PO")%>&nbsp;</font></td>
	<td  ><font size="1" face="Arial"><%= MyRec.fields("Bales_VF")%> </font></td>
	
<td  ><font size="1" face="Arial"><%= MyRec.fields("Tons")%> </font></td>
	<td  ><font size="1" face="Arial"><%= MyRec.fields("UOM")%>&nbsp;</font></td>
		<td  ><font size="1" face="Arial"><%= MyRec.fields("Other_comment")%>&nbsp;</font></td>
</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>

