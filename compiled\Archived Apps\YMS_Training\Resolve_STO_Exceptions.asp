																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Resolve Exceptions</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->




<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate

strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -90, strtdate)

strsql = "SELECT tblCars.* FROM tblCars WHERE (left(Entry_Page,3) = 'STO' or Entry_Page = 'EnterSTOWADReceipt' or Entry_Page = 'EnterSTOReceipt') and  (Species = 'UNRESOLVED' or Species = 'Unresolved' or grade = 'Unresolved'  or Grade = 'UNRESOLVED') and Date_received >'" & strdate & "'"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<style type="text/css">
.style1 {
	font-family: Arial;
	font-size: xx-small;
}
</style>
</head>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Resolve STO Exceptions</b></font></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1>  
	 <tr class="tableheader">
<td>&nbsp;</td>
			<td> <p align="center">       <font face="Arial" size="1">Print<br> Receipt</font></td>

		<td> <span class="style1">Load</span><font face="Arial" size="1"> Number</font></td>

		<td><font face="Arial" size="1"><b>Trailer</b></font></td>
		<td><font face="Arial" size="1"><b>Carrier</b></font></td>
		<td><font face="Arial" size="1">Species</font></td>
    	<td><font face="Arial" size="1">Grade</font></td>
		<td><font face="Arial" size="1">KC Site</font></td>	
	    <td><font face="Arial" size="1">Location</font></td>
		<td><font face="Arial" size="1">Date<br> Received</font></td>
		<td><font face="Arial" size="1">Date<br>Unloaded</font></td>	
        <td><p align="center"><font face="Arial" size="1">Tons<br> Received</font></td>
       	<td><p align="center"> <font face="Arial" size="1">Net<br> Weight</font></td>
		
	
		<td><font size="1" face="Arial">Other</font></td>

	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td> <font size="1" face="Arial"><a href="STO_Edit.asp?id=<%= MyRec.fields("CID") %>">Edit</a></td>
	<td align = center> <font size="1" face="Arial"><a href="STO_Truck_Receipt.asp?p=r&id=<%= MyRec.fields("CID") %>"><%= MyRec.fields("CID")%></a></td>

	<td  >        <font size="1" face="Arial">        <%= MyRec.fields("STO_Number")%>&nbsp;</font></td>
	<td  > <font size="1" face="Arial"><b><%= MyRec.fields("Trailer")%>&nbsp;</font></b></td>
			<td  ><font size="1" face="Arial"><b><%= MyRec.fields("Carrier")%>&nbsp;</font></b></td>


		<td  ><font face="Arial" size="1"> <%= MyRec.fields("Species")%>&nbsp;</font></td>
				<td  >
				<font face="Arial" size="1"> <%= MyRec.fields("Grade")%>&nbsp;</font></td>

		<td><font size="1" face="Arial"><%= MyRec.fields("Generator")%>&nbsp;</font></td>
	
	<td  >  <font size="1" face="Arial">        <%= MyRec.fields("Location")%>&nbsp;</font></td>
			<td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Date_Received")%>&nbsp;</font></td>
			<% if len(MyRec.fields("Date_unloaded")) > 0 then %>
		<td  >		 <font size="1" face="Arial">        <%= formatdatetime(MyRec.fields("Date_unloaded"),2)%>&nbsp;</font></td>
		<% else %>
			<td  >		 <font size="1" face="Arial"> &nbsp;</font></td>
		
		<% end if %>
		<td align = right>
				 <font size="1" face="Arial">        <%= MyRec.fields("Tons_received")%>&nbsp;</font></td>
				 <td align = right >		 <font size="1" face="Arial">        <%= MyRec.fields("Net")%>&nbsp;</font></td>

	<td  >	 <font size="1" face="Arial">        <%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>
	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>


<!--#include file="Fiberfooter.inc"-->