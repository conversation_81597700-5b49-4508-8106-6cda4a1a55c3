																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Grading with Deduction</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->



<% Dim MyRec, strsql, strdate, strYdate, strToday

strdate = formatdatetime(Now(),2)
strToday = Now()
strYdate = dateadd("d", -120, strToday)

strsql = "SELECT tblOCCGrading.Deduct_approval, tblOccGrading.QID, tblCars.* "_
&" FROM tblCars INNER JOIN tblOCCGrading ON tblCars.CID = tblOCCGrading.CID "_
&"  WHERE Time_unloaded >= '" & strYdate & "' and deduction > 0 order by Time_Unloaded desc"



    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<style type="text/css">
.style1 {
	font-family: arial;
	font-size: medium;
}
</style>
</head>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center class="style1"><strong>Grading Deduction List</strong></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1>  
	 <tr class="tableheader">
<td>&nbsp;</td>
	<td  >
        <font face="Arial" size="2"><b>Trailer</b></font></td>
        <td  >
        <font face="Arial" size="1">Carrier</font></td>
		<td  >
        <font face="Arial" size="1">Species</font></td>
    
		<td  >
        <font face="Arial" size="1">Vendor</font></td>
		<td  >
        <font face="Arial" size="1">PO Number</font></td>
		<td  >
        <p align="center">
        <font face="Arial" size="1">Release<br> Number</font></td>
	
		<td  >
        <font face="Arial" size="1">REC Number</font></td>
	
		<td  >
        <p align="center">
        <font face="Arial" size="1">Tons<br> Received</font></td>
        <td  >
        <p align="center">
        <font face="Arial" size="1">Deduction</font></td>
<td align="center"><font face="Arial" size="1">Net</font></td>

	
		<td  >
        <p align="center">
        <font face="Arial" size="1">Approval</font></td>

       		<td  > <font face="Arial" size="1">Date<br> Unloaded</font></td>
		<td  >
        <font face="Arial" size="1">Generator</font></td>
		<td  >
        <font face="Arial" size="1">Generator<br> City</font></td>
		<td  >
        <font face="Arial" size="1">Gen<br> State</font></td>
		<td  >
        <font size="1" face="Arial">Other</font></td>


	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
   <% if Session("EmployeeID") = "C97338" or Session("EmployeeID") = "B96138"  or Session("EmployeeID") = "U04211"  then %>
   <% if MyRec("QID") > 59553 then %>
   <td> <font size="1" face="Arial"><a href="Grading_Edit_New.asp?id=<%= MyRec.fields("CID") %>&p=c">Edit</a></td>
   <% else %>
      <td> <font size="1" face="Arial"><a href="Grading_Edit.asp?id=<%= MyRec.fields("CID") %>&p=c">Edit</a></td>

   <% end if %>
   <% else %>
      <td> <font size="1" face="Arial">&nbsp;</td>
      <% end if %>
	<td  >
        <font size="2" face="Arial"><b>
        <%= MyRec.fields("Trailer")%></font></b></td>
        	<td  >      <font face="Arial" size="1">
        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
		
			<td  >
        <font face="Arial" size="1">
        <%= MyRec.fields("Species")%>&nbsp;</font></td>
		<td>
        <font size="1" face="Arial">
        <%= MyRec.fields("Vendor")%></font></td>
		<td  >
        <font size="1" face="Arial">
        <%= MyRec.fields("PO")%></font></td>
		<td  >
        <font size="1" face="Arial">
        <%= MyRec.fields("Release_Nbr")%></font></td>
	<td>
		 <font size="1" face="Arial">
        <%= MyRec.fields("REC_Number")%></font></td>
		<td align = right  >
		 <font size="1" face="Arial">
        <%= MyRec.fields("Tons_received")%></font></td>
        	<td align = right  >
		 <font size="1" face="Arial"><% if MyRec.fields("Deduction") > 0 then %>
        <%= round(MyRec.fields("Deduction"),3)%></font></td>
        <% else %>
       &nbsp;</font></td>
<% end if %>
<% if MyRec("Deduction") > 0 and MyRec("Net") = MyRec("Tons_Received") then %>
<td align = right bgcolor="pink"  >
<% else %>
<td align = right  >
<% end if %>

		 <font size="1" face="Arial">
        <%= MyRec.fields("Net")%>&nbsp;</font></td>

	<td align = right  >
		 <font size="1" face="Arial">
        <%= MyRec.fields("deduct_Approval")%>&nbsp;</font></td>

		<td  >
		 <font size="1" face="Arial">
        <%= MyRec.fields("Date_Unloaded")%></font></td>
       <td  >
		 <font size="1" face="Arial">
        <%= MyRec.fields("Generator")%></font></td>
		<td  >
		 <font size="1" face="Arial">
        <%= MyRec.fields("Gen_City")%></font></td>
		<td  >
		 <font size="1" face="Arial">
        <%= MyRec.fields("Gen_State")%></font></td>
		<td  >
		 <font size="1" face="Arial">
        <%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>

	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>

<!--#include file="Fiberfooter.inc"-->