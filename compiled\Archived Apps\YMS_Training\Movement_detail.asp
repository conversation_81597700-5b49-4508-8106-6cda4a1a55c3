																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Trailer Movement Report</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->



<% Dim MyRec, strsql, strid, MyConn, strsqlC
strid = Request.querystring("id")



strsql = "SELECT tblCars.Carrier, tblcars.trans_carrier, tblcars.transfer_trailer_nbr, tblCars.Trailer, tblCars.Entry_bid, tblCars.date_received, tblCars.Transfer_date, tblMovement.DDate, tblMovement.CID, tblMovement.Tdate, "_
&" tblMovement.From_location, tblMovement.To_location, tblMovement.BID, tblMovement.Comment, tblMovement.From_door, "_
&" tblMovement.To_door FROM tblCars INNER JOIN tblMovement ON tblCars.CID = tblMovement.CID where tblCars.CID = " & strid & " order by Ddate"
   
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    
    If not MyRec.eof then
    


%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=0>
<tr>

<td align = center><b>
<font face="arial" size="4" >Trailer Movement Report </td>


</tr>
	    </table><br>
	    <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=90%  border=0 align = center>
<tr>
<td align = center><font face="arial" size="2" >Trailer/Transfer_trailer: <%= MyRec.fields("Trailer")%>&nbsp; <%= MyRec.fields("Transfer_Trailer_nbr")%></font></td>

 <TD align = center><font size="2" face = arial>Date Received/Transferred: <%= MyRec.fields("Date_received")%>&nbsp;
<%= MyRec.fields("Transfer_date")%>&nbsp;</font></td>
<td align = center><font face="arial" size="2" >Carrier/Transfer Carrier: <%= MyRec.fields("Carrier")%>&nbsp; <%= MyRec.fields("Trans_Carrier")%></font></td>

<td align = center><font face="arial" size="2" >Received by: <%= MyRec.fields("Entry_BID")%>&nbsp;</font></td>


</tr>
	    </table><br>

	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">

	<td  ><b><font face="Arial" size="2">Movement Date</font></b></td>
		<td align = left><b><font face="Arial" size="2">Transaction Date</font></b></td>
    	<td align = left ><font face="Arial" size="2">
		<b><font face="Arial" size="2">From<br> Location</font></b></td>
		<td ><b><font face="Arial" size="2">To<br> Location</font></b></td>

<td width="69" ><b><font face="Arial" size="2">BID</font></b></td>
<td width="69" ><b><font face="Arial" size="2">Comments</font></b></td>
	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    


		
 
   <td  >  <font size="2" face="Arial">        <%= MyRec.fields("DDate")%></font></td>
    <td  >  <font size="2" face="Arial">        <%= MyRec.fields("TDate")%></font></td>
 <td  >  <font size="2" face="Arial">        <%= MyRec.fields("From_location")%></font></td>
 <td  >  <font size="2" face="Arial">        <%= MyRec.fields("To_location")%></font></td>
	
  <td  >  <font size="2" face="Arial">        <%= MyRec.fields("BID")%></font></td>	
	<td><font size="2" face="Arial"><%= MyRec.fields("Comment")%>&nbsp;</font></td>

	</tr>

 <%    ii = ii + 1
       MyRec.MoveNext
    
   Wend
   
else
Response.write ("<font fact = arial size = 3><br><br><b>There is no movement history for this trailer</b></br></font>")
end if %>