
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Warehouse Activity </TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<%
	Dim strsQL, rstEquip
	strBegDate = DateSerial(Year(now()), Month(now()), 1) 
	if datepart("d", Date()) = 1 then
	strBegDate = DateSerial(Year(now()), Month(now()) -1, 1) 
	end if 
	strEndDate = Dateadd("d", -1, Date())

 strsql = "SELECT  count(tblcars.cid) as CountofcID, sum(net) as SumofNet FROM tblCars  where (PMO_nbr = 'DC' or PMO_Nbr = 'MERCHANTS') "_
&" and date_received >= '" & strBegDate & "' and date_received <= '" & strEndDate & "'"
				Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   strCount = MyRec("CountofCID")
   end if
   


 strsql = "SELECT  count(tblcars.cid) as CountofcID, sum(net) as SumofNet FROM tblCars  where (PMO_nbr = 'DC') "_
&" and date_received >= '" & strBegDate & "' and date_received <= '" & strEndDate & "'"
				Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   strFromDC = MyRec("CountofCID")
   end if

 strsql = "SELECT  count(tblcars.cid) as CountofcID, sum(net) as SumofNet FROM tblCars  where ( PMO_Nbr = 'MERCHANTS') "_
&" and date_received >= '" & strBegDate & "' and date_received <= '" & strEndDate & "'"
				Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   strFromM = MyRec("CountofCID")
   end if

 strsql = "SELECT  count(tblcars.cid) as CountofcID, sum(net) as SumofNet FROM tblCars  where (Location = 'DC WHSE' or Location = 'MERCHANTS') "_
&" and date_received >= '" & strBegDate & "' and date_received <= '" & strEndDate & "'"
				Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   strToCount = MyRec("CountofCID")
   end if

 strsql = "SELECT  count(tblcars.cid) as CountofcID, sum(net) as SumofNet FROM tblCars  where (Location = 'DC WHSE') "_
&" and date_received >= '" & strBegDate & "' and date_received <= '" & strEndDate & "'"
				Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   strToDC = MyRec("CountofCID")
   end if

 strsql = "SELECT  count(tblcars.cid) as CountofcID, sum(net) as SumofNet FROM tblCars  where ( Location = 'MERCHANTS') "_
&" and date_received >= '" & strBegDate & "' and date_received <= '" & strEndDate & "'"
				Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   strToM = MyRec("CountofCID")
   end if

 %>
   <style type="text/css">
.style1 {
	text-align: left;
}
.style2 {
	font-family: Arial, Helvetica, sans-serif;
	text-align: left;
	font-size: x-small;
}
.style3 {
	font-family: Arial, Helvetica, sans-serif;
}
.style4 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.style5 {
	text-decoration: underline;
}
.style6 {
	text-align: left;
	font-size: x-small;
}
.style7 {
	font-size: x-small;
}
</style>
</head><br><table style="width: 100%">
<tr><td class="style4" width="25%"><strong><%= Now() %></strong></td><td class="style2"><strong>Warehouse Activity Report</strong></td>
</tr>
<tr>
<td class="style3" colspan="2">
<p class="style2"><strong>Date Range:  <%= strBegDate %> - <%= strEndDate %></strong></p></td>
</tr>
</table><br>
   <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>

<tr> <td bgcolor = white class="style1" width="12%" ><b><font face="Arial" size="2">Total 
	Movements FROM: <%= strCount%>&nbsp;</font></td><td>&nbsp;</td></tr>
 <tr> <td colspan ="2" bgcolor = white class="style1" ><b><font face="Arial" size="2">&nbsp;</font></td></tr>

 <tr><td width="12%">&nbsp;</td> <td bgcolor = white class="style1" ><b><font face="Arial" size="2">
	<span class="style5">Total From DC: <%= strFromDC%></span>&nbsp;<br>
 <% strBlue = "%" & "700483" & "%"
   strsql = "SELECT  Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.PMO_Nbr)='DC')) and Date_received <= '" & strEndDate & "' "_
&" And Left([Transfer_trailer_nbr],4)<>'BLUE' And Left([Transfer_trailer_nbr],6)<>'700483' and (Species = 'Wetlap' or Species = 'P-MORFF')   "_
&"  order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then %>
WETLAP:   <%= MyRec("CountofCid") %><br>
<% MyRec.close
end if
 
 
 

  strsql = "SELECT tblCars.Species, Tier_Rating, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.PMO_Nbr)='DC')) and Date_received <= '" & strEndDate & "' "_
&" And Left([Transfer_trailer_nbr],4)<>'BLUE' And Left([Transfer_trailer_nbr],6)<>'700483'   "_
&" and Species ='KCOP'"_
&" GROUP BY tblCars.Species, Tier_rating order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   while not MyRec.eof %>
 <%= UCASE(MyRec("Species")) %>-<%= MyRec("Tier_Rating") %>:  <%= MyRec("CountofCid") %><br>
<% MyRec.movenext
wend
MyRec.close
end if 

  strsql = "SELECT tblCars.Species, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.PMO_Nbr)='DC')) and Date_received <= '" & strEndDate & "' "_
&" And Left([Transfer_trailer_nbr],4)<>'BLUE' And Left([Transfer_trailer_nbr],6)<>'700483'   "_
&" and Species ='OF'"_
&" GROUP BY tblCars.Species order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   while not MyRec.eof %>
  <%= UCASE(MyRec("Species")) %>-<%= MyRec("Tier_Rating") %>:  <%= MyRec("CountofCid") %><br>
<% MyRec.movenext
wend
MyRec.close
end if 

  strsql = "SELECT tblCars.Species, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.PMO_Nbr)='DC')) and Date_received <= '" & strEndDate & "' "_
&" And Left([Transfer_trailer_nbr],4)<>'BLUE' And Left([Transfer_trailer_nbr],6)<>'700483'   "_
&" and Species <> 'Wetlap' and Species <> 'P-MORFF' and Species <> 'KCOP' and Species <> 'OF'"_
&" GROUP BY tblCars.Species order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   while not MyRec.eof %>
 <%= UCASE(MyRec("Species")) %>:  <%= MyRec("CountofCid") %><br>
<% MyRec.movenext
wend
MyRec.close
end if 




 strsql = "SELECT tblCars.Species, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.PMO_Nbr)='DC')) and Date_received <= '" & strEndDate & "' "_
&" And (Left([Transfer_trailer_nbr],4)='BLUE' OR Left([Transfer_trailer_nbr],6)='700483')  "_

&" GROUP BY tblCars.Species order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then    %>
 BLUE CONTAINER:  <%= MyRec("CountofCid") %><br>
<% 
MyRec.close
end if %>

 </font><br></td></tr>
 <tr> <td width="12%">&nbsp;</td><td bgcolor = white class="style1" ><b><font face="Arial" size="2">
	<span class="style5">Total From Merchants: <%= strFromM%></span>&nbsp;<br>
 
 <%    strsql = "SELECT  Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.PMO_Nbr)='MERCHANTS')) and Date_received <= '" & strEndDate & "' "_
&" And Left([Transfer_trailer_nbr],4)<>'BLUE' And Left([Transfer_trailer_nbr],6)<>'700483' and (Species = 'Wetlap' or Species = 'P-MORFF')   "
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   if MyRec("CountofCID") > 0 then %>
WETLAP:   <%= MyRec("CountofCid") %><br>
<% end if
MyRec.close
end if

 
   strsql = "SELECT tblCars.Species, Tier_Rating, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.PMO_Nbr)='MERCHANTS')) and Date_received <= '" & strEndDate & "' "_
&" And Left([Transfer_trailer_nbr],4)<>'BLUE' And Left([Transfer_trailer_nbr],6)<>'700483'   "_
&" and Species ='KCOP'"_
&" GROUP BY tblCars.Species, Tier_rating order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   while not MyRec.eof %>
 <%= UCASE(MyRec("Species")) %>-<%= MyRec("Tier_Rating") %>:  <%= MyRec("CountofCid") %><br>
<% MyRec.movenext
wend
MyRec.close
end if 

    strsql = "SELECT tblCars.Species, Tier_Rating, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.PMO_Nbr)='MERCHANTS')) and Date_received <= '" & strEndDate & "' "_
&" And Left([Transfer_trailer_nbr],4)<>'BLUE' And Left([Transfer_trailer_nbr],6)<>'700483'   "_
&" and Species ='OF'"_
&" GROUP BY tblCars.Species, Tier_rating order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   while not MyRec.eof %>
 <%= UCASE(MyRec("Species")) %>-<%= MyRec("Tier_Rating") %>:  <%= MyRec("CountofCid") %><br>
<% MyRec.movenext
wend
MyRec.close
end if 

 
 strsql = "SELECT tblCars.Species, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.PMO_Nbr)='MERCHANTS')) and Date_received <= '" & strEndDate & "' "_
&" And Left([Transfer_trailer_nbr],4)<>'BLUE' And Left([Transfer_trailer_nbr],6)<>'700483'   "_
&" and Species <> 'Wetlap' and Species <> 'P-MORFF' and Species <> 'KCOP' and Species <> 'OF'"_
&" GROUP BY tblCars.Species order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   while not MyRec.eof %>
  <%= UCASE(MyRec("Species")) %>:  <%= MyRec("CountofCid") %><br>
<% MyRec.movenext
wend
MyRec.close
end if 
 strsql = "SELECT tblCars.Species, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.PMO_Nbr)='MERCHANTS')) and Date_received <= '" & strEndDate & "' "_
&" And (Left([Transfer_trailer_nbr],4)='BLUE' OR Left([Transfer_trailer_nbr],6)='700483')  "_
&" GROUP BY tblCars.Species order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then    %>
 BLUE CONTAINER:  <%= MyRec("CountofCid") %><br>
<% 
MyRec.close
end if %>

 
 
 </font><br></td></tr>
  <tr><td width="12%">&nbsp;</td> <td bgcolor = white class="style1" ><b><font face="Arial" size="2">&nbsp;</font></td></tr>
 <tr>  <td width="12%" bgcolor = white class="style1" ><b><font face="Arial" size="2">Total 
	Movements TO: <%= strToCount%>&nbsp;<td>&nbsp;</td></tr>
     <tr> <td colspan="2" bgcolor = white class="style1" ><font face="Arial" size="2">&nbsp;</font></td></tr>
    
 <tr><td width="12%">&nbsp;</td> <td bgcolor = white class="style1" ><b><font face="Arial" size="2">
	<span class="style5">Total To DC: <%= strToDC%></span>&nbsp;<br>

    <%  strsql = "SELECT  Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE Date_received >= '" & strBegDate & "' AND Location='DC WHSE' and Date_received <= '" & strEndDate & "' "_
&"  and (Species = 'Wetlap' or Species = 'P-MORFF') "
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then 
   if MyRec("CountofCid") > 0 then %>
WETLAP:   <%= MyRec("CountofCid") %><br>
<% end if
MyRec.close
end if


strWLDC = 0
strsql = "SELECT  Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE Date_received >= '" & strBegDate & "' AND Location='DC WHSE' and Date_received <= '" & strEndDate & "' "_
&"  and (Species = 'Wetlap' or Species = 'P-MORFF') "
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then 
   if MyRec("CountofCid") > 0 then 
   strWLDC = MyREc("CountofCID")
   
   
  strsql2 = "SELECT  Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE Date_received >= '" & strBegDate & "' AND Location='DC WHSE' and Date_received <= '" & strEndDate & "' "_
&"  and (Species = 'Wetlap' or Species = 'P-MORFF') and carrier = 'RAIL' "
 	Set MyRec2 = Server.CreateObject("ADODB.Recordset")
	MyRec2.Open strSQL2, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec2.eof then 
   strWLDCR = MyRec2("CountofCID")
   strWLDC = strWLDC + (strWLDCR * 2)
   else
   strWLDCR = 0
   end if
   MyRec2.close
 
   
 if strWLDCR = 0 then  %>
 WETLAP:  <%= strWLDC %> <br>
 <% else %>
 WETLAP:  <%= strWLDC %> <br>
<% if strWLDCR = 1 then %>
 WETLAP Rail Cars: <%=   strWLDCR * 3 %> (<%= strWLDCR %> Rail Car) <br>
<% else %>
 WETLAP Rail Cars: <%= strWLDCR * 3 %> (<%=  strWLDCR %>  Rail Cars)<br>
<% end if
end if
 end if
MyRec.close
end if


     strsql = "SELECT tblCars.Species, Tier_rating, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.Location)='DC WHSE')) and Date_received <= '" & strEndDate & "' "_
&" and Species ='KCOP' "_
&" GROUP BY tblCars.Species, Tier_Rating order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   while not MyRec.eof 
   strRcount = 0
   strTier = MyRec("Tier_Rating")
   strSCount = MyREc("CountofCID")
  strsql2 = "Select count(CID) as CountofCID from tblCars  WHERE Date_received>= '" & strBegDate & "' AND Location='DC WHSE' and Date_received <= '" & strEndDate & "' "_
&" and Species = 'KCOP' and Tier_Rating = '" & strTier & "' and Carrier = 'RAIL'"
   	Set MyRec2 = Server.CreateObject("ADODB.Recordset")
	MyRec2.Open strSQL2, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec2.eof then
   strScount = strScount + (MyRec2("CountofCID") * 2)
   strYes = "Yes"
   strRcount = MyRec2("CountofCID")
   else
   strYes = "No"
   strRcount = 0
   end if
   MyRec2.close
If strRcount = 0 then %>
KCOP-<%= MyRec("Tier_Rating") %>:  <%= MyRec("CountofCid") %><br>

<%  else %>
  KCOP-<%= MyRec("Tier_Rating") %>:  <%= MyRec("CountofCid") %><br>
 <% if strRcount = 1 then %>
 KCOP-<%= MyRec("Tier_Rating") %>:  Rail Cars:  <%= strRcount * 3%>  (<%= strRcount %> Rail Car)<br>"
 <% else %>
 KCOP-<%= MyRec("Tier_Rating") %>:  Rail Cars:  <%= strRcount * 3%>  (<%= strRcount %> Rail Cars)<br>" <% end if
 end if
 MyRec.movenext
wend
MyRec.close
end if
   
      strsql = "SELECT tblCars.Species, Tier_rating, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.Location)='DC WHSE')) and Date_received <= '" & strEndDate & "' "_
&" and Species ='OF' "_
&" GROUP BY tblCars.Species, Tier_rating order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   while not MyRec.eof 
   strRcount = 0
   strTier = MyRec("Tier_Rating")
   strSCount = MyREc("CountofCID")
  strsql2 = "Select count(CID) as CountofCID from tblCars  WHERE Date_received>= '" & strBegDate & "' AND Location='DC WHSE' and Date_received <= '" & strEndDate & "' "_
&" and Species = 'OF' and Tier_Rating = '" & strTier & "' and Carrier = 'RAIL'"
   	Set MyRec2 = Server.CreateObject("ADODB.Recordset")
	MyRec2.Open strSQL2, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec2.eof then
   strScount = strScount + (MyRec2("CountofCID") * 2)
   strYes = "Yes"
   strRcount = MyRec2("CountofCID")
   else
   strYes = "No"
   strRcount = 0
   end if
   MyRec2.close
If strRcount = 0 then %>
OF-<%= MyRec("Tier_Rating") %>:  <%= MyRec("CountofCid") %><br>

<%  else %>
  OF-<%= MyRec("Tier_Rating") %>:  <%= MyRec("CountofCid") %><br>
 <% if strRcount = 1 then %>
 OF-<%= MyRec("Tier_Rating") %>:  Rail Cars:  <%= strRcount * 3%>  (<%= strRcount %> Rail Car)<br>"
 <% else %>
 OF-<%= MyRec("Tier_Rating") %>:  Rail Cars:  <%= strRcount * 3%>  (<%= strRcount %> Rail Cars)<br>" <% end if
 end if
 MyRec.movenext
wend
MyRec.close
end if
   
    
    strsql = "SELECT tblCars.Species, Count(tblCars.CID) AS CountOfCID  FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.Location)='DC WHSE')) and Date_received <= '" & strEndDate & "' "_
&" and Species <> 'Wetlap' and  Species <> 'P-MORFF'  and Species <> 'KCOP' and Species <> 'OF' "_
&" GROUP BY tblCars.Species order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   while not MyRec.eof  
     strRcount = 0
 
   strSCount = MyREc("CountofCID")
   strSpecies = MyRec("Species")
  strsql2 = "Select count(CID) as CountofCID from tblCars  WHERE Date_received>= '" & strBegDate & "' AND Location='DC WHSE' and Date_received <= '" & strEndDate & "' "_
&" and Species = '" & strSpecies & "'   and Carrier = 'RAIL'"
   	Set MyRec2 = Server.CreateObject("ADODB.Recordset")
	MyRec2.Open strSQL2, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec2.eof then
   strScount = strScount + (MyRec2("CountofCID") * 2)
   strYes = "Yes"
   strRcount = MyRec2("CountofCID")
   else
   strYes = "No"
   strRcount = 0
   end if
   MyRec2.close
If strRcount = 0 then %>
<%= strSpecies %>:  <%= MyRec("CountofCid") %><br>

<%  else %>
  <%= strSpecies %>:  <%= MyRec("CountofCid") %><br>
 <% if strRcount = 1 then %>
<%= strSpecies %>:  Rail Cars:  <%= strRcount * 3%>  (<%= strRcount %> Rail Car)<br>"
 <% else %>
<%= strSpecies %>:  Rail Cars:  <%= strRcount * 3%>  (<%= strRcount %> Rail Cars)<br>" 
<% end if
 end if
 MyRec.movenext
wend
MyRec.close
end if %>
 
 
 </font></td></tr>
     <tr> <td colspan="2" bgcolor = white class="style1" ><font face="Arial" size="2">&nbsp;</font></td></tr>
 <tr><td width="12%">&nbsp;</td> <td bgcolor = white class="style1" ><b><font face="Arial" size="2">
	<span class="style5">Total To Merchants: <%= strToM%></span>&nbsp;<br>
 
    
       <%  strsql = "SELECT  Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE Date_received >= '" & strBegDate & "' AND Location='MERCHANTS' and Date_received <= '" & strEndDate & "' "_
&"  and (Species = 'Wetlap' or Species = 'P-MORFF') "
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then 
   if MyRec("CountofCid") > 0 then %>
WETLAP:   <%= MyRec("CountofCid") %><br>
<% end if
MyRec.close
end if


strWLDC = 0
strsql = "SELECT  Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE Date_received >= '" & strBegDate & "' AND Location='MERCHANTS' and Date_received <= '" & strEndDate & "' "_
&"  and (Species = 'Wetlap' or Species = 'P-MORFF') "
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then 
   if MyRec("CountofCid") > 0 then 
   strWLDC = MyREc("CountofCID")
   
   
  strsql2 = "SELECT  Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE Date_received >= '" & strBegDate & "' AND Location='MERCHANTS' and Date_received <= '" & strEndDate & "' "_
&"  and (Species = 'Wetlap' or Species = 'P-MORFF') and carrier = 'RAIL' "
 	Set MyRec2 = Server.CreateObject("ADODB.Recordset")
	MyRec2.Open strSQL2, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec2.eof then 
   strWLDCR = MyRec2("CountofCID")
   strWLDC = strWLDC + (strWLDCR * 2)
   else
   strWLDCR = 0
   end if
   MyRec2.close
 
   
 if strWLDCR = 0 then  %>
 WETLAP:  <%= strWLDC %> <br>
 <% else %>
 WETLAP:  <%= strWLDC %> <br>
<% if strWLDCR = 1 then %>
 WETLAP Rail Cars: <%=   strWLDCR * 3 %> (<%= strWLDCR %> Rail Car) <br>
<% else %>
 WETLAP Rail Cars: <%= strWLDCR * 3 %> (<%=  strWLDCR %>  Rail Cars)<br>
<% end if
end if
 end if
MyRec.close
end if


     strsql = "SELECT tblCars.Species, Tier_rating, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.Location)='MERCHANTS')) and Date_received <= '" & strEndDate & "' "_
&" and Species ='KCOP' "_
&" GROUP BY tblCars.Species, Tier_Rating order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   while not MyRec.eof 
   strRcount = 0
   strTier = MyRec("Tier_Rating")
   strSCount = MyREc("CountofCID")
  strsql2 = "Select count(CID) as CountofCID from tblCars  WHERE Date_received>= '" & strBegDate & "' AND Location='MERCHANTS' and Date_received <= '" & strEndDate & "' "_
&" and Species = 'KCOP' and Tier_Rating = '" & strTier & "' and Carrier = 'RAIL'"
   	Set MyRec2 = Server.CreateObject("ADODB.Recordset")
	MyRec2.Open strSQL2, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec2.eof then
   strScount = strScount + (MyRec2("CountofCID") * 2)
   strYes = "Yes"
   strRcount = MyRec2("CountofCID")
   else
   strYes = "No"
   strRcount = 0
   end if
   MyRec2.close
If strRcount = 0 then %>
KCOP-<%= MyRec("Tier_Rating") %>:  <%= MyRec("CountofCid") %><br>

<%  else %>
  KCOP-<%= MyRec("Tier_Rating") %>:  <%= MyRec("CountofCid") %><br>
 <% if strRcount = 1 then %>
 KCOP-<%= MyRec("Tier_Rating") %>:  Rail Cars:  <%= strRcount * 3%>  (<%= strRcount %> Rail Car)<br>"
 <% else %>
 KCOP-<%= MyRec("Tier_Rating") %>:  Rail Cars:  <%= strRcount * 3%>  (<%= strRcount %> Rail Cars)<br>" <% end if
 end if
 MyRec.movenext
wend
MyRec.close
end if
   
      strsql = "SELECT tblCars.Species, Tier_rating, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.Location)='MERCHANTS')) and Date_received <= '" & strEndDate & "' "_
&" and Species ='OF' "_
&" GROUP BY tblCars.Species, Tier_rating order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   while not MyRec.eof 
   strRcount = 0
   strTier = MyRec("Tier_Rating")
   strSCount = MyREc("CountofCID")
  strsql2 = "Select count(CID) as CountofCID from tblCars  WHERE Date_received>= '" & strBegDate & "' AND Location='MERCHANTS' and Date_received <= '" & strEndDate & "' "_
&" and Species = 'OF' and Tier_Rating = '" & strTier & "' and Carrier = 'RAIL'"
   	Set MyRec2 = Server.CreateObject("ADODB.Recordset")
	MyRec2.Open strSQL2, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec2.eof then
   strScount = strScount + (MyRec2("CountofCID") * 2)
   strYes = "Yes"
   strRcount = MyRec2("CountofCID")
   else
   strYes = "No"
   strRcount = 0
   end if
   MyRec2.close
If strRcount = 0 then %>
OF-<%= MyRec("Tier_Rating") %>:  <%= MyRec("CountofCid") %><br>

<%  else %>
  OF-<%= MyRec("Tier_Rating") %>:  <%= MyRec("CountofCid") %><br>
 <% if strRcount = 1 then %>
 OF-<%= MyRec("Tier_Rating") %>:  Rail Cars:  <%= strRcount * 3%>  (<%= strRcount %> Rail Car)<br>"
 <% else %>
 OF-<%= MyRec("Tier_Rating") %>:  Rail Cars:  <%= strRcount * 3%>  (<%= strRcount %> Rail Cars)<br>" <% end if
 end if
 MyRec.movenext
wend
MyRec.close
end if
   
    
    strsql = "SELECT tblCars.Species, Count(tblCars.CID) AS CountOfCID  FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.Location)='MERCHANTS')) and Date_received <= '" & strEndDate & "' "_
&" and Species <> 'Wetlap' and  Species <> 'P-MORFF'  and Species <> 'KCOP' and Species <> 'OF' "_
&" GROUP BY tblCars.Species order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   while not MyRec.eof  
     strRcount = 0
 
   strSCount = MyREc("CountofCID")
   strSpecies = MyRec("Species")
  strsql2 = "Select count(CID) as CountofCID from tblCars  WHERE Date_received>= '" & strBegDate & "' AND Location='MERCHANTS' and Date_received <= '" & strEndDate & "' "_
&" and Species = '" & strSpecies & "'   and Carrier = 'RAIL'"
   	Set MyRec2 = Server.CreateObject("ADODB.Recordset")
	MyRec2.Open strSQL2, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec2.eof then
   strScount = strScount + (MyRec2("CountofCID") * 2)
   strYes = "Yes"
   strRcount = MyRec2("CountofCID")
   else
   strYes = "No"
   strRcount = 0
   end if
   MyRec2.close
If strRcount = 0 then %>
<%= strSpecies %>:  <%= MyRec("CountofCid") %><br>

<%  else %>
  <%= strSpecies %>:  <%= MyRec("CountofCid") %><br>
 <% if strRcount = 1 then %>
<%= strSpecies %>:  Rail Cars:  <%= strRcount * 3%>  (<%= strRcount %> Rail Car)<br>"
 <% else %>
<%= strSpecies %>:  Rail Cars:  <%= strRcount * 3%>  (<%= strRcount %> Rail Cars)<br>" 
<% end if
 end if
 MyRec.movenext
wend
MyRec.close
end if %> </font></td></tr>

 </table>
<strong>
<br>
</strong>
<%   strBegDate = dateadd("d", -1, Date())
strEndDate = Date() %>

<span class="style4"><strong><%= strBegDate %> Movement</strong></span>
   <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=50%  border=1 align = left>
 <tr class="tableheader">	
      <td  align="left" class="style7"  class="style6"><font face="Arial">
		<strong>To/From</strong></font></td>
	<td  align="left" class="style6" ><font face="Arial"><strong>Species</strong></font></td>
		<td  align="left" class="style6" ><font face="Arial"><strong>From Location</strong></font></td>
			<td  align="left" class="style6" ><font face="Arial"><strong>To Location</strong></font></td>
	<td  align="left" class="style6" ><font face="Arial"><strong>Trailer</strong></font></td>
	<td  align="left"  class="style6"><font face="Arial"><strong>Transfer Date</strong></font></td>	
 
</tr>



   <%
 strsql = "SELECT tblCars.* FROM tblCars "_
&" WHERE Date_received >= '" & strBegDate & "' AND (PMO_Nbr='MERCHANTS' or PMO_Nbr = 'DC') and Date_received < '" & strEndDate & "' "_
&" Order by PMO_Nbr, Species"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly

       ii = 0
   
   If not MyRec.eof then
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="#F1F1F8">
    <% else %>
       <tr bgcolor="#FFFFE8">
       
    <% end if %>

<td  align="left" ><font face = "arial" size = "1"><span class="style1">FROM</span>&nbsp;</td>

<td  align="left" ><font face = "arial" size = "1"><span class="style1"><%=MyRec.fields("Species")%></span>&nbsp;</td>
<td  align="left" ><font face = "arial" size = "1"><span class="style1">
<% if len(MyREC("PMO_NBR")) > 1 then %>

<%=MyRec.fields("PMO_Nbr")%>
<% else %>
Mill
<% end if %></span>&nbsp;</td>
<td  align="left"><font face = "arial" size = "1"><span class="style1"><%=MyRec.fields("Location")%></span>&nbsp;</td>

<td  align="left" ><font face = "arial" size = "1"><span class="style1"><%=MyRec.fields("Transfer_Trailer_Nbr")%></span>&nbsp;</td>

<td  align="left" ><font face = "arial" size = "1"><span class="style1"><%=MyRec.fields("Date_received")%></span>&nbsp;</td>
 

</tr>
    <% 
       ii = ii + 1
      
       MyRec.MoveNext
     Wend
     end if
  

   strsql = "SELECT tblCars.* FROM tblCars "_
&" WHERE Date_received>= '" & strBegDate & "' AND (Location ='MERCHANTS' or Location = 'DC WHSE') and Date_received < '" & strEndDate & "' "_
&" Order by Location, Species"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly

       ii = 0
   
   If not MyRec.eof then
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="#F1F1F8">
    <% else %>
       <tr bgcolor="#FFFFE8">
       
    <% end if %>

<td  align="left" ><font face = "arial" size = "1"><span class="style1">TO</span>&nbsp;</td>

<td  align="left" ><font face = "arial" size = "1"><span class="style1"><%=MyRec.fields("Species")%></span>&nbsp;</td>
<td  align="left" ><font face = "arial" size = "1"><span class="style1"><% if len(MyREC("PMO_NBR")) > 1 then %>

<%=MyRec.fields("PMO_Nbr")%>
<% else %>
Mill
<% end if %></span>&nbsp;</td>
<td  align="left"><font face = "arial" size = "1"><span class="style1"><%=MyRec.fields("Location")%></span>&nbsp;</td>

<td  align="left" ><font face = "arial" size = "1"><span class="style1"><%=MyRec.fields("Trailer")%></span>&nbsp;</td>

<td  align="left" ><font face = "arial" size = "1"><span class="style1"><%=MyRec.fields("Date_received")%></span>&nbsp;</td>
<td  align="left" ><font face = "arial" size = "1"><span class="style1"><%=MyRec.fields("Tier_rating")%></span>&nbsp;</td>

</tr>

    <% 
       ii = ii + 1
      
       MyRec.MoveNext
     Wend
     end if
    %>