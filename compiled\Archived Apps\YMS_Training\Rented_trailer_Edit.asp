																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Edit Rented Trailer </TITLE>


<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 



<% Dim MyRec, strsql, MyRec2, strsql2,  MyConn3, strsql3, strid

strid = Request.querystring("id")


  strsql = "Select * from tblRentedTrailer where ID = " & strid
  
      Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


  set objGeneral = new ASP_CLS_General
  if objGeneral.IsSubmit() Then	

  

	
        	
  	
	strsql =  "Update tblRentedTrailer Set Trailer_nbr = '" & request.form("Trailer") & "' where ID = " & strid
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
			if len(request.form("Expiration")) > 3 then
			  	
	strsql =  "Update tblRentedTrailer Set End_Date = '" & request.form("Expiration") & "' where ID = " & strid
	
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

			
			
end if

			
			If request.form("OOS") = "ON" then
				strsql =  "Update tblRentedTrailer Set OOS = 'Y' where ID = " & strid
				else
					strsql =  "Update tblRentedTrailer Set OOS = Null where ID = " & strid
					
	end if

							Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close


Response.redirect("Rented_trailers.asp")		
end if

	
%>

<style type="text/css">
.style3 {
	border: 1px solid #000000;
}
.style5 {
	border: 1px solid #C0C0C0;
	background-color: #FFFFD7;
}
.style6 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: arial;
	font-size: small;
	text-align: center;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style7 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	text-align: center;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
</style>
</head>

<body>
<br>
	
<form name="form1" action="Rented_trailer_Edit.asp?id=<%= strid %>" method="post" ID="Form1">
<TABLE cellSpacing=0 cellPadding=0 width=100% align = center class="style5">

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Modify Trailer Number</b></font></td>
<td align = right><font face="Arial"><a href="Rented_Trailers.asp">Return</a>&nbsp;</td>


</tr>
	    </table>



	<p>&nbsp;</p>
	<table id="table1" style="width: 40%" align="center" class="style3">
<tr bgcolor = #CCCCFF>
		<td bgcolor="#FFFFD7" class="style6" style="height: 67px">Trailer Number</td>
		<td bgcolor="#FFFFD7" class="style6" style="height: 67px">Expiration 
		Date</td>
		<td bgcolor="#FFFFD7" class="style6" style="height: 67px">Out of Service</td>
	</tr>
	<tr>
	
		<td class="style7" style="height: 75px">
		<input type="text" name="Trailer" size="20" style="width: 145px" tabindex="1" value='<%= MyRec("Trailer_nbr") %>' ></td>
	
		<td class="style7" style="height: 75px">
		<input type="text" name="Expiration" size="20" style="width: 145px" tabindex="1" value='<%= MyRec("End_Date") %>' ></td>
	
		<td class="style7" style="height: 75px">
		<input type="checkbox" value="ON" name="OOS" <% if MyREc("OOS") = "Y" then %> checked <% end if %>>	&nbsp;</td>
	</tr>
	
</table>
<br>
		

<p align="center"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></p>
</form>

<!--#include file="Fiberfooter.inc"-->