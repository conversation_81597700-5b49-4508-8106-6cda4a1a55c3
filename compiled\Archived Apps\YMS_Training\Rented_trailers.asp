																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Rented Trailers</TITLE>

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 



<% Dim MyRec, strsql, strstartdate
strStartdate = formatdatetime(now(),2)
    strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "' and User_type = 'A'"


    Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL3, Session("ConnectionString")


If not Myrec2.eof then
strPost = "OK"
else
strPost = ""
end if

strsql = "SELECT * from tblRentedTrailer order by Trailer_nbr"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<style type="text/css">
.style2 {
	border: 1px solid #000000;
}
.style4 {
	text-align: center;
}
.style5 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	text-align: center;
}
</style>
</head>

<body>

<br>
	<% if strPost = "OK" then %>
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>


<td align = left><b><font face="Arial">Rented Trailers</font></b></td>

<td align = right><font face="Arial"><a href="Rented_trailer_add.asp"><b>Add New</b></a>&nbsp;</td>


</tr>
	    </table>
	
	
	<TABLE cellSpacing=0 cellPadding=0 class = "style2" align = center style="width: 30%">  
	 <tr class="tableheader">
<td>&nbsp;</td>

				<td class="style4">     <font face="Arial" size="2">	<strong>Trailer 
				Number</strong></font></td>
		<td class="style5">     <strong>Expiration Date</strong></td>


		<td class="style5">     <strong>Out of Service</strong></td>


		<td align = right>&nbsp;</td>
	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td> <font size="2" face="Arial">

<a href="Rented_trailer_Edit.asp?id=<%= MyRec.fields("ID") %>">Edit</a></td>

	<td class="style4"  ><font size="2" face="Arial"><%= MyRec.fields("Trailer_nbr")%></font></td>
		<td class="style4"  ><font size="2" face="Arial"><%= MyRec.fields("End_Date")%></font></td>

		<td class="style4"  ><font size="2" face="Arial"><%= MyRec.fields("OOS")%></font></td>

<td> <font size="2" face="Arial"><a href="Rented_trailer_Delete.asp?id=<%= MyRec.fields("ID") %>">Delete</a></td>
	
</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>
<% end if %><!--#include file="Fiberfooter.inc"-->