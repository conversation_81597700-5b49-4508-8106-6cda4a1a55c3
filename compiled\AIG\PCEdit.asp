<HTML>
<HEAD>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 6.0">
<TITLE>Program Change Edit</TITLE>
</HEAD>

<!--#include file="classes/asp_cls_DataAccessAIG.asp"-->
<!--#include file="classes/asp_cls_headerAIG.asp"-->
<!--#include file="classes/asp_cls_SessionStringAIG.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
   
<body>
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class="tablecolor1" border=1>
  <tr><td colspan="4" bgcolor="white" class="subheader">&nbsp;</td></tr>

 <tr><TD><p align="left"><font face = arial size="3"><b>Program Change Request</b>&nbsp;&nbsp;</font></td>
<td align = right><b><a href="javascript:history.go(-1)"><b>Return</b></a></b></td></tr>
	    </table><br>
	    <%   
	      Dim SCRIPT_NAME
		Dim strApproval, iRecordID, strReview, strImplement, strRequest
		Dim strSQL, MyConn
		Dim BACK_TO_LIST_TEXT
        	 Dim objEPS
   	 	Dim objGeneral
  		 dim   rstEPS, strToday, MyRec

            
		SCRIPT_NAME = Request.ServerVariables("SCRIPT_NAME")

		BACK_TO_LIST_TEXT = "<p>Click <a href=""" & SCRIPT_NAME & """>" _
    		    & "here</a> to go back to record list.</p>"
	      set objGeneral = new ASP_CLS_General
 	
		Select Case LCase(Trim(Request.QueryString("action")))
		    Case "add"
		    %>
			<form action="<%= SCRIPT_NAME %>?action=addsave" method="post">
	
	   <br><b><font face = arial size = 2>Enter Description of Program Change Request</b>
			    	    <br>
                   	
			    <br> <textarea name="Request" cols="70" rows="4" maxlength="4000" style="padding:0; border:1 solid slategray; bgcolor:#ff6633" ></textarea>
			    <br>
			
		      

	
			<br><br> <br><Input name="Update" type="submit" Value="Submit" >
 			</form>
                    <%
	        

 		    Case "addsave"
			Dim strURequest

			strURequest = Replace(Request.Form("Request"), "'", "''") 
			strToday = FormatDateTime(Now(),2)
			
			strSQL = "Insert into tblProgramChange (Request, Requestor, Request_Date) Values('" & strURequest & "', " _
			   &  "'" & Session("EName") & "', '" & strToday & "')"
		
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

			Response.redirect "ProgramChange.asp"			

			  Case "edit"
			
			iRecordId = Request.QueryString("id")
			strSQL = "Select * From tblProgramChange Where ID = " & iRecordId

			Set MyRec = Server.CreateObject("ADODB.RecordSet")
			MyRec.Open strSQL, Session("ConnectionString")

			If Not MyRec.EOF Then
			If len(MyRec.fields("Approval")) > 1 then
			strApproval = "Yes"
			else
			strApproval = "No"
			End if 
			strRequest = MyRec.fields("Request")
			End if
			MyRec.close
			   %>
				<form action="<%= SCRIPT_NAME %>?action=editsave" method="post">
				    <input type="hidden" name="id" value="<%= iRecordID %>" />
'
	 			   <br><b><font face = arial size = 2>Program Request:</b>
			    	    <br>
                         <%= strRequest %>  <br>
		         <br><b><font face = arial size = 2>Approval to Start:</b>&nbsp;&nbsp;

			<select name="Approval" >
			<option value="No" <% if strApproval = "No" then %> Selected<% end if %>>No</option>
			<option value="Yes" <% if strApproval = "Yes" then %> Selected<% end if %>>Yes</option>
			</select></td> <br><br>		
			    	   
			<br><Input name="Update" type="submit" Value="Update Record" >
			</form><br>
   			
		
   			
			    <% 		

		    Case "editsave"
			strToday = FormatDateTime(Now(),2)
			iRecordId = Clng(Request.form("id"))
			If Request.form("Approval") = "Yes" then
		
			strSQL = "Update tblProgramChange Set Approval = '" & Session("Ename") & "', Approval_Date = '" & strToday & "' Where ID = " & iRecordId

			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			End if
			Response.redirect "ProgramChange.asp"

			Case "delete"
	       
			iRecordId = Clng(Request.QueryString("id"))
			

			strSQL = "DELETE FROM tblProgramChange WHERE ID = " & iRecordId 
'
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

			Response.redirect "ProgramChange.asp"

	
			End Select %>
			
   	
                             
      	</body>

<!--#include file="AIGfooter.inc"-->