
<html xmlns:m="http://schemas.microsoft.com/office/2004/12/omml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">


<TITLE>Select Release Number</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->

<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

 <%    Dim objDAC, strSQL, MyRec, objMOC, rstFiber, strRelease, strR, strID, rstFiber2
	Dim gSpecies, gSap_Nbr, gVendor, gPO, gRelease, gOID, gGenerator, gCity, gState
       Dim objGeneral, strDate, MyConn, strduplicate, strSTO
strDate = formatdatetime(Now(),2)
	
	
       set objGeneral = new ASP_CLS_General
  Call getData()   
if objGeneral.IsSubmit() Then
'call SendEmail()
If len(request.form("STO")) > 1 then 
strSTO = request.form("STO")
Response.redirect("Sto_Receipt_Auto.asp?id=" & strSTO)
else
strR = Request.form("Release")

strsql = "SELECT Release from tblDuplicates"
    Set MyRec = Server.CreateObject("ADODB.Recordset") 

    MyRec.Open strSQL, Session("ConnectionString")
    While not MyRec.eof

 If ucase(MyRec.fields("Release")) = ucase(strR)  then
  strduplicate = "YES"
  end if
  
  MyRec.movenext
  wend
  
  If strduplicate = "YES" then
 Response.redirect("Generic_Receipt.asp?r=" & strR)
 else



	Response.redirect("EnterBrokeReceipt.asp?id=" & strR)
	end if

end if
End if
%>
<SCRIPT LANGUAGE="JavaScript">
function formHandler(Form1){
document.forms["Form1"].submit();
}
</SCRIPT>

<style type="text/css">
.style1 {
	border-width: 1px;
	background-color: #E6ECFF;
}
.style3 {
	font-family: Arial, Helvetica, sans-serif;
	text-align: center;
}
.style4 {
	text-align: left;
}
.style5 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
	background-color: #EBEBF5;
}
.style6 {
	font-family: Arial, Helvetica, sans-serif;
	text-align: left;
}
</style>
</head>

<body>
<form name="form1" action="SelectBROKERelease.asp" method="post" >

<p class="style3"><strong>ENTER RECEIPT OF BROKE</strong></p>
<p class="style3">&nbsp;</p>
<table cellpadding="0" class = "style5" cellspacing="0" style="width: 80%;" bordercolor="#111111" id="AutoNumber2" align = center>

       <TD align = center class="style1">  <p class="style6">Select one of the 
		following 4 choices:</p> 
		<div class="style4">
			<font face="Arial" size="3"><b>INTERNAL PURCHASE FROM KC LOCATION<br>
			<br>
&nbsp;&nbsp;&nbsp; &nbsp;1)&nbsp;Internal STO Receipt from 
			other KC Location&nbsp;
     <font face="Arial">&nbsp;
     <select name="STO">
 	<option value="" selected>Load Number</option>
        				<% 	Do While Not rstFiber2.EOF
					%>
            			
						<option VALUE="<%= rstFiber2.fields("Load_nbr")%> ">
                				<%=rstFiber2.fields("Load_nbr")%></option>

					<% 
        					rstFiber2.MoveNext 
        					Loop         				
        					rstFiber2.close
					%>

     </select>&nbsp;&nbsp; <font size="2"> <strong> 
			<input type="submit" value="Continue" id=submit2 name=submit2></strong></font></font><br>
			<br>
&nbsp;&nbsp;&nbsp;&nbsp; 2)&nbsp;<a href="Sto_receipt.asp">Internal STO Receipt from 
			other KC Location</a>&nbsp; (Not choice in above dropdown)<br>
			<br>
			EXTERNAL PURCHASE FROM VENDOR<br>
			<br>
			&nbsp;
			&nbsp; 3) External purchase with clearly identifiable valid release #:&nbsp;&nbsp;</b></font>
	&nbsp;&nbsp;&nbsp;
     <font face="Arial">
     <select name="Release">
 	<option value="" selected>Release -- PO Nbr</option>
        				<% 	Do While Not rstFiber.EOF
					%>
            			
						<option VALUE="<%= rstFiber.fields("Release")%> ">
                				<%=rstFiber.fields("PRDisplay")%></option>

					<% 
        					rstFiber.MoveNext 
        					Loop         				
        					rstFiber.close
					%>

     </select><font size="2">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <strong> <input type="submit" value="Continue" id=submit1 name=submit1></strong><br>
			<br>
			<font face="Arial" size="3"><b>&nbsp; &nbsp; 4) <a href="Broke_Receipt.asp">External purchase with 
			unidentifiable or invalid release #</a></b></font></font><br>
			<br>
			<br>
		</div>

</TD></tr>

		<tr>

       <TD align = center class="style1">  &nbsp;</TD></tr>

</table>
</form>






<%

 Function GetData()
      Set rstFiber = Server.CreateObject("ADODB.Recordset")
strsql2 = "Select tblCars.Trailer, tblOrder.Grade, tblOrder.Species, tblOrder.Release, tblOrder.Release+' - '+tblOrder.PO AS PRDisplay "_
&" FROM tblCars RIGHT JOIN tblOrder ON tblCars.OID = tblOrder.OID "_
&" WHERE (tblOrder.Import_month='MAR 14' or tblOrder.import_month = 'FEB 14') AND tblCars.Trailer Is Null AND tblOrder.Grade='BROKE'"_
&" ORDER BY tblOrder.Release"
     
    rstFiber.Open strSQL2, Session("ConnectionString"), adOpenDynamic
        Set rstFiber2 = Server.CreateObject("ADODB.Recordset")

    Set rstFiber2 = Server.CreateObject("ADODB.Recordset")

strsql3 = "Select Load_nbr FROM tblInbound INNER JOIN tblBrokeSAP ON tblInbound.Status = tblBrokeSAP.SAP "_
&" WHERE tblBrokeSAP.Category= 'BROKE'  and len(Broke_Description) > 0 ORDER BY Load_nbr"
     
    rstFiber2.Open strSQL3, Session("ConnectionString"), adOpenDynamic

    End Function  %><!--#include file="Fiberfooter.inc"-->