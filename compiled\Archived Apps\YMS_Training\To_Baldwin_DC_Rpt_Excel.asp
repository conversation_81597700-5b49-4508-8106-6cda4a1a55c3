 

<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Load Activity TO Warehouse</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"--> 
<!--#include file="classes/asp_cls_General.asp"-->


<%  
strBegDate = request.querystring("b")
strEndDate = request.querystring("e")
strSpecies = request.querystring("s")
strLocation = request.querystring("l")
if strLocation = "MERCHANTS" then
strLocation = "ME"
end if
strsql2 = "SELECT cid, PO, location, Transfer_date, trans_unload_date, PMO_Nbr, tblcars.Pnbr, tblcars.species, "_
&" Inv_depletion_date, transfer_trailer_nbr, tblCars.release_nbr, tblCars.Vendor, tblCars.Trailer, tblCars.Generator, tblCars.Date_Received, "_
&"  tblCars.Date_unloaded, tblCars.Bales_RF,  tblcars.other_comments, tblCars.Tons_received, tblCars.Deduction, tblCars.Net from tblCars "_
 &"  where  Date_received >= '" & strBegDate & "' and Date_received <= '" & strEndDate & "'"

if len(strLocation) > 0 then
strsql2 = strsql2 & " and left(location,2) = '" & strlocation & "'"
end if

if len(strSpecies) > 1 then
strsql2 = strsql2 & " and Species = '" & strspecies & "'"
end if
 Set rstEquip = Server.CreateObject("ADODB.Recordset")

   rstEquip.Open strSQL2, Session("ConnectionString"), adOpenDynamic
'Response.write("strsql:" & strsql2)
 Response.ContentType = "application/vnd.ms-excel"	
 %>


<style type="text/css">
.style1 {
	font-size: x-small;
}
.style2 {
	font-family: Arial;
	font-weight: bold;
}
</style>
</head>

<table>
      <tr class="tableheader">	
      <td  align="center" >
		<p align="left" class="style1"><font face="Arial">Species</font></td>

		<td  align="center" class="style1" ><font face="Arial">Receipt #</font></td>
		<td  align="center" class="style1" ><font face="Arial">Vendor</font></td>
<td  align="center" class="style1" ><font face="Arial">Generator</font></td>
	<td  align="center" class="style1" ><font face="Arial">Trailer</font></td>
	<td  align="center" >
	<p align="left" class="style1"><font face="Arial">Date Received</font></td>
	<td  align="center" class="style1" ><font face="Arial">Date Unloaded</font></td>

	<td  align="center" class="style1" ><font face="Arial">Location</font></td>

	<td  align="center" class="style1" ><font face="Arial">Net</font></td>


	<td  align="center" class="style1" ><font  face="Arial">Other</font></td>

      
  	<%  
      Dim ii
       ii = 0
       while not rstEquip.Eof
      
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr class=tablecolor1>
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    


<td  align="left" ><font face = "arial" size = "1"><span class="style1"><%=rstEquip.fields("Species")%></span>&nbsp;</td>
<td  align="left" ><font face = "arial" size = "1"><span class="style1"><%=rstEquip.fields("CID")%></span>&nbsp;</td>
<td  align="center" ><font face = "arial" size = "1"><span class="style1"><%=rstEquip.fields("Vendor")%></span>&nbsp;</td>
<td  align="center" ><font face = "arial" size = "1"><span class="style1"><%=rstEquip.fields("Generator")%></span>&nbsp;</td>

<td  align="center" ><font face = "arial" size = "1"><span class="style1"><%=rstEquip.fields("Trailer")%></span>&nbsp;</td>

<td  align="left" ><font face = "arial" size = "1"><span class="style1"><%=rstEquip.fields("Date_received")%></span>&nbsp;</td>
<td  align="center" ><font face = "arial" size = "1"><span class="style1"><%=rstEquip.fields("Date_unloaded")%></span>&nbsp;</td>

<td  align="center" ><font face = "arial" size = "1"><span class="style1"><%=rstEquip.fields("Location")%></span>&nbsp;</td>
<% if not isnull(rstEquip.fields("Net")) then %>
<td  align="center" ><font face = "arial" size = "1"><span class="style1"><%= formatnumber(rstEquip.fields("net"),3)%></span>&nbsp;</td>
<% else %>
<td  align="center" class="style1" ><font face = "arial">&nbsp;</td>
<% end if %>



<td  align="center" ><font face = "arial" size = "1"><span class="style1"><%= rstEquip.fields("Other_Comments")%></span>&nbsp;</td>



 </tr>
    <% 
       ii = ii + 1
      
       rstEquip.MoveNext
     Wend
    %>
   </table>
