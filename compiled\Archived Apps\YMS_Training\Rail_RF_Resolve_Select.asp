<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">


<TITLE>Select RF Fiber Inbound</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

 <%    Dim objDAC, strSQL, MyRec, objMOC, rstFiber, strTrailer, strR, strID, strcid
       Dim objGeneral, strDate, MyConn
       
strcid = request.querystring("id")	
	
  set objGeneral = new ASP_CLS_General
  Call getData()   
if objGeneral.IsSubmit() Then

strR = request.form("Release")
strcid = request.querystring("c")


	Response.redirect("EnterRail.asp?id=" & strR & "&c=" & strcid)
	


End if
%>
<SCRIPT LANGUAGE="JavaScript">
function formHandler(Form1){
document.forms["Form1"].submit();
}
</SCRIPT>

<body>
<form name="form1" action="Rail_RF_Resolve_Select.asp?c=<%= strcid%>" method="post" >

<p>&nbsp;</p>
<table border="1" cellpadding="0"  cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="75%" id="AutoNumber2" align = center>

       <TD align = center bgcolor="#FFFFE8">  <p>&nbsp;</p> <font face="Arial" size="3"><b>
		Select Inbound RF Trailer/Car </b></font>&nbsp;&nbsp;<br><br>
     <font face="Arial">
     	<font size="2"> &nbsp;</font><select name="Release">
 	<option value="" selected>-----Select-----</option>
 	<%
 	       Set rstFiber = Server.CreateObject("ADODB.Recordset")
strsql2 = "SELECT VID, Release, Trailer, Trailer + '-' + Species AS TS, Vendor "_
&" FROM tblVirginFiber WHERE (((tblVirginFiber.Trailer) Is Not Null) AND ((tblVirginFiber.[status])='Inbound') AND ((tblVirginFiber.Vendor)='Corporate')) "_
&" ORDER BY tblVirginFiber.Trailer"
     
    rstFiber.Open strSQL2, Session("ConnectionString"), adOpenDynamic 
    While not rstFiber.eof
    %>
    <option value="<%= rstfiber.fields("Release")%>"><%= rstfiber.fields("TS") %></option>

<% rstfiber.movenext
wend%>
     </select><br>



		<p>&nbsp;</p> 
<p align="center"><input type="submit" value="Continue" id=submit1 name=submit1><br>&nbsp;</TD>

       </tr>

</table>
</form>


<p align="center">
     &nbsp;</p>



<%

 Function GetData()
       
        
              Set rstFiber = Server.CreateObject("ADODB.Recordset")
strsql2 = "SELECT tblVirginFiber.VID, tblVirginFiber.Trailer, Trailer + '-' + Species AS TS, tblVirginFiber.Vendor "_
&" FROM tblVirginFiber WHERE (((tblVirginFiber.Trailer) Is Not Null) AND ((tblVirginFiber.[status])='Inbound') AND ((tblVirginFiber.Vendor)='Corporate')) "_
&" ORDER BY tblVirginFiber.Trailer"
     
    rstFiber.Open strSQL2, Session("ConnectionString"), adOpenDynamic

         End Function  
%><!--#include file="Fiberfooter.inc"-->