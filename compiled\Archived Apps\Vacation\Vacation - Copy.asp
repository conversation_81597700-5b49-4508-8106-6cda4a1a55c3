﻿<html xmlns:m="http://schemas.microsoft.com/office/2004/12/omml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<!--#include file="classes/asp_cls_sessionPLI.asp"--> 
<!--#include file="classes/ASP_CLS_General.asp"--> 
<!--#include file="classes/asp_cls_header.asp"-->

<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Vacation</title>

<%If Month(Now()) = 11 Then
     Response.Write "<br><a href=""Vacation.asp?Year=" & Year(Now()) + 1 & """><font face=arial>Click here for Year " & Year(Now()) + 1 & "</a></font>"
  End If
%>

<% 
 Session("EmployeeID") = "U01303"
 'Session("EmployeeID") = "B87445"
 'Session("EmployeeID") = "B51564"
 'Session("EmployeeID") = "B55148"
 'Session("EmployeeID") = "B23977"
 set objGeneral = new ASP_CLS_General

 if Request("btnSubmitRequest") = "Submit Request(s)" Then
	strsql = "Delete From tblRequestedTimeOff where TechnicianID IN (SELECT ID FROM tblTechnician where KCID = '" & Session("EmployeeID") & "') AND TimeOffDate Between '" & Request("selStartDate") & "' AND '" & DateAdd("d",13,Request("selStartDate")) & "' AND Approved IS NULL"
	'Response.Write strsql & "<BR>"
	Set MyInsert = Server.CreateObject("ADODB.Connection")
	MyInsert.Open Session("ConnectionPolaris")
	MyInsert.Execute strSQL
	MyInsert.Close 
	Set MyInsert = Nothing
    For Each Item IN Request.Form
        If Left(Item,10) = "cbxRequest" Then
           ary = Split(Request.Form(Item),"|")
		   strsql = "Insert INTO tblRequestedTimeOff (TechnicianID,TimeoffDate,VacAreaID,TimeOffType) Values (" & ary(0) & ",'" & ary(1) & "'," & ary(2) & ",'" & ary(3) & "')"
		   'Response.Write strsql & "<BR>"
		   Set MyInsert = Server.CreateObject("ADODB.Connection")
		   MyInsert.Open Session("ConnectionPolaris")
		   MyInsert.Execute strSQL
		   MyInsert.Close 
		   Set MyInsert = Nothing
		End If
    Next
 end if


   
   %>
   <style type="text/css">
.style5 {
	border-width: 1px;
	background-color: #FFFFFF;
	text-align: right;
	font-family: Calibri;
	font-size: medium;
}
.style6 {
	font-family: Arial;
	font-size: xx-small;
}

.style26{
	font-family: Arial;
	font-size: xx-small;
	background-color: #000000;
	color: #FFFFFF;
}

.style27{
	font-family: Arial;
	font-size: xx-small;
	background-color: #FFFFFF;
	color: #000000
}


.style7 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: xx-small;
	background-color: #EAF1FF;
}
.style9 {
	border-width: 1px;
	background-color: #FFFFFF;
	text-align: center;
}
.style10 {
	font-family: Calibri;
	font-size: x-small;
}
.style11 {
	border-width: 1px;
	text-align: left;
	font-family: Calibri;
	font-size: x-small;
	background-color: #EAF1FF;
}
.style12 {
	font-family: Calibri;
}
.style13 {
	font-size: x-small;
}
.style16 {
	text-align: left;
}
.style17 {
	border: 1px solid #808080;
}
.style28 {
	background-color: #EAF1FF;
}
.style29 {
	font-family: Calibri;
	font-size: x-small;
	background-color: #EAF1FF;
}
.style30 {
	font-family: Arial;
	font-size: small;
	background-color: #EAF1FF;
}
.style31 {
	font-family: Arial, Helvetica, sans-serif;
}
.style33 {
	color: #FFFFFF;
}
.style34 {
	border-color: #000000;
	border-width: 1px;
	color: #FFFFFF;
}
.style35 {
	border-color: #000000;
	border-width: 1px;
	font-family: Arial, Helvetica, sans-serif;
	text-align: center;
}

div.wrapper {
    overflow:hidden;
    overflow-y: scroll;
    height: 600px; // change this to desired height
}
</style>


<%for i = 0 to 13%>
<script language="javascript">
function chbx<%=i%>(obj){
var that = obj;
if (document.getElementById(that.id).checked == true){
   document.getElementById('cbxRequestWks<%=i%>').checked = false;
   document.getElementById('cbxRequestPH<%=i%>').checked = false;
   document.getElementById('cbxRequestVD<%=i%>').checked = false;
   document.getElementById(that.id).checked = true;
   }
}
</script>
<%next%>

<form name="form1" action="Vacation.asp" method="post">
<input type="hidden" name="Year" value="<%=Request("Year")%>">

<table>
<tr class="style35">
<td align="left">
   Name:&nbsp;&nbsp;</td><td>
  <% 
    
    strsql = "SELECT Top 1 KCID, Technician, WSR from tblTechnician where KCID = '" & Session("EmployeeID") & "'"
    Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionPolaris")
    If not MyConn.eof Then 
       Response.Write MyConn("Technician") & "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Shift: " & MyConn("WSR") & ""
    Else
       Response.Write "Your name was not found."
	   Response.End
    End If
    MyConn.close %>
	 </td>
	 </tr>
<tr class="style35">
<td align="left">
   Start Date:&nbsp:&nbsp;
   </td>
   <td align="left">

<%
  If Len(Request("Year")) > 0 Then 'AND CInt(Request("Year")) <> Year(Now()) Then
     intWeekDay = WeekDay("1/1/" & Request("Year"))
     StartDate = DateAdd("d",1 - intWeekDay,"1/1/" & Request("Year"))
	 EndYear = CInt(Request("Year")) + 1

  Else
     intWeekDay = WeekDay(Now())
     StartDate = DateAdd("d",1 - intWeekDay,Now())
	 EndYear = Year(Now()) + 1
  End If
  %>
  <select name="selStartDate" size="1" tabindex="1" style="height: 22px">
<%
  For i = 0 To 53
     If Year(DateAdd("ww",i,StartDate)) >= EndYear Then
	    Exit For
     End If%>

<option <%If Request("selStartDate") = FormatDateTime(DateAdd("ww",i,StartDate),2) Then %> selected <%End If%>   value="<%=FormatDateTime(DateAdd("ww",i,StartDate),2)%>"><%=FormatDateTime(DateAdd("ww",i,StartDate),2)%></option>


<%    Next %>
</select>
	 </td>
</td>
</tr>
</table>
<BR>
<input type="submit" name="btnSubmit" value="Submit">
</span>
<BR><BR>

<%If Request("btnSubmit") = "Submit" OR Request("btnSubmitRequest") = "Submit Request(s)" Then
     Dim aryActualTotal(99)
     strsql = "SELECT * from tblTechnician WHERE KCID = '" & Session("EmployeeID") & "'"
     Set MyConn = Server.CreateObject("ADODB.Recordset")
     MyConn.Open strSQL, Session("ConnectionPolaris")
	 If NOT MyConn.EOF Then
	    strID = MyConn("ID")
	    strKCID = MyConn("KCID")
		strTechnician = MyConn("Technician")
        strArea = MyConn("Area")
        strWSR = MyConn("WSR")
		strTeamLeader = MyConn("TeamLeader")
		strsql = "select ID, Area, IsNull(MaxPerShift,0) as 'MaxOffPerDay' from tblArea " _
				& " where Area = '" & strArea & "'"
		'Response.Write strsql & "<BR>"
		Set MyConn2 = Server.CreateObject("ADODB.Recordset")
        MyConn2.Open strSQL, Session("ConnectionPolaris")
        If not MyConn2.eof Then
           strVacArea = MyConn2("Area")
		   intMaxOffPerDay = MyConn2("MaxOffPerDay")
		   intVacAreaID = MyConn2("ID")
		End If
		MyConn2.Close
		Set MyConn2 = Nothing
     End If
	 MyConn.Close
	 Set MyConn = Nothing%>
<BR>
<table>
<tr class="style35"><td align="left">Vacation Area:&nbsp;&nbsp;</td><td align="left"><%=strArea%></td></tr>
<tr class="style35"><td align="left">Maximum Allowed off/shift:&nbsp;&nbsp;</td><td align="left"><%=intMaxOffPerDay%></td></tr>
</table>
<br>
<table border="1">
<tr class="style35"><td>&nbsp;</td>
<%
  dtStartDate = Request("selStartDate")
  For i = 0 To 13
     dtCurrentDate = dateAdd("d",i,dtStartDate)
	 Response.Write "<td align=""center"">" & WeekDayName(WeekDay(dtCurrentDate)) & "</td>"

  Next
%>
</tr>
<tr class="style35"><td>&nbsp;</td>
<%
  dtStartDate = CDate(Request("selStartDate"))
  For i = 0 To 13
     dtCurrentDate = dateAdd("d",i,dtStartDate)
	 Response.Write "<td>" & FormatDateTime(dtCurrentDate,2) & "</td>"

  Next
%>
</tr>
<tr class="style35"><td>WKS</td>
<%Dim aryTechnicianScheduled(99)
  FOR i = 0 To 98
     aryTechnicianScheduled(i) = 0
  Next
  dtStartDate = Request("selStartDate")
  For i = 0 To 13
     dtCurrentDate = dateAdd("d",i,dtStartDate)

     strsql = "SELECT count(*) as 'ActualTotal', IsNULL(SUM(CASE TechnicianID WHEN " & strID & " Then 1 Else 0 END),0) as 'TechnicianScheduled'   FroM tblRequestedTimeOff where DateDIFF(d, TimeOffDate, '" & dtCurrentDate & "') = 0 AND VacAreaID = " & intVacAreaID & " AND TimeOffType = 'Wks'"
	 'Response.write strsql
	 Set MyConn2 = Server.CreateObject("ADODB.Recordset")
     MyConn2.Open strSQL, Session("ConnectionPolaris")
     If not MyConn2.eof Then
        aryActualTotal(i) = MyConn2("ActualTotal")
		aryTechnicianScheduled(i) = MyConn2("TechnicianScheduled")
	End If
	MyConn2.Close
	Set MyConn2 = Nothing

	 Response.Write "<td align=""center"">" & aryActualTotal(i) & "</td>"

  Next
%>
</tr>
<tr class="style35"><td>PH</td>
<%
  FOR i = 0 To 98
     aryTechnicianScheduled(i) = 0
  Next
  dtStartDate = Request("selStartDate")
  For i = 0 To 13
     dtCurrentDate = dateAdd("d",i,dtStartDate)

     strsql = "SELECT count(*) as 'ActualTotal', IsNULL(SUM(CASE TechnicianID WHEN " & strID & " Then 1 Else 0 END),0) as 'TechnicianScheduled'   FroM tblRequestedTimeOff where DateDIFF(d, TimeOffDate, '" & dtCurrentDate & "') = 0 AND VacAreaID = " & intVacAreaID & " AND TimeOffType = 'PH'"
	 'Response.write strsql
	 Set MyConn2 = Server.CreateObject("ADODB.Recordset")
     MyConn2.Open strSQL, Session("ConnectionPolaris")
     If not MyConn2.eof Then
        aryActualTotal(i) = MyConn2("ActualTotal")
		aryTechnicianScheduled(i) = MyConn2("TechnicianScheduled")
	End If
	MyConn2.Close
	Set MyConn2 = Nothing

	 Response.Write "<td align=""center"">" & aryActualTotal(i) & "</td>"

  Next
%>
</tr>

<tr class="style35"><td>VD</td>
<%
  FOR i = 0 To 98
     aryTechnicianScheduled(i) = 0
  Next
  dtStartDate = Request("selStartDate")
  For i = 0 To 13
     dtCurrentDate = dateAdd("d",i,dtStartDate)

     strsql = "SELECT count(*) as 'ActualTotal', IsNULL(SUM(CASE TechnicianID WHEN " & strID & " Then 1 Else 0 END),0) as 'TechnicianScheduled'   FroM tblRequestedTimeOff where DateDIFF(d, TimeOffDate, '" & dtCurrentDate & "') = 0 AND VacAreaID = " & intVacAreaID & " AND TimeOffType = 'VD'"
	 'Response.write strsql
	 Set MyConn2 = Server.CreateObject("ADODB.Recordset")
     MyConn2.Open strSQL, Session("ConnectionPolaris")
     If not MyConn2.eof Then
        aryActualTotal(i) = MyConn2("ActualTotal")
		aryTechnicianScheduled(i) = MyConn2("TechnicianScheduled")
	End If
	MyConn2.Close
	Set MyConn2 = Nothing

	 Response.Write "<td align=""center"">" & aryActualTotal(i) & "</td>"

  Next
%>
</tr>

<tr class="style35"><td>Requested</td>
<%
  Dim aryRequestedTotal(99)
  FOR i = 0 To 98
     aryRequestedTotal(i) = 0
  Next
  dtStartDate = Request("selStartDate")
  For i = 0 To 13
     dtCurrentDate = dateAdd("d",i,dtStartDate)
     strsql = "SELECT count(*) as 'RequestedTotal', IsNULL(SUM(CASE TechnicianID WHEN " & strID & " Then 1 Else 0 END),0) as 'TechnicianScheduled'   FroM tblRequestedTimeOff where DateDIFF(d, TimeOffDate, '" & dtCurrentDate & "') = 0 AND VacAreaID = " & intVacAreaID & " AND Approved IS NULL "
	 'Response.write strsql
	 Set MyConn2 = Server.CreateObject("ADODB.Recordset")
     MyConn2.Open strSQL, Session("ConnectionPolaris")
     If not MyConn2.eof Then
        aryRequestedTotal(i) = MyConn2("RequestedTotal")
	End If
	MyConn2.Close
	Set MyConn2 = Nothing

	 Response.Write "<td align=""center"">" & aryRequestedTotal(i) & "</td>"

  Next
%>
</tr>


<tr class="style35"><td>Pending</td>
<%
  FOR i = 0 To 98
     aryTechnicianScheduled(i) = 0
  Next
  dtStartDate = Request("selStartDate")
  For i = 0 To 13
     dtCurrentDate = dateAdd("d",i,dtStartDate)

     strsql = "SELECT count(*) as 'ActualTotal', IsNULL(SUM(CASE TechnicianID WHEN " & strID & " Then 1 Else 0 END),0) as 'TechnicianScheduled'   FroM tblRequestedTimeOff where DateDIFF(d, TimeOffDate, '" & dtCurrentDate & "') = 0 AND VacAreaID = " & intVacAreaID & " AND Approved = 1 "
	 'Response.write strsql
	 Set MyConn2 = Server.CreateObject("ADODB.Recordset")
     MyConn2.Open strSQL, Session("ConnectionPolaris")
     If not MyConn2.eof Then
        aryActualTotal(i) = MyConn2("ActualTotal")
		aryTechnicianScheduled(i) = MyConn2("TechnicianScheduled")
	End If
	MyConn2.Close
	Set MyConn2 = Nothing

	 Response.Write "<td align=""center"">" & aryActualTotal(i) & "</td>"

  Next
%>
</tr>

<tr class="style35"><td>Available</td>
<%
  dtStartDate = Request("selStartDate")
  For i = 0 To 13
      Response.Write "<td align=""center"">" & intMaxOffPerDay - aryActualTotal(i) - aryRequestedTotal(i) & "</td>"
  Next
%>
</tr>
<%
  Dim aryTimeOffType(99) 
  For i = 0 To 13
     dtCurrentDate = dateAdd("d",i,dtStartDate)

     strsql = "SELECT IsNull(TimeOffType,'') as 'TimeOffType' From tblRequestedTimeOff where DateDIFF(d, TimeOffDate, '" & dtCurrentDate & "') = 0 AND VacAreaID = " & intVacAreaID & " AND TechnicianID = " & strID
	 'Response.write strsql
	 Set MyConn2 = Server.CreateObject("ADODB.Recordset")
     MyConn2.Open strSQL, Session("ConnectionPolaris")
	 aryTimeOffType(i) = ""
     If not MyConn2.eof Then
        aryTimeOffType(i) = MyConn2("TimeOffType")
	End If
	MyConn2.Close
	Set MyConn2 = Nothing
  Next
  %>
<tr class="style35"><td>Check To Request WKS</td>
<%
  dtStartDate = Request("selStartDate")
  For i = 0 To 13
     dtCurrentDate = dateAdd("d",i,dtStartDate)
     strsql = "SELECT IsNull(TimeOffType,'') as 'TimeOffType' From tblRequestedTimeOff where DateDIFF(d, TimeOffDate, '" & dtCurrentDate & "') = 0 AND VacAreaID = " & intVacAreaID & " AND TechnicianID = " & strID & " AND TimeOffType = 'Wks' AND (Approved = 1 OR Approved IS NULL)"
	 'Response.write strsql
	 Set MyConn2 = Server.CreateObject("ADODB.Recordset")
     MyConn2.Open strSQL, Session("ConnectionPolaris")
     If not MyConn2.eof Then
        checked = " checked "
	 Else
	    checked = ""
	End If
	MyConn2.Close
	Set MyConn2 = Nothing
	 Response.Write "<td align=""center"">"
	 If intMaxOffPerDay > (aryActualTotal(i) + aryRequestedTotal(i)) And aryTechnicianScheduled(i) = 0 Then
	    Response.Write "<input type=""checkbox"" id=""cbxRequestWks" & i & """ name=""cbxRequestWks" & i & """ value=""" & strID & "|" & dtCurrentDate & "|" & intVacAreaID & "|Wks"" onclick=""chbx" & i & "(this)""" & checked & ">" 
     ElseIf intMaxOffPerDay > (aryActualTotal(i) + aryRequestedTotal(i)) And aryTechnicianScheduled(i) > 0 Then
	    Response.Write "<input type=""checkbox"" id=""cbxRequestWks" & i & """ name=""cbxRequestWks" & i & """ value=""" & strID & "|" & dtCurrentDate & "|" & intVacAreaID & "|Wks"" onclick=""chbx" & i & "(this)""" & checked & " disabled>"	 
	 Else
	    Response.Write "&nbsp;N/A&nbsp;"
	 End If	 
	 Response.Write "</td>"

  Next
%>
</tr>
<tr class="style35"><td>Check To Request PH</td>
<%
  dtStartDate = Request("selStartDate")
  For i = 0 To 13
     dtCurrentDate = dateAdd("d",i,dtStartDate)
	  strsql = "SELECT IsNull(TimeOffType,'') as 'TimeOffType' From tblRequestedTimeOff where DateDIFF(d, TimeOffDate, '" & dtCurrentDate & "') = 0 AND VacAreaID = " & intVacAreaID & " AND TechnicianID = " & strID & " AND TimeOffType = 'PH' AND (Approved = 1 OR Approved IS NULL)"
	 'Response.write strsql
	 Set MyConn2 = Server.CreateObject("ADODB.Recordset")
     MyConn2.Open strSQL, Session("ConnectionPolaris")
     If not MyConn2.eof Then
        checked = " checked "
	 Else
	    checked = ""
	End If
	MyConn2.Close
	Set MyConn2 = Nothing  
	 Response.Write "<td align=""center"">"
	 If intMaxOffPerDay > (aryActualTotal(i) + aryRequestedTotal(i)) And aryTechnicianScheduled(i) = 0 Then
	    Response.Write "<input type=""checkbox"" id=""cbxRequestPH" & i & """ name=""cbxRequestPH" & i & """ value=""" & strID & "|" & dtCurrentDate & "|" & intVacAreaID & "|PH"" onclick=""chbx" & i & "(this)""" & checked & ">"
	 ElseIf intMaxOffPerDay > (aryActualTotal(i) + aryRequestedTotal(i)) And aryTechnicianScheduled(i) = 1 Then
	    Response.Write "<input type=""checkbox"" id=""cbxRequestPH" & i & """ name=""cbxRequestPH" & i & """ value=""" & strID & "|" & dtCurrentDate & "|" & intVacAreaID & "|PH"" onclick=""chbx" & i & "(this)""" & checked & " disabled>"
	 Else
	    Response.Write "&nbsp;N/A&nbsp;"
	 End If	 
	 Response.Write "</td>"

  Next
%>
</tr>
<tr class="style35"><td>Check To Request VD</td>
<%
  dtStartDate = Request("selStartDate")
  For i = 0 To 13
     dtCurrentDate = dateAdd("d",i,dtStartDate)
     strsql = "SELECT IsNull(TimeOffType,'') as 'TimeOffType' From tblRequestedTimeOff where DateDIFF(d, TimeOffDate, '" & dtCurrentDate & "') = 0 AND VacAreaID = " & intVacAreaID & " AND TechnicianID = " & strID & " AND TimeOffType = 'VD' AND (Approved = 1 OR Approved IS NULL)"
	 'Response.write strsql
	 Set MyConn2 = Server.CreateObject("ADODB.Recordset")
     MyConn2.Open strSQL, Session("ConnectionPolaris")
     If not MyConn2.eof Then
        checked = " checked "
	 Else
	    checked = ""
	End If
	MyConn2.Close
	Set MyConn2 = Nothing
	 Response.Write "<td align=""center"">"
	 If intMaxOffPerDay > (aryActualTotal(i) + aryRequestedTotal(i)) And aryTechnicianScheduled(i) = 0 Then
	    Response.Write "<input type=""checkbox"" id=""cbxRequestVD" & i & """ name=""cbxRequestVD" & i & """ value=""" & strID & "|" & dtCurrentDate & "|" & intVacAreaID & "|VD"" onclick=""chbx" & i & "(this)""" & checked & ">"
	 ElseIf intMaxOffPerDay > (aryActualTotal(i) + aryRequestedTotal(i)) And aryTechnicianScheduled(i) > 0 Then
	    Response.Write "<input type=""checkbox"" id=""cbxRequestVD" & i & """ name=""cbxRequestVD" & i & """ value=""" & strID & "|" & dtCurrentDate & "|" & intVacAreaID & "|VD"" onclick=""chbx" & i & "(this)""" & checked & " disabled>"
	 Else
	    Response.Write "&nbsp;N/A&nbsp;"
	 End If	 
	 Response.Write "</td>"

  Next
%>
</tr>
</table>
<input type="submit" name="btnSubmitRequest" value="Submit Request(s)">

<%End If%>
 
<TABLE cellSpacing=1 cellPadding=2 class = "style17" style="width: 95%" align="center">  



</TABLE>
</form>