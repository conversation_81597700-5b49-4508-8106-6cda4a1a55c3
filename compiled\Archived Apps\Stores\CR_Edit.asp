<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<head>

<TITLE>Modify Cross Reference</TITLE>
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_Session.asp"-->
<!--#include file="classes/asp_cls_DataAccess.asp"-->
</head>
<style type="text/css">
.style1 {
	font-family: Arial;
	text-align: center;
}
.style3 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
.style4 {
	text-align: left;
}
.style5 {
	font-family: Arial;
	font-size: medium;
}
.style6 {
	text-align: center;
}
.auto-style1 {
	border-style: solid;
	border-color: #C0C0C0;
	font-family: Arial;
	text-align: center;
	font-size: x-small;
	background-color: #E7EBFE;
}
.auto-style2 {
	font-weight: bold;
	font-size: x-small;
	border-style: solid;
	border-color: #C0C0C0;
	background-color: #E7EBFE;
}
</style>
<% 
 dim strCode, strDescription, strid, strsql, MyRec, strName, strBID, strMill, strType
strid = request.querystring("id")



strsql = "SELECT tbl_Stores_IDs.* from tbl_Stores_IDs where ID = " & strid

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
strname = MyRec.fields("Display_Name")
strBID= MyRec.fields("Logon_ID")
strBadge = MyREc("Badge")
 
MyRec.close

 set objGeneral = new ASP_CLS_General


if objGeneral.IsSubmit() Then
If request.form("Delete") = "ON" then
  strsql = "Delete from tbl_Stores_IDs where ID = " & strid 
  	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
          Response.redirect("CR_List.asp")


else

	Call SaveData() 
	end if

End if %>
<body><form name="form1" action="CR_edit.asp?id=<%= strid%>" method="post">
 
<table border="0" cellpadding="0" cellspacing="0" width = 60% style="border-collapse: collapse" bordercolor="000000" height="25" align = center >
  <tr>
    <td  bgcolor="#FFFFFF" bordercolor="#CCCCFF" height="25" >
    <p align="center"><font face="Arial" size="4">Modify </font>
	<span class="style5">Cross Reference </span></td>
    <td align = center height="25"><font face="Arial"><b><a href="CR_List.asp">RETURN</a></b></font></td>

    <td align = center bgcolor="#FFFFFF" height="25">
  <Input name="Submit" type="submit" Value="Submit" style="float: right" ></td>
  </tr>
</table><br><br>
<div align="center">
<table cellpadding="0" cellspacing="0" style="width: 35%;" bordercolor="#808080"  height="10" class="style3">
    <tr>
<td align="left" style="height: 39" class="auto-style2" >
	<font face="Arial">Name   </font></td>

<td class="auto-style1" style="height: 39" >
	<strong>Logon ID</strong></td>   
   <td class="auto-style1" style="height: 39" >
	<strong>Badge</strong></td>   

   <td class="auto-style1" style="height: 39" >
	<strong>Delete</strong></td>   

  </tr>
  
    <tr>
    <td  bordercolor="#CCCCFF" height="47"  align="center" class="style4" >   <font face="Arial">	
		<input type="text" name="Name" size="35" value="<%= strName %>" style="width: 177px" tabindex="1">
</font></td>
    	  	
   <td  bordercolor="#CCCCFF" height="47"  align="center" class="style6" >  	
     <font face="Arial">	
		<input type="text" name="BID" size="35" value="<%= strBID %>" style="width: 79px; height: 22px;" tabindex="3"></font></td> 

   <td  bordercolor="#CCCCFF" height="47"  align="center" class="style6" >  	
     <font face="Arial">	
		<input type="text" name="Badge" size="35" value="<%= strBadge %>" style="width: 79px; height: 22px;" tabindex="3"></font></td> 

   <td  bordercolor="#CCCCFF" height="47"  align="center" class="style6" >  <input type="checkbox" name="Delete" value="ON">
     &nbsp;</td> 

  </tr>
 
  </table>
</div>



</form>
   
  

</body>
 <%

  
  Function SaveData()

strName = Replace(Request.form("Name"), "'", "''")   
strBID = Request.form("BID") 
strBadge = request.form("Badge")

 
  
  strsql = "Update tbl_Stores_IDs set Display_Name = '" & strName & "', Logon_ID = '" & strBID & "', Badge = " & strbadge & " where ID = " & strid 
  	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
          Response.redirect("CR_List.asp")
  End Function
  
   %><!--#include file="footer.inc"-->