<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">


<TITLE>Transfer Truck from Yard to Baldwin</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

 <%    Dim objDAC, strSQL, MyRec, objMOC, rstFiber, strCID, rstFiberTrans

       Dim objGeneral, strDate, MyConn, strTCID

	
	
       set objGeneral = new ASP_CLS_General
  Call getData()   
if objGeneral.IsSubmit() Then

strCID = Request.form("CID")



	Response.redirect("TransYtoB.asp?id=" & strCID)
	Session("Trailer") = ""
	


End if
%>
<SCRIPT LANGUAGE="JavaScript">
function formHandler(Form1){
document.forms["Form1"].submit();
}
</SCRIPT>

<body>
<form name="Form1" action="SelectTruckYtoB.asp" method="post" >

<p>&nbsp;</p>
<table border="1" cellpadding="0" class = "tablecolor1" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="60%" id="AutoNumber2" align = center>

       <TD align = center>  <p>&nbsp;</p> <font face="Arial" size="3"><b>Select Trailer to Transfer from Yard to Baldwin:&nbsp;&nbsp;</b></font>
	&nbsp;&nbsp;&nbsp;
     <font face="Arial">
     <select name="CID">
 	<option value="" selected>Trailer Number</option>
      <%= objGeneral.OptionListAsString(rstFiber, "CID", "Trailer", strCID) %>
     </select><font size="2"> </font></font>
		<p>&nbsp;</p>
</tr>

</table>
	<p>&nbsp;</p> 
<p align="center"><input type="submit" value="Continue" id=submit1 name=submit1><br><br><br>
<br><br>
</form>


<%

 Function GetData()
        set objMOC = new ASP_CLS_FIBER
        set rstFiber = objMOC.YtoB()
     
     

    End Function 
    


 %><!--#include file="Fiberfooter.inc"-->