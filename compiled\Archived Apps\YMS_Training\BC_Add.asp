																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Add Broke Consumption Projection </TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->


<% Dim MyRec, strsql


  set objGeneral = new ASP_CLS_General
  if objGeneral.IsSubmit() Then	
  Dim strdate, strTB, strTG, strWTB, strBTB  

  	
	If len(request.form("TB")) > 0 then
  	strTB = request.form("TB")
  	else
  	strTB = 0
  	end if
  	
  	If len(request.form("TG")) > 0 then
  	strTG = request.form("TG")
  	else
  	strTG = 0
  	end if
	
  	If len(request.form("WTB")) > 0 then
  	strWTB = request.form("WTB")
  	else
  	strWTB = 0
  	end if
  	
   	  	If len(request.form("BTB")) > 0 then
  	strBTB= request.form("BTB")
  	else
  	strBTB = 0
  	end if
 
  	

   	 strdate = request.form("C_Date")
   	 
   	
  	
	strsql =  "INSERT INTO tblBrokeConsumption (INV_Date, Tissue_bales, Tissue_gaylords, White_towel_bales, Brown_towel_bales) "_
	&" SELECT '" & strDate & "', " & strTB & ", " & strTG & ", " & strWTB & ", " & strBTB & ""
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
Response.redirect("BC.asp")		
end if
	
%>

<style type="text/css">
.style2 {
	font-family: arial;
	font-size: x-small;
}
.style4 {
	border-style: solid;
	border-width: 1px;
}
.style5 {
	font-size: x-small;
	font-family: Arial, Helvetica, sans-serif;
	text-align: center;
}
.style6 {
	background-color: #FFFFD7;
}
.style7 {
	text-align: center;
}
.style8 {
	font-family: arial;
	font-size: x-small;
	text-align: center;
}
</style>
</head>

<body>
<br>
	
<form name="form1" action="BC_Add.asp" method="post" ID="Form1">
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>
 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Add Consumption Projection</b></font></td>
<td align = right><font face="Arial"><a href="BC.asp">Return</a>&nbsp;</td>


</tr>
	    </table>



	<p>&nbsp;</p>
	<table id="table1" style="width: 35%" class="style4" align="center">
<tr bgcolor = #CCCCFF>
		<td bgcolor="#FFFFD7" class="style8">

Consumption Date</td>
		<td bgcolor="#FFFFD7" class="style8">

&nbsp;Tissue Bales</td>
		<td bgcolor="#FFFFD7" class="style8">

&nbsp;Tissue Gaylords</td>

		<td bgcolor="#FFFFD7">

<p align="center" class="style2">White Towel Bales</td>
		<td bgcolor="#FFFFD7">

<p align="center" class="style2">Brown Towel Bales</td>

	</tr>
	<tr>
		<td class="style7"><font face = arial size = 1>
		<input type="text" name="C_Date" size="20" style="width: 112px"></td>
		<td class="style7"><font face = arial size = 1>
		<input type="text" name="TB" size="20" style="width: 69px"></td>
			<td class="style7"><font face = arial size = 1>
		<input type="text" name="TG" size="20" style="width: 69px"></td>

		<td>
		<p align="center"><font face = arial size = 1>
		<input type="text" name="WTB" size="20" style="width: 53px"></td>
			<td>
		<p align="center"><font face = arial size = 1>
		<input type="text" name="BTB" size="20" style="width: 53px"></td>

	</tr>
</table>


<p align="center"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></p>
</form>
<!--#include file="Fiberfooter.inc"-->