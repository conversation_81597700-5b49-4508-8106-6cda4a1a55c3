
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">


<TITLE>Transfer Truck from Yard to Meyer Building</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

 <%    Dim objDAC, strSQL, MyRec, objMOC, rstFiber, strCID, rstFiberTrans

       Dim objGeneral, strDate, MyConn, strTCID

	
	
       set objGeneral = new ASP_CLS_General
   
if objGeneral.IsSubmit() Then
if request.form("Transfer") = "" then
strCID = Request.form("CID")
else
strCID = request.form("Transfer")
end if



	Response.redirect("TransYtoMeyer.asp?id=" & strCID)
	Session("Trailer") = ""
	


End if
%>
<SCRIPT LANGUAGE="JavaScript">
function formHandler(Form1){
document.forms["Form1"].submit();
}
</SCRIPT>

<style type="text/css">
.style1 {
	font-size: small;
}
.auto-style1 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
	background-color: #F2FBFF;
}
</style>
</head>

<body>
<form name="Form1" action="SelectTruckYtoMeyer.asp" method="post" >

<p>&nbsp;</p>
<div align="center">
<table cellpadding="0"  cellspacing="0" style="width: 60%; height: 144;" bordercolor="#111111" id="AutoNumber2" class="auto-style1" >

       <TD align = center>  <p>&nbsp;</p> <font face="Arial" size="3"><b>Select Trailer to Transfer from Yard to 
	   Meyer Building:&nbsp;&nbsp;</b></font>
	&nbsp;&nbsp;&nbsp;
     <font face="Arial">
     <select name="CID">
 	<option value="" selected>Trailer Number</option>
	<% strsql = "Select CID, Species, Trailer from tblCars where Trailer <> 'UNKNOWN' and Location = 'YARD' order by Trailer"
 	      Set MyRec = Server.CreateObject("ADODB.Recordset")
 
    MyRec.Open strSQL, Session("ConnectionString"), adOpenDynamic
    If not MyRec.eof then
    While not MyRec.eof %>
 

    <option value=<%= MyRec("CID") %>><%= MyRec("Trailer") %> - <%= MyRec("Species") %></option>
  
    <% MyRec.movenext
    wend
    end if
    MyRec.close %>  
 	
     </select><font size="2"> <br>
		<br>
		</font><strong><span class="style1">Select Shuttle Trailer to 
		Transfer to Meyer Building:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
     <select name="Transfer">
    	<option value="" selected>Trailer Number</option>
	<% strsql = "Select CID, Species, Transfer_trailer_Nbr from tblCars where Trailer = 'UNKNOWN' and Location = 'YARD' order by Transfer_Trailer_nbr"
 	      Set MyRec = Server.CreateObject("ADODB.Recordset")
 
    MyRec.Open strSQL, Session("ConnectionString"), adOpenDynamic
    If not MyRec.eof then
    While not MyRec.eof %>
 

    <option value=<%= MyRec("CID") %>><%= MyRec("Transfer_Trailer_NBR") %> - <%= MyRec("Species") %></option>
  
    <% MyRec.movenext
    wend
    end if
    MyRec.close %>     </select></span></strong></font><p>&nbsp;</p>
</tr>
 

</table>
	</div><br><br>
	<p align="center"><b><font face="Arial"><a href="Meyer_receipt.asp">If Truck is not an option, click 
	here</a></font></b></p> <br><br>
<p align="center"><input type="submit" value="Continue" id=submit1 name=submit1><br><br><br>
<br><br>
</form>


<!--#include file="Fiberfooter.inc"-->