<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 14.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Damaged Trailer</title>
<style type="text/css">
.style1 {
	border: 1px solid #E4E4E4;
	border-collapse: collapse;
		background-color: #FFFFCC;
}
.style2 {
	border-style: solid;
	border-color: #DFDFF7;
	background-color: #DFDFF7;
}
.style3 {
	font-weight: bold;
	border-style: solid;
	border-color: #FFFFCC;
	background-color: #FFFFCC;
}
.style7 {
	background-color: #FFFFCC;
}
.style8 {
	background-color: #DFDFF7;
}
.style9 {
	font-family: Arial;
	font-size: medium;
	font-weight: bold;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID, objMOC, rstFiber, strCarrier
      
    Dim strTrailer
    Dim strReleaseNbr
    Dim strRECNbr
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strGenerator
    Dim strGenCity
    Dim strGenState
    Dim strOther, strR, strsql3

       strId  = Trim(Request.QueryString("id")) 


 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
 
		Call SaveData() 

	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to delete a Receipt.</font></br>")
	MyRec.close
	end if

  
ELSE


strsql = "SELECT tblCars.* from tblCars WHERE tblCars.CID = " & strid

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


    strTrailer = MyRec.fields("Trailer")
    strCarrier = MyRec.fields("Carrier")
    strRECNbr = MyRec.fields("CID")
 
     strDateReceived = MyRec.fields("Damage_Date")
		strDateRepaired = MyRec.fields("Damage_repair")
    

MyRec.close

	end if

%>



<body>
<table width = 100%><tr><td width = 33%></td><td align = center width = 34%>
	<span class="style9">Damaged Trailer</span><b> </b></td><td align = right width = 33%><b><font face = arial size = 2><a href="javascript:history.go(-1);">RETURN</a></font></b></td></tr></table>



<form name="form1" action="Damage_Edit.asp?id=<%=strid%>" method="post">
<table cellspacing="0" width="60%" align = center class="style1">
<tr>
    <td class="style2" colspan="2">&nbsp;</td>

  </tr>
    <tr>

      <td align = right class="style3" style="height: 41px">
  <font face="Arial" size="2">Receipt Number:&nbsp;</font></td>
<td  align = left style="height: 41px"> <font face="Arial" size="2"><%= strRecNbr%>

      &nbsp;</td></tr>

  <tr>
    <td  align = right class="style3" style="height: 48px" >
   <font face="Arial" size="2">Trailer:&nbsp;</font></td>
<td  align = left style="height: 48px"><font face="Arial" size="2">

     <%= strTrailer%></td></tr>

  </tr>
<tr>
    <td class="style3" style="height: 54px">  
	<p align="right">  <font face="Arial" size="2">&nbsp;Carrier:&nbsp;</font></td>
    <td class="style7" style="height: 54px"><font face="Arial" size="2"> <%= strCarrier %></td>
  </tr>

<tr>
    <td class="style8" colspan="2">&nbsp;</td>

  </tr>
       <tr>
          <td  align = right class="style3" style="height: 56px" >
    <font face="Arial" size="2">Date Damaged:&nbsp;</font></td>
<td align = left style="height: 56px">

      <input type="text" name="Date_Received" size="15" value = "<%= strDateReceived%>"></td></tr>
         <tr>
          <td  align = right class="style3" style="height: 57px" >
  <font face="Arial" size="2">Date Repaired:&nbsp;</font></td >
   <td align = left style="height: 57px">   

      <input type="text" name="Date_repaired" size="15" value = "<%= strDateRepaired%>"></td></tr>

  <tr>
    <td class="style8">&nbsp;</td>

    <td align = left class="style8"><Input name="Update" type="submit" Value="Submit" ></td>
  </tr>
</table>

</form>
</body>
<%



 Function SaveData()
strDateReceived = Request.form("Date_received")
strDateRepaired = Request.form("Date_repaired")

If len(strDateReceived) > 5 then


         strSql = "Update tblCars Set  Damage_date =  '" & strDateReceived & "', Damaged = 'YES' where CID = " & strid & ""
         
         else
         
         strSql = "Update tblCars Set  Damage_date =  Null, Damaged = 'NO' where CID = " & strid & ""

         end if
         
 	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
      
  If len(strDateRepaired) > 5 then       
         
      strSql = "Update tblCars Set  Damage_Repair = '" & strDateRepaired & "', Damaged = 'NO' where CID = " & strid & ""
else
strSql = "Update tblCars Set  Damage_Repair = NULL where CID = " & strid & ""

  end if       
 	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 

    


Response.redirect ("Edit_Yard_Trucks.asp")



End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->