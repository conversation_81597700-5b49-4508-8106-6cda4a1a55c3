<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Yard Email</title>
</head>
<%
dim strbody, strCID, strECarrier, strETrailer, strELoad, strEDoc, strEDC, strEDCName, strEAdd, strECity, strEState
dim strECC, strEBCC, strESpecies, MyRec, strsql, MyConn2, strbody3, strbody1




dim  strCarrier, str<PERSON>railer,   strsql3, MyRec3, gTcount, str<PERSON><PERSON>, str<PERSON>oney, MyConn
dim strSpecies, strDateReceived, strVendor, strDetention, strDays, strdate, gcount, gSpecies, strNow, strEmailto, strEmailCC
dim gAvgdays, gExcessDays, gTotaldays, strExcess, strMTDDetention, gMTDAvgDays, gMTDTotalCount, gMTDTotalDays, gMTDExcess
dim strMTDDetentionR, gMTDAvgDaysR, gMTDTotalCountR, gMTDTotalDaysR, gMTDExcessR
Dim gCountR, strMoneyR, gAvgDaysR, gExcessR, gTcountR, strTMoneyR
strdate = formatdatetime(now(),2)
gcount = 0
gcountR = 0
gtcountR = 0
gtcount = 0
gSpecies = ""
strMoney = 0
strMoneyR = 0
strTMoney = 0
strTMoneyR = 0
gAvgdays = 0
gAvgdaysR = 0
gExcessDays = 0
gExcessDaysR = 0
gTotaldays = 0
strMTDDetention = 0
gMTDAvgDays = 0
gMTDTotalCount = 0
gMTDTotalDays = 0
gMTDExcess = 0
strMTDDetentionR = 0
gMTDAvgDaysR = 0
gMTDTotalCountR = 0
gMTDTotalDaysR = 0
gMTDExcessR = 0

strsql = "SELECT tblCars.*,  [date_received] AS Sort_Date FROM tblCars WHERE (((tblCars.Date_received) Is Not Null) AND "_
&"  ((tblCars.Location)='Yard') AND ((tblCars.Trailer) Is Not Null)) and Species <> 'KDF'  and Species <> 'Display Components' ORDER BY tblCars.Species, Sort_Date "   
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

 Dim ii
       ii = 0
		
           strEmailTo = "<EMAIL>"
          strEmailCC = "<EMAIL>"          
              
			Set objMail = Server.CreateObject("CDO.Message")		
			objMail.From = "<EMAIL>"
			objMail.To = strEmailTo	
			
			objMail.BCC = strEmailCC
 
			objMail.Subject = "Fiber Yard Report "
			strBody2 = "<table border=0  cellpadding=0 cellspacing=0 width=800><tr><td align = left><font face = arial size = 2>Species&nbsp; </td><td align = left><font face = arial size = 2>Date Received&nbsp; </td><td align = left><font face = arial size = 2> Trailer </td><td align = left><font face = arial size = 2>Carrier&nbsp;</td><td align = left><font face = arial size = 2>Vendor&nbsp;</td><td align = left><font face = arial size = 2>To-Date<br> Detention</td><td align = left><font face = arial size = 2>Days in<br>Yard</td></tr>"
			
				While not MyRec.EOF
			'On error Resume Next

    



     if isnull(MyRec.fields("Transfer_date")) then 
     strCarrier = MyRec.fields("Carrier") 
     strTrailer = MyRec.fields("Trailer") 
     strSpecies = MyRec.fields("Species") 
     strDateReceived = MyRec.fields("Date_received")
     strVendor = MyRec.fields("Vendor") 
       strRejected = MyRec.fields("Rejected")  
        
       else
         strSpecies = MyRec.fields("Species") & " SHUTTLE"        
		strDateReceived = MyRec.fields("Transfer_Date")
		strTrailer =  MyRec.fields("Transfer_trailer_nbr")
		strCarrier = MyRec.fields("Trans_Carrier")
		 strVendor = MyRec.fields("Vendor") 
		 strRejected = MyRec.fields("Rejected")
	 end if 

        if strRejected = "YES" then
        strRejected = "Rejected"
        end if    
       if isnull(MyRec.fields("Transfer_date")) then
        
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

             else
        
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Trans_Carrier") & "' "
             end if  
   
                  strDays = fix(Now()- cdate(MyRec.fields("Date_received")))
                  
           
                  
    If MyRec.fields("Carrier") = "RAIL"  or MyRec.fields("Carrier") = "RAIL" Then
          strdays = int(strdays)
              if strdays = 0 then
          strDetention = 0
          elseif strdays = 1 then
          strDetention = 27.00
          elseif strdays = 2 then
          strDetention = 53.00
          
          elseIf strdays = 3 then 
          strDetention = 80.00
          elseif strdays = 4 then
          strDetention = 106.00
          elseif strdays = 5 then
          strDetention = 145.00
          elseif strdays = 6 then
          strDetention = 185.00
          elseif strdays = 7 then
          strDetention = 262.00
          elseif strdays = 8 then 
          strDetention = 339.00
          elseif strdays > 8 then
          strDetention = round(339.48 + (77.45*(strdays-8)),0)
          end if
    	if strdays  > 2 then 
    	strExcess = 1
  			else            
       		
			strExcess = 0
	 end if          
  else        
 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    if strDays  > strFree then 
   strDetention =   (int(strDays) * strFee) - (strFee * strFree)
   strExcess = 1
	 else 
	strDetention = 0
	strExcess = 0
	 end if 
	
end if

If left(MyRec("Trailer"), 4) = "GACX" then
strDetention = 0
strExcess = 0
end if  
	
if UCase(MyRec.fields("Species")) = Ucase(gSpecies) or gcount = 0 then	
	if strdays < 6 or left(MyRec("Species"),6) = "FIBRIA" Then	
 	strbody2 = strbody2 & " <tr bgcolor=white><td align = left><font face = arial size = 1> " & strSpecies &  "</td><td align = left><font face = arial size = 1>" & strDateReceived & "</td><td align = left><font face = arial size = 1>"  & strTrailer & "</td><td align = left><font face = arial size = 1>"  & strCarrier & "</td><td align = left><font face = arial size = 1>" & strVendor & "</td><td align = left><font face = arial size = 1>" & strDetention & " " & strRejected & " </td><td align = left><font face = arial size = 1>" & strDays & " </td></tr> "
	elseif strdays > 5 and strdays < 9 then
	 strbody2 = strbody2 & " <tr bgcolor=#FFFF99><td align = left><font face = arial size = 1> " & strSpecies &  "</td><td align = left><font face = arial size = 1>" & strDateReceived & "</td><td align = left><font face = arial size = 1>"  & strTrailer & "</td><td align = left><font face = arial size = 1>"  & strCarrier & "</td><td align = left><font face = arial size = 1>" & strVendor & "</td><td align = left><font face = arial size = 1>" & strDetention & " " & strRejected & "</td><td align = left><font face = arial size = 1>" & strDays & " </td></tr> "
	else
	 strbody2 = strbody2 & " <tr bgcolor=#FCDCE6><td align = left><font face = arial size = 1> " & strSpecies &  "</td><td align = left><font face = arial size = 1>" & strDateReceived & "</td><td align = left><font face = arial size = 1>"  & strTrailer & "</td><td align = left><font face = arial size = 1>"  & strCarrier & "</td><td align = left><font face = arial size = 1>" & strVendor & "</td><td align = left><font face = arial size = 1>" & strDetention & " " & strRejected & "</td><td align = left><font face = arial size = 1>" & strDays & " </td></tr> "

	end if 

else

	strBody2 = strbody2 & "<font face = arial size = 1> Total " & gSpecies & ": " & gcount
 	if strdays < 6 or left(MyRec("Species"),6) = "FIBRIA" Then	
 	strbody2 = strbody2 & " <tr bgcolor=white><td align = left><font face = arial size = 1> " & strSpecies &  "</td><td align = left><font face = arial size = 1>" & strDateReceived & "</td><td align = left><font face = arial size = 1>"  & strTrailer & "</td><td align = left><font face = arial size = 1>"  & strCarrier & "</td><td align = left><font face = arial size = 1>" & strVendor & "</td><td align = left><font face = arial size = 1>" & strDetention & " " & strRejected & " </td><td align = left><font face = arial size = 1>" & strDays & " </td></tr> "
	elseif strdays > 5 and strdays < 9 then
 	strbody2 = strbody2 & " <tr bgcolor=#FFFF99><td align = left><font face = arial size = 1> " & strSpecies &  "</td><td align = left><font face = arial size = 1>" & strDateReceived & "</td><td align = left><font face = arial size = 1>"  & strTrailer & "</td><td align = left><font face = arial size = 1>"  & strCarrier & "</td><td align = left><font face = arial size = 1>" & strVendor & "</td><td align = left><font face = arial size = 1>" & strDetention & " " & strRejected & "</td><td align = left><font face = arial size = 1>" & strDays & " </td></tr> "
	else
 	strbody2 = strbody2 & " <tr bgcolor=#FCDCE6><td align = left><font face = arial size = 1> " & strSpecies &  "</td><td align = left><font face = arial size = 1>" & strDateReceived & "</td><td align = left><font face = arial size = 1>"  & strTrailer & "</td><td align = left><font face = arial size = 1>"  & strCarrier & "</td><td align = left><font face = arial size = 1>" & strVendor & "</td><td align = left><font face = arial size = 1>" & strDetention & " " & strRejected & "</td><td align = left><font face = arial size = 1>" & strDays & " </td></tr> "

	end if 
gcount = 0

end if

If Myrec.fields("Carrier") = "RAIL" or MyRec.fields("Carrier") = "Rail" then
	gTotaldaysR = gTotaldaysR + strdays    
  gcountR = gcountR + 1
       gTcountR = gTcountR + 1
       gRTcount = gRTcount + 1
 		gSpecies = MyRec.fields("Species")
 		strMoneyR = strMoneyR + cint(strDetention)
 		strTMoneyR = strTmoneyR + strMoney
 		gExcessR = gExcessR + strExcess
elseif Myrec.fields("Carrier") = "GACX" then
gRTcount = gRTcount + 1
else

 	gTotaldays = gTotaldays + strdays    
  gcount = gcount + 1
       gTcount = gTcount + 1
       
 		gSpecies = MyRec.fields("Species")
 		strMoney = strMoney + cint(strDetention)
 		strTMoney = strTmoney + strMoney
 		gExcess = gExcess + strExcess
 		
 		end if
       ii = ii + 1
       MyRec.MoveNext
            
  
		 		WEND
           		MyRec.Close
           		Dim strMdate
           		strMdate = formatdatetime(Now(),2)
           		
    strsql = "SELECT tblCars.* FROM tblCars WHERE (month(Date_unloaded) = month('" & strMdate & "') and year(Date_unloaded) = year('" & strMdate & "') and species <> 'KDF' and Species <> 'Display Components') "_
    &" or (((tblCars.Date_received) Is Not Null) AND ((tblCars.Location)='Yard') AND Species <> 'KDF'  and Species <> 'Display Components' AND ((tblCars.Trailer) Is Not Null)) "  
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")    
   				While not MyRec.EOF 
   				

  if isnull(MyRec.fields("Transfer_date")) then
        
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

             else
        
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Trans_Carrier") & "' "
             end if  
             
             
             
   If isnull(Myrec.fields("Date_unloaded")) then 
                  strDays = Fix(Now()- cdate(MyRec.fields("Date_received")))
                else 
              strdays =  Fix(datediff("d", Myrec.fields("date_received"), Myrec.fields("Date_unloaded")))
				
                  end if 
                  
                  
                   
    If MyRec.fields("Carrier") = "RAIL"  or MyRec.fields("Carrier") = "Rail" Then
	
	   
          strdays = int(strdays)
          if strdays = 0 then
          strDetention = 0
          elseif strdays = 1 then
          strDetention = 27.00
          elseif strdays = 2 then
          strDetention = 53.00
          
          elseIf strdays = 3 then 
          strDetention = 80.00
          elseif strdays = 4 then
          strDetention = 106.00
          elseif strdays = 5 then
          strDetention = 145.00
          elseif strdays = 6 then
          strDetention = 185.00
          elseif strdays = 7 then
          strDetention = 262.00
          elseif strdays = 8 then 
          strDetention = 339.00
          elseif strdays > 8 then
          strDetention = round(339.48 + (77.45*(strdays-8)),0)
          end if
    	if strdays  > 2 then 
    	strExcess = 1
  			else            
       		
			strExcess = 0
	 end if 
	 
If left(MyRec("Trailer"), 4) = "GACX" then
strDetention = 0
strExcess = 0
end if            
  else                   
          
 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    
    end if  
    MyRec3.close
    if strDays  > strFree then 
   strDetention =   (int(strDays) * strFee) - (strFee * strFree) 
   strExcess = 1 
   else
   strDetention = 0
   strExcess = 0
   end if
 end if  
 If MyRec.fields("Carrier") = "Rail" or MyRec.fields("Carrier") = "RAIL" then
	strMTDDetentionR  = strMTDDetentionR + strDetention 	
  	gMTDExcessR = gMTDExcessR + strExcess
 	gMTDTotaldaysR = gMTDTotaldaysR + strdays    
 	gMTDTotalCountR = gMTDTotalCountR + 1 
 	else
 		strMTDDetention  = strMTDDetention + strDetention 	
  	gMTDExcess = gMTDExcess + strExcess
 	gMTDTotaldays = gMTDTotaldays + strdays    
 	gMTDTotalCount = gMTDTotalCount + 1 
 	end if
 		
     
       MyRec.MoveNext



            
  
		 		WEND
           		MyRec.Close      
        		
           		strNow = formatdatetime(now(),0)
           		gAvgDays = round((gTotaldays/gTcount),1)
           		if gTcountR > 0 then 
           		gAvgDaysR = round((gTotaldaysR/gTcountR),1)
           		else
           		gAvgDaysR = 0
           		end if
           		if gMTDTotalCountR > 0 then
           		gMTDAvgDaysR = round((gMTDTotaldaysR/gMTDTotalCountR),1)
           		else
           		gMTDAvgDaysR = 0
           		end if
           			gMTDAvgDays = round((gMTDTotaldays/gMTDTotalCount),1)
	
	strBody2 = strbody2 & "Total " & gSpecies & ": " & gcount 
		
		strBody3 = "<p>On Yard Trailer Totals</p><table border=0  cellpadding=0 cellspacing=0 width=800><tr><font face = arial size = 1><td>Total Count</td><td>Detention Total</td><td>On Avg Days</td><td># Loads in Excess of Free Days</td></tr>"
						
	strBody3 = strbody3 & "<tr><font face = arial size = 1><td>" & gtcount & "</td><td>$" & formatNumber(strMoney,0) & "</td><td>" & gAvgDays & "</td><td>" & gExcess & "</td></tr>"
	
				strBody3 = strBody3 & "<tr><font face = arial size = 1><td>&nbsp;</td><td>MTD Detention</td><td>MTD Average Days</td><td>MTD # Loads in Excess of Free Days</td></tr>"
				
					strBody3 = strBody3 & "<tr><font face = arial size = 1><td>&nbsp;</td><td> $" & formatNumber(strMTDDetention,0) & "</td><td>" & gMTDAvgDays & "</td><td>" & gMTDExcess & "</td></tr></table>"
		
				strBody3 = strbody3 & "<p>On Yard Rail Totals</p><table border=0  cellpadding=0 cellspacing=0 width=800><tr><font face = arial size = 1><td>Total Count</td><td>Demurrage Total</td><td>On Avg Days</td><td># Loads in Excess of Free Days</td></tr>"
						
	strBody3 = strbody3 & "<tr><font face = arial size = 1><td>" & gRTcount & "</td><td>$" & formatNumber(strMoneyR,0) & "</td><td>" & gAvgDaysR & "</td><td>" & gExcessR & "</td></tr>"
	
				strBody3 = strBody3 & "<tr><font face = arial size = 1><td>&nbsp;</td><td>MTD Demurrage</td><td>MTD Average Days</td><td>MTD # Loads in Excess of Free Days</td></tr>"
				
					strBody3 = strBody3 & "<tr><font face = arial size = 1><td>&nbsp;</td><td> $" & FormatNumber(strMTDDetentionR,0) & "</td><td>" & gMTDAvgDaysR & "</td><td>" & gMTDExcessR & "</td></tr></table>"

strbody1 = "<p><font face = arial size = 2 color=navy><b> NOTE: Because trailers are constantly arriving and being emptied, the below Information is only valid for a <br>short time following the time listed above.  Please re-run the yard reports from the Fiber Logistics System<br> which can be accessed from the Fiber team web page when needed for decision making.  Contact Steven <br>Day Ext 2181 for help with these reports.</b></p>"
		objMail.HTMLBody = "<font face = arial size = 2> Fiber Yard report for " &  strNow  & " <br><br>" & strbody1 & strbody3 & strbody2




			' objMail.Send
			Set objMail = nothing
		
			strsql = "Update tblYardEmailDate set  Email_Date = '" & strNow & "', Ename = '" & Session("EmployeeID") & "' where E_Key = 1"

	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

   Response.redirect("Send_yard_email.asp")

 %>

