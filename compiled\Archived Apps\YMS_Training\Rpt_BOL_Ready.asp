
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<meta http-equiv="REFRESH" content="60">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">
<TITLE>Loads BOL Ready</TITLE>

<!--#include file="classes/asp_cls_headerOYM.asp"-->
<!--#include file="classes/asp_cls_SessionStringOYM.asp"-->
 


    <% Dim MyRec, strsql, strBeg, strEnd
  strBeg = request.querystring("b")
  strEnd = request.querystring("e")
   
 %>

<style type="text/css">
.style1 {
	font-family: Arial, Helvetica, sans-serif;
}
</style>

<body bgcolor = "#FCFBF8"><br><p align = center>
<b><font face="Arial">Loads that went BOL Ready&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</font></b></p><br>

          

 <table align = center width = 55%>
<tr>



 
    
    <td><font face = Arial size = 2><strong>Time</strong></td>
        <td><font face = Arial size = 2> <strong>Trailer</strong></td>
          
             <td><font face = Arial size = 2> <strong>Carrier</strong></td> 
             <td><font face = Arial size = 2> <strong>Outbound BOL</strong></td> 
</tr>


<% Dim strcount
count = 0
 
			   strsql = "SELECT Master_Table.* FROM Master_Table where Time_BOL_Ready >= '" & strBeg & "' and Time_BOL_Ready <= '" & strEnd & "' "_   
   &" UNION SELECT  Master_Table_Backup.* "_
&"  FROM Master_Table_Backup where Time_BOL_Ready >= '" & strBeg & "' and Time_BOL_Ready <= '" & strEnd & "'"

		
		Set MyRec = Server.CreateObject("ADODB.Recordset") 
   		MyRec.Open strSQL, Session("ConnectionString")

do until  MyRec.eof

 %>

<tr>



 
    
    <td><font face = Arial size = 2><%= MyRec.fields("Time_BOL_Ready") %></td>
        <td><font face = Arial size = 2> <%= MyRec.fields("Trailer") %></td>
           <td><font face = Arial size = 2> <%= MyRec.fields("Carrier") %></td>
             <td><font face = Arial size = 2> <%= MyRec.fields("Outbound_BOL") %></td> 
          
</tr>

    <%  
     count = count + 1
 	 MyRec.MoveNext
	
loop
MyRec.close

      
    %>

  </table>    

<p align="center" class="style1"><strong>Total Count: <%= count %></strong></p>
</BODY></font><br>
