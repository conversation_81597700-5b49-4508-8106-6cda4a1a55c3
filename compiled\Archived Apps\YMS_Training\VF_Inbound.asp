																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>In Transit Rail Cars </TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->


<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate

strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -7, strtdate)

strsql = "SELECT * from tblVirginFiber where status = 'Inbound'  order by species,  Trailer"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<style type="text/css">
.style1 {
	font-size: xx-small;
	font-family: Arial, Helvetica, sans-serif;
}
</style>
</head>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center>
<p align="left"><b><font face="Arial">In-Transit Loads</font></b></td>
<td align = right><font face="Arial"><a href="VF_add.asp">Add New</a>&nbsp;</td>


</tr>
	    </table>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1 align = center>  
	 <tr class="tableheader">
<td style="height: 16px"></td>
<td  align = left style="height: 16px">     <font face="Arial" size="1">	ID</font></td>
				<td  align = left style="height: 16px">     <font face="Arial" size="1">	Trailer/Car</font></td>
		<td style="height: 16px"  ><b><font face="Arial" size="1">Date Shipped</font></b></td>
		<td style="height: 16px"  ><b><font face="Arial" size="1">Date Received</font></b></td>
		<td  align = left style="height: 16px">     <font face="Arial" size="1">	Lead Time</font></td>
		<td  align = left style="height: 16px">     <font face="Arial" size="1">ETA</font></td>
		<td  align = left style="height: 16px">     <font face="Arial" size="1">Release</font></td>
		<td  align = left style="height: 16px" class="style1">     Tier</td>
		<td  align = left style="height: 16px">     <font face="Arial" size="1">Vendor</font></td>
		<td style="height: 16px"  ><b><font face="Arial" size="1">Item Description</font></b></td>
			<td style="height: 16px"  ><b><font face="Arial" size="1">SAP</font></b></td>
				<td style="height: 16px"  ><b><font face="Arial" size="1">PO</font></b></td>
		<td style="height: 16px"  ><b><font face="Arial" size="1">Units</font></b></td>
		<td  align = left style="height: 16px">     <font face="Arial" size="1">	Tons</font></td>
		<td style="height: 16px"  ><b><font face="Arial" size="1">UOM</font></b></td>
			<td style="height: 16px"  ><b><font face="Arial" size="1">Comments</font></b></td>
			<td style="height: 16px"></td>
	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td> <font size="1" face="Arial">

<a href="VF_edit.asp?id=<%= MyRec.fields("VID") %>">Edit</a></td>
<td> <font size="1" face="Arial">

<%= MyRec.fields("VID") %></td>

	<td  ><font size="1" face="Arial"><%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	
<td  ><font size="1" face="Arial"><%= MyRec.fields("Date_shipped")%>&nbsp;</font></td>
 <td  ><font size="1" face="Arial">
<% strVFID = MyRec.fields("VID")
strRelease = ""
strsql2 = "Select CID, Release_nbr, Date_received from tblCars where VID = " & strVFID

   Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL2, Session("ConnectionString")
    if  not MyConn.eof then %>
 
 <%= MyConn.fields("Date_received")%> 
<% end if
MyConn.close %>
&nbsp;</font></td> 

	<td  ><font size="1" face="Arial"><%= MyRec.fields("Lead_time")%>&nbsp;</font></td>
<% if datediff("d", MyRec.fields("ETA"), strTdate) > 0 then %>	
<td bgcolor="#FEDAD6"  >
<% else %>
<td>
<% end if %><font size="1" face="Arial"><%= MyRec.fields("ETA")%>&nbsp;</font></td>
<td  ><font size="1" face="Arial"><%= MyRec.fields("Release")%>&nbsp;</font></td>

<td><font size="1" face="Arial">
<% strTier = ""
strRelease = MyRec("Release")

 if len(strRelease) > 3 then

strsql = "SELECT tblOrder.Release, tblTier.Tier FROM tblTier INNER JOIN tblOrder ON (tblTier.Grade = tblOrder.Species) AND (tblTier.City = tblOrder.City) AND (tblTier.Generator = tblOrder.Generator) "_
&" AND (tblTier.State = tblOrder.State) AND (tblTier.Vendor = tblOrder.Vendor) WHERE  tblOrder.Release='" & strRelease & "'"
 Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
    if  not MyConn.eof then
    strTier = MyConn("Tier")
    end if
    MyConn.close
    end if %>
    <%= strTier %>

&nbsp;</td>
	<td  ><font size="1" face="Arial"><%= MyRec.fields("Vendor")%>&nbsp;
	
	<% if len(strRelease) > 3 then
	
	strsql = "SELECT Release, Vendor from tblOrder where   tblOrder.Release='" & strRelease & "'"
 Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
    if  not MyConn.eof then
    strV = MyCOnn("Vendor")
    end if
    MyConn.close
    end if %>

	
	
	
	<%= strV %></font></td>
	
<td  ><font size="1" face="Arial"><%= MyRec.fields("Species")%>&nbsp;</font></td>
<td  ><font size="1" face="Arial"><%= MyRec.fields("SAP_NBR")%>&nbsp;</font></td>
<td  ><font size="1" face="Arial"><%= MyRec.fields("PO")%>&nbsp;</font></td>
	<td  ><font size="1" face="Arial"><%= MyRec.fields("Bales_VF")%>&nbsp;</font></td>
	
<td  ><font size="1" face="Arial"><%= MyRec.fields("Tons")%>&nbsp;</font></td>
	<td  ><font size="1" face="Arial"><%= MyRec.fields("UOM")%>&nbsp;</font></td>
		<td  ><font size="1" face="Arial"><%= MyRec.fields("Other_comment")%>&nbsp;</font></td>
	<td> <font size="1" face="Arial">

<a href="VF_delete.asp?id=<%= MyRec.fields("VID") %>">

Delete</a></td>
</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>

<!--#include file="Fiberfooter.inc"-->