<HTML>
<HEAD>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Approval History </TITLE>
<style type="text/css">
.auto-style2 {
	border: 1px solid #C0C0C0;
}
.auto-style3 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.auto-style4 {
	font-family: Calibri;
	font-size: small;
	border: 1px solid #C0C0C0;
}
.auto-style5 {
	font-family: Calibri;
}
.auto-style6 {
	font-family: Calibri;
	font-size: small;
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.auto-style7 {
	font-size: small;
}
.auto-style9 {
	font-family: <PERSON><PERSON>ri;
	font-weight: bold;
}
.auto-style10 {
	font-family: Calibri;
	font-size: small;
}
.auto-style12 {
	font-size: x-small;
}
</style>
</HEAD>


<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_Procedure.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
   
<body>

<font face="Arial"><br>
	    <%   
	      Dim SCRIPT_NAME
		Dim strSQL, strID, strE_name, strBID, strsid, MyConn
		Dim BACK_TO_LIST_TEXT
           strid = Request.querystring("id")
           strBID = Session("EmployeeID")
strE_name = Session("Ename")
strsid = Request.querystring("s")

            
		SCRIPT_NAME = Request.ServerVariables("SCRIPT_NAME")

		BACK_TO_LIST_TEXT = "<p>Click <a href=""" & SCRIPT_NAME & """>" _
    		    & "here</a> to go back to record list.</p>" 
    		    Dim strD
    		    
    		    if len(request.querystring("s")) > 0 then 
    		    '
    		    else
    		    strsid = strid
    		    end if
    		    
    		      		        strsql = "Select Sdescription  from tblSOP where SOP_NO = '" & strSid & "'"
       Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
   		strD = MyConn.fields("SDescription")
   		
   		MyConn.close 
  %>
    		    
    		    <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% border=1 class="auto-style5">
  <tr><td colspan="4" bgcolor="white" class="subheader">&nbsp;</td></tr>

 <tr><TD><p align="left"><font size="3" face="Arial"><b>Approval History for <%= strsid%>&nbsp;&nbsp;<%= strD%></b></font></td>
</tr>
	    
	<%	Select Case LCase(Trim(Request.QueryString("action")))
		   
 		     Case "edit"
 		     
			iRecordId = Request.QueryString("id")
			strsid = Request.querystring("s")
			
			strsql = "Select HA_Status from tblHA where SpaceID = '" & strsid & "'"
			   Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
   		strHAStatus = MyConn.fields("HA_Status")
   		MyConn.close
			

			
			     Dim strWA
      strsql = "Select Workarea from tblSOP where SOP_NO = '" & strsid & "'"
       Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
   		strWA = MyConn.fields("Workarea")
   		
   		MyConn.close 
   		
   			 strSQL = "SELECT tblApprovers.* from tblApprovers where Work_area = '" & strWA & "' and P_BID = '" & strBID & "'"
      Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
   		 If not MyConn.eof then
		
			strSQL = "Select * From tblChangeLog Where CID = " & iRecordId

			Set MyRec = Server.CreateObject("ADODB.RecordSet")
			MyRec.Open strSQL, Session("ConnectionString")

			If Not MyRec.EOF Then
			    %>
				<form action="<%= SCRIPT_NAME %>?action=editsave&id=<%= strSID %>" method="post">
				    <font face="Arial">
				    <input type="hidden" name="id" value="<%= MyRec.Fields("CID").Value %>" />
				    <input type = "hidden" name = "sid" value = "<%= Request.querystring("s") %>" />
				     <input type = "hidden" name = "Approver1" value = "<%= MyRec.fields("Approver1") %>" />
				       <input type = "hidden" name = "Status" value = "<%= request.querystring("h") %>" />
					</font><font face="Arial">

<p align = right><font face = arial size = 2><a href="Change_history.asp?action=new&id=<%= strsid %>&ha=<%= strHAStatus%>">RETURN</a></font></p>
<% if len(MyRec.fields("Approver1")) > 3 then %>
	 <br><b>Approver 2 Signoff:</b> 
			    	    <br>
                   			 &nbsp;<p><b>Accept/Reject Proposed Change:</b></p>
					<p><select size="1" name="Accept2">
					<option selected>Accept</option>
					<option>Reject</option>
					</select></p>
					<p><br>
					
						    <% else %>
	 			   <font face="Arial">
	 			   <br><b>Approver 1 Signoff:</b>
			    	    </font>
			    	    <br>
                    &nbsp;<br>
		          &nbsp;</p>
					<p><b>Accept/Reject Proposed Change:</b></p>
					<p><select size="1" name="Accept1">
					<option selected>Accept</option>
					<option>Reject</option>
					</select></p>
					<p><br>
                   		<% end if %>
			    
                   			 <b>Comments:</b><br>
			    	    <textarea rows="5" name="Comments" cols="59"><%= MyRec.fields("Comments") %></textarea>&nbsp; 
					</p>
						

					<p>&nbsp;<br><Input name="Update" type="submit" Value="Submit" >
					</p>
				
				</form><br>
   			 <%  end if 
   			 else
   			 Response.write ("<font face = arial size = 3><br>You do not have authorization to approve this change")		 
   			 End if 
   			  
			   		

		    Case "editsave"
			iRecordId = Clng(Request.form("id"))
			Dim strToday, strComments
			strnow = dateadd("h", -5, now())
			strToday = formatdatetime(strnow,2)
			strHAStatus = request.form("Status")
			

	
		
		strComments = Replace(Request.form("Comments"), "'", "''") 
		
				if len(request.form("Approver1")) > 0 then
				
				
		strSQL = "Update tblChangeLog Set Comments = '" & strcomments & "', Approver2 = '" & StrE_Name & "', Accept_reject_2 = '" & Request.form("Accept2") & "', approver2_date = '" & strToday & "' Where CID = " & iRecordId
	
		
	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
			

else 
				strSQL = "Update tblChangeLog Set Comments = '" & strcomments & "', Approver1 = '" & strE_name & "', Accept_reject_1 = '" & Request.form("Accept1") & "', approver1_date = '" & strToday & "' Where CID = " & iRecordId
	
		

	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
end if 
			strid = Request.form("sid")
			Response.redirect ("Change_History.asp?id=" & strid & "&ha=" & strHAStatus)

		


		    Case else   'Default view
		    Dim strApprover1, strApprover2
		    strsql = "SELECT HA_Approver_1, HA_Approver_2, HA_status from tblHA where SpaceID = '" & strid & "'"
			Set MyRec = Server.CreateObject("ADODB.Recordset") 
   			MyRec.Open strSQL, Session("ConnectionString")
   			strHAStatus = MyRec.fields("HA_status")
   			strApprover1 = MyRec.fields("HA_Approver_1")
   			strApprover2 = MyRec.fields("HA_Approver_2")
   			MyRec.close
			 
			strSQL = "SELECT * from tblHA where SpaceID = '" & strid & "'"	
	     	
			Dim strHastatus, MyRec2, strsql2, strH
			
 			Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
			strH = MyRec.fields("IDHazAssess")
			MyRec.close

			strSQL = "SELECT * from tblChangeLog where SOP_NO = '" & strid & "' order by Change_Date desc"	

						
 			Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
			%>
			<table width = 100%><tr>
				<td width = 15% align = left class="auto-style5">
				<font size = 2 class="auto-style5">HA Status: <%= strHAStatus%></td></font>
			<% strSQL2 = "SELECT * from tblPortals where Haz_ID = " & strH & ""	
	     	 Set MyRec2 = Server.CreateObject("ADODB.Recordset")
			MyRec2.Open strSQL2, Session("ConnectionString"), adOpenForwardOnly
			If not MyRec2.eof then %>
			
			
			</font>
			
			<td  align = center class="auto-style5">
			<a href="Approve.asp?id=<%= strid%>"><span class="auto-style5">Approve Hazard Assessment</span></a><font face="Arial"><span class="auto-style5">&nbsp;
			
			</span>
			
			</td>
			<% else %>
			<td  align = center class="auto-style9"><font color = red>
			A Portal must be Entered before this Hazard Assessment can be approved</font></td>
						<% 
						MyRec2.close
						end if%>
			<td align = right class="auto-style5">
			<font size = 2 class="auto-style5">
			HA Approver 1: <%= strApprover1%>&nbsp;&nbsp;Approver 2: <%= strApprover2%>&nbsp;</td>
			<td align = right class="auto-style9"><a href="CESearch.asp">Return</tr></table>
			
			
			</font></p>
				<table width = 100% cellspacing="1" class="auto-style2">
			<thead>
			    <tr>
   		<th class="auto-style6">Section</th>
			      <th class="auto-style6">Description          </th>
 				<th class="auto-style6">Date    </th>
                		<th class="auto-style6">Author            </th>
                		<th class="auto-style3">
						<font size = 1 class="auto-style10"> Compartment or <br>Portal </th>
                     
              <th class="auto-style6">Approver 1          </th>
 				<th class="auto-style3"><font size = 1 class="auto-style10"> Approver 1 <br>Date    </th>
                		<th class="auto-style6">Approver 2 </th>
                		<th class="auto-style3">
						<font size = 1 class="auto-style10"> Approver 2 <br>Date       
              <th class="auto-style3"><font size = 1 class="auto-style10"> Approver <br>Comments </th></th>
                		
                       <th class="auto-style6">Edit
                       
                        </th>
              	    </tr>
			    </thead>
			    <tbody>
	
        		        <% While not MyRec.EOF %>
              			    <tr>
		<td align = "center" class="auto-style4"><font size = 1 face = Arial>
		<span class="auto-style5"><span class="auto-style7"><% = MyRec.fields("Section") %>&nbsp;</span></span></td>
		<td align = "left" class="auto-style2"><font size = 1 face = Arial>
		<span class="auto-style5"><span class="auto-style7"><% = MyRec.fields("Change_Desc") %>&nbsp;</span></span></td>
		<td align = "left" class="auto-style2"><font size = 1 face = Arial>
		<span class="auto-style5"><span class="auto-style7"><% = MyRec.fields("Change_Date") %>&nbsp;</span></span></td>

		<td align = "left" class="auto-style2"><font size = 1 face = Arial>
		<span class="auto-style5"><span class="auto-style7"><% = MyRec.fields("Change_Name") %>&nbsp;</span></span></td>
                	
		<td align = "center" class="auto-style2">
		<font size = 1 class="auto-style10"><% = MyRec.fields("Compartment_name") %>&nbsp;<% = MyRec.fields("Portal_name") %></td>
		<td align = "left" class="auto-style2"><font size = 1 face = Arial>
		<span class="auto-style5"><span class="auto-style7"><% = MyRec.fields("Approver1") %>&nbsp;</span></span></td>

		<td align = "left" class="auto-style2">
		<font size = 1 class="auto-style10"><%= MyRec.fields("Accept_reject_1") %> &nbsp;<% = MyRec.fields("Approver1_date") %></td>
	
		<td align = "left" class="auto-style2"><font size = 1 face = Arial> 
		<span class="auto-style5"><span class="auto-style7"> <% = MyRec.fields("Approver2") %>&nbsp;</span></span></td>

		<td align = "left" class="auto-style2"><font size = 1 face = Arial>
		<span class="auto-style5"><span class="auto-style7"><%= MyRec.fields("Accept_reject_2") %>&nbsp;<% = MyRec.fields("Approver2_date") %>&nbsp;</span></span></td>
		<td align = "left" class="auto-style2"><font size = 1 face = Arial>
		<span class="auto-style5"><span class="auto-style7"><% = MyRec.fields("Comments") %>&nbsp;</span></span></td>

					
		<% if MyRec.fields("Section") = "1" or MyRec.fields("Section") = "2"  or MyRec.fields("Section") = "3" then %>
         <td class="auto-style4"><a href="<%= SCRIPT_NAME %>?action=edit&id=<%= MyRec.Fields("CID").Value %>&s=<%= strid%>&h=<%= strHAStatus%>">Edit</a></td>
 					<% else %>
	<td align = "left" class="auto-style4">&nbsp;</td>
	<% end if %>
 </tr>
                            
              	    <% 
				      MyRec.MoveNext
                 			WEND
                 			MyRec.Close
              	    %>

			    </tbody> </table>
		<%					
			End Select %>
		
              <p align = left><font face = arial size = 2>
              			<a href="Approval_Delete.asp?id=<%= strid%>">
			  <span class="auto-style10">Delete Approver</span></a>&nbsp;</p>  </font>             
      	</body>

<!--#include file="footer.inc"-->