																

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Mobile Pricing - Purchase Orders to Confirm</TITLE>
<style type="text/css">
.auto-style1 {
	font-family: Arial, Helvetica, sans-serif;
}
.auto-style2 {
	border-width: 0;
}
.auto-style3 {
	border: 1px solid #C0C0C0;
	background-color: #E7E7E7;
}
.auto-style4 {
	text-align: center;
	border: 1px solid #C0C0C0;
	background-color: #E7E7E7;
}
.auto-style5 {
	text-align: center;
}
</style>
</head>

<!--#include file="classes/asp_cls_DataAccessAIG.asp"-->
<!--#include file="classes/asp_cls_headerAIG.asp"-->
<!--#include file="classes/asp_cls_SessionStringAIG.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->



<% Dim MyRec, strsql, MyConn, strUserType

 strPO = request.querystring("p")
strVendor = Trim(request.querystring("v"))

    strUserType = ""
 
       If Session("EmployeeID") = "C97338" or Session("EmployeeID") = "B63398" or Session("EmployeeID") = "B96138" or Session("EmployeeID") = "U04211" then
    strUserType = "B"
    end if
    If strUserType = "" then

    Response.write ("<br><br><font face = arial size = 3><b>You do not have authorization to view this page</b></font>")
   else
    BuildScreen()
    end if 
 set objGeneral = new ASP_CLS_General

if objGeneral.IsSubmit() Then
	Call SaveData() 

End if

    %>
    <%Sub Buildscreen

if len(strPO) > 3 then 
 strsql = "SELECT tblPricing.* FROM tblPricing "_
&" WHERE Site = 'MOBILE' AND Price Is Not Null  AND Do_not_invoice Is Null and Confirmed='No' and PO_Nbr >= " & strPO & ""
else
 strsql = "SELECT tblPricing.* FROM tblPricing "_
&" WHERE Site = 'MOBILE' AND Price Is Not Null  AND Do_not_invoice Is Null and Confirmed='No' and SAP_Vendor = '" & strVendor & "'"
end if

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


%>

<body>
<br>
 <form name="form1" action="Mobile_Pricing_to_confirm.asp?p=<%= strPO %>&v=<%= strVendor %>" method="post" >	
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>

<tr>
<td align = center><b>
<font face="arial" size="4" >Mobile Recovered Paper Purchase Orders to Confirm</font></b></span></td>
<td align = center class="auto-style1"><a href="Mobile_pricing_to_confirm_Select.asp">
<strong>RETURN</strong></a></td>
<td><input type="submit" value="Submit"></td>


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=75% class = "auto-style2" align = center>  
	    <tbody class="auto-style5">
	 <tr class="tableheader">
	<td class="auto-style3"><strong></strong></td>
	<td class="auto-style3"  >  <font face="Arial" size="2"><strong>PO #</strong></font></td>
	<td class="auto-style3"  >  <font face="Arial" size="2"><strong>Vendor Number</strong></font></td>
		<td class="auto-style3"  >  <font face="Arial" size="2"><strong>Vendor Name</strong></font></td>
		<td class="auto-style3"  >  <font face="Arial" size="2"><strong>Generator</strong></font></td>
        <td class="auto-style3"  > <font face="Arial" size="2"><strong>Price</strong></font></td>
             <td class="auto-style4"  > <font face="Arial" size="2"><strong>Confirm</strong></font></td>
		


	</tr>

 <% Dim ii
       ii = 0
      while not  MyRec.eof 
    strAIGPO = MyRec.fields("PO")
    strsql2 = "SELECT distinct PO from tblCars where date_received > '01/01/2023' and PO = '" & strAIGPO & "'"
 
	Set rs = Server.CreateObject("ADODB.Recordset")
    	rs.Open strSQL2, Session("ConnectionString2")
	if not rs.eof then
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
	<% if strUsertype = "D" then %>
			<td>   <font size="2" face="Arial"> &nbsp;</font></td>
			<% else %>
				<td> <font size="2" face="Arial"><a href="AIG_Delete_pricing.asp?id=<%= MyRec.fields("ID") %>&p=m&v=<%= strVendor %>&po=<%= strPO %>">Delete</a></td>
 
<% end if %>

	<td  >      <font size="2" face="Arial">        <%= MyRec.fields("PO")%>&nbsp;</font></td>
      	<td  >      <font size="2" face="Arial">        <%= MyRec.fields("Vendor_nbr")%>&nbsp;</font></td>
      		<td  >      <font size="2" face="Arial">        <%= MyRec.fields("SAP_Vendor")%>&nbsp;</font></td>
      			<td  >      <font size="2" face="Arial">        <%= MyRec.fields("Generator")%>&nbsp;</font></td>
			<td  >        <font size="2" face="Arial">        <%= MyRec.fields("Price")%>&nbsp;</font></td>
					<% if strUsertype = "D" then %>
			<td>   <font size="2" face="Arial"> &nbsp;</font></td>
			<% else %>
	<td class="auto-style5"> <input type="checkbox" name="<%= MyRec("PO") %>" value="ON"></td>	
	<% end if %>
	</tr>

 <% rs.close
 end if
       ii = ii + 1
       MyRec.MoveNext
     Wend
     MyRec.close
    %>
</table>
</form>

<% End Sub %>
<%   Function SaveData()

if len(strPO) > 3 then 
 strsql = "SELECT tblPricing.* FROM tblPricing "_
&" WHERE Site = 'MOBILE' AND Price Is Not Null  AND Do_not_invoice Is Null and Confirmed='No' and PO_Nbr >= " & strPO & ""
else
 strsql = "SELECT tblPricing.* FROM tblPricing "_
&" WHERE Site = 'MOBILE' AND Price Is Not Null  AND Do_not_invoice Is Null and Confirmed='No' and SAP_Vendor = '" & strVendor & "'"
end if
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    while not MyREc.eof
    
    If Request.form(MyREc("PO")) = "ON" then
    strMyPO = MyRec("PO")
    strsql2 = "Update tblPricing set Confirmed = 'Yes' where PO = '" & strMyPO & "'"
  	 set MyRec2 = new ASP_CLS_DataAccess
        MyRec2.ExecuteSql strSql2 

    end if
    MyREc.movenext
    wend
    MyRec.close
  
REsponse.redirect("Mobile_pricing_to_Confirm_Select.asp")
end Function %>


<!--#include file="AIGfooter.inc"-->