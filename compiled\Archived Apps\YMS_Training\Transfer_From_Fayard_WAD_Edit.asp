<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Modify Wadding Transfer from Fayard</title>
<style type="text/css">
.style3 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
.style4 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	text-align: right;
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
}
.style7 {
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
}
.style8 {
	font-weight: bold;
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
}
.style9 {
	color: #000000;
}
.auto-style1 {
	font-family: Arial;
	font-size: x-small;
}
.auto-style2 {
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
	text-align: right;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID, objMOC, rstFiber, strCarrier, strSAP, objEPS, strFactor
      
    Dim strTrailer, strSpecies, strESpecies, rstSpecies, strGrade
    Dim strReleaseNbr
    Dim strRECNbr
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strGenerator
    Dim strGenCity
    Dim strGenState
    Dim strOther, strR, strsql3, strTransTrailer, strBrokeDescription

       strId  = Trim(Request.QueryString("id")) 
       
       strsql = "Select tblCars.* from tblCars where CID = " & strid


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")


	If not Myrec.eof then

 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then


 
 
		Call SaveData() 

end if
%>



<body><br>
<table width = 100%><tr><td width = 33%></td><td align = center width = 34%><b><font face="Arial" size="4">
Transfer Wadding from Fayard Warehouse</font></b></td><td align = right width = 33%>
	&nbsp;</td></tr></table>



<form name="form1" action="Transfer_From_Fayard_WAD_Edit.asp?id=<%= strid %>" method="post">
<div align="center">
<table cellspacing="0" bordercolor="#C0C0C0" bgcolor="#FFFFE8" style="width: 75%;" cellpadding="0" class="style3">
<tr>
    <td style="width: 361px" class="style7">&nbsp;</td>

    <td class="style7">&nbsp;</td>
  </tr>


      <td align = right style="width: 361px" class="style8">
  	<span class="auto-style1">Warehouse Ticket Number</span><font face="Arial" size="2">:&nbsp;</font></td>
<td  align = left class="style7">

      <input type="text" name="WH_Ticket" size="15" value = "<%= MyRec("WH_Ticket") %>" tabindex="1"></td></tr>


<tr>
    <td style="width: 361px" class="style7">&nbsp;</td>

    <td class="style7">&nbsp;</td>
  </tr>


      <td align = right style="width: 361px" class="style8">
  <font face="Arial" size="2">Transfer Trailer Number:&nbsp;</font></td>
<td  align = left class="style7">

      <input type="text" name="Trans_Trailer" size="15" value = "<%= MyRec("Transfer_Trailer_nbr") %>" tabindex="2"></td></tr>
      
       <tr><td style="width: 361px" class="style7">&nbsp;</td>
  <td class="style7">&nbsp;</td></tr>
<tr>
    <td style="width: 361px" class="style8">  
	<p align="right">  <font face="Arial" size="2">Carrier:&nbsp;</font></td>
    <td class="style7">  <select name="Carrier" style="font-weight: 700" tabindex="3">
 	<option value="" selected>  Select Carrier (Required)</option>
 	<% strsql = "Select Carrier,C_Description, Weight from tblCarrier order by Carrier"
 	   	 Set MyRec4 = Server.CreateObject("ADODB.Recordset")
   	 MyRec4.Open strSQL, Session("ConnectionString")
   	 while not MyRec4.eof %>
   	 <option <% if MyRec("Trans_Carrier") = MyRec4("Carrier") then %> selected <% end if %> value="<%= MyRec4("Carrier") %>"><%= MyRec4("Carrier") %>&nbsp;<%= MyRec4("C_Description") %>&nbsp;<%= MyRec4("Weight") %></option>"

 <%MyRec4.Movenext
 wend
 MyRec4.close %>
     </select></td>
  </tr>
        <tr>
    <td style="width: 361px" class="style7">&nbsp;</td>

    <td class="style7">&nbsp;</td>
  </tr>
  <tr>
    <td class="style8" style="width: 361px">  
	<p align="right">  <font face="Arial" size="2">SAP #:&nbsp;</font></td>
    <td class="style7">    <select name="SAP" tabindex="4">
  <% strsql = "Select SAP, Type from tblBrokeSAP where Category = 'WADDING' order by Type"
  
    Set MyRec4 = Server.CreateObject("ADODB.Recordset")
    MyRec4.Open strSQL, Session("ConnectionString")
    While not MyRec4.eof %>
    <option <% if MyRec("SAP_NBR") = MyRec4("SAP") then %> selected <% end if %> value="<%= MyRec4("SAP") %>"><%= MyRec4("SAP") %>&nbsp;<%= MyREc4("Type") %></option>
<% Myrec4.movenext
wend
MyRec4.close %>
	</select>   
 </td>
  </tr>
          <tr>
    <td style="width: 361px" class="style7">&nbsp;</td>

    <td class="style7">&nbsp;</td>
  </tr>

  <tr>
    <td class="style4" style="width: 361px"><strong>Rolls:&nbsp;&nbsp;&nbsp; </strong></td>

    <td class="style8"><font size="2" face = arial>
      <input type="text" name="Rolls" size="11" style="width: 36px" tabindex="6" value="<%= MyRec("Roll_Count") %>"> </font></td>
  </tr>

    <tr>
    <td align="right" style="height: 50px; width: 361px" class="style8">
	<font face="Arial" size="2">Enter WEIGHT (lbs) from WH:&nbsp;&nbsp;&nbsp; </font></td>

    <td style="height: 50px" class="style7">

     
      <input type="text" name="Weight" size="11" style="width: 106px" tabindex="7" value="<%= MyRec("Net") %>"></td>

  </tr>





    <tr>
    <td style="width: 361px" class="style7">&nbsp;</td>

    <td class="style7">&nbsp;</td>
  </tr>





       <tr>
          <td  align = right height="27" style="width: 361px" class="style8" >
    <font face="Arial" size="2">Date Transferred:&nbsp;</font></td>
<td align = left height="27" class="style7">

      <input type="text" name="Date_Received" size="15" value = "<%= MyRec("Date_Received") %>" tabindex="8"></td></tr>

  <tr>
    <td style="width: 361px" class="style7">&nbsp;</td>

    <td align = left class="style7">&nbsp;</td>
  </tr>

  <tr>
        <td  align = right height="27" style="width: 361px" class="style8" >
 <font face="Arial" size="2">Comments/Notes: </td>

    <td align = left class="style7">   <font face="Arial" color="teal" size="4">   
	<input name="Other_Comments" size="25" value = '<%= MyRec("Other_Comments") %>' style="font-weight: 700; width: 563px;" tabindex="9"></font></td>
  </tr>

  <tr>
    <td style="width: 361px" class="style7">&nbsp;</td>

    <td align = left class="style7">&nbsp;</td>
  </tr>

  <tr>
    <td style="width: 361px" class="style7">&nbsp;</td>

    <td align = left class="style7"><Input name="Update" type="submit" Value="Submit" ></td>
  </tr>
</table>
<% end if 
MyRec.close %>
</div>

</form>
</body>
<%


 Function SaveData()

strTrailer = Request.form("Trans_Trailer")
strCarrier = Replace(Request.form("Carrier"), "'", "''")

strDateReceived = Request.form("Date_received")

 

Dim strRightnow, strTons
strRightnow = Now()
if len(Request.form("Weight")) > 0 then
strTons = Request.form("Weight")
else
strTons = 0
end if

 If len(request.form("Rolls")) > 0 then  
strRolls = request.form("Rolls")
else
strRolls = 0
end if

strsap = request.form("SAP")
 
  strsql = "Select  Type from tblBrokeSAP where SAP = '" & strSAP & "'"
  
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    If not MyRec.eof then
    strDescription = MyRec("Type")
    else
    strDescription = ""
    end if
 	strOther = Replace(Request.form("Other_Comments"), "'", "''") 
 
strsql = "Update tblCars set Date_Received = '" & strDateReceived & "', Transfer_Trailer_nbr = '" & strTrailer & "', Transfer_Date = '" & strDateReceived & "', "_
&" Trans_Carrier = '" & strCarrier & "', Species = '" & strDescription & "', SAP_Nbr = '" & strSAP & "', Tons_received = '" & strTons & "', Net = '" & strTons & "', "_
&" Roll_Count = " & strRolls & ", WH_Ticket = '" & request.form("WH_Ticket") & "', WAD_Description =  '" & strDescription & "', "_
&" Other_Comments = '" & strOther & "' where CID = " & strid


	
	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
		 	 MyConn.Execute strSQL
			MyConn.Close

  '  Response.write ("strsql: " & strsql)
  

  Response.redirect ("Edit_Fayard_WAD_Shuttle.asp")
 


End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->