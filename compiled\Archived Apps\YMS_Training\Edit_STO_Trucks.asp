																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Edit Fiber Trucks in Yard</TITLE>
<style type="text/css">
.auto-style1 {
	text-align: center;
}
</style>
</head>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->




<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate

strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -7, strtdate)
strRdate = "1/31/2019"

    strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "' and User_type = 'A'"


    Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL3, Session("ConnectionString")
	If not MyRec2.eof then
	stradmin = "OK"
	else
	stradmin = ""
	end if
	MyRec2.close

strsql = "SELECT tblCars.* FROM tblCars "_
&"  WHERE (Entry_Page = 'EnterSTOWaddingReceipt' or Entry_page = 'EnterSTOWadReceipt' or Entry_page = 'STO_Broke_Receipt' "_
&"  or Entry_page = 'STO_Receipt' or Entry_page = 'Generic_STO_Receipt' or Entry_page = 'Generic_STO_WAD_Receipt')  "_
&"   and (Location='yard' or Location = 'YARD')  and date_received > '" & strRdate & "' order by Release_nbr"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Edit STO Trailers in Yard</b></font></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1>  
	 <tr class="tableheader">
<td>&nbsp;</td>
			<td  > <p align="center">       <font face="Arial" size="1">Print<br> Receipt</font></td>
			<td  > <p align="center">       <font face="Arial" size="1">STO<br>Delivery #</font></td>
	
		<td  ><font face="Arial" size="1"><b>Trailer</b></font></td>
		<td  ><font face="Arial" size="1"><b>Carrier</b></font></td>
		<td  ><font face="Arial" size="1">Species</font></td>
    
		<td  ><font face="Arial" size="1">Vendor</font></td>
		<td  >       <font face="Arial" size="1">PO Number</font></td>
 
		<td align="center">       <font face="Arial" size="1">Bales</font></td>
		<td  align="center">       <font face="Arial" size="1">Gross<br> Weight</font></td>
		<td  align="center"> <font face="Arial" size="1">Tare<br> Weight</font></td>
		<td   align="center"><font face="Arial" size="1">Tons<br> Received</font></td>
		<td class="auto-style1"  >  <font face="Arial" size="1">Date<br> Received</font></td>
		<td  >  <font size="1" face="Arial">Other</font></td>
	<td  > <font size="1" face="Arial">Delete<br>Receipt</font></td>
	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
  
    <%  if MyRec("Entry_page") = "EnterSTOWaddingReceipt"   then %>
        <td class="auto-style1"> <font size="1" face="Arial"><a href="EditSTOWaddingReceipt.asp?cid=<%= MyRec.fields("CID") %>">Edit</a></td>
        <% elseif MyRec("Entry_page") = "EnterSTOWADReceipt" or MyRec("Entry_page") = "EnterSTOReceipt" then %>
           <td class="auto-style1"> <font size="1" face="Arial"><a href="EditSTOWadReceipt.asp?cid=<%= MyRec.fields("CID") %>">Edit</a></td>
<% elseif MyRec("Entry_Page") = "STO_Broke_Receipt"  or MyRec("Entry_Page") = "STO_Receipt" then %>
 <td class="auto-style1"> <font size="1" face="Arial"><a href="Edit_STO_Broke_Receipt.asp?cid=<%= MyRec.fields("CID") %>">Edit</a></td>
 <% elseif MyRec("Entry_Page") = "Generic_STO_Receipt" then %>
 <td class="auto-style1"> <font size="1" face="Arial"><a href="Edit_Generic_STO_Receipt.asp?cid=<%= MyRec.fields("CID") %>">Edit</a></td>
 <% elseif MyRec("Entry_Page") = "Generic_STO_WAD_Receipt" then %>
 <td class="auto-style1"> <font size="1" face="Arial"><a href="Edit_Generic_STO_WAD_Receipt.asp?cid=<%= MyRec.fields("CID") %>">Edit</a></td>

<% end if %>
	
			 

	<td align = center> <font size="1" face="Arial"><a href="STO_Wadding_Truck_receipt.asp?id=<%= MyRec.fields("CID") %>"><%= MyRec.fields("CID")%></a></td>
	<td class="auto-style1"  ><font size="1" face="Arial"><%= MyRec.fields("STO_Number")%>&nbsp;</font></td>

	<td  > <font size="1" face="Arial"><b><%= MyRec.fields("Trailer")%></font></b></td>
			<td  ><font size="1" face="Arial"><b><%= MyRec.fields("Carrier")%>&nbsp;</font></b></td>


		<td  ><font face="Arial" size="1"> <%= MyRec.fields("Species")%></font></td>
		<td><font size="1" face="Arial"><%= MyRec.fields("Vendor")%>&nbsp;</font></td>
		<td  >        <font size="1" face="Arial">        <%= MyRec.fields("PO")%>&nbsp;</font></td>
 
	
		<td  >  <font size="1" face="Arial">  <b>
			<% if len(MyRec("Bales_RF")) > 0 then
			if MyRec("Bales_RF") > 0 then%>
			<%= MyRec.fields("Bales_RF")%>
			<% else %>	
			
			<%= MyRec.fields("Bales_VF")%>
			<% end if 
			else %>		
			
			<%= MyRec.fields("Bales_VF")%>

			<% end if %>&nbsp;</b></font></td>

		<td align = right >		 <font size="1" face="Arial">        <%= MyRec.fields("Gross_Weight")%>&nbsp;</font></td>
		<td align = right >		 <font size="1" face="Arial">        <%= MyRec.fields("Tare_Weight")%>&nbsp;</font></td>
		<td align = right <% if MyRec.fields("Tons_received") = 21  or MyRec.fields("Weigh_required") = "W" then %>bgcolor = pink <% end if %>  >
				 <font size="1" face="Arial">        <%= MyRec.fields("Tons_received")%>&nbsp;<%= MyRec.fields("Weigh_required") %></font></td>
		<td  align="center" >		 <font size="1" face="Arial">        <%= MyRec.fields("Date_Received")%></font></td>
 
		<td  >	 <font size="1" face="Arial">        <%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>
<td align = center >	 <font size="1" face="Arial"><a href="ReceiptDelete.asp?id=<%= Myrec.fields("CID")%>"> Delete</a></font></td>
 
	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>


<!--#include file="Fiberfooter.inc"-->