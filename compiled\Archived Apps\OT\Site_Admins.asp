																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE> Administrator list</TITLE>

<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_Session.asp"-->
<!--#include file="classes/asp_cls_DataAccess.asp"-->


<% 

Dim MyRec, strsql, MyConn, strPost
 Dim MyS, MySQ, strOK, strsite

STRSQL = "Select EMP_ID from tblOT_Admin where EMP_ID = '" & Session("EmployeeiD") & "'  "

Set MyS = Server.CreateObject("ADODB.Recordset") 
   		Set MyS = Server.CreateObject("ADODB.RecordSet")
		MyS.Open strSQL, Session("ConnectionString")
If not MyS.eof then
strPost = "OK"
else 
strPost = ""
end if
MyS.close

if Session("EmployeeID") = "C97338" then 
strPost = "OK"
end if


strsql = "SELECT tblOT_Admin.* from tblOT_Admin order by EMP_Name"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>
<style type="text/css">
.style1 {
	border: 1px solid #000000;
}
.style2 {
	border: 1px solid #C0C0C0;
}
.style3 {
	text-align: center;
	border: 1px solid #C0C0C0;
}
.style5 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style6 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	text-align: center;
}
.style7 {
	font-size: small;
}
.style8 {
	border-width: 1px;
}
</style>
</head>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=60%  border=1 align = center>
<tr>
 <TD align = left style="width: 15%" class="style8"><font size="2" face = arial>
	<span class="style7"><strong>
<% if strPost = "OK" then%></strong>

 <a href="Site_Admin_Add.asp"><strong>Add New</strong></a>
 <strong>
 <% else %>
 &nbsp;</strong></span>
 <% end if %></font></td>
<td align = center><b>
<font face="arial" size="4" >  Administrator List</font></b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </td>
<td align = center>&nbsp;</td>


</tr>
	    </table>
	
	
	<TABLE align = center style="width: 45%" class="style1">  
	 <tr bgcolor="#FFFFCC">
<td  class="style5"></td>
	<td  align="left" class="style6" >  <strong>&nbsp;Name</strong></td>
	<td class="style6" >  <strong>Employee ID</strong></td>
	<td  class="style5"></td>



	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
       strTeamEMP_ID = ""
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
	<td style="height: 30px" class="style3"> <font size="2" face="Arial">
	<% if strPost = "OK" Then %>
	<a href="Site_Admin_Edit.asp?id=<%= MyRec.fields("ID") %>">Edit</a>
	<% else %>
	&nbsp;
	<% end if %></td>	
    
	<td  align="left" class="style2" > <font size="2" face="Arial"> <%= MyRec.fields("EMP_Name")%>&nbsp;</font></td>
          <td  class="style3"> <font size="2" face="Arial">   <%= MyRec.fields("EMP_ID")%>&nbsp;</font>
       
</td>




          <td style="height: 30px; width: 97px;" class="style3"> <font size="2" face="Arial">
	<% if strPost = "OK" Then %>
<a href="Site_Admin_Delete.asp?id=<%= MyRec.fields("ID") %>">Delete</a>
	<% else %>
	&nbsp;
	<% end if %></td>	
	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
     MyRec.close
    %>
</table>




<!--#include file="footer.inc"-->

