																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Species coming from Sapism.txt</TITLE>

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 



<% Dim MyRec, strsql, strstartdate
strStartdate = formatdatetime(now(),2)

strsql = "SELECT * from tblBrokeSap order by Category, Type"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<style type="text/css">
.style1 {
	font-family: Arial;
	font-size: x-small;
}
.style2 {
	font-family: Arial;
}
</style>
</head>

<body>

<% if Session("EmployeeID") = "C97338" or Session("EmployeeID") = "B48888" or Session("EmployeeID") = "B55548" or Session("EmployeeID") = "B38763" or Session("EmployeeID") = "B48943" or Session("EmployeeID") = "B53909"  then %>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>


<td align = left class="style2">STO Items</td>
<td align = right>&nbsp;</td>

<td align = right><font face="Arial"><a href="SAPISM_add.asp"><b>Add New</b></a>&nbsp;</td>


</tr>
	    </table>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=60% class = "tablecolor1" border=1 align = center>  
	 <tr class="tableheader">
<td>&nbsp;</td>

				<td  align = left class="style1">     D<font size="2">escription</font></td>
		<td class="style1"  > <font face="Arial" size="2">SAP #</font></td>
			<td class="style1"  > <font face="Arial" size="2">UOM</font></td>
		<td  align = left class="style1">  Category</font></td>
		<td align = right>&nbsp;</td>
	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td> <font size="2" face="Arial">

<a href="SAPISM_Edit.asp?id=<%= MyRec.fields("ID") %>">

Edit</a></td>

	<td  ><font size="2" face="Arial"><%= MyRec.fields("Type")%></font></td>
	
<td  ><font size="2" face="Arial"><%= MyRec.fields("SAP")%></font></td>
<td  ><font size="2" face="Arial"><%= MyRec.fields("UOM")%>&nbsp;</font></td>
	<td  ><font size="2" face="Arial"><%= MyRec.fields("Category")%></font></td>
<td> <font size="2" face="Arial"><a href="SAPISM_Delete.asp?id=<%= MyRec.fields("ID") %>">

Delete</a></td>
	
</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>
<% else %>
<p align="center"><font face="arial" size="3"><b>You do not have authorization to view this page</b></font></p>
<% end if %><!--#include file="Fiberfooter.inc"-->