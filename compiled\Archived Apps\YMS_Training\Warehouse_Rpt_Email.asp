
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Warehouse Activity </TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<%
	Dim strsQL, rstEquip
	strBegDate = DateSerial(Year(now()), Month(now()), 1) 
	if datepart("d", Date()) = 1 then
	strBegDate = DateSerial(Year(now()), Month(now()) -1, 1) 
	end if 
	strEndDate = Dateadd("d", -1, Date())

 strsql = "SELECT  count(tblcars.cid) as CountofcID, sum(net) as SumofNet FROM tblCars  where (PMO_nbr = 'DC' or PMO_Nbr = 'MERCHANTS') "_
&" and date_received >= '" & strBegDate & "' and date_received <= '" & strEndDate & "'"
				Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   strCount = MyRec("CountofCID")
   end if

 strsql = "SELECT  count(tblcars.cid) as CountofcID, sum(net) as SumofNet FROM tblCars  where (PMO_nbr = 'DC') "_
&" and date_received >= '" & strBegDate & "' and date_received <= '" & strEndDate & "'"
				Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   strFromDC = MyRec("CountofCID")
   end if

 strsql = "SELECT  count(tblcars.cid) as CountofcID, sum(net) as SumofNet FROM tblCars  where ( PMO_Nbr = 'MERCHANTS') "_
&" and date_received >= '" & strBegDate & "' and date_received <= '" & strEndDate & "'"
				Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   strFromM = MyRec("CountofCID")
   end if

 strsql = "SELECT  count(tblcars.cid) as CountofcID, sum(net) as SumofNet FROM tblCars  where (Location = 'DC WHSE' or Location = 'MERCHANTS') "_
&" and date_received >= '" & strBegDate & "' and date_received <= '" & strEndDate & "'"
				Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   strToCount = MyRec("CountofCID")
   end if

 strsql = "SELECT  count(tblcars.cid) as CountofcID, sum(net) as SumofNet FROM tblCars  where (Location = 'DC WHSE') "_
&" and date_received >= '" & strBegDate & "' and date_received <= '" & strEndDate & "'"
				Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   strToDC = MyRec("CountofCID")
   end if

 strsql = "SELECT  count(tblcars.cid) as CountofcID, sum(net) as SumofNet FROM tblCars  where ( Location = 'MERCHANTS') "_
&" and date_received >= '" & strBegDate & "' and date_received <= '" & strEndDate & "'"
				Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   strToM = MyRec("CountofCID")
   end if
   
   strToCountR = 0
   strToDCR = 0
   StrTOMR = 0
    strsql = "SELECT  count(tblcars.cid) as CountofcID, sum(net) as SumofNet FROM tblCars  where (Location = 'DC WHSE' or Location = 'MERCHANTS') and Carrier = 'RAIL' "_
&" and date_received >= '" & strBegDate & "' and date_received <= '" & strEndDate & "'"
				Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   strToCountR = MyRec("CountofCID")
   end if

 strsql = "SELECT  count(tblcars.cid) as CountofcID, sum(net) as SumofNet FROM tblCars  where (Location = 'DC WHSE') and Carrier = 'RAIL' "_
&" and date_received >= '" & strBegDate & "' and date_received <= '" & strEndDate & "'"
				Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   strToDCR = MyRec("CountofCID")
   end if

 strsql = "SELECT  count(tblcars.cid) as CountofcID, sum(net) as SumofNet FROM tblCars  where ( Location = 'MERCHANTS') and Carrier = 'RAIL' "_
&" and date_received >= '" & strBegDate & "' and date_received <= '" & strEndDate & "'"
				Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   strToMR = MyRec("CountofCID")
   end if
   
   strTocount = strTocount + (strTocountR*2)
   strToDC = strToDC + (strToDCR *2)
   strTOM = strToM + (strTOMR *2 )


 %>
   <style type="text/css">
.style1 {
	text-align: left;
}
.style2 {
	font-family: Arial, Helvetica, sans-serif;
	text-align: left;
	font-size: x-small;
}
.style3 {
	font-family: Arial, Helvetica, sans-serif;
}
.style4 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.style5 {
	text-decoration: underline;
}
.style6 {
	text-align: left;
	font-size: x-small;
}
.style7 {
	font-size: x-small;
}
</style>
</head><br> 
<%
Set objMail = Server.CreateObject("CDO.Message")

		
			objMail.From = "<EMAIL>"
			strEmailto ="<EMAIL>"
	
			objMail.To = strEmailto		
		'objMail.To = "<EMAIL>"

			objMail.Subject = "Warehouse Activity Report through " & strEndDate & " "

strbody = "<table style=width: 100% ><tr><td ><font face=arial size=2><strong>" &  Now()& "</strong></td></tr>"

strbody = strbody & "<tr><td><strong><font face=arial size=2>Date Range: " &  strBegDate & " - " &  strEndDate & "</strong></td></tr>"
strbody = strbody & "<tr><td>&nbsp;</td></tr>"
strbody = strbody & "<tr><td><font face=arial size=2 color=navy><b>Note:  Quantities below are Truck Loads.  RAIL CARS are included below as 1 Rail Car is equal to 3 Truck loads for volume comparison.</b></font></font></td></tr><tr>"
strbody = strbody & "<tr></tr></table><br>"

strbody = strbody & " <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=0 align = center><tr>"
strbody = strbody & "<td colspan=2 ><b><font face=Arial size=2>Total Movements FROM: " & strCount & "</font></td></tr>"
strbody = strbody & " <tr> <td colspan =2 bgcolor = white>&nbsp;</td></tr>"
strbody = strbody & " <tr><td width=3% >&nbsp;</td> <td align=left><b><font face=Arial size=2><u>Total From DC: " &  strFromDC & "</u><br>"
strBlue = "%" & "700483" & "%"

   strsql = "SELECT  Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.PMO_Nbr)='DC')) and Date_received <= '" & strEndDate & "' "_
&" And Left([Transfer_trailer_nbr],4)<>'BLUE' And Left([Transfer_trailer_nbr],6)<>'700483' and (Species = 'Wetlap' or Species = 'P-MORFF')   "_
&" and Left([Transfer_trailer_nbr],3)<>'RED' And Left([Transfer_trailer_nbr],7)<>'7731105' "_
&"  order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then 
   
strbody = strbody & "WETLAP:  " & MyRec("CountofCid") & "<br>"
  MyRec.close
end if



  strsql = "SELECT tblCars.Species, Tier_Rating, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.PMO_Nbr)='DC')) and Date_received <= '" & strEndDate & "' "_
&" And Left([Transfer_trailer_nbr],4)<>'BLUE' And Left([Transfer_trailer_nbr],6)<>'700483'   "_
&" and Species ='KCOP'"_
&" GROUP BY tblCars.Species, Tier_rating order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   while not MyRec.eof 
   strbody = strbody & UCASE(MyRec("Species")) & "-" &  MyRec("Tier_Rating") & ":" & MyRec("CountofCid") & "<br>"
 MyRec.movenext
wend
MyRec.close
end if 


  strsql = "SELECT tblCars.Species, Tier_Rating, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.PMO_Nbr)='DC')) and Date_received <= '" & strEndDate & "' "_
&" And Left([Transfer_trailer_nbr],4)<>'BLUE' And Left([Transfer_trailer_nbr],6)<>'700483'   "_
&" and Species ='OF'"_
&" GROUP BY tblCars.Species, Tier_rating order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   while not MyRec.eof 
   strbody = strbody & UCASE(MyRec("Species")) & "-" &  MyRec("Tier_Rating") & ":" & MyRec("CountofCid") & "<br>"
 MyRec.movenext
wend
MyRec.close
end if 




 strsql = "SELECT tblCars.Species, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.PMO_Nbr)='DC')) and Date_received <= '" & strEndDate & "' "_
&" And Left([Transfer_trailer_nbr],4)<>'BLUE' And Left([Transfer_trailer_nbr],6)<>'700483'   "_
&" and Left([Transfer_trailer_nbr],3)<>'RED' And Left([Transfer_trailer_nbr],7)<>'7731105' "_
&" and Species <> 'Wetlap' and Species <> 'P-MORFF' and Species <> 'KCOP' and Species <> 'OF'"_
&" GROUP BY tblCars.Species order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   while not MyRec.eof 
 strbody = strbody & " " &  UCASE(MyRec("Species")) & ":  " &  MyRec("CountofCid") & "<br>"
  MyRec.movenext
wend
MyRec.close
end if



 strsql = "SELECT tblCars.Species, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.PMO_Nbr)='DC')) and Date_received <= '" & strEndDate & "' "_
&" And (Left([Transfer_trailer_nbr],4)='BLUE' OR Left([Transfer_trailer_nbr],6)='700483'  "_
&" or Left([Transfer_trailer_nbr],3)='RED' And Left([Transfer_trailer_nbr],7)='7731105') "_

&" GROUP BY tblCars.Species order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then   
strbody = strbody & "CONTAINER:  " &  MyRec("CountofCid") & "<br>"
 
MyRec.close
end if
 strbody = strbody & "</font><br></td></tr>"
 strbody = strbody & "<tr><td width=3% >&nbsp;</td><td align=left><b><font face=Arial size=2><u>Total From Merchants: " & strFromM & "</u><br>"
 
 
strsql = "SELECT  Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.PMO_Nbr)='MERCHANTS')) and Date_received <= '" & strEndDate & "' "_
&" And Left([Transfer_trailer_nbr],4)<>'BLUE' And Left([Transfer_trailer_nbr],6)<>'700483' and (Species = 'Wetlap' or Species = 'P-MORFF')   "_
&" and Left([Transfer_trailer_nbr],3)<>'RED' And Left([Transfer_trailer_nbr],7)<>'7731105' "
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   if MyRec("CountofCID") > 0 then 
strbody = strbody & "WETLAP:  " & MyRec("CountofCid") & "<br>"
end if
MyRec.close
end if




  strsql = "SELECT tblCars.Species, Tier_Rating, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.PMO_Nbr)='MERCHANTS')) and Date_received <= '" & strEndDate & "' "_
&" And Left([Transfer_trailer_nbr],4)<>'BLUE' And Left([Transfer_trailer_nbr],6)<>'700483'   "_
&" and Species ='KCOP'"_
&" GROUP BY tblCars.Species, Tier_rating order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   while not MyRec.eof 
   strbody = strbody & UCASE(MyRec("Species")) & "-" &  MyRec("Tier_Rating") & ":" & MyRec("CountofCid") & "<br>"
 MyRec.movenext
wend
MyRec.close
end if 


  strsql = "SELECT tblCars.Species, Tier_Rating, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.PMO_Nbr)='MERCHANTS')) and Date_received <= '" & strEndDate & "' "_
&" And Left([Transfer_trailer_nbr],4)<>'BLUE' And Left([Transfer_trailer_nbr],6)<>'700483'   "_
&" and Species ='OF'"_
&" GROUP BY tblCars.Species, Tier_rating order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   while not MyRec.eof 
   strbody = strbody & UCASE(MyRec("Species")) & "-" &  MyRec("Tier_Rating") & ":" & MyRec("CountofCid") & "<br>"
 MyRec.movenext
wend
MyRec.close
end if 


 strsql = "SELECT tblCars.Species, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.PMO_Nbr)='MERCHANTS')) and Date_received <= '" & strEndDate & "' "_
&" And Left([Transfer_trailer_nbr],4)<>'BLUE' And Left([Transfer_trailer_nbr],6)<>'700483'   "_
&" and Left([Transfer_trailer_nbr],3)<>'RED' And Left([Transfer_trailer_nbr],7)<>'7731105' and Species <> 'Wetlap' and Species <> 'P-MORFF' and Species <> 'KCOP' and Species <> 'OF' "_
&" GROUP BY tblCars.Species order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   while not MyRec.eof 
 strbody = strbody & " " &  UCASE(MyRec("Species")) & ":  " &  MyRec("CountofCid") & "<br>"
  MyRec.movenext
wend
MyRec.close
end if
 strsql = "SELECT tblCars.Species, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.PMO_Nbr)='MERCHANTS')) and Date_received <= '" & strEndDate & "' "_
&" And (Left([Transfer_trailer_nbr],4)='BLUE' OR Left([Transfer_trailer_nbr],6)='700483'  "_
&" or Left([Transfer_trailer_nbr],3)='RED' or Left([Transfer_trailer_nbr],7)='7731105') "_
&" GROUP BY tblCars.Species order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then 
strbody = strbody & "CONTAINER:  " &  MyRec("CountofCid") & "<br>" 
MyRec.close
end if 

 strbody = strbody & "</font><br></td></tr>"
  

 strbody = strbody & "<tr><td colspan=2>&nbsp;</font></td></tr><tr>"
 strbody = strbody & "<td colspan=2 ><b><font face=Arial size=2>Total Movements TO: " & strToCount & "</font></td></tr>"
 strbody = strbody & "<tr><td colspan=2>&nbsp;</font></td></tr><tr>"
strbody = strbody & " <tr><td width=3% >&nbsp;</td> <td align=left><b><font face=Arial size=2><u>Total To DC: " &  strToDC & "</u><br>"
strWLDC = 0
strsql = "SELECT  Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE Date_received >= '" & strBegDate & "' AND Location='DC WHSE' and Date_received <= '" & strEndDate & "' "_
&"  and (Species = 'Wetlap' or Species = 'P-MORFF') "
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then 
   if MyRec("CountofCid") > 0 then 
   strWLDC = MyREc("CountofCID")
   
   
  strsql2 = "SELECT  Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE Date_received >= '" & strBegDate & "' AND Location='DC WHSE' and Date_received <= '" & strEndDate & "' "_
&"  and (Species = 'Wetlap' or Species = 'P-MORFF') and carrier = 'RAIL' "
 	Set MyRec2 = Server.CreateObject("ADODB.Recordset")
	MyRec2.Open strSQL2, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec2.eof then 
   strWLDCR = MyRec2("CountofCID")
   strWLDC = strWLDC + (strWLDCR * 2)
   else
   strWLDCR = 0
   end if
   MyRec2.close
 
   
 if strWLDCR = 0 then  
   
strbody = strbody & "WETLAP:  " & strWLDC & "<br>"
else
strbody = strbody & "WETLAP:  " & strWLDC & "<br>"
if strWLDCR = 1 then 
strbody = strbody & "WETLAP Rail Cars:  " & (strWLDCR * 3) &  " (" & strWLDCR & " Rail Car)<br>"
else
strbody = strbody & "WETLAP Rail Cars:  " & (strWLDCR * 3) &  " (" & strWLDCR & " Rail Cars)<br>"
end if
end if
 end if
MyRec.close
end if

       
    
strSpeciescount = 0
  strScount = 0
    
    
    strsql = "SELECT tblCars.Species, Tier_rating, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.Location)='DC WHSE')) and Date_received <= '" & strEndDate & "' "_
&" and Species ='KCOP' "_
&" GROUP BY tblCars.Species, Tier_Rating order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   while not MyRec.eof
   strRcount = 0
   strTier = MyRec("Tier_Rating")
   strSCount = MyREc("CountofCID")
  strsql2 = "Select count(CID) as CountofCID from tblCars  WHERE Date_received>= '" & strBegDate & "' AND Location='DC WHSE' and Date_received <= '" & strEndDate & "' "_
&" and Species = 'KCOP' and Tier_Rating = '" & strTier & "' and Carrier = 'RAIL'"
   	Set MyRec2 = Server.CreateObject("ADODB.Recordset")
	MyRec2.Open strSQL2, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec2.eof then
   strScount = strScount -  MyRec2("CountofCID")  
   strYes = "Yes"
   strRcount = MyRec2("CountofCID")
   else
   strYes = "No"
   strRcount = 0
   end if
   MyRec2.close
If strRcount = 0 then

 strbody = strbody & "KCOP- " & strTier & ":  " &  strScount & " <br>"
 else
   strbody = strbody & "KCOP- " & strTier & ":  " &  strScount & " <br>"
 if strRcount = 1 then
  strbody = strbody & "KCOP- " & strTier & "  Rail Cars:  " &  (strRcount * 3) & " (" & strRcount & " Rail Car)<br>"
 else
    strbody = strbody & "KCOP- " & strTier & "   Rail Cars:  " &  (strRcount * 3) & " (" & strRcount & " Rail Cars)<br>"
 end if
 end if
 MyRec.movenext
wend
MyRec.close
end if
 
strSpeciescount = 0
  strScount = 0
    
    
    strsql = "SELECT tblCars.Species, Tier_rating, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.Location)='DC WHSE')) and Date_received <= '" & strEndDate & "' "_
&" and Species ='OF' "_
&" GROUP BY tblCars.Species, Tier_Rating order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   while not MyRec.eof
   strRcount = 0
   strTier = MyRec("Tier_Rating")
   strSCount = MyREc("CountofCID")
  strsql2 = "Select count(CID) as CountofCID from tblCars  WHERE Date_received>= '" & strBegDate & "' AND Location='DC WHSE' and Date_received <= '" & strEndDate & "' "_
&" and Species = 'OF' and Tier_Rating = '" & strTier & "' and Carrier = 'RAIL'"
   	Set MyRec2 = Server.CreateObject("ADODB.Recordset")
	MyRec2.Open strSQL2, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec2.eof then
   strScount = strScount -  MyRec2("CountofCID")  
   strYes = "Yes"
   strRcount = MyRec2("CountofCID")
   else
   strYes = "No"
   strRcount = 0
   end if
   MyRec2.close
If strRcount = 0 then

 strbody = strbody & "OF- " & strTier & ":  " &  strScount & " <br>"
 else
   strbody = strbody & "OF- " & strTier & ":  " &  strScount & " <br>"
 if strRcount = 1 then
  strbody = strbody & "OF- " & strTier & " Rail Cars:  " &  (strRcount * 3) & " (" & strRcount & " Rail Car)<br>"
 else
    strbody = strbody & "OF- " & strTier & " Rail Cars:  " &  (strRcount * 3) & " (" & strRcount & " Rail Cars)<br>"
 end if
 end if
 MyRec.movenext
wend
MyRec.close
end if
  
    
    
strSpeciescount = 0
  strScount = 0
    
    
    strsql = "SELECT tblCars.Species, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.Location)='DC WHSE')) and Date_received <= '" & strEndDate & "' "_
&" and Species <> 'Wetlap' and  Species <> 'P-MORFF' and  Species <> 'KCOP' and  Species <> 'OF'  "_
&" GROUP BY tblCars.Species order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   while not MyRec.eof
   strRcount = 0
   strSpeciesCount = MyRec("Species")
   strSCount = MyREc("CountofCID")
  strsql2 = "Select count(CID) as CountofCID from tblCars  WHERE Date_received>= '" & strBegDate & "' AND Location='DC WHSE' and Date_received <= '" & strEndDate & "' "_
&" and Species = '" & strSpeciesCount & "' and Carrier = 'RAIL'"
   	Set MyRec2 = Server.CreateObject("ADODB.Recordset")
	MyRec2.Open strSQL2, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec2.eof then
   strScount = strScount -  MyRec2("CountofCID")  
   strYes = "Yes"
   strRcount = MyRec2("CountofCID")
   else
   strYes = "No"
   strRcount = 0
   end if
   MyRec2.close
If strRcount = 0 then

 strbody = strbody & " " &  UCASE(strSpeciesCount) & ":  " &  strScount & " <br>"
 else
  strbody = strbody & " " &  UCASE(strSpeciesCount) & ":  " &  strScount & " <br>"
 if strRcount = 1 then
  strbody = strbody & " " &  UCASE(strSpeciesCount) & " Rail Cars:  " &  (strRcount * 3) & " (" & strRcount & " Rail Car)<br>"
 else
   strbody = strbody & " " &  UCASE(strSpeciesCount) & " Rail Cars:  " &  (strRcount * 3) & " (" & strRcount & " Rail Cars)<br>"
 end if
 end if
 MyRec.movenext
wend
MyRec.close
end if
 
 strbody = strbody & "</font><br></td></tr>"
 
 

 
 
  strbody = strbody & "<tr><td colspan=2>&nbsp;</font></td></tr><tr>"
  
  
  
  
  
  
  
strbody = strbody & " <tr><td width=3% >&nbsp;</td> <td align=left><b><font face=Arial size=2><u>Total To MERCHANTS (Target <30): : " &  strToM & "</u><br>"
 strsql = "SELECT  Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE Date_received >= '" & strBegDate & "' AND Location='MERCHANTS' and Date_received <= '" & strEndDate & "' "_
&"  and (Species = 'Wetlap' or Species = 'P-MORFF') "
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then 
   if MyRec("CountofCid") > 0 then
   strW = MyRec("COuntofCID")
   strsql2 = "SELECT  Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE Date_received >= '" & strBegDate & "' AND Location='MERCHANTS' and Date_received <= '" & strEndDate & "' "_
&"  and (Species = 'Wetlap' or Species = 'P-MORFF') and Carrier = 'RAIL'"
	Set MyRec2 = Server.CreateObject("ADODB.Recordset")
	MyRec2.Open strSQL2, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec2.eof then 
   strW = strW + MyRec2("CountofCID")
   end if
   MyRec2.close

  
strbody = strbody & "WETLAP:  " & strW & "<br>"
end if
MyRec.close
end if
   

strSpeciescount = 0
  strScount = 0
    
    
    strsql = "SELECT tblCars.Species, Tier_rating, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.Location)='MERCHANTS')) and Date_received <= '" & strEndDate & "' "_
&" and Species ='KCOP' "_
&" GROUP BY tblCars.Species, Tier_Rating order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   while not MyRec.eof
   strRcount = 0
   strTier = MyRec("Tier_Rating")
   strSCount = MyREc("CountofCID")
  strsql2 = "Select count(CID) as CountofCID from tblCars  WHERE Date_received>= '" & strBegDate & "' AND Location='MERCHANTS' and Date_received <= '" & strEndDate & "' "_
&" and Species = 'KCOP' and Tier_Rating = '" & strTier & "' and Carrier = 'RAIL'"
   	Set MyRec2 = Server.CreateObject("ADODB.Recordset")
	MyRec2.Open strSQL2, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec2.eof then
   strScount = strScount - MyRec2("CountofCID") 
   strYes = "Yes"
   strRcount = MyRec2("CountofCID")
   else
   strYes = "No"
   strRcount = 0
   end if
   MyRec2.close
If strRcount = 0 then

 strbody = strbody & "KCOP-" & strTier & ":  " &  strScount & " <br>"
 else
   strbody = strbody & "KCOP-"  & strTier & ":  " &  strScount & " <br>"
 if strRcount = 1 then
  strbody = strbody & "KCOP-"  & strTier & "  Rail Cars:  " &  (strRcount * 3) & " (" & strRcount & " Rail Car)<br>"
 else
    strbody = strbody & "KCOP-" & strTier & " Rail Cars:  " &  (strRcount * 3) & " (" & strRcount & " Rail Cars)<br>"
 end if
 end if
 MyRec.movenext
wend
MyRec.close
end if

strSpeciescount = 0
  strScount = 0
    
    
    strsql = "SELECT tblCars.Species, Tier_rating, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.Location)='MERCHANTS')) and Date_received <= '" & strEndDate & "' "_
&" and Species ='OF' "_
&" GROUP BY tblCars.Species, Tier_Rating order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   while not MyRec.eof
   strRcount = 0
   strTier = MyRec("Tier_Rating")
   strSCount = MyREc("CountofCID")
  strsql2 = "Select count(CID) as CountofCID from tblCars  WHERE Date_received>= '" & strBegDate & "' AND Location='MERCHANTS' and Date_received <= '" & strEndDate & "' "_
&" and Species = 'OF' and Tier_Rating = '" & strTier & "' and Carrier = 'RAIL'"
   	Set MyRec2 = Server.CreateObject("ADODB.Recordset")
	MyRec2.Open strSQL2, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec2.eof then
  strScount = strScount - MyRec2("CountofCID")  
   strYes = "Yes"
   strRcount = MyRec2("CountofCID")
   else
   strYes = "No"
   strRcount = 0
   end if
   MyRec2.close
If strRcount = 0 then

 strbody = strbody & "OF-" & strTier & ":  " &  strScount & " <br>"
 else
   strbody = strbody & "OF-" & strTier & ":  " &  strScount & " <br>"
 if strRcount = 1 then
  strbody = strbody & "OF-" & strTier & " Rail Cars:  " &  (strRcount * 3) & " (" & strRcount & " Rail Car)<br>"
 else
    strbody = strbody & "OF-" & strTier & " Rail Cars:  " &  (strRcount * 3) & " (" & strRcount & " Rail Cars)<br>"
 end if
 end if
 MyRec.movenext
wend
MyRec.close
end if


    
  strSpeciescount = 0
  strScount = 0
    
    
    strsql = "SELECT tblCars.Species, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (((tblCars.Date_received)>= '" & strBegDate & "') AND ((tblCars.Location)='MERCHANTS')) and Date_received <= '" & strEndDate & "' "_
&" and Species <> 'Wetlap' and  Species <> 'P-MORFF'  and Species <> 'KCOP' and Species <> 'OF'"_
&" GROUP BY tblCars.Species order by CountofCID Desc"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   while not MyRec.eof
   strRcount = 0
   strSpeciesCount = MyRec("Species")
   strSCount = MyREc("CountofCID")
  strsql2 = "Select count(CID) as CountofCID from tblCars  WHERE Date_received>= '" & strBegDate & "' AND Location='MERCHANTS' and Date_received <= '" & strEndDate & "' "_
&" and Species = '" & strSpeciesCount & "' and Carrier = 'RAIL'"
   	Set MyRec2 = Server.CreateObject("ADODB.Recordset")
	MyRec2.Open strSQL2, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec2.eof then
  strScount = strScount- MyRec2("CountofCID")  
   strYes = "Yes"
   strRcount = MyRec2("CountofCID")
   else
   strYes = "No"
   strRcount = 0
   end if
   MyRec2.close
If strRcount = 0 then

 strbody = strbody & " " &  UCASE(strSpeciesCount) & ":  " &  strScount & " <br>"
 else
  strbody = strbody & " " &  UCASE(strSpeciesCount) & ":  " &  strScount & " <br>"
  if strRcount = 1 then 
  strbody = strbody & " " &  UCASE(strSpeciesCount) & " Rail Cars:  " &  (strRcount * 3) & " (" & strRcount & " Rail Car) <br>"
  else
    strbody = strbody & " " &  UCASE(strSpeciesCount) & " Rail Cars:  " &  (strRcount * 3) & " (" & strRcount & " Rail Cars) <br>"
  end if
 end if
 MyRec.movenext
wend
MyRec.close
end if







strBegDate = dateadd("d", -1, Date())
strEndDate = Date()  
 strbody = strbody & "</font><br></td></tr> </table><br>"
 strbody = strbody & "<font face=Arial size=2><strong>" &  strBegDate & " Movement</strong><br><br>"
strbody = strbody & "<TABLE cellSpacing=0 cellPadding=0 width=50% border=1 align = left> "
strbody = strbody & "<tr><td align=left><font face=Arial size=2>	<strong>To/From</strong></font></td>"
strbody = strbody & "<td  align=left ><font face=Arial size=2><strong>Species</strong></font></td>"
strbody = strbody & "<td  align=left ><font face=Arial size=2><strong>From Location</strong></font></td>"
strbody = strbody & "<td  align=left ><font face=Arial size=2><strong>To Location</strong></font></td>"
strbody = strbody & "<td  align=left ><font face=Arial size=2><strong>Trailer</strong></font></td>"
strbody = strbody & "<td  align=left ><font face=Arial size=2><strong>Transfer Date</strong></font></td>"
strbody = strbody & "<td  align=left ><font face=Arial size=2><strong>Tier</strong></font></td></tr>"
 strsql = "SELECT tblCars.* FROM tblCars "_
&" WHERE Date_received >= '" & strBegDate & "' AND (PMO_Nbr='MERCHANTS' or PMO_Nbr = 'DC') and Date_received < '" & strEndDate & "' "_
&" Order by PMO_Nbr, Species"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly

       ii = 0
   
   If not MyRec.eof then
       while not MyRec.Eof
    if ( ii mod 2) = 0 Then 
   strbody = strbody & "<tr bgcolor=#F1F1F8>"
    else
       strbody = strbody & "<tr bgcolor=#FFFFE8>"
       
  end if
   strbody = strbody & "<td  align=left><font face = arial size = 1>FROM</td>"
  strbody = strbody & " <td  align=left ><font face = arial size = 1>" & MyRec.fields("Species") & "&nbsp;</td>"
  strbody = strbody & " <td  align=left ><font face = arial size = 1>"
  
if len(MyREC("PMO_NBR")) > 1 then 

 strbody = strbody & " " & MyRec.fields("PMO_Nbr") & " "
 else 
 strbody = strbody & "Mill"
 end if
  strbody = strbody & "</td>"
  strbody = strbody & " <td  align=left ><font face = arial size = 1>" & MyRec.fields("Location") & "&nbsp;</td>"
    strbody = strbody & " <td  align=left ><font face = arial size = 1>" & MyRec.fields("Transfer_Trailer_Nbr") & "&nbsp;</td>"
      strbody = strbody & " <td  align=left ><font face = arial size = 1>" & MyRec.fields("Date_received") & "&nbsp;</td>"
          strbody = strbody & " <td  align=left ><font face = arial size = 1>" & MyRec.fields("Tier_Rating") & "&nbsp;</td>"

       ii = ii + 1
      
       MyRec.MoveNext
     Wend
     end if
     
strsql = "SELECT tblCars.* FROM tblCars "_
&" WHERE Date_received>= '" & strBegDate & "' AND (Location ='MERCHANTS' or Location = 'DC WHSE') and Date_received < '" & strEndDate & "' "_
&" Order by Location, Species"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly

       ii = 0
   
   If not MyRec.eof then
       while not MyRec.Eof
    if ( ii mod 2) = 0 Then 
   strbody = strbody & "<tr bgcolor=#F1F1F8>"
    else
       strbody = strbody & "<tr bgcolor=#FFFFE8>"
       
  end if
   strbody = strbody & "<td  align=left><font face = arial size = 1>TO</td>"
  strbody = strbody & " <td  align=left ><font face = arial size = 1>" & MyRec.fields("Species") & "&nbsp;</td>"
  strbody = strbody & " <td  align=left ><font face = arial size = 1>"
  
if len(MyREC("PMO_NBR")) > 1 then 

 strbody = strbody & " " & MyRec.fields("PMO_Nbr") & " "
 else 
 strbody = strbody & "Mill"
 end if
  strbody = strbody & "</td>"
  strbody = strbody & " <td  align=left ><font face = arial size = 1>" & MyRec.fields("Location") & "&nbsp;</td>"
    strbody = strbody & " <td  align=left ><font face = arial size = 1>" & MyRec.fields("Trailer") & "&nbsp;</td>"
      strbody = strbody & " <td  align=left ><font face = arial size = 1>" & MyRec.fields("Date_received") & "&nbsp;</td>"
           strbody = strbody & " <td  align=left ><font face = arial size = 1>" & MyRec.fields("Tier_Rating") & "&nbsp;</td>"

       ii = ii + 1
      
       MyRec.MoveNext
     Wend
     end if





	objMail.HTMLBody = strbody
' objMail.Send
	
			Set objMail = nothing 
			If request.querystring("s") = "i" then
				
Response.redirect("Send_yard_email.asp")

			else
			 Response.redirect("Broke_email.asp")
			   end if
			   
			
			   
			   %>