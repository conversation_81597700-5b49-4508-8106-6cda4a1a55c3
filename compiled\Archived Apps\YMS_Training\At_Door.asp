	<% Response.buffer = False %>																

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>At Door Trailers</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->



<% Dim MyRec, strsql



strsql = "SELECT * FROM tblCars WHERE tblcars.location ='AT DOOR' and date_unloaded is null ORDER BY Trailer "
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0  align = center cellPadding=0 border=0>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><b>
<font face="arial" size="4" >Trailers At Door</font></b></span></td>
<td align = center>&nbsp;</td>


</tr>
	    </table>


	<p>&nbsp;</p>
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=35% align = center class = "tablecolor1" border=0>  
	 <tr class="tableheader">

	<td  ><font face="Arial" size="1"><b>Trailer</b></font></td>
	
		<td align = center  ><font face="Arial" size="1"><b>Door</b></font></td>

	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    <td  > <font size="2" face="Arial"> <%= MyRec.fields("Trailer")%></font></td>

		<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Door")%></font></td>
		
	</tr>
	<%
	
       ii = ii + 1
       MyRec.MoveNext
    


Wend %>


</table>