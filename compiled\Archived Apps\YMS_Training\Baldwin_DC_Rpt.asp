
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Load Activity FROM Warehouse</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

<%  
	Dim strsQL, rstEquip

   	Dim objEquipSearch, rstTotals, strCount, strDed, strNet, strTons, rstTotalReceived, strTR, objNew, rstSpecies
  	Dim  strVendor, strBegDate, strEndDate, strSpecies, strDateReceived, strDateAdded, strAVG
 	Dim  rstVendor, strFIQ, strBAQ, rstGenerator, strGenerator, strVendorname, strGeneratorname
  	Dim objGeneral, gcount, strCountTwo, strSum, strSum1, strNetSum, strDepdate, strLocation, strRented
   	
   	Dim intDirection
  	Dim intPageNumber
   	Dim strPageNav

   intPageNumber = 1

   set objGeneral = new ASP_CLS_General

  if objGeneral.IsSubmit() Then
    Call GetFormData()
    Select Case intDirection
      Case 10: 'goto mocinfo.asp
  
      Case Else:
        Call LoadSearchResults()
    End Select
  else
    intDirection = 0
  end if
Call GetData()
%>

<script language="javascript">
 
  function GotoMOC(MOCID)
  {
    document.forms["form1"].elements["tbl_ID"].value = MOCID;
    document.forms["form1"].elements["Direction"].value = 10;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }

  function Search()
  {
    
    document.forms["form1"].elements["Direction"].value = 1;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }

  function NextPage()
  {
    document.forms["form1"].elements["Direction"].value = 5;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }
  function PrevPage()
  {
    document.forms["form1"].elements["Direction"].value = 6;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }
</script>


<style type="text/css">
.auto-style1 {
	border-color: #C0C0C0;
	border-width: 1px;
	background-color: #E7EBFE;
}
.auto-style2 {
	font-weight: bold;
	border-color: #C0C0C0;
	border-width: 1px;
	background-color: #E7EBFE;
}
.auto-style3 {
	border-style: solid;
	border-width: 1px;
}
.auto-style4 {
	font-size: x-small;
}
.auto-style5 {
	font-size: x-small;
	text-align: left;
}
</style>
</head>


<form name="form1" action="Baldwin_DC_rpt.asp" method="post">
 <input type="hidden" name="Direction" value="1" >
 <input type="hidden" name="tbl_ID" value="" >
 <input type="hidden" name=PageNumber value="<%=intPageNumber%>" >
 
<TABLE borderColor=white cellSpacing=0 cellPadding=0  width = 100% border=1 >  
  <tr>
  <td  align = left><b><font face = "Arial">Load Activity FROM Warehouse</font></b></td>
	<td align = right><font face="Arial"><b><a href="Loginscreen.asp">HOME</a></b></font></td></tr>
</table>


<TABLE borderColor=#808080 cellSpacing=0 cellPadding=0 id="table2" class="auto-style3" style="width: 100%">  

  <TR>
   
	<TD class="auto-style2" ><font face="Arial" size="2">Beg Date:</font></TD>
	<td class="auto-style1"><font face="Arial"><input name="Beg_Date" size="10" maxlength="10" value="<%=strBegDate%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633"></TD>



     <td class="auto-style2"><font size="2" face="Arial">&nbsp; End Date</font></td>
     <td class="auto-style1"><font face="Arial"><input name="End_Date" size="10" maxlength="10" value="<%=strEndDate%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633"></td>

     <td class="auto-style2"><font size="2" face="Arial">&nbsp; From Location&nbsp;&nbsp;&nbsp;&nbsp; </font><select size="1" name="From_Location">
	<option <% if strlocation = "DC" then %>selected <% end if %>>DC</option>
		<option <% if strlocation = "DF" then %>selected <% end if %>>DF</option>

		<option <% if strLocation = "MERCHANTS" then %>selected <% end if %>>MERCHANTS</option>
			<option <% if strlocation = "TRANSLOAD" then %>selected <% end if %>>TRANSLOAD</option>
		
		</select></td>

 

		<TD class="auto-style2"><font face="Arial" size="2">Species:</font></TD>
    <TD class="auto-style1">  <font face="Arial">   <select size="1" name="Species">
     <option value="">--- All ---</option>
       <%= objGeneral.OptionListAsString(rstSpecies, "Species", "Species", strSpecies) %>
     </select></font></td>  <TD class="auto-style1"> <input type="button" onClick="javascript:Search()" value="Search" caption="Search"></td>
</TR>

	</table>
</form>


  <% if objGeneral.IsSubmit() Then 

%>
   <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% align = center class="auto-style3">
    <tr><td colspan="7" bgcolor="white" ><font face="Arial" size="2"><b>Search Results</b> - Page <%=intPageNumber%></font></td>
       <td><a href="Baldwin_DC_Rpt_Excel.asp?b=<%= strBegDate %>&e=<%= strEndDate %>&l=<%= strlocation %>&s=<%= strSpecies %>">Export</a></td>
<% if intPageNumber = 1 then %>


 <td bgcolor = white align="center" ><b><font face="Arial" size="2">Count<br><%= strCount%>&nbsp; </font></td>
  <td bgcolor = white align="center" ><b><font face="Arial" size="2">Rented<br><%= strRented%>&nbsp;  </font></td>
    <td bgcolor = white align="center" ><b><font face="Arial" size="2">Rented %<br>
    <% if strCount > 0 and strRented > 0 then %>
    
    <%= round(strRented/strcount * 100,0)%>
    <% end if %>&nbsp;  </font></td>
        <td bgcolor = white  align="center" ><font face="Arial" size = 2><b>Total Net<br><%= strNetSum%>&nbsp;</b></font></b></td>
    <% else %>

    <% end if %>
    </tr>
    <tr><td colspan="16" bgcolor="white" align="right" ><%=strPageNav%>&nbsp;</td></tr>
      <tr class="tableheader">	
      <td  align="center" class="auto-style4" >
		<p align="left" class="auto-style4"><font face="Arial"><strong>Species</strong></font></td>

		<td  align="center" class="auto-style4" ><font face="Arial"><strong>From Location</strong></font></td>
		<td  align="center" class="auto-style4" ><font face="Arial"><strong>PMO</strong></font></td>
		<td  align="center" class="auto-style4" ><font face="Arial"><strong>Rental</strong></font></td>

	<td  align="center" class="auto-style4" ><font face="Arial"><strong>Transfer Trailer</strong></font></td>
	<td class="auto-style5" ><font face="Arial"><strong>Transfer Date</strong></font></td>
	<td  align="center" class="auto-style4" ><font face="Arial"><strong>Trans Unload</strong></font></td>
	<td  align="center" ><font face="Arial" size="1" class="auto-style4">
	<strong>Inv Depletion<br>Date</strong></font></td>
	<td  align="center" class="auto-style4" ><font face="Arial"><strong>Location</strong></font></td>

	<td  align="center" class="auto-style4" ><font face="Arial"><strong>Net</strong></font></td>


	<td  align="center" class="auto-style4" ><font  face="Arial"><strong>Other</strong></font></td>
	<td  align="center" class="auto-style4" ><font  face="Arial"><strong>Count</strong></font></td>
      
  	<%   strcount = 1
      Dim ii
       ii = 0
       while not rstEquip.Eof
      
    %>
    <% if ( ii mod 2) = 0 Then %>
      <tr bgcolor="#E7EBFE">
      
    <% else %>
      <tr bgcolor="white">
    <% end if %>
    
    <%    if len(rstEquip.fields("Inv_depletion_date")) > 2 then
   if formatdatetime(rstEquip.fields("Inv_depletion_date"),2) <> strDepdate then
       strcount = 1
       end if 
       end if %> 

<td  align="left" class="auto-style4" ><font face = "arial" size = "1">
<span class="auto-style4"><%=rstEquip.fields("Species")%>&nbsp;</span></td>

<td  align="center" ><font face = "arial" size = "1"><span class="auto-style4"><%=rstEquip.fields("PMO_Nbr")%>&nbsp;</span></td>
<td  align="center" ><font face = "arial" size = "1"><span class="auto-style4"><%=rstEquip.fields("Pnbr")%>&nbsp;</span></td>
<%     if rstEquip("Trailer") = "UNKNOWN" Then 
    strCheck = right(rstEquip("Transfer_trailer_nbr"),5)
    else
     strCheck = right(rstEquip("Trailer"),5)
	end if
    strsql2 = "select Trailer_Nbr from tblRentedTrailer where right(Trailer_Nbr,5) = '" & strCheck & "'"
    
    		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL2, Session("ConnectionString")
		If not MyRec.eof then
%>
<td  align="center" class="auto-style4" ><font face = "arial">YES&nbsp;</td>
<% else %>
<td  align="center" class="auto-style4" ><font face = "arial">&nbsp;</td>
<% end if 
MyRec.close %>

<td  align="center" class="auto-style4" ><font face = "arial" size = "1">
<span class="auto-style4"><%=rstEquip.fields("Transfer_Trailer_nbr")%>&nbsp;</span></td>

<td  align="left" ><font face = "arial" size = "1"><span class="auto-style4"><%=rstEquip.fields("Transfer_date")%>&nbsp;</span></td>
<td  align="center" ><font face = "arial" size = "1"><span class="auto-style4"><%=rstEquip.fields("Trans_unload_date")%>&nbsp;</span></td>
<td  align="center" ><font face = "arial" size = "1"><span class="auto-style4"><%=rstEquip.fields("Inv_depletion_date")%>&nbsp;</span></td>
<td  align="center" ><font face = "arial" size = "1"><span class="auto-style4"><%=rstEquip.fields("Location")%>&nbsp;</span></td>
<% if not isnull(rstEquip.fields("Net")) then %>
<td  align="center" class="auto-style4" ><font face = "arial" size = "1">
<span class="auto-style4"><%= formatnumber(rstEquip.fields("net"),3)%>&nbsp;</span></td>
<% else %>
<td  align="center" class="auto-style4" ><font face = "arial">&nbsp;</td>
<% end if %>



<td  align="center" class="auto-style4" ><font face = "arial" size = "1">
<span class="auto-style4"><%= rstEquip.fields("Other_Comments")%>&nbsp;</span></td>
<td  align="center" ><font face = "arial" size = "1"><span class="auto-style4"><%= strCount%>&nbsp;</span></td>


 </tr>
    <% 
       ii = ii + 1
       if len(rstEquip.fields("Inv_depletion_date")) > 2 then
          strDepDate = formatdatetime(rstEquip.fields("Inv_depletion_date"),2)
          end if
           strcount = strcount + 1
       rstEquip.MoveNext
     Wend
    %>
   </table>
<table>    <tr><td colspan="16" bgcolor="white" align="right"><%=strPageNav%>&nbsp;</td></tr></table>
<% end if %><% Function GetData()

   set objNew = new ASP_Cls_Fiber
          

		set rstSpecies = objNew.FiberSpecies()



End Function
   
 Function GetFormData()
      intDirection = cint(Request.Form("Direction")) 
       
	

	strBegDate = Request.form("Beg_date")
	strEndDate = Request.form("End_date")
	strSpecies = Request.form("Species")
	strLocation = Request.form("From_Location")


      intPageNumber = cint(Request.Form("PageNumber"))

 
    End Function

    Function LoadSearchResults()
      Dim objEquipSearch, objTotals

      if intDirection =1 Then
         intPageNumber = 1
      end if

      if intDirection = 5 Then
         intPageNumber = intPageNumber + 1
      end if
      if intDirection = 6 Then
         intPageNumber = intPageNumber - 1
      end if


	
	
	strGenerator = ""
	strSpecies = Request.form("Species")
	strLocation = Request.form("From_Location")
      set objEquipSearch = new ASP_Cls_Fiber
    

if strLocation = "TRANSLOAD" then
    Set rstEquip = Server.CreateObject("ADODB.Recordset")
if strSpecies = "" then
 
strsql2  = "SELECT tblCars.* from tblCars  where RC_CID > 0 "_
&" and Date_received  >= '" & strBegDate & "' and tblCars.Date_received <= '" & strEndDate & "' ORDER BY Date_received desc "

else
strsql2  = "SELECT tblCars.* FROM tblCars  where RC_CID > 0 and species = '" & strSpecies & "' "_
&" and Date_received  >= '" & strBegDate & "' and tblCars.Date_received <= '" & strEndDate & "' ORDER BY Date_received desc "

end if
    rstEquip.Open strSQL2, Session("ConnectionString"), adOpenDynamic
else
 set rstEquip = objEquipSearch.DCSearch(50, intPageNumber, strBegDate, strEndDate, strSpecies, strLocation)
 end if


If strLocation = "TRANSLOAD" Then 


Set rstTotals = Server.CreateObject("ADODB.Recordset")
if strSpecies = "" then
 
strsql  = "SELECT  count(tblcars.cid) as CountofcID, sum(net) as SumofNet FROM tblCars  where RC_CID > 0 "_
&" and Date_received  >= '" & strBegDate & "' and tblCars.Date_received <= '" & strEndDate & "'  "

else
strsql  = "SELECT  count(tblcars.cid) as CountofcID, sum(net) as SumofNet FROM tblCars  where RC_CID > 0 "_
&" and Date_received  >= '" & strBegDate & "' and tblCars.Date_received <= '" & strEndDate & "'  and Species = '" & strSpecies & "' "

end if
    rstTotals.Open strSQL, Session("ConnectionString"), adOpenDynamic
else

set rstTotals = objEquipSearch.DCSearch_totals(intPageNumber, strBegDate, strEndDate,  strSpecies, strLocation)

end if

Set rstTotalsR = Server.CreateObject("ADODB.Recordset")

If strLocation = "TRANSLOAD" Then 


	if strSpecies = "" then
 
	strsqlR  = "SELECT  Transfer_trailer_Nbr, Trailer FROM tblCars  where RC_CID > 0 "_
	&" and Date_received  >= '" & strBegDate & "' and tblCars.Date_received <= '" & strEndDate & "'  "

	else
	strsqlR  = "SELECT  Transfer_trailer_Nbr, Trailer FROM tblCars  where RC_CID > 0 "_
	&" and Date_received  >= '" & strBegDate & "' and tblCars.Date_received <= '" & strEndDate & "'  and Species = '" & strSpecies & "' "
	end if
else

	if strSpecies = "" then

	strsqlR = "SELECT  Transfer_trailer_nbr, trailer from tblCars  where PMO_Nbr = '" & strLocation & "' "_
 	&"  and Date_received  >= '" & strBegDate & "' and tblCars.Date_received <= '" & strEndDate & "'"
	else
	strsqlR = "SELECT  Transfer_trailer_nbr, trailer from tblCars  where PMO_Nbr = '" & strLocation & "' "_
 	&"  and Date_received  >= '" & strBegDate & "' and tblCars.Date_received <= '" & strEndDate & "' and Species = '" & strSpecies & "'"
	end if

end if

strRented = 0
    rstTotalsR.Open strSQLR, Session("ConnectionString"), adOpenDynamic
   
 if not rstTotalsR.eof then
    While not rstTotalsR.eof
    if rstTotalsR("Trailer") = "UNKNOWN" Then 
    strCheck = right(rstTotalsR("Transfer_trailer_nbr"),5)
    else
     strCheck = right(rstTotalsR("Trailer"),5)
	end if
    strsql2 = "select Trailer_Nbr from tblRentedTrailer where right(Trailer_Nbr,5) = '" & strCheck & "'"
    
    		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL2, Session("ConnectionString")
		If not MyRec.eof then
		strRented = strRented + 1
		end if
		MyRec.close
		
		rstTotalsR.movenext
		wend
		rstTotalsR.close
    end if




     if  not rstTotals.Eof Then
    ' strNetSum = round(rstTotals.fields("SumofNet"),3)
     strNetSum = rsttotals.fields("SumofNet")
      strCount = rstTotals.fields("CountofCID").value

      
	else
	strCount = 0
	strNetSum = 0

      
      end if 
if strLocation = "TRANSLOAD" then

else
     if ( not rstEquip.Eof) Then
      if ( intPageNumber < rstEquip.fields("TotalPage").value ) Then
        strPageNav = "<a href=javascript:NextPage()><B>Next Page</b></a>"
      end if
      if ( intPageNumber > 1 ) Then
         strPageNav = "<a href=javascript:PrevPage()><b>Previous Page</b></a>&nbsp;&nbsp;&nbsp;&nbsp;" & strPageNav
      end if
     end if
end if
    End Function
 %><!--#include file="Fiberfooter.inc"-->