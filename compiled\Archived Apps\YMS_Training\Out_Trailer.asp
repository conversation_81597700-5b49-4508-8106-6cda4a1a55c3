<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Out Trailer</title>
<style type="text/css">
.auto-style1 {
	border-color: #EAF1FF;
	border-width: 1px;
	background-color: #EAF1FF;
}
.auto-style2 {
	background-color: #EAF1FF;
}
.auto-style3 {
	font-weight: bold;
	background-color: #EAF1FF;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID, objMOC, rstFiber, strCarrier, strLocation
      
    Dim strTrailer
    Dim strReleaseNbr
    Dim strRECNbr
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived, strSpecies, strRail
    Dim strGenerator
    Dim strGenCity
    Dim strGenState
    Dim strOther, strR, strsql3, strTransTrailer

       strId  = Trim(Request.QueryString("id")) 


 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
	
	If Request.form("location") = "" then
	Response.write("<br><br><font face=Arial size== 3 color=red><b>Please select a location</font></b></font>")
 	else	
		Call SaveData() 
	end if	

	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to change the location of a Trailer.</font></br>")
	MyRec.close
	end if

  
end if

strsql = "SELECT tblCars.* from tblCars WHERE tblCars.CID = " & strid

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


    strTrailer = MyRec.fields("Transfer_Trailer_Nbr")
    strSpecies = MyRec.fields("Species")
    strCarrier = MyRec.fields("Trans_Carrier")
strDateReceived = formatdatetime(Now(),0)
strRail = MyRec("RC_CID")
strShredOCC = MyRec("Shred_OCC")
    

MyRec.close
Call getdata()
	

%>



<body><br>
<table width = 100%><tr><td width = 33%></td><td align = center width = 34%>
	<b><font face="Arial">Car-out Shuttle Trailer in YARD</font></b></td><td align = right>
	<font face="Arial"><b><a href="SelectTruckUnload.asp">RETURN</a></b></font></td></tr></table>



<form name="form1" action="Out_Trailer.asp?id=<%=strid%>" method="post">
<input type="hidden" name="Rail" value="<%= strRail %>">
<div align="center">
<table border="1" cellspacing="0" width="60%" bgcolor="#FOFOFF" style="border-collapse: collapse" cellpadding="0" align = center>

  <tr>
      <td  align = center bgcolor="#F0F0FF"  > 
   <b>
   <font face="Arial" size="2">Trailer:&nbsp;</font></b>&nbsp;&nbsp;

   <font face = arial><b> <%= strTrailer%>&nbsp;&nbsp;&nbsp;

 
 <font face="Arial" size="2">Carrier:</font>
    <%= strCarrier %>&nbsp;&nbsp;&nbsp; <font face="Arial" size="2">Species:</font>
     <%= strSpecies %> 
   </td></tr></table>
   </div>
<div align="center">
      <table border="2"  width="60%" bgcolor="#F0F0FF" style="border-collapse: collapse" cellpadding="2">

    <tr>

      <td  bgcolor="#FOFOFF" align = right bordercolor="#F0F0FF" class="auto-style2">
 <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF" class="auto-style2">

      &nbsp;</td></tr>
      
       <tr> <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF" class="auto-style2">&nbsp;</td>
 <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF" class="auto-style2">&nbsp;</td></tr>
  <tr>
   <td  bgcolor="#FOFOFF" align = right bordercolor="#F0F0FF" class="auto-style3">
  <font face="Arial" size="2">New Location:&nbsp;</font></td>

      <td  bgcolor="#FOFOFF" align = LEFT bordercolor="#F0F0FF" class="auto-style2">
	<font face="Arial">	
	<select size="1" name="location">
	<option value="" >Select Location</option>
	<option >BALDWIN</option>
	<option >BROKE CENTER</option>
	<option value="Cotton Street WH" >COTTON STREET WH</option>
	<option>DC WHSE</option>
	<option>DFF</option>
	<option>HERBERT ST</option>
 	<option>MERCHANTS</option>
 	<option value="Meyer Bldg">MEYER BUILDING</option>
	<option>MMS</option>
   	<option <% if strSpecies = "OCC" or strSpecies = "LPSBS" or strSpecies = "USBS" or StrSpecies = "HWM" or strSpecies = "MXP" or strSpecies = "SWL" or (strSpecies = "SHRED" and (strShredOCC = -1 or strShredOCC = True)) then %>selected<% end if %>>OCC</option>
   	<option>PJ WH</option>
	<option <% if  strSpecies = "KBLD" or strSpecies = "OF3" or strSpecies = "PMX" or strSpecies = "HBX" or (strSpecies = "SHRED"  and (strShredOCC = 0 or strShredOCC = False or strShredOCC = Null)) then %>selected<% end if %>>RF</option>
	<option>TM BASEMENT</option>
	</select></font>  </td>
</tr>

<tr>
   <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF" class="auto-style3">
	<p align="right">  <font face="Arial" size="2">&nbsp;</font></td>
      <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF" class="auto-style3">
 &nbsp;</td>
  </tr>
  <tr>
    <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF" class="auto-style2">&nbsp;</td>

 <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF" class="auto-style2">&nbsp;</td>
  </tr>



       <tr>
         <td  bgcolor="#FOFOFF" ALIGN = right bordercolor="#F0F0FF" class="auto-style3">
    <font face="Arial" size="2">Date Relocated:&nbsp;</font></td>
 <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF" align = LEFT class="auto-style2">

      <input type="text" name="Date_Received" size="23" value = "<%= strDateReceived%>"></td></tr>
<tr>
  <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF" class="auto-style2">&nbsp;</td>

    <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF" class="auto-style2">&nbsp;</td>
  </tr>

  <tr>
   <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF" class="auto-style2">&nbsp;</td>

    <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF" align = left class="auto-style2" ><Input name="Update" type="submit" Value="Submit" ></td>
  </tr>
</table>

</div>

</form>
</body>
<%
 Function GetData()
  set objMOC = new ASP_CLS_FIBER
        set rstFiber = objMOC.FiberCarrier()
        strDateReceived = formatdatetime(Now(),0)
End Function



 Function SaveData()
 
  strsql = "SELECT tblCars.* from tblCars WHERE tblCars.CID = " & strid

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


    strTrailer = MyRec.fields("Transfer_Trailer_Nbr")
    strSpecies = MyRec.fields("Species")
    strCarrier = MyRec.fields("Trans_Carrier")
    strDateSite = MyRec("Date_Received")


    MyRec.close


strLocation = Request.form("location")

If request.form("location") = "" then
response.write ("You must select a Location")
else
dim strnow
strnow = formatdatetime(Now(),0)
strRail = request.form("Rail")

strDateReceived = Request.form("Date_received")        

         strsql = "Update tblCars set Date_unloaded = '" & strDateReceived & "', Trans_unload_date = '" & strDateReceived & "',  Location = '" & strLocation & "' where CID = " & strid & ""
 
 	     set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
         If len(strRail) > 0 then
             strsql = "INSERT INTO tblMovement ( CID, Comment, DDate, Tdate, From_location, To_location, BID ) "_
		&" SELECT " & strid & ", 'OUT TRAILER - SHUTTLE', '" & strDateReceived & "', '" & strnow & "', 'YARD', '" & strlocation & "', '" & Session("EmployeeID") & "'"
        
           set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql
         
         else
       strsql = "INSERT INTO tblMovement ( CID, Comment, DDate, Tdate, From_location, To_location, BID ) "_
		&" SELECT " & strid & ", 'TRANSFER TRAILER', '" & strDateReceived & "', '" & strnow & "', 'YARD', '" & strlocation & "', '" & Session("EmployeeID") & "'"
        
           set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql
        end if

         
   If strLocation = "RF" or strLocation = "OCC" or  strLocation = "BROKE CENTER"  then
         strsql = "Update tblCars set Inv_depletion_date = '" & strDateReceived & "' where CID = " & strid & ""
 
 	     set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql
         end if

 

   
       

    
     Dim strEmailAdd
 if strCarrier = "RENT" or strCarrier = "LSPI" then


 strsql = "Select Email_add  from tblCarrier where  Carrier = '" & strCarrier & "'"
 
    	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")

	If not Myrec.eof then
	
	strEmailadd = MyRec.fields("Email_add")
	if strEmailadd = "<EMAIL>" then
	'skip
	else
 

              strEmailTo = strEmailadd
        	
             strEBCC = "<EMAIL>"
                 
			Set objMail = Server.CreateObject("CDO.Message")		
			objMail.From = "<EMAIL>"
			objMail.To = strEmailTo
			'objMail.BCC = strEBCC
			

objMail.Subject = "Trailer " & strTrailer & " Empty "
objMail.HTMLBody = "<font face = arial size = 2>Trailer<b> " & strTrailer & "</b> arrived at KC Mobile, AL  on " & strDateSite & " with " & strSpecies & " and was emptied on " & strDateReceived & "."

			' objMail.Send
			Set objMail = nothing
			
	end if  ' if has email address
	end if   ' if not eof
	end if ' if carrrier is rent or LSPI
 Response.redirect ("Transfer_Grading_entry.asp?id=" & strid)
  end if
End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->