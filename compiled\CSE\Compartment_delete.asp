<html>
<!--#include file="classes/asp_cls_SessionString.asp"-->


<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 6.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Delete Compartment</title>
</head>
<% dim strsql, MyRec


	

strid = Request.querystring("id")



 strsql =  "Delete from tblCompartment where ID = " & strid & ""
 Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 
			
			
strsql =  "Delete from tblPortals where Compartment_ID = " & strid & ""
 Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 
			
			


Response.write ("<body bgcolor = #D9E1F9><br><br><p align = center><font face = arial size = 2><b>The Compartment and associated Entry Portals have been deleted. </b><br><br> Please exit out of this window, and refresh the HA.<br><br> If you have added any information to your HA, <br>SAVE your data by clicking SUBMIT rather than refreshing the page, and it will refresh it when you save it.")

 %>
