
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">
<TITLE>Daily Inventory Report</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

<!--#include file="classes/asp_cls_sessionPolaris.asp"-->
	
	
	   <style type="text/css">
.style1 {
	font-family: Arial;
	font-size: x-small;
}
.style2 {
	font-size: x-small;
	background-color: #FFFFDD;
	text-align: center;
}
.style3 {
	background-color: #FFFFDD;
	font-size: x-small;
}
.style4 {
	border: 1px solid #F0F0F0;
}
.style14 {
	border: 1px solid #F0F0F0;
	text-align: center;
	background-color: #F3F3F3;
}
.style15 {
	font-family: Arial;
}
.style16 {
	font-size: x-small;
}
.style18 {
	border: 1px solid #000000;
}
.style21 {
	border: 1px solid #F0F0F0;
	font-family: Arial;
	font-size: x-small;
	text-align: center;
	background-color: #FFFFCC;
}
.style29 {
	border-left-color: #C0C0C0;
	border-left-width: 1px;
	border-right-color: #000080;
	border-right-width: 1px;
	border-top-color: #C0C0C0;
	border-top-width: 1px;
	border-bottom-color: #000080;
	border-bottom-width: 1px;
	background-color: #F0F0F0;
}
.style30 {
	border-color: #C0C0C0;
	border-width: 1px;
	text-align: center;
	font-family: Arial;
	font-size: x-small;
	background-color: #E8E8FF;
}
.style34 {
	border-color: #C0C0C0;
	border-width: 1px;
	text-align: center;
	font-family: Arial;
	font-size: x-small;
	background-color: #FAFBBF;
}
.style38 {
	border-color: #C0C0C0;
	border-width: 1px;
	text-align: center;
	font-family: Arial;
	font-size: x-small;
	background-color: #FFD7FF;
}
.style40 {
	border-left: 1px solid #C0C0C0;
	border-right: 1px solid #000080;
	border-top: 1px solid #C0C0C0;
	border-bottom: 1px solid #000080;
	background-color: #E8E8FF;
}
.style41 {
	border: 1px solid #E8E8FF;
	font-size: x-small;
}
.style44 {
	border: 1px solid #C0C0C0;
	text-align: center;
		font-family: Arial;
		font-size: x-small;
	background-color: #F3F3F3;
}
.style47 {
	border: 1px solid #F0F0F0;
	text-align: center;
	font-family: Arial;
	background-color: #F3F3F3;
}
.style48 {
	border: 1px solid #F0F0F0;
	font-family: Arial;
	font-size: xx-small;
	text-align: center;
	background-color: #FFD7FF;
}
.style49 {
	border: 1px solid #F0F0F0;
	font-family: Arial;
	font-size: xx-small;
	text-align: center;
	background-color: #E8E8FF;
}
.style50 {
	border: 1px solid #F0F0F0;
	background-color: #E8E8FF;
}
.style52 {
	border-left-color: #C0C0C0;
	border-left-width: 1px;
	border-right-color: #000080;
	border-right-width: 1px;
	border-top-color: #C0C0C0;
	border-top-width: 1px;
	border-bottom-color: #000080;
	border-bottom-width: 1px;
	background-color: #FFFFFF;
}
.style53 {
	border-left-color: #C0C0C0;
	border-left-width: 1px;
	border-right-color: #000080;
	border-right-width: 1px;
	border-top-color: #C0C0C0;
	border-top-width: 1px;
	border-bottom-color: #000080;
	border-bottom-width: 1px;
	background-color: #FFECFF;
}
.style55 {
	border: 1px solid #FFFFFF;
	text-align: center;
	font-family: Arial;
	font-size: x-small;
	background-color: #FCFDDB;
}
.style65 {
	text-align: center;
	font-size: x-small;
}
.style66 {
	border: 1px solid #E8E8FF;
	text-align: center;
	font-size: x-small;
}
.style67 {
	font-family: Arial;
	font-size: xx-small;
}
.style68 {
	font-size: xx-small;
}
.style69 {
	text-align: center;
}
.style70 {
	font-size: x-small;
	background-color: #00B0F0;
	text-align: center;
}
.style71 {
	font-size: x-small;
	background-color: #92D050;
	text-align: center;
}
.style72 {
	font-size: x-small;
	background-color: yellow;
	text-align: center;
}
.style73 {
	font-size: x-small;
	background-color: red;
	text-align: center;
}


</style>
</head>
<%  
	Dim strsQL, rstDaily, strBegDate, strEndDate, strcount, objPro, strCdate, strKCOP, strOther, strOCC, strMonthName, strMonthDay, strDayofweek
	Dim strdate, objDAC, strOCCRail, strOtherRail, strKCOPRail, strOCCAvg,  strKCOPAvg, strnow, MyConn, strTday7
	Dim strdate1, strYdate
	Dim strOB, MyRec2, strsql2
	Dim KCOP_one, KCOP_Two, KCOP_Three, KCOP_Four, KCOP_Five, KCOP_Six, KCOP_Seven
	DIm OCC_one, OCC_Two, OCC_Three, OCC_Four, OCC_Five, OCC_six, OCC_Seven, MyRec8, strSQL8
	
	strnow = formatdatetime(Now(),2)
	strBegdate = dateadd("d", -7, strnow)
	strEnddate = formatdatetime(now())
	  strTday = formatdatetime(now(),2)
  strYear = datepart("yyyy", strTday)
  strTD = datepart("d", strTday)
  strTM = datepart("m", strTDay)
  strTday1 = dateadd("d", 1, strTday)
    strTD1 = datepart("d", strTday1)
  strTM1 = datepart("m", strTDay1)
  strTday2 = dateadd("d", 2, strTday)
    strTD2 = datepart("d", strTday2)
  strTM2 = datepart("m", strTDay2)
  strTday3 = dateadd("d", 3, strTday)
    strTD3 = datepart("d", strTday3)
  strTM3 = datepart("m", strTDay3)
  
    strTday4 = dateadd("d", 4, strTday)
    strTD4 = datepart("d", strTday4)
  strTM4 = datepart("m", strTDay4)


  strTday5 = dateadd("d", 5, strTday)
    strTD5 = datepart("d", strTday5)
  strTM5 = datepart("m", strTDay5)


  strTday6 = dateadd("d", 6, strTday)
    strTD6 = datepart("d", strTday6)
  strTM6 = datepart("m", strTDay6)

strTday7 = dateadd("d", 7, strTday )
  
  
  strsql = "Select Tissue_Bales from tblBrokeConsumption where INV_Date = '" & strTday7 & "'"
     Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
if not MyRec.eof then
' do nothing
else

  	
  	strsql8 = "Select  Tissue_Bales, Tissue_Gaylords, White_towel_Bales, Brown_Towel_Bales from tblBrokeConsumption where ID = 1 "
  	    Set MyRec8 = Server.CreateObject("ADODB.Recordset")
   MyRec8.Open strSQL8, Session("ConnectionString") 
   TDefault = MyRec8.fields("Tissue_Bales")
   TGDefault = MyRec8.fields("Tissue_Gaylords")
   TWDefault = MyRec8.fields("White_towel_Bales")
   TBDefault = MyRec8.fields("Brown_towel_Bales")

   MyRec8.close

  	
	strsql9 =  "INSERT INTO tblBrokeConsumption ( INV_Date, Tissue_Bales, Tissue_Gaylords, White_towel_Bales, Brown_Towel_Bales) "_
	&" SELECT '" & strTday7 & "', " & TDefault & ", " & TGDefault & ", " & TWDefault & ", " & TBDefault & " "
	
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL9
			MyConn.Close

  
end if
MyRec.close
   
     strTday7 = dateadd("d", 8, strTday )
  

  
  strsql = "Select  Tissue_Bales, Tissue_Gaylords, White_towel_Bales, Brown_Towel_Bales from tblBrokeConsumption where INV_Date = '" & strTday7 & "'"
     Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
if not MyRec.eof then
' do nothing
else

  	
  	strsql8 = "Select  Tissue_Bales, Tissue_Gaylords, White_towel_Bales, Brown_Towel_Bales from tblBrokeConsumption where ID = 1 "
  	    Set MyRec8 = Server.CreateObject("ADODB.Recordset")
   MyRec8.Open strSQL8, Session("ConnectionString") 
   TDefault = MyRec8.fields("Tissue_Bales")
   TGDefault = MyRec8.fields("Tissue_Gaylords")
   TWDefault = MyRec8.fields("White_towel_Bales")
   TBDefault = MyRec8.fields("Brown_towel_Bales")

   MyRec8.close

  	
	strsql9 =  "INSERT INTO tblBrokeConsumption ( INV_Date, Tissue_Bales, Tissue_Gaylords, White_towel_Bales, Brown_Towel_Bales) "_
	&" SELECT '" & strTday7 & "', " & TDefault & ", " & TGDefault & ", " & TWDefault & ", " & TBDefault & " "
	
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL9
			MyConn.Close

  
end if
MyRec.close
   
   
     strTday7 = dateadd("d", 9, strTday )
  

  
  strsql = "Select  Tissue_Bales, Tissue_Gaylords, White_towel_Bales, Brown_Towel_Bales from tblBrokeConsumption where INV_Date = '" & strTday7 & "'"
     Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
if not MyRec.eof then
' do nothing
else

  	
  	strsql8 = "Select  Tissue_Bales, Tissue_Gaylords, White_towel_Bales, Brown_Towel_Bales from tblBrokeConsumption where ID = 1 "
  	    Set MyRec8 = Server.CreateObject("ADODB.Recordset")
   MyRec8.Open strSQL8, Session("ConnectionString") 
   TDefault = MyRec8.fields("Tissue_Bales")
   TGDefault = MyRec8.fields("Tissue_Gaylords")
   TWDefault = MyRec8.fields("White_towel_Bales")
   TBDefault = MyRec8.fields("Brown_towel_Bales")

   MyRec8.close

  	
	strsql9 =  "INSERT INTO tblBrokeConsumption ( INV_Date, Tissue_Bales, Tissue_Gaylords, White_towel_Bales, Brown_Towel_Bales) "_
	&" SELECT '" & strTday7 & "', " & TDefault & ", " & TGDefault & ", " & TWDefault & ", " & TBDefault & " "
	
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL9
			MyConn.Close

  
end if
MyRec.close
		
			strYdate = dateadd("d", -3, now())
		
	     
   set objDAC = new ASP_CLS_DataAccess
   

	        call objDAC.ExecuteSp1("asp_cls_Broke_ConsumptionR1", array(strBegdate, strEnddate))
	        
	             set objPRO = new ASP_CLS_Fiber

        set rstDaily = Server.CreateObject("ADODB.Recordset")
 strsql2 = "SELECT WEekday, Consumption_Date, KCOP, Non_KCOP, OCC, Weekday, KCOP_Rail, OCC_Rail, Other_Rail, MXP, MXP_Rail from tblTempInvReport "_
&" order by consumption_date"
     
    rstDaily.Open strSQL2, Session("ConnectionString"), adOpenDynamic

	%>

<p class="style1"><strong>KC MOBILE 
Daily BROKE&nbsp; REPORT<br><%= Now()%><br><br>
Previous Days Consumption for Reference</strong></p>
<font face="arial" size="1"><table width="50%" class="style4">
<tr><td class="style3">&nbsp;</td><td class="style3">&nbsp;</td><td class="style2">
	Tissue Bales</td>
	<td class="style2">Tissue Gaylords<font face="arial">&nbsp;</td>

	<td class="style2">Towel Bales (White)</td>
		<td class="style2">Towel Bales (Brown)</td>
		<td class="style2">Tissue Total<font face="arial">&nbsp;</td>
	<td class="style2"><font face="arial">Towel Total</td></tr>
<%  strdate = null

strKCOPAvg = 0
strOCCAvg= 0
strCount = 0
while not rstDaily.Eof
strKCOP = 0
strKCOPRail = 0
strOther = 0
strOtherRail = 0
strOCC = 0
strOCCRail = 0
strMXP = 0
strMXPRail = 0

strCdate = rstDaily.fields("Consumption_Date")

 	if rstdaily.fields("Weekday")  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif rstdaily.fields("Weekday")   = 2 then
 	 strDayofweek =  "Monday" 
 	elseif rstdaily.fields("Weekday")   = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif rstdaily.fields("Weekday")   = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif rstdaily.fields("Weekday")  = 5 then
 	 strDayofweek =  "Thursday"
 	elseif rstdaily.fields("Weekday")   = 6 then
 	 strDayofweek =  "Friday"
 	elseif rstdaily.fields("Weekday")  = 7 then
 	 strDayofweek =  "Saturday"
 	end if
%>
<tr><td class="style41"><font face="arial"><%= strCdate %></td>
  <% If datepart("d", strCdate) = datepart("d", strnow) then %>
  <td class="style66"><font face="arial">Consumed since midnight TODAY (Not part of average)</td>
  <% else %>
<td class="style66"><font face="arial"><%= strDayofWeek%></td>
<% end if %>
<%  if isnull(rstDaily.fields("KCOP_Rail")) then
strKCOPRail = 0
else
strKCOPRail = rstDaily.fields("KCOP_Rail") 
end if

if isnull(rstDaily.fields("KCOP")) then
strKCOP = 0
else
strKCOP = rstDaily.fields("KCOP") 
end if

if isnull(rstDaily.fields("OCC")) then
strOCC = 0
else
strOCC = rstDaily.fields("OCC") 
end if

if isnull(rstDaily.fields("OCC_Rail")) then
strOCCRail = 0
else
strOCCRail = rstDaily.fields("OCC_Rail") 
end if

if isnull(rstDaily.fields("Non_KCOP")) then
strOther = 0
else
strOther = rstDaily.fields("Non_KCOP") 
end if

if isnull(rstDaily.fields("OTher_Rail")) then
strOtherRail = 0
else
strOtherRail = rstDaily.fields("OTher_Rail") 
end if

if isnull(rstDaily.fields("MXP")) then
strMXP = 0
else
strMXP = rstDaily.fields("MXP") 
end if


if isnull(rstDaily.fields("MXP_Rail")) then
strMXPRail = 0
else
strMXPRail = rstDaily.fields("MXP_Rail") 
end if


strKCOP = strKCOP + (strKCOPRail * 3)
strOCC = strOCC + (strOCCRail * 3)
strOther = strOther + (StrOtherRail * 3)
strMXP = strMXP + (StrMXPRail * 3) %>

<td class="style66"><font face="arial"><%= strKCOP %></td>

<td class="style66"><font face="arial"><%= strOCC %></td>

<td class="style66"><font face="arial"><%= strOther %></td>
<td class="style66"><font face="arial"><%= strMXP %></td>
<td class="style66"><font face="arial"><%= strKCOP + strOCC %></td>
<td class="style66"><font face="arial"><%= strOther + strMXP %></td>
</tr>

	
	   <% 
	   If datepart("d", strCdate) <> datepart("d", strnow) then
	   strOCCAvg = strOther + strMXP  + strOCCAvg
	   strKCOPAvg = strKCOP + strOCC + strKCOPAvg
	   end if
	   strCount = strCount + 1
	   rstDaily.MoveNext
	   
	  
     Wend
     strcount = strcount - 1
%>

<tr><td colspan="5" class="style16">&nbsp;</td>
<td class="style16"><font face="arial">Average</td>
<% IF strcount > 0 then %>
<td class="style65"><font face="arial"><%= round(strKCOPAvg / strCount,1) %></td>
<td class="style65"><font face="arial"><%= round(strOCCAvg / strCount,1) %></td>
<% else %>
<td class="style65"><font face="arial">0</td>

<td class="style65"><font face="arial">0</td>
<% end if %>


</tr>
</table>
<p class="style1"><strong>
ICR (Inventory, Consumption, Receipts)&nbsp;&nbsp;&nbsp; Note:&nbsp;  Yard 
total includes&nbsp;<%= strRailCars %> Rail Cars which are each counted as 3 trailer loads.</strong></p>

<table style="width: 80%" class="style18">
	<tr>
		<td colspan="2" style="height: 40px" class="style40"></td>
		<td colspan="4" class="style30" style="height: 40px"><strong>Yard INV</strong></td>
		<td colspan="4" class="style34" style="height: 40px"><strong>Inbounds</strong></td>
		<td style="height: 40px" colspan="2" class="style38"><strong>Consumption</strong></td>
		<td colspan="5" class="style44" style="height: 40px"><strong>Inventory EOD</strong></td>
	</tr>
	<tr>
		<td class="style29" style="height: 5px" colspan="6"></td>
		<td colspan="4" class="style55" style="height: 5px"><strong></strong></td>
		<td class="style53"></td>
		<td class="style53"></td>
		<td class="style52" style="height: 5px" colspan="5"></td>
		<tr>
		<td class="style50" colspan="2">&nbsp;</td>
		<td class="style49"><strong>Tissue<br> Bales</strong></td>
		<td class="style49" style="width: 6px"><strong>Tissue<br>Gaylords</strong></td>
		<td  class="style49"><strong>Towel<br> White</strong></td>
		<td  class="style49"><strong>Towel<br> Brown</strong></td>
		<td class="style21" >
<font face="arial" size="1"><strong>Tissue<br> Bales</strong></td>
		<td class="style21">
<font face="arial" size="1"><strong>Tissue<br>Gaylords</strong></td>
		<td class="style21" >
<font face="arial" size="1"><strong>Towel<br> White</strong></td>
		<td class="style21" >
<font face="arial" size="1"><strong>Towel<br> Brown</strong></td>

		<td  class="style48"><strong><span class="style68">
		Tissue<br> Total</span></strong><span class="style68"><strong>&nbsp;</strong></span></td>
		<td class="style48" ><strong>Towel<br> Total&nbsp;</strong></td>
		<td class="style47"><strong><span class="style67">Tissue<br> Total&nbsp;</span></strong><span class="style67"><strong>&nbsp;</strong></span></td>
		<td class="style14"><span class="style67">
		<strong>Towel<br></strong></span><span class="style15"><strong><span class="style68">&nbsp;</span><font face="arial" size="1"><span class="style67">Total</span></strong></span></td>
		<td class="style14" colspan="3">
<font face="arial" size="1">
	
<font face="arial"><strong><span class="style68">Legend</span></strong></font>&nbsp;</td>
	

	</tr> 
	<%
Dim gT, gTG, gTW, gTB, strRailCars
gT = 0
gTG = 0
gTW = 0
gTB = 0
strsql = "SELECT tblCars.*,  [date_received] AS Sort_Date FROM tblCars WHERE Location = 'YARD' and Species = 'BROKE' "_
&"  ORDER BY Broke_Description, Sort_Date "   
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    while not MyRec.eof
 
    If    MyRec("SAP_Nbr") = "70139381" or MyRec("SAP_Nbr") = "70000029"  then
    gT = gT + 1
    elseif MyRec.fields("SAP_Nbr") = "70005510"  then
    gTG = gTG + 1
  	elseif MyRec("SAP_Nbr") = "70000111" or MyRec("Broke_Description") = "70010634" then
    gTW = gTW + 1

    end if
    MyRec.movenext
    wend
    MyRec.close
    
    StrRailCars = 0
    
 strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE carrier = 'RAIL'and  Location = 'YARD' and Species = 'BROKE' and (SAP_Nbr = '70139381' or SAP_Nbr = '70000111') "
   Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then
    strRailCars = MyRec.fields("CountofCID")
gT = gT + (MyRec.Fields("CountofCID") * 2)
end if
    Myrec.close
         StrRailCars = 0 
  strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE carrier = 'RAIL' and Location = 'YARD' and Species = 'BROKE' and SAP_Nbr = '70005510' "
   Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then
    strRailCars = MyRec.fields("CountofCID")
gTG = gTG + (MyRec.Fields("CountofCID") * 2)
end if
    Myrec.close
        StrRailCars = 0
     strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE carrier = 'RAIL' and Location = 'YARD' and Species = 'BROKE' and (SAP_NBr = '70000111' or SAP_Nbr = '70010634') "
   Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then
    strRailCars = MyRec.fields("CountofCID")
gTW = gTW + (MyRec.Fields("CountofCID") * 2)
end if
    Myrec.close
   
   dim strUse
    strUse = date()
  strDay2 = dateadd("d", -3, strUse)
  strDay0 = dateadd("d", 1, struse)
  
  Dim strTomorrow
  strTomorrow = strDay0

    
   	Dim countT, countTG, countTW, countTB, countT2, countTG2, countTW2, countTB2, strLoad, strBD
 		countT = 0
 		countTG = 0
 		countTW = 0
 		countTB = 0
 		countT2 = 0
 		countTG2 = 0
 		countTW2 = 0
 		countTB2 = 0
 		strT3 = 0
 		strTG3 = 0
 		strTW3 = 0
 		strTB3 = 0
 		strT4 = 0
 		strTG4 = 0
 		strTW4 = 0
 		strTB4 = 0
	strT5 = 0
 		strTG5 = 0
 		strTW5 = 0
 		strTB5 = 0
 		strT6 = 0
 		strTG6 = 0
 		strTW6 = 0
 		strTB6 = 0
 		strT7 = 0
 		strTG7 = 0
 		strTW7 = 0
 		strTB7 = 0


  	     strsql = "SELECT  broke_Description, Release, load_nbr, dateadd(dd, datediff(dd,0,Date_to),0) as Delivery_date "_
  			&" FROM tblInbound "_
			&" WHERE len(broke_description) > 2 and   Ship_Status <> 'Tender Accepted'"_	
			&" and dateadd(dd, datediff(dd,0,Date_to),0) > '" & strDay2 & "' "_
			&" and dateadd(dd, datediff(dd,0,Date_to),0) <= '" & strUse & "' "_
			&" ORDER BY dateadd(dd, datediff(dd,0,Date_to),0)" 	

 	
   		Set MyRec = Server.CreateObject("ADODB.Recordset")
  		MyRec.Open strSQL, Session("ConnectionString") 
		While not MyRec.eof 

 		
		strOB2 = MyRec.fields("Release")
		strLoad = MyRec.fields("Load_nbr")
		
		
  		if MyRec("Broke_Description") = "Tissue Bales" then
				countt = countt + 1
				If len(Release) > 2 then 
				strSql2 = "Select ID from tblCars where Release_Num = '" & strOB2 & "'"
				Set MyRec2 = Server.CreateObject("ADODB.Recordset")
  		 		MyRec2.Open strSQL2, Session("ConnectionString") 
  				 if not MyRec2.eof then
  				 Countt = countt - 1
  				 end if
  				 end if
  		 
  		 			If len(Load_nbr) > 2 then 
		strSql2 = "Select ID from tblCars where Delivery_number = '" & strLoad & "'"
			Set MyRec2 = Server.CreateObject("ADODB.Recordset")
  		 MyRec2.Open strSQL2, Session("ConnectionString") 
  		 if not MyRec2.eof then
  		 Countt = countt - 1
  		 end if
  		 end if

		elseif MyRec("Broke_Description") = "Tissue Gaylords" then
		countTG = countTG + 1		
			
  	   
				If len(Release) > 2 then 
		strSql2 = "Select ID from tblCars where Release_Num = '" & strOB2 & "'"
			Set MyRec2 = Server.CreateObject("ADODB.Recordset")
  		 MyRec2.Open strSQL2, Session("ConnectionString") 
  		 if not MyRec2.eof then
  		 Counttg = counttg - 1
  		 end if
  		 end if
  		 
  		If len(Load_nbr) > 2 then 
		strSql2 = "Select ID from tblCars where Delivery_number = '" & strLoad & "'"
			Set MyRec2 = Server.CreateObject("ADODB.Recordset")
  		 MyRec2.Open strSQL2, Session("ConnectionString") 
  		 if not MyRec2.eof then
  		 Counttg = counttg - 1
  		 end if
  		 end if	
				
					
		elseif MyRec("Broke_Description") = "Towel Bales - White" then		
		countTW= countTW + 1
			
						
		If len(Release) > 2 then 
		strSql2 = "Select ID from tblCars where Release_Num = '" & strOB2 & "'"
		Set MyRec2 = Server.CreateObject("ADODB.Recordset")
  		 MyRec2.Open strSQL2, Session("ConnectionString") 
  		 if not MyRec2.eof then
  		 Counttw = counttw - 1
  		 end if
  		 end if
  		 
  		If len(Load_nbr) > 2 then 
		strSql2 = "Select ID from tblCars where Delivery_number = '" & strLoad & "'"
		Set MyRec2 = Server.CreateObject("ADODB.Recordset")
  		 MyRec2.Open strSQL2, Session("ConnectionString") 
  		 if not MyRec2.eof then
  		 Counttw = counttw - 1
  		 end if
  		 end if

	elseif MyRec("Broke_Description") = "Towel Bales - Brown" then		
		countTB= countTB + 1
			If len(Release) > 2 then 
		strSql2 = "Select ID from tblCars where Release_Num = '" & strOB2 & "'"
			Set MyRec2 = Server.CreateObject("ADODB.Recordset")
  		 MyRec2.Open strSQL2, Session("ConnectionString") 
  		 if not MyRec2.eof then
  		 Counttb = counttb - 1
  		 end if
  		 end if
  		 
  		If len(Load_nbr) > 2 then 
		strSql2 = "Select ID from tblCars where Delivery_number = '" & strLoad & "'"
			Set MyRec2 = Server.CreateObject("ADODB.Recordset")
  		 MyRec2.Open strSQL2, Session("ConnectionString") 
  		 if not MyRec2.eof then
  		 Counttb = counttb - 1
  		 end if
  		 end if


				
		end if
		

 
      	 MyRec.MoveNext
    	 Wend
    	 MyRec.close

        strsql = "SELECT  broke_Description, Release, load_nbr, dateadd(dd, datediff(dd,0,Date_to),0) as Delivery_date "_
          		&" FROM tblInbound "_
			&" WHERE len(broke_description) > 2  and  dateadd(dd, datediff(dd,0,Date_to),0) = '" & strTomorrow & "' and Ship_Status <> 'Tender Accepted' "_
			&" ORDER BY dateadd(dd, datediff(dd,0,Date_to),0), left(release,1)" 	


   		Set MyRec = Server.CreateObject("ADODB.Recordset")
  	 MyRec.Open strSQL, Session("ConnectionString") 
		While not MyRec.eof 
		%>
		<%= MyRec("Broke_Description") %> - <%= MyRec("Delivery_date") %>
		<%

 		
		
		strOB2 = MyRec.fields("Release")
		strLoad = MyRec.fields("Load_nbr")
		
		
  		if MyRec("Broke_Description") = "Tissue  Purchased (TON)" or MyRec("Broke_Description") = "Tissue  GBC (T)" then
				countt2= countt2+ 1
				If len(Release) > 2 then 
				strSql2 = "Select ID from tblCars where Release_Num = '" & strOB2 & "'"
				Set MyRec2 = Server.CreateObject("ADODB.Recordset")
  		 		MyRec2.Open strSQL2, Session("ConnectionString") 
  				 if not MyRec2.eof then
  				 Countt2= countt2- 1
  				 end if
  		 end if
  		 
  		 If len(Load_nbr) > 2 then 
		strSql2 = "Select ID from tblCars where Delivery_number = '" & strLoad & "'"
			Set MyRec2 = Server.CreateObject("ADODB.Recordset")
  		 MyRec2.Open strSQL2, Session("ConnectionString") 
  		 if not MyRec2.eof then
  		 Countt2= countt2- 1
  		 end if
  		 end if

		elseif MyRec("Broke_Description") = "Tissue  Gaylords (T)" then
		countTG2 = countTG2 + 1		
			
				If len(Release) > 2 then 
		strSql2 = "Select ID from tblCars where Release_Num = '" & strOB2 & "'"
			Set MyRec2 = Server.CreateObject("ADODB.Recordset")
  		 MyRec2.Open strSQL2, Session("ConnectionString") 
  		 if not MyRec2.eof then
  		 Counttg2 = counttg2 - 1
  		 end if
  		 end if
  		 
  		If len(Load_nbr) > 2 then 
		strSql2 = "Select ID from tblCars where Delivery_number = '" & strLoad & "'"
			Set MyRec2 = Server.CreateObject("ADODB.Recordset")
  		 MyRec2.Open strSQL2, Session("ConnectionString") 
  		 if not MyRec2.eof then
  		 Counttg2 = counttg2 - 1
  		 end if
  		 end if	
				
					
		elseif MyRec("Broke_Description") = "Towel  Purchased (TON)" or MyRec("Broke_Description") = "Towel  White In house/HPC (T)" then		
		countTW2 = countTW2 + 1
			
						
		If len(Release) > 2 then 
		strSql2 = "Select ID from tblCars where Release_Num = '" & strOB2 & "'"
		Set MyRec2 = Server.CreateObject("ADODB.Recordset")
  		 MyRec2.Open strSQL2, Session("ConnectionString") 
  		 if not MyRec2.eof then
  		 Counttw2 = counttw2 - 1
  		 end if
  		 end if
  		 
  		If len(Load_nbr) > 2 then 
		strSql2 = "Select ID from tblCars where Delivery_number = '" & strLoad & "'"
		Set MyRec2 = Server.CreateObject("ADODB.Recordset")
  		 MyRec2.Open strSQL2, Session("ConnectionString") 
  		 if not MyRec2.eof then
  		 Counttw2 = counttw2 - 1
  		 end if
  		 end if

	
  		 
  		

				
		end if
		

 
      	 MyRec.MoveNext
    	 Wend
    	 MyRec.close
				
		  
   
        strsql = "SELECT  count(CID) as Countoford_id, Broke_Description, dateadd(dd, datediff(dd,0,Date_to),0) as Delivery_date "_
  			&" FROM tblInbound "_
			&" WHERE Destination_City ='MOBILE' and len(Broke_Description) > 2"_				
			&"GROUP BY dateadd(dd, datediff(dd,0,Date_to),0), Broke_Description "_			
			&" HAVING '" & strdate & "' <= dateadd(dd, datediff(dd,0,Date_to),0) "_
			&" ORDER BY dateadd(dd, datediff(dd,0,Date_to),0), Broke_Description"
   		


			Set MyRec = Server.CreateObject("ADODB.Recordset")
   			 MyRec.Open strSQL, Session("ConnectionString") 
			While not MyRec.eof

If strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2 and (MyRec.fields("Broke_Description") = "Tissue  Purchased (TON)"  or MyRec("Broke_Description") = "Tissue  GBC (T)") then   
strt3 = MyRec.fields("countoford_id")
elseif strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and MyRec.fields("Broke_Description") = "Tissue  Gaylords (T)" then   
strTG3 = strTG3 + MyRec.fields("countoford_id")
elseif strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and (MyRec.fields("Broke_Description") = "Towel  Purchased (TON)" or MyRec("Broke_Description") = "Towel  White In house/HPC (T)") then  
strTW3 = strTW3 + MyRec.fields("countoford_id")


elseif strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3  and (MyRec.fields("Broke_Description") = "Tissue  Purchased (TON)"  or MyRec("Broke_Description") = "Tissue  GBC (T)") then    
strt4 = MyRec.fields("countoford_id")
elseif strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3  and MyRec.fields("Broke_Description") = "Tissue  Gaylords (T)" then   
strTG4 = strTG4 + MyRec.fields("countoford_id")
elseif strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3  and (MyRec.fields("Broke_Description") = "Towel  Purchased (TON)" or MyRec("Broke_Description") = "Towel  White In house/HPC (T)") then  

strTW4 = strTW4 + MyRec.fields("countoford_id")

elseif strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4  and (MyRec.fields("Broke_Description") = "Tissue  Purchased (TON)"  or MyRec("Broke_Description") = "Tissue  GBC (T)") then  
strt5 = MyRec.fields("countoford_id")
elseif strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4  and MyRec.fields("Broke_Description") = "Tissue  Gaylords (T)" then    
strTG5 = strTG5 + MyRec.fields("countoford_id")
elseif strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4  and (MyRec.fields("Broke_Description") = "Towel  Purchased (TON)" or MyRec("Broke_Description") = "Towel  White In house/HPC (T)") then  
strTW5 = strTW5 + MyRec.fields("countoford_id")


elseif strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5  and (MyRec.fields("Broke_Description") = "Tissue  Purchased (TON)"  or MyRec("Broke_Description") = "Tissue  GBC (T)") then   
strt6 = MyRec.fields("countoford_id")
elseif strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5  and MyRec.fields("Broke_Description") = "Tissue  Gaylords (T)" then    
strTG6 = strTG6 + MyRec.fields("countoford_id")
elseif strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5  and (MyRec.fields("Broke_Description") = "Towel  Purchased (TON)" or MyRec("Broke_Description") = "Towel  White In house/HPC (T)") then  

strTW6 = strTW6 + MyRec.fields("countoford_id")


elseif strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6  and (MyRec.fields("Broke_Description") = "Tissue  Purchased (TON)"  or MyRec("Broke_Description") = "Tissue  GBC (T)") then  
strt7 = MyRec.fields("countoford_id")
elseif strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6  and MyRec.fields("Broke_Description") = "Tissue  Gaylords (T)" then   
strTG7 = strTG7 + MyRec.fields("countoford_id")
elseif strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6  and (MyRec.fields("Broke_Description") = "Towel  Purchased (TON)" or MyRec("Broke_Description") = "Towel  White In house/HPC (T)") then  
 
strTW7 = strTW7 + MyRec.fields("countoford_id")


end if
   MyRec.MoveNext
     Wend
     MyRec.close


 %>

	<tr> 
		<td style="height: 22px" class="style1"><font face="arial"><%= strNow %></td>
			<% strDate1 = datepart("w", strNow)
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

		<td  class="style16" class="style42"><font face="arial"><%= strDayofWeek %></td>
		<td  class="style65"><font face="arial"><%= gT %></td>
		<td  class="style65"><font face="arial"><%= gTG %></td>
		<td  class="style65"><font face="arial"><%= gTW %></td>		
		<td  class="style65"><font face="arial"><%= gTB %></td>
	
		<td  class="style69"><font face="arial" size="2"><span class="style16"><%= countt %>&nbsp; 
		</span> </td>
		<td  class="style69"><font face="arial" size="2"><span class="style16"><%= counttg %></span>&nbsp;</td>
		<td  class="style69"><font face="arial" size="2"><span class="style16"><%= counttw %>&nbsp; 
		</span> </td>
		<td  class="style69"><font face="arial" size="2"><span class="style16"><%= counttb %>&nbsp; 
		</span> </td>
	<%  strsql = "Select tblBrokeConsumption.* from tblBrokeConsumption where INV_Date = '" & strnow & "'"
		
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
T_One = MyRec.fields("Tissue_Bales")
TG_One = MyRec.fields("Tissue_Gaylords")
TW_One = MyRec.fields("White_towel_bales")
TB_One = MyRec.fields("Brown_towel_bales")

else
T_One = 0
TG_One = 0
TW_One = 0
TB_One = 0
end if
MyRec.close
Tissue_Total_one = T_One + TG_One
Towel_total_one = TW_One + TB_One
strGroupTissueOne = gT + gTG + countt + counttg - Tissue_Total_one
strGroupTowelOne = gTB + gTW + counttb + counttw - Towel_Total_one %>
		
		<td class="style65"><%= Tissue_total_One %></td>
		<td class="style65"><%= Towel_total_One %></td>
<% if strGroupTissueOne > 4 then %>
<td class="style70">
<% elseif strGroupTissueOne = 3 or strGroupTissueOne = 4 then %>
<td class="style71">
<% elseif strGroupTissueOne = 1 or strGroupTissueOne = 2 then %>
<td class="style72">
<% else %>
<td class="style73">
<% end if %>
		<font face="arial" size="2"><span class="style16"><%=  strGroupTissueOne  %>&nbsp; 
		</span> </td>
		
<% if strGroupTowelOne > 4 then %>
<td class="style70">
<% elseif strGroupTowelOne = 3 or strGroupTowelOne = 4 then %>
<td class="style71">
<% elseif strGroupTowelOne = 1 or strGroupTowelOne = 2 then %>
<td class="style72">
<% else %>
<td class="style73">
<% end if %>
<font face="arial" size="2"><span class="style16"><%= strGroupTowelOne  %>&nbsp; 
		</span> </td>
					<td height="17" class="style70">Tissue &gt; 4</td>
		
				<td height="17"  class="style70">Towel &gt; 4</td>

</tr>
	<tr>
		<td style="height: 23px" class="style31"><font face="arial" size="2"><%= dateadd("d", 1, strnow)%></td>
				<% strDate1 = datepart("w", dateadd("d", 1, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

		<td  class="style31"><font face="arial" size="2"><%= strDayofWeek %></td>
		<td style="height: 23px" class="style64"></td>
		<td style="height: 23px" class="style57"></td>
		<td class="style57"></td>
		<td class="style57"></td>
	
			<td  class="style69"><font face="arial" size="2"><span class="style16"><%= countt2 %>&nbsp; 
		</span> </td>
		<td  class="style69"><font face="arial" size="2"><span class="style16"><%= counttg2 %></span>&nbsp;</td>
		<td  class="style69"><font face="arial" size="2"><span class="style16"><%= counttw2 %>&nbsp; 
		</span> </td>
		<td  class="style69"><font face="arial" size="2"><span class="style16"><%= counttb2 %>&nbsp; 
		</span> </td>
		
		
<% strSelectdate = dateadd("d", 1, strnow)
strsql = "Select tblBrokeConsumption.* from tblBrokeConsumption where INV_Date = '" & strSelectdate & "'"
		
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
T_two = MyRec.fields("Tissue_Bales")
TG_two = MyRec.fields("Tissue_Gaylords")
TW_two = MyRec.fields("White_towel_bales")
TB_two = MyRec.fields("Brown_towel_bales")

else
T_two = 0
TG_two = 0
TW_two = 0
TB_two = 0
end if
MyRec.close
Tissue_Total_two = T_two + TG_two
Towel_total_two = TW_two + TB_two
strGroupTissueTwo = strGroupTissueOne +  countt2 + counttg2 - Tissue_Total_two 
strGroupTowelTwo = strGroupTowelOne +  counttb2 + counttw2 - Towel_Total_two
 %>

		<td class="style65"><%= Tissue_total_two %></td>
		<td class="style65"><%= Towel_total_two %></td>

<% if strGroupTissueTwo > 4 then %>
<td class="style70">
<% elseif strGroupTissueTwo =3 or strGroupTissueTwo = 4 then %>
<td class="style71">
<% elseif strGroupTissueTwo = 1 or strGroupTissueTwo = 2 then %>
<td class="style72">
<% else %>
<td class="style73">
<% end if %>
<font face="arial" size="2"><span class="style16"><%= strGroupTissueTwo %>&nbsp; 
		</span> </td>
<% if strGroupTowelTwo > 4 then %>
<td class="style70">
<% elseif strGroupTowelTwo =3 or strGroupTowelTwo = 4 then %>
<td class="style71">
<% elseif strGroupTowelTwo = 1 or strGroupTowelTwo = 2 then %>
<td class="style72">
<% else %>
<td class="style73">
<% end if %>
<font face="arial" size="2"><span class="style16"><%= strGroupTowelTwo  %>&nbsp; 
		</span> </td>
					<td height="17" class="style71">	Tissue 3-4</td>
		
				<td height="17"  class="style71">Towel 3-4 </td> </tr>


<tr>
		<td style="height: 23px" class="style31"><font face="arial" size="2"><%= dateadd("d", 2, strnow)%></td>
				<% strDate1 = datepart("w", dateadd("d", 2, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

		<td  class="style31"><font face="arial" size="2"><%= strDayofWeek %></td>
		<td style="height: 23px" class="style64"></td>
		<td style="height: 23px" class="style57"></td>
		<td class="style57"></td>
		<td class="style57"></td>
	
			<td  class="style69"><font face="arial" size="2"><span class="style16"><%= strt3 %>&nbsp;</span> </td>
		<td  class="style69"><font face="arial" size="2"><span class="style16"><%= strtg3 %></span>&nbsp;</td>
		<td  class="style69"><font face="arial" size="2"><span class="style16"><%= strtw3 %>&nbsp;</span> </td>
		<td  class="style69"><font face="arial" size="2"><span class="style16"><%= strtb3 %>&nbsp;</span> </td>
		
		
<% strSelectdate = dateadd("d", 2, strnow)
strsql = "Select tblBrokeConsumption.* from tblBrokeConsumption where INV_Date = '" & strSelectdate & "'"
		
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
T_three = MyRec.fields("Tissue_Bales")
TG_three = MyRec.fields("Tissue_Gaylords")
TW_three = MyRec.fields("White_towel_bales")
TB_three = MyRec.fields("Brown_towel_bales")

else
T_three = 0
TG_three = 0
TW_three = 0
TB_three = 0
end if
MyRec.close
Tissue_Total_three = T_three + TG_three
Towel_total_three = TW_three + TB_three
strGroupTissuethree = strGroupTissueTwo + strT3 + strTG3  - Tissue_Total_three
strGroupTowelthree = strGroupTowelTwo + strTB3 + strTW3  - Towel_Total_three
 %>

		<td class="style65"><%= Tissue_total_three %></td>
		<td class="style65"><%= Towel_total_three %></td>

<% if strGroupTissueThree > 4 then %>
<td class="style70">
<% elseif strGroupTissueThree  =3 or strGroupTissueThree  = 4 then %>
<td class="style71">
<% elseif strGroupTissueThree  = 1 or strGroupTissueThree  = 2 then %>
<td class="style72">
<% else %>
<td class="style73">
<% end if %>
<font face="arial" size="2"><span class="style16"><%= strGroupTissueThree %>&nbsp;</span> </td>
<% if strGroupTowelThree > 4 then %>
<td class="style70">
<% elseif strGroupTowelThree  =3 or strGroupTowelThree = 4 then %>
<td class="style71">
<% elseif strGroupTowelThree  = 1 or strGroupTowelThree  = 2 then %>
<td class="style72">
<% else %>
<td class="style73">
<% end if %>
<font face="arial" size="2"><span class="style16"><%= strGroupTowelThree %>&nbsp; </span> </td>
		<td height="17" class="style72">Tissue 1-2</td>		
		<td height="17"  class="style72">Towel 1-2 </td> </tr>
		
		<td class="style31" style="height: 22px"><font face="arial" size="2"><%= dateadd("d", 3, strnow)%>&nbsp;</td>
								<% strDate1 = datepart("w", dateadd("d", 3, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

		<td  class="style31"><font face="arial" size="2"><%= strDayofWeek %></td>
		<td style="height: 23px" class="style64"></td>
		<td style="height: 23px" class="style57"></td>
		<td class="style57"></td>
		<td class="style57"></td>
	
			<td  class="style69"><font face="arial" size="2"><span class="style16"><%= strt4 %>&nbsp;</span> </td>
		<td  class="style69"><font face="arial" size="2"><span class="style16"><%= strtg4 %></span>&nbsp;</td>
		<td  class="style69"><font face="arial" size="2"><span class="style16"><%= strtw4 %>&nbsp;</span> </td>
		<td  class="style69"><font face="arial" size="2"><span class="style16"><%= strtb4 %>&nbsp;</span> </td>

<% strSelectdate = dateadd("d", 3, strnow)
strsql = "Select tblBrokeConsumption.* from tblBrokeConsumption where INV_Date = '" & strSelectdate & "'"
		
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
T_Four = MyRec.fields("Tissue_Bales")
TG_Four = MyRec.fields("Tissue_Gaylords")
TW_Four = MyRec.fields("White_towel_bales")
TB_Four = MyRec.fields("Brown_towel_bales")

else
T_Four = 0
TG_Four = 0
TW_Four = 0
TB_Four = 0
end if
MyRec.close
Tissue_Total_Four = T_Four + TG_Four
Towel_total_Four = TW_Four + TB_Four
strGroupTissueFour = strGroupTissueThree + strT4 + strTG4  - Tissue_Total_four
strGroupTowelFour = strGroupTowelThree + strTB4 + strTw4  - Towel_Total_four
 %>

		<td class="style65"><%= Tissue_total_four %></td>
		<td class="style65"><%= Towel_total_four%></td>

<% if strGroupTissueFour > 4 then %>
<td class="style70">
<% elseif strGroupTissueFour   =3 or strGroupTissueFour   = 4 then %>
<td class="style71">
<% elseif strGroupTissueFour   = 1 or strGroupTissueFour   = 2 then %>
<td class="style72">
<% else %>
<td class="style73">
<% end if %>
<font face="arial" size="2"><span class="style16"><%= strGroupTissueFour %>&nbsp; 
		</span> </td>
<% if strGroupTowelFour > 4 then %>
<td class="style70">
<% elseif strGroupTowelFour   =3 or strGroupTowelFour   = 4 then %>
<td class="style71">
<% elseif strGroupTowelFour   = 1 or strGroupTowelFour   = 2 then %>
<td class="style72">
<% else %>
<td class="style73">
<% end if %>
<font face="arial" size="2"><span class="style16"><%= strGroupTowelFour %>&nbsp; 
		</span> </td>
		<td height="17" class="style73">Tissue &lt; 1</td>		
		<td height="17"  class="style73">Towel &lt; 1 </td> </tr>

	<tr>
		<td style="height: 19px" class="style31"><font face="arial" size="2"><%= dateadd("d", 4, strnow)%>&nbsp;</td>
								<% strDate1 = datepart("w", dateadd("d", 4, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>
<td  class="style31"><font face="arial" size="2"><%= strDayofWeek %></td>
		<td style="height: 23px" class="style64"></td>
		<td style="height: 23px" class="style57"></td>
		<td class="style57"></td>
		<td class="style57"></td>
	
			<td  class="style69"><font face="arial" size="2"><span class="style16"><%= strt5 %>&nbsp;</span> </td>
		<td  class="style69"><font face="arial" size="2"><span class="style16"><%= strtg5 %></span>&nbsp;</td>
		<td  class="style69"><font face="arial" size="2"><span class="style16"><%= strtw5 %>&nbsp;</span> </td>
		<td  class="style69"><font face="arial" size="2"><span class="style16"><%= strtb5 %>&nbsp;</span> </td>

<% strSelectdate = dateadd("d", 4, strnow)
strsql = "Select tblBrokeConsumption.* from tblBrokeConsumption where INV_Date = '" & strSelectdate & "'"
		
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
T_Five = MyRec.fields("Tissue_Bales")
TG_Five = MyRec.fields("Tissue_Gaylords")
TW_Five = MyRec.fields("White_towel_bales")
TB_Five = MyRec.fields("Brown_towel_bales")

else
T_Five = 0
TG_Five = 0
TW_Five = 0
TB_Five = 0
end if
MyRec.close
Tissue_Total_Five = T_Five + TG_Five
Towel_total_Five = TW_Five + TB_Five
strGroupTissueFive = strGroupTissueFour + strT5 +strTG5  - Tissue_Total_five
strGroupTowelFive = strGroupTowelFour + strTW5 + strTb5  - Towel_Total_five
 %>

		<td class="style65"><%= Tissue_total_Five %></td>
		<td class="style65"><%= Towel_total_Five %></td>

		<% if strGroupTissueFive > 4 then %>
<td class="style70">
<% elseif strGroupTissueFive   =3 or strGroupTissueFive   = 4 then %>
<td class="style71">
<% elseif strGroupTissueFive  = 1 or strGroupTissueFive   = 2 then %>
<td class="style72">
<% else %>
<td class="style73">
<% end if %>
<font face="arial" size="2"><span class="style16"><%= strGroupTissueFive %>&nbsp; 
		</span> </td>
				<% if strGroupTowelFive > 4 then %>
<td class="style70">
<% elseif strGroupTowelFive   =3 or strGroupTowelFive   = 4 then %>
<td class="style71">
<% elseif strGroupTowelFive  = 1 or strGroupTowelFive   = 2 then %>
<td class="style72">
<% else %>
<td class="style73">
<% end if %>
<font face="arial" size="2"><span class="style16"><%= strGroupTowelFive %>&nbsp; 
		</span> </td>
		<td height="17" >&nbsp;</td>		
		<td height="17" >&nbsp; </td> </tr>


	<td style="height: 19px" class="style31"><font face="arial" size="2"><%= dateadd("d", 5, strnow)%>&nbsp;</td>
								<% strDate1 = datepart("w", dateadd("d", 5, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>
<td  class="style31"><font face="arial" size="2"><%= strDayofWeek %></td>
		<td style="height: 23px" class="style64"></td>
		<td style="height: 23px" class="style57"></td>
		<td class="style57"></td>
		<td class="style57"></td>
	
			<td  class="style69"><font face="arial" size="2"><span class="style16"><%= strt6 %>&nbsp;</span> </td>
		<td  class="style69"><font face="arial" size="2"><span class="style16"><%= strtg6 %></span>&nbsp;</td>
		<td  class="style69"><font face="arial" size="2"><span class="style16"><%= strtw6 %>&nbsp;</span> </td>
		<td  class="style69"><font face="arial" size="2"><span class="style16"><%= strtb6 %>&nbsp;</span> </td>

<% strSelectdate = dateadd("d", 5, strnow)
strsql = "Select tblBrokeConsumption.* from tblBrokeConsumption where INV_Date = '" & strSelectdate & "'"
		
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
T_Six = MyRec.fields("Tissue_Bales")
TG_Six  = MyRec.fields("Tissue_Gaylords")
TW_Six  = MyRec.fields("White_towel_bales")
TB_Six  = MyRec.fields("Brown_towel_bales")

else
T_Six  = 0
TG_Six  = 0
TW_Six  = 0
TB_Six  = 0
end if
MyRec.close
Tissue_Total_Six  = T_Six  + TG_Six 
Towel_total_Six  = TW_Six  + TB_Six 
strGroupTissueSix  = strGroupTissueFive + strT6 +strTG6  - Tissue_Total_six
strGroupTowelSix = strGroupTowelFive + strTW6 + strTb6  - Towel_Total_six
 %>

		<td class="style65"><%= Tissue_total_six %></td>
		<td class="style65"><%= Towel_total_six %></td>
		<% if strGroupTissueSix > 4 then %>
<td class="style70">
<% elseif strGroupTissueSix   =3 or strGroupTissueSix  = 4 then %>
<td class="style71">
<% elseif strGroupTissueSix = 1 or strGroupTissueSix  = 2 then %>
<td class="style72">
<% else %>
<td class="style73">
<% end if %>
<font face="arial" size="2"><span class="style16"><%= strGroupTissuesix %>&nbsp; 
		</span> </td>
				<% if strGroupTowelSix > 4 then %>
<td class="style70">
<% elseif strGroupTowelSix   =3 or strGroupTowelSix  = 4 then %>
<td class="style71">
<% elseif strGroupTowelSix = 1 or strGroupTowelSix  = 2 then %>
<td class="style72">
<% else %>
<td class="style73">
<% end if %>
<font face="arial" size="2"><span class="style16"><%= strGroupTowelsix %>&nbsp; 
		</span> </td>
		<td height="17" >&nbsp;</td>		
		<td height="17" >&nbsp; </td> </tr>
<td style="height: 19px" class="style31"><font face="arial" size="2"><%= dateadd("d", 6, strnow)%>&nbsp;</td>

<% strDate1 = datepart("w", dateadd("d", 6, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>
<td  class="style31"><font face="arial" size="2"><%= strDayofWeek %></td>
		<td style="height: 23px" class="style64"></td>
		<td style="height: 23px" class="style57"></td>
		<td class="style57"></td>
		<td class="style57"></td>
	
			<td  class="style69"><font face="arial" size="2"><span class="style16"><%= strt7 %>&nbsp;</span> </td>
		<td  class="style69"><font face="arial" size="2"><span class="style16"><%= strtg7 %></span>&nbsp;</td>
		<td  class="style69"><font face="arial" size="2"><span class="style16"><%= strtw7 %>&nbsp;</span> </td>
		<td  class="style69"><font face="arial" size="2"><span class="style16"><%= strtb7 %>&nbsp;</span> </td>

<% strSelectdate = dateadd("d", 6, strnow)
strsql = "Select tblBrokeConsumption.* from tblBrokeConsumption where INV_Date = '" & strSelectdate & "'"
		
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
T_Seven = MyRec.fields("Tissue_Bales")
TG_Seven   = MyRec.fields("Tissue_Gaylords")
TW_Seven   = MyRec.fields("White_towel_bales")
TB_Seven   = MyRec.fields("Brown_towel_bales")

else
T_Seven   = 0
TG_Seven   = 0
TW_Seven   = 0
TB_Seven   = 0
end if
MyRec.close
Tissue_Total_Seven   = T_Seven   + TG_Seven 
Towel_total_Seven   = TW_Seven   + TB_Seven  
strGroupTissueSeven   = strGroupTissueSix + strT7 +strTG7  - Tissue_Total_seven
strGroupTowelSeven = strGroupTowelSix + strTW7 + strTb7  - Towel_Total_seven
 %>

		<td class="style65"><%= Tissue_total_seven %></td>
		<td class="style65"><%= Towel_total_seven %></td>

		<% if strGroupTissueSeven > 4 then %>
<td class="style70">
<% elseif strGroupTissueSeven    =3 or strGroupTissueSeven   = 4 then %>
<td class="style71">
<% elseif strGroupTissueSeven  = 1 or strGroupTissueSeven   = 2 then %>
<td class="style72">
<% else %>
<td class="style73">
<% end if %>
<font face="arial" size="2"><span class="style16"><%= strGroupTissueseven %>&nbsp; 
		</span> </td>
<% if strGroupTowelSeven > 4 then %>
<td class="style70">
<% elseif strGroupTowelSeven    =3 or strGroupTowelSeven   = 4 then %>
<td class="style71">
<% elseif strGroupTowelSeven  = 1 or strGroupTowelSeven   = 2 then %>
<td class="style72">
<% else %>
<td class="style73">
<% end if %>
<font face="arial" size="2"><span class="style16"><%= strGroupTowelseven %>&nbsp; 
		</span> </td>
		<td height="17" >&nbsp;</td>		
		<td height="17" >&nbsp; </td> </tr>

</table>

<p class="style1"><strong>SAP Inventory<br>To get real-time inventory use SAP, Transaction MB52</strong></p>
<% Dim strSAPdate
strsql = "SELECT Max(tblInventoryHistory.Rpt_date) AS MaxOfRpt_date FROM tblInventoryHistory"

     Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionPolaris") 

strSAPdate = MyRec.fields("MaxofRpt_Date")
MyRec.close

strsql = "SELECT tblInventoryHistory.Rpt_date, tblInventoryHistory.SAP_nbr, tblReference.Commodity, tblReference.Description, tblInventoryHistory.Location, tblInventoryHistory.UOM, tblInventoryHistory.Value "_
&" FROM tblInventoryHistory INNER JOIN tblReference ON tblInventoryHistory.SAP_nbr = tblReference.SAP "_
&" WHERE 	tblInventoryHistory.Rpt_date='" & strSAPdate & "' AND tblReference.Commodity='Broke' and tblInventoryHistory.value <> 0  order by   SAP_Nbr, Location"
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionPolaris") 


 %>
<table width="60%" border="1">
     <tr bgcolor="#D9D9FF">
    

      <td  align="left" colspan="2" class="style16">
		<font face="Arial" size = 1 class="style16">Last Downloaded<br> from SAP</font></td>
         <td  align="left" colspan="2" class="style16"><font face="Arial">Material #</font></td>
      <td  align="center" colspan="2" class="style16"><font face="Arial">Location</font></td>
         <td  align="center" colspan="2" class="style16"><font face="Arial">Commodity</font></td>

   <td  align="center" colspan="2" class="style16"><font face="Arial">Description</font></td>
   <td  align="center" colspan="2" class="style16"><font face="Arial">Qty</font></td>
   <td  align="center" colspan="2" class="style16"><font face="Arial">UOM</font></td>

      </tr>
           <%  ii = 0
       while not MyRec.Eof
    
    %>
<tr>
      
   <td  align="left" colspan="2" class="style16"><font face="Arial"><%= strSAPDate %></font></td>
   <td  align="left" colspan="2" class="style16"><font face="Arial"><%= MyRec.fields("SAP_nbr") %></font></td>
   <td  align="center" colspan="2" class="style16"><font face="Arial"><%= MyRec.fields("Location") %></font></td>
   <td  align="center" colspan="2" class="style16"><font face="Arial"><%= MyRec.fields("Commodity") %></font></td>
   <td  align="center" colspan="2" class="style16"><font face="Arial"><%= MyRec.fields("Description") %></font></td>
   <td  align="center" colspan="2" class="style16"><font face="Arial"><%= MyRec.fields("Value") %></font></td>
   <td  align="center" colspan="2" class="style16"><font face="Arial"><%= MyRec.fields("UOM") %></font></td>

</tr>
<%   MyRec.MoveNext
     Wend
     MyRec.close %>
</table>