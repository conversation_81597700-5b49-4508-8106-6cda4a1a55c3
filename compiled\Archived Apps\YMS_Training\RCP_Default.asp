																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Rail Car Target Default</TITLE>

 
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 



<% Dim MyRec, strsql, strid


strsql = "Select Target from tblRailTarget where ID = 1"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 

strTarget = MyRec.fields("Target")

MyRec.close


  set objGeneral = new ASP_CLS_General
  if objGeneral.IsSubmit() Then	

  

  	
  	If len(request.form("Target")) > 0 then
  	strTarget = request.form("Target")
  	else
  	strTarget = 0
  	end if
  	
 
  	
strsql =  "Update tblRailTarget set Target = " & strTarget & " where ID = 1"

	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
Response.redirect("RC_Projections.asp")		
end if
	
%>

<style type="text/css">
.style2 {
	font-family: arial;
	font-size: x-small;
	text-align: center;
}
.style4 {
	border-style: solid;
	border-width: 1px;
}
.style5 {
	text-align: center;
}
</style>
</head>

<body>
<br>
	
<form name="form1" action="RCP_Default.asp?id=<%= strid%>" method="post" ID="Form1">
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
Rail Car Target Default</strong></font></td>
<td align = right><font face="Arial"><a href="RC_Projections.asp">Return</a>&nbsp;</td>


</tr>
	    </table>



	<p>&nbsp;</p>
	<table id="table1" style="width: 15%" class="style4" align="center">
<tr bgcolor = #CCCCFF>
		<td bgcolor="#FFFFD7" class="style2">&nbsp;Target</td>
	</tr>
	<tr>
		<td class="style5"><font face = arial size = 1>
		<input type="text" name="RFF" size="20" style="width: 69px" value="<%= strTarget %>"></td>
	



	</tr>
</table>


<p align="center"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></p>
</form>
<!--#include file="Fiberfooter.inc"-->