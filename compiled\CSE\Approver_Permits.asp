
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 6.0">
<TITLE>Approver Roles</TITLE>

<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_Procedure.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<%    Dim   rstTeam, strTeam, rstESL, rstWA, strWA,  strID, objEPS, strName, strName2

    set objGeneral = new ASP_CLS_General %>
	<body>

<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% border=1>
  <tr><td colspan="4" bgcolor="white" class="subheader">&nbsp;</td></tr>

 <TD>
     <p align="left"><font size="3" face="Arial"><b>Approver Permits</b>&nbsp;&nbsp;</font></td>
		<td align = right><a href="javascript:history.go(-1);"><font face="Arial"><b>Return</b></font></td></tr>
	    </table><font face="Arial"><br>
	    <%  
	        Dim SCRIPT_NAME
		Dim strSQL, strApprover
		

		SCRIPT_NAME = Request.ServerVariables("SCRIPT_NAME")

strApprover = request.querystring("id")
    		    
    		strSQL = "SELECT tblSOP.SDescription, tblHA.* FROM tblHA INNER JOIN tblSOP ON tblHA.SpaceID = tblSOP.SOP_NO "_
 &" where HA_Approver_1 = '" & strApprover & "' or HA_Approver_2 = '" & strApprover & "'"
		        Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly	
			
If not MyRec.eof then

			%>
			</font><font face="arial">
			<table Border=1 bgcolor = #F0F0F0 BORDERCOLOR = #CCCCFF cellpadding = 3 width = 75% align = center>
			<thead>
			    <tr>
		
                	
                		<th bgcolor="#FDFEE9"> <font size="2">Space ID</font></th>
	     		
                		</font><font face="Arial">
	     		
                		<th bgcolor="#FDFEE9"> <font face="arial" size="2">
						Description</font></th>
                	
                		
			
	     	
                	
                		<th bgcolor="#FDFEE9"> <font face="arial" size="2">
						Approver 1</font></th>
                	
                		
			
	     	
                	
                		<th bgcolor="#FDFEE9"><font face="arial" size="2">
						Approver 2</font></th>
                       
              	    </tr>
			    </thead>
			    <tbody>
	
        		        <% While not MyRec.EOF %>
              			    <tr>
               <td bgcolor="#FFFFFF"><font face="Arial"><%= MyRec.Fields("SpaceID").Value %></font></td>
              		   <td bgcolor="#FFFFFF"><font face="Arial"><%= MyRec.Fields("SDescription").Value %></font></td>
              		   <td align = center bgcolor="#FFFFFF"><font face="Arial"><%= MyRec.Fields("HA_approver_1").Value %>&nbsp;</td>
						   <td align = center bgcolor="#FFFFFF"><font face="Arial"><%= MyRec.Fields("HA_approver_2").Value %>&nbsp;</td>
					
  						 </tr>
                            
              			    <% 
				        MyRec.MoveNext
                 			WEND
                 			MyRec.Close
                 			end if
              			    %>

			    </tbody>
			    <tfoot>
			    </tfoot>

            		</table>
	                   
      	</body>
<!--#include file="footer.inc"-->