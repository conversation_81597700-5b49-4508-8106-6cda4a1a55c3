
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Modify Technician</TITLE>
<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_sessionPLI.asp"--> 
<!--#include file="classes/ASP_CLS_General.asp"--> 
<!--#include file="classes/asp_cls_header.asp"-->

</head>
<style type="text/css">
.style1 {
	font-family: Arial;
}
.style3 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
.auto-style1 {
	border-style: solid;
	border-color: #C0C0C0;
	font-family: Arial;
	background-color: #E2F3FE;
}
.auto-style2 {
	font-weight: bold;
	border-style: solid;
	border-color: #C0C0C0;
	background-color: #E2F3FE;
	font-family: Arial;
}
</style>

<% Dim strName, strBID, strSite, strType


 set objGeneral = new ASP_CLS_General


if objGeneral.IsSubmit() Then


	Call SaveData() 

End if %>

<body><form name="form1" action="Tech_add.asp" method="post">
 
<table border="0" cellpadding="0" cellspacing="0" width = 60% style="border-collapse: collapse" bordercolor="000000" height="25" align = center >
  <tr>
    <td  bgcolor="#FFFFFF" bordercolor="#CCCCFF" height="25" >
    <p align="center"><font face="Arial" size="4">Modify Technician</font></td>
    <td align = center height="25"><font face="Arial"><b><a href="Technicians.asp">RETURN</a></b></font></td>

    <td align = center bgcolor="#FFFFFF" height="25">
  <Input name="Submit" type="submit" Value="Submit" style="float: right" ></td>
  </tr>
</table><br><br>
<div align="center">
<table cellpadding="0" cellspacing="0" bordercolor="#808080"  height="10" class="style3" style="width: 75%">
    <tr>
<td height="22" align="center" class="auto-style2" >
	Area</td>
   
  <td height="22" align="center" class="auto-style1" >
	<strong>Team Leader</strong></td>
   
  <td height="22" align="center" class="auto-style1" >
	<strong>Crew</strong></td>
   
  <td height="22" align="center" class="auto-style1" style="width: 186px" >
	<strong>Name</strong></td>
   <td height="22" align="center" class="auto-style1" style="width: 186px" >
	<strong>KC ID</strong></td>

   <td height="22" align="center" class="auto-style1" style="width: 186px" >
	<strong>SAP ID&nbsp;</strong></td>

  </tr>
  
    <tr>
       <td  bordercolor="#CCCCFF" height="47"  align="center" >  	
     <font face="Arial">
       <select name="Area" size="1" tabindex="1">
           <option  value="">Select</option>
    <% strsql = "Select Area from tblArea order by Area"
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    while not MyRec.eof %>
    <option <% if strArea = MyRec("Area") then %> selected <% end if %>><%= MyRec("Area")%></option>

 <% MyRec.movenext
 wend
 MyRec.close
 %>
 
 
     </select></font></td>
    	

    <td  bordercolor="#CCCCFF" height="47"  align="center" >  	
     <font face="Arial">	
		<input type="text" name="TeamLeader" size="35" value="<%= strTeamLeader %>" style="width: 220px" tabindex="2"></font></td>


    <td  bordercolor="#CCCCFF" height="47"  align="center" >  	
     <font face="Arial">
       <select name="WSR" size="1" tabindex="3">
           <option  value="">Select</option>
    <% strsql = "Select WSR from tblWSR order by WSR"
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    while not MyRec.eof %>
    <option <% if strWSR = MyRec("WSR") then %> selected <% end if %>><%= MyRec("WSR")%></option>

 <% MyRec.movenext
 wend
 MyRec.close
 %>
 

 
     </select></font></td>

	<td  bordercolor="#CCCCFF" height="47"  align="center" class="style6" >  	
     <font face="Arial">	
		<input type="text" name="Technician" size="35" value="<%= strTechnician %>" style="width: 227px" tabindex="4"></font></td>
   	<td  bordercolor="#CCCCFF" height="47"  align="center" class="style6" >  	
     <font face="Arial">	
		<input type="text" name="KCID" size="35" value="<%= strKCID %>" style="width: 75px" tabindex="5"></font></td>

   	<td  bordercolor="#CCCCFF" height="47"  align="center" class="style6" >  	
     <font face="Arial">	
		<input type="text" name="SAPID" size="35" value="<%= strSAPID %>" style="width: 100px" tabindex="6"></font></td>

  </tr>
 
  </table>
</div>



</form>
   
  

</body>
 <%

  
  Function SaveData()



strTechnician = Replace(Request.form("Technician"), "'", "''")  
strTL = Replace(Request.form("TeamLeader"), "'", "''") 
strKCID = Request.form("KCID")  

strArea = Request.form("Area")
strWSR = Request.form("WSR")
strSAPID = request.form("SAPID")

 
  strsql = "Insert into tblTechnician (Technician, KCID, WSR, Area, TeamLeader, SAPID) "_
  &" values ('" & strTechnician & "',  '" & strKCID & "',  '" & strWSR & "',  '" & strArea & "', '" & strTL & "', '" & strSAPID & "')"
 

   
  	 set MyRec = new ASP_CLS_DataAccess
        MyRec.ExecuteSql strSql 
         
          Response.redirect("Technicians.asp")
  End Function
  
   %><!--#include file="footer.inc"-->