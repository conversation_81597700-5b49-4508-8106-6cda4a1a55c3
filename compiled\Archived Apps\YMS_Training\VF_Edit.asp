																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Edit Inbound Virgin Fiber </TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->


<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate, rstSpecies, objEPS, MyConn3, strsql3, strID, strComments, strRelease
  Dim strTrailer,  strETA, strLead, strVendor, strSpecies,  strSAP, strPO, strBales, strTons, strUOM, rstVendor, strStatus

strid = request.querystring("id")

  set objGeneral = new ASP_CLS_General
  
  
  if objGeneral.IsSubmit() Then	

  strid = request.querystring("id")
  	strTrailer = request.form("Trailer")

  	
  	If len(request.form("Vendor")) > 0 then
  	strVendor = request.form("Vendor")
  	else
  	strVendor = ""
  	end if 
  	
  	If len(request.form("Units")) > 0 then
  	strUnits = request.form("Units")
  	else
  	strUnits = 0
  	end if
  	
  		
 
  	strLead = request.form("Lead_time")

  	
  	If len(request.form("Tons")) > 0 then
  	strTons = request.form("Tons")
  	else
  	strTons = 0
  	end if
  	
  	strSpecies = request.form("Species")
  	
 	If len(request.form("Release")) > 0 then
  	strrelease= request.form("Release")
  	else
  	strRelease = ""
  	end if
  	
  		If len(request.form("Comments")) > 0 then
  	strComments= Replace(request.Form("Comments"), "'", "''") 
  	else
  	strComments = ""
  	end if
  	
  	 	 strPO = Request.form("PO")
  	 	 strETA = Request.form("ETA")
  	

  	 strsql3 = "Select SAP, UOM, Schedule_agreement from tblVFSpecies where Fiber_species = '" & strSpecies & "'"
  	 Set MyConn3 = Server.CreateObject("ADODB.Recordset")
   	 MyConn3.Open strSQL3, Session("ConnectionString")

  
   	 strUOM = MyConn3.fields("UOM")
   	 strSAP = MyConn3.fields("SAP")
   	 MyConn3.close
   	 
   	 strStatus = Request.form("Status")
   	 

   	 
strdate = request.form("Date_shipped")
   	   	
   	 strsql =  "Update tblVirginFiber set Other_Comment = '" & strComments & "', Release = '" & strRelease & "', Trailer = '" & strTrailer & "', Date_shipped = '" & strDate & "',  Lead_time = " & strLead & ", "_
	&" Vendor = '" & strVendor & "', Species = '" & strSpecies & "', SAP_Nbr = '" & strSAP & "',  ETA = '" & strETA & "', "_
	&" PO = '" & strPO & "', Bales_VF = " & strUnits & ", Tons = " & strTons & ", UOM = '" & strUOM & "', Status = '" & strStatus & "' where VID = " & strid
	

	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
			Response.redirect("VF_Inbound.asp")
		
else

strsql = "Select * from tblVirginFiber where VID = " & strid
	   	 Set MyConn3 = Server.CreateObject("ADODB.Recordset")
   	 MyConn3.Open strSQL, Session("ConnectionString")
   	 
   	 strTrailer = MyConn3.fields("Trailer")
   	 strDate = MyConn3.fields("Date_Shipped")
     strLead = MyConn3.fields("Lead_time")
   	 strVendor = MyConn3.fields("Vendor")
   	 strSpecies = MyConn3.fields("Species")
   	 strTons = MyConn3.fields("Tons")
   	 strUnits = MyConn3.fields("Bales_VF")
   	 strComments = MyConn3.fields("Other_comment")
   	 strRelease = MyConn3.fields("Release")
   	 strPO = MyConn3.fields("PO")
   	 strETA = MyConn3.fields("ETA")
   	 strStatus = MyConn3.fields("Status")
   	 MyConn3.close

   	 
   	 
   	 
  end if  	 

	Call getdata()
%>

<body>
<br>
	
<form name="form1" action="VF_Edit.asp?id=<%= strid%> " method="post" ID="Form1">
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Edit Inbound Virgin Fiber Load</b></font></td>
<td align = right><font face="Arial"><a href="VF_Inbound.asp">Return</a>&nbsp;</td>


</tr>
	    </table>



	<p>&nbsp;</p>
	<table border="1" width="100%" id="table1">
<tr bgcolor = #CCCCFF>
		<td bgcolor="#FFFFD7">

<font face="arial" size="1">Trailer/Car</font></td>
		<td bgcolor="#FFFFD7">

<font face="arial" size="1">Date Shipped</font></td>
		<td bgcolor="#FFFFD7" align="center">

<font face="Arial" size="1">Release #</font></td>
		<td bgcolor="#FFFFD7"><b><font face="arial" size="1">Lead Time (Days)</font></b></td>
		<td bgcolor="#FFFFD7">
		<p align="center"><b><font face="arial" size="1">ETA</font></b></td>
		<td bgcolor="#FFFFD7"><b><font face="arial" size="1">Vendor</font></b></td>
	</tr>
	<tr>
		<td><font face = arial size = 1>
		<input type="text" name="Trailer" size="20" value="<%= strTrailer%>"></td>
		<td><font face = arial size = 1>
		<input type="text" name="Date_shipped" size="20" value="<%= strDate %>"></td>
		<td align="center"><font face = arial size = 1>
		<input type="text" name="Release" size="16" value="<%= strRelease%>"></td>
		<td><font face = arial size = 1>
<select name="Lead_time" size="1" >
 <option value=0>--Select --</option>

     <option <% if strLead = 1 then %> selected <% end if %>>1</option>
      <option <% if strLead = 2 then %> selected <% end if %>>2</option>
       <option <% if strLead = 3 then %> selected <% end if %>>3</option>
        <option <% if strLead = 4 then %> selected <% end if %>>4</option>
         <option <% if strLead = 5 then %> selected <% end if %>>5</option>
          <option <% if strLead = 6 then %> selected <% end if %>>6</option>
           <option <% if strLead = 7 then %> selected <% end if %>>7</option>
            <option <% if strLead = 8 then %> selected <% end if %>>8</option>
             <option <% if strLead = 9 then %> selected <% end if %>>9</option>
              <option <% if strLead = 10 then %> selected <% end if %>>10</option>
               <option <% if strLead = 11 then %> selected <% end if %>>11</option>
                <option <% if strLead = 12 then %> selected <% end if %>>12</option>
                 <option <% if strLead = 13 then %> selected <% end if %>>13</option>
                  <option <% if strLead = 14 then %> selected <% end if %>>14</option>
                   <option <% if strLead = 15 then %> selected <% end if %>>15</option>
                    <option <% if strLead = 16 then %> selected <% end if %>>16</option>
                     <option <% if strLead = 17 then %> selected <% end if %>>17</option>
                      <option <% if strLead = 18 then %> selected <% end if %>>18</option>
                       <option <% if strLead = 19 then %> selected <% end if %>>19</option>
                        <option <% if strLead = 20 then %> selected <% end if %>>20</option>                   


     </select></td>
		<td><font face = arial size = 1>
		<p align="center"><input type="text" name="ETA" size="16" value="<%= strETA%>"></td>
		<td><font face = arial size = 1>
<select name="Vendor" size="1" >
 <option value="">--Select --</option>
       <%= objGeneral.OptionListAsString(rstVendor, "Vendor", "Vendor", strVendor) %>
     </select></td>
	</tr>
</table>
<br>
		
		<table border="1" width="100%" cellpadding="0" id="table4" >
			<tr bgcolor = #CCCCFF>

		<td style="font-family: Arial" bgcolor="#FFFFD7" width="147"><font size="1"><b>Item 
		Description</b></font></td>

	<td style="font-family: Arial" bgcolor="#FFFFD7">
	<p align="center"><font size="1"><b>Tons</b></font></td>
	<td style="font-family: Arial" bgcolor="#FFFFD7">
	<p align="center"><font size="1"><b>Units</b></font></td>
	
	<td style="font-family: Arial" bgcolor="#FFFFD7" align="center">
	<font size="1"><b>PO</b></font></td>
	
	<td style="font-family: Arial" bgcolor="#FFFFD7" align="center">
	<font size="1">Comments</font></td>
	
	<td style="font-family: Arial" bgcolor="#FFFFD7" align="center">
	<font size="1">Status</font></td>
	
	</tr>
	
	<td width="147"><font face = arial size = 1>
<select name="Species" size="1" >
 <option value="">--Select --</option>
       <%= objGeneral.OptionListAsString(rstSpecies, "Fiber_species", "Fiber_species", strSpecies) %>
     </select></td>
<td height="18"><p align="center"><font face = arial size = 1><%= strScheduler%>&nbsp;<input type="text" name="Tons" size="9" value="<%= strTons %>"></td>
<td height="18"><p align="center"><font face = arial size = 1><%= strSafety_Specialist%>&nbsp;<input type="text" name="Units" size="7" value="<%= strUnits %>"></td>


<td height="18" align="center"><font face = arial size = 1>
<input type="text" name="PO" size="12" value="<%= strPO %>"></td>


<td height="18" align="center"><font face = arial size = 1>
<input type="text" name="Comments" size="39"></td>


<td height="18" align="center"><font face = arial size = 1>
<select name="Status" size="1" >


     <option  <% if strStatus = "Inbound" then %> selected <% end if %>>Inbound</option>
      <option <% if strStatus = "Received" then %> selected <% end if %>>Received</option>
             


     </select></td>


</tr></table>


<p align="center"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></p>
</form>

<%   Function GetData()
        set objEPS = new ASP_CLS_Fiber
        set rstSpecies = objEPS.VFSpecies()
           set rstVendor = objEPS.VFVendor()

    End Function %><!--#include file="Fiberfooter.inc"-->