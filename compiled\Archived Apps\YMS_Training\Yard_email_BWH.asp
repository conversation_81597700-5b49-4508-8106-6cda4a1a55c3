<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Yard Email</title>
</head>
<%	
           strEmailTo = "<EMAIL>"  
        '  strEmailCC = "<EMAIL>"          
              
			Set objMail = Server.CreateObject("CDO.Message")		
			objMail.From = "<EMAIL>"
			objMail.To = strEmailTo
			
			'objMail.BCC = strEmailCC
 
			objMail.Subject = "Broke Trucks identified for Warehouse "
 
	
 
strbody = strbody & "<TABLE cellSpacing=0 cellPadding=0 width=55% border=1 align = left> "
strbody = strbody & "<td  align=left ><font face=Arial size=2><strong>Species</strong></font></td>"
strbody = strbody & "<td  align=left ><font face=Arial size=2><strong>Carrier</strong></font></td>"
strbody = strbody & "<td  align=left ><font face=Arial size=2><strong>Trailer</strong></font></td>"
strbody = strbody & "<td  align=left ><font face=Arial size=2><strong>Vendor</strong></font></td>"
strbody = strbody & "<td  align=center ><font face=Arial size=2><strong>Days since <br>Requested Move</strong></font></td>"
strbody = strbody & "<td  align=center ><font face=Arial size=2><strong>Days in Yard</strong></font></td></tr>"

		
			
 

strsql = "SELECT tblCars.*,  [date_received] AS Sort_Date FROM tblCars WHERE (((tblCars.Date_received) Is Not Null) "_
&"  AND ((tblCars.Location)='Yard') AND ((tblCars.Trailer) Is Not Null))  and (Species='BROKE' or Grade = 'BROKE' or Species = 'WBROK') and Commodity = 'Y' "_
&"  ORDER BY tblCars.Species, Sort_Date " 
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
   
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

 Dim ii
       ii = 0
       If not MyRec.eof then
       	While not MyRec.EOF  
       	if len(MyRec("Exit_Date")) > 3 then
       	strdays =   round(Now()- cdate(MyRec.fields("Exit_Date")),1)   
       	else
       	strdays = 0
       	end if
       	
       	
      if isnull(MyRec.fields("Transfer_date")) then
             strYDays = round(Now()- cdate(MyRec.fields("Date_received")),1)          

             else
             strYDays = round(Now()- cdate(MyRec.fields("Transfer_date")),1)      
             end if  
      if ( ii mod 2) = 0 Then 
   strbody = strbody & "<tr bgcolor=#F1F1F8>"
    else
       strbody = strbody & "<tr bgcolor=#FFFFE8>"
       
  end if
 
 
 
    strbody = strbody & " <td  align=left ><font face = arial size = 2>" & MyRec.fields("Species") & "&nbsp;</td>"
   
 if MyRec("Trailer") = "UNKNOWN" then
 strbody = strbody & " <td  align=left ><font face = arial size = 2>" & MyRec.fields("Trans_Carrier") & "&nbsp;</td>"
 strbody = strbody & " <td  align=left ><font face = arial size = 2>" & MyRec.fields("Transfer_trailer_nbr") & "&nbsp;</td>"
 else 
 strbody = strbody & " <td  align=left ><font face = arial size = 2>" & MyRec.fields("Carrier") & "&nbsp;</td>"
 strbody = strbody & " <td  align=left ><font face = arial size = 2>" & MyRec.fields("Trailer") & "&nbsp;</td>"
  end if
     strbody = strbody & " <td  align=left ><font face = arial size = 2>" & MyRec.fields("Vendor") & "&nbsp;</td>"
      
     strbody = strbody & " <td  align=center ><font face = arial size = 2>" & strdays & "&nbsp;</td>"    	
        strbody = strbody & " <td  align=center ><font face = arial size = 2>" & strYdays & "&nbsp;</td>"  	
       	
MyRec.movenext
wend
else
Response.write ("There are no trucks to display")
end if
MyRec.close


	objMail.HTMLBody = strbody
' objMail.Send
 %>

