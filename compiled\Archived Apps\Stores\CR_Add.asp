
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Add New Cross Reference Name</TITLE>
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_Session.asp"-->
<!--#include file="classes/asp_cls_DataAccess.asp"-->

</head>
<style type="text/css">
.style1 {
	font-family: Arial;
}
.style3 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
.auto-style1 {
	border-style: solid;
	border-color: #C0C0C0;
	font-family: Arial;
	font-size: x-small;
	background-color: #E7EBFE;
}
.auto-style2 {
	font-weight: bold;
	font-size: x-small;
	border-style: solid;
	border-color: #C0C0C0;
	background-color: #E7EBFE;
}
.auto-style3 {
	font-size: x-small;
	font-weight: bold;
}
</style>

<% Dim strName, strBID, strSite, strType


 set objGeneral = new ASP_CLS_General


if objGeneral.IsSubmit() Then


	Call SaveData() 

End if %>

<body><form name="form1" action="CR_add.asp" method="post">
 
<table border="0" cellpadding="0" cellspacing="0" width = 60% style="border-collapse: collapse" bordercolor="000000" height="25" align = center >
  <tr>
    <td  bgcolor="#FFFFFF" bordercolor="#CCCCFF" height="25" > <font face="Arial" size="4">
	Add New Name for Cross Reference </font></td>
    <td align = center height="25" class="auto-style3"><font face="Arial"><a href="CR_list.asp">RETURN</a></font></td>

    <td align = center bgcolor="#FFFFFF" height="25">
  <Input name="Submit" type="submit" Value="Submit" style="float: right" ></td>
  </tr>
</table><br><br>
<div align="center">
<table cellpadding="0" cellspacing="0" width="50%" bordercolor="#808080"  height="10" class="style3">
    <tr>
<td height="22" align="center" class="auto-style2" >
	<font face="Arial"><strong>Name</strong></font></td>
   
  <td height="22" align="center" class="auto-style1" >
	<strong>Logon ID</strong></td>
   
  <td height="22" align="center" class="auto-style1" >
	<strong>Badge</strong></td>
   
  </tr>
  
    <tr>
    <td  bordercolor="#CCCCFF" height="47"  align="center" >   <font face="Arial">	
		<input type="text" name="Name" size="35" value="" style="width: 282px" tabindex="1">
</font></td>
    	

    <td  bordercolor="#CCCCFF" height="47"  align="center" >  	
     <font face="Arial">	
		<input type="text" name="BID" size="35" value="" style="width: 98px" tabindex="2"></font></td>


    <td  bordercolor="#CCCCFF" height="47"  align="center" >  	
     <font face="Arial">	
		<input type="text" name="Badge" size="35" value="" style="width: 98px" tabindex="3"></font></td>


  </tr>
 
  </table>
</div>



</form>
   
  

</body>
 <%

  
  Function SaveData()



strName = Replace(Request.form("Name"), "'", "''")   
strBID = Request.form("BID")  
strBadge = request.form("Badge")

 

 
  strsql = "Insert into tbl_Stores_IDs (Display_Name, Logon_ID, Badge) "_
  &" Values('" & strName & "', '" & strBID & "', " & strbadge & ")"

   
  	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
          Response.redirect("CR_list.asp")
  End Function
  
   %><!--#include file="footer.inc"-->