																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Recovered Paper Yard Inventory Report by Carrier</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->



<% Dim MyRec, strsql, strDays, strDate, gSpecies, gCount, gTCount, strsql3, MyRec3

	strdate = formatdatetime(now(),2)
gcount = 0
gSpecies = ""


    strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "' and User_type = 'A'"


    Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL3, Session("ConnectionString")



If not Myrec2.eof then
strAdmin = "YES"
else
strAdmin = ""
end if
MyRec2.close 

IF Session("EmployeeID") = "D10480" then
strAdmin = "YES"
end if

strAdmin = "YES"


	 if strAdmin = "" then %>
	<!--#include file="classes/asp_cls_headerFIBER.asp"-->

	<p align="center"><font face="arial" color="red" size="3"><b>The  Yard Inventory Report is Not Available.  <br><br>
	Please use the <a href="Spotting_report.asp">Shift Spotting Report</a><br>
	</b></font></p>
	<% else 


strsql = "UPDATE tblCars SET tblCars.Carrier_sort = [carrier] WHERE (((tblCars.Carrier_sort) Is Null) "_
&" and (Oasis_Status = 'IN' or Oasis_Status is null) AND ((tblCars.Location)='Yard'))"

 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
         
   strsql = "Update tblCars set Carrier_sort = [Trans_Carrier] where Location = 'Yard' and len(Transfer_trailer_nbr) > 1 and (Oasis_Status = 'IN' or Oasis_Status is null) "
 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
           

strsql = "SELECT tblCars.* FROM tblCars WHERE tblCars.Location='Yard' AND tblCars.Date_received Is Not Null "_
&"  AND tblCars.Trailer Is Not Null and (Oasis_Status = 'IN' or Oasis_Status is null) order by Carrier_sort, Date_received"   
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=90%  border=0>

<td align = left><b>
<font face="arial" size="2" >Yard Inventory Report for <%= strDate%></font></b></span></td>



</tr>
	    </table><br>

	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=80% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td ><font face="Arial" size="1"><b>Carrier</b></font></td>
			<td ><font face="Arial" size="1"><b>Shuttle</b></font></td>
				<td ><font face="Arial" size="1"><b>To Date<br>Detention</b></font></td>
		<td align="center" ><font face="Arial" size="1"><b>Days in<br> Yard</b></font></td>
	<td  ><font face="Arial" size="1"><b>Species</b></font></td>
		<td align = center  ><font face="Arial" size="1"><b>Date <br>Received</b></b></font></td>
    	<td ><font face="Arial" size="1"><b>&nbsp;&nbsp;Trailer</b></font></td>
	
	<td ><font face="Arial" size="1"><b>REC Nbr</b></font></td>
	<td ><font face="Arial" size="1"><b>PO</b></font></td>
<td ><font face="Arial" size="1"><b>Vendor</b></font></td>
<td align="center" ><font face="Arial" size="1"><b>TO</b></font></td>


<td align="right" ><font face="Arial" size="1"><b>Tons</b></font></td>
<td ><p align="left"><font face="Arial" size="1"><b>&nbsp;&nbsp;Other</b></font></td>

	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
      ' if MyRec("Commodity") = "Y" then 
       'skip
      ' else
    %>
  
       <tr bgcolor="white">
  
    
    
    <% if  MyRec.fields("Carrier_sort") = gSpecies  or  isnull(MyRec.fields("Carrier_sort")) or gcount = 0 then %>

        
    
         <% if isnull(MyRec.fields("Transfer_date")) then
             strDays = round(Now()- cdate(MyRec.fields("Date_received")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

             else
             strDays = round(Now()- cdate(MyRec.fields("Transfer_date")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Trans_Carrier") & "' "
             end if  
          
 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close 
      If left(MyRec("Trailer"),4) = "GACX" then
    strFee = 0
    end if %>

	
        
        <% if isnull(MyRec.fields("Transfer_date")) then %>
        	<td  >       <font size="1" face="Arial">       <%= MyRec.fields("Carrier_sort")%>&nbsp</font></td>
        <td>&nbsp;</td>
         <%   if strDays  > strFree then %>
    <td align = center><font size="1" face="Arial"> $<%= (int(strDays) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="1" face="Arial">$0&nbsp;</font></td>
	<% end if %>



        <td align = center><font size="1" face="Arial"><%= strDays%></font></td>
<td>        <font size="1" face="Arial"> <%= MyRec.fields("Species")%>
<% if MyRec.fields("Rejected") = "YES" Then %>
        - Rejected
        <% end if %></font></td>
        		<td align = center ><font size="1" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
        			<td align = left > <font size="1" face="Arial"> &nbsp;&nbsp;<%= MyRec.fields("Trailer")%></font></td>
        			<td  >       <font size="1" face="Arial">        <%= MyRec.fields("REC_number")%></font></td>
        <% else %>
        	<td  >       <font size="1" face="Arial">      <%= MyRec.fields("Carrier_sort")%>&nbsp</font></td>
        <td><font size = "1" face = "arial">Shuttle</td></font></td>
        
             <% if isnull(MyRec.fields("Transfer_date")) then
             strDays = round(Now()- cdate(MyRec.fields("Date_received")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

             else
             strDays = round(Now()- cdate(MyRec.fields("Transfer_date")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Trans_Carrier") & "' "
             end if  
          
 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    
  If left(MyRec("Trailer"),4) = "GACX" then
    strFee = 0
    end if  %>  
        
                 <%   if strDays  > strFree then %>
    <td align = center><font size="1" face="Arial"> $<%= (int(strDays) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="1" face="Arial">$0&nbsp;</font></td>
	<% end if %>
        <td align = center><font size="1" face="Arial"><%= strDays%></font></td>
<td>        <font size="1" face="Arial"> <%= MyRec.fields("Species")%>
<% if MyRec.fields("Rejected") = "YES" Then %>
        - Rejected
        <% end if %></font></td>
		<td align = center ><font size="1" face="Arial"><%= MyRec.fields("Transfer_Date")%></font></td>
			<td align = left > <font size="1" face="Arial"> &nbsp;&nbsp;<%= MyRec.fields("Transfer_trailer_nbr")%></font></td>
			<td  >       <font size="1" face="Arial">        <%= MyRec.fields("PMO_Nbr")%></font></td>
		<% end if %>
		  <td  >        <font size="1" face="Arial">        <%= MyRec.fields("PO")%></font></td>
        <td  >        <font size="1" face="Arial">        <%= MyRec.fields("Vendor")%></font></td>
           <td align = center >        <font size="1" face="Arial">  <%= MyRec.fields("Location")%></font></td>
       
	
	<td align = RIGHT><font size="1" face="Arial"><%= MyRec.fields("Tons_received")%></font></td>
	<td><font size="1" face="Arial">&nbsp;&nbsp;<%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>
	</tr>

     	<%  gcount = gcount + 1
	 gTcount = gTcount + 1
	 gSpecies = MyRec.fields("Carrier_sort")
	 
	
       ii = ii + 1
       ' end if ' skip record for broke sent to WH
       MyRec.MoveNext
    
    %>
<% else %>
<td bgcolor ="white"><font face = arial size = 1><u></u>Total:&nbsp;<%= gcount%></u></font></td>


	</tr>
	<TR bgcolor = "white"><td colspan = 12>&nbsp;</td></tr>
	
	  <tr class=tablecolor2>
    

  
         <% if isnull(MyRec.fields("Transfer_date")) then
             strDays = round(Now()- cdate(MyRec.fields("Date_received")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

             else
             strDays = round(Now()- cdate(MyRec.fields("Transfer_date")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Trans_Carrier") & "' "
             end if  
          
 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close 
    
    If left(MyRec("Trailer"),4) = "GACX" then
    strFee = 0
    end if  %>

	
        
        <% if isnull(MyRec.fields("Transfer_date")) then %>
        	<td  >       <font size="1" face="Arial">       <%= MyRec.fields("Carrier_sort")%>&nbsp</font></td>
        <td>&nbsp;</td>
         <%   if strDays  > strFree then %>
    <td align = center><font size="1" face="Arial"> $<%= (int(strDays) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="1" face="Arial">$0&nbsp;</font></td>
	<% end if %>

        <td align = center><font size="1" face="Arial"><%= strDays%></font></td>
<td>        <font size="1" face="Arial"> <%= MyRec.fields("Species")%>
<% if MyRec.fields("Rejected") = "YES" Then %>
        - Rejected
        <% end if %></font></td>
        		<td align = center ><font size="1" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
        			<td align = left > <font size="1" face="Arial"> &nbsp;&nbsp;<%= MyRec.fields("Trailer")%></font></td>
        			<td  >       <font size="1" face="Arial">        <%= MyRec.fields("REC_number")%></font></td>
        <% else %>
        	<td  >       <font size="1" face="Arial">      <%= MyRec.fields("Carrier_sort")%>&nbsp</font></td>
        <td><font size = "1" face = "arial">Shuttle</td></font></td>

                
               <% if isnull(MyRec.fields("Transfer_date")) then
             strDays = round(Now()- cdate(MyRec.fields("Date_received")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

             else
             strDays = round(Now()- cdate(MyRec.fields("Transfer_date")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Trans_Carrier") & "' "
             end if  
          
 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    
  If left(MyRec("Trailer"),4) = "GACX" then
    strFee = 0
    end if  %>

	

         <%   if strDays  > strFree then %>
    <td align = center><font size="1" face="Arial"> $<%= (int(strDays) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="1" face="Arial">$0&nbsp;</font></td>
	<% end if %>  
        
        
        
        
        
        
        	<td align = center><font size="1" face="Arial"><%= strDays%></font></td>
<td>        <font size="1" face="Arial"> <%= MyRec.fields("Species")%>
<% if MyRec.fields("Rejected") = "YES" Then %>
        - Rejected
        <% end if %></font></td>
		<td align = center ><font size="1" face="Arial"><%= MyRec.fields("Transfer_Date")%></font></td>
			<td align = left > <font size="1" face="Arial"> &nbsp;&nbsp;<%= MyRec.fields("Transfer_trailer_nbr")%></font></td>
			<td  >       <font size="1" face="Arial">        <%= MyRec.fields("PMO_Nbr")%></font></td>
		<% end if %>
        <td  >        <font size="1" face="Arial">        <%= MyRec.fields("PO")%></font></td>
        <td  >        <font size="1" face="Arial">        <%= MyRec.fields("Vendor")%></font></td>
           <td align = center >        <font size="1" face="Arial">  <%= MyRec.fields("Location")%></font></td>
        
	
	<td align = RIGHT><font size="1" face="Arial"><%= MyRec.fields("Tons_received")%>&nbsp;</font></td>
	<td><font size="1" face="Arial"><%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>
	</tr>
		
	<% gcount = 1
	 gTcount = gTcount + 1
	 gSpecies = MyRec.fields("Carrier_sort")
	 
	
       ii = ii + 1
       ' end if ' skip record for broke sent to WH
       MyRec.MoveNext
        
    
  end if ' species is the same 
	wend %>

 
	  <tr class=tablecolor2>
<td><font size="1" face="Arial"><u></u>Total:&nbsp;<font size="1" face="Arial"><%= gcount%></u></font></td>
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
   <td>&nbsp;</td>
   <td>&nbsp;</td>
       <td>&nbsp;</td>
      <td>&nbsp;</td>
<td>&nbsp;</td>	
<td>&nbsp;</td>

<td>&nbsp;</td>
	</tr>

<TR><td><font face = arial size = 1><b></b>Grand Total:&nbsp;<%= gTcount%></b></font></td>
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
   <td>&nbsp;</td>
   <td>&nbsp;</td>
       <td>&nbsp;</td>
    
<td>&nbsp;</td>	
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
	</tr>

</table>
<% end if ' strAdmin %>