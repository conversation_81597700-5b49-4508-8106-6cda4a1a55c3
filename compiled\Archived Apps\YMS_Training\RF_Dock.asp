
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">
<TITLE>RF Dock Report</TITLE>

<!--#include file="classes/asp_cls_headerOYM.asp"-->
<!--#include file="classes/asp_cls_SessionStringOYM.asp"-->
 
<!--#include file="classes/asp_cls_DataAccessOYM.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

    <% Dim MyRec, strsql
   strDate = NOW()
 %>

<style type="text/css">
.style1 {
	border: 1px solid #C0C0C0;
}
.style2 {
	border: 1px solid #F0EBDB;
}
.style3 {
	font-weight: bold;
	border: 1px solid #F0EBDB;
}
</style>

<body bgcolor = "#FCFBF8"><br><p align = center>
<b><font face="Arial">RF Dock Report as of <%= strdate%>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</font></b></p><br>

           

 <table align = center width = 80% class="style1">
       <tr>
       <td class="style3"><font size="2" face="Arial">Dock</font></td>
       <td class="style3"><font size="2" face="Arial">Date Received</font></td>
		<td class="style3">
		<font size="2" face="Arial">Carrier</font></td>
		<td align="left" class="style3"><font size="2" face="Arial">Trailer</font></td>
				<td align="left" class="style3"><font size="2" face="Arial">Release #</font></td>

		<td align="left" class="style3"><font size="2" face="Arial">Grade</font></td>

		<td align="left" class="style3"><font size="2" face="Arial">Vendor</font></td>
		<td align="left" class="style3"><font size="2" face="Arial">City/State</font></td>
				<td align="left" class="style3"><font size="2" face="Arial">Weight</font></td>
	<td align="left" class="style3"><font size="2" face="Arial">Attention Flag</font></td>




<%
   
	strsql = "SELECT  Master_Table.Dock,  Date_time_in, RF_LD_Type, Inbound_BOL, Rec_weight,  Vendor, Origin, Master_Table.Carrier, "_
	&" Master_Table.TRAILER,  Master_Table.Attention_Flag "_
	&" FROM Master_Table WHERE Dock >0  AND [Status]= 1 "_
	&" AND Dock >=47 AND Dock <=81 "_
	&" ORDER BY Dock"

		
		Set MyRec = Server.CreateObject("ADODB.Recordset") 
   		MyRec.Open strSQL, Session("ConnectionString")

do until  MyRec.eof %>

<tr>

			
		 
    
    <td class="style2"><font face = Arial size = 2><%= MyRec.fields("Dock").value %></td>
        
    <td class="style2"><font face = Arial size = 2><%= MyRec.fields("Date_time_In").value %></td>

        <td class="style2"><font face = Arial size = 2><%= MyRec.fields("Carrier").value %></td>
 <td class="style2"><font face = Arial size = 2><%= MyRec.fields("Trailer").value %></td>
  <td class="style2"><font face = Arial size = 2><%= MyRec.fields("Inbound_BOL").value %></td>
 <td class="style2"><font face = Arial size = 2><%= MyRec.fields("RF_LD_Type").value %></td>
 <td class="style2"><font face = Arial size = 2><%= MyRec.fields("Vendor").value %></td>
 <td class="style2"><font face = Arial size = 2><%= MyRec.fields("Origin").value %></td>
 <td class="style2"><font face = Arial size = 2><%= MyRec.fields("Rec_Weight").value %></td>
     <td class="style2"><font face = Arial size = 2><%= MyRec.fields("Attention_Flag").value %></td>


     </tr>
    <%  
       
 	 MyRec.MoveNext
	
loop
MyRec.close

      
    %>

  </table>    
                        





  <font size="2" face="Arial">    
                        





  </table>

</BODY></font><br>