<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 14.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Recovered Paper Car Unload Entry</title>
</head>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->
<%

Dim strSQL, MyRec, strid, strDateReceived, strCarLocation, strFQE, strFQG, strFQ<PERSON>, strBQE, strBQG, strBQP, strMQD, strMQL, strMQM, strMQH
Dim strSpecies, strSAP_Nbr, strVendor, strPO, strRelease_Nbr, strTrailer, strTons_Received, strGenerator, strTime_unloaded
Dim strGen_City, strGen_State, strREC_Number, strCarDate_Received, strTotal_Bales, strOCC_Tech, strGradingDate_Received
Dim strDate_Unloaded, strLocation, strOther_Comments, strNet, strDeduction, strCarrier, strAdhesive
Dim strFQExcellent, strFQGood, strFQPoor, strFiber_Quality_Reject
Dim strBQExcellent, strBQGood, strBQPoor, strBale_Quality_Reject, strTransTrailer
Dim strMoisture_Dry, strMoisture_Light, strMoisture_Medium, strMoisture_Heavy, strWeight_Sample_Bale, strNbr_Wet_Bales, strBales_Rejected
Dim strGround_Wood, strCardboard, strNewsprint, strPlastics, strBrightness, strWet_Strength, strBeater_Dyed
Dim strEnvelopes, strTrash, strDirt, strFiber_Comments, strBale_Comments, strMoisture_Comments
strid = Request.querystring("id")
Call getdata()


 set objGeneral = new ASP_CLS_General

  

if objGeneral.IsSubmit() Then


	Call SaveData() 

End if
%>
<body>
<form name="form1" action="Trans_Car_unload_view.asp?id=<%=strid%>&t=<%=strTrailer%>" method="post">
 <input type="hidden" name="TR" value="<%= strTons_Received%>" >
<table border="0" cellpadding="0" cellspacing="0" width = 100% style="border-collapse: collapse" bordercolor="000000" >
  <tr>
    <td  bgcolor="#FFFFFF" bordercolor="#CCCCFF" >
    <p align="center"><font face="Arial" size="4">Recovered Paper Car Unload 
    Entry</font></td>

  <td align = center><font face="Arial"><b><a href="javascript:history.go(-1)">RETURN</a></b></font></td>
  </tr>
</table>
<table border="1" cellpadding="0" cellspacing="0" width = 100% style="border-collapse: collapse" bordercolor="000000" >
  <tr>
    <td bgcolor="#F0F0FF" bordercolor="#CCCCFF" >
  
    <font face="Arial" size="1">Species</font></b></td>
    <td bgcolor="#F0F0FF" bordercolor="#CCCCFF" >

    <font face="Arial" size="1">SAP Number</font></b></td>
    <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" >
 
    <font size="1" face="Arial">Vendor</font></b></td>
    <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" >
 
    <font size="1" face="Arial">PO</font></b></td>
    <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" >
  
    <font size="1" face="Arial">Release</font></b></td>
    <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" >    <font size="1" face="Arial">Original Car/Trailer</font></b></td>
      <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" >    <font size="1" face="Arial">Transfer Trailer</font></b></td>

    </tr>
  <tr height = 18>
    <td  bgcolor="white" bordercolor="#CCCCFF" height="19">
   <font size="1" face="Arial"><%= strSpecies%></td>
    <td  bgcolor="white" bordercolor="#CCCCFF" height="19"><font size="1" face="Arial"><%= strSAP_Nbr%></td>
    <td  bgcolor="white"  bordercolor="#CCCCFF" height="19"><font size="1" face="Arial"><%= strVendor%></td>
    <td bgcolor="white" " bordercolor="#CCCCFF" height="19"><font size="1" face="Arial"><%= strPO%></td>
    <td bgcolor="white"  bordercolor="#CCCCFF" height="19"><font size="1" face="Arial"><%= strRelease_Nbr%></td>
    <td bgcolor="white"  bordercolor="#CCCCFF" height="19"><font size="1" face="Arial"><%= strTrailer%></td>
   <td bgcolor="white"  bordercolor="#CCCCFF" height="19"><font size="1" face="Arial"><b><%= strTransTrailer%></b></td>

  </tr>

  <tr>
    <td bgcolor="#F0F0FF" bordercolor="#CCCCFF" height="21" align="left"><b>
    <font size="1" face="Arial">Generator</font></b></td>
    <td bgcolor="#F0F0FF" bordercolor="#CCCCFF" height="21" align="left"><b>
    <font size="1" face="Arial">Generator City</font></b></td>
    <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" height="21" align="left"><b>
    <font size="1" face="Arial">Generator State</font></b></td>
    <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" height="21" align="left"><b>
    <font face="Arial" size="1">Receipt Number</font></b></td>
    <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" height="21" align="left"><b>
    <font face="Arial" size="1">Date Received</font></b></td>
 <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" height="21"><b>
    <font face="Arial" size="1">&nbsp;Location</font></b></td>
   <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" height="21"><b>
    <font face="Arial" size="1">&nbsp;</font></b><font size="1" face="Arial">Carrier</font></b></td>
  
  </tr>
  <tr>
    <td  bordercolor="#CCCCFF"><font face="Arial" size="1"><%= strGenerator%></font></td>
    <td  bordercolor="#CCCCFF"><font face="Arial" size="1"><%= strGen_City%></font></td>
    <td  bordercolor="#CCCCFF"><font face="Arial" size="1"><%= strGen_State%></font></td>
    <td  bordercolor="#CCCCFF"><font face="Arial" size="1"><%= strRec_Number%></font></td>
    <td  bordercolor="#CCCCFF"><font face="Arial" size="1"><%= strDateReceived%></font></td>
     <td  bordercolor="#CCCCFF"><font face="Arial" size="1"><%= strCarLocation%></font></td>
   <td bgcolor="white"  bordercolor="#CCCCFF" height="19"><font size="1" face="Arial"><%= strCarrier%></td>
  </tr>
</table>
<table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" height="51" >
    <tr>
    <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="18"><b>	<font face="Arial" size="1">Date Unloaded</font></b></td>
    <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="18"><b>	<font face="Arial" size="1">Location Unloaded</font></b></td>
    <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="18"><b>	<font face="Arial" size="1">Other Comments</font></b></td>
       <td  bgcolor="#FF0000" bordercolor="#000000" height="18" align="center"><b>	
	<font face="Arial" size="1">&nbsp;</font><font color="#FFFFFF"><font face="Arial" size="1">Total Bales&nbsp;</font><font face="Arial" size="2"><br><font size = 1>Required</font></font></b></td>
   
    <td bgcolor="#F0F0FF" bordercolor="#CCCCFF" align="center"><b>	<font face="Arial" size="1">&nbsp;OCC Tech</font></b></td>
    <td align = center bgcolor="#D9FFEC" bordercolor="#CCCCFF" ><b><font size="1" face="Arial">&nbsp;Tons Received</font></b></td>
    <td align = center bgcolor="#D9FFEC" bordercolor="#CCCCFF" ><b>  <font face="Arial" size="1">Deduction</font></b> </td>
    <td  bgcolor="#D9FFEC" bordercolor="#CCCCFF" align = center>	<font face="Arial" size="1">&nbsp;Net&nbsp;</font></b></td>
   
  </tr>
  
    <tr>
    <td  bordercolor="#CCCCFF">	<font face="Arial"><%= strDate_Unloaded%></font></td>
    <td bordercolor="#CCCCFF">	
	<font face="Arial">	
	<%= strLocation %></td>
    <td bordercolor="#CCCCFF">	
	<font face="Arial">	
	<%= strOther_Comments%></font></td>
	   <td  bordercolor="#CCCCFF" align="center" bgcolor="#FF0000">	
	<font face="Arial" color = white>	<b>
<%= strTotal_bales%></font></b></td>
    
    <td  bordercolor="#CCCCFF" align="center">
	<font face="Arial" size="2">
	<%= strOCC_Tech%></font></td>
    <td  bgcolor="white" bordercolor="#CCCCFF">
	<p align="center">
	<font face="Arial">
	

	<%= strTons_received%>
	</font></td>
    <td  bgcolor="white"  bordercolor="#CCCCFF">
	<p align="center"><b> <font size="2" face="Arial">&nbsp;&nbsp;</font></b>
  <%= strDeduction%></font><b><font size="2" face="Arial">&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;</font></b></td>
    <td bgcolor="white"  bordercolor="#CCCCFF">	
	<p align="center">	
	<font face="Arial">	
	<%= strNet%></font></td>
    
  </tr>
  
  </table>
<table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" height="240">
  <tr>
    <td width="35%" bgcolor="#FFFFD7" bordercolor="#000000" height="19" colspan="6">
    <p align="center"><b><font size="2" face="Arial">Fiber Quality</font></b></td>
    <td width="21%" bgcolor="#FFFFD7" bordercolor="#000000" height="19" colspan="4">
    <p align="center"><b><font size="2" face="Arial">Bale Quality</font></b></td>
    <td width="48%" bgcolor="#FFFFD7" bordercolor="#000000" colspan="8" height="19">
    <p align="center"><b><font size="2" face="Arial">Moisture Rating</font></b></td>
  </tr>
  <tr>
  
    <td width="8%" bgcolor="#FFFFD7" bordercolor="#000000" colspan="2" height="36">
    <p align="center"><b>
    <font face="Arial" size="1"><br>Excellent</font></b></td>
    <td width="7%" bgcolor="#FFFFD7" bordercolor="#000000" colspan="2" height="36">
    <p align="center"><b>
    <font size="1" face="Arial"><br>Good</font></b></td>
    <td width="5%" bgcolor="#FFFFD7" bordercolor="#000000" height="36">
    <p align="center"><b>
    <font size="1" face="Arial"><br>Poor</font></b></td>
    <td width="7%" bgcolor="#FFFFD7" bordercolor="#000000" height="36">
    <p align="center">
    <font size="1" face="Arial">Down Grade Reject</font></td>
    <td width="5%" bgcolor="#FFFFD7" bordercolor="#000000" height="36">
    <p align="center"><b>
    <font face="Arial" size="1"><br>&nbsp;Excellent&nbsp;</font></b></td>
    <td width="6%" bgcolor="#FFFFD7" bordercolor="#000000" height="36">
    <p align="center"><b>
    <font size="1" face="Arial"><br>Good</font></b></td>
    <td width="4%" bgcolor="#FFFFD7" bordercolor="#000000" height="36">
    <p align="center"><b>
    <font size="1" face="Arial"><br>Poor</font></b></td>
    <td width="6%" bgcolor="#FFFFD7" bordercolor="#000000" height="36">
    <p align="center">
    <font size="1" face="Arial">Down Grade <br>Reject </font></td>
    <td width="8%" bgcolor="#FFFFD7" bordercolor="#000000" colspan="1" height="36" align="center"><b>
    <font size="1" face="Arial"><br>Dry</font></b></td>
    <td width="5%" bgcolor="#FFFFD7" bordercolor="#000000" height="36" align="center"><b>
    <font size="1" face="Arial"><br>Light</font></b></td>
    <td width="4%" bgcolor="#FFFFD7" bordercolor="#000000" height="36" align="center">
    <p align="center"><b><font size="1" face="Arial"><br>&nbsp;Medium&nbsp;</font></b></td>
        <td width="4%" bgcolor="#FFFFD7" bordercolor="#000000" height="36" align="center">
    <p align="center"><b><font size="1" face="Arial"><br>&nbsp;Heavy&nbsp;</font></b></td>
    <td width="29%" bgcolor="#FFFFD7" bordercolor="#000000" colspan="3" height="36">
	<p align="center"><font size = 2 face="Arial"><b>
    Heavy</b><font size = 1> - weigh a bale<br> to represent all wet bales 
    marked below</font></b></td>
  </tr>
  <tr>
   
    <td colspan = 5   align = center>
 <fieldset style="padding: 0" >
	
	&nbsp;&nbsp; <input type="radio" value="3" name="FQ" <% if strFQExcellent = -1 then %> checked<%end if %> >&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp; 
	<input type="radio" name="FQ" value="2" <% if strFQGood = -1 then %> checked <%end if %>>	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;
	<input type="radio" name="FQ" value="1" <% if strFQPoor = -1 then %>checked <%end if %>></fieldset></td>
  
    <td width="7%" height="27" align = center>
	<%= strFiber_Quality_Reject%></td>

    <td colspan = 3   align = center>
 <fieldset style="padding: 0">
	
	<input type="radio" value="3" name="BQ" <% if strBQExcellent = -1 then %> checked <%end if %>>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	<input type="radio" name="BQ" value="2" <% if strBQGood = -1 then %> checked<%end if %>>	&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;
	<input type="radio" name="BQ" value="1" <% if strBQPoor = -1 then %> checked <%end if %>></fieldset></td>
    <td width="6%" height="27">
	<p align="center">
<%= strBale_Quality_Reject%></td>
        <td colspan = 4   align = center>
 <fieldset style="padding: 0">
	
	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <input type="radio" value="3" name="MQ" <% if strMoisture_dry = -1 then %> checked <%end if %>>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	<input type="radio" name="MQ" value="2" <% if strMoisture_light = -1 then %> checked <%end if %>>	&nbsp;&nbsp;&nbsp; &nbsp;
	<input type="radio" name="MQ" value="1" <% if strMoisture_medium = -1 then %> checked <%end if %>>   &nbsp;&nbsp;&nbsp; &nbsp;
	<input type="radio" name="MQ" value="0" <% if strMoisture_heavy = -1 then %> checked <%end if %>></fieldset></td>
    <td width="15%" bgcolor="#FFFFD7" bordercolor="#000000" height="27" >
	<p align="center"><b>
    <font face="Arial" size="1">Weight of Sample Bales</font></b></td>
    <td width="12%" height="27" colspan = 2>
	<p align="center">
<%= strWeight_Sample_Bale%></td>
  </tr>
  <tr>
    <td width="35%" bgcolor="#FFFFD7" bordercolor="#000000" colspan="6" height="37"> <b>
    <font face="Arial" style="font-size: 9pt">Bale Count - Down Grade/Reject 
    </font></b></td>
    <td width="35%" bgcolor="#FFFFFF" bordercolor="#000000" colspan="2" height="37" align = center>
	
	<%= strBales_Rejected%></td>
 <td width="38%" bgcolor="#FFFFD7" bordercolor="#000000" colspan="6" height="37">&nbsp;</td>
    <td width="15%" bgcolor="#FFFFD7" bordercolor="#000000" height="37">
	<p align="center"><b>
    <font size="1" face="Arial">Number of Bales to Apply</font></b></td>
    <td width="12%" height="37" colspan = 2>
	<p align="center">
<%= strNbr_Wet_Bales%></td>
  </tr>


  <tr>
    <td width="68%" colspan="14" bgcolor="#FFFFD7" height="25">
    <p align="center"><b><font face="Arial" style="font-size: 9pt">Contaminants</font></b></td>
    <td width="4%" bgcolor="#FFFFD7" height="25" bordercolor="#FFFFD7">&nbsp;</td>
    <td width="29%" bgcolor="#FFFFD7" bordercolor="#FFFFD7" colspan="2" height="25">&nbsp;</td>
  </tr>
  <tr>
    <td width="7%" bgcolor="#FFFFD7" height="40" align="center"><b><font face="Arial" size="1">
    Ground Wood</font></b></td>
    <td width="11%" colspan="2" bgcolor="#FFFFFF" height="40" align = center>
    <input type="checkbox" name="Ground_Wood" value="ON" <% if strGround_wood = -1 then%> checked <%end if %>></td>
    <td width="6%" colspan="2" bgcolor="#FFFFD7" height="40" align="center"><b><font face="Arial" size="1">
    Wet Strength</font></b></td>
    <td width="4%" colspan="1" bgcolor="#FFFFFF" height="40" align = center>
    <input type="checkbox" name="Wet_Strength" value="ON" <% if strWet_Strength = -1 then%> checked <%end if %>></td>
  
    <td width="7%" bgcolor="#FFFFD7" height="40" align="center"><b><font face="Arial" size="1">
    Cardboard</font></b></td>
    <td width="5%" colspan="1" bgcolor="#FFFFFF" height="40" align = center>
    <input type="checkbox" name="Cardboard" value="ON" <% if strCardboard = -1 then%> checked <% end if %>></td>
    <td width="6%" bgcolor="#FFFFD7" height="40" align="center"><b><font face="Arial" size="1">
    Beater Dyed</font></b></td>
    <td width="4%" colspan="1" bgcolor="#FFFFFF" height="40" align = center>
    <input type="checkbox" name="Beater_Dyed" value="ON" <% if strBeater_Dyed = -1 then%> checked <%end if %>></td>
  
    <td width="6%" bgcolor="#FFFFD7" height="40" align="center"><b><font face="Arial" size="1">
	Newsprint</font></b></td>
    <td width="3%" colspan="1" bgcolor="#FFFFFF" height="40" align = center>
    <input type="checkbox" name="Newsprint" value="ON" <% if strNewsprint = -1 then%> checked <%end if %>></td>
    <td width="5%" bgcolor="#FFFFD7" height="40" align="center" colspan="2">&nbsp;</td>
  <td width="5%" bgcolor="#FFFFD7" height="40" bordercolor="#FFFFD7">&nbsp;</td>
    <td width="4%" bgcolor="#FFFFD7" height="40" bordercolor="#FFFFD7">&nbsp;</td>
    <td width="29%" bgcolor="#FFFFD7" bordercolor="#FFFFD7" colspan="2" height="40">&nbsp;</td>
  </tr>

  <tr>
    <td width="7%" bgcolor="#FFFFD7" height="31" align="center"><b><font face="Arial" size="1">
    Plastic</font></b></td>
    <td width="11%" colspan="2" bgcolor="#FFFFFF" height="31" align = center>
    <input type="checkbox" name="Plastics" value="ON" <% if strPlastics = -1 then%> checked <%end if %>></td>
    <td width="6%" colspan="2" bgcolor="#FFFFD7" height="31" align="center"><b><font face="Arial" size="1">
    Trash</font></b></td>
    <td width="4%" colspan="1" bgcolor="#FFFFFF" height="31" align = center>
    <input type="checkbox" name="Trash" value="ON" <% if strTrash = -1 then%> checked <%end if %>></td>
    <td width="7%" bgcolor="#FFFFD7" height="31" align="center"><b>
	<font face="Arial" size="1">Brightness</font></b></td>
    <td width="5%" colspan="1" bgcolor="#FFFFFF" height="31" align = center>
    <input type="checkbox" name="Brightness" value="ON" <% if strBrightness = -1 then%> checked <%end if %>></td>
    <td width="6%" bgcolor="#FFFFD7" height="31" align="center"><b>
	<font face="Arial" size="1">Dirt</font></b></td>
    <td width="4%" colspan="1" bgcolor="#FFFFFF" height="31" align = center>
    <input type="checkbox" name="Dirt" value="ON" <% if strDirt = -1 then%> checked <%end if %>></td>
       <td width="5%" bgcolor="#FFFFD7" height="40" align="center"><b>
	<font face="Arial" size="1">Envelopes</font></b></td>
    <td width="5%" colspan="1" bgcolor="#FFFFFF" height="40" align = center>
    <input type="checkbox" name="Envelopes" value="ON" <% if strEnvelopes = -1 then%> checked <%end if %>></td>
     <td width="5%" bgcolor="#FFFFD7" height="40" align="center"><b>
	<font face="Arial" size="1">Adhesives</font></b></td>
   <td width="5%" colspan="1" bgcolor="#FFFFFF" height="40" align = center>
    <input type="checkbox" name="Adhesive" value="ON" <% if strAdhesive = -1 then%> checked <%end if %>></td>
    <td width="4%" bgcolor="#FFFFD7" height="40" bordercolor="#FFFFD7">&nbsp;</td>
    <td width="29%" bgcolor="#FFFFD7" bordercolor="#FFFFD7" height="40">&nbsp;</td>
  </tr>
</table>
   <table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" >
  <tr>
    <td  bgcolor="#FFFFD7" height="19" bordercolor="#000000" align = center>
  <b><font size="2" face="Arial">Fiber Quality Comments</font></b></td>
    <td  bgcolor="#FFFFD7" height="19" bordercolor="#000000" align = center>
   <b><font face="Arial" size="2">Bale Quality Comments</font></b></td>
       <td  bgcolor="#FFFFD7" height="19" bordercolor="#000000" align = center>
        <b><font size="2" face="Arial">Moisture Rating Comments</font></b></td>
  </tr>



  <tr>
    <td  bgcolor="#FFFFFF" height="45">
   <%= strFiber_Comments%></td>
    
    

    <td bgcolor="#FFFFFF" bordercolor="#000000"  height="45">
  <%= strBale_Comments%></td>
  
    <td  bgcolor="#FFFFFF" bordercolor="#000000" height="45">
      <p align="center">
 <%= strMoisture_Comments%></td>

  </tr>

</table>
</form>
  <%
  Function getData() 
  strid = Request.querystring("id")
  strSql="SELECT tblCars.* FROM tblCars WHERE CID= '" & strid & "'" 
  
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString")
  
  strid = Request.querystring("id")
  strSpecies = MyRec.Fields("Species")
  strSAP_Nbr = MyRec.Fields("SAP_Nbr")
  strVendor = MyRec.Fields("Vendor")
  strPO = MyRec.Fields("PO")
  strRelease_Nbr = MyRec.Fields("Release_Nbr")
  strTrailer = MyRec.Fields("Trailer")
  strTons_Received = MyRec.Fields("Tons_Received")
  strGenerator = MyRec.Fields("Generator")
  strGen_City = MyRec.Fields("Gen_City")
  strGen_State = MyRec.Fields("Gen_State")
  strREC_Number = MyRec.Fields("REC_Number")
  strDateReceived = MyRec.Fields("Date_received")
  
  strDate_Unloaded = formatdatetime(Now(),2)
  strCarLocation = MyRec.fields("Location")
  strOther_Comments = MyRec.fields("Other_Comments")
  strCarrier = MyRec.fields("Carrier")
  strTons_received = MyRec.fields("Tons_received")
  strDeduction= MyRec.fields("Deduction")
  strNet= MyRec.fields("Net")
  strTransTrailer = MyRec.fields("Transfer_trailer_nbr")
  strLocation = MyRec.fields("Location")
  
  MyRec.close
  
     strSql="SELECT tblOCCGrading.* FROM tblOCCGrading WHERE CID= '" & strid & "'" 
  
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
  strTotal_Bales = MyRec.fields("Trans_total_Bales")
  strOCC_Tech = MyRec.fields("Trans_OCC_Tech")
  strFiber_Comments = MyRec.fields("Trans_Fiber_comments")
  strBale_Comments = MyRec.fields("Trans_Bale_Comment")
  strMoisture_Comments = MyRec.fields("Trans_Moisture_Comment")
    
  strFiber_Quality_Reject = MyRec.fields("Trans_Fiber_Quality_Reject")
  strBale_Quality_Reject = MyRec.fields("Trans_Bale_Quality_Reject")
  strWeight_Sample_Bale = MyRec.fields("Trans_Weight_Sample_Bale")
  strNbr_wet_bales = MyRec.fields("Trans_Nbr_wet_bales")
  strBales_rejected = MyRec.fields("Trans_Bales_Rejected")
  strGround_Wood = MyRec.fields("Trans_Ground_Wood")
  strCardboard = MyRec.fields("Trans_Cardboard")
  strNewsprint = MyRec.fields("Trans_Newsprint")
  strPlastics = MyRec.fields("Trans_Plastics")
  strBrightness = MyRec.fields("Trans_Brightness")
  strWet_Strength = MyRec.fields("Trans_Wet_Strength")
  strBeater_Dyed = MyRec.fields("Trans_Beater_Dyed")
  strEnvelopes = MyRec.fields("Trans_Envelopes")
  strTrash = MyRec.fields("Trans_Trash")
  strDirt = MyRec.fields("Trans_Dirt")
  strAdhesive = MyRec.fields("Trans_Adhesive")
  strBQExcellent = MyRec.fields("Trans_BQ_Excellent")
  strBQGood = MyRec.fields("Trans_BQ_Good")
  strBQPoor = MyRec.fields("Trans_BQ_Poor")
  strFQExcellent = MyRec.fields("Trans_FQ_Excellent")
  strFQGood = MyRec.fields("Trans_FQ_Good")
  strFQPoor = MyRec.fields("Trans_FQ_Poor")
  strMoisture_Dry = MyRec.fields("Trans_Moisture_dry")
  strMoisture_Light= MyRec.fields("Trans_Moisture_Light")
  strMoisture_Medium= MyRec.fields("Trans_Moisture_Medium")
  strMoisture_Heavy= MyRec.fields("Trans_Moisture_Heavy")
  
  
  MyRec.close
  
  End Function
  
  
 
   %>            
  

</body>

</html>
<!--#include file="Fiberfooter.inc"-->