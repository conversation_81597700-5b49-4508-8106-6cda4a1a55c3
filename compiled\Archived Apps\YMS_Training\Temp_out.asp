﻿<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->

<%

dim strID, strStatus, strsql, Myconn
	
strID = Request.querystring("id")
strStatus = request.querystring("s")

 If strstatus  = "IN" then  
    

	strSQL = "Update tblCars set Oasis_status = 'OUT' where CID = " &  strID & ""
	else
		strSQL = "Update tblCars set Oasis_status = 'IN' where CID = " &  strID & ""
		end if

			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			response.redirect("Temp_out_Yard_trucks.asp")


%>