
<html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Daily Inventory Report</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"--> 
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->
<!--#include file="classes/asp_cls_sessionOWB.asp"-->
	
	
	   <style type="text/css">
.style1 {
	font-family: Arial;
	font-size: x-small;
}
.style4 {
	border: 1px solid #F0F0F0;
}
.style14 {
	border: 1px solid #F0F0F0;
	text-align: center;
	background-color: #F3F3F3;
	font-size: x-small;
}
.style15 {
	font-family: Arial;
}
.style16 {
	font-size: x-small;
}
.style17 {
	border: 1px solid #F0F0F0;
	text-align: center;
		font-family: Arial;
	background-color: #FFD7FF;
}
.style18 {
	border: 1px solid #000000;
}
.style21 {
	border: 1px solid #F0F0F0;
	font-family: Arial;
	font-size: x-small;
	text-align: center;
	background-color: #FFFFCC;
}
.style31 {
	border-width: 1px;
	background-color: #E8E8FF;
}
.style34 {
	border-color: #C0C0C0;
	border-width: 1px;
	text-align: center;
	font-family: Arial;
	font-size: x-small;
	background-color: #FAFBBF;
}
.style38 {
	border-color: #C0C0C0;
	border-width: 1px;
	text-align: center;
	font-family: Arial;
	font-size: x-small;
	background-color: #FFD7FF;
}
.style40 {
	border-left: 1px solid #C0C0C0;
	border-right: 1px solid #000080;
	border-top: 1px solid #C0C0C0;
	border-bottom: 1px solid #000080;
	background-color: #E8E8FF;
}
.style41 {
	border: 1px solid #808080;
	font-size: x-small;
}
.style44 {
	border: 1px solid #C0C0C0;
	text-align: center;
		font-family: Arial;
		font-size: x-small;
	background-color: #F3F3F3;
}
.style47 {
	border: 1px solid #F0F0F0;
	text-align: center;
	font-family: Arial;
	background-color: #F3F3F3;
	font-size: x-small;
}
.style48 {
	border: 1px solid #F0F0F0;
	font-family: Arial;
	font-size: x-small;
	text-align: center;
	background-color: #FFD7FF;
}
.style50 {
	border: 1px solid #F0F0F0;
	background-color: #E8E8FF;
}
.style54 {
	border-width: 1px;
	background-color: #FFECFF;
}
.style56 {
	border-width: 1px;
	background-color: #FCFDDB;
	text-align: center;
}
.style62 {
	border-width: 1px;
	background-color: #FFECFF;
	font-size: x-small;
	text-align: center;
}
.style67 {
	border: 1px solid #FFFFFF;
	background-color: #E8E8FF;
}
.style70 {
	border: 1px solid #FFFFFF;
	font-size: x-small;
	background-color: #FAFBBF;
}
.style71 {
	border-style: solid;
	border-width: 1px;
}
.style72 {
	border: 1px solid #000000;
	font-size: x-small;
	background-color: #FFFFDD;
	text-align: center;
}
.style73 {
	border: 1px solid #808080;
	font-size: x-small;
	background-color: #FFFFDD;
	text-align: center;
}
.style77 {
	border-left: 1px solid #C0C0C0;
	border-right: 1px solid #808080;
	border-top: 1px solid #C0C0C0;
	border-bottom: 1px solid #808080;
	font-size: x-small;
	text-align: center;
}
.style80 {
	border: 1px solid #808080;
	background-color: #FFFFDD;
	font-size: x-small;
}
.style81 {
	color: black;
	font-size: 10.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: 1.0pt solid windowtext;
	border-bottom: .5pt solid windowtext;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #E8E8FF;
}
.style82 {
	color: black;
	font-size: 10.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right: 1.0pt solid windowtext;
	border-top: 1.0pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #E8E8FF;
}
.style83 {
	color: black;
	font-size: 10.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: .5pt solid windowtext;
	border-bottom: 1.0pt solid windowtext;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #FCFBF5;
}
.style84 {
	color: black;
	font-size: 10.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left-style: none;
	border-left-color: inherit;
	border-left-width: medium;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: .5pt solid windowtext;
	border-bottom: 1.0pt solid windowtext;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #FCFBF5;
}
.style85 {
	color: black;
	font-size: 10.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left-style: none;
	border-left-color: inherit;
	border-left-width: medium;
	border-right: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom: 1.0pt solid windowtext;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #FCFBF5;
}
.style86 {
	color: black;
	font-size: 10.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom: 1.0pt solid windowtext;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #FCFBF5;
}
.style87 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top-style: none;
	border-top-color: inherit;
	border-top-width: medium;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #00B0F0;
}
.style88 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-style: none;
	border-color: inherit;
	border-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #00B0F0;
}
.style89 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left-style: none;
	border-left-color: inherit;
	border-left-width: medium;
	border-right: 1.0pt solid windowtext;
	border-top-style: none;
	border-top-color: inherit;
	border-top-width: medium;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #00B0F0;
}
.style90 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right: 1.0pt solid windowtext;
	border-top-style: none;
	border-top-color: inherit;
	border-top-width: medium;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #00B0F0;
}
.style91 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #92D050;
}
.style92 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left-style: none;
	border-left-color: inherit;
	border-left-width: medium;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #92D050;
}
.style93 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left-style: none;
	border-left-color: inherit;
	border-left-width: medium;
	border-right: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #92D050;
}
.style94 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #92D050;
}
.style95 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: yellow;
}
.style96 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left-style: none;
	border-left-color: inherit;
	border-left-width: medium;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: yellow;
}
.style97 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left-style: none;
	border-left-color: inherit;
	border-left-width: medium;
	border-right: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: yellow;
}
.style98 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: yellow;
}
.style99 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: red;
}
.style100 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left-style: none;
	border-left-color: inherit;
	border-left-width: medium;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: red;
}
.style101 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left-style: none;
	border-left-color: inherit;
	border-left-width: medium;
	border-right: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: red;
}
.style102 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: red;
}
.style103 {
	font-size: xx-small;
}
.style104 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #E8E8FF;
	text-align: center;
	font-size: x-small;
	font-weight: bold;
}
.style105 {
	border: 1px solid #E1E1E1;
	font-size: x-small;
	text-align: center;
}
.style106 {
	border: 1px solid #E1E1E1;
	font-size: x-small;
	text-align: center;
	font-weight: bold;
}
.style107 {
	font-size: x-small;
	font-weight: bold;
}
.style108 {
	border: 1px solid #F0F0F0;
	font-family: Arial;
	font-size: x-small;
	text-align: center;
	background-color: #FFD7FF;
	font-weight: bold;
}
.style109 {
	border-width: 1px;
	background-color: #FCFDDB;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	text-align: center;
}
.style112 {
	border-width: 1px;
	background-color: #FCFDDB;
	font-size: x-small;
	text-align: center;
}
.style114 {
	border: 1px solid #F0F0F0;
	text-align: center;
	font-family: Arial;
	background-color: #F3F3F3;
	font-size: x-small;
	font-weight: bold;
}
.style115 {
	text-align: center;
}
.style117 {
	border-right: 1px solid #000000;
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: red;
}
.style118 {
	border-right: 1px solid #000000;
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: yellow;
}
.style119 {
	border-right: 1px solid #000000;
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #92D050;
}
.style120 {
	border-right: 1px solid #000000;
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-top-style: none;
	border-top-color: inherit;
	border-top-width: medium;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #00B0F0;
}
.style121 {
	border-right: 1px solid #000000;
	color: black;
	font-size: 10.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom: 1.0pt solid windowtext;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #FCFBF5;
}
.style122 {
	text-align: center;
	font-size: x-small;
}
.style123 {
	border: 1px solid #F0F0F0;
	text-align: center;
	background-color: #F3F3F3;
	font-size: x-small;
	font-weight: bold;
}
	   .auto-style1 {
		   border: 1px solid #FFFFFF;
		   font-size: x-small;
		   background-color: #E2F3FE;
	   }
	   .auto-style2 {
		   border: 1px solid #000000;
		   font-size: x-small;
		   background-color: #E2F3FE;
		   text-align: center;
	   }
	   .auto-style3 {
		   border: 1px solid #808080;
		   font-size: x-small;
		   background-color: #E2F3FE;
		   text-align: center;
	   }
	   .auto-style4 {
		   border: 1px solid #808080;
		   background-color: #E2F3FE;
		   font-size: x-small;
	   }
	   .auto-style5 {
		   border-left: 1px solid #C0C0C0;
		   border-right-style: solid;
		   border-right-width: 1px;
		   border-top: 1px solid #C0C0C0;
		   border-bottom-style: solid;
		   border-bottom-width: 1px;
		   background-color: #E2F3FE;
		   text-align: center;
		   font-size: x-small;
		   font-weight: bold;
	   }
	   .auto-style6 {
		   color: black;
		   font-size: 10.0pt;
		   font-weight: 700;
		   font-style: normal;
		   text-decoration: none;
		   font-family: Arial, sans-serif;
		   text-align: center;
		   vertical-align: middle;
		   white-space: normal;
		   border-left: 1.0pt solid windowtext;
		   border-right-style: none;
		   border-right-color: inherit;
		   border-right-width: medium;
		   border-top: 1.0pt solid windowtext;
		   border-bottom: .5pt solid windowtext;
		   padding-left: 1px;
		   padding-right: 1px;
		   padding-top: 1px;
		   background: #E2F3FE;
	   }
	   .auto-style7 {
		   color: black;
		   font-size: 10.0pt;
		   font-weight: 700;
		   font-style: normal;
		   text-decoration: none;
		   font-family: Arial, sans-serif;
		   text-align: center;
		   vertical-align: middle;
		   white-space: normal;
		   border-left: 1.0pt solid windowtext;
		   border-right: 1.0pt solid windowtext;
		   border-top: 1.0pt solid windowtext;
		   border-bottom-style: none;
		   border-bottom-color: inherit;
		   border-bottom-width: medium;
		   padding-left: 1px;
		   padding-right: 1px;
		   padding-top: 1px;
		   background: #E2F3FE;
	   }
	   .auto-style8 {
		   border: 1px solid #F0F0F0;
		   background-color: #E2F3FE;
	   }
	   .auto-style9 {
		   border-left: 1px solid #C0C0C0;
		   border-right: 1px solid #000080;
		   border-top: 1px solid #C0C0C0;
		   border-bottom: 1px solid #000080;
		   background-color: #E2F3FE;
	   }
	   .auto-style10 {
		   border-width: 1px;
		   background-color: #E2F3FE;
		   text-align: center;
	   }
	   .auto-style11 {
		   border-width: 1px;
		   background-color: #E2F3FE;
		   font-size: x-small;
		   text-align: center;
	   }
	   .auto-style12 {
		   border-width: 1px;
		   background-color: #E2F3FE;
		   font-family: Arial, Helvetica, sans-serif;
		   font-size: x-small;
		   text-align: center;
	   }
	   .auto-style13 {
		   border: 1px solid #F0F0F0;
		   font-family: Arial;
		   font-size: x-small;
		   text-align: center;
		   background-color: #E2F3FE;
	   }
	   .auto-style14 {
		   border-color: #C0C0C0;
		   border-width: 1px;
		   text-align: center;
		   font-family: Arial;
		   font-size: x-small;
		   background-color: #E2F3FE;
	   }
	   .auto-style15 {
		   border: 1px solid #FFFFFF;
		   background-color: #E2F3FE;
	   }
	   .auto-style16 {
		   border-width: 1px;
		   background-color: #E2F3FE;
	   }
	   .auto-style17 {
		   border-collapse: collapse;
	   }
	   </style>
</head>
<%  '  1812 ends first section of Previous Consumption
	Dim strsQL, rstDaily, strBegDate, strEndDate, strcount, objPro, strCdate, strKBLD, strOther, strOCC, strMonthName, strMonthDay, strDayofweek
	Dim strdate, objDAC, strOCCRail, strOtherRail, strKBLDRail, strOCCAvg,  strKBLDAvg, strnow, MyConn, strTday7
	Dim strdate1, strYdate
	Dim strOB, MyRec2, strsql2
	Dim KBLD_one, KBLD_Two, KBLD_Three, KBLD_Four, KBLD_Five, KBLD_Six, KBLD_Seven
	DIm OCC_one, OCC_Two, OCC_Three, OCC_Four, OCC_Five, OCC_six, OCC_Seven, MyRec8, strSQL8
	Dim strMXP, MXP_One, MXP_Two, MXP_three, MXP_Four, MXP_Five, MXP_Six, MXP_Seven, strMXPRail, strMXPAvg
	
	strnow = formatdatetime(Now(),2)
	strBegdate = dateadd("d", -7, strnow)
	strEnddate = formatdatetime(now())
	strTomorrow = dateadd("d", 1, strnow)
	strYdate = dateadd("d", -3, now())
		
	%>

<p class="style1"><strong>KC MOBILE Daily RF REPORT<br><%= Now()%> </strong></p>
<font face="arial" size="1"><table class="style4" style="width: 70%">
<tr><td class="auto-style4" rowspan="2">&nbsp;</td>
	<td class="auto-style4" rowspan="2"><strong>Previous Days Consumption for Reference</strong></td>
	<td class="auto-style2"><font face="arial" size="1">Trailers/Rail</td>
			<td class="auto-style2"><font face="arial" size="1">Trailers/Rail</td>

	<td class="auto-style2"><span class="style103">Trailers/Rail</span></td>

	<td class="auto-style2"><font face="arial" size="1"> <span class="style103">Trailers/Rail</span></td>

 

	<td class="auto-style2"><font face="arial" size="1"> <span class="style103">Trailers/Rail</span></td>

	</tr>
<tr><td class="auto-style3">&nbsp;KBLD</td>
 
			<td class="auto-style3">&nbsp;&nbsp; OF3</td>

	 
	<td class="auto-style3">&nbsp;PMX</td>
	<td class="auto-style3">&nbsp;SHRED</td>
	<td class="auto-style3">HBX</td>
	</tr>
	<tr><td class="style41"></td>
  <td   class="style77">Last 30 Day Avg (In TL)</td>
  <% str30Date = dateadd("d", -31, date())
    strTodayDate = date()
    str07Date = dateadd("d", -7, date())

   
  
  strsql = "SELECT tblCars.Species, Count(tblCars.CID) AS CCID FROM tblCars "_
&" WHERE  Inv_depletion_date >'" & str30Date & "' and  Inv_depletion_date < '" & strTodayDate & "' and (Shred_OCC = 0 or Shred_OCC = null) "_
&" GROUP BY tblCars.Species ORDER BY tblCars.Species"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
While not MyRec.eof  
if MyRec("Species") = "KBLD" then
str30KBLDD = MyRec("CCID")
elseif MyRec("Species") = "OF3" then
str30OF = MyRec("CCID")
elseif MyRec("Species") = "PMX" then
str30PMX = MyRec("CCID")
elseif Trim(MyRec("Species")) = "HBX" then
str30HBX = MyRec("CCID")
elseif MyRec("Species") = "SHRED" then
str30SHRED = MyRec("CCID")

end if
MyRec.movenext
wend
MyRec.close


  strsql = "SELECT tblCars.Species, Count(tblCars.CID) AS CCID FROM tblCars "_
&" WHERE  Inv_depletion_date >'" & str30Date & "' and  Inv_depletion_date < '" & strTodayDate & "' and carrier = 'RAIL' and (SHRED_OCC = 0 or SHRED_OCC is Null)"_
&" GROUP BY tblCars.Species ORDER BY tblCars.Species"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
While not MyRec.eof  
if MyRec("Species") = "KBLD" then
strR30KBLD = MyRec("CCID")
elseif MyRec("Species") = "OF3" then
strR30OF = MyRec("CCID")
elseif MyRec("Species") = "PMX" then
strR30PMX = MyRec("CCID")
elseif MyRec("Species") = "SHRED" then
strR30SHRED = MyRec("CCID")

end if
MyRec.movenext
wend
MyRec.close

  strsql = "SELECT tblCars.Species, Count(tblCars.CID) AS CCID FROM tblCars "_
&" WHERE  Inv_depletion_date >'" & str07Date & "' and  Inv_depletion_date < '" & strTodayDate & "' and (SHRED_OCC = 0 or SHRED_OCC is Null) "_
&" GROUP BY tblCars.Species ORDER BY tblCars.Species"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
While not MyRec.eof  
if MyRec("Species") = "KBLD" then
str07KBLD = MyRec("CCID")
elseif MyRec("Species") = "OF3" then
str07OF = MyRec("CCID")
elseif MyRec("Species") = "PMX" then
str07PMX = MyRec("CCID")
elseif Trim(MyRec("Species")) = "HBX" then
str07HBX = MyRec("CCID")
elseif MyRec("Species") = "SHRED" then
str07SHRED = MyRec("CCID")

end if
MyRec.movenext
wend
MyRec.close


  strsql = "SELECT tblCars.Species, Count(tblCars.CID) AS CCID FROM tblCars "_
&" WHERE  Inv_depletion_date >'" & str07Date & "' and  Inv_depletion_date < '" & strTodayDate & "' and carrier = 'RAIL' and  (SHRED_OCC = 0 or SHRED_OCC is Null) "_
&" GROUP BY tblCars.Species ORDER BY tblCars.Species"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
While not MyRec.eof  
if MyRec("Species") = "KBLD" then
strR07KBLD = MyRec("CCID")
elseif MyRec("Species") = "OF3" then
strR07OF = MyRec("CCID")
elseif MyRec("Species") = "PMX" then
strR07PMX = MyRec("CCID")
elseif Trim(MyRec("Species")) = "HBX" then
strR07HBX = MyRec("CCID")
elseif MyRec("Species") = "SHRED" then
strR07SHRED = MyRec("CCID")

end if
MyRec.movenext
wend
MyRec.close
 


%>

<td class="style77"> <%= round(( str30KBLD + (strR30KBLD * 3))/30,1) %> &nbsp;   </td>
 
<% strKBLDRail = strR30KBLD
strKBLDTrailer = str30KBLD   %>

<td class="style77"> <%= round(((strR30OF * 3))/30,1) %> &nbsp;   </td>
<% strOFRail = strR30OF  
strOFTrailer = str30OF   %>

<td class="style77">
<% if ((strR30PMX * 3) + str30PMx) > 0 then %>
<%= round(((strR30PMX * 3) + str30PMx)/30,1) %>
<% end if %> &nbsp; </td>


<td class="style77"> <%= round((str30SHRED + (strR30SHRED * 3))/30,1) %> &nbsp;   </td>


<% strSHREDRail =   strR30SHRED  
strSHREDTrailer =   str30SHRED    %>


<td class="style77"> 
<% if ((strR30HBX * 3) + str30HBX) > 0 then %>
<%= round(((strR30HBX * 3) + str30HBX)/30,1) %>
<% end if %> &nbsp; </td>



</tr>
<tr><td class="style41"></td>
  <td   class="style77">Last 7 Day Avg (In TL)</td>
<td class="style77"> <%= round((str07KBLD +  (strR07KBLD * 3))/7,1) %> &nbsp;   </td>
 
<% strKBLDRail = strR07KBLD   
strKBLDTrailer = str07KBLD     %>

<td class="style77"> <%= round((str07OF + (strR07OF * 3))/7,1) %> &nbsp;   </td>
<% strOFRail = strR07OF  
strOFTrailer = str07OF   %>
 
 
<td class="style77">
<% if ((strR07PMX * 3) + str07PMx) > 0 then %>
<%= round(((strR07PMX * 3) + str07PMx)/07,1) %>
<% end if %> &nbsp; </td>


<td class="style77"> <%= round((str07SHRED + (strR07SHRED * 3))/7,1) %> &nbsp; </td>

<% strSHREDRail = strR07ShredTotal -  strR07SHRED - strR07SHREDT5   
strSHREDTrailer = str07ShredTotal - str07SHRED - str07SHREDT5   %>


<td class="style77">
<% if ((strR07HBX * 3) + str07HBX) > 0 then %>
<%= round(((strR07HBX * 3) + str07HBX)/07,1) %>
<% end if %> &nbsp; </td>



 


</tr>

<%   	strnow = formatdatetime(Now(),2)
	strBegdate7 = dateadd("d", -7, strnow)
	strBegDate6 = dateadd("d", -6, strnow)
	strBegDate5 = dateadd("d", -5, strnow)
	strBegDate4 = dateadd("d", -4, strnow)
	strBegDate3 = dateadd("d", -3, strnow)
	strBegDate2 = dateadd("d", -2, strnow)
	strBegDate1 = dateadd("d", -1, strnow)

	
	strEnddate = formatdatetime(now())
	
	Function getweekday(strD)

 	if weekday(strD)  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif  weekday(strD)   = 2 then
 	 strDayofweek =  "Monday" 
 	elseif   weekday(strD)   = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif  weekday(strD)   = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif   weekday(strD)  = 5 then
 	 strDayofweek =  "Thursday"
 	elseif   weekday(strD)   = 6 then
 	 strDayofweek =  "Friday"
 	elseif  weekday(strD)  = 7 then
 	 strDayofweek =  "Saturday"
 	end if
 	
 	end Function
 	
 	call  getweekday(strBegdate7)
  
 	 call getrow( strBegDate7, strBegDate6)
 	 	call  getweekday(strBegdate6)
 	 call getrow(strBegDate6, strBegDate5)
 	 	call  getweekday(strBegdate5)
 	 call getrow(strBegDate5, strBegDate4)
 	 	call  getweekday(strBegdate4)
 	 call getrow(strBegDate4, strBegDate3)
 	 	call  getweekday(strBegdate3)
 	 call getrow(strBegDate3, strBegDate2)
 	 	call  getweekday(strBegdate2)
 	 call getrow(strBegDate2, strBegDate1)
 	 	call  getweekday(strBegdate1)
 	 call getrow(strBegDate1, strnow)
 	 	call  getweekday(strnow)
 	 call getrow(strnow, strTomorrow)
 	 
 	Function  getRow( strDA, strDA1)
 	
%>
<tr><td class="style41"><font face="arial"><%= strDA %></td>
  <% If datepart("d", strDA) = datepart("d", strnow) then %>
  <td   class="style77"><font face="arial">Consumed since midnight TODAY </td>
  <% else %>
<td class="style77"><font face="arial"><%= strDayofWeek %>  </td>
<% end if %>
 

<td class="style77">
<% strCountKBLD = 0
strCountKBLDR = 0

strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'KBLD'  and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and (carrier <> 'RAIL' or carrier is null)"
 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
strCountKBLD = MyREc("CountofCID")
end if

 strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'KBLD'  and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and carrier ='RAIL'"

   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then 
strCountKBLDR =  MyREc("CountofCID")  
end if

%>
<% if strCOuntKBLD = 0 then %>
&nbsp;
<% else %>
<%= strCountKBLD %>  
<% end if %>
<% if strCountKBLDR > 0 then %>
/  <%= strCountKBLDR %>
<% end if %></td>

<td class="style77">
<% strCountOF3 = 0
strCountOF3R = 0

strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'OF3'  and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and (carrier <> 'RAIL' or carrier is null)"
 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
strCountOF3 = MyREc("CountofCID")
end if

 strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'OF3'  and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and carrier ='RAIL'"

   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then 
strCountOF3R =  MyREc("CountofCID")  
end if

%>
<% if strCOuntOF3 = 0 then %>
&nbsp;
<% else %>
<%= strCountOF3 %>  
<% end if %>
<% if strCountOF3R > 0 then %>
/  <%= strCountOF3R %>
<% end if %></td>

 
 



<td class="style77">
<% strCountPMX = 0
strCountPMXR = 0

strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'PMX'  and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and (carrier <> 'RAIL' or carrier is null)"
 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
strCountPMX = MyREc("CountofCID")
end if

 strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'PMX'  and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and carrier ='RAIL'"

   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then 
strCountPMXR =  MyREc("CountofCID")  
end if

%>
<% if strCOuntPMX = 0 then %>
&nbsp;
<% else %>
<%= strCountPMX %>  
<% end if %>
<% if strCountPMXR > 0 then %>
/  <%= strCountPMXR %>
<% end if %></td>


<td class="style77">
<% strCountSHRED = 0
strCountSHREDR = 0

strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'SHRED' and (SHRED_OCC = 0 or SHRED_OCC is Null) and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and (carrier <> 'RAIL' or carrier is null)"
 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
strCountSHRED = MyREc("CountofCID")
end if

 strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'SHRED' and (SHRED_OCC = 0 or SHRED_OCC is Null) and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and carrier ='RAIL'"

   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then 
strCountSHREDR =  MyREc("CountofCID")  
end if

%>
<% if strCOuntSHRED = 0 then %>
&nbsp;
<% else %>
<%= strCountSHRED %>  
<% end if %>
<% if strCountSHREDR > 0 then %>
/  <%= strCountSHREDR %>
<% end if %></td>

<td class="style77">
<% strCountHBX = 0
strCountHBXR = 0

strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'HBX'  and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and (carrier <> 'RAIL' or carrier is null)"
 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
strCountHBX = MyREc("CountofCID")
end if

 strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'HBX'  and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and carrier ='RAIL'"

   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then 
strCountHBXR =  MyREc("CountofCID")  
end if

%>
<% if strCOuntHBX = 0 then %>
&nbsp;
<% else %>
<%= strCountHBX %>  
<% end if %>
<% if strCountHBXR > 0 then %>
/  <%= strCountHBXR %>
<% end if %></td>


</tr>
<% end function %>
</table><p class="style1"><strong>

 <%   Dim strKBLD1, strPMX1, strOCC1, strKBLD2, strPMX2, strOCC2, strKBLD3, strPMX3, strOCC3, strKBLD4, strPMX4, strOCC4, strsql9
  Dim strKBLD5, strPMX5, strOCC5, strKBLD6, strPMX6, strOCC6, strKBLD7, strPMX7, strOCC7, strTday, strTD, strTM, strYear
  Dim strTD1, strTM1, strTday1
  strTday = formatdatetime(now(),2)
  strYear = datepart("yyyy", strTday)
  strTD = datepart("d", strTday)
  strTM = datepart("m", strTDay)
  strTday1 = dateadd("d", 1, strTday)
    strTD1 = datepart("d", strTday1)
  strTM1 = datepart("m", strTDay1)
  strTday2 = dateadd("d", 2, strTday)
    strTD2 = datepart("d", strTday2)
  strTM2 = datepart("m", strTDay2)
  strTday3 = dateadd("d", 3, strTday)
    strTD3 = datepart("d", strTday3)
  strTM3 = datepart("m", strTDay3)
  
    strTday4 = dateadd("d", 4, strTday)
    strTD4 = datepart("d", strTday4)
  strTM4 = datepart("m", strTDay4)


  strTday5 = dateadd("d", 5, strTday)
    strTD5 = datepart("d", strTday5)
  strTM5 = datepart("m", strTDay5)


  strTday6 = dateadd("d", 6, strTday)
    strTD6 = datepart("d", strTday6)
  strTM6 = datepart("m", strTDay6)
  
  
  strConsumptionDate = strTday6
  call  Consumption()
  
   strConsumptionDate = dateadd("d", 7, strTday )
    call  Consumption()
    
      strConsumptionDate = dateadd("d", 8, strTday )
	  call  Consumption()
	 
	 strConsumptionDate =  dateadd("d", 9, strTday )
  call  Consumption()

  
  Function Consumption()
  
      strsql = "Select Total_OCC from tblTempYardTotals where INV_Date = '" & strConsumptionDate & "'"
     Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
if not MyRec.eof then
' do nothing
else

  	
  		strsql8 = "Select tblTempYardTotals.* from tblTempYardTotals where ID = 16 "
  	    Set MyRec8 = Server.CreateObject("ADODB.Recordset")
   MyRec8.Open strSQL8, Session("ConnectionString") 
   RFDefault = MyRec8.fields("Total_RF")
   OCCDefault = MyRec8.fields("Total_OCC")
     MXPDefault = MyRec8.fields("Total_MXP")
   SHREDDefault = MyRec8("SHRED_T1")
 
 	HBXDefault = MyRec8.fields("HBX")
	PMXDefault = MyRec8.fields("PMX")
	
	KBLDDefault = Myrec8("KBLD")
	OF3Default = MyRec8("OF3")
	SWLDefault = MyRec8("SWL")
	USBSDefault = MyRec8("USBS")
	LPSBSDefault = MYrec8("LPSBS")
	HWMDefault = MyRec8("HWM")
	ShredOCCDefault = MyRec8("Shred_OCC")
 
If len(SWLDefault) > 0 then
  	'do nothing
  	else
  	SWLDefault = 0
  	end if
 
 	If len(USBSDefault) > 0 then
  	'do nothing
  	else
  	USBSDefault = 0
  	end if
  	
  	If len(LPSBSDefault) > 0 then
  	' do nothing
  	else
  	LPSBSDefault = 0
  	end if
 
   		If len(HWMDefault) > 0 then
  	' do nothing
  	else
  	HWMDefault = 0
  	end if
 
  
   		If len(ShredOCCDefault) > 0 then
  ' do nothing
  	else
  	ShredOCCDefault = 0
  	end if
 

   
   MyRec8.close
 

  	
  	
	strsql9 =  "INSERT INTO tblTempYardTotals ( INV_Date, total_RF,   total_OCC, total_MXP, KBLD,   OF3, PMX, Shred_t1,  HBX, SWL, USBS, LPSBS, HWM, Shred_OCC) "_
	&" SELECT '" & StrConsumptionDate & "', " & RFDefault & ", " & OCCDefault & ", " & MXPDefault & ", " & KBLDDefault & ",   "_
	&" " & OF3Default & ", " & PMXDefault & ", " & ShredDefault & ",  " & HBXDefault & ", " & SWLDefault & ", " & USBSDefault & ", " & LPSBSDefault & ", "_
	&" " & HWMDefault & ", " & ShredOCCDefault & ""
	
	'Response.write ("strsql9 " & strsql9)
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL9
			MyConn.Close

  
end if
MyRec.close

End Function
  
 
strOCC3 = 0
strKBLD3 = 0
strOCC4 = 0
strKBLD4 = 0
strOCC5 = 0
strKBLD5 = 0
strOCC6 = 0
strKBLD6 = 0
strOCC7 = 0
strKBLD7 = 0

strOF3 = 0
strOF4 = 0
strOF5 = 0
strOF6 = 0
strOF7 = 0

strHBX3 = 0
strHBX4 = 0
strHBX5 = 0
strHBX6 = 0
strHBX7 = 0

strSHRED3 = 0
strSHRED4 = 0
strSHRED5 = 0
strSHRED6 = 0
strSHRED7 = 0

strMXP3 = 0
strMXP4 = 0
strMXP5 = 0
strMXP6 = 0
strMXP7 = 0
strPMX3 = 0
strPMX4 = 0
strPMX5 = 0
strPMX6 = 0
strPMX7 = 0

	
   
        strsql = "SELECT  count(CID) as Countoford_id, left(release,1) as Species,  dateadd(dd, datediff(dd,0,Date_to),0) as Delivery_date "_
  			&" FROM tblInbound "_
			&" WHERE Destination_City ='MOBILE' and Release <> '0' and (OCC_Shred = 'N'  or OCC_Shred is null or OCC_Shred = '') "_								
			&" GROUP BY dateadd(dd, datediff(dd,0,Date_to),0), left(Release,1) "_			
			&" HAVING '" & strdate & "' <= dateadd(dd, datediff(dd,0,Date_to),0) "_
			&" ORDER BY dateadd(dd, datediff(dd,0,Date_to),0), left(Release,1)"



   		Set MyRec = Server.CreateObject("ADODB.Recordset")
   		 MyRec.Open strSQL, Session("ConnectionString") 


While not MyRec.eof




If strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2 and (MyRec.fields("Species") = "P" or MyRec.fields("Species") = "p" or MyRec.fields("Species") = "s" or  MyRec.fields("Species") = "S") then   
strPMX3 = MyRec.fields("countoford_id") + strPMX3
elseif strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "C"  or MyRec.fields("Species") = "c" or MyRec.fields("Species") = "r" or MyRec.fields("Species") = "R"  ) then 
strOCC3 = strOCC3 + MyRec.fields("countoford_id")
elseif strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "M"  or MyRec.fields("Species") = "m" ) then 
strMXP3 = strMXP3 + MyRec.fields("countoford_id")
elseif strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "D"  or MyRec.fields("Species") = "d" ) then 
strSHRED3 = strSHRED3 + MyRec.fields("countoford_id")
elseif strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "H"  or MyRec.fields("Species") = "h" ) then 
strHBX3 = strHBX3 + MyRec.fields("countoford_id")
elseif strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "F"  or MyRec.fields("Species") = "f" ) then 
strOF3 = strOF3 + MyRec.fields("countoford_id")
elseif strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "K"  or MyRec.fields("Species") = "k" ) then 
strKBLD3 = strKBLD3 + MyRec.fields("countoford_id")




elseIf strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3 and  (MyRec.fields("Species") = "P" or MyRec.fields("Species") = "p" or MyRec.fields("Species") = "s" or  MyRec.fields("Species") = "S") then 
strPMX4 = MyRec.fields("countoford_id") + strPMX4
elseif strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3  and (MyRec.fields("Species") = "C"  or MyRec.fields("Species") = "c" or myRec.fields("Species") = "R" or myRec.fields("Species")="r" ) then 
strOCC4 = strOCC4 + MyRec.fields("countoford_id")
elseif strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3  and (MyRec.fields("Species") = "M"  or MyRec.fields("Species") = "m" ) then 
strMXP4 = strMXP4 + MyRec.fields("countoford_id")
elseif strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3  and  (MyRec.fields("Species") = "D"  or MyRec.fields("Species") = "d" ) then 
strSHRED4 = strSHRED4 + MyRec.fields("countoford_id")
elseif strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3  and  (MyRec.fields("Species") = "H"  or MyRec.fields("Species") = "h" ) then 
strHBX4 = strHBX4 + MyRec.fields("countoford_id")
elseif strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "F"  or MyRec.fields("Species") = "f" ) then 
strOF4 = strOF4 + MyRec.fields("countoford_id")
elseif strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "K"  or MyRec.fields("Species") = "k" ) then 
strKBLD4 = strKBLD4 + MyRec.fields("countoford_id")




elseIf strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4 and  (MyRec.fields("Species") = "P" or MyRec.fields("Species") = "p" or MyRec.fields("Species") = "s" or  MyRec.fields("Species") = "S") then       
strPMX5 = MyRec.fields("countoford_id") + strPMX5
elseif strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4  and  (MyRec.fields("Species") = "C"  or MyRec.fields("Species") = "c" or myRec.fields("Species") = "R" or myRec.fields("Species")="r") then 
 strOCC5 = strOCC5 +  MyRec.fields("countoford_id")
elseif strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4  and  (MyRec.fields("Species") = "M"  or MyRec.fields("Species") = "m" ) then 
 strMXP5 = strMXP5 + MyRec.fields("countoford_id")
 elseif strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5 and  (MyRec.fields("Species") = "D"  or MyRec.fields("Species") = "d" ) then 
strSHRED5 = strSHRED5 + MyRec.fields("countoford_id")
elseif strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5  and  (MyRec.fields("Species") = "H"  or MyRec.fields("Species") = "h" ) then 
strHBX5 = strHBX5 + MyRec.fields("countoford_id")
elseif strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "F"  or MyRec.fields("Species") = "f" ) then 
strOF5 = strOF5 + MyRec.fields("countoford_id")
elseif strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "K"  or MyRec.fields("Species") = "k" ) then 
strKBLD5 = strKBLD5 + MyRec.fields("countoford_id")



elseIf strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5 and  (MyRec.fields("Species") = "P" or MyRec.fields("Species") = "p" or MyRec.fields("Species") = "s" or  MyRec.fields("Species") = "S") then        
strPMX6 = MyRec.fields("countoford_id") + strPMX6
elseif strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5  and  (MyRec.fields("Species") = "C" or MyRec.fields("Species") = "c"  or myRec.fields("Species") = "R" or myRec.fields("Species")="r") then 
strOCC6 = strOCC6 +  MyRec.fields("countoford_id")
elseif strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5  and  (MyRec.fields("Species") = "M" or MyRec.fields("Species") = "m" ) then 
strMXP6 = strMXP6 + MyRec.fields("countoford_id")
 elseif strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6 and  (MyRec.fields("Species") = "D"  or MyRec.fields("Species") = "d" ) then 
strSHRED6 = strSHRED6 + MyRec.fields("countoford_id")
elseif strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6  and  (MyRec.fields("Species") = "H"  or MyRec.fields("Species") = "h" ) then 
strHBX6 = strHBX6 + MyRec.fields("countoford_id")
elseif strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "F"  or MyRec.fields("Species") = "f" ) then 
strOF6 = strOF6 + MyRec.fields("countoford_id")
elseif strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "K"  or MyRec.fields("Species") = "k" ) then 
strKBLD6 = strKBLD6 + MyRec.fields("countoford_id")



elseIf strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6 and  (MyRec.fields("Species") = "P" or MyRec.fields("Species") = "p" or MyRec.fields("Species") = "s" or  MyRec.fields("Species") = "S") then          
strPMX7 = MyRec.fields("countoford_id") + strPMX7

elseif strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6  and  (MyRec.fields("Species") = "C" or MyRec.fields("Species") = "c"  or myRec.fields("Species") = "R" or myRec.fields("Species")="r") then  
strOCC7 = strOCC7 +  MyRec.fields("countoford_id")
elseif strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6  and  (MyRec.fields("Species") = "M" or MyRec.fields("Species") = "m" ) then  
strMXP7 = strMXP7 + MyRec.fields("countoford_id")
 elseif strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM7 and  (MyRec.fields("Species") = "D"  or MyRec.fields("Species") = "d" ) then 
strSHRED7 = strSHRED7 + MyRec.fields("countoford_id")
elseif strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM7  and  (MyRec.fields("Species") = "H"  or MyRec.fields("Species") = "h" ) then 
strHBX7 = strHBX7 + MyRec.fields("countoford_id")
elseif strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "F"  or MyRec.fields("Species") = "f" ) then 
strOF7 = strOF7 + MyRec.fields("countoford_id")
elseif strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "K"  or MyRec.fields("Species") = "k" ) then 
strKBLD7 = strKBLD7 + MyRec.fields("countoford_id")



end if
   MyRec.MoveNext
     Wend
     MyRec.close
  
   RstrPMX0 = 0
 RstrOCC0 = 0
 RstrMXP0 = 0
 RstrKBLD0 = 0
 RstrOF0 = 0
 RstrSHRED0 = 0
 RstrHBX0 = 0
 
 RstrPMX1 = 0
 RstrOCC1 = 0
 RstrMXP1 = 0
 RstrKBLD1 = 0
 RstrOF1 = 0
 RstrSHRED1 = 0
 RstrHBX1 = 0   
     
 RstrPMX2 = 0
 RstrOCC2 = 0
 RstrMXP2 = 0
 RstrKBLD2 = 0
 RstrOF2 = 0
 RstrSHRED2 = 0
 RstrHBX2 = 0
           
     
 RstrPMX3 = 0
 RstrOCC3 = 0
 RstrMXP3 = 0
 RstrKBLD3 = 0
 RstrOF3 = 0
 RstrSHRED3 = 0
 RstrHBX3 = 0
 
  RstrPMX4 = 0
 RstrOCC4 = 0
 RstrMXP4 = 0
 RstrKBLD4 = 0
 RstrOF4 = 0
 RstrSHRED4 = 0
 RstrHBX4 = 0
 
 RstrPMX5 = 0
 RstrOCC5 = 0
 RstrMXP5 = 0
 RstrKBLD5 = 0
 RstrOF5 = 0
 RstrSHRED5 = 0
 RstrHBX5 = 0

 
 RstrPMX6 = 0
 RstrOCC6 = 0
 RstrMXP6 = 0
 RstrKBLD6 = 0
 RstrOF6 = 0
 RstrSHRED6 = 0
 RstrHBX6 = 0
 
 RstrPMX7 = 0
 RstrOCC7 = 0
 RstrMXP7 = 0
 RstrKBLD7 = 0
 RstrOF7 = 0
 RstrSHRED7 = 0
 RstrHBX7 = 0
 
 strTM0 = datepart("m", now())
RKT1D1 = 0
RKT1D2 = 0
RKT1D3 = 0
RKT1D4 = 0
RKT1D5 = 0
RKT1D6 = 0
RKT1D7 = 0

strETADate = date()


strsql = "SELECT tblVirginFiber.ETA, tblOrder.Species, Count(tblVirginFiber.VID) AS CountOfVID FROM tblVirginFiber INNER JOIN tblOrder ON tblVirginFiber.Release = tblOrder.Release "_
&" WHERE tblVirginFiber.Status='Inbound' GROUP BY tblVirginFiber.ETA, tblOrder.Species HAVING tblVirginFiber.ETA >='" & strETADate & "' "

   		Set MyRec = Server.CreateObject("ADODB.Recordset")
   		 MyRec.Open strSQL, Session("ConnectionString") 

While not MyRec.eof
if   MyRec("Species") = "KBLD" and MyRec("ETA") =  strETADate then
RKT1D1 = MyRec("CountofVID")
elseif   MyRec("Species") = "KBLD" and MyRec("ETA") = dateadd("d", 1, strETADate) then
RKT1D2 =  MyRec("CountofVID")
elseif   MyRec("Species") = "KBLD" and MyRec("ETA") = dateadd("d", 2, strETADate) then
RKT1D3 =  MyRec("CountofVID")
elseif   MyRec("Species") = "KBLD" and MyRec("ETA") = dateadd("d", 3, strETADate) then
RKT1D4 =  MyRec("CountofVID")
elseif   MyRec("Species") = "KBLD" and MyRec("ETA") = dateadd("d", 4, strETADate) then
RKT1D5 =  MyRec("CountofVID")
elseif   MyRec("Species") = "KBLD" and MyRec("ETA") = dateadd("d", 5, strETADate) then
RKT1D6 =  MyRec("CountofVID")
elseif   MyRec("Species") = "KBLD" and MyRec("ETA") = dateadd("d", 6, strETADate) then
RKT1D7 =  MyRec("CountofVID")

elseif   MyRec("Species") = "OF3" and MyRec("ETA") =  strETADate then
ROT1D1 = MyRec("CountofVID")
elseif   MyRec("Species") = "OF3" and MyRec("ETA") = dateadd("d", 1, strETADate) then
ROT1D2 =  MyRec("CountofVID")
elseif   MyRec("Species") = "OF3" and MyRec("ETA") = dateadd("d", 2, strETADate) then
ROT1D3 =  MyRec("CountofVID")
elseif   MyRec("Species") = "OF3" and MyRec("ETA") = dateadd("d", 3, strETADate) then
ROT1D4 =  MyRec("CountofVID")
elseif  MyRec("Species") = "OF3" and MyRec("ETA") = dateadd("d", 4, strETADate) then
ROT1D5 =  MyRec("CountofVID")
elseif   MyRec("Species") = "OF3" and MyRec("ETA") = dateadd("d", 5, strETADate) then
ROT1D6 =  MyRec("CountofVID")
elseif   MyRec("Species") = "O3F" and MyRec("ETA") = dateadd("d", 6, strETADate) then
ROT1D7 =  MyRec("CountofVID")


end if
MyRec.movenext
wend
MyRec.close



   
        strsql = "SELECT  count(VID) as Countoford_id,  Species,  dateadd(dd, datediff(dd,0,ETA),0) as Delivery_date "_
  			&" FROM tblVirginFiber "_
			&" WHERE Status = 'Inbound' and Vendor = 'Corporate' "_								
			&"GROUP BY dateadd(dd, datediff(dd,0,ETA),0), Species "_			
			&" HAVING '" & strdate & "' <= dateadd(dd, datediff(dd,0,ETA),0) "_
			&" ORDER BY dateadd(dd, datediff(dd,0,ETA),0), Species"



   		Set MyRec = Server.CreateObject("ADODB.Recordset")
   		 MyRec.Open strSQL, Session("ConnectionString") 


While not MyRec.eof


If strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM0 and MyRec.fields("Species") = "PMX"  then   
RstrPMX0 = MyRec.fields("countoford_id") + RstrPMX0
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM0  and  (MyRec.fields("Species") = "OCC"  or MyRec("Species") = "USBS") then 
RstrOCC0 = RstrOCC0 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM0  and  (MyRec.fields("Species") = "MXP" ) then 
RstrMXP0 = RstrMXP0 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM0  and  (MyRec.fields("Species") = "KBLD" ) then 
RstrKBLD0 = RstrKBLD0 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM0  and  (MyRec.fields("Species") = "OF3" ) then 
RstrOF0 = RstrOF0 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM0  and  (MyRec.fields("Species") = "SHRED") then 
RstrSHRED0 = RstrSHRED0 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM0  and  (MyRec.fields("Species") = "HBX" ) then 
RstrHBX0 = RstrHBX0 + MyRec.fields("countoford_id")

elseIf strTD1 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM1 and  MyRec.fields("Species") = "PMX" then   
RstrPMX1 = MyRec.fields("countoford_id") + RstrPMX1
elseif strTD1 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM1  and  (MyRec.fields("Species") = "OCC"  or MyRec("Species") = "USBS") then 
RstrOCC1 = RstrOCC1 + MyRec.fields("countoford_id")
elseif strTD1 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM1  and  (MyRec.fields("Species") = "MXP" ) then 
RstrMXP1 = RstrMXP1 + MyRec.fields("countoford_id")
elseif strTD1 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM1  and  (MyRec.fields("Species") = "KBLD" ) then 
RstrKBLD1 = RstrKBLD1 + MyRec.fields("countoford_id")
elseif strTD1 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM1  and  (MyRec.fields("Species") = "OF3" ) then 
RstrOF1 = RstrOF1 + MyRec.fields("countoford_id")
elseif strTD1 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM1  and  (MyRec.fields("Species") = "SHRED") then 
RstrSHRED1 = RstrSHRED1 + MyRec.fields("countoford_id")
elseif strTD1 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM1  and  (MyRec.fields("Species") = "HBX" ) then 
RstrHBX1 = RstrHBX1 + MyRec.fields("countoford_id")

elseIf strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2 and MyRec.fields("Species") = "PMX" then   
RstrPMX2 = MyRec.fields("countoford_id") + RstrPMX2
elseif strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "OCC"  or MyRec("Species") = "USBS") then 
RstrOCC2 = RstrOCC2 + MyRec.fields("countoford_id")
elseif strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "MXP" ) then 
RstrMXP2 = RstrMXP2 + MyRec.fields("countoford_id")
elseif strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "KBLD" ) then 
RstrKBLD2 = RstrKBLD2 + MyRec.fields("countoford_id")
elseif strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "OF3" ) then 
RstrOF2 = RstrOF2 + MyRec.fields("countoford_id")
elseif strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "SHRED") then 
RstrSHRED2 = RstrSHRED2 + MyRec.fields("countoford_id")
elseif strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "HBX" ) then 
RstrHBX2 = RstrHBX2 + MyRec.fields("countoford_id")

elseIf strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3 and MyRec.fields("Species") = "PMX"  then   
RstrPMX3 = MyRec.fields("countoford_id") + RstrPMX3
elseif strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3  and  (MyRec.fields("Species") = "OCC"  or MyRec("Species") = "USBS") then 
RstrOCC3 = RstrOCC3 + MyRec.fields("countoford_id")
elseif strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3  and  (MyRec.fields("Species") = "MXP" ) then 
RstrMXP3 = RstrMXP3 + MyRec.fields("countoford_id")
elseif strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3  and  (MyRec.fields("Species") = "KBLD" ) then 
RstrKBLD3 = RstrKBLD3 + MyRec.fields("countoford_id")
elseif strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3  and  (MyRec.fields("Species") = "OF3" ) then 
RstrOF3 = RstrOF3 + MyRec.fields("countoford_id")
elseif strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3  and  (MyRec.fields("Species") = "SHRED") then 
RstrSHRED3 = RstrSHRED3 + MyRec.fields("countoford_id")
elseif strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3  and  (MyRec.fields("Species") = "HBX" ) then 
RstrHBX3 = RstrHBX3 + MyRec.fields("countoford_id")

elseIf strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4 and MyRec.fields("Species") = "PMX"  then   
RstrPMX4 = MyRec.fields("countoford_id") + RstrPMX4
elseif strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4  and  (MyRec.fields("Species") = "OCC"  or MyRec("Species") = "USBS") then 
RstrOCC4 = RstrOCC4 + MyRec.fields("countoford_id")
elseif strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4  and  (MyRec.fields("Species") = "MXP" ) then 
RstrMXP4 = RstrMXP4 + MyRec.fields("countoford_id")
elseif strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4  and  (MyRec.fields("Species") = "KBLD" ) then 
RstrKBLD4 = RstrKBLD4 + MyRec.fields("countoford_id")
elseif strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4  and  (MyRec.fields("Species") = "OF3" ) then 
RstrOF4 = RstrOF4 + MyRec.fields("countoford_id")
elseif strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4  and  (MyRec.fields("Species") = "SHRED") then 
RstrSHRED4 = RstrSHRED4 + MyRec.fields("countoford_id")
elseif strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4  and  (MyRec.fields("Species") = "HBX" ) then 
RstrHBX4 = RstrHBX4 + MyRec.fields("countoford_id")


elseIf strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5 and  MyRec.fields("Species") = "PMX"  then   
RstrPMX5 = MyRec.fields("countoford_id") + RstrPMX5
elseif strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5  and  (MyRec.fields("Species") = "OCC"  or MyRec("Species") = "USBS") then 
RstrOCC5 = RstrOCC5 + MyRec.fields("countoford_id")
elseif strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5  and  (MyRec.fields("Species") = "MXP" ) then 
RstrMXP5 = RstrMXP5 + MyRec.fields("countoford_id")
elseif strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5  and  (MyRec.fields("Species") = "KBLD" ) then 
RstrKBLD5 = RstrKBLD5 + MyRec.fields("countoford_id")
elseif strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5  and  (MyRec.fields("Species") = "OF3" ) then 
RstrOF5 = RstrOF5 + MyRec.fields("countoford_id")
elseif strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5  and  (MyRec.fields("Species") = "SHRED") then 
RstrSHRED5 = RstrSHRED5 + MyRec.fields("countoford_id")
elseif strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5  and  (MyRec.fields("Species") = "HBX" ) then 
RstrHBX5 = RstrHBX5 + MyRec.fields("countoford_id")

elseIf strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6 and MyRec.fields("Species") = "PMX"  then   
RstrPMX6 = MyRec.fields("countoford_id") + RstrPMX6
elseif strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6  and  (MyRec.fields("Species") = "OCC"  or MyRec("Species") = "USBS") then 
RstrOCC6 = RstrOCC6 + MyRec.fields("countoford_id")
elseif strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6  and  (MyRec.fields("Species") = "MXP" ) then 
RstrMXP6 = RstrMXP6 + MyRec.fields("countoford_id")
elseif strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6  and  (MyRec.fields("Species") = "KBLD" ) then 
RstrKBLD6 = RstrKBLD6 + MyRec.fields("countoford_id")
elseif strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6  and  (MyRec.fields("Species") = "OF3" ) then 
RstrOF6 = RstrOF6 + MyRec.fields("countoford_id")
elseif strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6  and  (MyRec.fields("Species") = "SHRED") then 
RstrSHRED6 = RstrSHRED6 + MyRec.fields("countoford_id")
elseif strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6  and  (MyRec.fields("Species") = "HBX" ) then 
RstrHBX6 = RstrHBX6 + MyRec.fields("countoford_id")
 
 elseIf strTD7 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM7 and  MyRec.fields("Species") = "PMX" then   
RstrPMX7 = MyRec.fields("countoford_id") + RstrPMX7
elseif strTD7 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM7  and  (MyRec.fields("Species") = "OCC"  or MyRec("Species") = "USBS") then 
RstrOCC7 = RstrOCC7 + MyRec.fields("countoford_id")
elseif strTD7 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM7  and  (MyRec.fields("Species") = "MXP" ) then 
RstrMXP7 = RstrMXP7 + MyRec.fields("countoford_id")
elseif strTD7 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM7  and  (MyRec.fields("Species") = "KBLD" ) then 
RstrKBLD7 = RstrKBLD7 + MyRec.fields("countoford_id")
elseif strTD7 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM7  and  (MyRec.fields("Species") = "OF3" ) then 
RstrOF7 = RstrOF7 + MyRec.fields("countoford_id")
elseif strTD7 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM7  and  (MyRec.fields("Species") = "SHRED") then 
RstrSHRED7 = RstrSHRED7 + MyRec.fields("countoford_id")
elseif strTD7 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM7  and  (MyRec.fields("Species") = "HBX" ) then 
RstrHBX7 = RstrHBX7 + MyRec.fields("countoford_id")

end if
   MyRec.MoveNext
     Wend
     MyRec.close


 Dim gKBLD, gOCC, gPMX, strRailCars
gKBLD = 0
gOCC = 0
gPMX = 0
gOF = 0
gMXP = 0
gHBX = 0
gSHRED = 0
strsql = "SELECT tblCars.*,  [date_received] AS Sort_Date FROM tblCars WHERE Location = 'YARD' and Rejected is null "_
&" and Net > 0 and (SHRED_OCC = 0 or SHRED_OCC is Null) ORDER BY tblCars.Species, Sort_Date "   
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    while not MyRec.eof
    If MyRec.fields("Species") = "KBLD"  then
    gKBLD = gKBLD + 1
    elseif MyRec.fields("Species") = "PMX"  then
    gPMX = gPMX + 1
    elseif MyRec.fields("Species") = "OCC" Then
    gOCC = gOCC + 1
    
    elseif MyRec.fields("Species") = "MXP" then
    gMXP = gMXP + 1
        elseif MyRec.fields("Species") = "OF3"  Then
    gOF = gOF + 1
           elseif MyRec.fields("Species") = "SHRED"  Then
    gSHRED = gSHRED + 1
           elseif MyRec.fields("Species") = "HBX"  Then
    gHBX = gHBX + 1



    end if
    MyRec.movenext
    wend
    MyRec.close
    
    StrRailCars = 0
    
 strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (tblCars.Location='YARD' AND tblCars.Carrier='RAIL' AND (tblCars.Species='KBLD' ))"
   Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then
    strRailCars = MyRec.fields("CountofCID")
gKBLD = gKBLD + (MyRec.Fields("CountofCID") * 2)
end if
    Myrec.close
    
     strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (tblCars.Location='YARD' AND tblCars.Carrier='RAIL' AND ( tblCars.Species='OF3'))"
   Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then
    strRailCars = strRailCars + MyRec.fields("CountofCID")
gOF = gOF + (MyRec.Fields("CountofCID") * 2)
end if
    Myrec.close

 strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (tblCars.Location='YARD' AND tblCars.Carrier='RAIL' AND tblCars.Species='PMX'  )"
   Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then
    strRailCars = strRailCars + MyRec.fields("CountofCID")
    gPMX = gPMX + (MyRec.Fields("CountofCID") * 2)
    end if
    Myrec.close
    
     strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (tblCars.Location='YARD' AND tblCars.Carrier='RAIL' AND tblCars.Species='OCC' )"
   Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then
    strRailCars = strRailCars + MyRec.fields("CountofCID")
    gOCC = gOCC + (MyRec.Fields("CountofCID") * 2)
    end if
    Myrec.close
    
         strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (tblCars.Location='YARD' AND tblCars.Carrier='RAIL' AND (tblCars.Species='MXP'))"
   Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then
    strRailCars = strRailCars + MyRec.fields("CountofCID")
    gMXP = gMXP + (MyRec.Fields("CountofCID") * 2)
    end if
    Myrec.close
    
             strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (tblCars.Location='YARD' AND tblCars.Carrier='RAIL' AND (tblCars.Species='HBX'))"
   Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then
    strRailCars = strRailCars + MyRec.fields("CountofCID")
    gHBX = gHBX+ (MyRec.Fields("CountofCID") * 2)
    end if
    Myrec.close
    
             strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (tblCars.Location='YARD' AND tblCars.Carrier='RAIL' AND (tblCars.Species='SHRED'))"
   Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then
    strRailCars = strRailCars + MyRec.fields("CountofCID")
    gSHRED = gSHRED + (MyRec.Fields("CountofCID") * 2)
    end if
    Myrec.close



''''''''''''''''''''''''''''' This starts the routine to count Inbound Loads  
      
    dim strUse
    strUse = date()
  strDay2 = dateadd("d", -3, strUse)
  strDay0 = dateadd("d", 1, struse)
  
  Dim strTomorrow
  strTomorrow = strDay0
  
   	Dim strOB2, countKOP, countPMX, countOCC, countKBLD2, countPMX2, countOCC2, countMXP, countMXP2
 		countKBLD = 0
 		countPMX = 0
 		countOCC = 0
 		countMXP = 0
 		countHBX = 0
 		countSHRED = 0
 		countMXP2 = 0
 		countKBLD2 = 0
 		countPMX2 = 0
 		countOCC2 = 0
 		countHBX2 = 0
 		countSHRED2 = 0

 		countOF = 0
 		countOF2 = 0
 		strCountK1A = 0
 		strCountK1B = 0
 		strCountK2 = 0
 		strCountKNG = 0
 		strCountOF1A = 0
 		strCountOFB = 0  
 		strCountOFNG = 0
 		strCountK1A2 = 0
 		strCountK1B2 = 0
 		strCountK22 = 0  
 		strCountKNG2 = 0
 		strCountOF1A2 = 0
 		strCountOFB2 = 0 
 		strCountOFNG2 = 0
 		strCountSHRED1 = 0
 		strCountSHRED5 = 0
 		strCountSHREDNG = 0
 		strCountSHRED12 = 0
 		strCOuntSHRED52 = 0
 	 	strCountSHREDNG2 = 0
 	  	strCountSHRED13 = 0
 		strCOuntSHRED53 = 0
 		strCountSHREDNG3 = 0
  		strCountSHRED14 = 0
 		strCOuntSHRED54 = 0
 		strCountSHREDNG4 = 0
 	    strCountSHRED15 = 0
 		strCOuntSHRED55 = 0
 		strCountSHREDNG5 = 0
 	    strCountSHRED16 = 0
 		strCOuntSHRED56 = 0
 		strCountSHREDNG6 = 0
 	     strCountSHRED17 = 0
 		strCOuntSHRED57 = 0
 		strCountSHREDNG7 = 0




  	     strsql = "SELECT  left(release,1) as Species, Release, dateadd(dd, datediff(dd,0,Date_to),0) as Delivery_date "_
  			&" FROM tblInbound "_
			&" WHERE Destination_City ='MOBILE' and Release <> '0'"_	
			&" and dateadd(dd, datediff(dd,0,Date_to),0) > '" & strDay2 & "' "_
			&" and dateadd(dd, datediff(dd,0,Date_to),0) <= '" & strUse & "' "_
			&" and Ship_Status <>'Tendered' "_
			&" ORDER BY Date_to" 	

 	
   		Set MyRec = Server.CreateObject("ADODB.Recordset")
  		 MyRec.Open strSQL, Session("ConnectionString") 
		While not MyRec.eof 	
		
		
		strOB2 = MyRec.fields("Release")
		strsql2 = "Select [Date_received] from tblCars where [Release_nbr] = '" & strOB2 & "'"
 		Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    	MyRec2.Open strSQL2, Session("ConnectionString")
   	 	if not MyRec2.eof then  
  		else
  		
  		strsql3 = "Select Species, OCC_SHRD from tblOrder where Release = '" & strOB2 & "'"
  			Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    	MyRec3.Open strSQL3, Session("ConnectionString")
   	 	if not MyRec3.eof then
  			if MyRec3("Species") = "PMX"  then
				countPMX = countPMX + 1
				elseif MyRec3("Species") = "OCC"  Then
				countOCC = countOCC + 1
				elseif   MyRec3("Species") = "MXP" Then
								countMXP = countMXP + 1
	elseif   MyRec3("Species") = "HBX" Then
					countHBX = countHBX2 + 1

			elseif MyRec3("Species") = "KBLD" Then
				
				countKBLD = countKBLD + 1
						elseif MyRec3("Species") = "OF3" Then		
			
				countOF = countOF + 1
		elseif MyRec3("Species") = "SHRED" and (MyRec3("OCC_SHRD") = "N" or isnull(MyRec3("OCC_SHRD"))) Then					
					
				countShred = countShred + 1
				
				end if ' Species

				end if ' MyRec2eof

				end if ' MyRec2EOF
	
   	 	
  		
		MyRec2.close
 
      	 MyRec.MoveNext
    	 Wend
    	 MyRec.close

       strsql = "SELECT  left(release,1) as Species, Release, dateadd(dd, datediff(dd,0,Date_to),0) as Delivery_date "_
  			&" FROM tblInbound "_
			&" WHERE Destination_City ='MOBILE' and Release <> '0' and  dateadd(dd, datediff(dd,0,Date_to),0) = '" & strTomorrow & "' "_
				&" and Ship_Status <> 'Tendered' "_

			&" ORDER BY Date_to, left(release,1)" 	


   		Set MyRec = Server.CreateObject("ADODB.Recordset")
  	 MyRec.Open strSQL, Session("ConnectionString") 
		While not MyRec.eof 

 		
		strOB2 = MyRec.fields("Release")
		strsql2 = "Select [Date_received] from tblCars where [Release_nbr] = '" & strOB2 & "'"
 		Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    	MyRec2.Open strSQL2, Session("ConnectionString")
   	 	if not MyRec2.eof then  
  		else
  		
  		strsql3 = "Select Species, OCC_Shrd from tblOrder where Release = '" & strOB2 & "'"
  			Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    	MyRec3.Open strSQL3, Session("ConnectionString")
   	 	if not MyRec3.eof then
  			if MyRec3("Species") = "PMX"  then
				countPMX2 = countPMX2 + 1
				elseif MyRec3("Species") = "OCC"  Then
				countOCC2 = countOCC2 + 1
				elseif   MyRec3("Species") = "MXP" Then
								countMXP2 = countMXP2 + 1
	elseif   MyRec3("Species") = "HBX" Then
					countHBX2 = countHBX2 + 1

			elseif MyRec3("Species") = "KBLD" Then
				
				countKBLD2 = countKBLD2 + 1
						elseif MyRec3("Species") = "OF3" Then		
			
				countOF2 = countOF2 + 1
		elseif MyRec3("Species") = "SHRED" and (MyRec3("OCC_SHRD") = "N" or isnull(MyRec3("OCC_SHRD"))) Then					
					
				countShred2 = countShred2 + 1
				
				end if ' Species

				end if ' MyRec3eof

				end if ' MyRec2EOF
				

		MyRec2.close
 
      	 MyRec.MoveNext
    	 Wend
    	 MyRec.close
    	 
			 
    	 

    strKBLD1 = countKBLD  
    strPMX1 = countPMX
    strOCC1 = CountOCC
    strOF1 = countOF
    strMXP1 = countMXP
    strHBX1 = countHBX
    strSHRED1 = countSHRED
    
    strKBLD2 = countKBLD2 
    strPMX2 = countPMX2
    strOCC2 = countOCC2
    strOF2 = countOF2
    strMXP2 = countMXP2
    strHBX2 = countHBX2
    strSHRED2 = countShred2
    
 strsql = "Select tblTempYardTotals.* from tblTempYardTotals where INV_Date = '" & strnow & "'"
		
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
KBLD_One = MyRec.fields("KBLD")
OCC_One = MyRec.fields("Total_OCC")
MXP_One = MyRec.fields("TOtal_MXP")
OF_One = MyRec.fields("OF3")
 
PMX_one = MyRec("PMX")
HBX_one = MyRec("HBX")
SHRED_one = MyREc("SHred_T1")
else
KBLD_One = 0
OCC_One = 0
MXP_One = 0
OF_One = 0
 
PMX_one = 0
HBX_one = 0
SHRED_one = 0


end if
MyRec.close

  strSelectdate = dateadd("d", 1, strnow)
				strsql = "Select  tblTempYardTotals.* from tblTempYardTotals where INV_Date = '" & strSelectdate & "'"
		
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
KBLD_Two = MyRec.fields("KBLD")
OCC_Two = MyRec.fields("Total_OCC")
MXP_Two = MyRec.fields("TOtal_MXP")
OF_Two = MyRec.fields("OF3")
 
PMX_Two = MyRec("PMX")
HBX_Two = MyRec("HBX")
SHRED_Two = MyREc("SHred_T1")
else
KBLD_Two = 0
OCC_Two = 0
MXP_Two = 0
OF_Two = 0
 
PMX_Two = 0
HBX_Two = 0
SHRED_Two = 0

end if
MyRec.close 
strSelectdate = dateadd("d", 2, strnow)

				strsql = "Select  tblTempYardTotals.* from tblTempYardTotals where INV_Date = '" & strSelectdate & "'"
		
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
KBLD_Three = MyRec.fields("KBLD")
OCC_Three = MyRec.fields("Total_OCC")
MXP_Three = MyRec.fields("TOtal_MXP")
OF_Three = MyRec.fields("OF3")
 
PMX_Three = MyRec("PMX")
HBX_Three = MyRec("HBX")
SHRED_Three = MyREc("SHred_T1")
else
KBLD_Three = 0
OCC_Three = 0
MXP_Three = 0
OF_Three = 0
 
PMX_Three = 0
HBX_Three = 0
SHRED_Three = 0
end if
MyRec.close 

strSelectdate = dateadd("d", 3, strnow)
				strsql = "Select  tblTempYardTotals.* from tblTempYardTotals where INV_Date = '" & strSelectdate & "'"
		
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
KBLD_Four = MyRec.fields("KBLD")
OCC_Four = MyRec.fields("Total_OCC")
MXP_Four = MyRec.fields("TOtal_MXP")
OF_Four = MyRec.fields("OF3")
 
PMX_Four = MyRec("PMX")
HBX_Four = MyRec("HBX")
SHRED_Four = MyREc("SHred_T1")
else
KBLD_Four = 0
OCC_Four = 0
MXP_Four = 0
OF_Four = 0
 
PMX_Four = 0
HBX_Four = 0
SHRED_Four = 0
end if
MyRec.close 
 strSelectdate = dateadd("d", 4, strnow)
				strsql = "Select  tblTempYardTotals.* from tblTempYardTotals where INV_Date = '" & strSelectdate & "'"
		
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
KBLD_Five = MyRec.fields("KBLD")
OCC_Five = MyRec.fields("Total_OCC")
MXP_Five = MyRec.fields("TOtal_MXP")
OF_Five = MyRec.fields("OF3")
 
PMX_Five = MyRec("PMX")
HBX_Five = MyRec("HBX")
SHRED_Five = MyREc("SHred_T1")
else
KBLD_Five = 0
OCC_Five = 0
MXP_Five = 0
OF_Five = 0
 
PMX_Five = 0
HBX_Five = 0
SHRED_Five = 0
end if
MyRec.close
 strSelectdate = dateadd("d", 5, strnow)
				strsql = "Select  tblTempYardTotals.* from tblTempYardTotals where INV_Date = '" & strSelectdate & "'"
		
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
KBLD_Six = MyRec.fields("KBLD")
OCC_Six = MyRec.fields("Total_OCC")
MXP_Six = MyRec.fields("TOtal_MXP")
OF_Six = MyRec.fields("OF3")
 
PMX_Six = MyRec("PMX")
HBX_Six = MyRec("HBX")
SHRED_Six = MyREc("SHred_T1")
else
KBLD_Six = 0
OCC_Six = 0
MXP_Six = 0
OF_Six = 0
 
PMX_Six = 0
HBX_Six = 0
SHRED_Six = 0

end if
MyRec.close 
 strSelectdate = dateadd("d", 6, strnow)
				strsql = "Select  tblTempYardTotals.* from tblTempYardTotals where INV_Date = '" & strSelectdate & "'"
		
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
KBLD_Seven = MyRec.fields("KBLD")
OCC_Seven = MyRec.fields("Total_OCC")
MXP_Seven = MyRec.fields("TOtal_MXP")
OF_Seven = MyRec.fields("OF3")
 
PMX_Seven = MyRec("PMX")
HBX_Seven = MyRec("HBX")
SHRED_Seven = MyREc("SHred_T1")
else
KBLD_Seven = 0
OCC_Seven = 0
MXP_Seven = 0
OF_Seven = 0
 
PMX_Seven = 0
HBX_Seven = 0
SHRED_Seven = 0
end if
MyRec.close %>





<% strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars WHERE Species = 'KBLD' and (Carrier <> 'RAIL' or Carrier is null)   and Rejected is null and Net > 0 and  Location = 'YARD'"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
XKBLD = MyRec("CountofCID")
end if
MyRec.close

 

 strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars WHERE Species = 'KBLD' and Carrier= 'RAIL'     and Rejected is null and Net > 0 and  Location = 'YARD'"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
RXKBLD = MyRec("CountofCID")
end if
MyRec.close


strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars WHERE Species = 'OF3' and (Carrier <> 'RAIL' or Carrier is null)   and Rejected is null and Net > 0 and  Location = 'YARD'"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
XOF = MyRec("CountofCID")
end if
MyRec.close


 strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars WHERE Species = 'OF3' and Carrier= 'RAIL'   and Rejected is null and Net > 0 and  Location = 'YARD'"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
RXOF = MyRec("CountofCID")
end if
MyRec.close

 strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars WHERE Species = 'SHRED' and (SHRED_OCC = 0 or SHRED_OCC = Null) and (Carrier <> 'RAIL' or Carrier is null)   and Rejected is null and Net > 0 and  Location = 'YARD'"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
XSHRED = MyRec("CountofCID")
end if
MyRec.close


 strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars WHERE Species = 'SHRED' and (SHRED_OCC = 0 or SHRED_OCC = Null) and Carrier= 'RAIL'   and Rejected is null and Net > 0 and  Location = 'YARD'"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
RXSHRED = MyRec("CountofCID")
end if
MyRec.close




strsql = "SELECT    Count(tblCars.CID) AS CountOfCID FROM tblCars   WHERE Species = 'PMX'  and (Carrier <> 'RAIL' or Carrier is null)   and Rejected is null and Net > 0 and  Location = 'YARD' "
 Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
XPMX = MyRec("CountofCID")
else
XPMX = 0
End if

strsql = "SELECT    Count(tblCars.CID) AS CountOfCID FROM tblCars   WHERE (Species = 'HBX') and (Carrier <> 'RAIL' or Carrier is null)   and Rejected is null and Net > 0 and  Location = 'YARD' "
 Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
XHBX = MyRec("CountofCID")
else
XHBX= 0
End if


strsql = "SELECT    Count(tblCars.CID) AS CountOfCID FROM tblCars   WHERE Species = 'PMX'  and Carrier= 'RAIL'    and Rejected is null and Net > 0 and  Location = 'YARD' "
 Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
RXPMX = MyRec("CountofCID")
else
RXPMX = 0
End if

strsql = "SELECT    Count(tblCars.CID) AS CountOfCID FROM tblCars   WHERE Species = 'HBX' and Carrier= 'RAIL'    and Rejected is null and Net > 0 and  Location = 'YARD' "
 Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
RXHBX = MyRec("CountofCID")
else
RXHBX = 0
End if

 



%>


 <br>
<font face="arial" size="2"><b>Legend - Yard Targets</b></font>
<table border="0" cellpadding="0" width="550" style="width:310pt" class="auto-style17">
	<colgroup>
		<col width="55" span="6" style="mso-width-source:userset;mso-width-alt:2011;
 width:41pt">
	</colgroup>
	<tr height="20">
		<td height="20"  style="height: 15.0pt; " class="auto-style6">
		&nbsp;&nbsp;&nbsp; KBLD</td>
		<td    class="auto-style7">&nbsp;&nbsp; OF3</td>
		<td     class="auto-style7">SHRED</td>
		<td     class="auto-style7">HBX</td>
		<td     class="auto-style7">PMX</td>
	</tr>
	<tr height="20">
		<td height="20" width="55" style="height: 15.0pt; width: 41pt" class="style87">
				&gt;12</td>
	
		<td width="55" style="width: 41pt" class="style87">&gt;10</td>
		<td width="55" style="width: 41pt" class="style90">&gt;12</td>
		<td width="55" style="width: 41pt" class="style90">&gt;2</td>
		<td width="55" style="width: 41pt" class="style90">&gt;4</td>
	</tr>
	<tr height="20">
		<td height="20" width="55" style="height: 15.0pt; width: 41pt" class="style91">
		6-12</td>
 
		<td width="55" style="width: 41pt" class="style91">5-10</td>
		<td width="55" style="width: 41pt" class="style94">6-12</td>
		<td width="55" style="width: 41pt" class="style94">2</td>
		<td width="55" style="width: 41pt" class="style94">2-4</td>
	</tr>
	<tr height="20">
		<td height="20" width="55" style="height: 15.0pt; width: 41pt" class="style95">
		1-5</td>
 
		<td width="55" style="width: 41pt" class="style95">1-4</td>
		<td width="55" style="width: 41pt" class="style98">1-5</td>
		<td width="55" style="width: 41pt" class="style98">1</td>
		<td width="55" style="width: 41pt" class="style98">1</td>
	</tr>
	<tr height="20">
		<td height="20" width="55" style="height: 15.0pt; width: 41pt" class="style99">
		0</td>
	 
		<td width="55" style="width: 41pt" class="style99">0</td>
		<td width="55" style="width: 41pt" class="style102">0</td>
		<td width="55" style="width: 41pt" class="style102">0</td>
		<td width="55" style="width: 41pt" class="style102">0</td>
	</tr>
</table><br>
Current Yard Inventory
<table style="width: 50%" class="style18" cellspacing="0">
	<tr>
		 
		<td colspan="7" class="auto-style5" style="height: 25">Trailers</td>
	 
	</tr>
		<tr><td class="style105">&nbsp;</td><td class="style105">&nbsp;KBLD</td> 
			<td class="style105">&nbsp;OF3</td>
			<td class="style105">&nbsp;SHRED</td>
			<td class="style105">HBX</td><td class="style105">PMX</td>
			<td class="style105">Total</td>
		</tr>
		<tr><td class="style105">Trailers</td>
			<td class="style105"><%= XKBLD %></td>
	 
			<td class="style105"><%= XOF %>&nbsp; </td>
				<td class="style105"><%= XSHRED %>&nbsp; </td>
			 
				<td class="style105"  ><%=  XHBX %>&nbsp;</td>

		<td class="style105"><%= XPMX %>&nbsp;</td>
	
			<td class="style105"><%= XKBLD + (RXKBLD * 3) + XOF + (RXOF*3) + XPMX + (RXPMX * 3) + xHBX + xSHRED + (RXShred * 3) %>&nbsp;</td>
	</tr> 
 
		    <td class="style105">Rail Cars</td>
 
		    <td class="style105"><%= RXKBLD %>&nbsp;</td>
	 
			   <td  class="style105"><%= RXOF  %>&nbsp;</td>
	 
				<td class="style105"><%= RXSHRED %>&nbsp; </td>
			 
				<td class="style105"  ><%=  RXHBX %>&nbsp;</td>
		
			<td class="style105"><%= RXPMX %>&nbsp;</td>
	
			 
	</tr></table>



&nbsp;<table style="width: 85%" class="style18">
	<tr>
		<td colspan="2" style="height: 40px" class="auto-style9"></td>
		<font face="arial" size="1">
		<td colspan="5" class="style44" style="height: 40px"><strong>Inventory BOD</strong></td>

		<td colspan="5" class="auto-style14" style="height: 40px"><strong>Inbounds</strong></td>
		<td style="height: 40px" colspan="5" class="style38"><strong>Consumption</strong></td>
	</tr>
 
		<tr>
		<td class="auto-style8" style="height: 24;  " ></td>
		<td class="auto-style8" style="height: 24px" ></td>

		<td class="style114" style="height: 24px"><strong>
		<span class="style15">KBLD </span></strong>
		<span class="style15"><strong>&nbsp;</strong></span></td>

		<td class="style114" style="height: 24px">OF3</td>
			<td class="style114" style="height: 24px">SHRED</td>
		<td class="style123" style="height: 24px">HBX</td>
		<td class="style123" style="height: 24px">PMX</td>


	<td class="auto-style13"  ><strong>KBLD</td>
	<td class="auto-style13"  ><strong> OF3</td>
		<td class="auto-style13"  ><strong> SHRED </td>
	<td class="auto-style13"  ><strong>HBX</strong></td>

	<td class="auto-style13" style="width: 28px"  ><strong>PMX</strong></td>

<td class="style108" >KBLD</td>
<td class="style108" >OF3</td>
<td class="style108" >SHRED</td>
<td class="style108" >HBX</td>
		<td class="style108"  >PMX</td>



	</tr>
	<tr> 
		<td   class="auto-style15"><font face="arial" size="2"><%= strNow %></td>
		<% strDate1 = datepart("w", strNow)
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

	
		<td class="auto-style8" ><font face="arial" size="2"><%= strDayofWeek %></td>
			
		<td align="center"
<% strGroup1 = xKBLD + 3*(RXKBLD)
strGroupK = strGroup1 %>
			<% if strGroup1 > 12 then %>
		
		bgcolor="#00B0F0" >
				 
				<% elseif strGroup1  < 13 and strGroup1 >5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 6 and strGroup1 > 0 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %>	<span class="style16">
<%= strGroup1 %></span> </td>
			

<td align="center"
<% strGroup1 = xOF + 3*(RXOF) 
strGroupOF = strGroup1 %>
		<% if strGroup1 > 10 then  %>
		
		bgcolor="#00B0F0">
				
				<% elseif strGroup1  > 4 and  strGroup1 < 11 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  > 0 and strGroup1 < 5 then %>
	bgcolor="yellow">

	<% elseif strGroup1 <= 0 then %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>
	 

			
	<td class="style115"
<% strGroup1 = xSHRED + 3*(RXSHRED) 
strGroupSHRED = strGroup1  
			 if strGroup1 > 12 then %>
		
		bgcolor="#00B0F0" >
				 
				<% elseif strGroup1  < 13 and strGroup1 >5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 6 and strGroup1 > 0 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %>	<span class="style16">
<%= strGroup1 %></span> </td>
	 


	<td class="style115"
<% strGroup1 = xHBX + 3*(RXHBX) 
strGroupHBX = strGroup1  
	  if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0"  >
			
				<% elseif strGroup1  = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1   then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %>	<span class="style16">
<%= strGroup1 %></span> </td>



				
				
	<td class="style115"
<% strGroup1 = xPMX + 3*(RXPMX) 
strGroupPMX = strGroup1  
if strGroup1 > 4 then %>
		
		bgcolor="#00B0F0"  >
				
				<% elseif strGroup1  > 1 and strGroup < 5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>
		
		
	
		<td   class="auto-style11" ><%= strKBLD1 %><% if RKT1D1 > 0 then %> / <%= RKT1D1 %> <% end if %></td>	 
	
		<td    class="auto-style11" ><%= strOF1 %><% if ROT1D1 > 0 then %> / <%= ROT1D1 %> <% end if %></td>
		<td    class="auto-style11" ><%= strShred1 %></td>		
		<td   class="auto-style12" ><%= strHBX1 %><% if RstrHBX0 > 0 then %> / <%= RstrHBX0 %> <% end if %></td>
	
		<td   class="auto-style12" ><%= strPMX1 %><% if RstrPMX0 > 0 then %> / <%= RstrPMX0 %> <% end if %></td>
		
		<td class="style62" ><%= KBLD_One %></td>			
		<td   class="style62" ><%= OF_One %></td>		
		<td   class="style62" ><%= SHRED_One   %></td>					
		<td   class="style62" ><%= HBX_One %></td>		
		<td   class="style62" ><%= PMX_One %></td>		
	

	</tr>
	<tr>
		<td  class="auto-style16"  ><font face="arial" size="2"><%= dateadd("d", 1, strnow)%></td>
				<% strDate1 = datepart("w", dateadd("d", 1, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

		<td  class="auto-style16" ><font face="arial" size="2"><%= strDayofWeek %></td>
		
		<td   align="center"
	
		<% strGroup1 =  strGroupK + strKBLD1 - KBLD_One  + (RKT1D1*3)
		strGroup2K = strGroup1		
			 if strGroup1 > 12 then %>
		
		bgcolor="#00B0F0" >
				 
				<% elseif strGroup1  < 13 and strGroup1 >5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 6 and strGroup1 > 0 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %>	<span class="style16">
<%= strGroup1 %></span> </td>
		
		

	<td    class="style115"
<% strGroup1 = strGroupOF + strOF1  - OF_One + (ROT1D1*3)

strGroup2OF = strGroup1 %>
	<% if strGroup1 > 10 then  %>
		
		bgcolor="#00B0F0">
				
				<% elseif strGroup1  > 4 and  strGroup1 < 11 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  > 0 and strGroup1 < 5 then %>
	bgcolor="yellow">

	<% elseif strGroup1 <= 0 then %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

	
			
	<td   class="style115"
<% strGroup1 = strGroupShred + strShred1 - SHRED_One   + 3*(RstrSHRED0)
strGroupSHRED1 = strGroup1 		
			 if strGroup1 > 12 then %>
		
		bgcolor="#00B0F0" >
				 
				<% elseif strGroup1  < 13 and strGroup1 >5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 6 and strGroup1 > 0 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %>	<span class="style16">
<%= strGroup1 %></span> </td>
	 
	<td  class="style115"
<% strGroup1 = strGroupHBX + strHBX1 - HBX_one + 3*(RstrHBX0)
strGroup2HBX = strGroup1  
	  if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" >
				
				<% elseif strGroup1  = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1   then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

			
				
	<td  class="style115"
<% strGroup1 = strGroupPMX + strPMX1 - PMX_one + (3*RstrPMX0)
strGroup2PMX = strGroup1 
	if strGroup1 > 4 then %>
		
		bgcolor="#00B0F0"  >
				
				<% elseif strGroup1  > 1 and strGroup < 5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>
		
		
	
		<td   class="auto-style12" ><span class="style16"><%= strKBLD2 %><% if RKT1D2 > 0 then %> / <%= RKT1D2 %> <% end if %></span> </td>	 
	
	 
	
		<td   class="auto-style11" ><%= strOF2   %><% if ROT1D2 > 0 then %> / <%= ROT1D2 %> <% end if %></span> </td>	
		
		<td   class="auto-style10" ><font face="arial" size="2"><%= strShred2 %></td>

		<td   class="auto-style10" ><font face="arial" size="2"><%= strHBX2 %><% if RstrHBXP1 > 0 then %> / <%= RstrHBX1 %> <% end if %></td>
	
		<td   class="auto-style10" style="width: 28px" ><font face="arial" size="2"><%= strPMX2 %><% if RstrPMX1 > 0 then %> / <%= RstrPMX1 %> <% end if %></td>
		
		

		<td class="style62" ><%= KBLD_Two %></td>		
		
		
				<td   class="style62" ><%= OF_Two   %></td>
		
					<td   class="style62" ><%= SHRED_Two   %></td>
     <td   class="style62" ><%=  HBX_two %></td>
		<td  class="style62" ><%= PMX_two %></td>

	
	</tr>
	<tr>
		<td class="auto-style16" ><font face="arial" size="2"><%= dateadd("d", 2, strnow)%>&nbsp;</td>
						<% strDate1 = datepart("w", dateadd("d", 2, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

		<td class="auto-style16" ><font face="arial" size="2"><%= strDayofWeek %>&nbsp;</td>

			<td   align="center"
	
		<% strGroup1 = strGroup2K + strKBLD2 - KBLD_Two  + (RKT1D2*3)  
		strGroup3K = strGroup1
		 if strGroup1 > 12 then %>
		
		bgcolor="#00B0F0" >
				 
				<% elseif strGroup1  < 13 and strGroup1 >5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 6 and strGroup1 > 0 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %>	<span class="style16">
<%= strGroup1 %></span> </td>
		

	<td   class="style115"
<% strGroup1 = strGroup2OF + strOF2 - OF_Two + (ROT1D2*3)

strGroup3OF = strGroup1 %>
		<% if strGroup1 > 10 then  %>
		
		bgcolor="#00B0F0">
				
				<% elseif strGroup1  > 4 and  strGroup1 < 11 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  > 0 and strGroup1 < 5 then %>
	bgcolor="yellow">

	<% elseif strGroup1 <= 0 then %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>
	

	<td   class="style115"
<% strGroup1 = strGroup2Shred + strShred2 - SHRED_Two  + 3*(RstrSHRED1)
strGroup3SHRED = strGroup1 
			 if strGroup1 > 12 then %>
		
		bgcolor="#00B0F0" >
				 
				<% elseif strGroup1  < 13 and strGroup1 >5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 6 and strGroup1 > 0 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %>	<span class="style16">
<%= strGroup1 %></span> </td>
	 
				
	<td  class="style115"
<% strGroup1 = strGroup2HBX + strHBX2 - HBX_two + 3*(RstrHBX1)
strGroup3HBX = strGroup1  
	  if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" >
				
				<% elseif strGroup1  = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1   then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>
				
	<td  class="style115"
<% strGroup1 = strGroup2PMX + strPMX2 - PMX_two + (3*RstrPMX1)
strGroup3PMX = strGroup1 
	 if strGroup1 > 4 then %>
		
		bgcolor="#00B0F0"  >
				
				<% elseif strGroup1  > 1 and strGroup < 5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>
		
		
	
			<td   class="auto-style12"><span class="style16"><%= strKBLD3  %><% if RKT1D3 > 0 then %> / <%= RKT1D3 %> <% end if %></span> </td>
	
 
	
		<td    class="auto-style11"><%= strOF3  %><% if ROT1D3 > 0 then %> / <%= ROT1D3 %> <% end if %></span> </td>

			<td   class="auto-style10"><font face="arial" size="2"><%= strShred3 %></td>

			<td   class="auto-style10"><font face="arial" size="2"><%= strHBX3 %><% if RstrHBX2 > 0 then %> / <%= RstrHBX2 %> <% end if %></td>

	<td   class="auto-style10" style="width: 28px"><font face="arial" size="2"><%= strPMX3 %><% if RstrPMX2 > 0 then %> / <%= RstrPMX2 %> <% end if %></td>

		
		<td class="style62" ><%= KBLD_Three %></td>		
		
		
				<td   class="style62" ><%= OF_Three  %></td>
		
					<td   class="style62" ><%= SHRED_Three   %></td>
 <td   class="style62"><%=   HBX_three %></td>
 
<td   class="style62"><%= PMX_three %></td>
	</tr>
	<tr>
		<td class="auto-style16"  ><font face="arial" size="2"><%= dateadd("d", 3, strnow)%>&nbsp;</td>
								<% strDate1 = datepart("w", dateadd("d", 3, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

		<td class="auto-style16"><font face="arial" size="2"><%= strDayofWeek %>&nbsp;</td>

		
							<td   align="center"
	
		<% strGroup1 = strGroup3K + strKBLD3 - KBLD_Three  + (RKT1D3*3)  
		strGroup4K = strGroup1
			 if strGroup1 > 12 then %>
		
		bgcolor="#00B0F0" >
				 
				<% elseif strGroup1  < 13 and strGroup1 >5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 6 and strGroup1 > 0 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %>	<span class="style16">
<%= strGroup1 %></span> </td>
		
		 

	<td    class="style115"
	 
<% strGroup1 = strGroup3OF + strOF3 - OF_Three + (ROT1D3*3)

strGroup4OF = strGroup1 %>
		<% if strGroup1 > 10 then  %>
		
		bgcolor="#00B0F0">
				
				<% elseif strGroup1  > 4 and  strGroup1 < 11 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  > 0 and strGroup1 < 5 then %>
	bgcolor="yellow">

	<% elseif strGroup1 <= 0 then %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

			
	<td   class="style115"
<% strGroup1 = strGroup3Shred + strSHRED3 - SHRED_Three + 3*(RstrSHRED2)
strGroup4SHRED = strGroup1  
			 if strGroup1 > 12 then %>		
		bgcolor="#00B0F0" >				 
				<% elseif strGroup1  < 13 and strGroup1 >5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 6 and strGroup1 > 0 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %>	<span class="style16">
<%= strGroup1 %></span> </td>
	 

				
	<td  class="style115"
<% strGroup1 = strGroup3HBX + strHBX3 - HBX_three  + 3*(RstrHBX2)
strGroup4HBX = strGroup1  
	  if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" >
				
				<% elseif strGroup1  = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1   then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

				
	<td  class="style115"
<% strGroup1 = strGroup3PMX + strPMX3 - PMX_three + (3*RstrPMX2)
strGroup4PMX = strGroup1 
	if strGroup1 > 4 then %>
		
		bgcolor="#00B0F0"  >
				
				<% elseif strGroup1  > 1 and strGroup < 5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>
		
		
	
	<td   class="auto-style12"><span class="style16"><%= strKBLD4 %><% if RKT1D4 > 0 then %> / <%= RKT1D4 %> <% end if %></span> </td>
	
		<td    class="auto-style11"><%= strOF4 %><% if ROT1D4 > 0 then %> / <%= ROT1D4 %> <% end if %></span> </td>
					<td   class="auto-style10"><font face="arial" size="2"><%= strShred4 %></td>

			<td   class="auto-style10"><font face="arial" size="2"><%= strHBX4 %><% if RstrHBX3 > 0 then %> / <%= RstrHBX3 %> <% end if %></td>

	<td   class="auto-style10" style="width: 28px"><font face="arial" size="2"><%= strPMX4 %><% if RstrPMX3 > 0 then %> / <%= RstrPMX3 %> <% end if %></td>

		
		<td class="style62" ><%= KBLD_Four %></td>		
		
		
				<td   class="style62" ><%= OF_Four   %></td>
		
					<td   class="style62" ><%= SHRED_Four   %></td>

<td  class="style62"><%= HBX_four %></td>

<td   class="style62"><%= PMX_four %></td>

	</tr>
	<tr>
		<td class="auto-style16"  ><font face="arial" size="2"><%= dateadd("d", 4, strnow)%>&nbsp;</td>
								<% strDate1 = datepart("w", dateadd("d", 4, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

		<td class="auto-style16"><font face="arial" size="2"><%= strDayofWeek %></td>


							<td   align="center"
	
		<% strGroup1 = strGroup4K+ strKBLD4 - KBLD_Four  + (RKT1D4*3)  
		strGroup5K = strGroup1

		 if strGroup1 > 12 then %>
		
		bgcolor="#00B0F0" >
				 
				<% elseif strGroup1  < 13 and strGroup1 >5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 6 and strGroup1 > 0 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %>	<span class="style16">
<%= strGroup1 %></span> </td>
		
	 


	<td    class="style115"
	 
<% strGroup1 = strGroup4OF + strOF4  - OF_Four + (ROT1D4*3)

strGroup5OF = strGroup1 %>
			<% if strGroup1 > 10 then  %>
		
		bgcolor="#00B0F0">
				
				<% elseif strGroup1  > 4 and  strGroup1 < 11 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  > 0 and strGroup1 < 5 then %>
	bgcolor="yellow">

	<% elseif strGroup1 <= 0 then %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

			
	<td   class="style115"
<% strGroup1 = strGroup4Shred + strSHRED4 - SHRED_Four + 3*(RstrSHRED3)
strGroup5SHRED = strGroup1 
			 if strGroup1 > 12 then %>
		
		bgcolor="#00B0F0" >
				 
				<% elseif strGroup1  < 13 and strGroup1 >5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 6 and strGroup1 > 0 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %>	<span class="style16">
<%= strGroup1 %></span> </td>
	 
				
	<td  class="style115"
<% strGroup1 = strGroup4HBX + strHBX4 - HBX_four + 3*(RstrHBX3)
strGroup5HBX = strGroup1  
	  if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" >
				
				<% elseif strGroup1  = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1   then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>
				
				
	<td  class="style115"
<% strGroup1 = strGroup4PMX + strPMX4 - PMX_four + (3*RstrPMX3)
strGroup5PMX = strGroup1  
	if strGroup1 > 4 then %>
		
		bgcolor="#00B0F0"  >
				
				<% elseif strGroup1  > 1 and strGroup < 5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>
		
	
	<td   class="auto-style12"><span class="style16"><%= strKBLD5 %><% if RKT1D5 > 0 then %> / <%= RKT1D5 %> <% end if %></span> </td>

	
 
	
		<td    class="auto-style11"><%= strOF5 %><% if ROT1D5 > 0 then %> / <%= ROT1D5 %> <% end if %></span> </td>
			<td   class="auto-style10"><font face="arial" size="2"><%= strSHRED5%></td>

			<td   class="auto-style10"><font face="arial" size="2"><%= strHBX5 %><% if RstrHBX4 > 0 then %> / <%= RstrHBX4 %> <% end if %></td>

	<td   class="auto-style10" style="width: 28px"><font face="arial" size="2"><%= strPMX5 %><% if RstrPMX4 > 0 then %> / <%= RstrPMX4 %> <% end if %></td>

		
		<td class="style62" ><%= KBLD_Five %></td>		
		
		
				<td   class="style62" ><%= OF_Five  %></td>
		
					<td   class="style62" ><%= SHRED_Five   %></td>
<td  class="style62"><%= HBX_five %></td>
<td   class="style62"><%= PMX_five %></td>
	</tr>
	<tr>
		<td class="auto-style16"  ><font face="arial" size="2"><%= dateadd("d", 5, strnow)%>&nbsp;</td>
				<% strDate1 = datepart("w", dateadd("d", 5, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

		<td class="auto-style16" ><font face="arial" size="2"><%= strDayofWeek %>&nbsp;</td>

			
	<td   align="center"
	
		<% strGroup1 = strGroup5K + strKBLD5 - KBLD_Five  + (RKT1D5*3)  
		strGroup6K = strGroup1

		 if strGroup1 > 12 then %>
		
		bgcolor="#00B0F0" >
				 
				<% elseif strGroup1  < 13 and strGroup1 >5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 6 and strGroup1 > 0 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %>	<span class="style16">
<%= strGroup1 %></span> </td>
		
		 


	<td    class="style115"
	 
<% strGroup1 = strGroup5OF + strOF5  - OF_Five + (ROT1D5*3)

strGroup6OF = strGroup1 %>
		<% if strGroup1 > 10 then  %>
		
		bgcolor="#00B0F0">
				
				<% elseif strGroup1  > 4 and  strGroup1 < 11 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  > 0 and strGroup1 < 5 then %>
	bgcolor="yellow">

	<% elseif strGroup1 <= 0 then %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>
			 
			
	<td   class="style115"
<% strGroup1 = strGroup5Shred + strSHRED5 - SHRED_Five + 3*(RstrSHRED4)
strGroup6SHRED = strGroup1 
			 if strGroup1 > 12 then %>
		
		bgcolor="#00B0F0" >
				 
				<% elseif strGroup1  < 13 and strGroup1 >5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 6 and strGroup1 > 0 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %>	<span class="style16">
<%= strGroup1 %></span> </td>
	 
	 

				
	<td  class="style115"
<% strGroup1 = strGroup5HBX + strHBX5 - HBX_five + 3*(RstrHBX4)
strGroup6HBX = strGroup1  
	  if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" >
				
				<% elseif strGroup1  = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1   then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>
				
				
	<td  class="style115"
<% strGroup1 = strGroup5PMX + strPMX5 - PMX_five + (3*RstrPMX4)
strGroup6PMX = strGroup1  
			if strGroup1 > 4 then %>
		
		bgcolor="#00B0F0"  >
				
				<% elseif strGroup1  > 1 and strGroup < 5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>
		
		
	
	<td   class="auto-style12"><span class="style16"><%= strKBLD6 %><% if RKT1D6 > 0 then %> / <%= RKT1D6 %> <% end if %></span> </td>
	

		<td    class="auto-style11"><%= strOF6 %><% if ROT1D6 > 0 then %> / <%= ROT1D6 %> <% end if %></span> </td>

			<td   class="auto-style10"><font face="arial" size="2"><%= strSHRED6 %></td>

			<td   class="auto-style10"><font face="arial" size="2"><%= strHBX6 %><% if RstrHBX5 > 0 then %> / <%= RstrHBX5 %> <% end if %></td>

	<td   class="auto-style10" style="width: 28px"><font face="arial" size="2"><%= strPMX6 %><% if RstrPMX5 > 0 then %> / <%= RstrPMX5 %> <% end if %></td>

		
		<td class="style62" ><%= KBLD_Six %></td>		
		
		
				<td   class="style62" ><%= OF_Six  %></td>
		
					<td   class="style62" ><%= SHRED_Six  %></td>

<td  class="style62"><%= HBX_six %></td>
<td   class="style62"><%= PMX_six %></td>
	</tr>
	<tr>
		<td class="auto-style16"  ><font face="arial" size="2"><%= dateadd("d", 6, strnow)%></td>
				<% strDate1 = datepart("w", dateadd("d", 6, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

		<td  class="auto-style16" ><font face="arial" size="2"><%= strDayofWeek %></td>

	<td   align="center"
	
		<% strGroup1 = strGroup6K + strKBLD6 - KBLD_Six  + (RKT1D6*3)  

		strGroup7K = strGroup1
			 if strGroup1 > 12 then %>
		
		bgcolor="#00B0F0" >
				 
				<% elseif strGroup1  < 13 and strGroup1 >5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 6 and strGroup1 > 0 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %>	<span class="style16">
<%= strGroup1 %></span> </td>
		

	<td    class="style115"
	 
<% strGroup1 = strGroup6OF + strOF6 - OF_Six + (ROT1D6*3)

strGroup7OF = strGroup1 %>
		<% if strGroup1 > 10 then  %>
		
		bgcolor="#00B0F0">
				
				<% elseif strGroup1  > 4 and  strGroup1 < 11 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  > 0 and strGroup1 < 5 then %>
	bgcolor="yellow">

	<% elseif strGroup1 <= 0 then %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>
			 
			
	<td   class="style115"
<% strGroup1 = strGroup6Shred + strSHRED6 - SHRED_Six + 3*(RstrSHRED5)
strGroup7SHRED = strGroup1 
				 if strGroup1 > 12 then %>
		
		bgcolor="#00B0F0" >
				 
				<% elseif strGroup1  < 13 and strGroup1 >5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 6 and strGroup1 > 0 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %>	<span class="style16">
<%= strGroup1 %></span> </td>
	 
	<td  class="style115"
<% strGroup1 = strGroup6HBX + strHBX6 - HBX_six + 3*(RstrHBX5)
strGroup7HBX = strGroup1  
	  if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" >
			
				<% elseif strGroup1  = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1   then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %>	<span class="style16">
<%= strGroup1 %></span> </td>
				
	<td  class="style115"
<% strGroup1 = strGroup6PMX + strPMX6 - PMX_six + (3*RstrPMX5)
strGroup7PMX = strGroup1  
			  if strGroup1 > 4 then %>
		
		bgcolor="#00B0F0"  >
				
				<% elseif strGroup1  > 1 and strGroup < 5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>
		
	
	<td   class="auto-style12"><span class="style16"><%= strKBLD7 %><% if RKT1D7 > 0 then %> / <%= RKT1D7 %> <% end if %></span> </td> 
	
		<td    class="auto-style11"><%= strOF7  %><% if ROT1D7 > 0 then %> / <%= ROT1D7 %> <% end if %></span> </td> 	
			<td   class="auto-style10"><font face="arial" size="2"><%= strSHRED7 %></td>

			<td   class="auto-style10"><font face="arial" size="2"><%= strHBX7 %><% if RstrHBX6 > 0 then %> / <%= RstrHBX6 %> <% end if %></td>

	<td   class="auto-style10" style="width: 28px;"><font face="arial" size="2"><%= strPMX7 %><% if RstrPMX6 > 0 then %> / <%= RstrPMX6 %> <% end if %></td>

		
		<td class="style62" ><%= KBLD_Seven %></td>		
		
		
				<td   class="style62" ><%= OF_Seven   %></td>
		
					<td   class="style62" ><%= SHRED_Seven   %></td>
<td   class="style62"><%= HBX_seven %></td>
<td   class="style62"><%= PMX_seven %></td>
	</tr>
	<tr>
		<td class="auto-style16"  ></td>
		<td class="auto-style16">  </td>

									<td   align="center"
	
		<% strGroup1 = strGroup7K + strKBLD7 - KBLD_Seven  + (RKT1D7*3)  
 		 if strGroup1 > 12 then %>
		
		bgcolor="#00B0F0" >
				 
				<% elseif strGroup1  < 13 and strGroup1 >5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 6 and strGroup1 > 0 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %>	<span class="style16">
<%= strGroup1 %></span> </td>
		
		 

	<td    class="style115"
	 
<% strGroup1 = strGroup7OF + strOF7  - OF_Seven + (ROT1D6*3)  %>
			<% if strGroup1 > 10 then  %>
		
		bgcolor="#00B0F0">
				
				<% elseif strGroup1  > 4 and  strGroup1 < 11 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  > 0 and strGroup1 < 5 then %>
	bgcolor="yellow">

	<% elseif strGroup1 <= 0 then %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

				
			
	<td   class="style115"
<% strGroup1 = strGroup7Shred + strSHRED7 - SHRED_Seven + 3*(RstrSHRED6) 
				 if strGroup1 > 12 then %>
		
		bgcolor="#00B0F0" >
				 
				<% elseif strGroup1  < 13 and strGroup1 >5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 6 and strGroup1 > 0 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %>	<span class="style16">
<%= strGroup1 %></span> </td>
	 
	<td  class="style115"
<% strGroup1 = strGroup7HBX + strHBX7 - HBX_seven + 3*(RstrHBX6)
	  if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" >
			
				<% elseif strGroup1  = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1   then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %>	<span class="style16">
<%= strGroup1 %></span> </td>
				
				
	<td  class="style115"
<% strGroup1 = strGroup7PMX + strPMX7 - PMX_seven + (3*RstrPMX6)
		  if strGroup1 > 4 then %>
		
		bgcolor="#00B0F0"  >
				
				<% elseif strGroup1  > 1 and strGroup < 5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>
		
		
	
		<td  class="auto-style10"  ></td>
		<td  class="auto-style10"></td>
		<td class="auto-style10" ></td>
			<td class="auto-style10"></td>
			<td class="auto-style10" style="width: 28"></td>

		<td  class="style54"></td>

		<td  class="style54"></td>

		<td class="style54"  ></td>

			<td  class="style54"></td>
		 <td  class="style54"></td>

	</tr>
</table>
<p class="style16">
<strong><span class="style15">Planned Loads from OWB

<table style="width: 25%" class="style71">
     <tr>
    

      <td  align="left"  class="auto-style1"><font face="Arial">Scheduled Date</font></td>
         <td  align="left"  class="auto-style1"><font face="Arial">Material #</font></td>
      <td  align="center"  class="auto-style1"><font face="Arial">Count</font></td>
      </tr>
      
      <% strsql = "SELECT Count(tblSapAutoImport.ID) AS CountOfID, tblSapAutoImport.Delivery_Date, Right([Material],8) AS Mat FROM tblSapAutoImport "_
&" WHERE (((tblSapAutoImport.Vendor)='OWENSBORO GLOBAL SALES MILL') AND ((tblSapAutoImport.Received_qty)=0)) "_
&" GROUP BY tblSapAutoImport.Delivery_Date, Right([Material],8) "_
&" HAVING (((tblSapAutoImport.Delivery_Date)>20181101) AND ((Right([Material],8))='70000166' Or (Right([Material],8))='70000071')) "_
&" ORDER BY tblSapAutoImport.Delivery_Date"

       Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
    
       ii = 0
       while not MyRec.Eof
  if ( ii mod 2) = 0 Then %>
           <tr class=tablecolor1>
    <% else %>
       <tr class=tablecolor2>
    <% end if %>

         <td  align="left"  class="style16">
			<font face="Arial" size = 1 class="style16"><%= mid(MyRec.fields("Delivery_Date"),5,2)%>-<%= right(MyRec.fields("Delivery_Date"),2)%></font></td>
         <td  align="left"  class="style16"><font face="Arial"><%= MyRec.fields("Mat")%></font></td>
      <td  align="center"  class="style16"><font face="Arial"><%= MyRec.fields("countofid")%></font></td>

   </tr>
    <% 
       ii = ii + 1
 
       MyRec.MoveNext
     Wend
     MyRec.close
    %>


   </table>

