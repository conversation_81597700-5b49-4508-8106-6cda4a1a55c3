<html>
<script>window.history.go(1);</script>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionPolaris.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Enter Non-Fiber Trailer Receipt</title>
<style type="text/css">
.auto-style1 {
	border-width: 1px;
	background-color: #F2FBFF;
}
.auto-style2 {
	font-weight: bold;
	border-width: 1px;
	background-color: #F2FBFF;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID, strPO, MyConn, objMOC, rstFiber, strPS, strAT, strUOM, strNewID, strPaper, strBrand 
    Dim strTrailer, strCarrier
    Dim strPONbr
    Dim strRECNbr, gVendorName

    Dim strTonsReceived
    Dim strDateReceived

    Dim strOther, strR, strsql3, gDescription, gPO, gSAP_Nbr, gVendor, gOID, strNet, strMultiLoad

    strPO  = Trim(Request.QueryString("id")) 
	strR = left(strPO,1)

 	set objGeneral = new ASP_CLS_General

  

if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close

 	strTrailer = Request.form("Trailer")
	strOther = Replace(Request.form("Other_Comments"), "'", "''") 
	strDateReceived = Request.form("Date_received")
	strRECNbr = Request.form("Rec_Number")
	strCarrier = Request.form("Carrier")
	strUOM = Request.form("UOM")


	strTonsReceived = Request.form("Tons_received")


	
	If isnull(strTrailer) or strTrailer = ""  or isnull(strCarrier) or strCarrier = ""  or isnull(strTonsReceived) or strTonsReceived = "" or isnull(strDateReceived) or strDateReceived = "" then
	
	Response.redirect("EnterNFReceipt.asp?id=" & strPO & "&n=T")
	else
	Call SaveData() 
	Response.redirect ("SelectWeyInbound.asp")
	end if
	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to save this information.</font></br>")
	MyRec.close
	end if

  
ELSE
  Call GetData()
	strsql = "SELECT tblSAPOpenPO.* FROM tblSAPOpenPO WHERE OID = '" & strPO & "'"

   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
	
	gDescription = MyRec.fields("Description")
	gSap_Nbr = MyRec.fields("Material")
	gVendor = MyRec.fields("Vendor")

	
	IF gvendor = "WEYERHAEUSER COMPANY" then
	strCarrier = "BWIF"
	end if


	If not isnull(MyRec.fields("Loaded_qty")) then
	strTonsReceived = MyRec.fields("Loaded_qty")
	strUOM = MyRec.fields("Loaded_uom")
	else
	strTonsReceived = MyRec.fields("Quantity")
	strUOM = MyRec.fields("OUn")
	end if
	

	strPONbr = MyRec.fields("Purchdoc") & "-" & Myrec.fields("Item")
	gOID = Myrec.fields("OID")
	If MyRec.fields("Trailer") <> "Unknown" then
	strTrailer  = MyRec.fields("Trailer")
	else
	strTrailer = ""
	End if
	
	strMultiload = MyRec.fields("Multi_load")


	MyRec.close
	
	
	strsql = "SELECT tblReference.* FROM tblReference WHERE SAP = " & gSAP_Nbr & ""
	
	Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionPolaris")
   	 If not MyRec.eof then
	strPS = MyRec.fields("Product System")
	strAT = MyRec.fields("Asset Team")
	end if
	MyRec.close
	
	strDateReceived = formatdatetime(Now(),2)
	

end if
%>



<body>

<% if Request.querystring("n") = "T" then %>
<p align = center><font face = arial size = 3 color = red><b>You must enter the Carrier, Trailer Number, Quantity and Unit of Measure.  Please re-enter your information.</p></b></font>
<% else %>
&nbsp;
<% end if %>
<table width = 100%><tr><td width = 33%>
<font face="Arial" size="2">

</td><td align = center width = 34%><b><font face="Arial" size="4">
Enter Trailer Receipt </font> </b></td>
<td align = center><font face="Arial" size="2"><b></td>
<td align = right width = 10%><a href="SelectWeyInbound.asp"><b><font face = arial size = 2>RETURN</a></font></b></td></tr></table>




<form name="form1" action="EnterNFReceipt.asp?id=<%=strPO%>" method="post">
<div align="center">
<table border="1" cellspacing="0" bordercolor="#808080" width="45%" bgcolor="#FFFFE1" style="border-collapse: collapse" cellpadding="0" bordercolorlight="#C0C0C0">
<tr>
    <td width="19%" class="auto-style1">&nbsp;</td>

    <td colspan="2" class="auto-style1">&nbsp;</td>
  </tr>
    <tr>
    <td  align = left width="19%" class="auto-style2" >
   <font face="Arial" size="2">PO Number:&nbsp;</font></td>
<td  align = left width="48%" class="auto-style1">

     <font face="Arial" size="2">

      <%= strPONbr%></font>

  </td>
<td  align = left width="32%" class="auto-style1">

		<font face="Arial"><font size="2"><b>Check to Print Receipt:</b></font><input type="checkbox" name="Print_receipt" value="ON" checked></td></tr>
  <tr>
    <td  align = left width="19%" class="auto-style2" >
    <font face="Arial" size="2">Vendor Receipt #:</font></td>
<td  align = left colspan="2" class="auto-style1">

      <font face="Arial">

      <input name="REC_Number" size="24" value = "<%= strRECNbr%>" style="font-weight: 700"></font></td></tr>
  <tr>

      <td align = left width="19%" class="auto-style2">
  <font face="Arial" size="2">Select Carrier:&nbsp;&nbsp;</font></td>
<td  align = left colspan="2" class="auto-style1">

      <font face="Arial">   
	<select name="Carrier" style="font-weight: 700">
 	<option value="" selected>  Select Carrier (Required)</option>
 	<% if UCASE(left(strTrailer,3)) = "MCH" then 
 	strCarrier = "MCH"
 	end if
 	
 	
 	strsql = "Select Carrier,C_Description, Weight from tblCarrier order by Carrier"
 	   	 Set MyRec4 = Server.CreateObject("ADODB.Recordset")
   	 MyRec4.Open strSQL, Session("ConnectionString")
   	 while not MyRec4.eof %>
   	 <option <% if strCarrier = MyRec4("Carrier") then %> selected <% end if %> value="<%= MyRec4("Carrier") %>"><%= MyRec4("Carrier") %>&nbsp;<%= MyRec4("C_Description") %>&nbsp;<%= MyRec4("Weight") %></option>"

 <%MyRec4.Movenext
 wend
 MyRec4.close %>
     </select></font></td></tr>
<tr>
    <td align="left" width="19%" class="auto-style1">  
	<p>  <b><font face="Arial" size="2">Trailer</font></b><font face="Arial" size="2"><b>:&nbsp;&nbsp;</b></font></td>
    <td colspan="2" class="auto-style1">   <font face="Arial">   

      <input name="Trailer" size="24" value = "<%= strTrailer%>" style="font-weight: 700"></font>&nbsp;&nbsp;
      
      <% if strMultiload = "x"  or strMultiload = "X" then %> <font size = 2 face = arial><b>Multi-Item Load</b></font>
      <% end if %>
      </td>
  </tr>

       <tr>  <td align = left width="19%" class="auto-style2"> <font face="Arial" size="2">
			Quantity:&nbsp;</font></td>
<td align = left colspan="2" class="auto-style1">    <font face="Arial">  
<input name="Tons_Received" size="15" value = "<%= strTonsReceived%>" style="font-weight: 700"> </td></tr>

<tr><td align="left" width="19%" class="auto-style2"><font face="Arial" size="2">Unit of 
	Measure:&nbsp;</font></td>
    <td colspan="2" class="auto-style1">    <font face="Arial">  
	<input name="UOM" size="15" value = "<%= strUOM%>" style="font-weight: 700"></td>
  </tr>
       <tr><td  align = left width="19%" class="auto-style1" >&nbsp;</td>
<td align = left colspan="2" class="auto-style1"> &nbsp;</td></tr>
       <tr><td  align = left width="19%" class="auto-style2" ><font face="Arial" size="2">Date Received:&nbsp;</font></td>
<td align = left colspan="2" class="auto-style1"> <font face="Arial"> 
<input name="Date_Received" size="15" value = "<%= strDateReceived%>" style="font-weight: 700"></font></td></tr>
              <tr>
          <td  align = left width="19%" class="auto-style2" >
   <font face="Arial" size="2">Vendor:&nbsp;</font></td>
<td align = left colspan="2" class="auto-style1">
     <font face="Arial" size="2"> <%= gVendor%>&nbsp;</TD></tr>
   
              <tr>
          <td  align = left width="19%" class="auto-style2" >
   <font face="Arial" size="2">Commodity:&nbsp;</font></td>
<td align = left colspan="2" class="auto-style1"><font face="Arial" size="2"><%= gDescription%>
      &nbsp;</TD></tr>
   
              <tr>
          <td  align = left width="19%" class="auto-style2" >
   <font face="Arial" size="2">SAP #:&nbsp;</font></td>
<td align = left class="auto-style1"><% if len(gSAP_Nbr) > 2 then %>
   <font face="Arial" size="2">   <%= gSAP_Nbr%>&nbsp;
   <% else %>
    <font face="Arial" size="3" color = red> <b>THERE IS SOMETHING WRONG WITH THE PO#, PLEASE EDIT</b>&nbsp;</font>
   </font> <b><font color="#0000FF" face="Arial">
<a href="ML_KDF_Edit.asp">HERE</a></font></b>
<% end if %>
</TD>
<td align = left class="auto-style1">

		<font face="Arial"><input type="checkbox" name="Paperwork" value="ON"></font><b><font face="Arial" size="2">Paper 
	work doesn't match&nbsp; </font></b></TD></tr>
   
              <tr>
          <td  align = left width="19%" class="auto-style1" >
   <b><font face="Arial" size="2">Product System</font></b><font face="Arial" size="2"><b>:&nbsp;</b></font></td>
<td align = left colspan="2" class="auto-style1">
   <font face="Arial" size="2">  <%= strPS%> &nbsp;</TD></tr>
   
              <tr>
          <td  align = left width="19%" class="auto-style2" >
   <font face="Arial" size="2">Asset Team:</font></td>
<td align = left colspan="2" class="auto-style1">
     <font face="Arial" size="2">  <%= strAT%> &nbsp;</TD></tr>
   
         <tr>
          <td  align = left width="19%" class="auto-style2" >
  <font face="Arial" size="2">Comments/Other:&nbsp;</font></td >
   <td align = left colspan="2" class="auto-style1">   <font face="Arial">   
	<input name="Other_Comments" size="60" value = "<%= strOther%>" style="font-weight: 700"></font></td></tr>
<tr>
    <td width="19%" class="auto-style1">&nbsp;</td>

    <td colspan="2" class="auto-style1">&nbsp;</td>
  </tr>

  <tr>
    <td width="19%" class="auto-style1">&nbsp;</td>

    <td align = left colspan="2" class="auto-style1"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" >&nbsp;&nbsp;&nbsp; 
	If this is a multi-load trailer, and &quot;<b><font size="2">Multi-item Load</font></b>&quot; 
	does not appear beside the trailer number,<b> DO NOT SUBMIT</b>.&nbsp; 
	Instead, click <b><font color="#0000FF"><a href="ML_KDF_Edit.asp">HERE</a> </font></b> 
		to note all PO/Line numbers for this load as being a &quot;Multi Item&quot;.</font></td>
  </tr>
</table>

</div>

</form>
</body>
<% Function GetData()
  set objMOC = new ASP_CLS_FIBER
        set rstFiber = objMOC.FiberCarrier()
End Function

Function SaveData()


    strPO  = Trim(Request.QueryString("id")) 

		strsql = "SELECT tblSAPOpenPO.* FROM tblSAPOpenPO WHERE OID = '" & strPO & "'"

   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
	
	gDescription = MyRec.fields("Stype")
	gSap_Nbr = MyRec.fields("Material")
	gVendor = MyRec.fields("Vendor")
	gVendorName = MyRec.fields("Vendor_Name")
	If len(MyRec.fields("Brand")) > 0  then
	strBrand = MyRec.fields("Brand")
	else
	strBrand = ""
	end if 
		strBrand = MyRec.fields("Brand")

	strPONbr = MyRec.fields("Purchdoc")
	gOID = Myrec.fields("OID")


	MyRec.close

		strsql = "SELECT tblReference.* FROM tblReference WHERE SAP = " & gSAP_Nbr & ""
	
	Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionPolaris")
   	 If not MyRec.eof then
	strPS = MyRec.fields("Product System")
	strAT = MyRec.fields("Asset Team")
	end if
	MyRec.close
	
	strDateReceived = Request.form("Date_Received")
	
	If Request.form("Paperwork") = "ON" then
	strPaper = 1
	else
	strPaper = 0
	end if
	
	
	
	Dim strRightNow
	strRightnow = Now()
	
	strsql =  "INSERT INTO tblCars ( Brand, Paperwork, Carrier,  Grade, SAP_Nbr, Vendor, PO, Date_received,  Location, NFID,  Trailer, Other_Comments, REC_Number, "_
	&" Tons_Received, Entry_Time, Entry_BID, Entry_Page, Asset_team, PS, Species, UOM) "_
	&" SELECT '" & strBrand & "', " & strPaper & ", '" & strCarrier & "',  'NF', '" & gSAP_Nbr & "', '" & gVendor & "', '" & strPONbr & "', '" & strDateReceived & "',   'YARD', " & gOID & ",   "_
	&" '" & strTrailer & "', '" & strOther & "', '" & strRecNbr & "', " & strTonsReceived & ",  '" & strRightNow & "', '" & Session("EmployeeID") & "', 'EnterNFReceipt', "_
	&" '" & strAT & "', '" & strPS & "', '" & gDescription & "', '" & strUOM & "'"
	
	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
	strsql = "Select CID from tblCars where NFID = " & gOID & ""
	
	   Set MyConn = Server.CreateObject("ADODB.Recordset")
   		MyConn.Open strSQL, Session("ConnectionString")
   		Dim strCID
   		strCID = MyConn.fields("CID")
   		MyConn.close
		
			strsql = "Insert into tblMovement (CID, Ddate, TDate, From_location, To_location, BID) Select "_
			&" " & strCID & ", '" & strdatereceived & "', '" & strRightNow & "', '" & gVendorName & "', 'Yard', '" & Session("EmployeeID") & "'"
			
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
strsql = "Update tblSAPOpenPO set Status = 'R', Report_Location = 'B', Rec_date = '" & strdatereceived & "' where OID = " & gOID & ""
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
		strsql = "SELECT tblSAPOpenPO.OID FROM tblSAPOpenPO  "_
		&" WHERE  tblSapOpenPO.Trailer = '" & strTrailer & "' and (tblSapOpenPO.Multi_load = 'x' or tblSapOpenPO.Multi_load = 'X') and Status is null"	
		 Set MyConn = Server.CreateObject("ADODB.Recordset")
   		MyConn.Open strSQL, Session("ConnectionString")
   		If not MyConn.eof then
   		strNewID = MyConn.fields("OID")
   		Myconn.close
   		
   			strsql =  "INSERT INTO tblCarsMT ( PID, Carrier,  Grade, SAP_Nbr, Vendor, PO, Date_received,  Location, NFID,  Trailer, Other_Comments, REC_Number, "_
	&" Tons_Received, Entry_Time, Entry_BID, Entry_Page, Asset_team, PS, Species, UOM) "_
	&" SELECT " & strCID & ", '" & strCarrier & "',  'NF', '" & gSAP_Nbr & "', '" & gVendor & "', '" & strPONbr & "', '" & strDateReceived & "',   'YARD', " & gOID & ",   "_
	&" '" & strTrailer & "', '" & strOther & "', '" & strRecNbr & "', " & strTonsReceived & ",  '" & strRightNow & "', '" & Session("EmployeeID") & "', 'EnterNFReceiptMT', "_
	&" '" & strAT & "', '" & strPS & "', '" & gDescription & "', '" & strUOM & "'"
	
	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
   		Response.redirect("EnterNFReceiptMT.asp?id=" & strnewid & "&p=" & strCID)
   		else	
			
	MyConn.close		
Response.redirect("KDF_receipt.asp?id="& strCID)
 end if 
End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->