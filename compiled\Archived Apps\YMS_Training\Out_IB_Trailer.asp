<html>
<script>window.history.go(1);</script>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Car out IB Trailer</title>
<style type="text/css">
.style1 {
	font-weight: bold;
	border-width: 1px;
	background-color: #EAF1FF;
}
.style2 {
	border-color: #C0C0C0;
	border-width: 0;
	background-color: #EAF1FF;
}
.style3 {
	font-weight: bold;
	border-left-color: #C0C0C0;
	border-left-width: 0;
	border-right-color: #EAF1FF;
	border-right-width: 0;
	border-top-color: #C0C0C0;
	border-top-width: 0;
	border-bottom-color: #EAF1FF;
	border-bottom-width: 0;
	background-color: #EAF1FF;
}
.style4 {
	border-left-color: #C0C0C0;
	border-left-width: 0;
	border-right-color: #EAF1FF;
	border-right-width: 0;
	border-top-color: #C0C0C0;
	border-top-width: 0;
	border-bottom-color: #EAF1FF;
	border-bottom-width: 0;
	background-color: #EAF1FF;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID, objMOC, rstFiber, strCarrier, strLocation, strTimeout
      
    Dim strTrailer
    Dim strReleaseNbr
    Dim strRECNbr
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived, strSpecies, strDateSite
    Dim strGenerator
    Dim strGenCity
    Dim strGenState
    Dim strOther, strR, strsql3, strTransTrailer

       strId  = Trim(Request.QueryString("id")) 

strTimeout = formatdatetime(now(),0)   
 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
 		
		Call SaveData() 
		

	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to change the location of a Trailer.</font></br>")
	MyRec.close
	end if

  
ELSE


strsql = "SELECT tblCars.* from tblCars WHERE tblCars.CID = " & strid

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


    strTrailer = MyRec.fields("Trailer")
    strSpecies = MyRec.fields("Species")
    strCarrier = MyRec.fields("Carrier")
	strDateReceived = formatdatetime(Now(),0)
	strDateSite = MyRec.fields("Date_received")
	strTransload = MyRec("RC_Transload")

    

MyRec.close
Call getdata()
	end if

%>



<body><br>
<table width = 100%><tr><td width = 33%></td><td align = center width = 34%>
	<b><font face="Arial">Car-out Inbound Trailer</font></b></td>
	<td align = right>	<font face="Arial"><b><a href="SelectTruckUnload.asp">RETURN</a></b></font></td></tr></table>



<form name="form1" action="Out_IB_Trailer.asp?id=<%=strid%>" method="post">
<input type="hidden" value="<%= strSpecies %>" name="Species">
<input type="hidden" value="<%= strCarrier %>" name="Carrier">
<input type="hidden" value="<%= strDateSite %>" name="Date_site">
<input type="hidden" value="<%= strTrailer %>" name="Trailer">

<table border="1" cellspacing="0" width="60%" bgcolor="#FOFOFF" style="border-collapse: collapse" cellpadding="0" align = center>

  <tr>
    <td  align = center class="style1"  > <b>
   <font face="Arial" size="2">Trailer:&nbsp;</font></b>&nbsp;&nbsp;
   <font face = arial><b> <%= strTrailer%>&nbsp;&nbsp;&nbsp;

 
 <font face="Arial" size="2">Carrier:</font>
    <%= strCarrier %>&nbsp;&nbsp;&nbsp; <font face="Arial" size="2">Species:</font>
     <%= strSpecies %>   </td></tr></table>
     
     <div align="center">
     
     <table border="2"  width="60%" bgcolor="#F0F0FF" style="border-collapse: collapse" cellpadding="2">

    <tr>

      <td  bgcolor="#FOFOFF" align = right width="276" class="style4">
  &nbsp;</td>
<td  align = left class="style4">

      &nbsp;</td></tr>
      
       <tr><td width="276" height="22" class="style4">&nbsp;</td>
  <td height="22" class="style4">&nbsp;</td></tr>
  <tr>

      <td  bgcolor="#FOFOFF" align = right width="276" class="style3">
  <font face="Arial" size="2">New Location:&nbsp;</font></td>

    <td class="style2">	
	<font face="Arial">	
	<select size="1" name="location">
	<option >--Select--</option>
	<option >BALDWIN</option>
	<option >BROKE CENTER</option>
	<option value="Cotton Street WH" >COTTON STREET WH</option>
	<option>DC WHSE</option>
	<option>DFF</option>
	<option>HERBERT ST</option>
 	<option>MERCHANTS</option>
 	<option value="Meyer Bldg">MEYER BUILDING</option>
	<option>MMS</option>
   	<option <% if strSpecies = "OCC" or strSpecies = "LPSBS" or strSpecies = "USBS" or strSpecies = "MXP" or strSpecies = "HWM" or strSpecies = "SWL"   or  strShredOCC = -1 or strShredOCC = True then %>selected<% end if %>>OCC</option>
   
	<option>OWENSBORO</option>
	  	<option>PJ WH</option>

	<option <% if (strSpecies = "KBLD" or strSpecies = "OF3" or strSpecies = "PMX" or strSpecies = "SHRED")  and (strShredOCC = 0 or strShredOCC = False or strShredOCC = Null) then %>selected<% end if %>>RF</option>
	<option>TM BASEMENT</option>
	<option>TRANS LOADING STATION</option>

	</select></font></td>
</tr>

<tr>
    <td  bgcolor="#FOFOFF" width="276" class="style3">  
	<p align="right">  <font face="Arial" size="2">&nbsp;</font></td>
    <td bgcolor="#FOFOFF" class="style4">   &nbsp;</td>
  </tr>
  <tr>
    <td  bgcolor="#FOFOFF" width="276" height="22" class="style4">&nbsp;</td>

    <td bgcolor="#FOFOFF" height="22" class="style4">&nbsp;</td>
  </tr>



       <tr>
          <td  align = right bgcolor="#FOFOFF" width="276" class="style3" >
    <font face="Arial" size="2">Date Relocated:&nbsp;</font></td>
<td align = left class="style2">

      <input type="text" name="Date_Received" size="21" value = "<%= strDateReceived%>"></td></tr>
<tr>
    <td  bgcolor="#FOFOFF" width="276" class="style4">&nbsp;</td>

    <td bgcolor="#FOFOFF" class="style4">&nbsp;</td>
  </tr>

  <tr>
    <td bgcolor="#FOFOFF" width="276" class="style4">&nbsp;</td>

    <td align = left bgcolor="#FOFOFF" class="style2"><Input name="Update" type="submit" Value="Submit" ></td>
  </tr>
</table>

</div>

</form>
</body>
<%
 Function GetData()
  		set objMOC = new ASP_CLS_FIBER
        set rstFiber = objMOC.FiberCarrier()
        strDateReceived = formatdatetime(Now(),0)
End Function



 Function SaveData()
 If request.form("location") = "--Select--" then
 Response.write("You must select a location")
 else
 
 strLocation = Request.form("location")

strDateReceived = Request.form("Date_received")     

dim strNow
strNow = formatdatetime(Now(),0)

         strsql = "Update tblCars set Date_unloaded = '" & strDateReceived & "',  Location = '" & strLocation & "' where CID = " & strid & ""
 
 	     set MyRec = new ASP_CLS_DataAccess
        MyRec.ExecuteSql strSql 
         
         If strTransload = "YES" then 
         'do nothing
         else
         If strLocation = "RF" or strLocation = "OCC" or  strLocation = "BROKE CENTER"  then
         strsql = "Update tblCars set Inv_depletion_date = '" & strDateReceived & "' where CID = " & strid & ""
 
 	     set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
       
         
         end if
         end if ' if not a transload
         
           strsql = "INSERT INTO tblMovement ( CID, DDate, Tdate, From_location, To_location, BID, Comment ) "_
		&" SELECT " & strid & ", '" & strDateReceived & "', '" & strnow & "', 'YARD', '" & strlocation & "', '" & Session("EmployeeID") & "', 'Out IB Trailer'"
        
           set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql


         
    strCarrier = request.form("Carrier")     
    strSpecies = request.form("Species")
    strDateSite = request.form("Date_site")
    strTrailer = request.form("Trailer")
    strDateReceived = formatdatetime(strDateReceived,2)
 
 Dim strEmailAdd
 strsql = "Select Carrier, Email_add  from tblCarrier where  Carrier = '" & strCarrier & "'"
 
    	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")

	If not Myrec.eof then
	
	strEmailadd = MyRec.fields("Email_add")
	if strEmailadd = "<EMAIL>" then
	'skip
	else
 

              strEmailTo = strEmailadd
        	 
             strEBCC = "<EMAIL>"
                 
			Set objMail = Server.CreateObject("CDO.Message")		
			objMail.From = "<EMAIL>"
			objMail.To = strEmailTo
		 
			'objMail.BCC = strEBCC
                 
	If strLocation = "LOUDON"  then
			objMail.Subject = "Trailer " & strTrailer & " Sent to Loudon "
			
	objMail.HTMLBody = "<font face = arial size = 2>Trailer<b> " & strTrailer & "</b> arrived at KC Mobile, AL  on " & strDateSite & " with " & strSpecies & " and was sent to Loudon on " & strDateReceived & "."
elseif strLcoation = "OWENSBORO" then 
			objMail.Subject = "Trailer " & strTrailer & " Sent to Owensboro "
			
	objMail.HTMLBody = "<font face = arial size = 2>Trailer<b> " & strTrailer & "</b> arrived at KC Mobile, AL  on " & strDateSite & " with " & strSpecies & " and was sent to Owensboro on " & strDateReceived & "."

else
objMail.Subject = "Trailer " & strTrailer & " Empty "
objMail.HTMLBody = "<font face = arial size = 2>Trailer<b> " & strTrailer & "</b> arrived at KC Mobile, AL  on " & strDateSite & " with " & strSpecies & " and was emptied on " & strDateReceived & "."
end if
			' objMail.Send
			Set objMail = nothing
		
end if
end if
MyRec.close






If strLocation = "RF" or strLocation = "OCC" Then

Response.redirect ("Grading_entry.asp?id=" & strid)
else

Response.redirect ("SelectTruckUnload.asp")
end if
end if

End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->