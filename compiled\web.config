<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>
    <!-- Remove custom error pages to allow ASP errors to show -->
    <httpErrors errorMode="Detailed" existingResponse="PassThrough" />

    <tracing>
      <traceFailedRequests>
        <add path="*">
          <traceAreas>
            <add provider="ASP" verbosity="Verbose" />
            <add provider="ASPNET" areas="Infrastructure,Module,Page,AppServices" verbosity="Verbose" />
            <add provider="ISAPI Extension" verbosity="Verbose" />
            <add provider="WWW Server" areas="Authentication,Security,Filter,StaticFile,CGI,Compression,Cache,RequestNotifications,Module,FastCGI,Rewrite" verbosity="Verbose" />
          </traceAreas>
          <failureDefinitions statusCodes="300-600" />
        </add>
      </traceFailedRequests>
    </tracing>
  </system.webServer>
</configuration>