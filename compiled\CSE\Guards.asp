﻿<html>
<title>Confined Space Entry</title>

<!--#include file="classes/asp_cls_DataAccessBadge.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_Procedure.asp"-->
<!--#include file="classes/asp_cls_ProcedureBadge.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->

<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_SessionStringBadge.asp"-->


<head>


<meta http-equiv="REFRESH" content="60">


<style>
<!--
 p.MsoNormal
	{mso-style-parent:"";
	margin-bottom:.0001pt;
	font-size:12.0pt;
	font-family:"Times New Roman";
	margin-left:0in; margin-right:0in; margin-top:0in}
.style1 {
	text-align: center;
}
.style2 {
	font-size: x-small;
}
.style3 {
	font-size: small;
}
.style4 {
	color: #000080;
}
-->
</style>
</head>
<% dim rstEPS, rstEPSTotal, MyRec1, strAdmin

 Dim strsql, MyRec, strdate, strdate3
 strnow = dateadd("h", -5, now())
strdate = formatdatetime(strnow,1)
strdate2 = formatdatetime(strnow,2)
strdate3 = formatdatetime(strnow,2)
strdate2 = dateadd("d", -1, strdate2)

strdate3 = dateadd("d", 1, strdate3)
stradmin = "View"
		strSQL = "Select * From tblSecurity Where BID = '" & SEssion("EmployeeID") & "'"

			Set MyRec1 = Server.CreateObject("ADODB.RecordSet")
			MyRec1.Open strSQL, Session("ConnectionString")

			If Not MyRec1.EOF Then
			stradmin = "Edit"
			end if
			MyRec1.close
			
If  session("EmployeeID") = "B41792"  or session("EmployeeID") = "C97338" then
 stradmin = "Edit"
 end if
	  
'getdata()
%>

<body leftmargin="0" topmargin="5" marginwidth="0" marginheight="0">

<table width="100%"  border="0" cellspacing="0" cellpadding="0">
   
            <tr>
              <td bgcolor="#FFFFFF">
				<font color = black size="2" face="Arial, Helvetica, sans-serif">
				<b>Confined Space Entries Scheduled for <%= strdate %></b>
            <table border = 1 cellspacing = 0 cellpadding = 0 width = 95% bordercolor="#F4F4F4"><tr>
             <td align="center" ><font face="Arial" size="2">Permit ID</font></td>
             <td align="center" ><font face="Arial" size="2">Space ID</font></td>
            <td align="center" ><font face="Arial" size="2">Space Name</font></td>
			<td align="center" > 
			<font face="Arial" size="2">Date of <br>Issue</font></td>
			<td align="center" >
			<font face="Arial" size="2">Date<br> Expires</font></td>
				<td align="center" >
			<font face="Arial" size="2">Entry<br>Status</font></td>
			<td align="center" ><span class="style2">ERT</span><font face="Arial" size="2"><br>Approval<br>for < 
			4</font></td>

		</tr>
            
<%  Dim strCount, strSname, strAlert
strCount = 0 
strAlert = 0

strsql = "SELECT tblSOP.SOP_NO, tblSOP.SDescription, tblPermit.Entry_approval,  tblPermit.Date_issued, tblPermit.PID, tblPermit.Date_expired, tblSOP.WorkArea, tblPermit.entry_status "_
		&" FROM tblPermit INNER JOIN tblSOP ON tblPermit.Space_ID = tblSOP.SOP_NO "_
		&" WHERE Date_issued < '" & strdate3 & "' and Date_expired > '" & strdate2 & "' and (Permit_status is null or Permit_status = 'Review Complete')"_
		&" or tblPermit.entry_status = 'Active'"
       Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly 
			While not MyRec.eof 
			if strcount = 0 or (strcount > 0 and strSname <> MyRec.fields("SOP_NO")) then
			if MyRec.fields("Entry_Status") = "Active" and (MyRec.fields("Entry_approval") <> "Yes" or isnull(MyRec.fields("Entry_approval")))  then
			strAlert = strAlert + 1
			end if 
			%>
           <tr>
             <td align="center" ><font face="Arial" size="2"><%= MyRec.fields("PID") %></font></td>

            <td align="center" ><font face="Arial" size="2"><%= MyRec.fields("SOP_NO") %></font></td>
			<td align="left" > 	<font face="Arial" size="2"><%= MyRec.fields("Sdescription") %></td>
			<td align="center"><font face="Arial" size="2">&nbsp;<%= MyRec.fields("Date_issued") %>&nbsp;</td>
			<td align="center" ><font face="Arial" size="2">&nbsp;<%= MyRec.fields("Date_expired") %>&nbsp;</td>
			
		<% if stradmin = "View" then %>
			<% if len(MyRec.fields("Entry_Status")) > 1 then %>
			<td align="center" ><font face="Arial" size="2"><%= MyRec.fields("Entry_Status") %></td>
			<% else %>

				<td align="center" ><font face="Arial" size="2">Dormant</td>
				<% END IF %>
		<% else %>
			<% if len(MyRec.fields("Entry_Status")) > 1 then %>
			<td align="center" ><font face="Arial" size="2"><a href="Status_Change.asp?id=<%= MyRec.fields("Entry_Status") %>&p=<%= MyRec.fields("PID")%>"><%= MyRec.fields("Entry_Status") %></a></td>
			<% else %>
			
			<td align="center" ><font face="Arial" size="2">&nbsp;<a href="Status_Change.asp?id=Dormant&p=<%= MyRec.fields("PID")%>">Dormant</a>&nbsp;</td>
			<% end if %>
	   <% end if %>
		
			<% if stradmin = "View" then %>
				<% if MyRec.fields("Entry_approval") = "Yes" then %>
				<td align="center" ><font face="Arial" size="2">Yes</td>
				<% else %>
				<td align="center" ><font face="Arial" size="2">No</td>
				<% end if %>
<% else %>	
			<% if MyRec.fields("Entry_approval") = "Yes" then %>
			<td align="center" ><font face="Arial" size="2"><a href="Approval_Change.asp?id=Yes&p=<%= MyRec.fields("PID")%>">Yes</a></td>
			<% else %>
			<td align="center" ><font face="Arial" size="2"><a href="Approval_Change.asp?id=No&p=<%= MyRec.fields("PID")%>">No</a></td>
			<% end if %>
		<% end if %>
	
	</tr>
 <%  else
 'skip printing
 end if 
 
 strcount = strcount + 1
		 strSname = MyRec.fields("SOP_NO")
       MyRec.MoveNext
     Wend
     MyRec.close %>
        
          </table>
    


<% Function getdata() 
      set objEPSSearch = new ASP_Cls_ProcedureBadge    


set rstEPS = objEPSSearch.BadgeSearch()

end function %>