																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Edit Incoming Records</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->




<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate, strsql3, strdatetwo, MyConn


strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -60, strtdate)

strdateTwo = dateadd("d", -14, strtdate)




    strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "' and User_type = 'A'"


    Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL3, Session("ConnectionString")


If not Myrec2.eof then



strsql = "SELECT tblSAPOpenPO.* FROM tblSAPOpenPO wHERE Delivdate > '" & strdate & "' order by PO_nbr, Item"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Incoming Non-Fiber Loads in past 60 days</b></font></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1>  
	 <tr class="tableheader">
<td>&nbsp;</td>
			<td  > <p align="center">       <font face="Arial" size="1">PO#</font></td>
		<td  > <p align="center">       <font face="Arial" size="1">Line #</font></td>
		<td  > <p align="center">       <font face="Arial" size="1">Partial Load</font></td>
		<td  ><font face="Arial" size="1"><b>Trailer</b></font></td>

		<td  ><font face="Arial" size="1"><b>Vendor</b></font></td>
		<td  ><font face="Arial" size="1">SAP #</font></td>
    

			<td  ><font face="Arial" size="1">Del Date</font></td>
				<td  ><font face="Arial" size="1">Rec Date</font></td>
		<td  >       <font face="Arial" size="1">Brand</font></td>
		<td  >       <font face="Arial" size="1">Status</font></td>
	<td>       <font face="Arial" size="1">Location</font></td>
<td>       <font face="Arial" size="1">Comments</font></td>
	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td> <font size="1" face="Arial"><a href="KDF_Inbound_Edit.asp?id=<%= MyRec.fields("oID") %>">Edit</a></td>


<td  ><font size="1" face="Arial"><%= MyRec.fields("PurchDoc")%>&nbsp;</font></td>
		<td  ><font size="1" face="Arial"><%= MyRec.fields("Item")%>&nbsp;</font></td>
			<td  > <font size="1" face="Arial"><b><%= MyRec.fields("Partial_load")%></font></b></td>
	<td  > <font size="1" face="Arial"><b><%= MyRec.fields("Trailer")%></font></b></td>

	<% if MyRec.fields("Vendor") = "WEYERHAEUSER COMPANY" then %>
			<td><font size="1" face="Arial">IP</font></td>
			<% else %>
		<td><font size="1" face="Arial"><%= MyRec.fields("Vendor")%></font></td>
		<% end if %>


		<td  ><font face="Arial" size="1"> <%= MyRec.fields("Material")%></font></td>
	
		<td><font size="1" face="Arial"><%= MyRec.fields("DelivDate")%></font></td>
				<td><font size="1" face="Arial"><%= MyRec.fields("Rec_date")%></font></td>
		<td  >        <font size="1" face="Arial">        <%= MyRec.fields("Brand")%></font></td>
		
			<td  >        <font size="1" face="Arial">  <%= MyRec.fields("Status")%>&nbsp;</td>
	
		<td  >		 <font size="1" face="Arial">       
				<% if MyRec.fields("Report_location") = "A" then%>
			     At Door
			     <% elseif MyRec.fields("Report_location") = "B" then%>
			     On Yard
			      <% elseif MyRec.fields("Report_location") = "C" then%>
			      In Transit
			       <% elseif MyRec.fields("Report_location") = "D" then%>
			       On Order
			       <% end if %>&nbsp;   </font></td>
				<td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Comments")%>&nbsp;</font></td>
	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>

<% Myrec2.close 
end if %><!--#include file="Fiberfooter.inc"-->