																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Damaged Trailer List </TITLE>

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 



<% Dim MyRec, strsql, strstartdate
strStartdate = formatdatetime(now(),2)

strsql = "SELECT * from tblCars where len(Damage_Date) > 5 order by Damage_date desc"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<style type="text/css">
.style1 {
	font-family: Arial;
	font-size: x-small;
}
</style>
</head>

<body>

<% if Session("EmployeeID") = "C97338" or Session("EmployeeID") = "B48888" or Session("EmployeeID") = "B55548" or Session("EmployeeID") = "B38763" or Session("EmployeeID") = "B48943" or Session("EmployeeID") = "B53909"  then %>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>


<td align = left><b><font face="Arial">Damaged Trailer list</font></b></td>


</tr>
	    </table>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=60% class = "tablecolor1" border=1 align = center>  
	 <tr class="tableheader">


				<td  align = left>     <font face="Arial" size="2">Trailer</font></td>
		<td class="style1"  > <font face="Arial" size="2">Carrier</font></td>
		<td  align = left class="style1">  <font face="Arial" size="2"> Date Received</font></td>
		<td  align = left class="style1">  <font face="Arial" size="2"> Date Damaged</font></td>
		<td  align = left class="style1">  <font face="Arial" size="2"> Date Repaired</font></td>
		<td  align = left class="style1">  <font face="Arial" size="2"> Date Unloaded</font></td>
	
	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>

	<td  ><font size="2" face="Arial"><%= MyRec.fields("Trailer")%></font></td>
	
<td  ><font size="2" face="Arial"><%= MyRec.fields("Carrier")%></font></td>
	<td  ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td  ><font size="2" face="Arial"><%= MyRec.fields("Damage_date")%>&nbsp;</font></td>
	<td  ><font size="2" face="Arial"><%= MyRec.fields("Damage_repair")%>&nbsp;</font></td>
		<td  ><font size="2" face="Arial"><%= MyRec.fields("Date_unloaded")%>&nbsp;</font></td>
</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>
<% else %>
<p align="center"><font face="arial" size="3"><b>You do not have authorization to view this page</b></font></p>
<% end if %><!--#include file="Fiberfooter.inc"-->