<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Enter Trailer Receipt</title>
</head>
<%
dim strbody, strCID, strECarrier, strETrailer, strELoad, strEDoc, strEDC, strEDCName, strEAdd, strECity, strEState
dim strECC, strEBCC, strESpecies, MyRec, strsql, MyConn2


strsql = "SELECT tblOasisCars.Carrier, tblOasisCars.CID, tblOasisCars.Trailer, tblOasisCars.Load_completed_date, "_
&" tblOasisCars.Doc_ID, tblOasisCars.Ship_Qty AS Quantity, tblOasisCars.OB_DC_ID as DC, tblDCList.[DC_Name], "_
&"  tblDCList.DC_Address, tblDCList.DC_City, tblDCList.DC_State "_
&" FROM tblOasisCars INNER JOIN tblDCList ON tblOasisCars.OB_DC_ID = tblDCList.DC_ID "_
&" WHERE tblOasisCars.Email_flag='YES' order by Load_Completed_date"

   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
   	 
  	 Set MyConn2 = Server.CreateObject("ADODB.Connection")
			MyConn2.Open Session("ConnectionString")
			
If not MyRec.eof then
            strEmailTo = "<EMAIL>, <EMAIL>, <EMAIL>"
                       strECC = "<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>"
                       strEBCC = "<EMAIL>"
                 
                 
			Set objMail = Server.CreateObject("CDO.Message")		
			objMail.From = "<EMAIL>"
			objMail.To = strEmailTo
			objMail.CC = strECC
			objMail.BCC = strEBCC
	
			objMail.Subject = "Trucks ready for Pickup "
			strBody2 = "<table border=0  cellpadding=0 cellspacing=0 width=800><tr><td align = center><font face = arial size = 2>&nbsp; Carrier&nbsp; </td><td align = left><font face = arial size = 2>Trailer&nbsp; </td><td align = left><font face = arial size = 2> BOL/Order # </td><td align = center><font face = arial size = 2>&nbsp; Load Complete&nbsp;</td><td align = center><font face = arial size = 2>&nbsp; DC&nbsp;</td><td align = left><font face = arial size = 2>DC Location</td></tr>"
			
				While not MyRec.EOF
			On error Resume Next
     strECarrier = MyRec.fields("Carrier") 
     strETrailer = MyRec.fields("Trailer")  
     strELoad = MyRec.fields("Load_Completed_Date")  
     strEdoc = MyRec.fields("Doc_ID")   
     strEDC = MyRec.fields("DC")  
     strEDCName = MyRec.fields("DC_Name")
     strEAdd = MyRec.fields("DC_Address")
     strECity = MyRec.fields("DC_City")
     strEState = MyRec.fields("DC_State")
     strCID = MyRec.fields("CID")
     
    	strdate = formatdatetime(now(),0)		
		
			

  strbody2 = strbody2 & " <tr><td align = center><font face = arial size = 1> " & strECarrier &  "</td><td align = left><font face = arial size = 1>" & strETrailer & "</td><td align = left><font face = arial size = 1>"  & strEDoc & "</td><td align = center><font face = arial size = 1>"  & strELoad & "</td><td align = center><font face = arial size = 1>" & strEDC & "</td><td align = left><font face = arial size = 1>" & strEDCName & " " & strEAdd & ", " & strECity & " " & strEState & " </td></tr> "
	MyConn2.Execute("UPDATE tblOasisCars set Email_Flag = 'NO', email_sent = '" & strDate & "' where CID = " & strCID & "")
		    

	MyRec.MoveNext
     			WEND
           		MyRec.Close
           	
	
	objMail.HTMLBody = "<font face = arial size = 2>The following truck(s) are ready to be picked up:<br><br>" & strbody2



			' objMail.Send
			Set objMail = nothing
	end if


   Response.redirect("Send_yard_email.asp")

 %>

