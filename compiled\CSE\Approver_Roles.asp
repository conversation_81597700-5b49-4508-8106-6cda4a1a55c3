
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Approver Roles</TITLE>

<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_Procedure.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<%    Dim   rstTeam, strTeam, rstESL, rstWA, strWA,  strID, objEPS, strName, strName2

    set objGeneral = new ASP_CLS_General %>
	<body>

<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% border=1>
  <tr><td colspan="4" bgcolor="white" class="subheader">&nbsp;</td></tr>

 <TD>
     <p align="left"><font size="3" face="Arial"><b>Approver Roles</b>&nbsp;&nbsp;</font></td>
		<td align = right><a href="Approver_roles.asp"><font face="Arial"><b>Return</b></font></td></tr>
	    </table><font face="Arial"><br>
	    <%  
	        Dim SCRIPT_NAME
		Dim strSQL, gWorkArea
		Dim BACK_TO_LIST_TEXT

		SCRIPT_NAME = Request.ServerVariables("SCRIPT_NAME")

		BACK_TO_LIST_TEXT = "<p>Click <a href=""" & SCRIPT_NAME & """>" _
    		    & "here</a> to go back to record list.</p>"
    		    
    		    gWorkArea = ""
    		    
    		strSQL = "SELECT * from tblAuthorizers where (P_Type = 'A' or P_Type = 'M') and P_BID = '" & Session("Employeeid") & "'"
		        Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly	
			
If not MyRec.eof then
gWorkArea = MyRec.fields("Work_Area")
end if
MyRec.close
	    
    		    
    If len(gWorkArea) > 2 or Session("EmployeeID") = "C97338" then	    
    
		Select Case LCase(Trim(Request.QueryString("action")))
		    Case "add"
		    %>
			</font>
			<form action="<%= SCRIPT_NAME %>?action=addsave" method="post">
			<%  Call GetFormData() %>
		            <font face="Arial">
		            <br><b>Work Area:</b> 
			    <br><font face="Arial"> <select name="Work_area">
       <option value="">--- Select ---</option>
       <%= objGeneral.OptionListAsString(rstWA, "Work_Area", "Work_Area", strWA) %>
     </select></font>
			    <br>
			    <br><b>BID:    </b> 
			    <br><Input name="BID" size="14">
			    <br>
			     <br><b>Hourly or Salary </b> 
			    <br>&nbsp;<select name="HS" size="1">
       <option value="H" selected>Hourly</option>
       <option value="S">Salary</option>
     </select><br>

			    	</font>

			    <br><Input name="Update" type="submit" Value="Add Name" >
 			</form>
                    <%
	            

 		    Case "addsave"
 		   
         strName2 = ReturnEname(Request.Form("BID"))
	
			strSQL = "Insert into tblApprovers (Work_area, P_BID, P_Name, P_Type) Values('" & Request.Form("Work_area") & "', " _
			   & "'" & Request.Form("BID") & "', '" & strName2 & "', '" & Request.form("HS") & "')"

			
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

			Response.Write("Record has been added successfully.")

			
	

		    Case "edit"
			iRecordId = Request.QueryString("id")
			 Call GetFormData() 
			strSQL = "Select * From tblApprovers Where NID = " & iRecordId

			Set MyRec = Server.CreateObject("ADODB.RecordSet")
			MyRec.Open strSQL, Session("ConnectionString")

			If Not MyRec.EOF Then
			    %>
				<form action="<%= SCRIPT_NAME %>?action=editsave" method="post">
				
				    <input type="hidden" name="id" value="<%= MyRec.Fields("NID").Value %>" />
				    <% strWA = MyRec.fields("Work_Area")%>
		            	    <font face="Arial">
		            	    <br><b>Work Area:</b> 
			    	    <br>
                    <font face="Arial">
       <select name="Work_area">
       <option value="">--- All ---</option>
       <%= objGeneral.OptionListAsString(rstWA, "Work_Area", "Work_Area", strWA) %>
     </select>
    </font></TD>
			    	    <br>
			    	    <br><b>BID:    </b> 
			    	    <br>
                    <input Name="BID" value="<%= MyRec.Fields("P_BID").Value %>" size="10" />
			    	   	    <br><br>
					<b>Hourly or Salary 
		    </b> 
		    <br>
                    <select name="HS" size="1">
       <option value="H" selected>Hourly</option>
       <option value="S">Salary</option>
     </select>
			    	   	    <br>
			    	   	    <br><Input name="Update" type="submit" Value="Update Record" > 
				</form>
			    <%
			End If

		
		

		    Case "editsave"
		    	
		       strName2 = ReturnEname(Request.Form("BID"))
			iRecordId = Clng(Request.form("id"))
		
			strSQL = "Update tblApprovers Set Work_area = '" & Request.form("Work_area") & "',  P_BID = '" & Request.Form("BID") & "',"_
			&"  P_Name = '" & strName2 & "', P_Type = '" & Request.form("HS") & "'  Where NID = " & iRecordId

			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

			Response.Write("Record has been updated successfully.")

		

		    Case "delete"
	        ' Get the id to delete
	        dim strPBID, MyRec5, MyConn2, strsql2
			iRecordId = Clng(Request.QueryString("id"))
			strPBID = Request.querystring("n")
			
			
			strsql2 = "Update tblHA set HA_Status = 'Pending' where HA_Approver_1 = '" & strPBID & "' or HA_Approver_2 = '" & strPBID & "'"
			Set MyConn2 = Server.CreateObject("ADODB.Connection")
			MyConn2.Open Session("ConnectionString")
			MyConn2.Execute strSQL2
			MyConn2.Close
			

			strsql2 = "Update tblHA set HA_Approver_1 = Null where HA_Approver_1 = '" & strPBID & "'"
			Set MyConn2 = Server.CreateObject("ADODB.Connection")
			MyConn2.Open Session("ConnectionString")
			MyConn2.Execute strSQL2
			MyConn2.Close
			

			strsql2 = "Update tblHA set HA_Approver_2 = Null where HA_Approver_2 = '" & strPBID & "'"
			Set MyConn2 = Server.CreateObject("ADODB.Connection")
			MyConn2.Open Session("ConnectionString")
			MyConn2.Execute strSQL2
			MyConn2.Close
			
	

			strSQL = "DELETE FROM tblApprovers WHERE NID = " & iRecordId 

			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

			Response.Write("Record has been deleted successfully.")


		    Case else   'Default view
		    If Session("EmployeeID") = "C97338" then
		    	strSQL = "SELECT  distinct tblApprovers.NID,  tblApprovers.Work_area, tblApprovers.P_BID, tblApprovers.P_Name, tblApprovers.P_Type "_
			&"	FROM tblApprovers INNER JOIN tblAuthorizers ON tblApprovers.Work_area = tblAuthorizers.Work_area "_
			&"   order by tblApprovers.work_area, tblApprovers.P_Name"
else
			strSQL = "SELECT  distinct tblApprovers.NID,  tblApprovers.Work_area, tblApprovers.P_BID, tblApprovers.P_Name, tblApprovers.P_Type "_
			&"	FROM tblApprovers INNER JOIN tblAuthorizers ON tblApprovers.Work_area = tblAuthorizers.Work_area "_
			&" WHERE (((tblAuthorizers.P_BID)='" & Session("EmployeeID") & "')) order by tblApprovers.work_area, tblApprovers.P_Name"
			end if
		        Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
			%>
			<table Border=1 bgcolor = #F0F0F0 BORDERCOLOR = #CCCCFF cellpadding = 3 width = 75% align = center>
			<thead>
			    <tr>
		
                	
                		<th bgcolor="#FDFEE9"> <font face="arial" size="2">Work Area</font></th>
	     		
                		<th bgcolor="#FDFEE9"> <font face="arial" size="2">Name</font></th>
                	
                		
			
	     	
                	
                		<th bgcolor="#FDFEE9"> <font size="2">Hourly/Salary</font></th>
                	
                		
			
	     	
                	
                		<th colspan="2" bgcolor="#FDFEE9">&nbsp;</th>
                       
              	    </tr>
			    </thead>
			    <tbody>
	
        		        <% While not MyRec.EOF %>
              			    <tr>
               <td bgcolor="#FFFFFF"><font face="Arial"><%= MyRec.Fields("Work_area").Value %></font></td>
              		   <td bgcolor="#FFFFFF"><font face="Arial"><%= MyRec.Fields("P_Name").Value %></font></td>
              		   <td align = center bgcolor="#FFFFFF"><font face="Arial"><%= MyRec.Fields("P_Type").Value %>&nbsp;</td>
					<td align="center" bgcolor="#FFFFFF"><font face="Arial"><a href="<%= SCRIPT_NAME %>?action=edit&id=<%= MyRec.Fields("NID").Value %>">Edit</a></font></td>
              			 <td align="center" bgcolor="#FFFFFF"><font face="Arial"><a href="<%= SCRIPT_NAME %>?action=delete&id=<%= MyRec.Fields("NID").Value %>&n=<%= MyRec.Fields("P_Name")%>">Delete</a></font></td>
  						 </tr>
                            
              			    <% 
				        MyRec.MoveNext
                 			WEND
                 			MyRec.Close
              			    %>

			    </tbody>
			    <tfoot>
				<tr>
				    <td colspan="5" align="right"><font face="Arial"><a href="<%= SCRIPT_NAME %>?action=add">Add a new record</a></font></td>
				</tr>
			    </tfoot>

            		</table>
		<font face="Arial">
		<%					
		End Select
		%>                    
      	</font>                    
      	</body>
      	
      	  <%  Function GetFormData()
	set objEPS = new ASP_CLS_ProcedureESL
	Dim strBID
	strBID = Session("Employeeid")
  	set rstWA = objEPS.Approver(strBID)

    End Function
   else
    Response.write ("<font face = arial size = 3>You do not have authorization to view this page")
   
    end if  %><!--#include file="footer.inc"-->