﻿<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->


<%

dim strID
strID = Request.querystring("id")

    set objGeneral = new ASP_CLS_General
  %>


   <form action="KDF_Edit_Delete_sys.asp?id=<%= strid%>&t=<%= strTrailer %>" method=post name="ReceiptDelete">

                <table width = 100%> 
<tr><td></td><td colspan=2 align = right><font face="Arial"><a href="NF_Sixty_day_list.asp"><b>Return</b></a></font></td></tr>
<% Dim <PERSON>, strsql1, strDate, strTrailer, strNFID, MyRec2


strsql1 = "SELECT tblCars.* FROM tblCars WHERE CID = " & strid & ""

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL1, Session("ConnectionString")

strTrailer = MyRec.fields("Trailer")
strNFID = MyRec.fields("NFID") %>


     <tr>
                        <td><Font size = 2 face = Arial> Are you sure you want to delete the Receipt?</b>? <br><br> If so, click the button below.
                           
            </td>
            
        </tr>
        
    </table>
<% Myrec.close %>
<p>

<Input name="Update" type="submit" Value="Delete Receipt" >
</form>


  <% if objGeneral.IsSubmit() Then 


Dim strsQL3

    strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


    Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL3, Session("ConnectionString")


If not Myrec2.eof then
   
    strid = request.querystring("id")
    strDate = formatdatetime(Now(),0)
    strTrailer = Request.querystring("t")
  
    
   
  
    
strsql1 = "SELECT tblCars.* FROM tblCars WHERE CID = " & strid & ""

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL1, Session("ConnectionString")

strTrailer = MyRec.fields("Trailer")
strNFID = MyRec.fields("NFID")
MyRec.close
    
           strsql = "INSERT INTO tblMovement ( CID, DDate, From_location,  Comment, BID ) "_
		&" SELECT " & strid & ", '" & strDate & "', 'Deleted', '" & strTrailer & "', '" & Session("EmployeeID") & "'"
        
           set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql
    
    
    
    
	Dim strsql, Myconn
	strSQL = "DELETE FROM tblCars where tblCars.CID = " &  strID & ""

			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close



	strSQL = "DELETE FROM tblCarsMT where PID = " &  strID & ""

			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close



	strSQL = "Update tblSapOpenPO set Status = Null, Report_location = 'C' where OID = " & strNFID & ""

			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close


Response.redirect ("NF_Sixty_day_list.asp") 
else
Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to delete a Receipt.</font></br>")

end if


end if


%><!--#include file="Fiberfooter.inc"-->