																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>AIG Vendors</TITLE>
<!--#include file="classes/asp_cls_DataAccessAIG.asp"-->
 
<!--#include file="classes/asp_cls_SessionStringAIG.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->




<% Dim MyRec, strsql, MyConn, strUserType

 Response.ContentType = "application/vnd.ms-excel"
strsql = "SELECT tblAIGUserType.* FROM tblAIGUserType where  BID = '" & Session("EmployeeID") & "' and User_type = 'V'" 

    Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
    If not MyConn.eof then
    struserType = MyConn.fields("User_type")
    else
   
    strUserType = ""
    end if
    If strUserType = "" and Session("EmployeeID") <> "C97338" then

    Response.write ("<br><br><font face = arial size = 3><b>You do not have authorization to view this page</b></font>")
   else
    BuildScreen()
    end if 

    %>
    <%Sub Buildscreen



strsql = "SELECT tblVendors.* FROM tblVendors order by Vendor " 
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<style type="text/css">
.style1 {
	font-family: Arial;
}
</style>
</head>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>
 
<td align = center><b>
<font face="arial" size="4" >AIG Vendors</font></b></span></td>



</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1 align = center>  
	 <tr class="tableheader">
 
	<td  >  <font face="Arial" size="2">Vendor</font></td>
	<td  >  <font face="Arial" size="2">Vendor Number</font></td>
		<td  >  <font face="Arial" size="2">Remit to Number</font></td>
		<td  >  <font face="Arial" size="2">Vendor Name</font></td>
        <td  > <font face="Arial" size="2">Vendor Address</font></td>
             <td  > <font face="Arial" size="2">City, State, Zip</font></td>
               <td  > <font face="Arial" size="2">Email Address</font></td>
               <td  > <font face="Arial" size="2">Summary Notation</font></td>
 
		


	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
     
	<td  >      <font size="2" face="Arial">        <%= MyRec.fields("Vendor")%>&nbsp;</font></td>
      	<td  >      <font size="2" face="Arial">        <%= MyRec.fields("Vendor_nbr")%>&nbsp;</font></td>
      	  	<td  >      <font size="2" face="Arial">        <%= MyRec.fields("Payee_Nbr")%>&nbsp;</font></td>
      		<td  >      <font size="2" face="Arial">        <%= MyRec.fields("Vendor_name")%>&nbsp;</font></td>
			<td  >        <font size="2" face="Arial">        <%= MyRec.fields("Vendor_Address")%>&nbsp;</font></td>
			<td  >        <font size="2" face="Arial">        <%= MyRec.fields("Vendor_Street")%>&nbsp;</font></td>
			<td  >        <font size="2" face="Arial">        <%= MyRec.fields("Email_Address")%>&nbsp;</font></td>
				<td  >        <font size="2" face="Arial">        <%= MyRec.fields("Address")%>&nbsp;</font></td>

		 
	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
     MyRec.close
    %>
</table>

<% End Sub %>