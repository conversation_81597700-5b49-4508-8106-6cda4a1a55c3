
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Overtime Entry</TITLE>
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_Session.asp"-->
<!--#include file="classes/asp_cls_DataAccess.asp"-->

</head>
<style type="text/css">
.style3 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
.style4 {
	font-family: Arial;
	font-size: medium;
}
.style5 {
	border-style: solid;
	border-color: #C0C0C0;
	background-color: #E6ECF9;
}
.style6 {
	font-family: <PERSON>ibri;
}
.style7 {
	border-style: solid;
	border-color: #C0C0C0;
	font-family: Calibri;
	background-color: #E6ECF9;
}
.style8 {
	color: #FF0000;
}
</style>

<% Dim strName, strBID, strSite, strType

strid = request.querystring("id")
strdate = request.querystring("d")
strCrew = request.querystring("c")
strMachine = request.querystring("m")

STRSQL = "Select EMP_ID from tblOT_Admin where EMP_ID = '" & Session("EmployeeiD") & "'  "

Set MyS = Server.CreateObject("ADODB.Recordset") 
   		Set MyS = Server.CreateObject("ADODB.RecordSet")
		MyS.Open strSQL, Session("ConnectionString")
If not MyS.eof then
strPost = "OK"
else 
strPost = ""
end if
MyS.close


if strid > 0 then 
strsql = "Select tblOT_Master.* from tblOT_Master where ID = " & strid
    	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		if not MyRec.eof then 
strdate = MyREc("OT_week")
strMachine = MyRec("Machine")
strCrew = MyREc("Crew")
str1D = MyRec("Sun_Day")
str2D = MyRec("Mon_day")
str3D = MyREc("Tue_day")
str4D = MyRec("Wed_day")
str5D = MyREc("Thu_Day")
str6D = MyREc("Fri_day")
str7D = MyREc("Sat_Day")
str1N = MyRec("Sun_Night")
str2N = MyRec("Mon_Night")
str3N = MyREc("Tue_Night")
str4N = MyRec("Wed_Night")
str5N = MyREc("Thu_Night")
str6N = MyREc("Fri_Night")
str7N = MyREc("Sat_Night")

MyREc.close
end if 
end if

If strid = 0 then  



strsql = "Select tblOT_Master.* from tblOT_Master where OT_Week = '" & strdate & "' and Machine = '" & strMachine & "' and Crew = '" & strCrew & "' "
    	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		if not MyRec.eof then 
 
		'do nothing
		else



	strsql2 = "Insert into tblOT_Master (OT_Week, Machine, Crew) values ('" & strdate & "', '" & strMachine & "', '" & strcrew & "')"
 	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql2
       
         
         strsql = "Select ID from tblOT_Master where OT_Week = '" & strdate & "' and Machine = '" & strMachine & "' and Crew = '" & strCrew & "' "
    	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		strid = MyREc("ID")
		MyREc.close

          Response.redirect("OT_Entry.asp?id=" & strid)
          
         end if
     
      
         end if

strD1 = datepart("d", strdate)
strdate2 = dateadd("d", 1, strdate)
strD2 = datepart("d", strdate2)
strdate3 = dateadd("d", 1, strdate2)
strD3 = datepart("d", strdate3)
strdate4 = dateadd("d", 1, strdate3)
strD4 = datepart("d", strdate4)
strdate5 = dateadd("d", 1, strdate4)
strD5 = datepart("d", strdate5)
strdate6 = dateadd("d", 1, strdate5)
strD6 = datepart("d", strdate6)
strdate7 = dateadd("d", 1, strdate6)
strD7 = datepart("d", strdate7)

 
 set objGeneral = new ASP_CLS_General
 
 
	


if objGeneral.IsSubmit() Then


	Call SaveData() 

End if %>

<body><form name="form1" action="OT_Entry.asp?id=<%= strid %>" method="post">
 
<table border="0" cellpadding="0" cellspacing="0" width = 60% style="border-collapse: collapse" bordercolor="000000" height="25" align = center >
  <tr>
    <td  bgcolor="#FFFFFF" bordercolor="#CCCCFF" height="25" >
    <p align="center">
	<span class="style4">Overtime Week&nbsp; for <%= strMachine %>&nbsp;&nbsp; Crew 
	
<% if strCrew = "A" then %>
A/C
<% else %>
B/D
<% end if %> </span></td>
    <td align = center height="25"><font face="Arial"><b><a href="Select.asp">RETURN</a></b></font></td>

    <td align = center bgcolor="#FFFFFF" height="25">
    <% if strPost = "OK" then %>
  <Input name="Submit" type="submit" Value="Submit" style="float: right" >
  <% end if %></td>
  </tr>
</table><br><br>
<div align="center">
<table cellpadding="0" cellspacing="0" bordercolor="#808080"  height="10" class="style3" style="width: 80%">
    <tr>
<td height="22" align="center" class="style5" ><span class="style6">Week Of: <%= strdate %></span> </td>
   
  <td height="22" align="center" class="style7" >
	Date <%= strD1 %></td>
   
  <td height="22" align="center" class="style7" >
	Date <%= strD2 %></td>
   
  <td height="22" align="center" class="style7"  >
	Date <%= strD3 %></td>
 
  <td height="22" align="center" class="style7"  >
	Date <%= strD4 %></td>
 
  <td height="22" align="center" class="style7"  >
	Date <%= strD5 %></td>
 
  <td height="22" align="center" class="style7"   >
	Date <%= strD6 %></td>
 
  <td height="22" align="center" class="style7" s  >
	Date <%= strD7 %></td>
 
  <td height="22" align="center" class="style7" s  >
	&nbsp;</td>
 
  <td height="22" align="center" class="style7" s  >
	&nbsp;</td>
 
  </tr>
  
    <tr>
<td height="22" align="center" class="style7" >Day of Week</td>
   
  <td height="22" align="center" class="style7" >
	SUN</td>
   
  <td height="22" align="center" class="style7" >
	MON</td>
   
  <td height="22" align="center" class="style7"  >
	TUE</td>
 
  <td height="22" align="center" class="style7"  >
	WED</td>
 
  <td height="22" align="center" class="style7"  >
	THU</td>
 
  <td height="22" align="center" class="style7"   >
	FRI</td>
 
  <td height="22" align="center" class="style7" s  >
	SAT</td>
 
  <td height="22" align="center" class="style7" s  >
	&nbsp;</td>
 
  <td height="22" align="center" class="style7" s  >
	&nbsp;</td>
 
  	</tr>
	<tr>
<td height="22" align="center" class="style7" >Day Needs</td>
   
  <td height="22" align="center" class="style7" >
	<select name="D_SUN">
	<option <% if str1D = 0 then %> selected <% end if %>>0</option>
	<option <% if str1D = 1 then %> selected <% end if %>>1</option>
	<option <% if str1D = 2 then %> selected <% end if %>>2</option>
	<option <% if str1D = 3 then %> selected <% end if %>>3</option>
	<option <% if str1D = 4 then %> selected <% end if %>>4</option>
	<option <% if str1D = 5 then %> selected <% end if %>>5</option>
	<option <% if str1D = 6 then %> selected <% end if %>>6</option>
	<option <% if str1D = 7 then %> selected <% end if %>>7</option>
	<option <% if str1D = 8 then %> selected <% end if %>>8</option> 
	<option <% if str1D = 9 then %> selected <% end if %>>9</option> 
	</select></td>
   
  <td height="22" align="center" class="style7" >
		<select name="D_MON">
	<option <% if str2D = 0 then %> selected <% end if %>>0</option>
	<option <% if str2D = 1 then %> selected <% end if %>>1</option>
	<option <% if str2D = 2 then %> selected <% end if %>>2</option>
	<option <% if str2D = 3 then %> selected <% end if %>>3</option>
	<option <% if str2D = 4 then %> selected <% end if %>>4</option>
	<option <% if str2D = 5 then %> selected <% end if %>>5</option>
	<option <% if str2D = 6 then %> selected <% end if %>>6</option>
	<option <% if str2D = 7 then %> selected <% end if %>>7</option>
	<option <% if str2D = 8 then %> selected <% end if %>>8</option> 
	<option <% if str2D = 9 then %> selected <% end if %>>9</option> 	</select></td>

   
  <td height="22" align="center" class="style7"  >
		<select name="D_TUE">
	<option <% if str3D = 0 then %> selected <% end if %>>0</option>
	<option <% if str3D = 1 then %> selected <% end if %>>1</option>
	<option <% if str3D = 2 then %> selected <% end if %>>2</option>
	<option <% if str3D = 3 then %> selected <% end if %>>3</option>
	<option <% if str3D = 4 then %> selected <% end if %>>4</option>
	<option <% if str3D = 5 then %> selected <% end if %>>5</option>
	<option <% if str3D = 6 then %> selected <% end if %>>6</option>
	<option <% if str3D = 7 then %> selected <% end if %>>7</option>
	<option <% if str3D = 8 then %> selected <% end if %>>8</option> 
	<option <% if str3D = 9 then %> selected <% end if %>>9</option> 
		</select></td>

 
  <td height="22" align="center" class="style7"  >
		<select name="D_WED">
	<option <% if str4D = 0 then %> selected <% end if %>>0</option>
	<option <% if str4D = 1 then %> selected <% end if %>>1</option>
	<option <% if str4D = 2 then %> selected <% end if %>>2</option>
	<option <% if str4D = 3 then %> selected <% end if %>>3</option>
	<option <% if str4D = 4 then %> selected <% end if %>>4</option>
	<option <% if str4D = 5 then %> selected <% end if %>>5</option>
	<option <% if str4D = 6 then %> selected <% end if %>>6</option>
	<option <% if str4D = 7 then %> selected <% end if %>>7</option>
	<option <% if str4D = 8 then %> selected <% end if %>>8</option> 
	<option <% if str4D = 9 then %> selected <% end if %>>9</option> 	</select></td>
  <td height="22" align="center" class="style7"  >
		<select name="D_THU">
<option <% if str5D = 0 then %> selected <% end if %>>0</option>
	<option <% if str5D = 1 then %> selected <% end if %>>1</option>
	<option <% if str5D = 2 then %> selected <% end if %>>2</option>
	<option <% if str5D = 3 then %> selected <% end if %>>3</option>
	<option <% if str5D = 4 then %> selected <% end if %>>4</option>
	<option <% if str5D = 5 then %> selected <% end if %>>5</option>
	<option <% if str5D = 6 then %> selected <% end if %>>6</option>
	<option <% if str5D = 7 then %> selected <% end if %>>7</option>
	<option <% if str5D = 8 then %> selected <% end if %>>8</option> 
	<option <% if str5D = 9 then %> selected <% end if %>>9</option> 
		</select></td>

 
  <td height="22" align="center" class="style7"   >
		<select name="D_FRI">
	<option <% if str6D = 0 then %> selected <% end if %>>0</option>
	<option <% if str6D = 1 then %> selected <% end if %>>1</option>
	<option <% if str6D = 2 then %> selected <% end if %>>2</option>
	<option <% if str6D = 3 then %> selected <% end if %>>3</option>
	<option <% if str6D = 4 then %> selected <% end if %>>4</option>
	<option <% if str6D = 5 then %> selected <% end if %>>5</option>
	<option <% if str6D = 6 then %> selected <% end if %>>6</option>
	<option <% if str6D = 7 then %> selected <% end if %>>7</option>
	<option <% if str6D = 8 then %> selected <% end if %>>8</option> 
	<option <% if str6D = 9 then %> selected <% end if %>>9</option> 
		</select></td>

 
  <td height="22" align="center" class="style7" s  >
		<select name="D_SAT">
	<option <% if str7D = 0 then %> selected <% end if %>>0</option>
	<option <% if str7D = 1 then %> selected <% end if %>>1</option>
	<option <% if str7D = 2 then %> selected <% end if %>>2</option>
	<option <% if str7D = 3 then %> selected <% end if %>>3</option>
	<option <% if str7D = 4 then %> selected <% end if %>>4</option>
	<option <% if str7D = 5 then %> selected <% end if %>>5</option>
	<option <% if str7D = 6 then %> selected <% end if %>>6</option>
	<option <% if str7D = 7 then %> selected <% end if %>>7</option>
	<option <% if str7D = 8 then %> selected <% end if %>>8</option> 
	<option <% if str7D = 9 then %> selected <% end if %>>9</option> 
		</select></td>

 
  <td height="22" align="center" class="style7" s  >
		&nbsp;</td>

 
  <td height="22" align="center" class="style7" s  >
		&nbsp;</td>

 
  	</tr>
	<tr>
<td height="22" align="center" class="style7" >Night Needs</td>
   
  <td height="22" align="center" class="style7" >
	<select name="N_SUN">
	<option <% if str1N = 0 then %> selected <% end if %>>0</option>
	<option <% if str1N = 1 then %> selected <% end if %>>1</option>
	<option <% if str1N = 2 then %> selected <% end if %>>2</option>
	<option <% if str1N = 3 then %> selected <% end if %>>3</option>
	<option <% if str1N = 4 then %> selected <% end if %>>4</option>
	<option <% if str1N = 5 then %> selected <% end if %>>5</option>
	<option <% if str1N = 6 then %> selected <% end if %>>6</option>
	<option <% if str1N = 7 then %> selected <% end if %>>7</option>
	<option <% if str1N = 8 then %> selected <% end if %>>8</option> 
	<option <% if str1N = 9 then %> selected <% end if %>>9</option> 
		</select></td>

   
   <td height="22" align="center" class="style7" >
		<select name="N_MON">
	<option <% if str2N = 0 then %> selected <% end if %>>0</option>
	<option <% if str2N = 1 then %> selected <% end if %>>1</option>
	<option <% if str2N = 2 then %> selected <% end if %>>2</option>
	<option <% if str2N = 3 then %> selected <% end if %>>3</option>
	<option <% if str2N = 4 then %> selected <% end if %>>4</option>
	<option <% if str2N = 5 then %> selected <% end if %>>5</option>
	<option <% if str2N = 6 then %> selected <% end if %>>6</option>
	<option <% if str2N = 7 then %> selected <% end if %>>7</option>
	<option <% if str2N = 8 then %> selected <% end if %>>8</option> 
	<option <% if str2N = 9 then %> selected <% end if %>>9</option> 
		</select></td>

   
  <td height="22" align="center" class="style7"  >
		<select name="N_TUE">
	<option <% if str3N = 0 then %> selected <% end if %>>0</option>
	<option <% if str3N = 1 then %> selected <% end if %>>1</option>
	<option <% if str3N = 2 then %> selected <% end if %>>2</option>
	<option <% if str3N = 3 then %> selected <% end if %>>3</option>
	<option <% if str3N = 4 then %> selected <% end if %>>4</option>
	<option <% if str3N = 5 then %> selected <% end if %>>5</option>
	<option <% if str3N = 6 then %> selected <% end if %>>6</option>
	<option <% if str3N = 7 then %> selected <% end if %>>7</option>
	<option <% if str3N = 8 then %> selected <% end if %>>8</option> 
	<option <% if str3N = 9 then %> selected <% end if %>>9</option> 
		</select></td>

 
  <td height="22" align="center" class="style7"  >
		<select name="N_WED">
	<option <% if str4N = 0 then %> selected <% end if %>>0</option>
	<option <% if str4N = 1 then %> selected <% end if %>>1</option>
	<option <% if str4N = 2 then %> selected <% end if %>>2</option>
	<option <% if str4N = 3 then %> selected <% end if %>>3</option>
	<option <% if str4N = 4 then %> selected <% end if %>>4</option>
	<option <% if str4N = 5 then %> selected <% end if %>>5</option>
	<option <% if str4N = 6 then %> selected <% end if %>>6</option>
	<option <% if str4N = 7 then %> selected <% end if %>>7</option>
	<option <% if str4N = 8 then %> selected <% end if %>>8</option> 
	<option <% if str4N = 9 then %> selected <% end if %>>9</option> 
		</select></td>
  <td height="22" align="center" class="style7"  >
		<select name="N_THU">
	<option <% if str5N = 0 then %> selected <% end if %>>0</option>
	<option <% if str5N = 1 then %> selected <% end if %>>1</option>
	<option <% if str5N = 2 then %> selected <% end if %>>2</option>
	<option <% if str5N = 3 then %> selected <% end if %>>3</option>
	<option <% if str5N = 4 then %> selected <% end if %>>4</option>
	<option <% if str5N = 5 then %> selected <% end if %>>5</option>
	<option <% if str5N = 6 then %> selected <% end if %>>6</option>
	<option <% if str5N = 7 then %> selected <% end if %>>7</option>
	<option <% if str5N = 8 then %> selected <% end if %>>8</option> 
	<option <% if str5N = 9 then %> selected <% end if %>>9</option> 
		</select></td>

 
  <td height="22" align="center" class="style7"   >
		<select name="N_FRI">
	<option <% if str6N = 0 then %> selected <% end if %>>0</option>
	<option <% if str6N = 1 then %> selected <% end if %>>1</option>
	<option <% if str6N = 2 then %> selected <% end if %>>2</option>
	<option <% if str6N = 3 then %> selected <% end if %>>3</option>
	<option <% if str6N = 4 then %> selected <% end if %>>4</option>
	<option <% if str6N = 5 then %> selected <% end if %>>5</option>
	<option <% if str6N = 6 then %> selected <% end if %>>6</option>
	<option <% if str6N = 7 then %> selected <% end if %>>7</option>
	<option <% if str6N = 8 then %> selected <% end if %>>8</option> 
	<option <% if str6N = 9 then %> selected <% end if %>>9</option>
		</select></td>

 
  <td height="22" align="center" class="style7" s  >
		<select name="N_SAT">
		<option <% if str7N = 0 then %> selected <% end if %>>0</option>
	<option <% if str7N = 1 then %> selected <% end if %>>1</option>
	<option <% if str7N = 2 then %> selected <% end if %>>2</option>
	<option <% if str7N = 3 then %> selected <% end if %>>3</option>
	<option <% if str7N = 4 then %> selected <% end if %>>4</option>
	<option <% if str7N = 5 then %> selected <% end if %>>5</option>
	<option <% if str7N = 6 then %> selected <% end if %>>6</option>
	<option <% if str7N = 7 then %> selected <% end if %>>7</option>
	<option <% if str7N = 8 then %> selected <% end if %>>8</option> 
	<option <% if str7N = 9 then %> selected <% end if %>>9</option> 
		</select></td>

 
 
  <td height="22" align="center" class="style7" s  >
		&nbsp;</td>

 
 
  <td height="22" align="center" class="style7" s  >
		&nbsp;</td>

 
 
  	</tr>
  
      <tr>
<td height="22" align="center" class="style7" colspan="10" >&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <% if strPost = "OK" Then %><a href="OT_Detail.asp?id=<%= strid %>">
Add Technician</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<span class="style8">&nbsp; (Note:&nbsp; Submit Needs prior to adding 
Technicians)<% end if %></span></td>
   
  	</tr>
 <% strsql = "Select tblOT.* from tblOT where OT_ID = " & strid
 
 	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		if not MyRec.eof then 
		While not MyRec.eof
		
 %>
     <tr>
<td height="22" align="center" class="style7" ><%= MyRec("EMP_name") %></td>
   
  <td height="22" align="center" class="style7" >
  <% if MyRec("Sun_Option") = "Y" then %>
  Yes
  <% elseif MyRec("Sun_Option") = "N" then %>
  No
  <% elseif MyRec("Sun_Option") = "F" then %>
  Forced
    <% elseif MyRec("Sun_Option") = "B" then %>
  Bumped

  <% end if %>
 
<% if MyRec("Sun_DN") = "D" then %>
&nbsp;&nbsp;Days
<% elseif MyRec("Sun_DN") = "N" then %>
&nbsp;&nbsp;Nights 
<% end if %>

</td>
   
  <td height="22" align="center" class="style7" >
  <% if MyRec("Mon_Option") = "Y" then %>
  Yes
  <% elseif MyRec("Mon_Option") = "N" then %>
  No
  <% elseif MyRec("Mon_Option") = "F" then %>
  Forced
      <% elseif MyRec("Sun_Option") = "B" then %>
  Bumped

  <% end if %>

	<% if MyRec("MOn_DN") = "D" then %>
&nbsp;&nbsp;Days
<% elseif MyRec("MOn_DN") = "N" then %>
&nbsp;&nbsp;Nights 
<% end if %>
</td>
   
  <td height="22" align="center" class="style7"  >
	  <% if MyRec("Tue_Option") = "Y" then %>
  Yes
  <% elseif MyRec("Tue_Option") = "N" then %>
  No
  <% elseif MyRec("Tue_Option") = "F" then %>
  Forced
      <% elseif MyRec("Sun_Option") = "B" then %>
  Bumped

  <% end if %>
	<% if MyRec("Tue_DN") = "D" then %>
&nbsp;&nbsp;Days
<% elseif MyRec("Tue_DN") = "N" then %>
&nbsp;&nbsp;Nights 
<% end if %>
</td>
 
  <td height="22" align="center" class="style7"  >
  <% if MyRec("Wed_Option") = "Y" then %>
  Yes
  <% elseif MyRec("Wed_Option") = "N" then %>
  No
  <% elseif MyRec("Wed_Option") = "F" then %>
  Forced
      <% elseif MyRec("Sun_Option") = "B" then %>
  Bumped
  <% end if %>

<% if MyRec("Wed_DN") = "D" then %>
&nbsp;&nbsp;Days
<% elseif MyRec("Wed_DN") = "N" then %>
&nbsp;&nbsp;Nights 
<% end if %>
</td>
 
  <td height="22" align="center" class="style7"  >
	  <% if MyRec("Thu_Option") = "Y" then %>
  Yes
  <% elseif MyRec("Thu_Option") = "N" then %>
  No
  <% elseif MyRec("Thu_Option") = "F" then %>
  Forced
      <% elseif MyRec("Sun_Option") = "B" then %>
  Bumped

  <% end if %>
	<% if MyRec("THU_DN") = "D" then %>
&nbsp;&nbsp;Days
<% elseif MyRec("THU_DN") = "N" then %>
&nbsp;&nbsp;Nights 
<% end if %>
</td>
 
  <td height="22" align="center" class="style7"   >
  <% if MyRec("Fri_Option") = "Y" then %>
  Yes
  <% elseif MyRec("Fri_Option") = "N" then %>
  No
  <% elseif MyRec("Fri_Option") = "F" then %>
  Forced
      <% elseif MyRec("Sun_Option") = "B" then %>
  Bumped

  <% end if %>

	<% if MyRec("FRI_DN") = "D" then %>
&nbsp;&nbsp;Days
<% elseif MyRec("FRI_DN") = "N" then %>
&nbsp;&nbsp;Nights 
<% end if %>
</td>
 
  <td height="22" align="center" class="style7" s  >
  <% if MyRec("Sat_Option") = "Y" then %>
  Yes
  <% elseif MyRec("Sat_Option") = "N" then %>
  No
  <% elseif MyRec("Sat_Option") = "F" then %>
  Forced
      <% elseif MyRec("Sun_Option") = "B" then %>
  Bumped

  <% end if %>

	<% if MyRec("SAT_DN") = "D" then %>
&nbsp;&nbsp;Days
<% elseif MyRec("SAT_DN") = "N" then %>
&nbsp;&nbsp;Nights 
<% end if %>
</td>
 
  <td height="22" align="center" class="style7" ><%= MyRec("PL_Emp_ID") %>
	&nbsp;</td>
 
  <td height="22" align="center" class="style7">
  <% if strPost = "OK" then %>
  <a href="OT_Edit.asp?id=<%= strid %>&d=<%= MyRec("ID") %>">Edit</a>
  <% end if %>&nbsp;</td>
 
  	</tr>
<% MyRec.movenext
wend
end if
MyRec.close %>
 
  </table>
</div>



</form>
   
  

</body>
 <%

  
  Function SaveData()

 strid = request.querystring("id")

 
  strsql = "Update tblOT_master set SUN_Day = " & Request.form("D_SUN") & ", MON_Day = " & Request.form("D_MON") & ", TUE_Day = " & Request.form("D_TUE") & ", "_
  &" WED_Day = " & Request.form("D_WED") & ", THU_Day = " & Request.form("D_THU") & ", FRI_Day = " & Request.form("D_FRI") & ", SAT_Day = " & Request.form("D_SAT") & ", "_
	&" SUN_Night = " & Request.form("N_SUN") & ", MON_Night = " & Request.form("N_MON") & ", TUE_Night = " & Request.form("N_TUE") & ", "_
  &" WED_Night = " & Request.form("N_WED") & ", THU_Night = " & Request.form("N_THU") & ", FRI_Night = " & Request.form("N_FRI") & ", SAT_Night = " & Request.form("N_SAT") & " "_
 
  &" where id = " & strid

   
  	 set MyRec = new ASP_CLS_DataAccess
        MyRec.ExecuteSql strSql 
         
       Response.write ("Updated")
  End Function
  
   %><!--#include file="footer.inc"-->