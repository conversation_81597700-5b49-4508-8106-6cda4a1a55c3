																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Transfer Trailers from Baldwin</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->



<% Dim MyRec, strsql

strsql = "SELECT tblCars.* FROM tblCars WHERE (((tblCars.Grade)<>'OCC') AND ((tblCars.Date_unloaded) Is Not Null) AND ((tblCars.Location)='Baldwin')) order by Trailer, Date_Unloaded "

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD width = 30% align = left><font size="2" face = arial><a href="TransferException.asp">Click Here 
	if Trailer Number is UNKNOWN</a>&nbsp;</font></td>
<td align = left><b>
<font face="arial" size="4" >&nbsp;&nbsp;&nbsp; Transfer Load from Baldwin to 
Yard</font></b></td>



</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=40% class = "tablecolor1" border=1 align = center>  
	 <tr class="tableheader">
<td>&nbsp;</td>
	<td> <font face="Arial" size="2"><b>Trailer</b></font></td>
		<td ><font face="Arial" size="1"><b>Date Unloaded</b></font></td>
		<td ><font face="Arial" size="1">Species</font></td>
    
      

	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td> <font size="1" face="Arial"><a href="TruckTransferEdit.asp?id=<%= MyRec.fields("CID") %>">Transfer</a></td>
	<td  >       <font size="2" face="Arial"><b>       <%= MyRec.fields("Trailer")%></font></b></td>
			<td> <font size="1" face="Arial">     <%= MyRec.fields("Date_Unloaded")%></font></td>
		<td  >        <font face="Arial" size="1">        <%= MyRec.fields("Species")%></font></td>
</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>


<!--#include file="Fiberfooter.inc"-->