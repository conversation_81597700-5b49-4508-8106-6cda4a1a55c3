																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Add Vendor </TITLE>


<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 



<% Dim MyRec, strsql, MyRec2, strsql2,  MyConn3, strsql3,  strSchedule_Agreement

strdate = formatdatetime(Now(),2)

  set objGeneral = new ASP_CLS_General
  if objGeneral.IsSubmit() Then	
  Dim strVendor
  

  strVendor = Replace(Request.form("Vendor"), "'", "''")	
  strGenerator= Replace(Request.form("Generator"), "'", "''")	
  strCity = Replace(Request.form("City"), "'", "''")
  strComments = Replace(Request.form("Comments"), "'", "''")	
  strTest = Replace(Request.form("Test"), "'", "''")		
        	
  	
	strsql =  "INSERT INTO tblTier (Vendor, Generator, City, State, Grade, Tier, Active, Comments, Test) "_
	&" SELECT  '" & strVendor & "', '" & strGenerator & "', '" & strCity & "', '" & request.form("State") & "', '" & request.form("Grade") & "', "_
	&" '" & Request.form("Tier") & "', '" & request.form("Active") & "', '" & strComments & "', '" & strTest & "'"
	
	
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
Response.redirect("RF_Vendor.asp")		
end if

	
%>

<style type="text/css">
.style3 {
	border: 1px solid #000000;
}
.style5 {
	border: 1px solid #C0C0C0;
	background-color: #FFFFD7;
}
.style6 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: arial;
	font-size: small;
	text-align: center;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style7 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	text-align: center;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style8 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
</style>
</head>

<body>
<br>
	
<form name="form1" action="RF_Vendor_Add.asp" method="post" ID="Form1">
<TABLE cellSpacing=0 cellPadding=0 width=100% align = center class="style5">

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Add Vendor</b></font></td>
<td align = right><font face="Arial"><a href="RF_Vendor.asp">Return</a>&nbsp;</td>


</tr>
	    </table>



	<p>&nbsp;</p>
	<table id="table1" style="width: 75%" align="center" class="style3">
<tr bgcolor = #CCCCFF>
		<td bgcolor="#FFFFD7" class="style6" colspan="2"><strong>Vendor</strong></td>
			<td bgcolor="#FFFFD7" class="style6"><strong>Generator</strong></td>
		<td bgcolor="#FFFFD7" class="style6"><strong>City</strong></td>
			<td bgcolor="#FFFFD7" class="style6"><strong>State</strong></td>
		<td bgcolor="#FFFFD7" class="style6"><strong>Grade</strong></td>
	</tr>
	<tr>
	
		<td class="style7" style="height: 75px" colspan="2">
		<input type="text" name="Vendor" size="20" style="width: 252px" tabindex="1" ></td>
		<td class="style7" style="height: 75px">
		<input type="text" name="Generator" size="20" style="width: 319px" tabindex="2" ></td>
		<td class="style7" style="height: 75px">
		<input type="text" name="City" size="20" tabindex="3" ></td>
		<td style="height: 75px" class="style8">
		<input type="text" name="State" size="20" style="width: 58px" tabindex="4" ></td>
	<td class="style7" style="height: 75px">
	<input type="text" name="Grade" size="20" style="width: 80px" tabindex="5"></td>
	</tr>
	<tr bgcolor = #CCCCFF>
		<td bgcolor="#FFFFD7" class="style6">Tier</td>
		<td bgcolor="#FFFFD7" class="style6">Test</td>
			<td bgcolor="#FFFFD7" class="style6">Comments</td>
		<td bgcolor="#FFFFD7" class="style6">&nbsp;</td>
			<td bgcolor="#FFFFD7" class="style6" colspan="2"><strong>Active</strong></td>
	</tr>
	<tr>
	
		<td class="style7" style="height: 75px">
		<input type="text" name="Tier" size="20" style="width: 54px" tabindex="6" ></td>
	
		<td class="style7" style="height: 75px">
		<input type="text" name="Test" size="10" style="width: 98px; height: 26px;" tabindex="7"  maxlength="10" ></td>
		<td class="style7" style="height: 75px" colspan="2">
		<input type="text" name="Comments" size="20" style="width: 571px" tabindex="7" ></td>
		<td style="height: 75px" colspan="2" class="style7">
		<select name="Active" tabindex="8">
		<option >Y</option>
		<option >N</option>
		</select>
		&nbsp;</td>
	</tr>

</table>
<br>
		

<p align="center"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></p>
</form>

<!--#include file="Fiberfooter.inc"-->