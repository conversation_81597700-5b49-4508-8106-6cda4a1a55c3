<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Yard Email</title>
</head>
<%
dim strbody, strCID, strECarrier, strETrailer, strELoad, strEDoc, strEDC, strEDCName, strEAdd, strECity, strEState
dim strECC, strEBCC, strESpecies, MyRec, strsql, MyConn2, strbody3, strbody1




dim  strCarrier, str<PERSON>railer,   strsql3, MyRec3, gTcount, str<PERSON><PERSON>, str<PERSON>oney, MyConn
dim strSpecies, strDateReceived, strVendor, strDetention, strDays, strdate, gcount, gSpecies, strNow, strEmailto, strEmailCC
dim gAvgdays, gExcessDays, gTotaldays, strExcess, strMTDDetention, gMTDAvgDays, gMTDTotalCount, gMTDTotalDays, gMTDExcess
dim strMTDDetentionR, gMTDAvgDaysR, gMTDTotalCountR, gMTDTotalDaysR, gMTDExcessR
Dim gCountR, strMoneyR, gAvgDaysR, gExcessR, gTcountR, strTMoneyR
strdate = formatdatetime(now(),2)

strtoday = Date()


strDay30 = FormatDateTime(DateAdd("d", -30, strtoday), 2)
strDay29 = FormatDateTime(DateAdd("d", -29, strtoday), 2)
strDay28 = FormatDateTime(DateAdd("d", -28, strtoday), 2)
strDay27 = FormatDateTime(DateAdd("d", -27, strtoday), 2)
strDay26 = FormatDateTime(DateAdd("d", -26, strtoday), 2)
strDay25 = FormatDateTime(DateAdd("d", -25, strtoday), 2)
strDay24 = FormatDateTime(DateAdd("d", -24, strtoday), 2)
strDay23 = FormatDateTime(DateAdd("d", -23, strtoday), 2)
strDay22 = FormatDateTime(DateAdd("d", -22, strtoday), 2)
strDay21 = FormatDateTime(DateAdd("d", -21, strtoday), 2)
strDay20 = FormatDateTime(DateAdd("d", -20, strtoday), 2)
strDay19 = FormatDateTime(DateAdd("d", -19, strtoday), 2)
strDay18 = FormatDateTime(DateAdd("d", -18, strtoday), 2)
strDay17 = FormatDateTime(DateAdd("d", -17, strtoday), 2)
strDay16 = FormatDateTime(DateAdd("d", -16, strtoday), 2)
strDay15 = FormatDateTime(DateAdd("d", -15, strtoday), 2)
strDay14 = FormatDateTime(DateAdd("d", -14, strtoday), 2)
strDay13 = FormatDateTime(DateAdd("d", -13, strtoday), 2)
strDay12 = FormatDateTime(DateAdd("d", -12, strtoday), 2)
strDay11 = FormatDateTime(DateAdd("d", -11, strtoday), 2)
strDay10 = FormatDateTime(DateAdd("d", -10, strtoday), 2)
strDay9 = FormatDateTime(DateAdd("d", -9, strtoday), 2)
strDay8 = FormatDateTime(DateAdd("d", -8, strtoday), 2)
strDay7 = FormatDateTime(DateAdd("d", -7, strtoday), 2)
strDay6 = FormatDateTime(DateAdd("d", -6, strtoday), 2)
strDay5 = FormatDateTime(DateAdd("d", -5, strtoday), 2)
strDay4 = FormatDateTime(DateAdd("d", -4, strtoday), 2)
strDay3 = FormatDateTime(DateAdd("d", -3, strtoday), 2)
strDay2 = FormatDateTime(DateAdd("d", -2, strtoday), 2)
strDay1 = FormatDateTime(DateAdd("d", -1, strtoday), 2)

STRSQL = "Select max(Inv_date) as LastDate from tblRailTarget"
Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strLastDate = MyRec("LastDate")
		end if
		MyRec.close
		
		strsql = "Select Target from tblRailTarget where ID = 1"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
			MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strTargetDefault = MyRec("Target")
		end if
		MyRec.close

	
		
strDiff = datediff("d", strLastdate, Date())
if strDiff = 1 then
strsql = "Insert into tblRailTarget (INV_Date, Target) Select '" & strtoday & "', " & strTargetDefault & ""
Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
end if

If strDiff = 2 then
		strYesterday = dateadd("d", -1, strToday)
strsql = "Insert into tblRailTarget (INV_Date, Target) Select '" & strYesterday & "', " & strTargetDefault & ""
Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			
strsql = "Insert into tblRailTarget (INV_Date, Target) Select '" & strtoday & "', " & strTargetDefault & ""
			MyConn.Execute strSQL
			MyConn.Close	
end if

If strDiff = 3 then
	strThree = dateadd("d", -2, strToday)
			strsql = "Insert into tblRailTarget (INV_Date, Target) Select '" & strThree & "', " & strTargetDefault & ""
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
		

		strYesterday = dateadd("d", -1, strToday)
			strsql = "Insert into tblRailTarget (INV_Date, Target) Select '" & strYesterday & "', " & strTargetDefault & ""
		
			MyConn.Execute strSQL
		
			
			strsql = "Insert into tblRailTarget (INV_Date, Target) Select '" & strtoday & "', " & strTargetDefault & ""
		
			MyConn.Execute strSQL
			MyConn.Close	
end if





strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay30 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay30t = MyRec.fields("Target")
		end if 
		
strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay29 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay29t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay28 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay28t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay27 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay27t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay26 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay26t = MyRec.fields("Target")
		end if
		 
strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay25 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay25t = MyRec.fields("Target")
		end if 
strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay24 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay24t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay23 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay23t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay22 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay22t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay21 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay21t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay20 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay20t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay19 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay19t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay18 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay18t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay17 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay17t = MyRec.fields("Target")
		end if 
strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay16 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay16t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay15 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay15t = MyRec.fields("Target")
		end if 
strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay14 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay14t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay13 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay13t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay12 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay12t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay11 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay11t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay10 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay10t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay9 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay9t = MyRec.fields("Target")
		end if 
		
strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay8 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay8t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay7 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay7t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay6 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay6t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay5 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay5t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay4 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay4t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay3 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay3t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay2 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay2t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay1 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay1t = MyRec.fields("Target")
		end if 

strDay30From = strDay30 & " " &  "00:00:01 AM" 
strDay30To = strDay30 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay30From & "' AND Date_unloaded <= '" & strDay30To & "'   AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay30c = MyRec.fields("CCID")
		end if 
		
strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay30To & "' and (Date_unloaded > '" & strDay30From & "' or Date_unloaded is Null) and (Species = 'PMX' or Species = 'KBLD' or Species = 'OF3' or Species = 'HBX') AND Carrier = 'RAIL' "
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay30A = MyRec("CCID")
		end if 


strDay29From = strDay29 & " " &  "00:00:01 AM" 
strDay29To = strDay29 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay29From & "' AND Date_unloaded <= '" & strDay29To & "'   AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay29c = MyRec.fields("CCID")
		end if 
		
		strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay29To & "' and (Date_unloaded > '" & strDay29From & "' or Date_unloaded is Null) and (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') "_
&" and (Carrier = 'RAIL' or carrier = 'RAIL')"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay29A = MyRec("CCID")
		end if

strDay28From = strDay28 & " " &  "00:00:01 AM" 
strDay28To = strDay28 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay28From & "' AND Date_unloaded <= '" & strDay28To & "'   AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay28c = MyRec.fields("CCID")
		end if 
		
				strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay28To & "' and (Date_unloaded > '" & strDay28From & "' or Date_unloaded is Null) and (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') "_
&" and (Carrier = 'RAIL' or carrier = 'RAIL')"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay28A = MyRec("CCID")
		end if


strDay27From = strDay27 & " " &  "00:00:01 AM" 
strDay27To = strDay27 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay27From & "' AND Date_unloaded <= '" & strDay27To & "'   AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay27c = MyRec.fields("CCID")
		end if 
		
				strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay27To & "' and (Date_unloaded > '" & strDay27From & "' or Date_unloaded is Null) and (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') "_
&" and (Carrier = 'RAIL' or carrier = 'RAIL')"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay27A = MyRec("CCID")
		end if


strDay26From = strDay26 & " " &  "00:00:01 AM" 
strDay26To = strDay26 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay26From & "' AND Date_unloaded <= '" & strDay26To & "'   AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay26c = MyRec.fields("CCID")
		end if 
		
				strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay26To & "' and (Date_unloaded > '" & strDay26From & "' or Date_unloaded is Null) and (Species = 'PMX' or Species = 'HBX'  or Species = 'KBLD' or Species = 'OF3') "_
&" and (Carrier = 'RAIL' or carrier = 'RAIL')"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay26A = MyRec("CCID")
		end if


strDay25From = strDay25 & " " &  "00:00:01 AM" 
strDay25To = strDay25 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay25From & "' AND Date_unloaded <= '" & strDay25To & "'   AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay25c = MyRec.fields("CCID")
		end if 
		
				strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay25To & "' and (Date_unloaded > '" & strDay25From & "' or Date_unloaded is Null) and (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') "_
&" and (Carrier = 'RAIL' or carrier = 'RAIL')"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay25A = MyRec("CCID")
		end if


strDay24From = strDay24 & " " &  "00:00:01 AM" 
strDay24To = strDay24 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay24From & "' AND Date_unloaded <= '" & strDay24To & "'   AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay24c = MyRec.fields("CCID")
		end if 
		
				strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay24To & "' and (Date_unloaded > '" & strDay24From & "' or Date_unloaded is Null) and (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') "_
&" and (Carrier = 'RAIL' or carrier = 'RAIL')"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay24A = MyRec("CCID")
		end if


strDay23From = strDay23 & " " &  "00:00:01 AM" 
strDay23To = strDay23 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay23From & "' AND Date_unloaded <= '" & strDay23To & "'   AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay23c = MyRec.fields("CCID")
		end if 
		
				strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay23To & "' and (Date_unloaded > '" & strDay23From & "' or Date_unloaded is Null) and (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') "_
&" and (Carrier = 'RAIL' or carrier = 'RAIL')"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay23A = MyRec("CCID")
		end if


strDay22From = strDay22 & " " &  "00:00:01 AM" 
strDay22To = strDay22 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay22From & "' AND Date_unloaded <= '" & strDay22To & "'  AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay22c = MyRec.fields("CCID")
		end if 
		
				strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay22To & "' and (Date_unloaded > '" & strDay22From & "' or Date_unloaded is Null) and (Species = 'PMX' or Species = 'KBLD' or Species = 'HBX' or Species = 'OF3') "_
&" and (Carrier = 'RAIL' or carrier = 'RAIL')"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay22A = MyRec("CCID")
		end if


strDay21From = strDay21 & " " &  "00:00:01 AM" 
strDay21To = strDay21 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay21From & "' AND Date_unloaded <= '" & strDay21To & "'   AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay21c = MyRec.fields("CCID")
		end if 
		
				strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay21To & "' and (Date_unloaded > '" & strDay21From & "' or Date_unloaded is Null) and (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') "_
&" and (Carrier = 'RAIL' or carrier = 'RAIL')"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay21A = MyRec("CCID")
		end if


strDay20From = strDay20 & " " &  "00:00:01 AM" 
strDay20To = strDay20 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay20From & "' AND Date_unloaded <= '" & strDay20To & "'   AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay20c = MyRec.fields("CCID")
		end if 
		
				strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay20To & "' and (Date_unloaded > '" & strDay20From & "' or Date_unloaded is Null) and (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') "_
&" and (Carrier = 'RAIL' or carrier = 'RAIL')"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay20A = MyRec("CCID")
		end if


strDay19From = strDay19 & " " &  "00:00:01 AM" 
strDay19To = strDay19 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay19From & "' AND Date_unloaded <= '" & strDay19To & "'   AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay19c = MyRec.fields("CCID")
		end if 
		
				strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay19To & "' and (Date_unloaded > '" & strDay19From & "' or Date_unloaded is Null) and (Species = 'PMX' or Species = 'KBLD' or Species = 'HBX' or Species = 'OF3') "_
&" and (Carrier = 'RAIL' or carrier = 'RAIL')"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay19A = MyRec("CCID")
		end if


strDay18From = strDay18 & " " &  "00:00:01 AM" 
strDay18To = strDay18 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay18From & "' AND Date_unloaded <= '" & strDay18To & "'  AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay18c = MyRec.fields("CCID")
		end if 
		
				strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay18To & "' and (Date_unloaded > '" & strDay18From & "' or Date_unloaded is Null) and (Species = 'PMX' or Species = 'KBLD' or Species = 'HBX' or Species = 'OF3') "_
&" and (Carrier = 'RAIL' or carrier = 'RAIL')"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay18A = MyRec("CCID")
		end if


strDay17From = strDay17 & " " &  "00:00:01 AM" 
strDay17To = strDay17 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay17From & "' AND Date_unloaded <= '" & strDay17To & "' AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay17c = MyRec.fields("CCID")
		end if 
		
				strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay17To & "' and (Date_unloaded > '" & strDay17From & "' or Date_unloaded is Null) and (Species = 'PMX' or Species = 'KBLD' or Species = 'HBX' or Species = 'OF3') "_
&" and (Carrier = 'RAIL' or carrier = 'RAIL')"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay17A = MyRec("CCID")
		end if


strDay16From = strDay16 & " " &  "00:00:01 AM" 
strDay16To = strDay16 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay16From & "' AND Date_unloaded <= '" & strDay16To & "' AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay16c = MyRec.fields("CCID")
		end if 
				strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay16To & "' and (Date_unloaded > '" & strDay16From & "' or Date_unloaded is Null) and (Species = 'PMX' or Species = 'KBLD' or Species = 'HBX' or Species = 'OF3') "_
&" and (Carrier = 'RAIL' or carrier = 'RAIL')"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay16A = MyRec("CCID")
		end if


strDay15From = strDay15 & " " &  "00:00:01 AM" 
strDay15To = strDay15 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay15From & "' AND Date_unloaded <= '" & strDay15To & "' AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay15c = MyRec.fields("CCID")
		end if 
		
				strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay15To & "' and (Date_unloaded > '" & strDay15From & "' or Date_unloaded is Null) and (Species = 'PMX'  or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') "_
&" and (Carrier = 'RAIL' or carrier = 'RAIL')"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay15A = MyRec("CCID")
		end if


strDay14From = strDay14 & " " &  "00:00:01 AM" 
strDay14To = strDay14 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay14From & "' AND Date_unloaded <= '" & strDay14To & "' AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay14c = MyRec.fields("CCID")
		end if 
		
				strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay14To & "' and (Date_unloaded > '" & strDay14From & "' or Date_unloaded is Null) and (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') "_
&" and (Carrier = 'RAIL' or carrier = 'RAIL')"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay14A = MyRec("CCID")
		end if


strDay13From = strDay13 & " " &  "00:00:01 AM" 
strDay13To = strDay13 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay13From & "' AND Date_unloaded <= '" & strDay13To & "' AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay13c = MyRec.fields("CCID")
		end if 
		
				strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay13To & "' and (Date_unloaded > '" & strDay13From & "' or Date_unloaded is Null) and (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') "_
&" and (Carrier = 'RAIL' or carrier = 'RAIL')"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay13A = MyRec("CCID")
		end if


strDay12From = strDay12 & " " &  "00:00:01 AM" 
strDay12To = strDay12 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay12From & "' AND Date_unloaded <= '" & strDay12To & "' AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay12c = MyRec.fields("CCID")
		end if 
		
				strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay12To & "' and (Date_unloaded > '" & strDay12From & "' or Date_unloaded is Null) and (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') "_
&" and (Carrier = 'RAIL' or carrier = 'RAIL')"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay12A = MyRec("CCID")
		end if


strDay11From = strDay11 & " " &  "00:00:01 AM" 
strDay11To = strDay11 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay11From & "' AND Date_unloaded <= '" & strDay11To & "' AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay11c = MyRec.fields("CCID")
		end if 
		
				strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay11To & "' and (Date_unloaded > '" & strDay11From & "' or Date_unloaded is Null) and (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') "_
&" and (Carrier = 'RAIL' or carrier = 'RAIL')"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay11A = MyRec("CCID")
		end if


strDay10From = strDay10 & " " &  "00:00:01 AM" 
strDay10To = strDay10 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay10From & "' AND Date_unloaded <= '" & strDay10To & "' AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay10c = MyRec.fields("CCID")
		end if 
		
				strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay10To & "' and (Date_unloaded > '" & strDay10From & "' or Date_unloaded is Null) and (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') "_
&" and (Carrier = 'RAIL' or carrier = 'RAIL')"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay10A = MyRec("CCID")
		end if


strDay9From = strDay9 & " " &  "00:00:01 AM" 
strDay9To = strDay9 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay9From & "' AND Date_unloaded <= '" & strDay9To & "' AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay9c = MyRec.fields("CCID")
		end if 
		
				strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay9To & "' and (Date_unloaded > '" & strDay9From & "' or Date_unloaded is Null) and (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') "_
&" and (Carrier = 'RAIL' or carrier = 'RAIL')"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay9A = MyRec("CCID")
		end if


strDay8From = strDay8 & " " &  "00:00:01 AM" 
strDay8To = strDay8 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay8From & "' AND Date_unloaded <= '" & strDay8To & "' AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay8c = MyRec.fields("CCID")
		end if 
		
						strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay8To & "' and (Date_unloaded > '" & strDay8From & "' or Date_unloaded is Null) and (Species = 'PMX'  or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') "_
&" and (Carrier = 'RAIL' or carrier = 'RAIL')"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay8A = MyRec("CCID")
		end if


strDay7From = strDay7 & " " &  "00:00:01 AM" 
strDay7To = strDay7 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay7From & "' AND Date_unloaded <= '" & strDay7To & "' AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay7c = MyRec.fields("CCID")
		end if 
		
						strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay7To & "' and (Date_unloaded >= '" & strDay7From & "' or Date_unloaded is Null) and (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') "_
&" and (Carrier = 'RAIL' or carrier = 'RAIL')"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay7A = MyRec("CCID")
		end if


strDay6From = strDay6 & " " &  "00:00:01 AM" 
strDay6To = strDay6 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay6From & "' AND Date_unloaded <= '" & strDay6To & "' AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay6c = MyRec.fields("CCID")
		end if 
		
						strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay6To & "' and (Date_unloaded >= '" & strDay6From & "' or Date_unloaded is Null) and (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') "_
&" and (Carrier = 'RAIL' or carrier = 'RAIL')"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay6A = MyRec("CCID")
		end if


strDay5From = strDay5 & " " &  "00:00:01 AM" 
strDay5To = strDay5 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay5From & "' AND Date_unloaded <= '" & strDay5To & "' AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay5c = MyRec.fields("CCID")
		end if 
		
						strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay5To & "' and (Date_unloaded >= '" & strDay5From & "' or Date_unloaded is Null) and (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') "_
&" and (Carrier = 'RAIL' or carrier = 'RAIL')"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay5A = MyRec("CCID")
		end if


strDay4From = strDay4 & " " &  "00:00:01 AM" 
strDay4To = strDay4 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay4From & "' AND Date_unloaded <= '" & strDay4To & "' AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay4c = MyRec.fields("CCID")
		end if 
		
						strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay4To & "' and (Date_unloaded >= '" & strDay4From & "' or Date_unloaded is Null) and (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') "_
&" and (Carrier = 'RAIL' or carrier = 'RAIL')"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay4A = MyRec("CCID")
		end if


strDay3From = strDay3 & " " &  "00:00:01 AM" 
strDay3To = strDay3 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay3From & "' AND Date_unloaded <= '" & strDay3To & "' AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay3c = MyRec.fields("CCID")
		end if 
		
						strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay3To & "' and (Date_unloaded >= '" & strDay3From & "'or Date_unloaded is Null) and (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') "_
&" and (Carrier = 'RAIL' or carrier = 'RAIL')"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay3A = MyRec("CCID")
		end if


strDay2From = strDay2 & " " &  "00:00:01 AM" 
strDay2To = strDay2 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay2From & "' AND Date_unloaded <= '" & strDay2To & "' AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay2c = MyRec.fields("CCID")
		end if 
		
						strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay2To & "' and (Date_unloaded >= '" & strDay2From & "'or Date_unloaded is Null) and (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') "_
&" and (Carrier = 'RAIL' or carrier = 'RAIL')"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay2A = MyRec("CCID")
		end if


strDay1From = strDay1 & " " &  "00:00:01 AM" 
strDay1To = strDay1 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay1From & "' AND Date_unloaded <= '" & strDay1To & "'AND  (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND Carrier = 'RAIL' and Location = 'RF'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay1c = MyRec.fields("CCID")
		end if 
		MyRec.close
		
						strsql = "SELECT Count(tblCars.CID) AS CCID FROM tblCars "_
&"  where Date_received <= '" & strDay1To & "' and (Date_unloaded >= '" & strDay1From & "' or Date_unloaded is Null) and (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') "_
&" and (Carrier = 'RAIL' or carrier = 'RAIL')"
	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay1A = MyRec("CCID")
		end if


strDay30g = strDay30c - strDay30t
strDay29g = strDay29c - strDay29t
strDay28g = strDay28c - strDay28t
strDay27g = strDay27c - strDay27t
strDay26g = strDay26c - strDay26t
strDay25g = strDay25c - strDay25t
strDay24g = strDay24c - strDay24t
strDay23g = strDay23c - strDay23t
strDay22g = strDay22c - strDay22t
strDay21g = strDay21c - strDay21t
strDay20g = strDay20c - strDay20t
strDay19g = strDay19c - strDay19t
strDay18g = strDay18c - strDay18t
strDay17g = strDay17c - strDay17t
strDay16g = strDay16c - strDay16t
strDay15g = strDay15c - strDay15t
strDay14g = strDay14c - strDay14t
strDay13g = strDay13c - strDay13t
strDay12g = strDay12c - strDay12t
strDay11g = strDay11c - strDay11t
strDay10g = strDay10c - strDay10t
strDay9g = strDay9c - strDay9t
strDay8g = strDay8c - strDay8t
strDay7g = strDay7c - strDay7t
strDay6g = strDay6c - strDay6t
strDay5g = strDay5c - strDay5t
strDay4g = strDay4c - strDay4t
strDay3g = strDay3c - strDay3t
strDay2g = strDay2c - strDay2t
strDay1g = strDay1c - strDay1t
strDay30rg = strDay30g
strDay29rg = strDay30rg + strDay29g
strDay28rg = strDay29rg + strDay28g
strDay27rg = strDay28rg + strDay27g
strDay26rg = strDay27rg + strDay26g
strDay25rg = strDay26rg + strDay25g
strDay24rg = strDay25rg + strDay24g
strDay23rg = strDay24rg + strDay23g
strDay22rg = strDay23rg + strDay22g
strDay21rg = strDay22rg + strDay21g
strDay20rg = strDay21rg + strDay20g
strDay19rg = strDay20rg + strDay19g
strDay18rg = strDay19rg + strDay18g
strDay17rg = strDay18rg + strDay17g
strDay16rg = strDay17rg + strDay16g
strDay15rg = strDay16rg + strDay15g
strDay14rg = strDay15rg + strDay14g
strDay13rg = strDay14rg + strDay13g
strDay12rg = strDay13rg + strDay12g
strDay11rg = strDay12rg + strDay11g
strDay10rg = strDay11rg + strDay10g
strDay9rg = strDay10rg + strDay9g
strDay8rg = strDay9rg + strDay8g
strDay7rg = strDay8rg + strDay7g
strDay6rg = strDay7rg + strDay6g
strDay5rg = strDay6rg + strDay5g
strDay4rg = strDay5rg + strDay4g
strDay3rg = strDay4rg + strDay3g
strDay2rg = strDay3rg + strDay2g
strDay1rg = strDay2rg + strDay1g


gcount = 0
gcountR = 0
gtcountR = 0
gtcount = 0
gSpecies = ""
strMoney = 0
strMoneyR = 0
strTMoney = 0
strTMoneyR = 0
gAvgdays = 0
gAvgdaysR = 0
gExcessDays = 0
gExcessDaysR = 0
gTotaldays = 0
strMTDDetention = 0
gMTDAvgDays = 0
gMTDTotalCount = 0
gMTDTotalDays = 0
gMTDExcess = 0
strMTDDetentionR = 0
gMTDAvgDaysR = 0
gMTDTotalCountR = 0
gMTDTotalDaysR = 0
gMTDExcessR = 0

strsql = "SELECT tblCars.*,  [date_received] AS Sort_Date FROM tblCars WHERE (((tblCars.Date_received) Is Not Null) AND "_
&"  ((tblCars.Location)='Yard') AND ((tblCars.Trailer) Is Not Null)) and (Carrier = 'Rail' or Carrier = 'RAIL') and (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') ORDER BY tblCars.Species, Sort_Date "   
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

 Dim ii
       ii = 0
		
         strEmailTo = "<EMAIL>"
          strEmailCC = "<EMAIL>"          
			'strEmailTo = "<EMAIL>"   
           
			Set objMail = Server.CreateObject("CDO.Message")		
			objMail.From = "<EMAIL>"
			objMail.To = strEmailTo	
		objMail.BCC = strEmailCC
	
			objMail.Subject = "RFF Rail Unload Report"
			
	strBody1 = "<br><table border=1  cellpadding=0 cellspacing=0 width=500><tr><td align = left><font face = arial size = 1>Date&nbsp; </td><td align = left><font face = arial size = 1>RF Cars Unloaded&nbsp; </td><td align = left><font face = arial size = 1> Target </td><td align = left><font face = arial size = 1>Gap&nbsp;</td><td align = left><font face = arial size = 1>Rolling 30 day GAP&nbsp;</td><td align = left><font face = arial size = 1>Available</td></tr>"
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay1 & "</td><td><font face = arial size = 1>&nbsp;" & strDay1c & " </td><td><font face = arial size = 1>&nbsp;" & strDay1t & "</td><td><font face = arial size = 1>&nbsp;" & strDay1g & " </td><td><font face = arial size = 1>&nbsp;" & strDay1rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay1A & " </td></tr>"			
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay2 & "</td><td><font face = arial size = 1>&nbsp;" & strDay2c & " </td><td><font face = arial size = 1>&nbsp;" & strDay2t & "</td><td><font face = arial size = 1>&nbsp;" & strDay2g & " </td><td><font face = arial size = 1>&nbsp;" & strDay2rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay2A & " </td></tr>"			
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay3 & "</td><td><font face = arial size = 1>&nbsp;" & strDay3c & " </td><td><font face = arial size = 1>&nbsp;" & strDay3t & "</td><td><font face = arial size = 1>&nbsp;" & strDay3g & " </td><td><font face = arial size = 1>&nbsp;" & strDay3rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay3A & " </td></tr>"			
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay4 & "</td><td><font face = arial size = 1>&nbsp;" & strDay4c & " </td><td><font face = arial size = 1>&nbsp;" & strDay4t & "</td><td><font face = arial size = 1>&nbsp;" & strDay4g & " </td><td><font face = arial size = 1>&nbsp;" & strDay4rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay4A & " </td></tr>"			
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay5 & "</td><td><font face = arial size = 1>&nbsp;" & strDay5c & " </td><td><font face = arial size = 1>&nbsp;" & strDay5t & "</td><td><font face = arial size = 1>&nbsp;" & strDay5g & " </td><td><font face = arial size = 1>&nbsp;" & strDay5rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay5A & " </td></tr>"			
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay6 & "</td><td><font face = arial size = 1>&nbsp;" & strDay6c & " </td><td><font face = arial size = 1>&nbsp;" & strDay6t & "</td><td><font face = arial size = 1>&nbsp;" & strDay6g & " </td><td><font face = arial size = 1>&nbsp;" & strDay6rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay6A & " </td></tr>"			
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay7 & "</td><td><font face = arial size = 1>&nbsp;" & strDay7c & " </td><td><font face = arial size = 1>&nbsp;" & strDay7t & "</td><td><font face = arial size = 1>&nbsp;" & strDay7g & " </td><td><font face = arial size = 1>&nbsp;" & strDay7rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay7A & " </td></tr>"			
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay8 & "</td><td><font face = arial size = 1>&nbsp;" & strDay8c & " </td><td><font face = arial size = 1>&nbsp;" & strDay8t & "</td><td><font face = arial size = 1>&nbsp;" & strDay8g & " </td><td><font face = arial size = 1>&nbsp;" & strDay8rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay8A & " </td></tr>"		
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay9 & "</td><td><font face = arial size = 1>&nbsp;" & strDay9c & " </td><td><font face = arial size = 1>&nbsp;" & strDay9t & "</td><td><font face = arial size = 1>&nbsp;" & strDay9g & " </td><td><font face = arial size = 1>&nbsp;" & strDay9rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay9A & " </td></tr>"			
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay10 & "</td><td><font face = arial size = 1>&nbsp;" & strDay10c & " </td><td><font face = arial size = 1>&nbsp;" & strDay10t & "</td><td><font face = arial size = 1>&nbsp;" & strDay10g & " </td><td><font face = arial size = 1>&nbsp;" & strDay10rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay10A & " </td></tr>"			
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay11 & "</td><td><font face = arial size = 1>&nbsp;" & strDay11c & " </td><td><font face = arial size = 1>&nbsp;" & strDay11t & "</td><td><font face = arial size = 1>&nbsp;" & strDay11g & " </td><td><font face = arial size = 1>&nbsp;" & strDay11rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay11A & " </td></tr>"			
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay12 & "</td><td><font face = arial size = 1>&nbsp;" & strDay12c & " </td><td><font face = arial size = 1>&nbsp;" & strDay12t & "</td><td><font face = arial size = 1>&nbsp;" & strDay12g & " </td><td><font face = arial size = 1>&nbsp;" & strDay12rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay12A & " </td></tr>"			
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay13 & "</td><td><font face = arial size = 1>&nbsp;" & strDay13c & " </td><td><font face = arial size = 1>&nbsp;" & strDay13t & "</td><td><font face = arial size = 1>&nbsp;" & strDay13g & " </td><td><font face = arial size = 1>&nbsp;" & strDay13rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay13A & " </td></tr>"			
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay14 & "</td><td><font face = arial size = 1>&nbsp;" & strDay14c & " </td><td><font face = arial size = 1>&nbsp;" & strDay14t & "</td><td><font face = arial size = 1>&nbsp;" & strDay14g & " </td><td><font face = arial size = 1>&nbsp;" & strDay14rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay14A & " </td></tr>"			
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay15 & "</td><td><font face = arial size = 1>&nbsp;" & strDay15c & " </td><td><font face = arial size = 1>&nbsp;" & strDay15t & "</td><td><font face = arial size = 1>&nbsp;" & strDay15g & " </td><td><font face = arial size = 1>&nbsp;" & strDay15rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay15A & " </td></tr>"			
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay16 & "</td><td><font face = arial size = 1>&nbsp;" & strDay16c & " </td><td><font face = arial size = 1>&nbsp;" & strDay16t & "</td><td><font face = arial size = 1>&nbsp;" & strDay16g & " </td><td><font face = arial size = 1>&nbsp;" & strDay16rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay16A & " </td></tr>"			
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay17 & "</td><td><font face = arial size = 1>&nbsp;" & strDay17c & " </td><td><font face = arial size = 1>&nbsp;" & strDay17t & "</td><td><font face = arial size = 1>&nbsp;" & strDay17g & " </td><td><font face = arial size = 1>&nbsp;" & strDay17rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay17A & " </td></tr>"			
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay18 & "</td><td><font face = arial size = 1>&nbsp;" & strDay18c & " </td><td><font face = arial size = 1>&nbsp;" & strDay18t & "</td><td><font face = arial size = 1>&nbsp;" & strDay18g & " </td><td><font face = arial size = 1>&nbsp;" & strDay18rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay18A & " </td></tr>"			
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay19 & "</td><td><font face = arial size = 1>&nbsp;" & strDay19c & " </td><td><font face = arial size = 1>&nbsp;" & strDay19t & "</td><td><font face = arial size = 1>&nbsp;" & strDay19g & " </td><td><font face = arial size = 1>&nbsp;" & strDay19rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay19A & " </td></tr>"			
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay20 & "</td><td><font face = arial size = 1>&nbsp;" & strDay20c & " </td><td><font face = arial size = 1>&nbsp;" & strDay20t & "</td><td><font face = arial size = 1>&nbsp;" & strDay20g & " </td><td><font face = arial size = 1>&nbsp;" & strDay20rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay20A & " </td></tr>"			
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay21 & "</td><td><font face = arial size = 1>&nbsp;" & strDay21c & " </td><td><font face = arial size = 1>&nbsp;" & strDay21t & "</td><td><font face = arial size = 1>&nbsp;" & strDay21g & " </td><td><font face = arial size = 1>&nbsp;" & strDay21rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay21A & " </td></tr>"			
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay22 & "</td><td><font face = arial size = 1>&nbsp;" & strDay22c & " </td><td><font face = arial size = 1>&nbsp;" & strDay22t & "</td><td><font face = arial size = 1>&nbsp;" & strDay22g & " </td><td><font face = arial size = 1>&nbsp;" & strDay22rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay22A & " </td></tr>"			
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay23 & "</td><td><font face = arial size = 1>&nbsp;" & strDay23c & " </td><td><font face = arial size = 1>&nbsp;" & strDay23t & "</td><td><font face = arial size = 1>&nbsp;" & strDay23g & " </td><td><font face = arial size = 1>&nbsp;" & strDay23rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay23A & " </td></tr>"			
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay24 & "</td><td><font face = arial size = 1>&nbsp;" & strDay24c & " </td><td><font face = arial size = 1>&nbsp;" & strDay24t & "</td><td><font face = arial size = 1>&nbsp;" & strDay24g & " </td><td><font face = arial size = 1>&nbsp;" & strDay24rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay24A & " </td></tr>"			
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay25 & "</td><td><font face = arial size = 1>&nbsp;" & strDay25c & " </td><td><font face = arial size = 1>&nbsp;" & strDay25t & "</td><td><font face = arial size = 1>&nbsp;" & strDay25g & " </td><td><font face = arial size = 1>&nbsp;" & strDay25rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay25A & " </td></tr>"			
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay26 & "</td><td><font face = arial size = 1>&nbsp;" & strDay26c & " </td><td><font face = arial size = 1>&nbsp;" & strDay26t & "</td><td><font face = arial size = 1>&nbsp;" & strDay26g & " </td><td><font face = arial size = 1>&nbsp;" & strDay26rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay26A & " </td></tr>"			
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay27 & "</td><td><font face = arial size = 1>&nbsp;" & strDay27c & " </td><td><font face = arial size = 1>&nbsp;" & strDay27t & "</td><td><font face = arial size = 1>&nbsp;" & strDay27g & " </td><td><font face = arial size = 1>&nbsp;" & strDay27rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay27A & " </td></tr>"			
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay28 & "</td><td><font face = arial size = 1>&nbsp;" & strDay28c & " </td><td><font face = arial size = 1>&nbsp;" & strDay28t & "</td><td><font face = arial size = 1>&nbsp;" & strDay28g & " </td><td><font face = arial size = 1>&nbsp;" & strDay28rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay28A & " </td></tr>"			
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay29 & "</td><td><font face = arial size = 1>&nbsp;" & strDay29c & " </td><td><font face = arial size = 1>&nbsp;" & strDay29t & "</td><td><font face = arial size = 1>&nbsp;" & strDay29g & " </td><td><font face = arial size = 1>&nbsp;" & strDay29rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay29A & " </td></tr>"		
	strBody1 = strBody1 & "<tr><td><font face = arial size = 1>&nbsp;" & strDay30 & "</td><td><font face = arial size = 1>&nbsp;" & strDay30c & " </td><td><font face = arial size = 1>&nbsp;" & strDay30t & "</td><td><font face = arial size = 1>&nbsp;" & strDay30g & " </td><td><font face = arial size = 1>&nbsp;" & strDay30rg & " </td><td><font face = arial size = 1>&nbsp;" & strDay30A & " </td></tr></table><br>"			
	
	

	
			
			
			
			
			
			strBody2 = "<table border=0  cellpadding=0 cellspacing=0 width=800><tr><td align = left><font face = arial size = 2>Species&nbsp; </td><td align = left><font face = arial size = 2>Date Received&nbsp; </td><td align = left><font face = arial size = 2> Trailer </td><td align = left><font face = arial size = 2>Carrier&nbsp;</td><td align = left><font face = arial size = 2>Vendor&nbsp;</td><td align = left><font face = arial size = 2>To-Date<br> Detention</td><td align = left><font face = arial size = 2>Days in<br>Yard</td></tr>"
			
				While not MyRec.EOF
			'On error Resume Next

    



     strCarrier = MyRec.fields("Carrier") 
     strTrailer = MyRec.fields("Trailer") 
     strSpecies = MyRec.fields("Species") 
     strDateReceived = MyRec.fields("Date_received")
     strVendor = MyRec.fields("Vendor") 
       strRejected = MyRec.fields("Rejected")  
        
    

        if strRejected = "YES" then
        strRejected = "Rejected"
        end if    
      
        
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' and Carrier = 'Rail' and (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3')  "
   
                  strDays = fix(Now()- cdate(MyRec.fields("Date_received")))
                  
           
                  
  
         strdays = int(strdays)
          if strdays = 0 then
          strDetention = 0
          elseif strdays = 1 then
          strDetention = 26.52
          elseif strdays = 2 then
          strDetention = 53.04
          
          elseIf strdays = 3 then 
          strDetention = 79.56
          elseif strdays = 4 then
          strDetention = 106.08
          elseif strdays = 5 then
          strDetention = 145.33
          elseif strdays = 6 then
          strDetention = 184.58
          elseif strdays = 7 then
          strDetention = 262.03
          elseif strdays = 8 then 
          strDetention = 339.48
          elseif strdays > 8 then
          strDetention = 339.48 + (77.45*(strdays-8))
          end if
    	if strdays  > 2 then 
    	strExcess = 1
  			else            
       		
			strExcess = 0
			 end if 
	 
If left(strTrailer, 4) = "GACX" then
strDetention = 0
strExcess = 0
end if         
	
if UCase(MyRec.fields("Species")) = Ucase(gSpecies) or gcount = 0 then	
	if strdays < 6 or left(MyRec("Species"),6) = "FIBRIA" Then	
 	strbody2 = strbody2 & " <tr bgcolor=white><td align = left><font face = arial size = 1> " & strSpecies &  "</td><td align = left><font face = arial size = 1>" & strDateReceived & "</td><td align = left><font face = arial size = 1>"  & strTrailer & "</td><td align = left><font face = arial size = 1>"  & strCarrier & "</td><td align = left><font face = arial size = 1>" & strVendor & "</td><td align = left><font face = arial size = 1>" & strDetention & " " & strRejected & " </td><td align = left><font face = arial size = 1>" & strDays & " </td></tr> "
	elseif strdays > 5 and strdays < 9 then
	 strbody2 = strbody2 & " <tr bgcolor=#FFFF99><td align = left><font face = arial size = 1> " & strSpecies &  "</td><td align = left><font face = arial size = 1>" & strDateReceived & "</td><td align = left><font face = arial size = 1>"  & strTrailer & "</td><td align = left><font face = arial size = 1>"  & strCarrier & "</td><td align = left><font face = arial size = 1>" & strVendor & "</td><td align = left><font face = arial size = 1>" & strDetention & " " & strRejected & "</td><td align = left><font face = arial size = 1>" & strDays & " </td></tr> "
	else
	 strbody2 = strbody2 & " <tr bgcolor=#FCDCE6><td align = left><font face = arial size = 1> " & strSpecies &  "</td><td align = left><font face = arial size = 1>" & strDateReceived & "</td><td align = left><font face = arial size = 1>"  & strTrailer & "</td><td align = left><font face = arial size = 1>"  & strCarrier & "</td><td align = left><font face = arial size = 1>" & strVendor & "</td><td align = left><font face = arial size = 1>" & strDetention & " " & strRejected & "</td><td align = left><font face = arial size = 1>" & strDays & " </td></tr> "

	end if 

else

	strBody2 = strbody2 & "<font face = arial size = 1> Total " & gSpecies & ": " & gcount
 	if strdays < 6 or left(MyRec("Species"),6) = "FIBRIA" Then	
 	strbody2 = strbody2 & " <tr bgcolor=white><td align = left><font face = arial size = 1> " & strSpecies &  "</td><td align = left><font face = arial size = 1>" & strDateReceived & "</td><td align = left><font face = arial size = 1>"  & strTrailer & "</td><td align = left><font face = arial size = 1>"  & strCarrier & "</td><td align = left><font face = arial size = 1>" & strVendor & "</td><td align = left><font face = arial size = 1>" & strDetention & " " & strRejected & " </td><td align = left><font face = arial size = 1>" & strDays & " </td></tr> "
	elseif strdays > 5 and strdays < 9 then
 	strbody2 = strbody2 & " <tr bgcolor=#FFFF99><td align = left><font face = arial size = 1> " & strSpecies &  "</td><td align = left><font face = arial size = 1>" & strDateReceived & "</td><td align = left><font face = arial size = 1>"  & strTrailer & "</td><td align = left><font face = arial size = 1>"  & strCarrier & "</td><td align = left><font face = arial size = 1>" & strVendor & "</td><td align = left><font face = arial size = 1>" & strDetention & " " & strRejected & "</td><td align = left><font face = arial size = 1>" & strDays & " </td></tr> "
	else
 	strbody2 = strbody2 & " <tr bgcolor=#FCDCE6><td align = left><font face = arial size = 1> " & strSpecies &  "</td><td align = left><font face = arial size = 1>" & strDateReceived & "</td><td align = left><font face = arial size = 1>"  & strTrailer & "</td><td align = left><font face = arial size = 1>"  & strCarrier & "</td><td align = left><font face = arial size = 1>" & strVendor & "</td><td align = left><font face = arial size = 1>" & strDetention & " " & strRejected & "</td><td align = left><font face = arial size = 1>" & strDays & " </td></tr> "

	end if 
gcount = 0

end if


	gTotaldaysR = gTotaldaysR + strdays    
  gcountR = gcountR + 1
       gTcountR = gTcountR + 1
       gRTcount = gRTcount + 1
 		gSpecies = MyRec.fields("Species")
 		strMoneyR = strMoneyR + cint(strDetention)
 		strTMoneyR = strTmoneyR + strMoney
 		gExcessR = gExcessR + strExcess
      
       ii = ii + 1
       MyRec.MoveNext
            
  
		 		WEND
           		MyRec.Close
           		Dim strMdate
           		strMdate = formatdatetime(Now(),2)
           		
    strsql = "SELECT tblCars.* FROM tblCars WHERE (month(Date_unloaded) = month('" & strMdate & "') and year(Date_unloaded) = year('" & strMdate & "')and (Carrier = 'Rail' or Carrier = 'RAIL') and (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3')) "_
    &" or (((tblCars.Date_received) Is Not Null) AND ((tblCars.Location)='Yard') and (Carrier = 'Rail' or Carrier = 'RAIL') and (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3') AND ((tblCars.Trailer) Is Not Null)) "  
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")    
   				While not MyRec.EOF 
   				

 
        
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' and Carrier = 'Rail' and (Species = 'PMX' or Species = 'HBX' or Species = 'KBLD' or Species = 'OF3')  "

           
             
             
   If isnull(Myrec.fields("Date_unloaded")) then 
                  strDays = Fix(Now()- cdate(MyRec.fields("Date_received")))
                else 
              strdays =  Fix(datediff("d", Myrec.fields("date_received"), Myrec.fields("Date_unloaded")))
				
                  end if 
                  
                  
                   

         strdays = int(strdays)
          if strdays = 0 then
          strDetention = 0
          elseif strdays = 1 then
          strDetention = 26.52
          elseif strdays = 2 then
          strDetention = 53.04
          
          elseIf strdays = 3 then 
          strDetention = 79.56
          elseif strdays = 4 then
          strDetention = 106.08
          elseif strdays = 5 then
          strDetention = 145.33
          elseif strdays = 6 then
          strDetention = 184.58
          elseif strdays = 7 then
          strDetention = 262.03
          elseif strdays = 8 then 
          strDetention = 339.48
          elseif strdays > 8 then
          strDetention = 339.48 + (77.45*(strdays-8))
          end if
    	if strdays  > 2 then 
    	strExcess = 1
  			else            
       		
			strExcess = 0
	 end if 
	 
If left(MyRec("Trailer"), 4) = "GACX" then
strDetention = 0
strExcess = 0
end if            

	strMTDDetentionR  = strMTDDetentionR + strDetention 	
  	gMTDExcessR = gMTDExcessR + strExcess
 	gMTDTotaldaysR = gMTDTotaldaysR + strdays    
 	gMTDTotalCountR = gMTDTotalCountR + 1 
 	
 		
     
       MyRec.MoveNext



            
  
		 		WEND
           		MyRec.Close      
        		
           		strNow = formatdatetime(now(),0)
      
           			
           			strsql = "SELECT Count(CID) AS CCID FROM tblCars "_
	&" WHERE ((DatePart(m,[Date_unloaded]))=DatePart(m,'" & strToday & "')) AND (datepart(yyyy, [Date_unloaded]) = datepart(yyyy, '" & strtoday & "')) "_
	&" AND (Carrier='RAIL' Or Carrier='Rail') and Location = 'RF' "_
	&"  AND (Species = 'PMX' or Species = 'KBLD' or Species = 'OF3' or Species = 'HBX')"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strMTDUnload = MyRec("CCID")
		end if
		
		strNumberDays = datepart("d", date())
		
		strAvgMTDUnload = round(strMTDUnload/strNumberDays,1)
		strAvgMTDUnload = formatnumber(strAvgMTDUnload,1)

		
		
strbody4 = "<p><font face = arial size = 1>MTD Average Unload per day: " & strAvgMTDUnload & "</p>"


		objMail.HTMLBody = "<font face = arial size = 2> RFF Rail Unload Report (Unloaded at Dry Fiber Rail Siding) for  " &  strNow  & " <br><br>"  & strbody4 & strbody1 &  strbody2


			' objMail.Send
			Set objMail = nothing
		
		if request.querystring("s") = "i" then
				Response.redirect("Send_yard_email.asp")
			else

	  Response.redirect("OYM_RF_Trailer_Inventory_Email.asp")
	end if


 %>

