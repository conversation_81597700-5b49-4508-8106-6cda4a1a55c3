
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Inventory Reduction Report</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->

<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

<%  
	Dim strsQL, rstEquip

   	Dim objEquipSearch, rstTotals, strCount, strDed, strNet, strTons, rstTotalReceived, strTR, objNew, rstSpecies
  	Dim  strVendor, strBegDate, strEndDate, strSpecies, strDateReceived, strDateAdded, strAVG
 	Dim strVendorname, strGeneratorname
  	Dim objGeneral, gcount, strCountTwo, strSum, strSum1, strNetSum, strDepdate, objTotals

   set objGeneral = new ASP_CLS_General
   strSpecies = request.querystring("s")
strBegDate = request.querystring("b")
strEndDate = request.querystring("e")
     
      set objEquipSearch = new ASP_Cls_Fiber
      
      if strSpecies = "OCC" then
     set rstEquip = objEquipSearch.IRSearchExcelOCCR1(strBegDate, strEndDate)
else
 set rstEquip = objEquipSearch.IRSearchExcelR1(strBegDate, strEndDate, strSpecies)
end if

   
  Response.ContentType = "application/vnd.ms-excel"	



%>   
<style type="text/css">
.style1 {
	text-align: left;
}
</style>
</head>

<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center><tr></tr>

 <td bgcolor = white align="left" ><font face="Arial" size="2"><b>Count<br><%= strCount%>&nbsp;</font></td>
        <td bgcolor = white  align="center" ><font face="Arial" size = 2>
        <% if len(strSpecies) > 2 then %>
        <b>Total Net for <%= strSpecies %>
        <% else %>
        
        <b>Total Net
        <% end if %>
        <br><%= strNetSum%>&nbsp;</b></font></b></td>

   </tr>
    <tr>
  
    <%   DIm MyConn
    If len(strSpecies) > 2 then
    ' do nothing
    else
    
    strsql = "SELECT Count(tblCars.CID) AS CountOfCID, Sum(tblCars.Net) AS SumOfNet, tblCars.Species FROM tblCars "_
	&" WHERE Inv_depletion_date > '" & strBegDate & "' and Inv_depletion_date  <= '" & strEndDate & "' "_
	&" GROUP BY tblCars.Species "_
	&" ORDER BY tblCars.Species"
	
	   Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
    IF not MyConn.eof  then
    while not MyConn.eof %>
 <td align="left"> <font face="Arial" size="1"> <%= MyConn("Species") %>: <%= MyConn("SumofNet") %></td>
  <td colspan="16" align="left" > <% if strNetSum > 0 then %>
 <font face="Arial" size="1"> <%= round((MyConn("SumofNet")/strNetSum) * 100,0) %>
  <% else %>
 <font face="Arial" size="1"> 
  <% end if %></td></tr>
    <% MyConn.movenext
    wend
    end if
    MyConn.close 
    end if
    %>


</table>

   <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>
       
      <tr class="tableheader">	
      <td class="style1" ><font face="Arial" size="1">Species</font></td>
       

	<td  align="lefr" ><font face="Arial" size="1">Vendor</font></td>
	<td  align="left" ><font face="Arial" size="1">Generator</font></td>
		<td  align="left" ><font face="Arial" size="1">City</font></td>

	<td  align="center" ><font face="Arial" size="1">PO</font></td>

	<td  align="center" ><font face="Arial" size="1">Release</font></td>
		<td  align="center" ><font face="Arial" size="1">Rec Nbr</font></td>
		<td  align="center" ><font face="Arial" size = 1>PMO</font></td>
	<td  align="center" ><font face="Arial" size = 1>Trailer</font></td>
	<td  align="center" ><font face="Arial" size = 1>Transfer Trailer</font></td>
	<td  align="center" ><font face="Arial" size="1">Transfer Date</font></td>
	<td  align="center" ><font face="Arial" size="1">Trans Unload</font></td>
	<td  align="center" ><font face="Arial" size="1">Inv Depletion<br>Date</font></td>
	<td  align="center" ><font face="Arial" size="1">Location</font></td>

	<td  align="center" ><font face="Arial" size="1">Net</font></td>


	<td  align="center" ><font  face="Arial" size="1">Other</font></td>
	<td  align="center" ><font  face="Arial" size="1">Count</font></td>
      
  	<%   strcount = 1
      Dim ii
       ii = 0
       while not rstEquip.Eof
      
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr class=tablecolor1>
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    <%    
   if formatdatetime(rstEquip.fields("Inv_depletion_date"),2) <> strDepdate then
       strcount = 1
       end if 
          strVendor = rstEquip("Vendor")
       strGenerator = rstEquip("Generator")
       strCity = rstEquip("Gen_City")
       strState = rstEquip("Gen_State")
       strGrade = Rstequip("Species") %> 

<td  align="left" ><font face = "arial" size = "1"><%=rstEquip.fields("Species")%>&nbsp;</td>

     <%   strsql2 = "SELECT Tier from tblTier where State = '" & strState & "' "_
     &" and City = '" & strCity & "' and Generator = '" & strGenerator & "' "_
     &" and Vendor = '" & strVendor & "' and Grade = '" & strGrade & "' "
 
         Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL2, Session("ConnectionString")
    If not MyRec2.eof then

    strTier = MyRec2("Tier")
    else
    strTier = ""
    
    
    end if
    MyRec2.close %>


 

<td  align="left" ><font face = "arial" size = "1"><%=rstEquip.fields("Vendor")%></td>
<td  align="left" ><font face = "arial" size = "1"><%=rstEquip.fields("Generator")%></td>
<td  align="left" ><font face = "arial" size = "1"><%=rstEquip.fields("Gen_City")%></td>

<td  align="left" ><font face = "arial" size = "1"><%=rstEquip.fields("PO")%></td>

<td  align="center" ><font face = "arial" size = "1"><%=rstEquip.fields("Release_nbr")%></td>

<td  align="center" ><font face = "arial" size = "1"><%=rstEquip.fields("PMO_Nbr")%></td>
<td  align="center" ><font face = "arial" size = "1"><%=rstEquip.fields("Pnbr")%></td>
<td  align="center" ><font face = "arial" size = "1"><%=rstEquip.fields("Trailer")%></td>

<td  align="center" ><font face = "arial" size = "1"><%=rstEquip.fields("Transfer_Trailer_nbr")%></td>

<td  align="left" ><font face = "arial" size = "1"><%=rstEquip.fields("Transfer_date")%></td>
<td  align="center" ><font face = "arial" size = "1"><%=rstEquip.fields("Trans_unload_date")%></td>
<td  align="center" ><font face = "arial" size = "1"><%=rstEquip.fields("Inv_depletion_date")%></td>
<td  align="center" ><font face = "arial" size = "1"><%=rstEquip.fields("Location")%></td>
<% if not isnull(rstEquip.fields("Net")) then %>
<td  align="center" ><font face = "arial" size = "1"><%= formatnumber(rstEquip.fields("net"),3)%></td>
<% else %>
<td  align="center" ><font face = "arial" size = "1"></td>
<% end if %>


<td  align="center" ><font face = "arial" size = "1"><%= rstEquip.fields("Other_Comments")%></td>
<td  align="center" ><font face = "arial" size = "1"><%= strCount%></td>


 </tr>
    <% 
       ii = ii + 1
          strDepDate = formatdatetime(rstEquip.fields("Inv_depletion_date"),2)
           strcount = strcount + 1
       rstEquip.MoveNext
     Wend
    %>
   </table>

