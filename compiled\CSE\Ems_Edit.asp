
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Edit Name</TITLE>
<!--#include file="classes/asp_cls_SessionStringBadge.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_DataAccessBadge.asp"-->

<%

Dim strSQL, MyRec,  strBID, strDisplay, strid
 set objGeneral = new ASP_CLS_General
 
strid = request.querystring("id")
     
 strsql = "Select tblEMS.* from tblEMS where id = " & strid
   Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionStringBadge")
strName = MyRec.fields("Display_name")
strCategory = MyRec.fields("Category")
strBID = MyRec.fields("EID")
MyRec.close

if objGeneral.IsSubmit() Then


	Call SaveData() 

End if

%>
<style type="text/css">
.style1 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
</style>
</head>

<body>
<form name="form1" action="EMS_Edit.asp?id=<%= strid %>" method="post">
 
<table border="0" cellpadding="0" cellspacing="0" width = 60% style="border-collapse: collapse" bordercolor="000000" height="25" align = center >
  <tr>
    <td  bgcolor="#FFFFFF" bordercolor="#CCCCFF" height="25" >
    <p align="center"><font face="Arial" size="4">Edit EMS/Fire Brigade Person</font></td><td align = center height="25"><font face="Arial"><b><a href="Ems.asp">RETURN</a></b></font></td>

    <td align = center bgcolor="#FFFFFF" height="25">
  <Input name="Submit" type="submit" Value="Submit" style="float: right" ></td>
  </tr>
</table><br><br>
<div align="center">
<table cellpadding="0" cellspacing="0" style="width: 50%;" bordercolor="#808080"  height="10" class="style1">
    <tr>
   
    <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="22" align="center" ><b>
	<font face="Arial">BID #</font></b></td>
   
   
    <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="22" align="center" ><b>
	<font face="Arial">Display Name</font></b></td>
   
   
    <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="22" align="center" ><b>
	<font face="Arial">Category</font></b></td>
   
   
  </tr>
  
    <tr>
    	

    <td  bordercolor="#CCCCFF" height="47"  align="center" >  	
		<font face="Arial">	<%= strBID %>
</font></td>   	

	

    <td  bordercolor="#CCCCFF" height="47"  align="center" >  	
		<font face="Arial">	
	<input type="text" name="Display" size="14" style="width: 220px" value="<%= strName %>"></font></td>   	

	

    <td  bordercolor="#CCCCFF" height="47"  align="center" >  	
		<font face="Arial">
	<select size="1" name="Category">
	<option  <% If strEMS = "EMS" then %> selected <% end if %>>EMS</option>
		<option  <% If strEMS = "EMS/FB" then %> selected <% end if %>>EMS/FB</option>
		<option <% If strEMS = "COM" then %> selected <% end if %>>COM</option>

<option <% If strEMS = "Co-COM" then %> selected <% end if %>>Co-COM</option>
<option <% If strEMS = "FB" then %> selected <% end if %>>FB</option>
	</select></font></td>   	

	

  </tr>
  </table>
</div>



</form>
   
  

</body>

 <%
 
  Function SaveData()

 

strName = Replace(Request.form("Display"), "'", "''") 
strCategory = request.form("Category")


  
  strsql = "Update tblEMS  set Display_name = '" & strName & "', Category = '" & strCategory & "' where ID = " & strid

   
  	 set MyRec = new ASP_CLS_DataAccessBadge
         MyRec.ExecuteSql strSql 
         
          Response.redirect("EMS.asp")
    
          
       
  End Function
  
   %><!--#include file="footer.inc"-->