<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 14.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Truck Load Transfer from Baldwin</title>
</head>
<%
    Dim strSQL, MyRec, strID, objMOC, rstFiber, strCarrier, strSAP
      
    Dim strTrailer, strSpecies, strESpecies, rstSpecies
    Dim strReleaseNbr
    Dim strRECNbr
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strGenerator
    Dim strGenCity
    Dim strGenState
    Dim strOther, strR, strsql3, strTransTrailer

       strId  = Trim(Request.QueryString("id")) 


 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
 
		Call SaveData() 

	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to enter a Receipt.</font></br>")
	MyRec.close
	end if

  
ELSE


Call getdata()
	end if

%>



<body><br>
<table width = 100%><tr><td width = 33%></td><td align = center width = 34%><b><font face="Arial" size="4">
Transfer Load from Baldwin </font> </b></td><td align = right width = 33%><a href="Transfer_Trucks.asp"><b><font face = arial size = 2>RETURN</a></font></b></td></tr></table>



<form name="form1" action="TransferException.asp" method="post">
<div align="center">
<table border="1" cellspacing="0" bordercolor="#C0C0C0" width="60%" bgcolor="#FFFFE8" style="border-collapse: collapse" cellpadding="0">
<tr>
    <td bgcolor="#FFFFE8" width="161">&nbsp;</td>

    <td  bgcolor="#FFFFE8">&nbsp;</td>
  </tr>


      <td  bgcolor="#FFFFE8" align = right width="161">
  <font face="Arial" size="2"><b>Transfer Trailer Number:&nbsp;</b></font></td>
<td  align = left bgcolor="#FFFFE8">

      <input type="text" name="Trans_Trailer" size="15" value = "<%= strTransTrailer%>"></td></tr>
      
       <tr><td bgcolor="#FFFFE8" width="161">&nbsp;</td>
  <td bgcolor="#FFFFE8">&nbsp;</td></tr>
  <tr>

      <td  bgcolor="#FFFFE8" align = right width="161">
  <b><font face="Arial" size="2">SAP Document ID</font></b><font face="Arial" size="2"><b>:&nbsp;</b></font></td>
<td  align = left bgcolor="#FFFFE8">

      <input type="text" name="SAP" size="15" value = "<%= strSAP%>"></td></tr>
      <tr>
    <td  bgcolor="#FFFFE8" width="161">&nbsp;</td>

    <td bgcolor="#FFFFE8">&nbsp;</td>
  </tr>
<tr>
    <td  bgcolor="#FFFFE8" width="161">  
	<p align="right">  <font face="Arial" size="2"><b>Select Carrier:&nbsp;</b></font></td>
    <td bgcolor="#FFFFE8">   <select name="Carrier">
 	<option value="BWIF" selected>BWIF</option>
      <%= objGeneral.OptionListAsString(rstFiber, "Carrier", "CDesc", strCarrier) %>
     </select></td>
  </tr>
        <tr>
    <td  bgcolor="#FFFFE8" width="161">&nbsp;</td>

    <td bgcolor="#FFFFE8">&nbsp;</td>
  </tr>
  <tr>
    <td  bgcolor="#FFFFE8" width="161">  
	<p align="right">  <font face="Arial" size="2"><b>Select Species:&nbsp;</b></font></td>
    <td bgcolor="#FFFFE8">   <select name="Species">
      <option value="">---Select ---</option>
       <%= objGeneral.OptionListAsString(rstSpecies, "Species", "Species", strSpecies) %>
     </select>&nbsp; <b><font size="2" face = arial>OR enter Species</font></b>:&nbsp;

      <input type="text" name="Enter_species" size="29"></td>
  </tr>
  <tr>
    <td  bgcolor="#FFFFE8" width="161">&nbsp;</td>

    <td bgcolor="#FFFFE8">&nbsp;</td>
  </tr>
    <tr>
    <td  bgcolor="#FFFFE8" width="161">
	<p align="right"><b><font face="Arial" size="2">Tons:</font></b></td>

    <td bgcolor="#FFFFE8">

      <input type="text" name="Tons" size="11" value="21"></td>
  </tr>
    <tr>
    <td  bgcolor="#FFFFE8" width="161">&nbsp;</td>

    <td bgcolor="#FFFFE8">&nbsp;</td>
  </tr>



       <tr>
          <td  align = right bgcolor="#FFFFE8" height="27" width="161" >
    <font face="Arial" size="2"><b>Date Transferred:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFE8" height="27">

      <input type="text" name="Date_Received" size="15" value = "<%= strDateReceived%>"></td></tr>
<tr>
    <td  bgcolor="#FFFFE8" width="161">&nbsp;</td>

    <td bgcolor="#FFFFE8">&nbsp;</td>
  </tr>

  <tr>
    <td bgcolor="#FFFFE8" width="161">
	<p align="right"><font face="Arial" size="2"><b>Print Movement Order:</b></font></td>

    <td align = left bgcolor="#FFFFE8"> <font size="1" face="Verdana">  
	<input type="checkbox"  value="ON" name="Print_receipt" checked></font></td>
  </tr>

  <tr>
    <td bgcolor="#FFFFE8" width="161">&nbsp;</td>

    <td align = left bgcolor="#FFFFE8">&nbsp;</td>
  </tr>

  <tr>
    <td bgcolor="#FFFFE8" width="161">&nbsp;</td>

    <td align = left bgcolor="#FFFFE8"><Input name="Update" type="submit" Value="Submit" ></td>
  </tr>
</table>

</div>

</form>
</body>
<%
 Function GetData()
  set objMOC = new ASP_CLS_FIBER
        set rstFiber = objMOC.FiberCarrier()
        strDateReceived = formatdatetime(Now(),2)        
        set rstSpecies = objMOC.FiberSpecies()
End Function




 Function SaveData()
strTrailer = Request.form("Trans_Trailer")
If len(Request.form("Enter_species")) > 0 then
strSpecies = Request.form("Enter_species")
else
strSpecies = Request.form("Species")
end if
strCarrier = Replace(Request.form("Carrier"), "'", "''")

strDateReceived = Request.form("Date_received")

strSAP = Request.form("SAP")

Dim strRightnow
strRightnow = Now()
dim strtons
strTons = request.form("Tons")


	strsql =  "INSERT INTO tblCars (PMO_Nbr, Date_Received, Trailer,  OID, Tare_weight, Gross_Weight, Transfer_Trailer_nbr, Transfer_Date, Trans_Carrier, Species, Grade, Location, SAP_Doc_ID, Tons_Received, Deduction, Net, Entry_Time, Entry_BID, Entry_Page ) "_
	&" SELECT  'BALDWIN', '" & strDateReceived & "', 'UNKNOWN',  0, 30000, 72000, '" & strTrailer & "', '" & strDateReceived & "', '" & strCarrier & "', '" & strSpecies & "', 'RF',  'YARD',   "_
	&"   '" & strSAP & "', " & strTons & ", 0, " & strTons & ", '" & strRightnow & "', '" & Session("EmployeeID") & "', 'TransferException'"
	
	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

Dim strlast
strsql = "Select Max(CID) as MAXofCID from tblCars"

   	 Set MyConn = Server.CreateObject("ADODB.Recordset")
   	 MyConn.Open strSQL, Session("ConnectionString")
   	 strlast = MyConn.fields("MaxofCID")
   	 MyConn.close

         
 dim strbody, strbody2, strEmailTo, strECC, strEBCC      
         


	   
	       strEmailTo = "<EMAIL>,   <EMAIL>"
	          

              
      
           
            
         

            strBCC = "<EMAIL> "
         
                 
			Set objMail = Server.CreateObject("CDO.Message")		
			objMail.From = "<EMAIL>"
			objMail.To = strEmailTo
		 
		

			objMail.Subject = "Truck of Recycle Fiber Arrived FROM Baldwin going to Yard"
			strBody2 = "<table border=0  cellpadding=0 cellspacing=0 width=800><tr><td align = center><font face = arial size = 2>&nbsp; Carrier&nbsp; </td><td align = left><font face = arial size = 2>Trailer&nbsp; </td><td align = left><font face = arial size = 2>Species&nbsp; </td></tr>"
			
          strbody2 = strbody2 & " <tr><td align = center><font face = arial size = 1> " & strCarrier &  "</td><td align = left><font face = arial size = 1>" & strTrailer & "</td><td align = left><font face = arial size = 1>" & strSpecies & "</td></tr> "
   
				
		objMail.HTMLBody = "<font face = arial size = 2>Truck of Recycle Fiber Arrived FROM Baldwin going to Yard:<br><br>" & strbody2



			objMail.Send
			Set objMail = nothing	       
         
         
      
         
     
         
         
 If Request.form("Print_receipt") = "ON" then
 Response.redirect("Transfer_receipt.asp?id=" & strlast)
 else        
  

Response.redirect ("Fiberindex.asp")
end if


End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->