
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">


<TITLE>Grading Fill Rate Summary</TITLE>


<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

 <%    Dim  strSQL, MyRec, objMOC, rstFiber, strBegDate, strEndDate
	
       Dim objGeneral, strDate, MyConn, strSpecies
strMonth = request.querystring("id")

%>

<style type="text/css">
.style1 {
	text-align: center;
	font-family: Arial, Helvetica, sans-serif;
}
.style2 {
	font-family: Arial, Helvetica, sans-serif;
}
.style3 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
.style5 {
	border: 1px solid #C0C0C0;
	text-align: center;
}
.style6 {
	border: 1px solid #C0C0C0;
	font-family: Arial, Helvetica, sans-serif;
	text-align: right;
}
.style7 {
	text-align: right;
	font-family: Arial, Helvetica, sans-serif;
}
</style>
</head>

<body>
<br>
<table width="100%"><tr><td  class="style2"><b>Fill Rates By Grade Report for Import Month <%= strMonth %>  </b></td>
<td align="right"><font face="arial" size="2"><a href="MOB_Select_Dates.asp"><b>RETURN</b></a></font></td></tr></table><br><br>
<div align="center">
<table cellpadding="0" class = "style3" cellspacing="0" style="width: 40%;" bordercolor="#111111" id="AutoNumber2">
<tr> <TD bgcolor="#F2F2FF" class="style1">  Species</td>
	<TD bgcolor="#F2F2FF" class="style7"> Ordered </td>
	<TD bgcolor="#F2F2FF" class="style7">Received</td>
	<TD bgcolor="#F2F2FF" class="style7">Fill Rate</td>
		<TD bgcolor="#F2F2FF" class="style7">Weight (Tons)</td>

	</tr>

        				<% 
			
        				
    
          Set rstFiber = Server.CreateObject("ADODB.Recordset")
strsql2 = "SELECT Species, Count(Release) AS Rcount FROM tblOrder  "_
&" where Import_month = '" & strmonth & "'  GROUP BY Species  ORDER BY Species"
     
    rstFiber.Open strSQL2, Session("ConnectionString"), adOpenDynamic			


	While Not rstFiber.EOF
        				strFilled = 0
        				strRate = 0
        				strCount = 0
        				strweight = 0
        				strSpecies = rstFiber("Species")
        				strCount = rstfiber("Rcount")
        		

        				
     strsql = "SELECT Count(tblOrder.OID) AS CountOfOID FROM tblOrder INNER JOIN tblVirginFiber ON tblOrder.Release = tblVirginFiber.Release "_
&" where Import_month = '" & strmonth & "'  and tblOrder.Species = '" & strSpecies & "'"
           Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString")	
   if not MyRec.eof then
   strcount = strcount + (MyRec("CountofOID") * 2)
   end if
   MyRec.close
   
        			
        			   strsql = "SELECT tblOrder.Species, Count(tblOrder.Release) AS Gcount, round(Sum([Net]),0) as Weight "_
&" FROM tblOrder INNER JOIN [tblCars] ON tblOrder.Release = [tblCars].Release_nbr "_
&" WHERE Import_month = '" & strmonth & "' AND Date_Received Is Not Null and tblOrder.Species = '" & strSpecies & "' GROUP BY tblOrder.Species"
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString")
'response.write("strsql" & strsql)
If not MyRec.eof then

strFilled = MyRec("Gcount")
strWeight = MyRec("Weight")
else
strFilled = 0
strWeight = 0
End if

strsql = "SELECT Count(tblOrder.OID) AS CountOfOID "_
&" FROM (tblOrder INNER JOIN tblVirginFiber ON tblOrder.Release = tblVirginFiber.Release) INNER JOIN tblCars ON tblVirginFiber.VID = tblCars.VID "_
&" WHERE Import_month = '" & strmonth & "' and tblOrder.Species = '" & strSpecies & "' AND  Date_Received Is Not Null"
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString")
   if not MyRec.eof then
   strFilled = strFilled + (MyRec("CountofOID") * 2)
end if


if strfilled = 0 then
strRate = 0
else
strRate = Round(strFilled/strCount * 100,0)
end if

					%>
            			
			<tr><td class="style5"><span class="style2"><%= rstFiber("Species")%></span> </td>
				<td class="style6"><%= strCount %></td>
				<td class="style6"><%= strFilled %></td>
				<td class="style6"><%= strRate %>&nbsp;%</td>
				<td class="style6"><%= strWeight %> </td>
				</tr>

					<% 
        					rstFiber.MoveNext 
        					wend     				
        					rstFiber.close
        				
					%>

</table>
</div>

<!--#include file="Fiberfooter.inc"-->