
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Vendor Quality Rating</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

<%  
	Dim strsQL, rstEquip

   	Dim objEquipSearch, rstTotals, strCount, strDed, strNet, strTons, rstTotalReceived, strTR, objNew, rstSpecies
  	Dim  strVendor, strBegDate, strEndDate, strSpecies, strDateReceived, strDateAdded, strAVG
 	Dim  rstVendor, strFIQ, strBAQ, rstGenerator, strGenerator, strVendorname, strGeneratorname
  	Dim objGeneral, gcount, strCountTwo, strSum, strSum1
   	
   	Dim intDirection
  	Dim intPageNumber
   	Dim strPageNav

   intPageNumber = 1

   set objGeneral = new ASP_CLS_General

  if objGeneral.IsSubmit() Then
    Call GetFormData()
    Select Case intDirection
      Case 10: 'goto mocinfo.asp
  
      Case Else:
        Call LoadSearchResults()
    End Select
  else
    intDirection = 0
  end if
Call GetData()
%>

<script language="javascript">
 
  function GotoMOC(MOCID)
  {
    document.forms["form1"].elements["tbl_ID"].value = MOCID;
    document.forms["form1"].elements["Direction"].value = 10;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }

  function Search()
  {
    
    document.forms["form1"].elements["Direction"].value = 1;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }

  function NextPage()
  {
    document.forms["form1"].elements["Direction"].value = 5;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }
  function PrevPage()
  {
    document.forms["form1"].elements["Direction"].value = 6;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }
</script>


<style type="text/css">
.auto-style1 {
	border: 2px solid #C0C0C0;
	background-color: #E7EBFE;
}
</style>
</head>


<form name="form1" action="Vendor_Rating.asp" method="post">
 <input type="hidden" name="Direction" value="1" >
 <input type="hidden" name="tbl_ID" value="" >
 <input type="hidden" name=PageNumber value="<%=intPageNumber%>" >
 
<TABLE borderColor=white cellSpacing=0 cellPadding=0  width = 100% border=1 >  
  <tr>
  <td  align = left><font face = "Arial"></font><b> <font face="Arial">Vendor 
	Quality Rating &nbsp;<font face = "Arial" size = "2">&nbsp;</font></b></td>
	<td align = right><font face="Arial"><b><a href="Loginscreen.asp">HOME</a></b></font></td></tr>
</table>

 

<TABLE cellSpacing=0 cellPadding=0 width=100% align = CENTER id="table1" class="auto-style1">  

  <TR>
         <TD><b><font face="Arial" size="2">Vendor:&nbsp;&nbsp; </font></b>   </td>
         <td>  <select size="1" name="Vendor">
     <option value="">--- All ---</option>
       <%= objGeneral.OptionListAsString(rstVendor, "Vendor", "Vendor", strVendor) %>
     </select></TD>
     <td><font size="2" face="Arial"><b>&nbsp; Enter Vendor for Exact Match</b></font><b><font size="2">:</font></b></td>
     <td><font face="Arial">		
		<input name="Vendor_name" size="22" maxlength="10" value="<%=strVendorName%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633"></td>
	<TD ><b><font face="Arial" size="2">Beg Date:</font></b></TD>
    <TD> <font face="Arial"><input name="Beg_Date" size="10" maxlength="10" value="<%=strBegDate%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633"></td>
		<TD><b><font face="Arial" size="2">Species:</font></b></TD>
    <TD>  <font face="Arial">   <select size="1" name="Species">
     <option value="">--- All ---</option>
       <%= objGeneral.OptionListAsString(rstSpecies, "Species", "Species", strSpecies) %>
     </select></td>
</TR>

<tr>
     <TD><font face="Arial"><font size="2"><b>Generator</b></font><b><font size="2">:</font></b>&nbsp;&nbsp;</td>
<td>  
<p align="left"> </font>  <select size="1" name="Generator">
     <option value="">--- All ---</option>
       <%= objGeneral.OptionListAsString(rstGenerator, "Generator", "Generator", strGenerator) %>
     </select></TD>  
     
     <td><b><font face="Arial" size="2">&nbsp; Enter Generator for Exact Match:</font></b></td>
     <td> <font face="Arial">	
		<input name="Generator_name" size="22" maxlength="10" value="<%=strGeneratorname%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633"></td>
          <TD ><font face="Arial"><font size="2"><b>End Date</b></font><b><font size="2">:</font></b>&nbsp;&nbsp;</td>
<td>   <font face="Arial"><input name="End_Date" size="10" maxlength="10" value="<%=strEndDate%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633"></TD>  
  
    <TD  colspan = 2>&nbsp;</TD>
     
</TR></table>
<table width = 100% bgcolor = white><tr>

  
    <TD  align="right"><input type="button" onClick="javascript:Search()" value="Search" caption="Search"></TD>
 
</TR>


  </TABLE></form>


  <% if objGeneral.IsSubmit() Then 

%>
   <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>
    <tr><td colspan="13" bgcolor="white" width="374"><font face="Arial" size="2"><b>Search Results</b> - Page <%=intPageNumber%></font></td>
       
<% if intPageNumber = 1 then %>


 <td bgcolor = white align="center" width="69"><font face="Arial" size="2"><b>Count<br><%= strCount%>&nbsp;</font></td>
        <td bgcolor = white  align="center" width="57"><font face="Arial" size = 2><b>AVG<br><%= strAvg%>&nbsp;</font></b></td>
    <% else %>

    <% end if %>
    </tr>
    <tr><td colspan="15" bgcolor="white" align="right" width="851"><%=strPageNav%>&nbsp;</td></tr>
      <tr class="tableheader">	
	<td  align="center" ><font face="Arial" size="1">Vendor</font></td>
	<td  align="center" ><font face="Arial" size="1">Release Nbr</font></td>
	<td  align="center" ><font face="Arial" size="1">Species</font></td>
	<td  align="center" ><font face="Arial" size="1">Car/Trailer</font></td>
		<td  align="center" ><font face="Arial" size="1">Generator</font></td>
		<td  align="center" ><font face="Arial" size = 1>Inv Depletion<br>Date</font></td>
	<td  align="center" ><font face="Arial" size = 1>Date<br> Received</font></td>
	<td  align="center" ><font face="Arial" size = 1>Date<br> Unloaded</font></td>
	<td  align="center" ><font face="Arial" size="1">RF Bale</font></td>
	<td  align="center" ><font face="Arial" size="1">Tons<br> Received</font></td>
	<td  align="center" ><font face="Arial" size="1">Deduction</font></td>
	<td  align="center" ><font face="Arial" size="1">Net</font></td>

	<td  align="center" ><font face="Arial" size="1">Fiber<br> Quality</font></td>
	<td  align="center" ><font  face="Arial" size="1">Bale<br> Quality</font></td>
	<td  align="center" ><font  face="Arial" size="1">&nbsp;AVG</font></td>
	<td  align="center" ><font  face="Arial" size="1">&nbsp;View<br>Initial<br>Grading</font></td>
<td  align="center" ><font  face="Arial" size="1">&nbsp;View<br>Second<br>Grading</font></td>
      
  	<% 
      Dim ii
       ii = 0
       while not rstEquip.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="white">
    <% else %>
       <tr bgcolor="#E7EBFE">
    <% end if %>
    
    
       <%	 if rstEquip.fields("Fiber_quality_excellent") = 3 then
    	strFIQ = 3
   	 elseif rstEquip.fields("Fiber_quality_good") = 2 then
   	 strFIQ = 2
     elseif rstEquip.fields("Fiber_quality_poor") = 1 then
   	 strFIQ = 1
   	 else
   	 strFIQ = 0
   	 End if
    
  if rstEquip.fields("Trans_Fiber_quality_excellent") = 3 or rstEquip.fields("Trans_Fiber_quality_good") = 2 or rstEquip.fields("Trans_Fiber_quality_poor") = 1 then
     if rstEquip.fields("Trans_Fiber_quality_excellent") = 3 then
    	strFIQ = 3
   	 elseif rstEquip.fields("Trans_Fiber_quality_good") = 2 then
   	 strFIQ = 2
     elseif rstEquip.fields("Trans_Fiber_quality_poor") = 1 then
   	 strFIQ = 1
   	 End if
   	 end if
    

    
    
    
    if rstEquip.fields("Bale_quality_excellent") = 3 then
    	strBAQ = 3
   	 elseif rstEquip.fields("Bale_quality_good") = 2 then
   	 strBAQ = 2
     elseif rstEquip.fields("Bale_quality_poor") = 1 then
   	 strBAQ = 1
   else 
     strBAQ = 0
     end if
  
    If strBAQ = 0 then
   	 if rstEquip.fields("Trans_Bale_quality_excellent") = 3 then
   		 strBAQ = 3
    	elseif rstEquip.fields("Trans_Bale_quality_good") = 2 then
    	strBAQ = 2
    	 elseif rstEquip.fields("Trans_Bale_quality_poor") = 1 then
   		 strBAQ = 1
   		 else
   		 strBAQ = 0
   		 End if
    End if      
    
    
    %>
    
    

<td  align="left" ><font face = "arial" size = "1"><%=rstEquip.fields("Vendor")%>&nbsp;</td>
<td  align="center" ><font face = "arial" size = "1"><%=rstEquip.fields("Release_Nbr")%>&nbsp;</td>
<td  align="center" ><font face = "arial" size = "1"><%=rstEquip.fields("Species")%>&nbsp;</td>
<% if rstEquip.fields("Trailer") = "UNKNOWN" then %>
<td  align="center" ><font face = "arial" size = "1"><%=rstEquip.fields("Transfer_Trailer_nbr")%>&nbsp;</td>
<% else %>
<td  align="center" ><font face = "arial" size = "1"><%=rstEquip.fields("Trailer")%>&nbsp;</td>
<% end if %>
<td  align="left" ><font face = "arial" size = "1"><%=rstEquip.fields("Generator")%>&nbsp;</td>
<td  align="left" ><font face = "arial" size = "1"><%=rstEquip.fields("Inv_Depletion_date")%>&nbsp;</td>
<td  align="center" ><font face = "arial" size = "1"><%=rstEquip.fields("Date_Received")%>&nbsp;</td>
<td  align="center" ><font face = "arial" size = "1"><%=rstEquip.fields("Date_unloaded")%>&nbsp;</td>
<td  align="center" ><font face = "arial" size = "1"><%=rstEquip.fields("Bales_Rf")%>&nbsp;</td>

<% if not isnull(rstEquip.fields("tons_received")) then %>
<td  align="center" ><font face = "arial" size = "1"><%= formatnumber(rstEquip.fields("tons_received"),3)%>&nbsp;</td>
<% else %>
<td  align="center" ><font face = "arial" size = "1">&nbsp;</td>
<% end if %>
<% if not isnull(rstEquip.fields("Deduction")) then %>
<td  align="center" ><font face = "arial" size = "1"><%= formatnumber(rstEquip.fields("Deduction"),3)%>&nbsp;</td>
<% else %>
<td  align="center" ><font face = "arial" size = "1">&nbsp;</td>
<% end if %>
<% if not isnull(rstEquip.fields("Net")) then %>
<td  align="center" ><font face = "arial" size = "1"><%= formatnumber(rstEquip.fields("net"),3)%>&nbsp;</td>
<% else %>
<td  align="center" ><font face = "arial" size = "1">&nbsp;</td>
<% end if %>


	<td  align="center" ><font face="Arial" size = 1><%= strFIQ %>&nbsp;</font></td>
	<td  align="center" ><font face="Arial" size = 1><%= strBAQ %>&nbsp;</font></td>
	<td  align="center" ><font face="Arial" size = 1><%= (strFIQ + strBAQ)/2%>&nbsp;</font></td>

    <td align = center> <font size="1" face="Arial"><a href="Grading_View.asp?id=<%= rstEquip.fields("CID") %>">View</a></td>

    <td align = center> <font size="1" face="Arial"><a href="Trans_Car_unload_view.asp?id=<%= rstEquip.fields("CID") %>">View</a></td>
   </tr>
    <% 
       ii = ii + 1
       rstEquip.MoveNext
     Wend
    %>
   </table>
<table>    <tr><td colspan="7" bgcolor="white" align="right"><%=strPageNav%>&nbsp;</td></tr></table>
<% end if %><% Function GetData()

   set objNew = new ASP_Cls_Fiber
          
	set rstVendor = objNew.FiberVendor()
		set rstSpecies = objNew.FiberSpecies()

set rstGenerator= objNew.FiberGenerator()


End Function
    Function GetFormData()
      intDirection = cint(Request.Form("Direction")) 
       
	
	strVendor = Request.form("Vendor")
	strGenerator = Request.form("Generator")
	strBegDate = Request.form("Beg_date")
	strEndDate = Request.form("End_date")
	strSpecies = Request.form("Species")
	strVendorname = Request.form("Vendor_name")
	strGeneratorname = Request.form("Generator_name")


      intPageNumber = cint(Request.Form("PageNumber"))

 
    End Function

    Function LoadSearchResults()
      Dim objEquipSearch, objTotals

      if intDirection =1 Then
         intPageNumber = 1
      end if

      if intDirection = 5 Then
         intPageNumber = intPageNumber + 1
      end if
      if intDirection = 6 Then
         intPageNumber = intPageNumber - 1
      end if


	  strVendor = Request.form("Vendor")
	  strVendorname = Request.form("Vendor_name")
	strGeneratorname = Request.form("Generator_name")
	strSpecies = Request.form("Species")
      set objEquipSearch = new ASP_Cls_Fiber
    
 if len(strVendorname) > 1  and len(strGeneratorname) > 1 then
  set rstEquip = objEquipSearch.VREVGSearch(50, intPageNumber, strBegDate, strEndDate, strGeneratorname, strSpecies, strVendorname)
 set rstTotals = objEquipSearch.VREVGSearch_totals(intPageNumber, strBegDate, strEndDate, strGeneratorname, strSpecies, strVendorname)
 

 elseif  len(strVendorname) = 0  and len(strGeneratorname) > 1 then
  set rstEquip = objEquipSearch.VREGSearch(50, intPageNumber, strBegDate, strEndDate, strGeneratorname, strSpecies, strVendor)
   set rstTotals = objEquipSearch.VREGSearch_totals(intPageNumber, strBegDate, strEndDate, strGeneratorname, strSpecies, strVendor)
   
 elseif  len(strVendorname) > 1 and len(strGeneratorname) = 0 then
    set rstEquip = objEquipSearch.VREVSearch(50, intPageNumber, strBegDate, strEndDate, strGenerator, strSpecies, strVendorname)
 set rstTotals = objEquipSearch.VREVSearch_totals(intPageNumber, strBegDate, strEndDate, strGenerator, strSpecies, strVendorname)
 

   else
 set rstEquip = objEquipSearch.VRSearch(50, intPageNumber, strBegDate, strEndDate, strGenerator, strSpecies, strVendor)
set rstTotals = objEquipSearch.VRSearch_totals(intPageNumber, strBegDate, strEndDate, strGenerator, strSpecies, strVendor)

end if


     if  not rstTotals.Eof Then
     Dim strFQAvg, strBQAvg
      strFQAvg = rstTotals.fields("SumofFQ_Rating")
      strBQAvg = rstTotals.fields("SumofBQ_Rating")
      
      strCount = rstTotals.fields("CountofCID").value
      strCountTwo = strCount * 2   
    Dim strTFQE, strTFQG, strTFQP, strTBQE, strTBQG, strTBQP
    strTFQE = 0
    strTFQG = 0
    strTFQP = 0
    strTBQE = 0
    strTBQG = 0
    strTBQP = 0
    If   rstTotals.fields("SumofFQ_Rating") > 0 then
    strTFQE = rstTotals.fields("SumofFQ_Rating")
	else
	strTFQE = 0
	End if
    
    If   rstTotals.fields("SumofBale_Quality_Excellent") > 0 then
    strTBQE = rstTotals.fields("SumofBale_Quality_Excellent")
    end if
    
    If   rstTotals.fields("SumofBale_Quality_Good") > 0 then
    strTBQG = rstTotals.fields("SumofBale_Quality_Good")
    end if
    
    If   rstTotals.fields("SumofBale_Quality_Poor") > 0 then
    strTBQP = rstTotals.fields("SumofBale_Quality_Poor")
    end if   
      
      
     ' strSum =  strTFQE + strTBQE + strTBQG + strTBQP
     strSum = strFQAvg + strBQAvg
 
      strAvg = formatnumber(strSum/strcounttwo,2)
      
	else
	strCount = 0
	strAvg = 0
      
      end if 

     if ( not rstEquip.Eof) Then
      if ( intPageNumber < rstEquip.fields("TotalPage").value ) Then
        strPageNav = "<a href=javascript:NextPage()><B>Next Page</b></a>"
      end if
      if ( intPageNumber > 1 ) Then
         strPageNav = "<a href=javascript:PrevPage()><b>Previous Page</b></a>&nbsp;&nbsp;&nbsp;&nbsp;" & strPageNav
      end if
     end if

       ' if ( not rstTotalReceived.Eof) Then
    ' strTR = rstTotalReceived.fields("countofoid").value
    ' end if
    End Function
 %><!--#include file="Fiberfooter.inc"-->