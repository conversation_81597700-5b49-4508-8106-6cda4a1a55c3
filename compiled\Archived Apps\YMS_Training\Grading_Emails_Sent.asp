																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Grading Email List </TITLE>

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 



<% Dim MyRec, strsql, strstartdate
strid = request.querystring("ID")

strsql = "SELECT tblCars.Release_nbr, tblGradeEmail.Date_sent, tblGradeEmail.Sent_by, tblGradeEmail.Net, tblGradeEmail.Deduction, tblGradeEmail.Total "_
&" FROM tblCars INNER JOIN tblGradeEmail ON tblCars.CID = tblGradeEmail.CID where tblGradeEmail.CID = " & strid
 

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<style type="text/css">
.style1 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial;
	font-size: x-small;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style2 {
	font-family: Arial;
}
.style3 {
	border: 1px solid #C0C0C0;
}
.style4 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style5 {
	font-family: Arial;
	text-align: right;
}
</style>
</head>

<body>

 
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>


<td align = left class="style2"><strong>Grading Emails Sent</strong></td>



<td class="style5">
<a href="javascript:history.go(-1);"><strong>RETURN</strong></a>
&nbsp;</td>



</tr>
	    </table>
	<br>
	
	<TABLE cellSpacing=0 cellPadding=0 align = center class="style3" style="width: 75%">  
	 <tr  >
<td  align = left width="25%" style="height: 30px" class="style1">	<strong>Release #</strong></td>
				<td  align = left width="25%" style="height: 30px" class="style1">	
				<strong>Date Sent </strong> </td>
		<td class="style1" style="height: 30px"  > <strong>Sent By </strong> </td>
		<td class="style1" style="height: 30px"  > <strong>Net </strong> </td>
	<td class="style1" style="height: 30px"  > <strong>Deduction </strong> </td>
	<td class="style1" style="height: 30px"  > <strong>Total</strong></td>


	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>

	<td class="style4"  ><font size="2" face="Arial"><%= MyRec.fields("Release_nbr")%></font></td>
	
<td class="style4"  ><font size="2" face="Arial"><%= MyRec.fields("Date_sent")%>&nbsp;</font></td>
<td class="style4"  ><font size="2" face="Arial"><%= MyRec.fields("Sent_by")%>&nbsp;</font></td>
<td class="style4"  ><font size="2" face="Arial"><%= MyRec.fields("Net")%>&nbsp;</font></td>
<td class="style4"  ><font size="2" face="Arial"><%= MyRec.fields("Deduction")%>&nbsp;</font></td>	
<td class="style4"  ><font size="2" face="Arial"><%= MyRec.fields("Total")%>&nbsp;</font></td>	

</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>
 <!--#include file="Fiberfooter.inc"-->