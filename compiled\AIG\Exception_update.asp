
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Update Exception</TITLE>
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_SessionStringAIG.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->

<!--#include file="classes/asp_cls_headerAIG.asp"-->
<!--#include file="classes/asp_cls_DataAccessAIG.asp"-->
<%

Dim strSQL, MyRec, strRelease, strDescription, strid
 dim strtoday, strNeeded, strTomorrow, strNow
  strNow= DateAdd("h", -5, Now())
 strtoday = formatdatetime(strnow,2)
 strtomorrow = formatdatetime(dateadd("d", 1, strToday),2)
 strid = request.querystring("id")

strsql = "Select Resolution, release, id, status from tblExceptions where id = " & strid
  Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
    If not MyConn.eof then
    
    strResolution = MyConn.fields("Resolution")
    strStatus = MyConn.fields("Status")
    strRelease = MyConn.fields("Release")
    end if
    MyConn.close

 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then


	Call SaveData() 

End if
%>
<style type="text/css">
.style1 {
	font-family: Arial;
	font-size: x-small;
}
.style2 {
	margin-left: 0px;
}
.style3 {
	border-color: #E1E1FF;
	text-align: left;
		border-width: 1px;
	background-color: #EAEAFF;
}
</style>
</head>

<body>
<form name="form1" action="Exception_update.asp?id=<%=strid%>" method="post">
 
<table border="0" cellpadding="0" cellspacing="0" width = 60% style="border-collapse: collapse" bordercolor="000000" height="25" align = center >
  <tr>
    <td  bgcolor="#FFFFFF" bordercolor="#CCCCFF" height="25" >
    <p align="center"><font face="Arial" size="4">Update Resolution&nbsp;&nbsp;for 
	Release # <%= strRelease %>
    </font>  	</td><td align = center height="25"><font face="Arial"><b><a href="Exception_Search.asp">RETURN</a></b></font></td>

    <td align = center bgcolor="#FFFFFF" height="25">
  <Input name="Submit" type="submit" Value="Submit" style="float: right" ></td>
  </tr>
</table><br><br>
<div align="center">
<table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse; height: 198px;" bordercolor="#808080" width="75%">
    <tr>
	 <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="22" align="center" class="style1"  width="50%">
		<strong>Current Resolution</strong></td>
	 <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="22" align="center" class="style1" >
		<strong>Enter or Add to the Resolution Description</strong></td>
   
  </tr>
  
    <tr>
	   <td width = "50%" style="height: 92px; width: 50%" valign="top" class="style3" > <font size="2" face="arial"> <%= strResolution %></font>	
    &nbsp;</td>
	   <td  bordercolor="#CCCCFF" width = "50%" align="center"  >  	
    <textarea name="Description" style="height: 211px; width: 465px" class="style2"><%= strDescription%></textarea></td>
    	

  </tr>


  </table>
</div></form>  
  

</body>

 <%
  
  Function SaveData()
 strid = request.querystring("id")
 if isnull(Request.form("Description")) then
 strDescription =  ""
else
 strDescription =  Replace(Request.form("Description"), "'", "''") 
end if

strsql = "Select Resolution, release, id, status from tblExceptions where id = " & strid
  Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
    If not MyConn.eof then
    
    strResolution = MyConn.fields("Resolution")
    strStatus = MyConn.fields("Status")
    strRelease = MyConn.fields("Release")
    end if
    MyConn.close
strDescription = strtoday & ": " & Session("Ename") & ": " & strDescription

If len(strResolution) > 0 then
strUpdate = Replace(strResolution, "'", "''")  & "<br>" & strDescription
else
strUpdate = strDescription
end if
  
  strsql = "Update tblExceptions set Resolution = '" & strUpdate & "', last_update = '" & strtoday & "', "_
  &" last_update_by = '" & Session("Ename") & "', status = 'Pending' where id = " & strid

   
  	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
          Response.redirect("Exception_Search.asp")
  End Function
  
   %><!--#include file="AIGfooter.inc"-->