																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<% IF Request.querystring("r") = "ER" then %>
<TITLE>Rail Receipt</TITLE>
<% else %>
<TITLE>Truck Receipt</TITLE>
<% end if %>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->




<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate, strid, strTrailer, strDateReceived, MyConn, strcid, strslq3, strpounds

strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -7, strtdate)
strid = request.querystring("id")
strpounds = request.querystring("p")
strTrailer = request.querystring("t")
strDateReceived = Request.querystring("d")


strsql = "SELECT tblCars.* FROM tblCars WHERE cid  = " & strid
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    
    strcid = MyRec.fields("CID")
    
  strsql3 = "Update tblcars set Rec_number = " & strcid & " where CID = " & strcid  
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL3
			MyConn.Close
			
%>
<style type="text/css">
.style1 {
	font-family: Arial, Helvetica, sans-serif;
	font-weight: bold;
}
.style2 {
	font-family: Arial, Helvetica, sans-serif;
}
.style3 {
	font-size: x-large;
}
.style4 {
	font-size: small;
}
.style5 {
	text-align: center;
}
</style>
</head>

<body onload="if (window.print) {window.print()}">

<div class="style5">

<p align="center"><img height="40" src="kcc40white2.gif" width="450"><br><br><br>
	
</p>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD align = left>
	&nbsp;</td>
<td align = center><b><font face="Arial">Receipt for <span class="style3">STO</span> Load at Kimberly-Clark 
<b> 
<a href="SelectSTO.asp">Mobile</a></b></font></b><br><b>
<span style="font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;
mso-fareast-font-family:Calibri;mso-fareast-theme-font:minor-latin;color:black;
mso-ansi-language:EN-US;mso-fareast-language:EN-US;mso-bidi-language:AR-SA">For 
SAP MIGO entry, use default Qty in SAP</span></b></td>

 <TD align = left>
	&nbsp;</td>



</tr>
	    </table><br><br><br>
	
<table width = 100%>
<Tr>	<td align="left"><b><font face="Arial">Receipt Number:&nbsp;<%= MyRec.fields("CID")%>

</font></b></td>
	<td align="right"><b><font face="Arial">Date Received:&nbsp;<%= MyRec.fields("Date_Received")%></font></b></td></Tr>


</table>	<br><br><br><br>

<table border="1" width="100%" id="table1" cellspacing="1" bordercolorlight="#808080" bordercolor="#000000">

			<tr>
		<td bordercolor="#808080" align="center" colspan="6">&nbsp;</td>
		
	</tr>

		<tr>
		<td bordercolor="#808080" align="center"><b><font face="Arial">Carrier</font></b></td>
	
		<td bordercolor="#808080" align="center"><b><font face="Arial">Trailer #</font></b></td>
	
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">Species</font></b></td>
		<td bordercolor="#808080" align="center" class="style1">Delivery #</td>
		
		<td bordercolor="#808080" align="center" class="style1">PO #</td>
		
		<td bordercolor="#808080" align="center" class="style1">BOL#</td>
		
	
		
	</tr>
			<tr>
	
		<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("Carrier")%></font></b></td>
			<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("Trailer")%></font></b></td>

		
						<td align="center"><b><font face="Arial"><%= MyRec.fields("Species")%>&nbsp;</font></b></td>



		<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("STO_Number")%></font></b></td>



<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("PO")%></font></b></td>


<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("BOL")%></font></b></td>


		
	</tr>
	</table>
	<table>
	<tr>
		<td colspan = 6>&nbsp;</td>
	</tr>
		<tr>
		<td colspan = 6>&nbsp;</td>
	</tr></table>
	
	<table width = 50% align = center>
	<tr>
		<td valign="top"><b><font face="Arial">KC Site Shipped From:&nbsp;&nbsp; 		<%= MyRec.fields("Vendor")%></font></b></td>		
		
	</tr></table>
	<div class="style5">
<br><img src="images/Wadding_Receipt.jpg"><br>

		<% if len(MyRec.fields("Other_comments")) > 0 then %>
		</div>
		<table width = 100% align = center><tr><td align = center>
	<b><font face="Arial"><%= MyRec.fields("Other_Comments")%></td></tr></table>
		<% end if %>

		<div align="center">
		<table width = 80% border="1">
	<tr><td align="center">
		<b><font face="Arial">SAP DOC ID</font></b></td>
		<td align="center"><b><font face="Arial">Receiver Name</font></b></td>
		
		<td align="center"><b><font face="Arial">Initials</font></b></td>
		
	</tr>
	
	<tr><% if MyRec.fields("Species") = "UNRESOLVED" or MyRec.fields("Species") = "Unresolved" then %>
	<td align="center" bgcolor="#E4E4E4"><b><font face="Arial" color="red">
	<strong>DO NOT ENTER INTO SAP</strong></font></b></td>
	<% else %>
		<% end if %>
		<td align="center">&nbsp;</td>
		<td align="center"><font face = Arial><b><%= session("Ename") %></td>
	
		<td align="center">&nbsp;</td>
	</tr>
	
</table></div>
</div>

