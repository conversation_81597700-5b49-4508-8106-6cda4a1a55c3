<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">


<TITLE>Move Trailer of BROKE</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 

 <%    Dim objDAC, strSQL, MyRec, objMOC, rstFiber, strCID, rstFiberTrans, rstNF, strNFCID

       Dim objGeneral, strDate, MyConn, strTCID

	strCID = ""

	
       set objGeneral = new ASP_CLS_General

if objGeneral.IsSubmit() Then


strNFID = Request.form("NFID")



	Response.redirect("Out_Boise_Trailer.asp?id=" & strNFID)


end if


%>
<SCRIPT LANGUAGE="JavaScript">
function formHandler(Form1){
document.forms["Form1"].submit();
}
</SCRIPT>

<body>
<form name="form1" action="SelectTruckUnloadBoise.asp" method="post" >

<p align="center"><font face="Arial"><b>MOVE TRAILER
</b></font></p>
<table border="1" cellpadding="0" class = "tablecolor1" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="60%" id="AutoNumber2" align = center>

       
<Td bgcolor = "#FFFFE8">
	
		<p align="center"> &nbsp;</p>
		<p align="center"> <font face="Arial" size="3"><b>&nbsp;&nbsp;Select 
		Trailer of BOISE CASCADE:&nbsp;&nbsp;</b></font></p>
		<p align="center"> <font face="Arial" size="3"><b><br></b></font>
	&nbsp;&nbsp;&nbsp;<br>
     <font face="Arial">
     <select name="NFID">
 	<option value="" selected>Trailer Number</option>
    <% strsql = "Select CID, Trailer from tblCars where Grade = 'BOISE' and Location = 'YARD' "
            Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
			If not MyRec.eof then
			while not MyRec.eof 
			%>
			<option value="<%= MyRec.fields("CID") %>"><%= MyRec.fields("Trailer") %></option>
<% MyRec.movenext
wend
end if
MyRec.close %>
     </select></p>
		<p>&nbsp;</p> 
<p align="center"><input type="submit" value="Continue" id=submit1 name=submit1><br><br><br>
<br><br>   </TD></tr>

</table>
</form>


<!--#include file="Fiberfooter.inc"-->