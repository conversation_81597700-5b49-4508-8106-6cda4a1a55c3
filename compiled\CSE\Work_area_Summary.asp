﻿<html>
<title>Work Area Summary</title>

<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_Procedure.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->

<head>
<% dim strdate, strWA, MyConn
strnow = dateadd("h", -5, now())
strDate = formatdatetime(strnow,2)
strWA = Request.querystring("id")
if left(strWA, 4) = "SF B" then
strWA = "SF Buildings & Grounds"
end if
 if left(strWA, 4) = "Comb" then
strWA = "Combined Heat & Power"
end if

 %>

<style>
<!--
 p.<PERSON><PERSON>
	{mso-style-parent:"";
	margin-bottom:.0001pt;
	font-size:12.0pt;
	font-family:"Times New Roman";
	margin-left:0in; margin-right:0in; margin-top:0in}
.style1 {
	font-family: <PERSON>l, Helvetica, sans-serif;
}
-->
</style>
</head>

<body leftmargin="0" topmargin="5" marginwidth="0" marginheight="0">

<table width="100%"  border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td  width="180" valign="top" bgcolor="#F4F4F4">
	<table width="94%"  border="0" cellspacing="0" cellpadding="2" align="center">
      <tr>
        <td width="160" valign="top"><br>
  
		<img src="images/space.gif" width="160" height="1"><br>
	<br>
	<table border = 1>
	<tr><td bordercolor="#F4F4F4">
		<font face="Arial" size="1">Entry Supervisor</font></td>
		<td bordercolor="#F4F4F4" align="center"><font face="Arial" size="1">Shift</font></td>

	<td align = center bordercolor="#F4F4F4">
	<font face="Arial" size="1">Last Training</font> </td></tr>
		<% Dim strsql, MyRec
		

	strSQL = "SELECT tblAirEntrySups.NID,  tblAirEntrySups.Work_area, work_shift, tblAirEntrySups.P_BID, tblAirEntrySups.P_Name, "_
			&"	tblAirEntrySups.P_Training FROM tblAirEntrySups  "_
			&" WHERE tblAirEntrySups.Work_area ='" & strWA & "' and tblAirEntrySups.P_Type = 'S'"
		       
		       
		Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly 
			While not MyRec.eof
			%>
	<tr><td align = center><font face="Arial" size="1"><%= MyRec.fields("P_name")%></font></td>
	<td align = center><font face="Arial" size="1"><%= MyRec.fields("Work_Shift")%>&nbsp;</font></td>

	<td align = center>
	<font face="Arial" size="1"  <% if datediff("d", MyRec.fields("P_training"), strdate) > 365 then %>color = "red"<% end if %>>
	<%= MyRec.fields("P_Training")%></font></td></tr>
	
	<%   
       MyRec.MoveNext
     Wend
     MyRec.close %>
     </table>
<br><br><br>
		
<table border = 1 width = 100%>
		<tr><td bordercolor="#F4F4F4">
			<p align="center"><font face="Arial" size="1">Air Testers</font></td>
			<td bordercolor="#F4F4F4" align="center"><font face="Arial" size="1">Shift</font></td>

			<td bordercolor="#F4F4F4">	
			<p align="center">	<font face="Arial" size="1">Last Training</font> </td></tr>
	
		<% strSQL = "SELECT tblAirEntrySups.NID,  tblAirEntrySups.Work_area, work_shift, tblAirEntrySups.P_BID, tblAirEntrySups.P_Name, "_
		&"	tblAirEntrySups.P_Training FROM tblAirEntrySups  "_
			&" WHERE tblAirEntrySups.Work_area ='" & strWA & "' and tblAirEntrySups.P_Type = 'A'"
		       
			Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly 
			While not MyRec.eof
			%>
			
	<tr><td align = center><font face="Arial" size="1"><%= MyRec.fields("P_name")%></font></td>
	<td align = center><font face="Arial" size="1"><%= MyRec.fields("work_shift")%>&nbsp;</font></td>

	<td align = center>
	<font face="Arial" size="1" <% if datediff("d", MyRec.fields("P_training"), strdate) > 365 then %> color = "red" <% end if %>>
	<%= MyRec.fields("P_Training")%></font></td></tr>
	
	<%   
       MyRec.MoveNext
     Wend
     MyRec.close %>
</table>
	
	
&nbsp;</td>
      </tr>
    </table></td>


    <td valign="top" style="width: 791px">
	<img border="0" src="images/bg_blueProducts.gif" width="801" height="32"><br>
      <table width="801" border="0" cellspacing="0" cellpadding="1">
        <tr>
          <td width="73%" valign="top">
		  	<table width="98%"  border="0" cellspacing="0" cellpadding="1" align="center">
            <tr>
              <td bgcolor="#FFFFFF" align = center><font size="4"><b><font color = black face="Arial, Helvetica, sans-serif">
		<%= strWA%></b>	</font><br>
          
				<br>
				
	<% strSQL = "SELECT P_Name from tblAuthorizers where Work_area ='" & strWA & "' and P_Type = 'A'  and P_Heading = 'Y'"
		       
		       
			 Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
			
			Dim strAdministrator
			If not myrec.eof then
			strAdministrator = MyRec.fields("P_Name")
			else
			strAdministrator = "There is not an Administrator for this Area"
			end if 
			MyRec.close
			
 %>
			
                  </font><b><font face="Arial">Team Administrator:&nbsp;<%= strAdministrator%></font></b><font size="4"><br>
                  <br>
            <table border = 1 cellspacing = 0 cellpadding = 0 width = 95% bordercolor="#F4F4F4"><tr>
             <td align="center" ><font face="Arial" size="1">Space ID</font></td>
            <td align="center" ><font face="Arial" size="1">Space Name</font></td>
            <td align="center" ><font face="Arial" size="1">Permit #</font></td>
			<td align="center" > <font face="Arial" size="1">Date of <br>Issue</font></td>
			<td align="center" ><font face="Arial" size="1">Date<br> Expires</font></td>
			<td align="center" ><font face="Arial" size="1">Review<br> Complete</font></td>
		<td align="center" ><font face="Arial" size="1">Entry<br>Status</font></td>

			<td align="center">&nbsp;</td></tr>
            
      <% strsql = "SELECT tblSOP.SOP_NO, tblSOP.SDescription, tblPermit.Date_issued, tblPermit.Date_expired, tblSOP.WorkArea, "_
      &" tblPermit.Permit_status, tblPermit.Status_date,  tblPermit.Safety_Date, tblPermit.PID, tblPermit.entry_status "_
		&" FROM tblPermit INNER JOIN tblSOP ON tblPermit.Space_ID = tblSOP.SOP_NO "_
		&" WHERE tblSOP.WorkArea='" & strWA& "'   and (Permit_status is null or permit_status = 'Review Complete') and Safety_BID is null order by Date_Expired desc "
       Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly 
			While not MyRec.eof %>
           <tr>
            
            <td align="center" ><font face="Arial" size="1"><%= MyRec.fields("SOP_NO") %></font></td>
			<td align="left" > 	<font face="Arial" size="1"><%= MyRec.fields("Sdescription") %></td>
				<td align="center" ><font face="Arial" size="1"><a href="Permit_History.asp?id=<%= MyRec.fields("PID")%>"><%= MyRec.fields("PID") %></a></td>

			<td align="center"><font face="Arial" size="1">&nbsp;<%= MyRec.fields("Date_issued") %>&nbsp;</td>
			<td align="center" ><font face="Arial" size="1">&nbsp;<%= MyRec.fields("Date_expired") %>&nbsp;</td>
			<td align="center" ><font face="Arial" size="1">&nbsp;<%= MyRec.fields("Status_date") %></font></td>	
						<td align="center" ><font face="Arial" size="1">&nbsp;<%= MyRec.fields("Entry_Status") %></font></td>	
			<td align="center" ><font face="Arial" size="1"><a href="Permit_Status_edit.asp?id=<%= MyRec.fields("PID") %>&wa=<%= strWA%> ">Edit</a></font></td>
			</tr>
 <%   
       MyRec.MoveNext
     Wend
     MyRec.close %>
            
            </table>
<br>
<font face = arial size = 1><a target =" _blank" href="Permits_not_used.asp?id=<%= strWA%>">View Permits Not Used</a></font>
<br>



</b></i></font></font></td>
            </tr>
          </table></td>
          <td width="26%" valign="top" bgcolor="#D6D6F5" align = center><font size="2" face="Arial, Helvetica, sans-serif"><br>
        

 &nbsp;<font size = 2>HA STATUS COUNT<br>
          
          
        <% strsql = "SELECT tblHA.HA_Status, Count(tblHA.IDHazAssess) AS StatusCount FROM tblHA "_
        &" INNER JOIN tblSOP ON tblHA.SpaceID = tblSOP.SOP_NO "_
 		 &" where tblSOP.WorkArea = '" & strWA & "' and tblSOP.SpaceStatus <> 'Removed' GROUP BY tblHA.HA_Status"

Set MyConn = Server.CreateObject("ADODB.Recordset") 

   		MyConn.Open strSQL, Session("ConnectionString")%>

         <table>
  
          <% While not MyConn.eof  %>
            <tr><td><font face = arial size = 1><%= MyConn.fields("HA_Status")%></td><td><font face = arial size = 1><%= MyConn.fields("StatusCount")%></td></tr> 
          <%
           MyConn.MoveNext
     Wend
     MyConn.close %>
     </table>
          
      <br><br><br>
      <table border = 1 bordercolor="#000000"><tr><td bordercolor="#D6D6F5"><font face = arial size = 2>
      HA Approvers</td><td bordercolor="#D6D6F5"><font face = arial size = 2>Hourly/Salary</td></tr>
      
      <%	strSQL = "SELECT tblApprovers.* from tblApprovers where Work_area = '" & strWA & "'"
      Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
   		 While not MyConn.eof%>
   		     <tr><td align = center><font face = arial size = 1>
   		    <a href="Approver_permits.asp?id=<%= MyConn.fields("P_name")%>"> <%= MyConn.fields("P_name")%></a></td><td align = center><font face = arial size = 1><%= MyConn.fields("P_type")%>&nbsp;</td></tr> 
         
         <%
           MyConn.MoveNext
     Wend
     MyConn.close %>
     </table>
          &nbsp;</font></td>


        </tr>
     
      </table></td>
      <td  bgcolor="#C5C5C5">&nbsp;</td>


</tr></table>


</body>

</html>

<!--#include file="footer.inc"-->
                                       