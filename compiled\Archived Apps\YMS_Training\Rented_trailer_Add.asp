																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Add Rented Trailer </TITLE>


<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 



<% Dim MyRec, strsql, MyRec2, strsql2,  MyConn3, strsql3, strid



  set objGeneral = new ASP_CLS_General
  if objGeneral.IsSubmit() Then	

  

	
        	
  	
	strsql =  "Insert into tblRentedTrailer (Trailer_nbr) Select '" & request.form("Trailer") & "'"
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
Response.redirect("Rented_trailers.asp")		
end if

	
%>

<style type="text/css">
.style3 {
	border: 1px solid #000000;
}
.style5 {
	border: 1px solid #C0C0C0;
	background-color: #FFFFD7;
}
.style6 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: arial;
	font-size: small;
	text-align: center;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style7 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	text-align: center;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
</style>
</head>

<body>
<br>
	
<form name="form1" action="Rented_trailer_Add.asp" method="post" ID="Form1">
<TABLE cellSpacing=0 cellPadding=0 width=100% align = center class="style5">

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Add Trailer Number</b></font></td>
<td align = right><font face="Arial"><a href="Rented_Trailers.asp">Return</a>&nbsp;</td>


</tr>
	    </table>



	<p>&nbsp;</p>
	<table id="table1" style="width: 25%" align="center" class="style3">
<tr bgcolor = #CCCCFF>
		<td bgcolor="#FFFFD7" class="style6" style="height: 67px">Trailer Number</td>
	</tr>
	<tr>
	
		<td class="style7" style="height: 75px">
		<input type="text" name="Trailer" size="20" style="width: 145px" tabindex="1" value="" ></td>
	</tr>
	
</table>
<br>
		

<p align="center"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></p>
</form>

<!--#include file="Fiberfooter.inc"-->