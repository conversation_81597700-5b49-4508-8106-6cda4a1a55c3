																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Edit Shuttles from PJ Warehouse</TITLE>
<style type="text/css">
.auto-style1 {
	font-family: Calibri;
}
.auto-style2 {
	font-family: Calibri;
	font-weight: bold;
	font-size: x-small;
}
.auto-style3 {
	font-family: <PERSON>ibri;
	font-size: x-small;
}
.auto-style4 {
	font-size: x-small;
}
.auto-style5 {
	font-family: <PERSON>ibri;
	font-weight: bold;
}
</style>
</head>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->




<% Dim <PERSON>, strsql, MyRec2, strsql2, strtdate, strdate

strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -4, strtdate)

strsql = "SELECT tblCars.* FROM tblCars WHERE  (Entry_page = 'Transfer_From_PJ.asp' or Entry_Page = 'Transfer_From_PJ_Release.asp') and Entry_time > '" & strdate & "' order by CID desc"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=80%  border=1>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center class="auto-style5">Edit Transfers from PJ Warehouse</td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=50% class = "tablecolor1" border=1 align="center">  
	 <tr class="tableheader">
<td class="auto-style3">&nbsp;</td>
			<td  > <p align="center">       <font size="1" class="auto-style3">Print<br> Receipt</font></td>
		<td  > <p align="left" class="auto-style3">       Receipt Number</td>
	
		<td class="auto-style3"  > Release Number</td>
	
		<td class="auto-style2"  >Trailer</td>
			<td class="auto-style2"  >Location</td>

		<td class="auto-style2"  >Carrier</td>
		<td class="auto-style3"  >Species</td>
  
		<td  > <p align="center" class="auto-style3"> Tons</td>
		<td  > <p align="left" class="auto-style3"> Date Transferred</td>
	

	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td class="auto-style3"> <a href="Transfer_From_PJ_Edit.asp?id=<%= MyRec.fields("CID") %>">Edit</a></td>

	<td align = center class="auto-style3"> <a href="Transfer_PJreceipt.asp?id=<%= MyRec.fields("CID") %>">Print</a></td>
	<td class="auto-style3"  ><%= MyRec.fields("CID") %></td>

	<td class="auto-style3"  ><%= MyRec.fields("PS")%>&nbsp;</td>

	<td class="auto-style2"  > <%= MyRec.fields("Transfer_Trailer_nbr")%></td>
		<td class="auto-style2"  > <%= MyRec.fields("Location")%></td>
			<td  ><b><font size="1" face="Arial"><span class="auto-style1">
			<span class="auto-style4"><%= MyRec.fields("Trans_Carrier")%>&nbsp;</span></span></font></b></td>


		<td class="auto-style3"  > <%= MyRec.fields("Species")%></td>
	
		<td class="auto-style3"  >        <%= MyRec.fields("Tons_received")%></td>
	<td class="auto-style3"  >  <%= MyRec.fields("Transfer_date")%></td>
	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>


<!--#include file="Fiberfooter.inc"-->