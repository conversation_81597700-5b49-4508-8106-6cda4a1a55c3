<html>
<script>window.history.go(1);</script>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionPolaris.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Edit KDF Receipt</title>
</head>
<%
    Dim strSQL, MyRec, strID, strPO, MyConn, objMOC, rstFiber, strPS, strAT, strUOM, strNewID, strPaper, strBrand 
    Dim strTrailer, strCarrier
    Dim strPONbr
    Dim strRECNbr, gVendorName, strDateUnloaded, strSpecies

    Dim strTonsReceived
    Dim strDateReceived

    Dim strOther, strR, strsql3, gDescription, gPO, gSAP_Nbr, gVendor, gOID, strNet, strMultiLoad

    strid = Trim(Request.QueryString("id")) 
    strpage = request.querystring("p")
	

 	set objGeneral = new ASP_CLS_General

  

if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close

 	strTrailer = Request.form("Trailer")

	strDateReceived = Request.form("Date_received")
	strRECNbr = Request.form("Rec_Number")
	strCarrier = Request.form("Carrier")
	strUOM = Request.form("UOM")

	strTonsReceived = Request.form("Tons_received")


	If isnull(strTrailer) or strTrailer = ""  or isnull(strCarrier) or strCarrier = ""  or isnull(strTonsReceived) or strTonsReceived = "" or isnull(strDateReceived) or strDateReceived = "" then
	
	Response.redirect("Edit_NFReceipt.asp?id=" & strid)
	else
	Call SaveData() 
	Response.redirect ("NF_Sixty_Day_List.asp")
	end if
	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to save this information.</font></br>")
	MyRec.close
	end if

  
ELSE
  Call GetData()
	strsql = "SELECT * from tblCars where CID = " & strid 

   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")



	strVendor = MyRec.fields("Vendor")
	strCarrier = MyRec.fields("Carrier")
	strPaper = MyRec.fields("Paperwork")
	strSAP = MyRec.fields("SAP_NBR")
	strLocation = MyRec.fields("Location")
	strComments = MyRec.fields("Other_Comments")
	strRecNbr = MyRec.fields("REC_Number")
	strBrand = MyRec.fields("Brand")
	strTonsReceived = MyRec.fields("Tons_received")
	strUOM = MyRec.fields("UOM")
	strPONbr = MyRec.fields("PO")
	strTrailer  = MyRec.fields("Trailer")
	strDateUnloaded = Myrec.fields("Date_Unloaded")
	strDateReceived = MyRec.fields("Date_received")
	strPS = MyRec.fields("PS")
	strAT = MyRec.fields("Asset_Team")
	strSpecies = MyRec.fields("Species")
	strBrand = MyRec.fields("Brand")

	MyRec.close


end if
%>
<body>
<table width = 100%><tr><td width = 33%>
<font face="Arial" size="2">

</td><td align = center width = 34%><b><font face="Arial" size="4">
	Edit Trailer Receipt </font> </b></td>
<td align = center><font face="Arial" size="2"><b><td align = right><b>
		<font face="Arial"><a href="KDF_Edit_received.asp">RETURN</a></font></b></td>
</tr></table>




<form name="form1" action="Edit_KDF.asp?id=<%=strid%>&p=<%= strpage %>" method="post">
<div align="center">
<table border="1" cellspacing="0" bordercolor="#808080" width="65%" bgcolor="#FFFFE1" style="border-collapse: collapse" cellpadding="0" bordercolorlight="#C0C0C0">
<tr>
    <td bgcolor="#FFFFDF" width="19%">&nbsp;</td>

    <td  bgcolor="#FFFFDF" colspan="4">&nbsp;</td>
  </tr>
    <tr>
    <td  align = left bgcolor="#FFFFDF" width="19%" >
   <b>
   <font face="Arial" size="2">PO Number:&nbsp;</font></b></td>
<td  align = left bgcolor="#FFFFDF" width="40%" colspan="3">

     <font face="Arial" size="2"><%= strPONbr%>

  </td>
<td  align = left bgcolor="#FFFFDF" width="40%">

		<font face="Arial"><input type="checkbox" name="Paperwork" value="ON" <% if strPaper = 1 then %>checked<% end if %>></font><b><font face="Arial" size="2">Paper 
	work doesn't match&nbsp; </font></b>

  </td>
</tr>
  <tr>
    <td  align = left bgcolor="#FFFFDF" width="19%" >
   <b><font face="Arial" size="2">Vendor Receipt #:</font></b></td>
<td  align = left bgcolor="#FFFFDF" colspan="4">

      <font face="Arial">

      <input name="REC_Number" size="24" value = "<%= strRECNbr%>" style="font-weight: 700"></font></td></tr>
  <tr>

      <td  bgcolor="#FFFFDF" align = left width="19%">
  <font face="Arial" size="2"><b>Select Carrier:&nbsp;&nbsp;</b></font></td>
<td  align = left bgcolor="#FFFFDF" colspan="4">

      <font face="Arial">   
	<select name="Carrier" style="font-weight: 700">
 	<option value="" selected>  Select Carrier (Required)</option>
      <%= objGeneral.OptionListAsString(rstFiber, "Carrier", "CDesc", strCarrier) %>
     </select></font></td></tr>
<tr>
    <td  bgcolor="#FFFFDF" align="left" width="19%">  
	<p>  <b><font face="Arial" size="2">Trailer</font></b><font face="Arial" size="2"><b>:&nbsp;&nbsp;</b></font></td>
    <td bgcolor="#FFFFDF" colspan="4">   <font face="Arial">   

      <input name="Trailer" size="24" value = "<%= strTrailer%>" style="font-weight: 700"></font>
      </td>
  </tr>

       <tr>  <td  bgcolor="#FFFFDF" align = left width="19%"> <font face="Arial" size="2">
			<b>Quantity:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFDF" colspan="4">    <font face="Arial">  
<input name="Tons_Received" size="15" value = "<%= strTonsReceived%>" style="font-weight: 700"> </td></tr>

<tr><td  bgcolor="#FFFFDF" align="left" width="19%"><font face="Arial" size="2"><b>Unit of 
	Measure:&nbsp;</b></font></td>
    <td  bgcolor="#FFFFDF" colspan="4">    <font face="Arial">  
	<input name="UOM" size="15" value = "<%= strUOM%>" style="font-weight: 700"></td>
  </tr>
       <tr><td  align = left bgcolor="#FFFFDF" width="19%" ><font face="Arial" size="2"><b>
		Date Received:</b></font></td>
<td align = left bgcolor="#FFFFDF" colspan="4"> <font face="Arial"> 
<input name="Date_Received" size="15" value = "<%= strDateReceived%>" style="font-weight: 700"></font></td></tr>
       <tr><td  align = left bgcolor="#FFFFDF" width="19%" ><font face="Arial" size="2"><b>Date 
		Unloaded:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFDF" colspan="4"> <font face="Arial"> 
<input name="Date_unloaded" size="21" value = "<%= strDateUnloaded%>" style="font-weight: 700"></font></td></tr>
              <tr>
          <td  align = left bgcolor="#FFFFDF" width="19%" >
   <font face="Arial" size="2"><b>Vendor:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFDF" colspan="4">
    <font face="Arial" size="2">
	<%= strVendor%></TD></tr>
   
              <tr>
          <td  align = left bgcolor="#FFFFDF" width="19%" >
   <b><font face="Arial" size="2">Commodity:</font></b></td>
<td align = left bgcolor="#FFFFDF" colspan="4"> <font face="Arial" size="2">
<%= strSpecies%></TD></tr>
   
              <tr>
          <td  align = left bgcolor="#FFFFDF" width="19%" >
   <font face="Arial" size="2"><b>SAP #:&nbsp;</b></font></td>

<td align = left bgcolor="#FFFFDF"> <font face="Arial" size="2">


<%=  strSAP %></TD>
<td align = left bgcolor="#FFFFDF" colspan="3">

		<b><font face="Arial" size="2">Brand</font></b><font face="Arial" size="2"><b> #:&nbsp;</b><%=  strBrand%></TD></tr>
   
              <tr>
          <td  align = left bgcolor="#FFFFDF" width="19%" >
   <b><font face="Arial" size="2">Product System</font></b><font face="Arial" size="2"><b>:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFDF" colspan="4"> <font face="Arial" size="2">

	<%= strPS %>	</TD></tr>
   
              <tr>
          <td  align = left bgcolor="#FFFFDF" width="19%" >
   <font face="Arial" size="2"><b>Asset Team:</b></font></td>
<td align = left bgcolor="#FFFFDF" colspan="2"> <font face="Arial" size="2"><%= strAT %></option>
</TD>
<td align = left bgcolor="#FFFFDF" colspan="2">

		<font face="Arial">
		<b><font size="2"> 
	&nbsp;</font></b><input type="checkbox" name="Lookup" value="ON" <% if strPaper = 1 then %>checked<% end if %></TD>&nbsp;
		<b><font size="2">Check to Print Receipt</font></b></tr>
   
        
<tr>
    <td  bgcolor="#FFFFDF" width="19%">&nbsp;</td>

    <td bgcolor="#FFFFDF" colspan="4">&nbsp;</td>
  </tr>

  <tr>
    <td bgcolor="#FFFFDF" width="19%">&nbsp;</td>

    <td align = left bgcolor="#FFFFDF" colspan="4"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" >&nbsp;&nbsp;&nbsp; 
	</font></td>
  </tr>
</table>

</div>

</form>
</body>
<% Function GetData()
  set objMOC = new ASP_CLS_FIBER
        set rstFiber = objMOC.FiberCarrier()
End Function

Function SaveData()

strid = request.querystring("id")


	
	strDateReceived = Request.form("Date_Received")
	
	If Request.form("Paperwork") = "ON" then
	strPaper = 1
	else
	strPaper = 0
	end if
	
	
	
	Dim strRightNow
	strRightnow = Now()
	

	
	If len(request.form("Date_unloaded")) > 7 then
	strsql =  "UPDATE tblCars set Date_unloaded = '" & Request.form("Date_unloaded") & "',  "_
	&"  Paperwork = " & strPaper & ", Carrier = '" & strCarrier & "', "_

	&" Date_received = '" & strDateReceived & "', Trailer = '" & strTrailer & "',  REC_Number = '" & strRecNbr & "', "_
	&" Tons_Received = " & strTonsReceived & ", Entry_Time =  '" & strRightNow & "', Entry_BID = '" & Session("EmployeeID") & "', "_
	&"   UOM = '" & strUOM & "' where CID = " & strid
	else
	strsql =  "UPDATE tblCars set  Paperwork = " & strPaper & ", Carrier = '" & strCarrier & "', "_
	
	&" Date_received = '" & strDateReceived & "', Trailer = '" & strTrailer & "',  REC_Number = '" & strRecNbr & "', "_
	&" Tons_Received = " & strTonsReceived & ", Entry_Time =  '" & strRightNow & "', Entry_BID = '" & Session("EmployeeID") & "', "_
	&" UOM = '" & strUOM & "' where CID = " & strid
	end if

	
	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
			if strpage = "e" then
			Response.redirect("NF_Sixty_Day_list.asp")
			else
	
Response.redirect("KDF_Edit_received.asp")
end if
End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->