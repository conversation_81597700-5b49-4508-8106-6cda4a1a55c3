<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Enter Rail Receipt</title>
<style type="text/css">
.style1 {
	font-size: x-small;
}
.style2 {
	font-family: Arial, Helvetica, sans-serif;
}
.style3 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID, strRelease, MyConn, objMOC, rstFiber, rstSpecies, strKCWeighed, strCarId, MyRec5, strsql5
    Dim strTrailer, strCarrier, strLocation, strSAP
    Dim strReleaseNbr, rstTrailer , strTrailerWeight , strTractor, strTrailerTID, strpounds
 
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strOther, strR, strsql3, strSpecies, strSAP_Nbr, strVendor,  strNet, strPO


 	set objGeneral = new ASP_CLS_General

if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
	strSpecies = "Unresolved"
	
 	strTrailer = Request.form("Trailer")
	strOther = Replace(Request.form("Other_Comments"), "'", "''") 
	strDateReceived = Request.form("Date_received")
	strpounds = Request.form("Tons_Received") * 2000
	
	strCarrier = "RAIL"
	strLocation = Request.form("Location")
	
		
	

	Call SaveData() 
		If Request.form("Print_receipt") =  "ON" then
	Response.redirect("Rail_receiptVFG.asp?id=" & strCarID )

		else
		Response.redirect ("Select_rail.asp")
		end if
	
	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to save this information.</font></br>")
	MyRec.close
	end if

  

end if
%>



<body>


<table width = 100%><tr><td width = 33%>
&nbsp;</td><td align = center width = 34%><b><font face="Arial" size="4">
Enter Rail Receipt </b></font> </b></td><td align = right width = 33%><b><font face = arial size = 2><a href="javascript:history.go(-1);">RETURN</a></font></b></td></tr></table>




<form name="form1" action="Generic_receipt_rail.asp?r=<%=strRelease%>" method="post">
<div align="center">
<table border="1" cellspacing="0" bordercolor="#C0C0C0" width="63%" bgcolor="#FFFFEA" style="border-collapse: collapse" cellpadding="0">

<tr>
    <td bgcolor="#FFFFEA" align="right"><b><font face="Arial" size="2">&nbsp;</font></b></td>

    <td  align = center bgcolor="#FFFFEA" colspan="2">&nbsp; 
	</td>
  </tr><tr>
    <td bgcolor="#FFFFEA" align="right"><b><font face="Arial" size="2">Species:&nbsp;</font></b></td>

    <td  bgcolor="#FFFFEA"> &nbsp;<font face="Arial"><strong><span class="style1">Unresolved</span></strong>
   </font> </td>

    <td  bgcolor="#FFFFEA" width="45%"> 
	<p align="center"><font face="Arial"><font size="2" face="Arial"><b>Check 
	to Print Receipt:&nbsp;
</b></font> <input type="checkbox" name="Print_receipt" value="ON" checked></td>

  </tr>
  
    <tr>
    <td bgcolor="#FFFFEA" align="right">&nbsp;</td>

    <td  bgcolor="#FFFFEA" colspan="2"> 
     <font face="Arial"><strong>&nbsp;</strong></font></td>
  </tr>
  
  <tr>
    <td  align = right bgcolor="#FFFFEA" height="29" >
   <b>
   <font face="Arial" size="2">Rail Car Number:&nbsp;</font></b></td>
<td  align = left colspan="2" height="29" bgcolor="#FFFFEA">

      <input type="text" name="Trailer" size="15" >&nbsp;
		<font face="Arial" size="2"><b>(Required)</b></font></td></tr>
  <tr>

      <td  bgcolor="#FFFFEA" align = right height="28">
	<font face="Arial" size="2"><b>Carrier: </b></font></td>
<td  align = left colspan="2" height="28" bgcolor="#FFFFEA">

     <font face="Arial" size="2"> &nbsp;<strong>RAIL</strong></td></tr>
<tr>
    <td  bgcolor="#FFFFEA">  
	<p align="right">  <font face="Arial" size="2"><b>&nbsp;</b></font></td>
    <td bgcolor="#FFFFEA" colspan="2">   &nbsp;</td>
  </tr>

<tr>
	<td  bgcolor="#EEEDDD" height="22" width="17%">
	<p align="right"><b><font face="Arial" size="2">&nbsp;Tons Received:</font></td>
    <td  bgcolor="#EEEDDD" height="22" width="21%" colspan="2">      
	
      <b><font face="Arial" size="2"> <input name="Tons_Received" size="15" value = "<%= strTonsReceived%>" >&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </font></td>
  </tr>
<tr>
	<td  bgcolor="#FFFFEA" height="22" width="17%">&nbsp;</td>
    <td  bgcolor="#FFFFEA" colspan="2" height="22">&nbsp;</td>
  </tr>

       <tr><td  align = right bgcolor="#FFFFEA" ><font face="Arial" size="2"><b>Date Received:&nbsp;</b></font></td>
<td align = left colspan="2" bgcolor="#FFFFEA"> <input type="text" name="Date_Received" size="15" value = <%= formatdatetime(Now(),2)%>></td></tr>
             
         <tr>
          <td  align = right bgcolor="#FFFFEA" >
  <font face="Arial" size="2"><b>Other:&nbsp;</b></font></td >
   <td align = left colspan="2" bgcolor="#FFFFEA">   <input type="text" name="Other_Comments" size="25">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font face="Arial"><font size="2">&nbsp;</font></td></tr>
	<tr> <td  align = right bgcolor="#FFFFEA" class="style2" >	<strong>
		<span class="style3">&nbsp;Location</span>: 
	</strong>  </td><td colspan="2">

	<select name="Location" style="font-weight: 700" size="1">

      <option selected>YARD</option>
 
     </select></font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	</td></tr>

<tr>
    <td  bgcolor="#FFFFEA">&nbsp;</td>

    <td bgcolor="#FFFFEA" colspan="2"> 
	<Input name="Update" type="submit" Value="Submit"  ></td>
  </tr>


</table>

</div>

</form>
</body>
<%

Function SaveData()
dim strbody, strCID, strECarrier, strETrailer, strELoad, strEDoc, strEDC, strEDCName, strEAdd, strECity, strEState, MyConn2, strsql4
dim strECC, strEBCC

             

   	 
if len(request.form("Tons_Received")) > 0 then
		strTonsReceived = Request.form("Tons_Received")
	 if  cint(strTonsReceived) > 0 then
	
		strKCWeighed = ""
		

	strGrossWeight = 0
	
    strNet = strTonsReceived
	end if 

	end if
	


	
	Dim strRightNow
	strRightnow = now()
	
	strsql =  "INSERT INTO tblCars ( PO, Carrier, Species, Grade, Date_received,  Location_unloaded, Location,   Trailer, Other_Comments,  Tons_Received, Net, Entry_Time, Entry_BID, Entry_Page, Status ) "_
	&" SELECT  'UNKNOWN', 'RAIL', 'UNKNOWN', 'RF',  '" & strDateReceived & "', 'RF', 'YARD',   "_
	&" '" & strTrailer & "', '" & strOther & "',  " & strTonsReceived & ", " & strTonsReceived & ",  '" & strRightNow & "', '" & Session("EmployeeID") & "', 'Generic_receipt_rail', " & strTonsReceived & ""
	
	
	
	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
				strsql5 = "SELECT Max(tblCars.CID) AS MaxOfCID FROM tblCars WHERE tblCars.Trailer = '" & strTrailer & "'"
			
   	 Set MyRec5 = Server.CreateObject("ADODB.Recordset")
   	 MyRec5.Open strSQL5, Session("ConnectionString")
   	 strCarID = MyRec5.fields("MaxofCID")
   	 MyRec5.close
		
			
			strEmailTo = "<EMAIL>"
        	'strECC = "<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>"
            strEBCC = "<EMAIL>"
                 
			Set objMail = Server.CreateObject("CDO.Message")		
			objMail.From = "<EMAIL>"
			objMail.To = strEmailTo
			'objMail.CC = strECC
			objMail.BCC = strEBCC
			
			objMail.Subject = "Rail Receipt for Car#: " & strTrailer & " Entered as an Exception "
			objMail.HTMLBody = "<font face = arial size = 2>Rail Car # " & strTrailer & " Receipt #: " & strid & " was entered as an exception. ."
			' objMail.Send
			Set objMail = nothing


End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->