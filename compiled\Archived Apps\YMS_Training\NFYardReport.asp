																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>KDF Yard Inventory Report</TITLE>
<style type="text/css">
.auto-style1 {
	text-align: center;
}
</style>
</head>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->



<% Dim MyRec, strsql, strDays, strDate, gSpecies, gCount, gTCount, strsql3, MyRec3

strdate = formatdatetime(now(),2)
gcount = 0
gSpecies = ""

strsql = "SELECT tblCars.*,  [date_received] AS Sort_Date FROM tblCars WHERE (Location = 'YARD' or Location = 'AT DOOR') and (((tblCars.Date_received) Is Not Null) AND (Date_unloaded is null and Trans_unload_date is null) AND ((tblCars.Trailer) Is Not Null))  and Species = 'KDF' ORDER BY  Sort_Date "   
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=0>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><b>
<font face="arial" size="4" >KDF Yard Inventory Report for <%= strDate%></font></b></span></td>
<td align = center>&nbsp;</td>


</tr>
	    </table>
<p align="right"><font face="Arial">Physically Checked by 
_______________________<br>Signature&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </font>&nbsp; </p>

	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">


		<td align = center  ><font face="Arial" size="1"><b>Date <br>Received</b></b></font></td>
		<td align = center  ><font face="Arial" size="1"><b>Door</b></b></font></td>
    	<td ><font face="Arial" size="1"><b>&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; Trailer</b></font></td>
		<td ><font face="Arial" size="1"><b>Carrier</b></font></td>
			<td ><font face="Arial" size="1"><b>Brand</b></font></td>
				<td ><font face="Arial" size="1"><b>SAP #</b></font></td>
					<td ><font face="Arial" size="1"><b>SPEC #</b></font></td>

	<td ><font face="Arial" size="1"><b>REC Nbr</b></font></td>
	<td ><font face="Arial" size="1"><b>PO</b></font></td>
<td align = center><font face="Arial" size="1"><b>Vendor</b></font></td>


<td align=center><font face="Arial" size="1"><b>To Date<br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="1"><b>Days <br>in Yard</b></font></td>


<td align = center ><font face="Arial" size="1"><b>Checkoff</b></font></td>
	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
       strNFID = MyRec("NFID")
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    
    <% if  MyRec.fields("Species") = gSpecies or gcount = 0 then  
    if len(mYrec("Brand")) > 1 then 
    strBrand = MyREc("Brand")
    'do nothing
    else
    strSAP = MyRec("SAP_Nbr")
    

strsql2 = "Select Brand from tblCars where SAP_Nbr = " & strSAP & " "
 Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL2, Session("ConnectionString")
    If not MyRec2.eof then
    strBrand = MyRec2("Brand")
    end if
    MyRec2.close
    end if %>
        
   

	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Door")%></font></td>
		<td  > <font size="2" face="Arial"> &nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;<%= MyRec.fields("Trailer")%></font></td>
	<td  >   <font size="2" face="Arial">        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">        <%= strBrand %>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">        <%= MyRec.fields("SAP_Nbr")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">
	
<% if len(strNFID) > 1 then 

strsql2 = "Select Spec from tblSapOpenPO where OID = " & strNFID
 Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL2, Session("ConnectionString")
    If not MyRec2.eof then %>
    <%= MyRec2("Spec") %>
    <% end if 
    MyRec2.close%>
<% end if %>
       &nbsp;</font></td>
	<% if isnull(MyRec.fields("Rec_number")) then %>
		 <td  >  <font size="2" face="Arial">        <%= MyRec.fields("CID")%>&nbsp;</font></td>
		 <% else %>
	 <td  >  <font size="2" face="Arial">        <%= MyRec.fields("REC_number")%>&nbsp;</font></td>
	 <% end if %>

		

		
 
   <td class="auto-style1"  >  <font size="2" face="Arial">        <%= MyRec.fields("PO")%>&nbsp;</font></td>
   <% if MyRec.fields("Vendor") = "WEYERHAEUSER COMPANY" then %>
     <td  align = center>  <font size="2" face="Arial">IP</font></td>
   <% else %>
    <td  align="center" >  <font size="2" face="Arial">        <%= MyRec.fields("Vendor")%>&nbsp;</font></td>
   <% end if %>
          
         <% 
             strDays = round(Now()- cdate(MyRec.fields("Date_received")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

       
          
 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    if strDays  > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(strDays) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"><%= strDays%></font></td>	
	

	<td ALIGN = CENTER><font size="2" face="Arial"><input type="checkbox" name="C1" value="ON">&nbsp;</font></td>
	</tr>

 <%    gcount = gcount + 1
       gTcount = gTcount + 1
 		gSpecies = MyRec.fields("Species")
       ii = ii + 1
       MyRec.MoveNext
    
    %>
<% else 
   if len(mYrec("Brand")) > 1 then 
    strBrand = MyREc("Brand")
    'do nothing
    else
    strSAP = MyRec("SAP_Nbr")
    

strsql2 = "Select Brand from tblCars where SAP_Nbr = " & strSAP & " "
 Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL2, Session("ConnectionString")
    If not MyRec2.eof then
    strBrand = MyRec2("Brand")
    end if
    MyRec2.close
    end if %>

<td colspan = 15><font face = arial size = 2><b></b>Total:&nbsp;<%= gcount%></b></font></td>


	</tr>
	<TR><td colspan = 15>&nbsp;</td>


	</tr>
	
	  <tr class=tablecolor2>
        
        <% if isnull(MyRec.fields("Transfer_date")) then %>
        		<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
        			<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Door")%></font></td>
        		<td  > <font size="2" face="Arial">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <%= MyRec.fields("Trailer")%></font></td>
        		<td  >    <font size="2" face="Arial">        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
        			<td  >   <font size="2" face="Arial">        <%= MyRec.fields("Brand")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">        <%= MyRec.fields("SAP_Nbr")%>&nbsp;</font></td>
<td>   <font size="2" face="Arial">
	
<% if len(strNFID) > 1 then 

strsql2 = "Select Spec from tblSapOpenPO where OID = " & strNFID
 Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL2, Session("ConnectionString")
    If not MyRec2.eof then %>
    <%= MyRec2("Spec") %>
    <% end if 
    MyRec2.close
    end if %>

       &nbsp;</font></td>

        		<% if isnull(MyRec.fields("Rec_number")) then %>
        		        		  <td> <font size="2" face="Arial">        <%= MyRec.fields("CID")%></font></td>
        		        		  <% else %>
        		  <td> <font size="2" face="Arial">        <%= MyRec.fields("REC_number")%></font></td>
        		  <% end if %>
        <% else %>
       
		<td   ><font size="2" face="Arial">SHUTTLE - <%= MyRec.fields("Transfer_Date")%>&nbsp;</font></td>
			<td   ><font size="2" face="Arial">&nbsp;</font></td>
			<td  > <font size="2" face="Arial">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <%= MyRec.fields("Transfer_Trailer_nbr")%></font></td>
				<td  >  <font size="2" face="Arial">   <%= MyRec.fields("Trans_Carrier")%>&nbsp;</font></td>
					<td  >   <font size="2" face="Arial">        <%= MyRec.fields("Brand")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">        <%= MyRec.fields("SAP_Nbr")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">       &nbsp;</font></td>

				<td> <font size="2" face="Arial"><%= MyRec.fields("PMO_nbr")%>&nbsp;</font></td>
		


		<% end if %>
		
<td>

     
        <font size="2" face="Arial">
        <%= MyRec.fields("PO")%></font></td>
        <td  >
        <font size="2" face="Arial">
        <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> 
        
              <% if MyRec.fields("Weigh_required") = "W" and MyRec.fields("Audit_tons") > 0  Then %>
      WC
       <% elseif (MyRec.fields("Weigh_required") = "W" and isnull(MyRec.fields("Audit_tons"))) or ( MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then %>W
        <% else %> &nbsp; <% end if %></td>

         <% 
             strDays = round(Now()- cdate(MyRec.fields("Date_received")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

        
          
 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    if strDays  > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(strDays) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"><%= strDays%></font></td>

	

	<td><font size="2" face="Arial"><%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>
	<td ALIGN = CENTER><font size="2" face="Arial"><input type="checkbox" name="C1" value="ON">&nbsp;</font></td>
	</tr>
		
	<% gcount = 1
	 gTcount = gTcount + 1
	 gSpecies = MyRec.fields("Species")
       ii = ii + 1
       MyRec.MoveNext
    
	 end if %>

<%  Wend %>
	  <tr class=tablecolor2>
<td colspan = 15><font face = arial size = 2><b></b>Total:&nbsp;<%= gcount%></b></font></td>

	</tr>
	<TR><td colspan = 14>&nbsp;</td>



	</tr>
<TR><td colspan = 15><font face = arial size = 2><b></b>Grand Total:&nbsp;<%= gTcount%></b></font></td>



	</tr>

</table>