<html>
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Delete Approver of Hazard Assessment</title>
</head>
<% dim strsql, MyRec, strid, strBID, strE_Name, strP_Date, MyConn, MyConn2, strsql2, strApprover1, strApprover2
Dim stra, strb, strStatus, strStatus1, strHAStatus


strBID = Session("EmployeeID")
strE_name = Session("Ename")
 strnow = dateadd("h", -5, now())
strP_date = formatdatetime(strnow,2)
		 strBID = Session("EmployeeID")
  		 strE_Name = ReturnEname(StrBID)

strid = Request.querystring("id")

     Dim strWA, MyConn3
      strsql = "Select Workarea from tblSOP where SOP_NO = '" & strid & "'"
       Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
   		strWA = MyConn.fields("Workarea")
   		
   		MyConn.close 
   		
   		
   		     strSQL = "SELECT tblApprovers.* from tblApprovers where Work_area = '" & strWA & "' and P_BID = '" & Session("EmployeeID") & "'"

Set MyConn3 = Server.CreateObject("ADODB.Recordset") 

   		MyConn3.Open strSQL, Session("ConnectionString")
   		
   		If not MyConn3.eof then
   		
   	strsql = "Select HA_Approver_1, HA_Approver_2, HA_Status from tblHA where SpaceID = '" & strid & "'"
       Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
   		strApprover1 = MyConn.fields("HA_Approver_1")
   		 		strApprover2 = MyConn.fields("HA_Approver_2")
   		 		
   		 		strHAStatus = MyConn.fields("HA_Status")
   		MyConn.close 

	
	set objGeneral = new ASP_CLS_General
  		 if objGeneral.IsSubmit = True Then
  		 strid = Request.querystring("id")
  		 strBID = Session("EmployeeID")
  		 strE_Name = ReturnEname(StrBID)
  		 strP_Date = formatdatetime(strnow,2)
  	     
  	     
  	     If request.form("D1") = "ON" then
  	     
  			 strsql =  "INSERT INTO tblChangeLog ( Change_Date, Change_Name,  SOP_NO,  Change_Desc) "_
 			&" SELECT  '" & strP_date & "', '" & strE_name & "', '" & strid & "',  'Delete Approver 1'"
 			Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 
			
			
		    If strHAStatus = "Approved"  or strHAStatus = "APPROVED" then	
			strsql =  "Update tblHA SET [HA_Status] = 'Pending', [HA_Approver_1] = Null where SpaceID = '" & strid & "'"
			else
			strsql =  "Update tblHA SET [HA_Approver_1] = Null where SpaceID = '" & strid & "'"
			end if 
 			Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 
			end if 
			If request.form("D2") = "ON" then
			
  		 	strsql =  "INSERT INTO tblChangeLog ( Change_Date, Change_Name,  SOP_NO,  Change_Desc) "_
 			&" SELECT  '" & strP_date & "', '" & strE_name & "', '" & strid & "',  'Delete Approver 2'"
 						
 			Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 
			
			If strHAStatus = "Approved"  or  strHAStatus = "APPROVED" then			
			strsql =  "Update tblHA SET [HA_Status] = 'Pending', [HA_Approver_2] = Null where SpaceID = '" & strid & "'"
 			else
 			strsql =  "Update tblHA SET  [HA_Approver_2] = Null where SpaceID = '" & strid & "'"
 			end if
 			Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 
			end if 
			
			
		 
  	
			Response.redirect("Change_history.asp?id=" & strid)
			
	
End if

 %>
 <body >
 <form name="form2" action="Approval_delete.asp?id=<%= strid%>" method="post" ID="Form2"  >

	<p align="center"><br><font face="Arial"><b>Hazard Assessment Approval for <%= strid%></b></font></br>
	<font face="Arial">&nbsp;</font></p>
	<p align="center">&nbsp;</p>

  <table border="1" cellpadding="0" align = center cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="70%" id="table1">

 <tr>
    <td  height="19" bgcolor="#FFFFDD">
	<p align="center"><font face="Arial" size="2">Approver 1</font></td>
	    <td  height="19" bgcolor="#FFFFDD" align="center">
	<font face="Arial" size="2">Delete</font></td>
    <td  height="19" bgcolor="#FFFFDD" align="center">
	<font face="arial" size="2">Approver 2</font></td>
    <td  height="19" bgcolor="#FFFFDD" align="center">
	<font face="Arial" size="2">Delete</font></td>
   
  </tr>

  <tr>
    <td align = center height="19" bgcolor="#FFFFFF" > <font face="Arial" size = 2>

    <%= strApprover1 %>
	</font></td>
	<td  height="19" bgcolor="#FFFFFF"  align = center ><font face="Arial" size="2" align = center>
	<input type="checkbox" name="D1" value="ON"></td>
    <td  height="19" bgcolor="#FFFFFF"  ><font face="Arial" size="2">
	<p align="center">
	<font face="Arial">
	<%= strApprover2 %></font></td>
	 <td  height="19" bgcolor="#FFFFFF">
		<p align="center"><font face="Arial" size="2">&nbsp;<input type="checkbox" name="D2" value="ON"></font>
		</td>
  
  </tr>


</table>
     
 <p align = center><INPUT TYPE="submit" value="Submit" ></p>
     
</form>
<% else 
Response.write("<br><font face = arial size = 3><br>You do not have authorization to delete an approver in this workarea.  You must be an approver in the work area to do so.</font>")
end if
MyConn3.close %><!--#include file="footer.inc"-->