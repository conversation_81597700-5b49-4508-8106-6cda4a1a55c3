
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Recovered Paper Consumption</TITLE>



<!--#include file="classes/asp_cls_headerFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->


<%  
	Dim strsQL, rstEquip

   	Dim objEquipSearch, rstTotals, strCount, strDed, strNet, strTons, rstTotalReceived, strTR, objNew, rstSpecies
  	Dim  strVendor, strBegDate, strEndDate, strSpecies, strMonth, strDateReceived, strDateAdded, rstMonth, strShipWeek
  	Dim objGeneral
 strMonth = request.querystring("id")
 stryear = request.querystring("y")
 
  	
  	strsql = "SELECT CID,  PS, location,  date_unloaded,net, Species, PO, Release_Nbr, Vendor, Generator, Gen_City, gen_State, grade "_
  	&" FROM tblCars  where datepart(m, Inv_depletion_date) = " & strmonth & " and datepart(yyyy, Inv_depletion_date) = " & stryear & " "_
  	&" and len(Vendor) > 0  and grade <> 'VF' and grade  <> 'NF' "_
  	&" order by Vendor, Generator, Gen_City, Species, date_unloaded"
  	
  	   Set rstEquip = Server.CreateObject("ADODB.Recordset")
    rstEquip.Open strSQL, Session("ConnectionString")
    If not rstequip.eof then
    strVendor = rstEquip.fields("Vendor")
    strSpecies = rstEquip.fields("Species")
%>
<style type="text/css">
.style1 {
	font-family: Arial;
	font-size: x-small;
}
.style2 {
	font-size: x-small;
}
.style5 {
	font-family: Arial;
}
</style>
</head>

<TABLE borderColor=#E7D1C2 cellSpacing=0 cellPadding=0 width="100%" border=0>  
  <tr><td colspan="5" size = "2" bordercolor="#FFFFFF"><b>
	<font face="Arial">Mobile Recovered Paper Consumption</font></td>
</tr></table>


   <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=90% border=1 align = center>
 
      <tr class="tableheader">
      <td  align="left" class="style2"><font face="Arial">Vendor</font></td>
<td  align="left" class="style2"><font face="Arial">Generator</font></td>
<td  align="left" class="style2"><font face="Arial">City</font></td>
<td  align="left" class="style2"><font face="Arial">State</font></td>
 	<td  align="center" class="style1">Species</td>
 	 
<td  align="center" class="style1">Location</td>

 		<td  align="center" class="style1">PO</td>
 			<td  align="center" class="style1">Release</td>
	<td  align="center"><font face="Arial" size = 1 class="style2">Date Unloaded</font></td>
	
	<td  align="center" class="style2"><font face="Arial">Net</font></td>


      
  	<% 
      Dim ii
       ii = 0
       while not rstEquip.Eof
   
       if ( ii mod 2) = 0 Then %>
       <tr class=tablecolor1>
    <% else %>
       <tr class=tablecolor2>
    <% end if
   %>
    <td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("Vendor")%>&nbsp;</td>

<td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("Generator")%>&nbsp;</td>
<td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("Gen_City")%>&nbsp;</td>
<td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("Gen_State")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Species")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Location")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("PO")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Release_nbr")%><%=rstEquip.fields("PS")%></td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("date_unloaded")%>&nbsp;</td>

	<td  align="right"><font face="Arial" size = 1>
	<% if len(rstEquip("Net")) > 0 then %>
	<%= round(rstEquip.fields("Net"),3)%>
	<% else %>
	<%= rstEquip.fields("Net")%>
	<% end if %>
	&nbsp;</font></td>



   </tr>
    <% 
       ii = ii + 1
       
   
       rstEquip.MoveNext
     Wend
 end if %>
</table>