<%
'---------------------------------------------------------------------------------------------------
'--## # FILE DESCRIPTION ---------------------------------------------------------------------------
'---------------------------------------------------------------------------------------------------
'--
'--
'-- This file created and maintained by Global Web Development Services. 
'-- Contact "_Web Development Services, Global" for any questions or problems.
'--
'-- 
'-- This file contains two classes used to query a Microsoft Active Directory and return a 
'-- collecion of user objects.
'--
'--     CLASS: ActiveDirectoryUserQuery      The Search  (wrapper/collection)object. 
'--     CLASS: ActiveDirectoryUser           A valid active directory user object, populated with
'--                                          standard LDAP Properties and some custom AD properties.
'--
'-- These classes are a replacement for the current scripts that access the exchange GAL for 
'-- employee information. They use LDAP to access employee information from the Windows 2000 Active 
'-- Directory services instead of from the Exchange 5.5 directory structure. This class is written 
'-- in VBScript, and can be used in ASP and Windows script host files.
'--
'-- IF THE CLASSES ARE USED UNDER ASP ...
'-- (1) Credentials must be provided to the script since NTLM credentials are not passed by a web
'--     user - NTLM authentication is a hash, not a full token, and cannot be passed from the web
'--     server. Cleartext credentials must be set in the script, and will be used with SSL to 
'--     create and pass a token. 
'-- (2) If no user credentials are provided the script will use an application account (USRNAP19)
'--     to authenticate. THIS ACCOUNT HAS SPECIAL PRIVELAGES and only has access to web services
'--     web servers and the following domain controllers: USTCNDC0,1,2,3,4,5. The script will fail
'--     if run against other servers that are not pre-authorized by computer security or ESM.
'--
'---------------------------------------------------------------------------------------------------
'--## # VERSION INFORMATION ------------------------------------------------------------------------
'---------------------------------------------------------------------------------------------------
'--
'-- Version 3.01
'-- Modified on 2/7/2005
'-- (1) Updated the 'groupNames' property and removed the ='CN=' test so that it correctly lists
'--     ZGL groups, which have no group container.
'-- (2) Added a sort routine to 'groupNames' so that they are easier to use.
'--
'-- Version 3.00
'-- Modified on 1/27/2005
'-- (1) Added the 'openBind' property.
'-- (2) Added the 'impersonateUser' property.
'-- (3) Added the 'validateCredentials' method and reorganized how credentials are used in the class.
'-- (4) Added the 'validateDC' method and reorganized how DCs are used in the class.
'-- (5*) Added the ability to search for groups, which includes the new property objectType
'-- 
'-- Version 2.07
'-- Modified on 8/10/2004
'-- (1) Updated some of the documentation.
'--
'-- Version 2.06
'-- Modified on 7/21/2004
'-- (1) Added additional domain controllers (ustcndc4 and 5) to the defaultDCList variable.
'--
'-- Version 2.05
'-- Modified on 5/21/2004
'-- (1) Changed Secretary AD propertiey to msexchassistantname to fit the AD2003 schema expansion.
'--
'-- Version 2.04
'-- Modified on 2/19/2004
'-- (1) Added DC Randomization and Failover with the getDC function.
'--
'-- Version 2.03
'-- Modified on 1/22/2004
'-- (1) Added "ObjectCategory = 'Person'" to the default WHERE clause to prevent machine accounts 
'--     from being returned.
'-- (2) Added the LDAP property "memberOf", and the friendly names "groupNames" and "" to the   
'--     ActiveDirectoryUser object so that a user's groups can be returned. THIS WILL NOT RETURN 
'--     NESTED GROUPS - only directly assigned groups.
'-- (3) Added the "udfmakeList" function in the ActiveDirectoryUser object.
'-- (4) Updated some of the "friendly" properties to include the single instance version of the 
'--     property. For example, 
'--     'telephoneNumber' is a single-instance field that contains 1 phone number.
'--     'phone' is the friendly name for the telephoneNumber field.
'--     'otherTelephone' is a multi-instance field that can contain up to 10 phone numbers. It does 
'--                      not contain the phone number contained in the telephoneNumber field.
'--     'phones' was the friendly name for the otherTelephone, however, now 'phones' is a combination 
'--              of the telephoneNumber and otherTelephone fields, so that all of the numbers appear 
'--              in a single property.
'--     The fields 'WebPages','phones','telephones','homephones','pagers','mobiles','faxes' and 
'--              'ipphones' were updated wherever the udfmakeList function is used.
'-- (5) Added the 'exchangeServer', 'exchangePath' and 'exchangeServer' properties, which list a 
'--     users K-C Exchange server. 
'--
'-- Version 2.02
'-- Added a default user ID to active direcotry for this class. Therefore, passing in credentials is 
'-- no longer required. If credentials are passed they are used, if not the default credentials are 
'-- used, which do allow for full access to the AD User container for read-only purposed. Added a 
'-- version number to the class for user identification.
'-- 
'-- Version 2.01
'-- Added properties for passing in user auithentication, so the object will work with Active 
'-- Directory securty changes made recenetly. This also allows the ActiveDirectoryUserQuery object 
'-- to access the extended AD attributes.
'-- 
'-- Version 2.00
'-- Re-Wrote the entire class. It is now broken into two classes:
'--    ActiveDirectoryUser, which is a user object populated with LDAP and AD properties.
'--    ActiveDirectoryUserQuery, which is a container collection used to query active directory 
'--    and return a collection of user Objects.
'-- 
'-- Version 1.00 (FindUser.cls)
'--
'---------------------------------------------------------------------------------------------------

'---------------------------------------------------------------------------------------------------
'---------------------------------------------------------------------------------------------------
'--## @ ADUserQuery Class --------------------------------------------------------------------------
'---------------------------------------------------------------------------------------------------
'---------------------------------------------------------------------------------------------------

'---------------------------------------------------------------------------------------------------
'--## @ ADUserQuery Description --------------------------------------------------------------------
'---------------------------------------------------------------------------------------------------
'-- 
'-- DESCRIPTION: A class used to search Active Directory. This will create a collection of 
'--              ActiveDirectoryUser objects.
'--
'---------------------------------------------------------------------------------------------------
'--## @ ADUserQuery Object Model -------------------------------------------------------------------
'---------------------------------------------------------------------------------------------------
'--
'-- PROPERTIES: 
'--     impersonateUser          Bln: Determines if credentials should be used used to execute.
'--                                   If impersonateUser = True the class will attempt to impersonate
'--                                      the current user. This works when logged in to a Windows XP
'--                                      or Windows 2000 machine. When running the class from an 
'--                                      ASP page the class may not work, or may work with only some
'--                                      of the properties being populated (the ones that do not 
'--                                      require authentication) as ASP cannot pass NTLM credentials
'--                                      back to Active Directory. 
'--                                   If impersonateUser = False then credentials must be passed. Use
'--                                      the authorizedUserName and authorizedUserPassword properties
'--                                      to pass valid domain credentials. The script also has 
'--                                      defaultUserName and defaultUserPassword properties which 
'--                                      may be provided as defaults by those who manage the script.
'--     authorizedUserName       Str: The user name/id to use to authenticate to Active Directory.
'--                                   The username should be set in 'domain\username' format.
'--     authorizedUserPassword   Str: The user password to use to authenticate to Active Directory.
'--     targetDomain             Str: The domain to search in 'yourcompany.com' format.
'--     objectType               Str: The type of object to search for. Either 'user' or 'group'.
'--     openBind                 Bln: Determines if a domain controller should be used to execute.
'--                                   If openBInd = True the class will attemt to bind to the domain
'--                                      by finding any available domain controller using the following
'--                                      syntax: LDAP://DC=yourcompany,DC=com
'--                                   If openBind = False the class will attemt to bind to the domain
'--                                      using a specific Domain controller provided by the user with
'--                                      the following syntax: LDAP://DC1/DC=yourcompany,DC=com 
'--                                   Sometimes it is necessary to bind to a domain using a specific
'--                                   server because of time-out issues or security, if some user
'--                                   accounts have limited server access.
'--     domainControllerList     Str: A list of Domain controllers to use to bind to the domain.
'--                                   Seperate the by commas (Ex: DC1,DC2,DC3). Multiple servers can
'--                                   be provided for failover - the class will select and validate
'--                                   that a server is responding before running. A private 
'--                                   DefaultDCList property is also provided in the class so that
'--                                   a default list of servers can be provided by those who manage
'--                                   the script.
'--     orderBy                  Str: The ADSI property(s) to sort the results by. For a complete 
'--                                   list of properties see the ADUser object documentation in this
'--                                   class file.
'--     resultsCount             Int: The results count from the last query executed by executeQuery.
'--     errorList                Str: A message listing all the errors in the errors collection.
'--     errorCount               Int: Count for number of errors in the erros collection.
'--     outputFormat             Str: The data output format. Either 'text' or 'html'.
'--     ldapSearchString         Str: The last search string executed by executeQuery. (ReadOnly)
'--     version                  Str: Indicates the version number of the object (in version 2.02+).
'--
'-- METHODS:    
'--     executeQuery             Executes the query and populates the results collection with  
'--                              ActiveDirectoryUser objects that meet the conditions in the filters. 
'--     addFilter Name,Value     Method: Add a filter set. 
'--                              Return: None.
'--                                      Name = The LDAP property name to filter on. (ex. cn, etc.)
'--                                      Value = The value the filter should check against.
'--                                                  use 'B12345' to find an exact match.
'--                                                  use 'B12345*' to find all entries that match 
'--                                                                base 'B12345???'.
'--                                                  use '*B12345*' to find all entries that 
'--                                                                 contain 'B12345'.
'--                                      ** both name and value must be typed as strings or an 
'--                                         error will occur.
'--     setFilter(Name)          Method: Sets the value for the specified Ldap property (filter).
'--                              Return: None.
'--     getFilter(Name)          Property (Str): Returns the value for the specified Ldap property.
'--     filterExists(Name)       Method: Determines if a filter for the property already exists.
'--                              Return: boolean
'--     removeFilter(Name)       Method: Removes an individual filter(property) determined by 'Name'.
'--                              Return: None.
'--     removeAllFilters         Method: Removes all filter criteria sets.
'--                              Return: None.
'--
'-- COLLECTIONS:
'--     filters (A collection of search Criteria - This is a dictionary object)          
'--       .count                    Property (Int): The current number of filter criteria sets.
'--       .keys                     Collection (Ary): An array of filter keys (ldap properties)
'--       .items                    Collection (Ary): An array of filter values (criteria)      
'--
'--     errors (A collection of Errors encountered during method calls.0
'--       .number                   Property (Int): Error Number raised by Environment or class.
'--       .description              Property (Str): Textual Error Description.
'--       .source                   Property (Str): Textual source definition of the error event.
'--
'--     Results (a collection of ActiveDirectoryUser objects - see Class for property descriptions)
'--
'---------------------------------------------------------------------------------------------------
'--## @ ADUserQuery Usage Examples -----------------------------------------------------------------
'---------------------------------------------------------------------------------------------------

'-------------------------------------------------------------------------------
'-- SETTING INITIALIZATION AND OUTPUT OPTIONS ----------------------------------
'------------------------------------------------------------------------------- 
'--     
'--     Option Explicit
'--     
'--     Dim objADUserQuery
'--     Set objADUserQuery = New ActiveDirectoryUserQuery
'-- 
'--     '-- Determine how Credentials are used with the Class. If impersonateUser = true the 
'--     '-- credentials of the current user will be sent and no additional credentials need to 
'--     '-- be set for te class to run. Generally impersonateUser = True when using this class 
'--     '-- in a vbscript file exectuting from a client user workstation.
'--         objADUserQuery.impersonateUser = True
'-- 
'--     '-- If impersonateUser = false credentials need to be set (unless default credentials
'--     '-- are provided in the class) On web Services Web Server default credentials are 
'--     '-- provided. The default settings are impersonateUser = False and web service 
'--     '-- provided credentials. Use impersonateUser = False when running from a web server.
'--         objADUserQuery.impersonateUser = False
'--         objADUserQuery.authorizedUserName = "kcus\b12345"
'--         objADUserQuery.authorizedUserPassword = "1stNewPa$$word"
'-- 
'--     '-- Specify the domain to search (default is 'kcc.com' if omitted).
'--         objADUserQuery.targetDomain = "kcc.com"
'--         
'--     '-- Specify if the class should attempt to bind to any available domain controller 
'--     '-- (called an open bind) of if the class should attempt to bind to a specific domain  
'--     '-- controller. If impersonateUser = True and the users will be accessing the script 
'--     '-- with standard user accounts use openBind = True. If the class will be executed by 
'--     '-- an application account (which usually has limited access privelages) use 
'--     '-- openBind = False and specify a list of domain controllers in 'domainControllerList' 
'--     '-- or make sure the user account has access to the list of default domain controllers 
'--     '-- (Currently ustcndc0,ustcndc1,ustcndc2,ustcndc3,ustcndc4,ustcndc5). The Default 
'--     '-- value is 'openBind = False'.
'--         objADUserQuery.openBind = True
'--     
'--     '-- If openBind = False the class requires a list of domain controllers to attempt to 
'--     '-- connect to. The default list used by the class is ustcndc0 - ustcndc5, which are
'--     '-- the primary Domain Controllers at the TCC. If the user account used to execute the
'--     '-- queries for this class (used by 'authorizedUserName') has limited access to network 
'--     '-- resources, make sure the account has access to these 6 servers, or modify the  
'--     '-- following property to include a list of the domain controllers that the user account
'--     '-- has access to. If you are unsure what servers the account has access to contact 
'--     '-- computer security.
'--         'objADUserQuery.domainControllerList = "ustcndc5"
'-- 
'--     '-- Add an order by clause to sort the results.
'--         objADUserQuery.orderBy = "sn"
'-- 
'--     Set objADUserQuery = Nothing
'-- 
'-------------------------------------------------------------------------------
'-- RETURNING A SINGLE USER FROM A VBSCRIPT ------------------------------------
'-------------------------------------------------------------------------------
'-- This example is meant to be run from a GDXP desktop. It will return
'-- information for a single user by accessing the nearest domain controller
'-- and using the credentials of the user logged on to the GDXP Workstation.
'-------------------------------------------------------------------------------    
'-- 
'--     Option Explicit
'--     
'--     Dim strResults
'--     Dim objADUserQuery
'--     Set objADUserQuery = New ActiveDirectoryUserQuery
'--         objADUserQuery.impersonateUser = True
'--         objADUserQuery.targetDomain = "kcc.com"  '(Default value, can be omitted)
'--         objADUserQuery.openBind = True
'--         objADUserQuery.addFilter "cn", "B16599"
'--         objADUserQuery.objectType = "user"       '(Default value, can be omitted)
'--         objADUserQuery.executeQuery
'-- 
'--     '-- Check for Errors. Display errors if they exist.
'--         If (objADUserQuery.errorCount > 0) Then
'--             strResults = objADUserQuery.errorList
'--     '-- If no errors exist, process the results set.
'--         Else
'--             If (objADUserQuery.resultsCount = 0) Then
'--                 strResults = "No users Found."
'--             ElseIf (objADUserQuery.resultsCount = 1) Then
'--             '-- Use standard array form '(x)' where x is an ordinal position in the array.    
'--                 strResults = objADUserQuery.results(0).UserSummary
'--             Else
'--                 strResults = "More than one user was found that met the criteria"
'--             End If
'--         End If
'--         WScript.Echo strResults
'-- 
'--     Set objADUserQuery = Nothing
'-- 
'--     WScript.Quit
'-- 
'-------------------------------------------------------------------------------  
'-- RETURNING A SPECIFIC SET OF USERS FROM A VBSCRIPT --------------------------  
'-------------------------------------------------------------------------------  
'-- This example is meant to be run from a GDXP desktop. It will return
'-- information for a single user by accessing the nearest domain controller
'-- and using the credentials of the user logged on to the GDXP Workstation.
'-------------------------------------------------------------------------------  
'-- 
'--     Option Explicit
'-- 
'--     Dim strResults
'--     Dim objADUserQuery
'--     Set objADUserQuery = New ActiveDirectoryUserQuery
'--         objADUserQuery.openBind = True
'--         objADUserQuery.impersonateUser = True
'--         objADUserQuery.setFilter "cn","IN('B16599','B16599S')"
'--         objADUserQuery.executeQuery
'-- 
'--     '-- Check for Errors. Display errors if they exist.
'--         If(objADUserQuery.errorCount > 0) Then
'--             WScript.Echo(objADUserQuery.errorList)
'--     '-- If no errors exist, process the results set.
'--         Else
'--             If(objADUserQuery.resultsCount = 0) Then
'--                 strResults = "No users Found."
'--             Else
'--                 Dim objADUser
'--                 For Each objADUser in objADUserQuery.Results
'--                     strResults = strResults & objADUser.UserSummary & vbCrLf
'--                 Next
'--                 Set objADUser = Nothing
'--             End If
'--         End If
'-- 
'--         WScript.Echo strResults
'-- 
'--     Set objADUserQuery = Nothing
'-- 
'-------------------------------------------------------------------------------  
'-- RETURNING A PATTERN OF USERS FROM A VBSCRIPT -------------------------------  
'-------------------------------------------------------------------------------  
'-- This example is meant to be run from a GDXP desktop. It will return
'-- information for a single user by accessing the nearest domain controller
'-- and using the credentials of the user logged on to the GDXP Workstation.
'-------------------------------------------------------------------------------  
'-- 
'--     Option Explicit
'-- 
'--     Dim strResults
'--     Dim objADUserQuery
'--     Set objADUserQuery = New ActiveDirectoryUserQuery
'--         objADUserQuery.openBind = True
'--         objADUserQuery.impersonateUser = True
'--         objADUserQuery.addFilter "cn","B16599*"
'--         objADUserQuery.executeQuery
'-- 
'--     '-- Check for Errors. Display errors if they exist.
'--         If(objADUserQuery.errorCount > 0) Then
'--             WScript.Echo(objADUserQuery.errorList)
'--     '-- If no errors exist, process the results set.
'--         Else
'--             If(objADUserQuery.resultsCount = 0) Then
'--                 strResults = "No users Found."
'--             Else
'--                 Dim objADUser
'--                 For Each objADUser in objADUserQuery.Results
'--                     strResults = strResults & objADUser.UserSummary & vbCrLf
'--                 Next
'--                 Set objADUser = Nothing
'--             End If
'--         End If
'-- 
'--         WScript.Echo strResults
'-- 
'--     Set objADUserQuery = Nothing
'--     
'-------------------------------------------------------------------------------  
'-- RETURNING A LIST OF GROUP MEMBERS FROM A VBSCRIPT --------------------------  
'-------------------------------------------------------------------------------  
'-- This example is meant to be run from a GDXP desktop. It will return
'-- information for a single user by accessing the nearest domain controller
'-- and using the credentials of the user logged on to the GDXP Workstation.
'-------------------------------------------------------------------------------  
'--     
'--     Option Explicit
'--     
'--     Dim strResults
'--     Dim objADUserQuery
'--     Set objADUserQuery = New ActiveDirectoryUserQuery
'--         objADUserQuery.openBind = True
'--         objADUserQuery.impersonateUser = True
'--         objADUserQuery.addFilter "cn","ZGL-096-614" '-- SAPPortalsClientTestingGroup
'--         objADUserQuery.objectType = "group"       
'--         objADUserQuery.executeQuery
'--     
'--     '-- Check for Errors. Display errors if they exist.
'--         If(objADUserQuery.errorCount > 0) Then
'--             strResults = objADUserQuery.errorList
'--     '-- If no errors exist, process the results set.
'--         Else
'--             If(objADUserQuery.resultsCount = 0) Then
'--                 strResults = "No groups Found."
'--             Else
'--                 Dim objADGroup
'--                 For Each objADGroup in objADUserQuery.Results
'--                     strResults = strResults & objADGroup.AllProperties & vbCrLf
'--                 Next
'--                 Set objADGroup = Nothing
'--             End If
'--         End If
'--     
'--         WScript.Echo strResults
'--     
'--     Set objADUserQuery = Nothing
'-- 
'-------------------------------------------------------------------------------
'-- RETURNING A SINGLE USER FROM A WEB PAGE ------------------------------------
'-------------------------------------------------------------------------------
'-- This example is meant to be run from a Windows 2000 or Windows 2003 IIS 
'-- Server. Information if requested from one of the 6 primary Domain 
'-- Controllers at the TCC. Credentials are provided so that the user 
'-- credentials of the user logged on to the GDXP workstation / Internet 
'-- Explorer ARE NOT USED (IIS cannot impersonate users and pass the credentials
'-- of a GDXP user account to a domain controller using the NTLM authentication
'-- method).
'--
'-- ** - These lines can be omitted if the script is being run on a standard
'--      Web Services supported web server. Classes stored on these servers are
'--      modified to provide a default set of credentials for all users.  
'--
'-------------------------------------------------------------------------------    
'-- 
'--     Option Explicit
'-- 
'--     Dim strResults
'--     Dim objADUserQuery
'--     Set objADUserQuery = New ActiveDirectoryUserQuery
'--         objADUserQuery.targetDomain = "kcc.com"  '(Default value, can be omitted)
'--         objADUserQuery.openBind = False          '(Default value, can be omitted)
'--         objADUserQuery.impersonateUser = False   '(Default value, can be omitted)
'--         objADUserQuery.authorizedUserName = "kcus\b12345"    '(** see note above)
'--         objADUserQuery.authorizedUserPassword = "password"   '(** see note above)
'--         objADUserQuery.addFilter "cn", "B16599"
'--         objADUserQuery.objectType = "user"       '(Default value, can be omitted)
'--         objADUserQuery.executeQuery
'--     
'--     '-- Check for Errors. Display errors if they exist.
'--         If (objADUserQuery.errorCount > 0) Then
'--             strResults = objADUserQuery.errorList
'--     '-- If no errors exist, process the results set.
'--         Else
'--             If (objADUserQuery.resultsCount = 0) Then
'--                 strResults = "No users Found."
'--             ElseIf (objADUserQuery.resultsCount = 1) Then
'--             '-- Use standard array form '(x)' where x is an ordinal position in the array.    
'--                 strResults = objADUserQuery.results(0).UserSummary
'--             Else
'--                 strResults = "More than one user was found that met the criteria"
'--             End If
'--         End If
'--         Response.Write(Replace(strResults,vbCrLf,"<BR>"))
'--     
'--     Set objADUserQuery = Nothing
'-- 
'-------------------------------------------------------------------------------  
'-- RETURNING A SPECIFIC SET OF USERS FROM A WEB PAGE --------------------------  
'-------------------------------------------------------------------------------  
'-- This example is meant to be run from a Windows 2000 or Windows 2003 IIS       
'-- Server. Information if requested from one of the 6 primary Domain             
'-- Controllers at the TCC. Credentials are provided so that the user             
'-- credentials of the user logged on to the GDXP workstation / Internet          
'-- Explorer ARE NOT USED (IIS cannot impersonate users and pass the credentials  
'-- of a GDXP user account to a domain controller using the NTLM authentication   
'-- method).                                                                      
'--                                                                               
'-- ** - These lines can be omitted if the script is being run on a standard      
'--      Web Services supported web server. Classes stored on these servers are   
'--      modified to provide a default set of credentials for all users.          
'--                                                                               
'-------------------------------------------------------------------------------  
'-- 
'--     Option Explicit
'--     
'--     Dim strResults
'--     Dim objADUserQuery
'--     Set objADUserQuery = New ActiveDirectoryUserQuery
'--         objADUserQuery.targetDomain = "kcc.com"  '(Default value, can be omitted)
'--         objADUserQuery.openBind = False          '(Default value, can be omitted)
'--         objADUserQuery.impersonateUser = False   '(Default value, can be omitted)
'--         objADUserQuery.authorizedUserName = "kcus\b12345"    '(** see note above)
'--         objADUserQuery.authorizedUserPassword = "password"   '(** see note above)
'--         objADUserQuery.setFilter "cn","IN('B16599','B16599S')"
'--         objADUserQuery.objectType = "user"       '(Default value, can be omitted)
'--         objADUserQuery.executeQuery
'--     
'--     '-- Check for Errors. Display errors if they exist.
'--         If(objADUserQuery.errorCount > 0) Then
'--             strResults = objADUserQuery.errorList
'--     '-- If no errors exist, process the results set.
'--         Else
'--             If(objADUserQuery.resultsCount = 0) Then
'--                 strResults = "No users Found."
'--             Else
'--                 Dim objADUser
'--                 For Each objADUser in objADUserQuery.Results
'--                     strResults = strResults & objADUser.UserSummary & vbCrLf
'--                 Next
'--                 Set objADUser = Nothing
'--             End If
'--         End If
'--     
'--         Response.Write(Replace(strResults,vbCrLf,"<BR>"))
'--     
'--     Set objADUserQuery = Nothing
'-- 
'-------------------------------------------------------------------------------  
'-- RETURNING A PATTERN OF USERS FROM A WEB PAGE -------------------------------  
'------------------------------------------------------------------------------- 
'-- This example is meant to be run from a Windows 2000 or Windows 2003 IIS       
'-- Server. Information if requested from one of the 6 primary Domain             
'-- Controllers at the TCC. Credentials are provided so that the user             
'-- credentials of the user logged on to the GDXP workstation / Internet          
'-- Explorer ARE NOT USED (IIS cannot impersonate users and pass the credentials  
'-- of a GDXP user account to a domain controller using the NTLM authentication   
'-- method).                                                                      
'--                                                                               
'-- ** - These lines can be omitted if the script is being run on a standard      
'--      Web Services supported web server. Classes stored on these servers are   
'--      modified to provide a default set of credentials for all users.          
'--                                                                               
'-------------------------------------------------------------------------------
'--     
'--     Option Explicit
'--     
'--     Dim strResults
'--     Dim objADUserQuery
'--     Set objADUserQuery = New ActiveDirectoryUserQuery
'--         objADUserQuery.targetDomain = "kcc.com"  '(Default value, can be omitted)
'--         objADUserQuery.openBind = False          '(Default value, can be omitted)
'--         objADUserQuery.impersonateUser = False   '(Default value, can be omitted)
'--         objADUserQuery.authorizedUserName = "kcus\b12345"    '(** see note above)
'--         objADUserQuery.authorizedUserPassword = "password"   '(** see note above)
'--         objADUserQuery.addFilter "cn","B16599*"
'--         objADUserQuery.objectType = "user"       '(Default value, can be omitted)
'--         objADUserQuery.executeQuery
'--     
'--     '-- Check for Errors. Display errors if they exist.
'--         If(objADUserQuery.errorCount > 0) Then
'--             strResults = objADUserQuery.errorList
'--     '-- If no errors exist, process the results set.
'--         Else
'--             If(objADUserQuery.resultsCount = 0) Then
'--                 strResults = "No users Found."
'--             Else
'--                 Dim objADUser
'--                 For Each objADUser in objADUserQuery.Results
'--                     strResults = strResults & objADUser.UserSummary & vbCrLf
'--                 Next
'--                 Set objADUser = Nothing
'--             End If
'--         End If
'--     
'--         Response.Write(Replace(strResults,vbCrLf,"<BR>"))
'--     
'--     Set objADUserQuery = Nothing
'--     
'-------------------------------------------------------------------------------  
'-- RETURNING A LIST OF GROUP MEMBERS FROM A WEB PAGE --------------------------  
'------------------------------------------------------------------------------- 
'-- This example is meant to be run from a Windows 2000 or Windows 2003 IIS       
'-- Server. Information if requested from one of the 6 primary Domain             
'-- Controllers at the TCC. Credentials are provided so that the user             
'-- credentials of the user logged on to the GDXP workstation / Internet          
'-- Explorer ARE NOT USED (IIS cannot impersonate users and pass the credentials  
'-- of a GDXP user account to a domain controller using the NTLM authentication   
'-- method).                                                                      
'--                                                                               
'-- ** - These lines can be omitted if the script is being run on a standard      
'--      Web Services supported web server. Classes stored on these servers are   
'--      modified to provide a default set of credentials for all users.          
'--                                                                               
'-------------------------------------------------------------------------------
'-- 
'--     Option Explicit
'-- 
'--     Dim strResults
'--     Dim objADUserQuery
'--     Set objADUserQuery = New ActiveDirectoryUserQuery
'--         objADUserQuery.targetDomain = "kcc.com"  '(Default value, can be omitted)
'--         objADUserQuery.openBind = False          '(Default value, can be omitted)
'--         objADUserQuery.impersonateUser = False   '(Default value, can be omitted)
'--         objADUserQuery.authorizedUserName = "kcus\b12345"    '(** see note above)
'--         objADUserQuery.authorizedUserPassword = "password"   '(** see note above)
'--         objADUserQuery.addFilter "cn","SAPPortalsClientTestingGroup" '-- 
'--         objADUserQuery.objectType = "group"       
'--         objADUserQuery.executeQuery
'-- 
'--     '-- Check for Errors. Display errors if they exist.
'--         If(objADUserQuery.errorCount > 0) Then
'--             strResults = objADUserQuery.errorList
'--     '-- If no errors exist, process the results set.
'--         Else
'--             If(objADUserQuery.resultsCount = 0) Then
'--                 strResults = "No groups Found."
'--             Else
'--                 Dim objADGroup
'--                 For Each objADGroup in objADUserQuery.Results
'--                     strResults = strResults & objADGroup.AllProperties & vbCrLf
'--                 Next
'--                 Set objADGroup = Nothing
'--             End If
'--         End If
'-- 
'--         Response.Write(Replace(strResults,vbCrLf,"<BR>"))
'-- 
'--     Set objADUserQuery = Nothing
'-- 
'---------------------------------------------------------------------------------------------------
'--## @ ADUserQuery Object -------------------------------------------------------------------------
'---------------------------------------------------------------------------------------------------

    Class ActiveDirectoryUserQuery

    '-----------------------------------------------------------------------------------------------
    '--## @ ADUserQuery Properties -----------------------------------------------------------------
    '-----------------------------------------------------------------------------------------------
        
        Public authorizedUserName            '-- The user to authenticate to AD as.
        Public authorizedUserPassword        '-- The password to authenticate to AD with.
        Public targetDomain                  '-- The domain to perform the search in.
        Public objectType                    '-- The type of object to search for. 'user' or 'group'.
        Public orderBy                       '-- A standard SQL order by clause
        Public results()                     '-- The collection of results returned by executeQuery.
        Public resultsCount                  '-- The results count from the last executed query. 
        Public ldapSearchString              '-- The last search string executed by executeQuery.
        Public domainControllerList          '-- A List of domain controllers to use.
        Public openBind                      '-- Determines if a domain controller is used to execute.
        Public impersonateUser               '-- Determines if credentials must be provided to execute.
        Public version                       '-- Indicates script version (in version 2.02+).
        Public filters                       '-- A collection of filters to apply to the search.
        Public errors                        '-- A collection of Errors.
        Public outputFormat                  '-- The data output format. 'text' or 'html'.

        Public Property Get errorCount : errorCount = errors.items.count             : End Property
        Public Property Get errorList  : errorList = getErrorList                    : End Property

        Private defaultUserName
        Private defaultUserPassword
        Private defaultDC
        Private defaultDCList
        Private userName
        Private userPassword

    '-----------------------------------------------------------------------------------------------
    '--## ADUserQuery.m.Class_Initialize -----------------------------------------------------------
    '-----------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Class Initialization Routine
    '--
    '-----------------------------------------------------------------------------------------------
        Private Sub Class_Initialize
            
            Set filters = CreateObject("Scripting.Dictionary")
            Set errors = New ErrorItems
            
            authorizedUserName      = Null
            authorizedUserPassword  = Null
            defaultUserName         = "KCUS\USRNAP19"
            defaultUserPassword     = "Dec2011"
            userName                = Null
            userPassword            = Null
            impersonateUser         = False
            
            openBind                = False
            domainControllerList    = Null
            defaultDC               = Null
            defaultDCList           = "ustcndc0,ustcndc1,ustcndc2,ustcndc3"
            targetDomain            = "kcc.com"
            
            objectType              = "user"
            outputFormat            = "text"
            version                 = "3.01"
            
        End Sub

    '-----------------------------------------------------------------------------------------------
    '--## ADUserQuery.m.Class_Terminate ------------------------------------------------------------
    '-----------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Class Initialization Routine
    '--
    '-----------------------------------------------------------------------------------------------
        Private Sub Class_Terminate
            Set filters = Nothing
            Set errors = Nothing
        End Sub
        
'---------------------------------------------------------------------------------------------------
'--## @ ADUserQuery Methods (Public) ---------------------------------------------------------------
'---------------------------------------------------------------------------------------------------

    '-----------------------------------------------------------------------------------------------
    '--## ADUserQuery.M.executeQuery ---------------------------------------------------------------
    '-----------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Wrapper function for execution of LDAP search.
    '--
    '-- OUTPUTS: Sets Errors Collection
    '--
    '-----------------------------------------------------------------------------------------------
        Public Sub executeQuery

            On Error Resume Next
                validateCredentials
                validateDC
                validateSearchCriteria
                If (errors.items.Count = 0) Then createLdapSearchString
                If (errors.items.Count = 0) Then executeSearch
            On Error Goto 0
            
        End Sub

    '-----------------------------------------------------------------------------------------------
    '--## ADUserQuery.M.addFilter ------------------------------------------------------------------
    '-----------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Adds a new filter criteria pair to the filters collection.
    '--
    '-- INPUTS: strFilterKey       A valid filter key (LDAP Property)
    '--         strFilterValue     a filter value 
    '--
    '-----------------------------------------------------------------------------------------------
        Public Sub addFilter(ByVal strFilterName, ByVal strCriteria)

            On Error Resume Next
                Err.Clear
                If (validateProperty(strFilterName)) Then
                    filters.Add CStr(strFilterName), CStr(strCriteria)
                    If (Err) Then
                    '--## ERRORS: ADUserQuery::addFilter::1
                        If (Err.Number = 457) Then
                            Errors.AddItem 1, "CLASS.ActiveDirectoryUserQuery::addFilter", _
                                              Err.Source & " " & Err.Number & ": " & _
                                              Err.Description & ": " &  _
                                              "Filter key '" & strFilterName & _
                                              "' already exists with value '" & _
                                              strCriteria & "'. To update the value use the " & _
                                              "setFilter method."
                        Else
                        '--## ERRORS: ADUserQuery::addFilter::2
                            Errors.AddItem 2, "CLASS.ActiveDirectoryUserQuery::addFilter", _
                                              Err.Source & " " & Err.Number & ": " & Err.Description 
                        End If
                    End If
                Else
                '--## ERRORS: ADUserQuery::addFilter::3    
                    Errors.AddItem 3, "CLASS.ActiveDirectoryUserQuery::addFilter", _
                                           "'" & strFilterName & "' is not a valid LDAP property."
                End If
            On Error Goto 0
            
        End Sub

    '-----------------------------------------------------------------------------------------------
    '--## ADUserQuery.M.getFilter ------------------------------------------------------------------
    '-----------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: gets the value of a filter criteria set.
    '--
    '-- INPUTS: strFilterKey       A valid filter key (LDAP Property)
    '--
    '-----------------------------------------------------------------------------------------------
        Public Function getFilter(ByVal strFilterName)

            On Error Resume Next
            Err.Clear
                If (Filters.Exists(CStr(strFilterName))) Then
                    getFilter = filters.Item(CStr(strFilterName))
                Else
                '--## ERRORS: ADUserQuery::getFilter::1
                    Errors.AddItem 1, "CLASS.ActiveDirectoryUserQuery::getFilter", _
                                      Err.Source & " " & Err.Number & ": " & Err.Description & _
                                      ": Filter key '" & strFilterName & "' does not exist. " & _
                                      "Use filterExists to determine if a filter exists before " & _
                                      "acting on it."
                End If
             '--## ERRORS: ADUserQuery::getFilter::2
                If (Err) Then errors.AddItem 2, "CLASS.ActiveDirectoryUserQuery::getFilter", _
                                                Err.Source & " " & Err.Number & ": " & Err.Description 
            On Error Goto 0
            
        End Function

    '-----------------------------------------------------------------------------------------------
    '--## ADUserQuery.M.setFilter ------------------------------------------------------------------
    '-----------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: sets the value of a filter criteria set. if the key doesn't exist the set is 
    '--              created rather than updated.
    '--
    '-- INPUTS: strFilterKey       A valid filter key (LDAP Property)
    '--         strCriteria        A filter string
    '--
    '-----------------------------------------------------------------------------------------------
        Public Function setFilter(ByVal strFilterName, ByVal strCriteria)

            On Error Resume Next
            Err.Clear
                If (Filters.Exists(CStr(strFilterName))) Then
                    filters.Item(CStr(strFilterName)) = CStr(strCriteria)
                Else
                    filters.Add CStr(strFilterName),CStr(strCriteria)
                End if

             '--## ERRORS: ADUserQuery::setFilter::1
                If (Err) Then errors.AddItem 1, "CLASS.ActiveDirectoryUserQuery::setFilter", _
                                                Err.Source & " " & Err.Number & ": " & Err.Description 
            On Error Goto 0
            
        End Function

    '-----------------------------------------------------------------------------------------------
    '--## ADUserQuery.M.filterExists ---------------------------------------------------------------
    '-----------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: determinies if a filter exists in the collection.
    '--
    '-- INPUTS: strFilterKey       A valid filter key (LDAP Property)
    '--
    '-----------------------------------------------------------------------------------------------
        Public Function filterExists(ByVal strFilterName)

            On Error Resume Next
            Err.Clear
                filterExists = Filters.Exists(CStr(strFilterName))
             '--## ERRORS: ADUserQuery::removeFilter::2
                If (Err) Then errors.AddItem 1, "CLASS.ActiveDirectoryUserQuery::removeFilter", _
                                                Err.Source & " " & Err.Number & ": " & Err.Description 
            On Error Goto 0
            
        End Function

    '-----------------------------------------------------------------------------------------------
    '--## ADUserQuery.M.removeFilter ---------------------------------------------------------------
    '-----------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: removes a new filter criteria pair from the filters collection.
    '--
    '-- INPUTS: strFilterKey       A valid filter key (LDAP Property)
    '--
    '-----------------------------------------------------------------------------------------------
        Public Sub removeFilter(ByVal strFilterName)

            On Error Resume Next
            Err.Clear
                If (Filters.Exists(CStr(strFilterName))) Then
                    filters.Remove CStr(strFilterName)
                Else
                '--## ERRORS: ADUserQuery::removeFilter::1
                    Errors.AddItem 1, "CLASS.ActiveDirectoryUserQuery::removeFilter", _
                                            "Filter key '" & strFilterName & _
                                            "' does not exist. Use filterExists to determine " & _
                                            "if a filter exists before acting on it."
                End If
                
            '--## ERRORS: ADUserQuery::removeFilter::2
                If (Err) Then errors.AddItem 2, "CLASS.ActiveDirectoryUserQuery::removeFilter", _
                                                Err.Source & " " & Err.Number & ": " & Err.Description 
            On Error Goto 0
            
        End Sub

    '-----------------------------------------------------------------------------------------------
    '--## ADUserQuery.M.removeAllFilters -----------------------------------------------------------
    '-----------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: removes a new filter criteria pair from the filters collection.
    '--
    '-----------------------------------------------------------------------------------------------
        Public Sub removeAllFilters

            On Error Resume Next
            Err.Clear
                filters.RemoveAll
             '--## ERRORS: ADUserQuery::removeAllFilters::1
                If (Err) Then errors.AddItem 1, "CLASS.ActiveDirectoryUserQuery::removeAllFilters", _
                                                Err.Source & " " & Err.Number & ": " & Err.Description 
            On Error Goto 0
            
        End Sub


'---------------------------------------------------------------------------------------------------
'--## @ ADUserQuery Methods (Private) --------------------------------------------------------------
'---------------------------------------------------------------------------------------------------

    '-----------------------------------------------------------------------------------------------
    '--## ADUserQuery.m.getErrorList ---------------------------------------------------------------
    '-----------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Returns a formatted list of errors to the user.
    '--
    '-----------------------------------------------------------------------------------------------
        Private Function getErrorList
        
            Dim objErr,strErrors
        
            For Each objErr in Errors.Items
                strErrors = strErrors & "ERROR NUMBER: " & objErr.Number & vbCrLf & _
                                        "SOURCE: " & objErr.Source & vbCrLf & _
                                        "DESCRIPTION: " & objErr.Description & vbCrLf & vbCrLf
            Next
            If (LCase(outputFormat) = "html") Then strErrors = Replace(strErrors,vbCrLf,"<BR>")
            getErrorList = strErrors
            Set objErr = Nothing
        
        End Function
        
    '-----------------------------------------------------------------------------------------------
    '--## ADUserQuery.m.getDC ----------------------------------------------------------------------
    '-----------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Returns a randomized, validated domain controller.
    '--
    '-----------------------------------------------------------------------------------------------
        Private Function getDC(strDCList)

            Const con_ADS_SECURE_AUTHENTICATION       = &H1 
            Const con_ADS_USE_ENCRYPTION              = &H2
            Const con_ADS_USE_SSL                     = &H2
            Const con_ADS_READONLY_SERVER             = &H4 
            Const con_ADS_PROMPT_CREDENTIALS          = &H8 
            Const con_ADS_NO_AUTHENTICATION           = &H10 
            Const con_ADS_FAST_BIND                   = &H20 
            Const con_ADS_USE_SIGNING                 = &H40 
            Const con_ADS_USE_SEALING                 = &H80 
            Const con_ADS_USE_DELEGATION              = &H100 
            Const con_ADS_SERVER_BIND                 = &H200

            Dim intIndex, arrDCs, strDC, strLog
            
            Dim objTemp, colTemp : colTemp = Split(strDCList,",")
            
            Dim colDCs, objDC
            Set colDCs = CreateObject("Scripting.Dictionary")
            For Each objTemp in colTemp
                colDCs.Add LCase(objTemp),""
            Next 
            
            Randomize
            On Error Resume Next
            Do 
                intIndex = Int((colDCs.Count) * Rnd)
                arrDCs = colDCs.Keys
                strDC = arrDCs(intIndex)
                strLog = strLog & strDC & ", "
                Err.Clear

                If (impersonateUser) Then
                    Set objDC = GetObject("WinNT://" & strDC)
                Else 
                    Set objDC = GetObject("WinNT:").OpenDSObject("WinNT://" & strDC, _
                                                                 userName, userPassword,_
                                                                 con_ADS_SECURE_AUTHENTICATION)
                End If
                
                If (Err.Number <> 0) Then
                    colDCs.Remove(strDC)
                Else
                    Set objDC = Nothing
                    Exit Do
                End If
            Loop Until (colDCs.Count = 0)
            
            If (colDCs.Count = 0) Then 
            '--## ERRORS: ADUserQuery::GetDC::1
                If (Err) Then errors.AddItem 1, "CLASS.ActiveDirectoryUserQuery::getDC", _
                                                "A user session could not be established " & _
                                                "with any of the specified domain controllers " & _
                                                "(" & Left(strLog,Len(strLog)-2) & ")." 
                getDC = Null
            Else
                getDC = strDC
            End If

        End Function

    '-----------------------------------------------------------------------------------------------
    '--## ADUserQuery.m.validateSearchCriteria -----------------------------------------------------
    '-----------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Validates the domain and search criteria before performing a search.
    '--
    '-----------------------------------------------------------------------------------------------
        Public Function validateSearchCriteria
            
            On Error Resume Next
             
        '-- Validate the target domain for errors.    
            Select Case LCase(Trim(targetDomain))
                   Case "kcc.com"                     '-- OK, this is a valid domain.
                   Case "test.kcc.com"                '-- OK, this is a valid domain.
                   Case "kctest.com"                  '-- OK, this is a valid domain.
                   Case "internet.kimberly-clark.com" '-- OK, this is a valid domain.
                   Case ""                            '-- Error, target domain is a required field.
                   '--## ERRORS: ADUserQuery::validateSearchCriteria::1     
                        Errors.AddItem 1,"CLASS.ActiveDirectoryUserQuery::validateSearchCriteria", _
                                         "'targetDomain' is a required property. " & _
                                         "Please specify a target domain to search."
                   Case Else
                   '--## ERRORS: ADUserQuery::validateSearchCriteria::2
                        Errors.AddItem 2,"CLASS.ActiveDirectoryUserQuery::validateSearchCriteria", _
                                         "'" & targetDomain & "' is an invalid value for the " & _
                                         "'targetDomain' property. Please specify a valid " & _
                                         "target domain. "
            End Select 
            
        '-- Check that the Filters collection has at least one filter criteria set.
        '--## ERRORS: ADUserQuery::validateSearchCriteria::3
            If (filters.Count = 0) Then _
               Errors.AddItem 3,"CLASS.ActiveDirectoryUserQuery::validateSearchCriteria", _
                                "Collection 'filters' contains no criteria. " & _
                                "You must define at least one set of filter criteria to search."

        '-- Check that the objectType is valid (either user or group).
        '--## ERRORS: ADUserQuery::validateSearchCriteria::4
            If (LCase(objectType) <> "user" AND LCase(objectType) <> "group") Then _
               Errors.AddItem 4,"CLASS.ActiveDirectoryUserQuery::validateSearchCriteria", _
                                "Property 'objectType' contains an invalid value. " & _
                                "Only 'user' and 'group' are valid values for this property."

        '-- Check that the output format is valid (either text or html).
        '--## ERRORS: ADUserQuery::validateSearchCriteria::5
            If (LCase(outputFormat) <> "text" AND LCase(outputFormat) <> "html") Then _
               Errors.AddItem 5,"CLASS.ActiveDirectoryUserQuery::validateSearchCriteria", _
                                "Property 'outputFormat' contains an invalid value. " & _
                                "Only 'text' and 'html' are valid values for this property."

        '--## ERRORS: ADUserQuery::validateSearchCriteria::6
            If (Err) Then Errors.AddItem 4, "CLASS.ActiveDirectoryUserQuery::validateSearchCriteria", _
                                           Err.Number & ":" & Err.Description & ":" & Err.Source
            
            On Error Goto 0

        End Function

 
    '-----------------------------------------------------------------------------------------------
    '--## ADUserQuery.m.validateCredentials --------------------------------------------------------
    '-----------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Validates user credentials  (if necessary) for script execution.
    '--
    '-----------------------------------------------------------------------------------------------
        Private Function validateCredentials

            Dim blnError  : blnError = False
            Dim blnResult : blnResult = False
            
            If NOT (impersonateUser) Then
                If (IsNull(userName)) Then
                    If (IsNull(authorizedUserName)) Then
                        If (IsNull(defaultUserName)) Then
                        '--## ERRORS: ADUserQuery::validateCredentials::1    
                            Errors.AddItem 1, "CLASS.ActiveDirectoryUserQuery::validateCredentials", _
                                              "The 'impersonateUser' property has a value of 'false' " & _
                                              "which requires the 'authorizedUserName' " & _
                                              "property or the 'defaultUserName' property of the " & _
                                              "class to contain a value. Both of these " & _
                                              "properties are NULL." 
                        Else    
                            userName = defaultUserName
                            userPassword = defaultUserPassword
                        End If
                    Else
                        If (Len(Trim(authorizedUserName)) > 0) Then
                            userName = authorizedUserName
                            userPassword = authorizedUserPassword
                        Else
                            If (IsNull(defaultUserName)) Then
                            '--## ERRORS: ADUserQuery::validateCredentials::2
                                Errors.AddItem 2, "CLASS.ActiveDirectoryUserQuery::validateCredentials", _
                                              "The 'impersonateUser' property has a value of 'false' " & _
                                              "which requires the 'authorizedUserName' " & _
                                              "property or the 'defaultUserName' property of the " & _
                                              "class to contain a value. Both of these " & _
                                              "properties are NULL." 
                            Else    
                                userName = defaultUserName
                                userPassword = defaultUserPassword
                            End If
                        End If
                    End If '-- If (IsNull(authorizedUserName))
                End If '-- If (NOT IsNull(userName))
            End If '-- If (impersonateUser)

        End Function
        
    '-----------------------------------------------------------------------------------------------
    '--## ADUserQuery.m.validateDC -----------------------------------------------------------------
    '-----------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Validates the domain controller (if necessary) for script execution.
    '--
    '-----------------------------------------------------------------------------------------------
        Private Function validateDC

            Dim blnError  : blnError = False
            Dim blnResult : blnResult = False
            
            If NOT (openBind) Then
                If (IsNull(defaultDC)) Then '-- DC may have already been validated during first search.
                    If (IsNull(domainControllerList)) Then
                        If (IsNull(defaultDCList)) Then
                        '--## ERRORS: ADUserQuery::validateDC::1
                            Errors.AddItem 1, "CLASS.ActiveDirectoryUserQuery::validateDC", _
                                              "The 'openBind' property has a value of 'false' " & _
                                              "which requires the 'domainControllerList' " & _
                                              "property or the 'defaultDCList' property of the " & _
                                              "class to contain a value. Both of these " & _
                                              "properties are NULL." 
                        Else    
                            If (Len(Trim(defaultDCList)) > 0) Then
                                defaultDC = getDC(defaultDCList)
                            Else
                            '--## ERRORS: ADUserQuery::validateDC::2    
                                Errors.AddItem 2, "CLASS.ActiveDirectoryUserQuery::validateDC", _
                                              "The 'openBind' property has a value of 'false' " & _
                                              "which requires the 'domainControllerList' " & _
                                              "property or the 'defaultDCList' property of the " & _
                                              "class to contain a value. Both of these " & _
                                              "properties are NULL." 
                            End If
                        End If
                    Else
                        If (Len(Trim(domainControllerList)) > 0) Then
                            defaultDC = getDC(domainControllerList)
                        Else
                            If (IsNull(defaultDCList)) Then
                            '--## ERRORS: ADUserQuery::validateDC::3    
                                Errors.AddItem 3, "CLASS.ActiveDirectoryUserQuery::validateDC", _
                                                  "The 'openBind' property has a value of 'false' " & _
                                                  "which requires the 'domainControllerList' " & _
                                                  "property or the 'defaultDCList' property of the " & _
                                                  "class to contain a value. Both of these " & _
                                                  "properties are NULL." 
                            Else    
                                If (Len(Trim(defaultDCList)) > 0) Then
                                    defaultDC = getDC(defaultDCList)
                                Else
                                '--## ERRORS: ADUserQuery::validateDC::4    
                                    Errors.AddItem 4, "CLASS.ActiveDirectoryUserQuery::validateDC", _
                                                  "The 'openBind' property has a value of 'false' " & _
                                                  "which requires the 'domainControllerList' " & _
                                                  "property or the 'defaultDCList' property of the " & _
                                                  "class to contain a value. Both of these " & _
                                                  "properties are NULL." 
                                End If
                            End If
                        End If
                    End If '-- If (IsNull(domainControllerList))
                End If '-- If (NOT IsNull(defaultDC))
            End If '-- If (openBind)

        End Function

    '-----------------------------------------------------------------------------------------------
    '--## ADUserQuery.m.createLdapSearchString -----------------------------------------------------
    '-----------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Creates the LDAP Search String to execute.
    '--
    '-----------------------------------------------------------------------------------------------
        Private Sub createLdapSearchString

            Dim strSqlWhere
            Dim strSqlSelect
            Dim strSQLOrderBy : strSQLOrderBy = ""
            Dim strTemp
            Dim arrTemp
            Dim strDC
            Dim objFilterKey

            On Error Resume Next
            On Error Goto 0
            Err.Clear
            
            If (Len(Trim(orderBy))>0) Then strSQLOrderBy = " ORDER BY " & orderBy
            If NOT (openBind) Then strDC = defaultDC & "/"
                           
            strSqlSelect = "SELECT cn, ADSPath " & _
                             "FROM 'LDAP://" & strDC & _
                                   "DC=" & Join(Split(LCase(targetDomain),"."),",DC=") & "' " & _
                             " WHERE objectClass = '" & LCase(objectType) & "' "
                            If (LCase(objectType) = "user") Then _                     
                                strSqlSelect = strSqlSelect & "AND ObjectCategory = 'Person' "
            
            For Each objFilterKey in filters
                If (validateProperty(objFilterKey)) Then
                    If (Left(UCase(Trim(filters.item(objFilterKey))),3) = "IN(" OR _
                        Left(UCase(Trim(filters.item(objFilterKey))),4) = "IN (") Then
                        strTemp = Trim(filters.item(objFilterKey))
                        strTemp = Replace(strTemp,"IN (","IN(")
                        strTemp = Left(strTemp,Len(strTemp)-1)
                        strTemp = Mid(strTemp,4)
                        strTemp = Replace(strTemp,"','","'',''")
                        arrTemp = Split(strTemp,"','")
                        strSqlWhere = strSqlWhere & " AND (" 
                        For Each strTemp in arrTemp
                             strSqlWhere = strSqlWhere & objFilterKey & " = " & strTemp & " OR "
                        Next
                        strSqlWhere = Left(strSqlWhere,Len(strSqlWhere)-4) & ")"                       
                    Else
                        strSqlWhere = strSqlWhere & " AND " & _
                                      objFilterKey & " = '" & filters.item(objFilterKey) & "'" 
                    End If
                Else
                '--## ERRORS: ADUserQuery::createLdapSearchString::2
                    Errors.AddItem 2, "CLASS.ActiveDirectoryUserQuery::createLdapSearchString", _
                                           "'" & objFilterKey & "' is not a valid LDAP property."
                End If
            Next
            Set objFilterKey = Nothing

            ldapSearchString = strSqlSelect & strSqlWhere & strSQLOrderBy

        '--## ERRORS: ADUserQuery::createLdapSearchString::1
            If (Err) Then Errors.AddItem 1, "CLASS.ActiveDirectoryUserQuery::createLdapSearchString", _
                                           Err.Number & ":" & Err.Description & ":" & Err.Source

            On Error Goto 0

        End Sub

    '-----------------------------------------------------------------------------------------------
    '--## ADUserQuery.m.executeSearch --------------------------------------------------------------
    '-----------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Executes a search against LDAP with a predefined search string
    '--
    '-----------------------------------------------------------------------------------------------
        Public Function executeSearch
    
            Const con_ADS_PASS_ENCRYPT = True
            Const con_ADS_QUERY_TIMEOUT = 90
            Const con_ADS_QUERY_CACHERESULTS = False
            Const con_ADS_SCOPE_BASE = 0
            Const con_ADS_SCOPE_ONELEVEL = 1 
            Const con_ADS_SCOPE_SUBTREE = 2
            Const con_ADS_CHASE_REFERRALS_NEVER = &H0 
            Const con_ADS_CHASE_REFERRALS_SUBORDINATE = &H20 
            Const con_ADS_CHASE_REFERRALS_EXTERNAL = &H40 
    
            Dim   objDBConnection
            Dim   rsAdsiUsers
            Dim   objAdoCmd

            'On Error Resume Next
            Err.Clear

            Set objDBConnection = CreateObject("ADODB.Connection")
                objDBConnection.Provider = "ADsDSOObject"
                If NOT (impersonateUser) Then
                    objDBConnection.Properties("User ID") = userName
                    objDBConnection.Properties("Password") = userPassword
                End If
                objDBConnection.Properties("Encrypt Password") = con_ADS_PASS_ENCRYPT
                objDBConnection.Open "DS Query"
                Set objAdoCmd = CreateObject("ADODB.Command")
                Set objAdoCmd.ActiveConnection = objDBConnection
                    objAdoCmd.Properties("Timeout") = con_ADS_QUERY_TIMEOUT
                    objAdoCmd.Properties("searchscope") = con_ADS_SCOPE_SUBTREE
                    objAdoCmd.Properties("Chase referrals") = con_ADS_CHASE_REFERRALS_EXTERNAL
                    objAdoCmd.Properties("Cache Results") = con_ADS_QUERY_CACHERESULTS
                    objAdoCmd.CommandText = ldapSearchString
                    
                    Set rsAdsiUsers = CreateObject("ADODB.Recordset")
                    Set rsAdsiUsers = objAdoCmd.Execute
                        If (Err) Then 
                            If (Err.Number = -2147217911) Then
                            '--## ERRORS: ADUserQuery::executeSearch::1 
                                Errors.AddItem 1, "CLASS.ActiveDirectoryUserQuery::executeSearch", _
                                       Err.Number & ":" & Err.Description & ":" & Err.Source & _
                                       ". Check 'authorizedUserName', 'authorizedUserPassword' and " & _
                                       "'targetDomain' to ensure they are a valid user/domain " & _
                                       "combination."
                                Err.Clear
                            Else
                            '--## ERRORS: ADUserQuery::executeSearch::2   
                                Errors.AddItem 2, "CLASS.ActiveDirectoryUserQuery::executeSearch", _
                                               Err.Number & ":" & Err.Description & ":" & Err.Source & _
                                               Chr(13) & ldapSearchString
                                Err.Clear
                            End If
                        Else
                            resultsCount = rsAdsiUsers.RecordCount
                            If NOT (rsAdsiUsers.BOF AND rsAdsiUsers.EOF) Then
                                Redim results(rsAdsiUsers.RecordCount-1)
                                Do While NOT rsAdsiUsers.EOF
                                    If (LCase(objectType) = "user") Then    
                                        Set results(rsAdsiUsers.AbsolutePosition-1) = _
                                            getUser(rsAdsiUsers("ADSPath"))
                                    Else
                                        Set results(rsAdsiUsers.AbsolutePosition-1) = _
                                            getGroup(rsAdsiUsers("ADSPath"))
                                    End If
                                    rsAdsiUsers.Movenext
                                Loop
                            End If
                            rsAdsiUsers.Close
                        End If
                    Set rsAdsiUsers = Nothing
                Set objAdoCmd = Nothing
            Set objDBConnection = Nothing
            
         '--## ERRORS: ADUserQuery::executeSearch::3   
            If (Err) Then Errors.AddItem 3, "CLASS.ActiveDirectoryUserQuery::executeSearch", _
                                           Err.Number & ":" & Err.Description & ":" & Err.Source
            
            On Error Goto 0
    
        End Function

    '-----------------------------------------------------------------------------------------------
    '--## ADUserQuery.m.GetUser --------------------------------------------------------------------
    '-----------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Creates and Loads a user object with LDAP properties.
    '--
    '-----------------------------------------------------------------------------------------------
        Private Function getUser(ByVal strAdsUserPath)

            Const con_ADS_SECURE_AUTHENTICATION       = &H1 
            Const con_ADS_USE_ENCRYPTION              = &H2
            Const con_ADS_USE_SSL                     = &H2
            Const con_ADS_READONLY_SERVER             = &H4 
            Const con_ADS_PROMPT_CREDENTIALS          = &H8 
            Const con_ADS_NO_AUTHENTICATION           = &H10 
            Const con_ADS_FAST_BIND                   = &H20 
            Const con_ADS_USE_SIGNING                 = &H40 
            Const con_ADS_USE_SEALING                 = &H80 
            Const con_ADS_USE_DELEGATION              = &H100 
            Const con_ADS_SERVER_BIND                 = &H200
                    
            On Error Resume Next
            Err.Clear

            Dim objUser
            If (impersonateUser) Then
                Set objUser = GetObject(strAdsUserPath)
            Else 
                Set objUser = GetObject("LDAP:").OpenDSObject(strAdsUserPath, userName, _
                                                 userPassword, _
                                                 con_ADS_SERVER_BIND + _
                                                 con_ADS_USE_DELEGATION + _
                                                 con_ADS_SECURE_AUTHENTICATION)
            End If

            If (Err) Then 
            '--## ERRORS: ADUserQuery::getUser::1 
               Errors.AddItem 1, "CLASS.ActiveDirectoryUserQuery::getUser", _
                                 "Error '" & Err.Number & " -- " & Err.Description & " -- " & _
                                             Err.Source & "' " & "when accesing '" & _
                                             strAdsUserPath & "'."
            Else
                Dim objADUser
                Set objADUser = New ActiveDirectoryUser
                With objADUser
            '-- Set Windows NT Properties
               '-- ADSI/LDAP Directory Keys and Qualifiers -----------------------------------------
                    .adsPath                       = objUser.adsPath
                    .distinguishedName             = objUser.distinguishedName
                    .cn                            = objUser.cn
                    .ou                            = GetCollectionValues(objUser.ou)
                    .o                             = GetCollectionValues(objUser.o)
                    .objectClass                   = GetCollectionValues(objUser.objectClass)
                    .objectCategory                = GetCollectionValues(objUser.objectCategory)
                '-- User Account Properties --------------------------------------------------------
                    .sAMAccountName                = objUser.sAMAccountName   
                    .primaryGroupID                = objUser.primaryGroupID
                    .seeAlso                       = objUser.seeAlso
                    .info                          = objUser.info
                '-- User Name Properties -----------------------------------------------------------    
                    .givenName                     = objUser.givenName  
                    .initials                      = objUser.initials   
                    .sn                            = objUser.sn         
                    .description                   = objUser.description
                    .personalTitle                 = objUser.personalTitle
                '-- Electronic Adresses ------------------------------------------------------------
                    .mail                          = GetCollectionValues(objUser.mail)              
                    .url                           = GetCollectionValues(objUser.url)               
                '-- Physical Addresses -------------------------------------------------------------
                    .physicalDeliveryOfficeName    = objUser.physicalDeliveryOfficeName
                    .street                        = objUser.street
                    .postOfficeBox                 = objUser.postOfficeBox
                    .l                             = objUser.l            
                    .st                            = objUser.st           
                    .postalCode                    = objUser.postalCode   
                    .countryCode                   = objUser.countryCode  
                    .c                             = objUser.c            
                    .co                            = objUser.co           
                    .postalAddress                 = objUser.postalAddress          
                    .street                        = objUser.street         
                '-- Telephone Numbers --------------------------------------------------------------
                    .telephoneNumber               = objUser.telephoneNumber           
                    .homePhone                     = objUser.homePhone                    
                    .pager                         = objUser.pager                        
                    .mobile                        = objUser.mobile                       
                    .facsimileTelephoneNumber      = objUser.facsimileTelephoneNumber   
    
            '-- Set Additional Windows 2000 Properties
                '-- ADSI/LDAP Directory Keys and Qualifiers ----------------------------------------
                    .canonicalName                 = objUser.canonicalName
                '-- User Account Properties --------------------------------------------------------
                    .scriptPath                    = objUser.scriptPath       
                    .homeDrive                     = objUser.homeDrive                
                    .homeDirectory                 = objUser.homeDirectory
                    .comment                       = objUser.comment
                    .userPrincipalName             = objUser.userPrincipalName
                    .profilePath                   = objUser.profilePath
                '-- User Name Properties -----------------------------------------------------------    
                    .displayName                   = objUser.displayName
                    .middleName                    = objUser.middleName
                '-- Electronic Adresses ------------------------------------------------------------
                    .wWWHomePage                   = GetCollectionValues(objUser.wWWHomePage)   
                    .proxyAddresses                = GetCollectionValues(objUser.proxyAddresses)    
                '-- Physical Addresses -------------------------------------------------------------
                    .registeredAddress             = objUser.registeredAddress      
                    .postOfficeBox                 = objUser.postOfficeBox          
                    .preferredDeliveryMethod       = objUser.preferredDeliveryMethod
                    .homePostalAddress             = objUser.homePostalAddress      
                    .otherMailbox                  = objUser.otherMailbox           
                    .streetAddress                 = objUser.streetAddress 
                    .msExchHomeServerName          = objUser.msExchHomeServerName
                '-- Telephone Numbers --------------------------------------------------------------
                    .otherTelephone                = GetCollectionValues(objUser.otherTelephone)            
                    .otherHomePhone                = GetCollectionValues(objUser.otherHomePhone)               
                    .otherPager                    = GetCollectionValues(objUser.otherPager)                   
                    .otherMobile                   = GetCollectionValues(objUser.otherMobile)                  
                    .telephoneAssistant            = objUser.telephoneAssistant  
                    .otherFacsimileTelephoneNumber = GetCollectionValues(objUser.otherFacsimileTelephoneNumber)
                    .ipPhone                       = objUser.ipPhone                      
                    .otherIpPhone                  = GetCollectionValues(objUser.otherIpPhone)
                '-- Organizational Information -----------------------------------------------------
                    .info                          = objUser.info                         
                    .title                         = objUser.title        
                    .department                    = objUser.department   
                    .company                       = objUser.company      
                    .manager                       = objUser.manager 
                    .secretary                     = objUser.msexchassistantname     
                    .directReports                 = GetCollectionValues(objUser.directReports)
                    .memberOf                      = GetCollectionValues(objUser.memberOf)
                End With
                Set getUser = objADUser
                Set objADUser = Nothing
            
            '--## ERRORS: ADUserQuery::getUser::2   
                If (Err) Then Errors.AddItem 2, "CLASS.ActiveDirectoryUserQuery::getUser", _
                              Err.Number & " -- " & Err.Description & " -- " & Err.Source
            
            End If
            
            Set objUser = Nothing
            
            On Error Goto 0

        End Function

    '-----------------------------------------------------------------------------------------------
    '--## ADUserQuery.m.GetGroup -------------------------------------------------------------------
    '-----------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Creates and Loads a Group object with LDAP properties.
    '--
    '-----------------------------------------------------------------------------------------------
        Private Function getGroup(ByVal strAdsGroupPath)

            Const con_ADS_SECURE_AUTHENTICATION       = &H1 
            Const con_ADS_USE_ENCRYPTION              = &H2
            Const con_ADS_USE_SSL                     = &H2
            Const con_ADS_READONLY_SERVER             = &H4 
            Const con_ADS_PROMPT_CREDENTIALS          = &H8 
            Const con_ADS_NO_AUTHENTICATION           = &H10 
            Const con_ADS_FAST_BIND                   = &H20 
            Const con_ADS_USE_SIGNING                 = &H40 
            Const con_ADS_USE_SEALING                 = &H80 
            Const con_ADS_USE_DELEGATION              = &H100 
            Const con_ADS_SERVER_BIND                 = &H200
                    
            On Error Resume Next
            Err.Clear

            Dim objGroup
            If (impersonateUser) Then
                Set objGroup = GetObject(strAdsGroupPath)
            Else 
                Set objGroup = GetObject("LDAP:").OpenDSObject(strAdsGroupPath, userName, _
                                                 userPassword, _
                                                 con_ADS_SERVER_BIND + _
                                                 con_ADS_USE_DELEGATION + _
                                                 con_ADS_SECURE_AUTHENTICATION)
            End If

            If (Err) Then 
            '--## ERRORS: ADUserQuery::getGroup::1 
               Errors.AddItem 1, "CLASS.ActiveDirectoryUserQuery::getGroup", _
                                 "Error '" & Err.Number & " -- " & Err.Description & " -- " & _
                                             Err.Source & "' " & "when accesing '" & _
                                             strAdsGroupPath & "'."
            Else
                Dim objADUser
                Dim objADGroup
                Set objADGroup = New ActiveDirectoryGroup
                With objADGroup
                    .adsPath     = objGroup.adsPath
                    .cn          = objGroup.cn
                    .description = objGroup.description
		            .members	 = ""
                End With

        		For Each objADUser in objGroup.members
        		    If (InStr(objADUser.Name,"S-1") = 0) Then
        		        If (objADGroup.members = "") Then 
        		            objADGroup.members = Mid(objADUser.Name,4)
        		        Else    
        		            objADGroup.members = objADGroup.members & ", " & Mid(objADUser.Name,4)
        		        End If
        		    End if
        		Next 
        		
                Set getGroup = objADGroup
                Set objADGroup = Nothing

            '--## ERRORS: ADUserQuery::getGroup::2
                If (Err) Then Errors.AddItem 2, "CLASS.ActiveDirectoryUserQuery::getGroup", _
                                                "Error '" & Err.Number & " -- " & _
                                                Err.Description & " -- " & Err.Source & "'."
            
            End If
            
            Set objGroup = Nothing
            
            On Error Goto 0

        End Function

    '-----------------------------------------------------------------------------------------------
    '--## ADUserQuery.m.GetCollectionValues --------------------------------------------------------
    '-----------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Creates a semi-colon seperated list of collection values.
    '--
    '-----------------------------------------------------------------------------------------------
        Private Function GetCollectionValues(colItems)
        
            Dim objItem
            Dim strItems

            On Error Resume Next
            Err.Clear
            
            Select Case VarType(colItems)
                   Case (8204)        
                        For Each objItem in colItems
                            strItems = strItems & objItem & ";"
                        Next
                        If (Len(strItems)>0) Then
                            GetCollectionValues = Left(strItems,Len(strItems)-1)
                        Else
                            GetCollectionValues = ""
                        End If
                   Case (8192) : GetCollectionValues = Join(colItems,";")
                   Case (10)   : GetCollectionValues = "ERROR: " & colItems.Description
                   Case Else   : GetCollectionValues = CStr(colItems)
            End Select
            
         '--## ERRORS: ADUserQuery::getCollectionValues::1   
            If (Err) Then Errors.AddItem 1, "CLASS.ActiveDirectoryUserQuery::getCollectionValues", _
                          Err.Number & " -- " & Err.Description & " -- " & Err.Source
            
            On Error Goto 0
        
        End Function

    '-----------------------------------------------------------------------------------------------
    '--## ADUserQuery.m.validateProperty -----------------------------------------------------------
    '-----------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Validate an LDAP Property.
    '--
    '-----------------------------------------------------------------------------------------------
        Private Function validateProperty(ByVal strPropname)

            Dim strValidProperties
                strValidProperties = " cn canonicalName distinguishedName ou o AdsPath objectClass " & _
                                     " userPrincipalName sAMAccountName profilePath scriptPath " & _
                                     " homeDrive homeDirectory seeAlso givenName initials sn " & _
                                     " description personalTitle middleName Name FullName mail " & _
                                     " proxyAddresses wWWHomePage url physicalDeliveryOfficeName " & _
                                     " streetAddress postOfficeBox registeredAddress otherMobile " & _
                                     " homePostalAddress l st postalCode countryCode c co Address " & _
                                     " otherTelephone homePhone otherHomePhone pager otherPager mobile " & _
                                     " facsimileTelephoneNumber otherFacsimileTelephoneNumber ipPhone " & _
                                     " info telephoneAssistant title department company manager " & _
                                     " secretary comment memberOf msExchHomeServerName objectCategory " & _
                                     " primaryGroupID displayName otherMailbox street otherIpPhone " & _
                                     " preferredDeliveryMethod telephoneNumber directReports " 

            If (InStr(LCase(strValidProperties)," " & LCase(Trim(strPropname)) & " ") > 0) Then
                validateProperty = true
            Else
                validateProperty = false
            End If

        End Function

    End CLASS

'---------------------------------------------------------------------------------------------------
'---------------------------------------------------------------------------------------------------
'--## @ ADUser Class -------------------------------------------------------------------------------
'---------------------------------------------------------------------------------------------------
'---------------------------------------------------------------------------------------------------
    
'---------------------------------------------------------------------------------------------------
'--## @ ADUser Description -------------------------------------------------------------------------
'---------------------------------------------------------------------------------------------------
'-- 
'-- This class is a single Active Directory user object. It contains all of the properties of a 
'-- standard Microsoft Active Directory / LDAP user container. It has no methods or collections.
'--
'---------------------------------------------------------------------------------------------------

'---------------------------------------------------------------------------------------------------
'--## @ ADUser Object Model ------------------------------------------------------------------------
'---------------------------------------------------------------------------------------------------
'--
'-- ** LDAP/AD PATH FORMAT: 
'--    CN=B12345,OU=NeenahAdmin,OU=US,OU=NorthAmerica,OU=Accounts,DC=kcc,DC=com
'--
'-- ** All properties are typed as string.
'--
'-- PROPERTIES -------------------------------------------------------------------------------------
'--
'-- NAME                           FRIENDLY NAME      DESCRIPTION
'-- ------------------------------ ------------------ ----------------------------------------------
'-- cn                             UserID             Canonical Name. (B12345)
'-- canonicalName                  UserID             Canonical Name (B12345)
'-- distinguishedName              ADPath             Distinguished Name - AD Path (CN=,OU=,DC=,DC=)
'-- ou                                                LDAP Organizational Unit Name
'-- o                                                 LDAP Organizational Name
'-- AdsPath                        LDAPPath           LDAP location in AD (LDAP://CN=,OU=,...)
'-- objectClass                    LDAPClass          LDAP Object Class
'-- objectCategory                 LDAPCategory       LDAP Object Category
'-- userPrincipalName                                 User Logon Name (<EMAIL>)
'-- sAMAccountName                 LogonName          Logon Name for Pre-2k (logonname)
'-- profilePath                                       Path for the users Windows Profile
'-- scriptPath                                        Path for the users startup scripts
'-- primaryGroupID                                    Numeric Group ID for users Primary KCUS Group. 
'-- homeDrive                                         Drive letter mapped to user's home Directory.
'-- homeDirectory                                     UNC path to the user's private folder.
'-- seeAlso                                           A Comment Field.
'-- givenName                      FirstName          First Name
'-- initials                                          Initials (FML), not middle initial.
'-- sn                             Surname,LastName   Surname, Last Name
'-- displayName                    GALName            Display Name (LastName, FirstName MI)
'-- description                                       Description (FirstName MI LastName)
'-- personalTitle                                     Title (Mr. Ms, etc..)
'-- middleName                                        Middle Name
'-- Name                                              First and Last Name
'-- FullName                                          FirstName MiddleName LastName
'-- mail                           Email              E-Mail
'-- otherMailbox
'-- proxyAddresses                 emails             All Email Addresses
'-- wWWHomePage                    WebPage            Web Page
'-- url                            WebPages           Web Page Other (a list)
'-- physicalDeliveryOfficeName     Office             Office
'-- street                                            Street Address
'-- streetAddress                                     Street Address
'-- postOfficeBox                  POBox              P.O. Box
'-- registeredAddress
'-- preferredDeliveryMethod
'-- homePostalAddress
'-- l                              City               City
'-- st                             State,Province     State/Province
'-- postalCode                     Zip,ZipCode        Zip/Postal Code
'-- countryCode                                       Country/region (pulldown)
'-- c                              Country            2-Digit Country Code (US)
'-- co                             CountryName        Country (UNITED STATES or USA)
'-- Address                                           Street, POBox, City, State, Zip
'-- telephoneNumber                Phone,Telephone    Telephone Number
'-- otherTelephone                 Phones,Telephones  Telephone Number Other (a list)
'-- homePhone                                         Home
'-- otherHomePhone                 HomePhones         Home Other (a list)
'-- pager                                             Pager
'-- otherPager                     Pagers             Pager Other (a list)
'-- mobile                                            Mobile
'-- otherMobile                    Mobiles            Mobile Other (a list)
'-- facsimileTelephoneNumber       Fax                Fax
'-- otherFacsimileTelephoneNumber  Faxes              Fax Other (a list)
'-- ipPhone                                           IP Phone
'-- otherIpPhone                   IPPhones           IP Phone Other (a list)
'-- info                           Notes              Misceleanous Notes
'-- telephoneAssistant             AssistantPhone     Secretary Phone                     
'-- title                                             Title
'-- department                                        Department
'-- company                                           Company
'-- manager                                           Manager
'-- directReports                                     Direct Reports (a list)
'-- secretary                                         User's Secretary
'-- comment
'-- msExchHomeServerName                              K-C Mailserver ADS Path (LDAP Standard Property)
'-- exchangePath                                      K-C Mailserver ADS Path
'-- exchangeServer                                    K-C Mailserver Name
'-- memberOf                                          A ; delimited list of AD Group paths.
'-- groupADsPaths                                     A ; delimited list of AD Group Paths.
'-- groupNames                                        A ; delimited, sorted list of AD Group names.
'-- AllProperties                                     A summary of all standard LDAP properties
'-- UserSummary                                       A basic user summary of Name, address, phones 
'--  
'---------------------------------------------------------------------------------------------------

'---------------------------------------------------------------------------------------------------
'--## @ ADUser Object ------------------------------------------------------------------------------
'---------------------------------------------------------------------------------------------------

    CLASS ActiveDirectoryUser

    '-----------------------------------------------------------------------------------------------
    '--## @ ADUser Properties ----------------------------------------------------------------------
    '-----------------------------------------------------------------------------------------------

    '-----------------------------------------------------------------------------------------------
    '--## ADUser.P.Base - EXPOSED - ADSI PROPERTIES MAPPED TO COMMON NAMES -------------------------
    '-----------------------------------------------------------------------------------------------
    '-- Error Information
        Public Errors                        '-- Errors collection for object.
    '-- ADSI Authentication Information
        Public AuthUser                      '-- The user to authenticate to AD as.
        Public AuthPassword                  '-- The password to authenticate to AD with.
    '-- ADSI Key Information -----------------------------------------------------------------------
        Public cn                            '-- Canonical Name. (B12345)
        Public canonicalName                 '-- Canonical Name (B12345)
        Public distinguishedName             '-- Distinguished Name (CN=B12345,OU=Users,DC=kcc,DC=com)
        Public ou                            '-- LDAP Organizational Unit Name
        Public o                             '-- LDAP Organizational Name
        Public AdsPath                       '-- LDAP location in Active Directory.
        Public objectClass                   '-- LDAP Object Class
        Public objectCategory                '-- LDAP Object Category
    '-- ADSI User Accound Information --------------------------------------------------------------
        Public userPrincipalName             '-- User Logon Name (<EMAIL>)
        Public sAMAccountName                '-- Logon Name for Pre-2k (logonname)
        Public profilePath                   '-- Path for the users Windows Profile
        Public scriptPath                    '-- Path for the users startup scripts
        Public primaryGroupID                '-- Numeric Group ID for users Primary KCUS Group. 
        Public homeDrive                     '-- Drive letter mapped to user's home Directory.
        Public homeDirectory                 '-- UNC path to the user's private folder.
        Public msExchHomeServerName
        Public memberOf                      '-- DIRECT (not nested) groups that a user is a member of.
        Public seeAlso                       '-- A Comment Field.
    '-- User Name Information ----------------------------------------------------------------------                                           
        Public givenName                     '-- First Name
        Public initials                      '-- Initials (FML), not middle initial.
        Public sn                            '-- Last Name
        Public displayName                   '-- Display Name
        Public description                   '-- Description
        Public personalTitle                 '-- Title (Mr. Ms, etc..)
        Public middleName                    '-- Middle Name
    '-- Electronic Addresses -----------------------------------------------------------------------
        Public mail                          '-- E-Mail
        Public proxyAddresses                '-- Other Email Addresses
        Public wWWHomePage                   '-- Web Page
        Public url                           '-- Web Page Other (a list)
    '-- Pysical Addresses Information --------------------------------------------------------------
        Public physicalDeliveryOfficeName    '-- Office
        Public street                        '-- Street
        Public streetAddress
        Public postOfficeBox                 '-- P.O. Box
        Public l                             '-- City
        Public st                            '-- State/Province
        Public postalCode                    '-- Zip/Postal Code
        Public countryCode                   '-- Country/region (pulldown)
        Public c                             '-- Country (US)
        Public co                            '-- Country (UNITED STATES)
        Public registeredAddress
        Public postalAddress
        Public preferredDeliveryMethod
        Public homePostalAddress
        Public otherMailbox
    '-- ADSI Telephones Information ----------------------------------------------------------------                                                            
        Public telephoneNumber               '-- Telephone Number
        Public otherTelephone                '-- Telephone Number Other (a list)
        Public homePhone                     '-- Home
        Public otherHomePhone                '-- Home Other (a list)
        Public pager                         '-- Pager
        Public otherPager                    '-- Pager Other (a list)
        Public mobile                        '-- Mobile
        Public otherMobile                   '-- Mobile Other (a list)
        Public facsimileTelephoneNumber      '-- Fax
        Public otherFacsimileTelephoneNumber '-- Fax Other (a list)
        Public ipPhone                       '-- IP Phone
        Public otherIpPhone                  '-- IP Phone Other (a list)
        Public info                          '-- Notes
        Public comment
        Public telephoneAssistant         
    '-- Adsi Organization Tab ----------------------------------------------------------------------                                            
        Public title                         '-- Title
        Public department                    '-- Department
        Public company                       '-- Company
        Public manager                       '-- Manager
        Public directReports                 '-- Direct Reports (a list)
        Public secretary                     '-- User's Secretary

    '-----------------------------------------------------------------------------------------------
    '--## ADUser.P.Friendly - EXPOSED - ADSI PROPERTIES MAPPED TO COMMON NAMES ---------------------
    '-----------------------------------------------------------------------------------------------
        Public Property Get UserId         : UserId = cn                                              : End Property
        Public Property Get ADPath         : ADPath = distinguishedName                               : End Property                                                           
        Public Property Get LDAPPath       : LDAPPath = AdsPath                                       : End Property                                                    
        Public Property Get LDAPClass      : LDAPClass = objectClass                                  : End Property
        Public Property Get LDAPCategory   : LDAPCategory = objectCategory                            : End Property
        Public Property Get LogonName      : LogonName = sAMAccountName                               : End Property        
        Public Property Get FirstName      : FirstName = givenName                                    : End Property
        Public Property Get LastName       : LastName = sn                                            : End Property
        Public Property Get Surname        : Surname = sn                                             : End Property
        Public Property Get GALName        : GALName = displayName                                    : End Property
        Public Property Get Email          : Email = mail                                             : End Property
        Public Property Get Emails         : Emails = proxyAddresses                                  : End Property
        Public Property Get WebPage        : WebPage = wWWHomePage                                    : End Property
        Public Property Get WebPages       : WebPages = udfmakeList(wWWHomePage,url)                  : End Property
        Public Property Get Office         : Office = physicalDeliveryOfficeName                      : End Property
        Public Property Get POBox          : POBox = postOfficeBox                                    : End Property
        Public Property Get City           : City = l                                                 : End Property
        Public Property Get State          : State = st                                               : End Property
        Public Property Get Province       : Province = st                                            : End Property
        Public Property Get Zip            : Zip = postalCode                                         : End Property
        Public Property Get ZipCode        : ZipCode = postalCode                                     : End Property
        Public Property Get Country        : Country = c                                              : End Property
        Public Property Get CountryName    : CountryName = co                                         : End Property
        Public Property Get phone          : phone = telephoneNumber                                  : End Property
        Public Property Get phones         : phones = udfmakeList(telephoneNumber,otherTelephone)     : End Property
        Public Property Get telephone      : telephone = telephoneNumber                              : End Property
        Public Property Get telephones     : telephones = udfmakeList(telephoneNumber,otherTelephone) : End Property
        Public Property Get homephones     : homephones = udfmakeList(homePhone,otherHomePhone)       : End Property
        Public Property Get pagers         : pagers = udfmakeList(pager,otherPager)                   : End Property
        Public Property Get mobiles        : mobiles = udfmakeList(mobile,otherMobile)                : End Property
        Public Property Get fax            : fax = facsimileTelephoneNumber                           : End Property
        Public Property Get faxes          : faxes = udfmakeList(fax, otherFacsimileTelephoneNumber)  : End Property
        Public Property Get ipphones       : ipphones = udfmakeList(ipPhone, otherIpPhone)            : End Property
        Public Property Get AssistantPhone : AssistantPhone = telephoneAssistant                      : End Property
        Public Property Get SecretaryPhone : SecretaryPhone = telephoneAssistant                      : End Property
        Public Property Get Assistant      : Assistant = secretary                                    : End Property
        Public Property Get groupADsPaths  : groupADsPaths = memberOf                                 : End Property
        Public Property Get exchangePath   : exchangePath = msExchHomeServerName                      : End Property
         

    '-----------------------------------------------------------------------------------------------
    '--## ADUser.P.Custom - EXPOSED - ADD ADDITIONAL PROPERTIES TO STANDARD SET --------------------
    '-----------------------------------------------------------------------------------------------
        Public Property Get FullName       : FullName = givenName & " " & middleName & " " & sn       : End Property
        Public Property Get Name           : Name = givenName & " " & sn                              : End Property

        Public Property Get exchangeServer 
            Dim arrTemp
            If (Len(msExchHomeServerName)>0) Then
            '-- /o=KCC/ou=MSXSITE00/cn=Configuration/cn=Servers/cn=USTCAX35
                arrTemp = Split(msExchHomeServerName,"=")
                exchangeServer = arrTemp(UBound(arrTemp))
            Else
                exchangeServer = ""
            End If
        End Property       

        Public Property Get Address
            Address = streetAddress & vbCrLf
            If (Len(postOfficeBox)>0) Then Address = Address & postOfficeBox & vbCrLf
            Address = Address & City & ", " & State & "  " & ZipCode
        End Property

        Public Property Get groupNames
            Dim strGroupName, strGroupNames, arrGroupNames, i, j, x, strTemp
            arrGroupNames = Split(memberOf,";")
            For x = 0 to UBound(arrGroupNames)
                If (Left(UCase(arrGroupNames(x)),3)="CN=" AND _
                    InStr(arrGroupNames(x),",") > 0) Then
                    arrGroupNames(x) = Mid(arrGroupNames(x),4,InStr(arrGroupNames(x),",")-4)
                Else
                    arrGroupNames(x) = ""
                End If
            Next            
            For i = UBound(arrGroupNames) - 1 To 0 Step -1
                For j = 0 to i
                    If UCase(arrGroupNames(j)) > UCase(arrGroupNames(j+1)) Then
                        strTemp = arrGroupNames(j+1)
                        arrGroupNames(j+1) = arrGroupNames(j)
                        arrGroupNames(j) = strTemp
                    End If
                Next
            Next 
            strGroupNames = Join(arrGroupNames,"; ")
            For x = 1 to 20
                strGroupNames = Replace(strGroupNames,"; ; ","; ")
            Next
            If (Len(strGroupNames) > 1) Then
                groupNames = strGroupNames
            Else
                groupNames = ""
            End If                          
        End Property
        
        Public Property Get UserSummary
            UserSummary = "USER SUMMARY:" & vbcrlf & _
                          "    User ID: "                       & sAMAccountName & vbCrLf & _
                          "    Name: "                          & Name & vbCrLf & _                           
                          "    Initials: "                      & initials & vbCrLf & _  
                          "    Display Name: "                  & displayName & vbCrLf & _  
                          "    Description: "                   & description & vbCrLf & _                   
                          "    Notes: "                         & info & vbCrLf & _                          
                          "    E-Mail Address: "                & email & vbCrLf & _ 
                          "    E-Mail Addresses: "              & emails & vbCrLf & _ 
                          "    WWW Home Page: "                 & wWWHomePage & vbCrLf & _ 
                          "    Work Phone Number: "             & telephoneNumber & vbCrLf & _   
                          "    Home Phone Number: "             & homePhone & vbCrLf & _
                          "    Mobile Phone Number: "           & mobile & vbCrLf & _                     
                          "    Pager Number: "                  & pager & vbCrLf & _                         
                          "    Fax: "                           & fax & vbCrLf & _      
                          "    Title: "                         & title & vbCrLf & _                         
                          "    Department: "                    & department & vbCrLf & _                    
                          "    Company: "                       & company & vbCrLf & _                       
                          "    Manager: "                       & manager & vbCrLf & _                     
                          "    Direct Reports: "                & directReports & vbCrLf & _
                          "    Office: "                        & office & vbCrLf & _    
                          "    Street Address: "                & streetAddress & vbCrLf & _                 
                          "    P. O. Box: "                     & postOfficeBox & vbCrLf & _                 
                          "    City: "                          & l & vbCrLf & _                             
                          "    State: "                         & st & vbCrLf & _                            
                          "    Zip Code: "                      & postalCode & vbCrLf & _                    
                          "    Country: "                       & co & vbCrLf & _
                          "    Groups: "                        & groupNames & vbCrLf                          
        End Property
    '-----------------------------------------------------------------------------------------------
    '--## ADUser.M.Functions -----------------------------------------------------------------------
    '-----------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Utility functions for the class.
    '--
    '-----------------------------------------------------------------------------------------------
        Private Function udfmakeList(strSingle, strList)
            
            If (strSingle = "" AND strList = "") Then 
                udfmakeList = ""
            ElseIf (strSingle > "" AND strList = "") Then
                udfmakeList = strSingle
            ElseIf (strSingle = "" AND strList > "") Then
                udfmakeList = strList
            ElseIf (strSingle > "" AND strList > "") Then
                udfmakeList = strSingle & ";" & strList 
            Else
                udfmakeList = "unhandled condition"  
            End If

        End Function

    '-----------------------------------------------------------------------------------------------
    '--## ADUser.P.Debugging -----------------------------------------------------------------------
    '-----------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Returns a text string of all the object's real LDAP Properties
    '--
    '-----------------------------------------------------------------------------------------------
        Public Property Get AllProperties
        
            AllProperties = "LDAP DIRECTORY INFORMATION"           & vbcrlf & _
                             "    distinguishedName: "             & distinguishedName & vbCrLf & _ 
                             "    AdsPath: "                       & AdsPath & vbCrLf & _                          
                             "    cn: "                            & cn & vbCrLf & _             
                             "    canonicalName: "                 & canonicalName & vbCrLf & _                     
                             "    ou: "                            & ou & vbCrLf & _                      
                             "    o: "                             & o & vbCrLf & _                            
                             "    objectClass: "                   & objectClass & vbCrLf & _                   
                             "    objectCategory: "                & objectCategory & vbCrLf  & _
                             "USER ACCOUNT INFORMATION"            & vbcrlf & _
                             "    sAMAccountName: "                & sAMAccountName & vbCrLf & _ 
                             "    userPrincipalName: "             & userPrincipalName & vbCrLf & _
                             "    profilePath: "                   & profilePath & vbCrLf & _                          
                             "    scriptPath: "                    & scriptPath & vbCrLf & _             
                             "    primaryGroupID: "                & primaryGroupID & vbCrLf & _                      
                             "    homeDrive: "                     & homeDrive & vbCrLf & _             
                             "    homeDirectory: "                 & homeDirectory & vbCrLf & _                     
                             "    exchangeServer: "                & exchangeServer & vbCrLf & _  
                             "    exchangePath: "                  & exchangePath & vbCrLf & _
                             "    seeAlso: "                       & seeAlso & vbCrLf & _
                             "    comment: "                       & comment & vbCrLf & _
                             "    info: "                          & info & vbCrLf & _
                             "USER NAME INFORMATION"               & vbcrlf & _                    
                             "    givenName: "                     & givenName & vbCrLf & _ 
                             "    sn: "                            & sn & vbCrLf & _                          
                             "    initials: "                      & initials & vbCrLf & _             
                             "    displayName: "                   & displayName & vbCrLf & _                     
                             "    description: "                   & description & vbCrLf & _                      
                             "    personalTitle: "                 & personalTitle & vbCrLf & _                            
                             "    middleName: "                    & middleName & vbCrLf & _                 
                             "ELECTRONIC ADDRESSES"                & vbcrlf & _                    
                             "    mail: "                          & mail & vbCrLf & _ 
                             "    proxyAddresses: "                & proxyAddresses & vbCrLf & _
                             "    wWWHomePage: "                   & wWWHomePage & vbCrLf & _
                             "    url: "                           & url & vbCrLf & _   
                             "PHYSICAL ADDRESSES"                  & vbcrlf & _ 
                             "    physicalDeliveryOfficeName: "    & physicalDeliveryOfficeName & vbCrLf & _                          
                             "    street: "                        & street & vbCrLf & _                          
                             "    streetAddress: "                 & streetAddress & vbCrLf & _                          
                             "    postOfficeBox: "                 & postOfficeBox & vbCrLf & _                          
                             "    l: "                             & l & vbCrLf & _                          
                             "    st: "                            & st & vbCrLf & _
                             "    postalCode: "                    & postalCode & vbCrLf & _
                             "    countryCode: "                   & countryCode & vbCrLf & _
                             "    c: "                             & c & vbCrLf & _
                             "    co: "                            & co & vbCrLf & _
                             "    registeredAddress: "             & registeredAddress & vbCrLf & _
                             "    postalAddress: "                 & postalAddress & vbCrLf & _
                             "    postOfficeBox: "                 & postOfficeBox & vbCrLf & _
                             "    preferredDeliveryMethod: "       & preferredDeliveryMethod & vbCrLf & _
                             "    homePostalAddress: "             & homePostalAddress & vbCrLf & _
                             "    otherMailbox: "                  & otherMailbox & vbCrLf & _
                             "TELEPHONE NUMBERS"                   & vbcrlf & _
                             "    telephoneNumber: "               & telephoneNumber & vbCrLf & _
                             "    otherTelephone: "                & otherTelephone & vbCrLf & _
                             "    telephones: "                    & telephones & vbCrLf & _
                             "    homePhone: "                     & homePhone & vbCrLf & _
                             "    otherHomePhone: "                & otherHomePhone & vbCrLf & _
                             "    homePhones: "                    & homePhones & vbCrLf & _
                             "    pager: "                         & pager & vbCrLf & _
                             "    otherPager: "                    & otherPager & vbCrLf & _
                             "    pagers: "                        & pagers & vbCrLf & _
                             "    mobile: "                        & mobile & vbCrLf & _
                             "    otherMobile: "                   & otherMobile & vbCrLf & _
                             "    mobiles: "                       & mobiles & vbCrLf & _
                             "    facsimileTelephoneNumber: "      & facsimileTelephoneNumber & vbCrLf & _
                             "    otherFacsimileTelephoneNumber: " & otherFacsimileTelephoneNumber & vbCrLf & _
                             "    faxes: "                         & faxes & vbCrLf & _
                             "    ipPhone: "                       & ipPhone & vbCrLf & _
                             "    otherIpPhone: "                  & otherIpPhone & vbCrLf & _
                             "    ipPhones: "                      & ipPhones & vbCrLf & _
                             "ORGANIZATIONAL INFORMATION"          & vbcrlf & _
                             "    title: "                         & title & vbCrLf & _
                             "    department: "                    & department & vbCrLf & _
                             "    company: "                       & company & vbCrLf & _
                             "    manager: "                       & manager & vbCrLf & _
                             "    directReports: "                 & directReports & vbCrLf & _
                             "    secretary: "                     & secretary & vbcrlf & _
                             "GROUP INFORMATION"                   & vbcrlf & _
                             "    groupNames: "                    & Replace(groupNames,";",";" & vbCrLf & String(16," ")) & vbCrLf & _
                             "    groupADsPaths: "                 & Replace(groupADsPaths,";",";" & vbCrLf & String(19," ")) & vbCrLf
        End Property
    
    End Class


'---------------------------------------------------------------------------------------------------
'---------------------------------------------------------------------------------------------------
'--## @ ADGroup Class ------------------------------------------------------------------------------
'---------------------------------------------------------------------------------------------------
'---------------------------------------------------------------------------------------------------
    
'---------------------------------------------------------------------------------------------------
'--## @ ADGroup Description ------------------------------------------------------------------------
'---------------------------------------------------------------------------------------------------
'-- 
'-- This class is a single Active Directory Group object. It contains all of the properties of a 
'-- standard Microsoft standard Microsoft Active Directory / LDAP user container. It has no methods 
'-- or collections. Active Directory / LDAP Group container. It has no methods or collections.
'--
'---------------------------------------------------------------------------------------------------

'---------------------------------------------------------------------------------------------------
'--## @ ADGroup Object Model -----------------------------------------------------------------------
'---------------------------------------------------------------------------------------------------
'--
'-- ** LDAP/AD PATH FORMAT: CN=B12345,OU=NeenahAdmin,OU=US,OU=NorthAmerica,OU=Accounts,DC=kcc,DC=com
'--
'-- ** All properties are typed as string.
'--
'-- PROPERTIES -------------------------------------------------------------------------------------
'--
'--   NAME           FRIENDLY NAME        DESCRIPTION
'-- - ------------   -------------   -------------------------------------------------------- 
'--   cn             GroupName       Canonical Name. (MIS_C)
'--   AdsPath        LDAPPath        LDAP location in Active Directory (LDAP://CN=,OU=,...)
'--   description                    Description (\\kcc.com\webservices\..\NA.US.kcc.com\MIS)
'--   members                        Comma-separated string of all members of the group
'--  
'---------------------------------------------------------------------------------------------------

    CLASS ActiveDirectoryGroup

    '-----------------------------------------------------------------------------------------------
    '--## @ ADGroup Properties ---------------------------------------------------------------------
    '-----------------------------------------------------------------------------------------------

    '-----------------------------------------------------------------------------------------------
    '--## ADGroup.P.Base - EXPOSED - ADSI PROPERTIES MAPPED TO COMMON NAMES ------------------------
    '-----------------------------------------------------------------------------------------------
    '-- ADSI Key Information -----------------------------------------------------------------------
        Public cn                            '-- Canonical Name.
        Public AdsPath                       '-- LDAP location in Active Directory.
        Public description                   '-- Description
	    Public members                       '-- Comma-separated string of all members of the group
    '-----------------------------------------------------------------------------------------------
    '--## ADGroup.P.Friendly - EXPOSED - ADSI PROPERTIES MAPPED TO COMMON NAMES --------------------
    '-----------------------------------------------------------------------------------------------
        Public Property Get GroupId          : GroupId = cn                       : End Property
        Public Property Get LDAPPath         : LDAPPath = AdsPath                 : End Property                                                    
        Public Property Get LDAPClass        : LDAPClass = objectClass            : End Property
        Public Property Get LDAPCategory     : LDAPCategory = objectCategory      : End Property

    '-----------------------------------------------------------------------------------------------
    '--## ADGroup.P.Debugging ----------------------------------------------------------------------
    '-----------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Returns a text string of all the object's real LDAP Properties (mainly used for
    '-- debugging).
    '--
    '-----------------------------------------------------------------------------------------------
        Public Property Get AllProperties
        
            AllProperties = "LDAP DIRECTORY INFORMATION"    & vbcrlf & _
                             "    AdsPath: "                & AdsPath & vbcrlf & _                          
                             "    Group Name: "             & cn & vbcrlf & _             
                             "    Description: "            & description & vbcrlf & _                      
			                 "    Members: "                & members
                    
        End Property
    
    End Class

'---------------------------------------------------------------------------------------------------        
'---------------------------------------------------------------------------------------------------
'--## # SUPPORTING CLASSES -------------------------------------------------------------------------
'---------------------------------------------------------------------------------------------------
'---------------------------------------------------------------------------------------------------

'---------------------------------------------------------------------------------------------------
'--## @ ErrorItems Description ---------------------------------------------------------------------
'---------------------------------------------------------------------------------------------------
'-- 
'-- DESCRIPTION: Supporting classes used to provide error information for the ADUserQuery object.
'--
'---------------------------------------------------------------------------------------------------

    CLASS ErrorItems

        Public Items

    	Private Sub Class_Initialize
           Set Items = CreateObject("Scripting.Dictionary")
    	End Sub

        Public Sub AddItem(ByVal intItemNumber, ByVal strSource, ByVal strItemDescription)
          Dim objItem
          Set objItem = New ErrorItem
              objItem.Number = CStr(intItemNumber)
              objItem.Description = CStr(strItemDescription)
              objItem.Source = CStr(strSource)
              Set Items(objItem) = objItem
          Set objItem = Nothing
        End Sub
        
        Public Sub Clear
            Items.RemoveAll
        End Sub

    End CLASS

    CLASS ErrorItem
    
        Public Number
        Public Description
        Public Source
        
    End CLASS
    
%>