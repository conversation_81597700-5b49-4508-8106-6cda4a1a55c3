
<html xmlns:m="http://schemas.microsoft.com/office/2004/12/omml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">


<TITLE>Select STO</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->

<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

 <%    Dim objDAC, strSQL, MyRec, objMOC, rstFiber, strRelease, strR, strID, rstFiber2, rstFiber3
	
       Dim objGeneral, strDate, MyConn, strSTO
strDate = formatdatetime(Now(),2)
	
	
       set objGeneral = new ASP_CLS_General
  Call getData()   
if objGeneral.IsSubmit() Then
strSTO = request.form("FIBER")
IF len(strSTO)> 4 then
strsql = "Select SAP_Nbr from tblSTOInbound where Load_nbr = " & strSTO
   Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then
strSAP = MyRec("SAP_NBR")
else
strSAP = "UNKNOWN"
end if
MyRec.close

If strSAP <> "UNKNOWN" Then
strsql = "Select Category from tblBrokeSAP where SAP = '" & strSAP & "'"
   Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
strCategory = MyRec("Category")
MyRec.close


IF strCategory ="FIBER" Then
Response.redirect("EnterSTOReceipt.asp?id=" & strSTO)
elseif strCategory = "BROKE" Then
Response.redirect("STO_Broke_Receipt.asp?id=" & strSTO)
elseif strCategory = "WADDING" then
Response.redirect("EnterSTOWaddingReceipt.asp?id=" & strSTO)
elseif strCategory = "KDF" then
Response.redirect("EnterSTOKDFReceipt.asp?id=" & strSTO)

else 
Response.write("<br><font face=arial color=red size=3)<b>We can not find the category for SAP number " & strSAP & " associated with the selected load.  Please go to System Maintenance, STO Type, to set it up.</b></font>")

end if

end if
End if


If len(request.form("SAP")) > 0 then
strSAP = Trim(request.form("SAP"))
strsql = "Select Category from tblBrokeSAP where SAP = '" & strSAP & "'"
   Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
strCategory = MyRec("Category")
MyRec.close


IF strCategory ="FIBER" Then
Response.redirect("EnterSTOReceipt.asp?s=" & strSAP)
elseif strCategory = "BROKE" Then
Response.redirect("STO_Broke_Receipt.asp?s=" & strSAP)
elseif strCategory = "WADDING" then
Response.redirect("EnterSTOWaddingReceipt.asp?s=" & strSAP)
 

end if

end if

end if
%>
<SCRIPT LANGUAGE="JavaScript">
function formHandler(Form1){
document.forms["Form1"].submit();
}
</SCRIPT>

<style type="text/css">
.style1 {
	border: 1px solid #C0C0C0;
background-color: #EAF1FF;
}
.style3 {
	font-family: Arial, Helvetica, sans-serif;
	text-align: center;
}
.style5 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
}
.style6 {
	font-weight: bold;
}
</style>
</head>

<body>
<form name="form1" action="SelectSTO.asp" method="post" >

<p class="style3"><strong>ENTER STO RECEIPT</strong></p>

<table cellpadding="0" class = "style5" cellspacing="0" style="width: 80%;" bordercolor="#111111" id="AutoNumber2" align = center>

       <TD class="style1" style="height: 293px">  
		<div>
			<font face="Arial" size="3"><b>INTERNAL PURCHASE FROM KC LOCATION <br>
			<br>
			&nbsp;&nbsp;&nbsp; &nbsp;1)&nbsp;Select Delivery Number&nbsp;
     <font face="Arial">&nbsp;
     <select name="FIBER">
 	<option value="" selected>Delivery Number</option>
        				<% 	Do While Not rstFiber2.EOF
        				strLoad = rstFiber2("Load_nbr")
					%>
            			<% strsql = "Select STO_Number from tblCars where sto_Number = '" & strLoad & "'"
            			    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not Myrec.eof then
    'skip
    else

            			%>
						<option VALUE="<%= rstFiber2.fields("Load_nbr")%> ">
                				<%=rstFiber2.fields("Load_nbr")%>-<%=rstFiber2.fields("Ship_From_Name")%>-<%=rstFiber2.fields("Category")%>&nbsp;(<%= rstFiber2("BOL") %>)</option>

					<%   end if
					MyRec.close
        					rstFiber2.MoveNext 
        					Loop         				
        					rstFiber2.close
					%>

     </select>&nbsp;&nbsp; <font size="2"> <strong> 
			<input type="submit" value="Continue" id=submit name=submit class="style6"><br>
			<br>
			<br>
			<font face="Arial" size="3">&nbsp;&nbsp;&nbsp;&nbsp; 2)&nbsp;If 
			Choice not in Delivery Number Dropdown (#1), select SAP#&nbsp;
     <font face="Arial">&nbsp;
     <select name="SAP">
 	<option selected value="">SAP Number</option>
        				<% 	Do While Not rstFiber.EOF
					%>
            			
						<option VALUE="<%= rstFiber.fields("SAP")%> ">
                				<%=rstFiber.fields("SAP")%>-<%= rstFiber("Type") %></option>

					<% 
        					rstFiber.MoveNext 
        					Loop         				
        					rstFiber.close
					%>

     </select>&nbsp;&nbsp; <font size="2">  
			<input type="submit" value="Continue" id=submit0 name=submit0 class="style6"><br>
			</font></font></font></strong></font></font><br>
			<br>
			&nbsp;&nbsp;&nbsp;&nbsp; 3) If you can't find Delivery Number or SAP# in 
			dropdowns, enter load via the applicable link below:&nbsp;<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
			<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <a href="Sto_receipt.asp">STO 
			BROKE Receipt</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <a href="EnterSTOReceipt.asp?p=u">STO FIBER Receipt</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <a href="EnterSTOWADReceipt.asp">STO 
			WADDING Receipt</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>
&nbsp;<br>
			&nbsp;&nbsp;&nbsp;&nbsp; 4) If you don't know if it's Broke, Fiber, KDF or Wadding 
			for option #3, use this link to note the Truck has arrived:<br>
&nbsp;<br>
			&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="Generic_Sto_receipt.asp">Unknown STO Material with Scale Weight</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
			<a href="Generic_Sto_WAD_receipt.asp">	Unknown STO Material WITHOUT Scale Weight</a>

			</b></font><font face="Arial">
			<br>
		</div>

</TD></tr>

		
</table>
</form>






<%

 Function GetData()
    

    Set rstFiber2 = Server.CreateObject("ADODB.Recordset")

strsql3 = "Select Load_nbr, Ship_From_Name, Category, BOL FROM tblSTOInbound INNER JOIN tblBrokeSAP ON tblSTOInbound.SAP_Nbr = tblBrokeSAP.SAP "_
&" ORDER BY Load_nbr"
     
    rstFiber2.Open strSQL3, Session("ConnectionString"), adOpenDynamic
    
    
       Set rstFiber = Server.CreateObject("ADODB.Recordset")

strsql = "Select distinct SAP, Type from tblBrokeSAP order by SAP "
     
    rstFiber.Open strSQL, Session("ConnectionString"), adOpenDynamic
    


    End Function  %><!--#include file="Fiberfooter.inc"-->