																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Add Inbound Virgin Fiber </TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->


<% Dim MyRec, strsql, MyRec2, strsql2,  MyConn3, strsql3,  strSchedule_Agreement

strdate = formatdatetime(Now(),2)

  set objGeneral = new ASP_CLS_General
  if objGeneral.IsSubmit() Then	
  Dim strVendor
  

  		strVendor = Replace(Request.form("Vendor"), "'", "''")	
  
        	
  	
	strsql =  "INSERT INTO tblVFVendor (Vendor) "_
	&" SELECT  '" & strVendor & "'"
	
	
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
Response.redirect("VF_Vendor.asp")		
end if

	
%>

<style type="text/css">
.style1 {
	font-family: arial;
	font-size: small;
	text-align: center;
}
.style3 {
	border-style: solid;
	border-width: 1px;
}
.style4 {
	text-align: center;
}
</style>
</head>

<body>
<br>
	
<form name="form1" action="VF_Vendor_Add.asp" method="post" ID="Form1">
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Add Vendor</b></font></td>
<td align = right><font face="Arial"><a href="VF_Vendor.asp">Return</a>&nbsp;</td>


</tr>
	    </table>



	<p>&nbsp;</p>
	<table id="table1" style="width: 35%" align="center" class="style3">
<tr bgcolor = #CCCCFF>
		<td bgcolor="#FFFFD7" class="style1"><strong>Vendor</strong></td>
	</tr>
	<tr>
		<td class="style4"><font face = arial size = 1>
		<input type="text" name="Vendor" size="20" style="width: 300px"></td>
	</tr>
</table>
<br>
		

<p align="center"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></p>
</form>

<!--#include file="Fiberfooter.inc"-->