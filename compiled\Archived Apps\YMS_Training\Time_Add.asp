																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Add Time </TITLE>


<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBERDEV.asp"-->




<% Dim MyRec, strsql, MyRec2, strsql2,  MyConn3, strsql3,  strSchedule_Agreement


	x = Request.Servervariables("LOGON_USER")
	strEID = ""
	If InStr(x,"\") Then
  		y = SPLIT(x,"\")
  		strEID = y(1)
	Else
  		strEID = x
	End If
	     	

         Session("EmployeeID") = strEID


  set objGeneral = new ASP_CLS_General
  if objGeneral.IsSubmit() Then	

  strTask = Replace(request.form("Task"), "'", "''")
        	
  	
	strsql =  "INSERT INTO tblTime(Time_start, Project, Person, Date_added, Task) "_
	&" SELECT '" & request.form("Start") & "',   '" & request.form("Project") & "', '" & Session("EmployeeID") & "', '" & now() & "', '" &  strTask & "'"
		
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
Response.redirect("Projects_Time.asp")		
end if

	
%>

<style type="text/css">
.style3 {
	border: 1px solid #808080;
}
.style7 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: arial;
	font-size: x-small;
	background-color: #E6EEFF;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style8 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
</style>
</head>

<body>
<br>
	
<form name="form1" action="Time_Add.asp" method="post" ID="Form1">
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Add Time</b></font></td>
<td align = right><font face="Arial"><a href="Projects_time.asp">Return</a>&nbsp;</td>


</tr>
	    </table>



	<p>&nbsp;</p>
	<table id="table1" style="width: 75%" align="center" class="style3">
<tr bgcolor = #CCCCFF>
		<td bgcolor="#FFFFD7" class="style7" style="height: 39px">

<strong>Project</strong></td>
		<td bgcolor="#FFFFD7" class="style7" style="height: 39px">

<strong>Date/Time Start</strong></td>
	
		<td bgcolor="#FFFFD7" class="style7" style="height: 39px">

Task</td>
	
	</tr>
	<tr>
		<td style="height: 64px" class="style8"><font face = arial size = 1>
		<select name="Project">
		<option value="">--Select--</option>		 
		<option>University Split</option>
		<option>Huntsville</option>	 
		<option>CYDSC</option>	 
		<option>KCP Safety</option>	 
		<option>Loudon</option>
		<option>Mobile</option> 	
		<option>Neenah Cold Springs</option>  
		<option>Owensboro</option>
		<option>P2020</option>
		<option>Pilot Facilities</option>
		<option>Project Management</option>
		<option>Project Workflow</option>
		<option>Risk Assessment</option>
		<option>sMOC</option>
		<option>sMOC Translation</option>
		<option>Xmill</option>	
			</select></td>
		<td style="height: 64px" class="style8"><font face = arial size = 1>
		<input type="text" name="Start" size="20" style="width: 182px" value="<%= now() %>"></td>
		<td style="height: 64px" class="style8"><font face = arial size = 1>
		<input type="text" name="Task" size="20" style="width: 433px" value=""></td>
	</tr>
</table>
<br>
		

<p align="center"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></p>
</form>

