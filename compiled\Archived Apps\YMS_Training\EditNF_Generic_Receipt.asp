<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 14.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Edit Non-Recovered Paper Truck Receipt</title>
</head>
<%
    Dim strSQL, MyRec, strID, objMOC, rstFiber, strCarrier, strSAP, strReceipt, strRelease
      
    Dim strTrailer
    Dim strReleaseNbr
    Dim strRECNbr
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strPO
    Dim strVendor
    Dim strSAPNbr, strSpecies
    Dim strOther, strR, strsql3, strUOM

       strId  = Trim(Request.QueryString("id")) 


 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
 
		Call SaveData() 

	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to delete a Receipt.</font></br>")
	MyRec.close
	end if

  
ELSE


strsql = "SELECT tblCars.* from tblCars WHERE tblCars.CID = " & strid

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


    strTrailer = MyRec.fields("Trailer")
    strReleaseNbr = MyRec.fields("Release_nbr")
    strRECNbr = MyRec.fields("rec_number")
    strGrossWeight = MyRec.fields("Gross_weight")
     strTareWeight = MyRec.fields("Tare_weight")
     strTonsReceived = MyRec.fields("Tons_Received")
     strDateReceived = MyRec.fields("Date_received")
  	strUOM = MyRec.fields("UOM")

    strOther = MyRec.fields("Other_comments")
    strCarrier = MyRec.fields("Carrier")
    strRelease = Myrec.fields("Release_nbr")
    strPO = MyRec.fields("PO")
    strVendor = MyRec.fields("Vendor")
    strSAPNbr = MyRec.fields("SAP_Nbr")
    strSpecies = MyRec.fields("Species")
    strSAP = MyRec.fields("SAP_DOC_ID")
    If isnull(MyRec.fields("REC_Number")) then
    strReceipt = MyRec.fields("CID")
    else
    strReceipt = MyRec.fields("REC_Number")
    end if
    

MyRec.close
Call getdata()
	end if

%>



<body>

<% if Request.querystring("n") = "T" then %>
<p align = center><font face = arial size = 3 color = red><b>You must enter the Carrier 
and Trailer Number.  Please re-enter your information.</p></b></font>
<% else %>
&nbsp;
<% end if %>
<table width = 100%><tr><td width = "25%">
<font face="Arial" size="2">

</td><td align = center width = "43%"><b><font face="Arial" size="4">
Edit Non-Recovered Paper Trailer Receipt </font> </b></td>
<td align = center><font face="Arial" size="2"><b></td>
<td align = right width = 10%><a href="Edit_NF_Yard_trucks.asp"><b><font face = arial size = 2>RETURN</a></font></b></td></tr></table>




<form name="form1" action="EditNF_Generic_Receipt.asp?id=<%= strid%>" method="post">
<div align="center">
<table border="1" cellspacing="0" bordercolor="#808080" width="95%" bgcolor="#FFFFE1" style="border-collapse: collapse" cellpadding="0" bordercolorlight="#C0C0C0">
<tr>
    <td bgcolor="#FFFFDF" width="19%">&nbsp;</td>

    <td  bgcolor="#FFFFDF" colspan="3">&nbsp;</td>
  </tr>
    <tr>
    <td  align = left bgcolor="#FFFFDF" width="19%" >
   <b>
   <font face="Arial" size="2">PO Number:&nbsp;</font></b></td>
<td  align = left bgcolor="#FFFFDF" width="34%">

     <font face="Arial" size="2">

		<font face="Arial"> 
<input name="PO" size="15" style="font-weight: 700" value="<%= strPO%>"></font><%= strPONbr%><b>
		</b></font>

  </td>
<td  align = left bgcolor="#FFFFDF" width="46%" colspan="2">

     <font size="2" face="Arial"><b>Check to Print Receipt:&nbsp;&nbsp;
</b></font> <font face="Arial"><input type="checkbox" name="Print_receipt" value="ON" checked></td></tr>
  <tr>
    <td  align = left bgcolor="#FFFFDF" width="19%" >
   <font face="Arial" size="2"><b>Receipt Number:&nbsp;&nbsp;</b></font></td>
<td  align = left bgcolor="#FFFFDF" colspan="3">

      <font size="2" face="Arial"><%= strReceipt%> &nbsp; 
		
		</font></td></tr>
  <tr>

      <td  bgcolor="#FFFFDF" align = left width="19%">
  <font face="Arial" size="2"><b>Select Carrier:&nbsp;&nbsp;</b></font></td>
<td  align = left bgcolor="#FFFFDF">

      <font face="Arial">   
		<span style="background-color: #FFFFFF">   
	<select name="Carrier" style="border:1px solid #E3E0FE; font-weight: 700" size="1">
 	<option value="" selected>  Select Carrier (Required)</option>
      <%= objGeneral.OptionListAsString(rstFiber, "Carrier", "CDesc", strCarrier) %>
     </select></span></font></td>
<td  align = left bgcolor="#FFFFDF">

      <p align="right"><b><font face="Arial" size="2">BOL #</font></b></td>
<td  align = left bgcolor="#FFFFDF">

		<font face="Arial">  
<input name="Release" size="15" style="font-weight: 700" value="<%= strRelease%>"></td></tr>
<tr>
    <td  bgcolor="#FFFFDF" align="left" width="19%">  
	<p>  <b><font face="Arial" size="2">Trailer</font></b><font face="Arial" size="2"><b>:&nbsp;&nbsp;</b></font></td>
    <td bgcolor="#FFFFDF" colspan="3">   <font face="Arial">   

      <input name="Trailer" size="24" style="font-weight: 700" value="<%= strTrailer%>"> <font size="2">&nbsp;(REQUIRED 
	FIELD)</font></font></td>
  </tr>

       <tr>  <td  bgcolor="#FFFFDF" align = left width="19%"> <font face="Arial" size="2">
			<b>Quantity:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFDF" colspan="3">    <font face="Arial">  
<input name="Tons_Received" size="15" style="font-weight: 700" value="<%= strTonsReceived%>"> </td></tr>

<tr><td  bgcolor="#FFFFDF" align="left" width="19%"><font face="Arial" size="2"><b>Unit of 
	Measure:&nbsp;</b></font></td>
    <td  bgcolor="#FFFFDF" colspan="3">    <font face="Arial">  
	<input name="UOM" size="15" style="font-weight: 700" value="<%= strUOM%>"></td>
  </tr>
       <tr><td  align = left bgcolor="#FFFFDF" width="19%" >
	<font size="2" face="Arial"><b>Location:</b> </font>   
		</td>
<td align = left bgcolor="#FFFFDF" colspan="3">   <font face="Arial">   
	<span style="background-color: #FFFFFF">   
	<select name="Location" style="border:1px solid #E3E0FE; font-weight: 700" size="1">

      <option selected>YARD</option>
 
     </select></span></font></td></tr>
       <tr><td  align = left bgcolor="#FFFFDF" width="19%" ><font face="Arial" size="2"><b>Date Received:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFDF" colspan="3"> <font face="Arial"> 
<input name="Date_received" size="15" value = "<%= strDateReceived%>" style="font-weight: 700">&nbsp; <font size="2">&nbsp;(REQUIRED 
	FIELD)</font></font></td></tr>
              <tr>
          <td  align = left bgcolor="#FFFFDF" width="19%" >
   <font face="Arial" size="2"><b>Vendor:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFDF" colspan="3">
     <font face="Arial" size="2"> <font face="Arial">
		<input name="Vendor" size="24" style="font-weight: 700" value="<%= strVendor%>"></font></TD></tr>
   
              <tr>
          <td  align = left bgcolor="#FFFFDF" width="19%" >
   <font face="Arial" size="2"><b>Commodity:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFDF" colspan="3">   <font face="Arial">   
	<span style="background-color: #FFFFFF">   
	<select name="Species" style="border:1px solid #E3E0FE; font-weight: 700" size="1">

      <option selected>FIBER</option>
 
     	<option>OCC</option>
 
     </select></span></font></TD></tr>
   
              <tr>
          <td  align = left bgcolor="#FFFFDF" width="19%" >
   <font face="Arial" size="2"><b>SAP Material #:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFDF" width="34%">
   <font face="Arial" size="2">  <font face="Arial">
	<input name="SAP_Nbr" size="24" style="font-weight: 700" value="<%= strSAPNbr%>"></font></TD>
<td align = left bgcolor="#FFFFDF" width="21%">
   <p align="right">
   <font face="Arial" size="2"><b>SAP Doc ID #:&nbsp;</b></font></TD>
<td align = left bgcolor="#FFFFDF" width="25%">
   <font face="Arial" size="2"><b>    <font face="Arial">

      <input name="SAP_DOC_ID" size="16" value = "<%= strSAP %>" style="font-weight: 700"></font></TD></tr>
   
         <tr>
          <td  align = left bgcolor="#FFFFDF" width="19%" >
  <font face="Arial" size="2"><b>Comments/Other:&nbsp;</b></font></td >
   <td align = left bgcolor="#FFFFDF" colspan="3">   <font face="Arial">   
	<input name="Other_comments" size="60" style="font-weight: 700" value="<%= strOther%>"></font></td></tr>
<tr>
    <td  bgcolor="#FFFFDF" width="19%">&nbsp;</td>

    <td bgcolor="#FFFFDF" colspan="3">&nbsp;</td>
  </tr>

  <tr>
    <td bgcolor="#FFFFDF" width="19%">&nbsp;</td>

    <td align = left bgcolor="#FFFFDF" colspan="3"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></td>
  </tr>
</table>

</div>

</form>
</body>
<% Function GetData()
  set objMOC = new ASP_CLS_FIBER
        set rstFiber = objMOC.FiberCarrier()
End Function

Function SaveData()
	

	
	If len(Request.form("Vendor")) > 0 then
	gVendor = Replace(Request.form("Vendor"), "'", "''") 

	else
	gVendor = ""
	end if
	
		If len(Request.form("PO")) > 0 then

	strPO = Request.form("PO")	
	else
	strPO = ""
	end if
	
	If len(Request.form("UOM")) > 0 then
	strUOM = Request.form("UOM")	
	else
	strUOM = ""
	end if
	
		If len(Request.form("Vendor")) > 0 then
	strVendor = Request.form("Vendor")	
	else
	strVendor = ""
	end if
	
	If len(Request.form("Tons_Received")) > 0 then
	strTonsReceived = Request.form("Tons_Received")	
	else
	strTonsReceived = 0
	end if
	
	If len(Request.form("SAP_Nbr")) > 0 then
	 strSAPNbr = Request.form("SAP_Nbr")	
	else
	 strSAPNbr = ""
	end if
	
	If len(Request.form("Other_comments")) > 0 then
	strOther = Request.form("Other_comments")	
	else
	strOther = ""
	end if
	
	
	if len(Request.form("Date_received")) > 0 then
	strDateReceived = Request.form("Date_received")
	else
	strDateReceived = formatdatetime(Now(),2)
	end if

strCarrier = Request.form("Carrier")
strTrailer = Request.form("Trailer")

	If len(Request.form("SAP_DOC_ID")) > 0 then
	strSAP = Request.form("SAP_DOC_ID")	
	else
	strSAP = ""
	end if

	
	If len(Request.form("Other_comments")) > 0 then
	strOther = Replace(Request.form("Other_comments"), "'", "''") 
	else
	strOther = ""
	end if
	
	If len(Request.form("Release")) > 0 then
	strRelease = Request.form("Release")
	else
	strRelease = ""
	end if
	
	strSql = "Update tblCars Set Carrier = '" & strCarrier & "', Release_nbr = '" & strRelease & "', PO = '" & strPO & "', UOM = '" & strUOM & "', "_
	&" Trailer = '" & strTrailer & "', Tons_Received = " & strTonsReceived & ", "_
	&" Location = '" & Request.form("Location") & "', Vendor = '" & strVendor & "', "_
	&" Species = '" & Request.form("Species") & "',  SAP_Nbr = '" &  strSAPNbr & "', "_
	&" Date_Received = '" & strDateReceived & "', Other_comments = '" & strOther & "', SAP_DOC_ID = '" & strSap & "' where CID = " & strid & ""
	
	
	
	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
				If Request.form("Print_receipt") =  "ON" then
	
				Response.redirect("Truck_receipt.asp?id=" & strRelease &"&t=" & strTrailer & "&d=" & strDateReceived)
		else
Response.redirect("Edit_NF_Yard_trucks.asp")
end if
End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->