﻿<html>
<title>Mobile Confined Space Entry</title>
<!--#include file="classes/asp_cls_DataAccessBadge.asp"-->

<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_Procedure.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_ProcedureBadge.asp"-->
<!--#include file="classes/asp_cls_SessionStringBadge.asp"-->



<head>


<meta http-equiv="REFRESH" content="60">


<style>
<!--
 p.<PERSON>o<PERSON>ormal
	{mso-style-parent:"";
	margin-bottom:.0001pt;
	font-size:12.0pt;
	font-family:"Times New Roman";
	margin-left:0in; margin-right:0in; margin-top:0in}
-->
</style>
</head>
<% dim rstEPS, rstEPSTotal, rstEPS2
 %>

<body leftmargin="0" topmargin="5" marginwidth="0" marginheight="0">

<table width="100%"  border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td  width="160" valign="top" bgcolor="#F4F4F4">
	<table width="94%"  border="0" cellspacing="0" cellpadding="2" align="center">
      <tr>
        <td width="160" valign="top">
		<img src="images/space.gif" width="160" height="1">
		
		<font size = 2>
	
<font face = arial><b><br>  
		</b></font><font face = arial size="2">&nbsp;</td>
      </tr>
    </table></td>


    <td width="695" valign="top">
	<img border="0" src="images/bg_blueProducts.gif" width="694" height="32"><img src="images/FEHeader.gif" width="388" height="32"><br>
      <table width="695" border="0" cellspacing="0" cellpadding="1">
        <tr>
          <td width="50%" valign="top">
		  	<table width="98%"  border="0" cellspacing="0" cellpadding="1" align="center">
            <tr>
              <td bgcolor="#FFFFFF"><font size="2"><b><font color = black face="Arial, Helvetica, sans-serif"><br>
               </font></b></font>
		     
 <% Dim strsql, MyRec, strdate, strdate3, strPC
 strnow = dateadd("h", -5, now())
strdate = formatdatetime(strnow,1)
strdate2 = formatdatetime(strnow,2)
strdate3 = formatdatetime(strnow,2)
strdate2 = dateadd("d", -1, strdate2)

strdate3 = dateadd("d", 1, strdate3)

%>


		<font size = 2>
	
<font face = arial><b>Welcome to the Kimberly-Clark Mobile <br> </b></font></font>
				<font color="#0072A8" size="2" face="Arial, Helvetica, sans-serif"> 
				Confined Space Entry&nbsp; <br>
                  <br>
                  <br><font color = black><b>Confined Space Entries Scheduled for <%= strnow %></b>
            <table border = 1 cellspacing = 0 cellpadding = 0 width = 95% bordercolor="#F4F4F4"><tr>
             <td align="center" ><font face="Arial" size="1">Space ID</font></td>
            <td align="center" ><font face="Arial" size="1">Space Name</font></td>
			<td align="center" > 
			<font face="Arial" size="1">Date of <br>Issue</font></td>
			<td align="center" >
			<font face="Arial" size="1">Date<br> Expires</font></td>
				<td align="center" >
			<font face="Arial" size="1">Entry<br>Status</font></td>
			<% if strPC < 4 then %>
	<td align="center" ><font face="Arial" size="1">ERT<br>Approval</font></td>
<% end if %>
		</tr>
            
<%  Dim strCount, strSname
 
strCount = 0 

strsql = "SELECT tblSOP.SOP_NO, tblSOP.SDescription, tblPermit.Date_issued, tblPermit.entry_status, tblPermit.entry_approval, tblPermit.Date_expired, tblSOP.WorkArea "_
		&" FROM tblPermit INNER JOIN tblSOP ON tblPermit.Space_ID = tblSOP.SOP_NO "_
		&" WHERE Date_issued < '" & strdate3 & "' and Date_expired > '" & strdate2 & "' and (Permit_status is null or Permit_status = 'Review Complete')"_
		&" or tblPermit.Entry_status = 'Active' order by sop_no, tblPermit.date_issued, tblpermit.time_issued"
       Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly 
			While not MyRec.eof 
			if strcount = 0 or (strcount > 0 and strSname <> MyRec.fields("SOP_NO")) then
			
			%>
           <tr>
            
            <td align="center" ><font face="Arial" size="1"><%= MyRec.fields("SOP_NO") %></font></td>
			<td align="left" > 	<font face="Arial" size="1"><%= MyRec.fields("Sdescription") %></td>
			<td align="center"><font face="Arial" size="1">&nbsp;<%= MyRec.fields("Date_issued") %>&nbsp;</td>
			<td align="center" ><font face="Arial" size="1">&nbsp;<%= MyRec.fields("Date_expired") %>&nbsp;</td>
			<% if len(MyRec.fields("Entry_Status")) > 1 then %>
			<td align="center" ><font face="Arial" size="1"><%= MyRec.fields("Entry_Status") %></td>
			<% else %>
				<td align="center" ><font face="Arial" size="1">Dormant</td>
				<% end if %>
				<% if strPC < 4 then %>
					<% if MyRec.fields("Entry_approval") = "Yes" then %>
					<td align="center" ><font face="Arial" size="1">Yes</td>
					<% else %>
						<td align="center" ><font face="Arial" size="1">No</td>
					<% end if %>
				<% end if %>

	</tr>
 <%  else
 'skip printing
 end if 
 
 strcount = strcount + 1
		 strSname = MyRec.fields("SOP_NO")
       MyRec.MoveNext
     Wend
     MyRec.close %>
            </table>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>




</font></font></td>
            </tr>
          </table></td>
          <td width="50%" valign="top" bgcolor="#D6D6F5"><font size="2" face="Arial, Helvetica, sans-serif">&nbsp;<strong>&nbsp; 
	
  
	

 			PUT ERT HERE<br>
 			<% Dim objBadgeSearch
 		      set objBadgeSearch = new ASP_CLS_ProcedureBadge
 				set rstBadgeSearch = objBadgeSearch.BadgeSearchEMS(B54758)
 				While not rstBadgeSearch.eof %>
 			<%= rstBadgeSearch.fields("BID") %> - <%= rstBadgeSearch.fields("timein") %><br>
 				<% rstBadgeSearch.movenext
 				wend
 			 %>
 			
 			</td>


        </tr>
     
      </table></td>

  
</td>
      <td  bgcolor="#C5C5C5">&nbsp;</td>


</tr></table>

</body>

<!--#include file="footer.inc"-->
                                       