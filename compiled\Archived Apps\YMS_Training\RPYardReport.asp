																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Recovered Paper Yard Inventory Report</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->



<% Dim MyRec, strsql, strDays, strDate, gSpecies, gCount, gTCount, strsql3, MyRec3

strdate = formatdatetime(now(),2)
gcount = 0
gSpecies = ""

    strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "' and User_type = 'A'"


    Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL3, Session("ConnectionString")



If not Myrec2.eof then
strAdmin = "YES"
else
strAdmin = ""
end if


MyRec2.close 

IF Session("EmployeeID") = "D10480" then
strAdmin = "YES"
end if

strAdmin = "YES"

	 if strAdmin = "" then %>
	<!--#include file="classes/asp_cls_headerFIBER.asp"-->

	<p align="center"><font face="arial" color="red" size="3"><b>The  Yard Inventory Report is Not Available for your Employee ID (<%= Session("EMployeeID") %>).  <br><br>
	Please use the <a href="Spotting_report.asp">Shift Spotting Report</a><br>
	</b></font></p>
	<% else 


strsql = "SELECT tblCars.*,  [date_received] AS Sort_Date FROM tblCars WHERE Location = 'YARD' "_
&"    ORDER BY tblCars.Species, Sort_Date "   
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=0>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><b>
<font face="arial" size="4" >Yard Inventory Report for <%= strDate%></font></b></span></td>
<td align = center>&nbsp;</td>


</tr>
	    </table>
<p align="right"><font face="Arial">Yard Physically Checked by 
_______________________<br>Signature&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </font>&nbsp; </p>

	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">

	<td  ><font face="Arial" size="1"><b>Species</b></font></td>
		<td align = center  ><font face="Arial" size="1"><b>Date <br>Received</b></b></font></td>
		<td align = center  ><font face="Arial" size="1"><b>Door</b></b></font></td>
    	<td ><font face="Arial" size="1"><b>&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; Trailer</b></font></td>
		<td ><font face="Arial" size="1"><b>Carrier</b></font></td>
	<td ><font face="Arial" size="1"><b>REC Nbr</b></font></td>
	<td ><font face="Arial" size="1"><b>PO</b></font></td>
<td width="69" >
<p align="center"><font face="Arial" size="1"><b>Vendor</b></font></td>
<td align = left>
<p align="center">
<font face="Arial" size="1"><b>Audit</b></font></td>

<td align=center><font face="Arial" size="1"><b>To Date<br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="1"><b>Days <br>in Yard</b></font></td>

<td align="center" ><font face="Arial" size="1"><b>Other</b></font></td>
<td align = center ><font face="Arial" size="1"><b>Checkoff</b></font></td>
	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
      ' if MyRec("Commodity") = "Y" then 
       'skip
      ' else
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    
    <% if  UCASE(MyRec.fields("Species")) = UCASE(gSpecies) or gcount = 0 then %>


        
        <% if isnull(MyRec.fields("Transfer_date")) then %>
        <td>       <font size="2" face="Arial">       <%= MyRec.fields("Species")%>
        <% if MyRec.fields("Rejected") = "YES" Then %>
        - Rejected
        <% end if %></font></td>
	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Door")%></font></td>
		<td  > <font size="2" face="Arial"> &nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;<%= MyRec.fields("Trailer")%></font></td>
	<td  >   <font size="2" face="Arial">        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
	<% if isnull(MyRec.fields("Rec_number")) then %>
		 <td  >  <font size="2" face="Arial">        <%= MyRec.fields("CID")%>&nbsp;</font></td>
		 <% else %>
	 <td  >  <font size="2" face="Arial">        <%= MyRec.fields("REC_number")%>&nbsp;</font></td>
	 <% end if %>
        <% else %>
        	<td>       <font size="2" face="Arial">       <%= MyRec.fields("Species")%>&nbsp;&nbsp;:SHUTTLE&nbsp; </font></td>
		<td align = center width="69" ><font size="2" face="Arial"><%= MyRec.fields("Transfer_Date")%>&nbsp;<%=  MyREc("CID") %></font></td>
		<td align = center width="69" ><font size="2" face="Arial">&nbsp;</font></td>
			<td  > <font size="2" face="Arial">&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; <%= MyRec.fields("Transfer_trailer_nbr")%></font></td>
		<td  >   <font size="2" face="Arial">        <%= MyRec.fields("Trans_Carrier")%>&nbsp;</font></td>
		   <td  >  <font size="2" face="Arial">        <%= MyRec.fields("PMO_nbr")%>&nbsp;</font></td>
		<% end if %>
		

		
 
   <td  >  <font size="2" face="Arial">        <%= MyRec.fields("PO")%>&nbsp;</font></td>
    <td  >  <font size="2" face="Arial">        <%= MyRec.fields("Vendor")%>&nbsp;</font></td>
      <td  >  <font size="2" face="Arial"> 
      <% if MyRec.fields("Weigh_required") = "W" and MyRec.fields("Audit_tons") > 0  Then %>
      WC
       <% elseif (MyRec.fields("Weigh_required") = "W" and isnull(MyRec.fields("Audit_tons"))) or ( MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then %>W
        <% else %> &nbsp; <% end if %></td>
          
         <% if isnull(MyRec.fields("Transfer_date")) then
             strDays = round(Now()- cdate(MyRec.fields("Date_received")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

             else
             strDays = round(Now()- cdate(MyRec.fields("Transfer_date")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Trans_Carrier") & "' "
             end if  
          
 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    
       If left(MyRec("Trailer"),4) = "GACX" then
    strFee = 0
    end if
    
    if strDays  > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(strDays) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"><%= strDays%></font></td>	
	
	<td><font size="2" face="Arial"><%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>
	<td ALIGN = CENTER><font size="2" face="Arial"><input type="checkbox" name="C1" value="ON">&nbsp;</font></td>
	</tr>

 <%    gcount = gcount + 1
       gTcount = gTcount + 1
 		gSpecies = UCASE(MyRec.fields("Species"))
       ii = ii + 1
       MyRec.MoveNext
    
    %>
<% else %>
<td colspan = 14><font face = arial size = 2><b></b>Total:&nbsp;<%= gcount%></b></font></td>


	</tr>
	<TR><td colspan = 14>&nbsp;</td>


	</tr>
	
	  <tr class=tablecolor2>
        
        <% if isnull(MyRec.fields("Transfer_date")) then %>
        <td>        <font size="2" face="Arial">         <%= MyRec.fields("Species")%>
        <% if MyRec.fields("Rejected") = "YES" Then %>
        - Rejected
        <% end if %></font></td>
        		<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
        			<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Door")%></font></td>
        		<td  > <font size="2" face="Arial">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <%= MyRec.fields("Trailer")%></font></td>
        		<td  >    <font size="2" face="Arial">        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
        		<% if isnull(MyRec.fields("Rec_number")) then %>
        		        		  <td> <font size="2" face="Arial">        <%= MyRec.fields("CID")%></font></td>
        		        		  <% else %>
        		  <td> <font size="2" face="Arial">        <%= MyRec.fields("REC_number")%></font></td>
        		  <% end if %>
        <% else %>
        	<td>       <font size="2" face="Arial">       <%= MyRec.fields("Species")%>&nbsp; &nbsp;:SHUTTLE</font></td>
		<td   ><font size="2" face="Arial"><%= MyRec.fields("Transfer_Date")%>&nbsp;</font></td>
			<td   ><font size="2" face="Arial">&nbsp;</font></td>
			<td  > <font size="2" face="Arial">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <%= MyRec.fields("Transfer_Trailer_nbr")%></font></td>
				<td  >  <font size="2" face="Arial">   <%= MyRec.fields("Trans_Carrier")%>&nbsp;</font></td>
				<td> <font size="2" face="Arial"><%= MyRec.fields("PMO_nbr")%>&nbsp;</font></td>
		


		<% end if %>
		
<td>

     
        <font size="2" face="Arial">
        <%= MyRec.fields("PO")%></font></td>
        <td  >
        <font size="2" face="Arial">
        <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> 
        
              <% if MyRec.fields("Weigh_required") = "W" and MyRec.fields("Audit_tons") > 0  Then %>
      WC
       <% elseif (MyRec.fields("Weigh_required") = "W" and isnull(MyRec.fields("Audit_tons"))) or ( MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then %>W
        <% else %> &nbsp; <% end if %></td>

         <% if isnull(MyRec.fields("Transfer_date")) then
             strDays = round(Now()- cdate(MyRec.fields("Date_received")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

             else
             strDays = round(Now()- cdate(MyRec.fields("Transfer_date")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Trans_Carrier") & "' "
             end if  
          
 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    
       If left(MyRec("Trailer"),4) = "GACX" then
    strFee = 0
    end if
    
    if strDays  > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(strDays) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"><%= strDays%></font></td>

	

	<td><font size="2" face="Arial"><%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>
	<td ALIGN = CENTER><font size="2" face="Arial"><input type="checkbox" name="C1" value="ON">&nbsp;</font></td>
	</tr>
		
	<% gcount = 1
	 gTcount = gTcount + 1
	 gSpecies = UCASE(MyRec.fields("Species"))
	 
       ii = ii + 1
       'end if ' Sent to WH
       MyRec.MoveNext
    
	 end if %>

<%  Wend %>
	  <tr class=tablecolor2>
<td colspan = 14><font face = arial size = 2><b></b>Total:&nbsp;<%= gcount%></b></font></td>

	</tr>
	<TR><td colspan = 14>&nbsp;</td>



	</tr>
<TR><td colspan = 14><font face = arial size = 2><b></b>Grand Total:&nbsp;<%= gTcount%></b></font></td>



	</tr>

</table>
<% end if %>