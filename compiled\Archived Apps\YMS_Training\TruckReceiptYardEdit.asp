<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Edit Truck Receipt</title>
<style type="text/css">
.style1 {
	border: 1px solid #E4E4E4;
	border-collapse: collapse;
		background-color: #FFFFCC;
}
.style2 {
	border: 1px solid #DFDFF7;
	font-weight: bold;
}
.style11 {
	border: 1px solid #DFDFF7;
	font-weight: bold;
	text-align: right;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID, objMOC, rstFiber, strCarrier
      
    Dim strTrailer
    Dim strReleaseNbr
    Dim strRECNbr
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strGenerator
    Dim strGenCity
    Dim strGenState
    Dim strOther, strR, strsql3

       strId  = Trim(Request.QueryString("id")) 


 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
 
		Call SaveData() 

	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to delete a Receipt.</font></br>")
	MyRec.close
	end if

  
ELSE


strsql = "SELECT tblCars.* from tblCars WHERE tblCars.CID = " & strid

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


    strTrailer = MyRec.fields("Trailer")
    strReleaseNbr = MyRec.fields("Release_nbr")
    strRECNbr = MyRec.fields("rec_number")
     strTonsReceived = MyRec.fields("Tons_Received")
    strDateReceived = MyRec.fields("Date_received")
    strGenerator = MyRec.fields("Generator")
    strGenCity = MyRec.fields("Gen_City")
     strGenState = MyRec.fields("Gen_state")
    strOther = MyRec.fields("OTher_comments")
    strCarrier = MyRec.fields("Carrier")
    strR = left(strReleaseNbr,1)
    strBales = MyRec.fields("Bales_VF")
    strGrade = MyRec.fields("Grade")
    
    strSpecies = MyRec.fields("Species")
    strPounds =  strTonsReceived * 2000

MyRec.close
Call getdata()
	end if

%>



<body>
<table width = 100%><tr><td width = 33%></td><td align = center width = 34%><b><font face="Arial" size="4">
Edit Trailer Receipt</font> </b></td><td align = right width = 33%><a href="javascript:history.go(-1);"><b><font face = arial size = 2>RETURN</a></font></b></td></tr></table>



<form name="form1" action="TruckReceiptYardEdit.asp?id=<%=strid%>&r=<%= strR%>" method="post">
 
<div align="center">
<table border="1" cellspacing="0" bordercolor="#C0C0C0" width="60%" bgcolor="#EAF1FF" style="border-collapse: collapse" cellpadding="0">
<tr>
    <td bgcolor="#EAF1FF">&nbsp;</td>

    <td  bgcolor="#EAF1FF">&nbsp;</td>
  </tr>
  <tr>
    <td  align = right bgcolor="#EAF1FF" >
   <b>
   <font face="Arial" size="2">Trailer:&nbsp;</font></b></td>
<td  align = left bgcolor="#EAF1FF">

      <input type="text" name="Trailer" size="15" value = "<%= strTrailer%>" tabindex="1"></td></tr>
  <tr>

      <td  bgcolor="#EAF1FF" align = right>
  <font face="Arial" size="2"><b>Receipt Number:&nbsp;</b></font></td>
<td  align = left bgcolor="#EAF1FF">

      <input type="text" name="REC_Number" size="15" value = "<%= strRECNbr%>" tabindex="2"></td></tr>
<tr>
    <td  bgcolor="#EAF1FF">  
	<p align="right">  <font face="Arial" size="2"><b>Select Carrier:&nbsp;</b></font></td>
    <td bgcolor="#EAF1FF">   <select name="Carrier" tabindex="3">
 	<option value="" selected>  Select Carrier (Required)</option>
      <%= objGeneral.OptionListAsString(rstFiber, "Carrier", "CDesc", strCarrier) %>
     </select></td>
  </tr><tr><td  align="right" bgcolor="#EAF1FF">  <font face="Arial" size="2"><b>Species:&nbsp;</b></font&nbsp;</td>

    <td bgcolor="#EAF1FF">
     <select size="1" name="Species" tabindex="4">
     <option value="">---Select ---</option>
     <% strsql4 = "SELECT fiber_species FROM tblVFSpecies order by Fiber_species"
     
   	 Set MyRec4 = Server.CreateObject("ADODB.Recordset")
   	 MyRec4.Open strSQL4, Session("ConnectionString")
   	 while not MyRec4.eof  %>
   	 <option <% if strSpecies = MyRec4("Fiber_Species") then %> selected <% end if %>><%= MYRec4("Fiber_Species") %></option>
<% MyRec4.movenext
wend
MyRec4.close %>
     
 
 
  
     </select> &nbsp;</td>
  </tr>
   
<tr>
    <td  bgcolor="#EAF1FF">&nbsp;</td>

    <td  bgcolor="#EAF1FF">&nbsp;</td>
  </tr>
 


 
        <tr>
          <td  bgcolor="#EAF1FF" align = right>
   <font face="Arial" size="2"><b>Pounds Received:&nbsp;</b></font></td>
<td align = left bgcolor="#EAF1FF">
<font face="Arial" size="2">
      <input type="text" name="Pounds" size="15" value = "<%= strPounds %>" style="width: 88px" tabindex="7"></td></tr>

 
<tr>
    <td  bgcolor="#EAF1FF" class="style11"><font face="Arial" size="2">VF Bales:</font></td>

    <td  bgcolor="#EAF1FF">
<font face="Arial" size="2">
      <input type="text" name="Bales" size="15" value = "<%= strBales%>" style="width: 88px" tabindex="7"></td>
  </tr>
  
  <tr>
    <td  bgcolor="#EAF1FF" class="style2"><font face="Arial" size="2">&nbsp;</font></td>

    <td  bgcolor="#EAF1FF">
<font face="Arial" size="2">
      &nbsp;</td>
  </tr>

       <tr>
          <td  align = right bgcolor="#EAF1FF" >
    <font face="Arial" size="2"><b>Date Received:&nbsp;</b></font></td>
<td align = left bgcolor="#EAF1FF">

      <input type="text" name="Date_Received" size="15" value = "<%= strDateReceived%>" tabindex="8"></td></tr>
              <tr>
          <td  align = right bgcolor="#EAF1FF" >
   <font face="Arial" size="2"><b>Generator:&nbsp;</b></font></td>
<td align = left bgcolor="#EAF1FF">
      <input type="text" name="Generator" size="25" value = "<%= strGenerator %>" tabindex="9"></TD></tr>
        <tr>
          <td  align = right bgcolor="#EAF1FF" >
    <font face="Arial" size="2"><b>Generator City:&nbsp;</b></font></td>
<td align = left bgcolor="#EAF1FF">
      <input type="text" name="Gen_City" size="15" value ="<%= strGenCity%>" tabindex="10"></td></tr>

           <tr>
          <td align = right bgcolor="#EAF1FF" >
   <font face="Arial" size="2"><b>Generator State:&nbsp;</b></font></td>
<td align = left bgcolor="#EAF1FF">
      <input type="text" name="Gen_State" size="15" value = "<%= strGenState%>" tabindex="11"></td></tr>
         <tr>
          <td  align = right bgcolor="#EAF1FF" >
  <font face="Arial" size="2"><b>Other:&nbsp;</b></font></td >
   <td align = left bgcolor="#EAF1FF">   
	<input type="text" name="Other_Comments" size="25" value = "<%= strOther%>" tabindex="12" style="width: 592px"></td></tr>
<tr>
    <td  bgcolor="#EAF1FF">&nbsp;</td>

    <td bgcolor="#EAF1FF">&nbsp;</td>
  </tr>

  <tr>
    <td bgcolor="#EAF1FF" height="34">&nbsp;</td>

    <td align = left bgcolor="#EAF1FF" height="34"><Input name="Update" type="submit" Value="Submit" ></td>
  </tr>
</table>

</div>

</form>
</body>
<%
 
 Function GetData()
  set objMOC = new ASP_CLS_FIBER
  set rstFiber = objMOC.FiberCarrier()
    set rstSpecies = objMOC.VFSpecies()

End Function


Function SaveData()
strid = request.querystring("id")
strTrailer = Request.form("Trailer")
strCarrier = Replace(Request.form("Carrier"), "'", "''")
strOther = Replace(Request.form("Other_Comments"), "'", "''") 
strDateReceived = Request.form("Date_received")
strRECNbr = Request.form("Rec_Number")
strSpecies = Request.form("Species")
strGenerator = Replace(Request.form("Generator"), "'", "''")
strGenCity = Replace(Request.form("Gen_City"), "'", "''")
strGenState = Request.form("Gen_State")
If len(Request.form("Pounds")) > 0 then
strTonsReceived = round(Request.form("Pounds")/2000,3)
else
strTonsReceived = 0
end if





         strSql = "Update tblCars Set Carrier = '" & strCarrier & "', Other_Comments = '" & strOther & "', Trailer = '" & strTrailer & "', "_
         &" Date_Received = '" & strDateReceived & "', Species = '" & strSpecies & "', "_
         &" Rec_Number = '" & strRECNbr & "', "_
    
         &" Tons_Received = " & strTonsReceived & ", Net = " & strTonsReceived & ", "_
         &" Generator = '" & strGenerator & "', "_
         &" Gen_City = '" & strGenCity & "', "_
         &" Gen_State = '" & strGenState & "' where CID = " & strid & ""
         
 	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
         
         If len(Request.form("Bales")) > 0 then
         strBales = Request.form("Bales")
            strSql = "Update tblCars Set Bales_VF = " & strBales & " where CID = " & strid & ""
 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 

end if

Response.redirect ("Edit_Yard_Trucks.asp")



End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->