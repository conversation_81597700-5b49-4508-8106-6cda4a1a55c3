
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Add Work Area</TITLE>

<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
 
<style type="text/css">
.style1 {
	border: 1px solid #C0C0C0;
	font-family: arial;
		font-size: x-small;
	text-align: center;
}
.style3 {
	border: 1px solid #000000;
	background-color: #FFFFDD;
}
.style4 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style5 {
	text-align: center;
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style8 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
.style9 {
	border: 1px solid #C0C0C0;
}
.style10 {
	border: 1px solid #C0C0C0;
	font-family: arial;
	font-size: x-small;
}
.auto-style1 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #EDF7FE;
}
.auto-style2 {
	text-align: center;
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #EDF7FE;
}
.auto-style3 {
	border: 1px solid #C0C0C0;
	background-color: #FFFFDD;
}
.auto-style4 {
	border: 1px solid #C0C0C0;
	font-family: arial;
	font-size: x-small;
	background-color: #EDF7FE;
}
</style>
</head>
<% dim strsql, MyRec, strid, strecp, strMill, objGeneral, strarea

strMill = request.querystring("m")
set objGeneral = new ASP_CLS_General
  		 if objGeneral.IsSubmit = True Then
   
  		     strWA =  Replace(Request.form("WA"), "'", "''")
  		     strTeamID = request.form("Team")
  		     strAbb = request.form("Abb")

			 
 strsql =  "INSERT INTO WorkAreas (VA_Team_ID, WorkArea, WA_Abb) SELECT  " & strTeamID & ", '" & strWA & "', '" & strAbb & "'"
 Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 


Response.redirect("WA_Setup.asp")
end if 

 %><body bgcolor="#FFFFFF"><br>
 <form name="form1" action="WA_Add.asp"  method="post" ID="Form1"  >
  
  <table width = 100% class="auto-style3"><tr>
	<td align = left class="auto-style1">
<font face = arial size = 3><strong>Add New Work Area</strong></font></td>
	<td class="auto-style2">
<a href="javascript:history.go(-1)"><font face = arial size = 2 color = black>
<strong>Return</strong></font></a></td>
<td align = right class="auto-style1"><INPUT TYPE="submit" value="Submit"></td>
</tr></table><br><br>

 
 <table cellpadding="0" bordercolor="#111111" bgcolor="#D9E1F9" align="center" class="style8" style="width: 45%">
   
  <tr>

   
    <td align="center" style="height: 54" class="auto-style4">
	<strong>Work Area</strong></td>

   
    <td align="center" style="height: 54" class="auto-style4">
	<strong>WA Abb</strong></td>

   
    <td align="center" style="height: 54" class="auto-style4">
	<strong>Team</strong></td>

   
  </tr>
  <tr>
 
    <td  bgcolor="#FFFFFF" class="style1" style="height: 94px"  >
	<font face="Arial" size="2">
	<input type="text" name="WA" size="55" style="width: 156px" tabindex="1" ></font></td>
	
    <td  bgcolor="#FFFFFF" class="style1" style="height: 94px"  >
	<font face="Arial" size="2">
	<input type="text" name="Abb" size="55" style="width: 75px" tabindex="1" ></font></td>
	
    <td  bgcolor="#FFFFFF" class="style1" style="height: 94px"  >
	<select name="Team">
	<option value="">--Select--</option>
	<% 
			strSQL = "SELECT VA_Team_Name, Va_Team_ID from VA_Team_names   order by VA_Team_Name"
		        Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly

	While Not MyRec.eof %>
	<option value='<%= MyRec("VA_TEam_ID") %>'><%= MyRec("VA_Team_name") %></option>
	
	<% MyRec.movenext
	wend
	MyRec.close %>
	</select></td>
	
  </table> 
	<p>&nbsp;</p>
	<p>&nbsp;</p>
	</form>
 
 
<!--#include file="footer.inc"-->