																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Edit Vendor </TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->


<% Dim MyRec, strsql, MyRec2, strsql2,  MyConn3, strsql3,  strFee

 

  set objGeneral = new ASP_CLS_General
  strid = Request.querystring("id")
  
  strsql = "Select * from tblVendors where ID = " & strid
  
      Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

  

  if objGeneral.IsSubmit() Then	
 
 
  	
  If len(request.form("Email_add")) > 1 then	
 strEmail_add = Request.form("Email_add")
 else
 strEmail_add = ""
 end if 
 
        	
  	
	strsql =  "Update tblVendors  set Email_address = '" & strEmail_add & "' where ID = " & strid
	
	
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
Response.redirect("Vendors.asp")		
end if

	
%>

<style type="text/css">
.style3 {
	border-style: solid;
	border-width: 1px;
}
.style6 {
	font-family: arial;
	font-size: x-small;
	background-color: #FFFFD7;
}
</style>
</head>

<body>
<br>
	
<form name="form1" action="Vendor_Edit.asp?id=<%= strid%>" method="post" ID="Form1">
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Edit Vendor Email Address List </b></font></td>
<td align = right><font face="Arial"><a href="Vendors.asp">Return</a>&nbsp;</td>


</tr>
	    </table>



	<p>&nbsp;</p>
	<table id="table1" style="width: 85%" align="center" class="style3">
<tr bgcolor = #CCCCFF>
		<td bgcolor="#FFFFD7" class="style6">

Vendor</td>
		<td bgcolor="#FFFFD7" class="style6">

Email List&nbsp;&nbsp; Separate addresses with semi-colon</td>
	</tr>
	<tr>
		<td><font face = arial size = 2>
		<%= MyRec.fields("Company_name") %></td>
		<td><font face = arial size = 1>
		<input type="text" name="Email_add" size="20" value='<%= MyRec.fields("Email_address") %>' style="width: 837px"></td>
	</tr>
</table>
<br>
		

<p align="center"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></p>
</form>
<% MyRec.close %><!--#include file="Fiberfooter.inc"-->