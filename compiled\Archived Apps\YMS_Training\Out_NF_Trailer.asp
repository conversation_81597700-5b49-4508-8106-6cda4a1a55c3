<script>window.history.go(1);</script>
<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Car out Non-Fiber Truck Loads</title>
</head>
<%
    Dim strSQL, MyRec, strID, objMOC, rstFiber, strCarrier, strLocation
      
    Dim strTrailer, strSAP
    Dim strReleaseNbr
    Dim strRECNbr
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived, strSpecies, strDateSite
    Dim strGenerator
    Dim strGenCity
    Dim strGenState
    Dim strOther, strR, strsql3, strTransTrailer, strCurrentStatus, strFromDoor, strNFID

       strId  = Trim(Request.QueryString("id")) 


 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
 		
		Call SaveData() 
		

	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to change the location of a Trailer.</font></br>")
	MyRec.close
	end if

  
ELSE


strsql = "SELECT tblCars.* from tblCars WHERE tblCars.CID = " & strid

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

	strCurrentStatus= MyRec.fields("Location")
    strTrailer = MyRec.fields("Trailer")
    strCarrier = MyRec.fields("Carrier")
    If strTrailer = "UNKNOWN" then 
    strTrailer = MyRec("Transfer_Trailer_NBR")
    strCarrier = MyREc("Trans_Carrier")
    end if
    strSpecies = MyRec.fields("Species")
    
        strDateSite = MyRec.fields("Date_received")

    strSAP = MyRec.fields("SAP_Nbr")
    strDateReceived = formatdatetime(Now(),0)
    If isnull(MyRec.fields("Door")) then 
    strFromdoor = "None"
    else
    strFromdoor = MyRec.fields("Door")
    end if

MyRec.close
Call getdata()
	end if

%>



<body><br>
<table width = 100%><tr><td width = 33%></td><td align = center width = 34%>
	<b><font face="Arial">Move Non-Recovered Paper Trailer </font></b></td><td align = right>
	<font face="Arial"><b><a href="SelectTruckUnloadNF.asp">RETURN</a></b></font></td></tr></table>



<form name="form1" action="Out_NF_Trailer.asp?id=<%=strid%>&s=<%= strCurrentStatus%>&d=<%= strFromDoor%>" method="post">
<input type="hidden" name="Species" value="<%= strSpecies%>">
<input type="hidden" name="Date_site" value="<%= strDateSite%>">
<input type="hidden" name="Carrier" value="<%= strCarrier %>">
<input type="hidden" name="Trailer" value="<%= strTrailer %>">

<div align="center">
<table border="1" cellspacing="0" width="60%" bgcolor="#FOFOFF" style="border-collapse: collapse" cellpadding="0" align = center>

  <tr>
      <td  align = center bgcolor="#F0F0FF"  > 
   <b>
   <font face="Arial" size="2">Trailer:&nbsp;</font></b>&nbsp;&nbsp;

   <font face = arial><b> <%= strTrailer%>&nbsp;&nbsp;&nbsp;

 
 <font face="Arial" size="2">Carrier:</font>
    <%= strCarrier %>&nbsp;&nbsp;&nbsp; <font face="Arial" size="2">Material:</b></font><font face = arial size=2>
     <%= strSpecies %> </font>&nbsp;&nbsp;&nbsp; <font face="Arial" size="2"><b>SAP#</b>:</b><%= strSAP%>
   </td></tr></table>
   </div>
<div align="center">
      <table border="2"  width="60%" bgcolor="#F0F0FF" style="border-collapse: collapse" cellpadding="2">

    <tr>

      <td  bgcolor="#FOFOFF" align = right bordercolor="#F0F0FF">
 <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF">

      &nbsp;</td></tr>
      
       <tr> <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF">&nbsp;</td>
 <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF">&nbsp;</td></tr>
   <tr>
   <td  bgcolor="#FOFOFF" align = right bordercolor="#F0F0FF">
  <b><font face="Arial" size="2">Current Status</font></b><font face="Arial" size="2"><b>:&nbsp;</b></font></td>

      <td  bgcolor="#FOFOFF" align = LEFT bordercolor="#F0F0FF">
	<font face="Arial">	<%= strCurrentStatus%>
	<% if strCurrentstatus = "AT DOOR" then %>
	&nbsp;&nbsp;&nbsp;&nbsp;DOOR:&nbsp;<%= strFromDoor %>
	<% end if %>
</font></td>
</tr>
       <tr> <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF">&nbsp;</td>
 <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF">&nbsp;</td></tr>
  <tr>
   <td  bgcolor="#FOFOFF" align = right bordercolor="#F0F0FF">
  <font face="Arial" size="2"><b>New Location:&nbsp;</b></font></td>

      <td  bgcolor="#FOFOFF" align = LEFT bordercolor="#F0F0FF">
	<font face="Arial">	
	<select size="1" name="location">
	<option selected>EMPTY</option>
	<option >AT DOOR</option>
	<option >YARD</option>
		<option >MERCHANTS</option>
	</select> </font></td>
</tr>
       <tr> <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF">&nbsp;</td>
 <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF">&nbsp;</td></tr>
  <tr>
   <td  bgcolor="#FOFOFF" align = right bordercolor="#F0F0FF">
  <b><font face="Arial" size="2">Door</font></b><font face="Arial" size="2"><b>:&nbsp;</b></font></td>

      <td  bgcolor="#FOFOFF" align = LEFT bordercolor="#F0F0FF">
	<font face="Arial">	
	<select size="1" name="Door">
	<option value = "" >--Select--</option>
	<option value = "1W" >1W West Dock-FC</option>
	<option value = "2W" >2W West Dock-FC</option>
	<option value = "3W" >3W West Dock-FC</option>
	<option value = "4W" >4W West Dock-FC</option>
	<option value = "5W" >5W West Dock-FC</option>
	<option value = "6W" >6W West Dock-FC</option>
	<option value = "7W" >7W West Dock - By Compactor</option>
	<option value = "1E" >1E East Dock</option>
	<option value = "2E" >2E East Dock</option>
	<option value = "3E" >3E East Dock</option>
	<option value = "4E" >4E East Dock</option>
	<option value = "5E" >5E East Dock</option>
	<option value = "6E" >6E East Dock</option>
	<option value = "7E" >7E East Dock</option>
	<option value = "8E" >8E East Dock</option>
	<option value = "9E" >9E East Dock</option>
	<option value = "10E" >10E East Dock - By HRT5</option>
	<option value = "1B" >1B BASEMENT</option>
	<option value = "2B" >2B BASEMENT- By Ramp</option>
	<option value = "1N" >1N OCC Dock</option>
	<option value = "2N" >2N OCC Dock</option>
	<option value = "3N" >3N OCC Dock</option>
	<option value = "4N" >4N OCC Dock</option>
	<option value = "5N" >5N North Dock</option>
	<option value = "6N" >6N North Dock</option>
	<option value = "7N" >7N North Dock</option>
	<option value = "8N" >8N North Dock</option>
	<option value = "9N" >9N North Dock</option>
	<option value = "1DF" >1DF Dry Fiber Dock</option>
	<option value = "2DF" >2DF Dry Fiber Dock</option>
	<option value = "3DF" >3DF Dry Fiber Dock</option>
	<option value = "4DF" >4DF Dry Fiber Dock</option>
	<option value = "5DF" >5DF Dry Fiber Dock</option>
	<option value = "1RF" >1RF Recycle Fiber Dock</option>
	<option value = "2RF" >2RF Recycle Fiber Dock</option>
	<option value = "3RF" >3RF Recycle Fiber Dock</option>
	<option value = "4RF" >4RF Recycle Fiber Dock</option>

	</select> (Do not select a Door if EMPTY)</font></td>
</tr>
<tr>
   <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF">
	<p align="right">  <font face="Arial" size="2"><b>&nbsp;</b></font></td>
    <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF">  &nbsp;</td>
  </tr>
  <tr>
    <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF">&nbsp;</td>

 <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF">&nbsp;</td>
  </tr>



       <tr>
         <td  bgcolor="#FOFOFF" ALIGN = right bordercolor="#F0F0FF">
    <font face="Arial" size="2"><b>Date Relocated:&nbsp;</b></font></td>
 <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF" align = LEFT>

      <input type="text" name="Date_Received" size="23" value = "<%= strDateReceived%>"></td></tr>
<tr>
  <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF">&nbsp;</td>

    <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF">&nbsp;</td>
  </tr>

  <tr>
   <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF">&nbsp;</td>

    <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF" align = left ><Input name="Update" type="submit" Value="Submit" ></td>
  </tr>
</table>

</div>

</form>
</body>
<%
 Function GetData()
  set objMOC = new ASP_CLS_FIBER
        set rstFiber = objMOC.FiberCarrier()
        strDateReceived = formatdatetime(Now(),0)
End Function



 Function SaveData()
	Dim strRightNow
	strRightnow = Now()
strLocation = Request.form("location")
strCurrentstatus = Request.querystring("s")
strToDoor = request.form("Door")
strCarrier = request.form("Carrier")
strTrailer = Request.form("Trailer")

strDateReceived = Request.form("Date_received")     
If Request.querystring("d") = "None" then
strFromDoor = "None"
else
strFromdoor = Request.querystring("d")
end if
If strLocation = "EMPTY" then   

 strsql = "Update tblCars set Date_unloaded = '" & strDateReceived & "',  Location = 'EMPTY' where CID = " & strid & ""
    set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
          strsql = "Update tblCarsMT set Date_unloaded = '" & strDateReceived & "',  Location = 'EMPTY' where PID = " & strid & ""
    set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
        strsql = "Insert into tblMovement (CID, Ddate, TDate, From_location, To_location, From_door,  BID) Select "_
			&" " & strID & ", '" & strdatereceived & "', '" & strRightNow & "', '" & strCurrentStatus & "', '" & strLocation & "', '" & strFromDoor & "',  '" & Session("EmployeeID") & "'"
			
		  set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
 
 elseif strLocation = "AT DOOR"  then
          strsql = "Update tblCars set Door = '" & strToDoor & "', Location = 'AT DOOR' where CID = " & strid & ""
           set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
            strsql = "Update tblCarsMT set Door = '" & strToDoor & "', Location = 'AT DOOR' where PID = " & strid & ""
           set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
       strsql = "Insert into tblMovement (CID, Ddate, TDate, From_location, To_location, From_door,  To_door, BID, Comment) Select "_
			&" " & strID & ", '" & strdatereceived & "', '" & strRightNow & "', '" & strCurrentStatus & "', '" & strLocation & "', "_
			&"  '" & strFromDoor & "',  '" & strTodoor & "', '" & Session("EmployeeID") & "', 'Out NF Trailer'"
			
		  set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
         	strsql = "Select NFID from tblCars where CID = " & strid & ""
	
	    Set MyRec = Server.CreateObject("ADODB.Recordset")
   		 MyRec.Open strSQL, Session("ConnectionString")
		
   		strNFID = MyRec.fields("NFID")
   		   	
   			MyRec.close
   		strsql = "Update tblSAPOpenPO set Report_Location = 'A' where OID = " & strNFID & ""
		set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
       
      
         
elseif strLocation = "MERCHANTS" then 
  strsql = "Update tblCars set Location = 'MERCHANTS', Door = Null where CID = " & strid & ""
  	     set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
         	strsql = "Insert into tblMovement (CID, Ddate, TDate, From_location, To_location, From_door, BID, Comment) Select "_
			&" " & strID & ", '" & strdatereceived & "', '" & strRightNow & "', '" & strCurrentStatus & "', 'MERCHANTS', '" & strFromDoor & "', "_
			&"  '" & Session("EmployeeID") & "', 'Out NF Trailer'"
			
		  set MyRec = new ASP_CLS_DataAccess
        MyRec.ExecuteSql strSql 



else
          strsql = "Update tblCars set Location = 'YARD', Door = Null where CID = " & strid & ""

 
 	     set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
         	strsql = "Insert into tblMovement (CID, Ddate, TDate, From_location, To_location, From_door, BID, Comment) Select "_
			&" " & strID & ", '" & strdatereceived & "', '" & strRightNow & "', '" & strCurrentStatus & "', 'YARD', '" & strFromDoor & "', "_
			&"  '" & Session("EmployeeID") & "', 'Out NF Trailer'"
			
		  set MyRec = new ASP_CLS_DataAccess
        MyRec.ExecuteSql strSql 
 end if 


If strLocation = "EMPTY" then  
strSpecies = request.form("Species")
 strDateSite = request.form("Date_site")
 strDateReceived = formatdatetime(strDateReceived,2)
 
 Dim strEmailAdd
 strsql = "Select Carrier, Email_add  from tblCarrier where  Carrier = '" & strCarrier & "'"
 
    	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")

	If not Myrec.eof then
	
	strEmailadd = MyRec.fields("Email_add")
	if strEmailadd = "<EMAIL>" then
	'skip
	else
	strCarrier = MyRec("Carrier")
 

 
 

              strEmailTo = strEmailadd
        	  
              strEBCC = "<EMAIL>"
                 
			Set objMail = Server.CreateObject("CDO.Message")		
			objMail.From = "<EMAIL>"
			objMail.To = strEmailTo
		 
			'objMail.BCC = strEBCC
                 
	
			objMail.Subject = "Trailer " & strTrailer & " Empty "
	objMail.HTMLBody = "<font face = arial size = 2>Trailer<b> " & strCarrier & " " & strTrailer & "</b> arrived at KC Mobile, AL  on " & strDateSite & " with " & strSpecies & " and was emptied on " & strDateReceived & "."



			' objMail.Send
			Set objMail = nothing
		
end if
end if
MyRec.close

end if




         
Response.redirect ("SelectTruckUnloadNF.asp")



End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->