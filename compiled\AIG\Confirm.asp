<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Confirm Pricing</TITLE>
<!--#include file="classes/asp_cls_DataAccessAIG.asp"-->
<!--#include file="classes/asp_cls_headerAIG.asp"-->
<!--#include file="classes/asp_cls_SessionStringAIG.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<%

Dim strSQL, MyRec, strid, strPage, strStatus
strtoday = DateAdd("h", -5, Now()) 

strid = Request.querystring("id")
strPage = Request.querystring("p")
strStatus = Request.querystring("c")
strPO = request.querystring("po")
strVendor = request.querystring("v")

  if strStatus = "No" then
  strsql = "Update tblPricing set Confirmed = 'Yes',  Confirm_date = '" & strtoday & "' where ID = " & strid
  else
    strsql = "Update tblPricing set Confirmed = 'No', Confirm_date = Null  where ID = " & strid
    end if


   
  	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
         If strPage = "MC" then
         
          Response.redirect("Mobile_pricing_to_confirm.asp?p=" & strPO & "&v=" & strvendor)
          elseif strPage = "M" then
           Response.redirect("Mobile_pricing.asp")
            elseif strPage = "L" then
            Response.redirect("Loudon_pricing.asp")
             elseif strPage = "LC" then
            Response.redirect("Loudon_Pricing_to_Confirm.asp?p=" & strPO & "&v=" & strvendor)

                 elseif strPage = "O" then
            Response.redirect("OWB_pricing.asp")
             elseif strPage = "OC" then
            Response.redirect("OWB_pricing_to_confirm.asp")
           end if
%>