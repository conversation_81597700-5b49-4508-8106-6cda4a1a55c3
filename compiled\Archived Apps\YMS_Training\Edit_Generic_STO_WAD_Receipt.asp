<html xmlns:m="http://schemas.microsoft.com/office/2004/12/omml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Edit STO Fiber Trailer Receipt</title>
<style type="text/css">
.style28 {
	color: #9F112E;
}
.style29 {
	font-weight: bold;
	font-family: Arial, Helvetica, sans-serif;
}
.style30 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	text-align: right;
		font-family: Arial, Helvetica, sans-serif;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style31 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial, Helvetica, sans-serif;
		font-size: x-small;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style32 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-weight: bold;
		text-align: center;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style34 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style35 {
	font-weight: bold;
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style36 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
.style38 {
	font-weight: bold;
	font-family: Arial;
	font-size: x-small;
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style39 {
	font-size: x-small;
	font-family: Arial, Helvetica, sans-serif;
}
.style40 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	font-family: Arial;
	font-size: x-small;
}
.auto-style1 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-weight: bold;
	text-align: center;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #EAF1FF;
}
.auto-style2 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	text-align: right;
	font-family: Arial, Helvetica, sans-serif;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #EAF1FF;
}
.auto-style3 {
	font-weight: bold;
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #EAF1FF;
}
.auto-style4 {
	font-weight: bold;
	font-family: Arial;
	font-size: x-small;
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #EAF1FF;
}
.auto-style5 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #EAF1FF;
}
.auto-style6 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #EAF1FF;
}
.auto-style7 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	font-family: Arial;
	font-size: x-small;
	background-color: #EAF1FF;
}
.auto-style8 {
	border: 1px solid #000000;
	border-collapse: collapse;
}
.auto-style9 {
	color: #000000;
	font-size: x-small;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID, strLoad, MyConn, objMOC, rstFiber, strLocation, strSAP, rstTrailer , strTrailerWeight , strTractor   
  
    strCID = request.querystring("cid")

    
strsql = "select  tblCars.* from tblCars where CID = " & strCID

	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
	 If not Myrec.eof Then
		strCarrier = MyRec("Carrier")
		strSpecies = MyRec("Species")
		strGrade = MyRec("Grade")
		strSAP = MyRec("SAP_Nbr")
		strBroke_Description = MyRec("Broke_Description")
		strTrailer_weight = MyRec("Trailer_weight")
		strTrailer_TID = MyRec("Trailer_TID")
		strDatereceived = MyRec("Date_received")
		strLocation = MyRec("Location")
		strTrailer = MyRec("Trailer")
		strOther_Comments = MyRec("Other_Comments")
		strTonsReceived = MyRec("Tons_Received")
		strNet = MyRec("Net")
		strSapWeight = MyRec("Net")
		strGrossWeight = MyRec("Gross_weight")
		strTareWeight = MyRec("Tare_weight")
		strEntry_Time = MyRec("Entry_Time")
		strEntry_BID = MyRec("Entry_BID")
		strEntry_Page = MyRec("Entry_Page")
		strStatus = MyRec("Status")
		strSTO_Number = MyRec("STO_Number")
		strLoad = MyREc("STO_Number")
		strPO = MyRec("PO")
		strBOL = MyRec("BOL")
		strFormat_pkg = Trim(MyRec("Format_pkg"))
		strBalesRF = Trim(MyRec("Bales_RF"))
		strTractorWeight = MyRec("Tractor_weight")
		strGenerator = MyRec("Vendor")
end if
 
  set objGeneral = new ASP_CLS_General
if objGeneral.IsSubmit() Then
 
	
	Call SaveData() 
		Response.redirect ("Edit_STO_Trucks.asp")	 
 
end if  
%>

<table width = 100%><tr><td width = 33% style="height: 24px">
 <td align = center style="height: 24px; width: 50%;"><b><font face="Arial" size="4">
Edit STO Wadding Trailer Receipt</font> </b></td>
	<td align = right width = 33% style="height: 24px"><b><font face = arial size = 2><a href="Edit_STO_Trucks.asp">RETURN</a></font></b></td></tr></table>




<form name="form1" action="Edit_Generic_STO_Wad_Receipt.asp?cid=<%= strcid %>" method="post">

<input type="hidden" name="SAP_Load" value="<%= strSap %>">
<div align="center">
<table cellspacing="0" bgcolor="#FFFFEA" style="width: 85%;" cellpadding="0" bordercolorlight="#C0C0C0" class="auto-style8">
  <tr>
    <td  align = right style="height: 42px; width: 25%;" class="auto-style3" >
   <span class="style39">Material (SAP #)</span>:&nbsp; </td>
<td  align = left style="height: 42px" class="auto-style7">

		U<font size="2">NRESOLVED</font></td>
<td  align = left width="29%" style="height: 42" class="auto-style3"> 

&nbsp;</td></tr>
  <tr>
    <td  align = right style="height: 32px; width: 25%;" class="auto-style3" >
   <font face="Arial" size="2">Trailer:&nbsp;</font></td>
<td  align = left colspan="2" style="height: 32px" class="auto-style6">

      <font face="Arial">

      <input name="Trailer" size="15" value = "<%= strTrailer%>" style="font-weight: 700" tabindex="1"><b>
		</b>&nbsp;&nbsp;</font></td>
	</tr>
  <tr>

      <td align = right style="height: 33px; width: 25%;" class="auto-style3">
	<font face="Arial" size="2">Select Carrier:</font></td>
<td  align = left colspan="2" style="height: 33px" class="auto-style6">

      <font face="Arial">   
	<select name="Carrier" style="font-weight: 700">
 	<option value="" selected>  Select Carrier (Required)</option>
 	<% strsql = "Select Carrier,C_Description, Weight from tblCarrier order by Carrier"
 	   	 Set MyRec4 = Server.CreateObject("ADODB.Recordset")
   	 MyRec4.Open strSQL, Session("ConnectionString")
   	 while not MyRec4.eof %>
   	 <option <% if strCarrier = MyRec4("Carrier") then %> selected <% end if %> value="<%= MyRec4("Carrier") %>"><%= MyRec4("Carrier") %>&nbsp;<%= MyRec4("C_Description") %>&nbsp;<%= MyRec4("Weight") %></option>"

 <%MyRec4.Movenext
 wend
 MyRec4.close %>
     </select></font><font face="Arial" size="2"><b>    

      &nbsp;</td>
</tr>



       <tr>
		<td  align = right style="height: 34px; width: 25%;" class="auto-style3" >
		<span class="style39">Shipping Ticket Weight</span>:&nbsp; </td>
<td align = left colspan="2" style="height: 34px" class="auto-style3"> 

<font face = arial size = 3 color = red>

<font face="Arial">  
<span class="style28">

	<input name="SAP_Weight" size="15" value="<%= strSAPWeight%>" tabindex="6">
</span>
<span class="auto-style9">

Tons</td>
</tr>

       <tr>
		<td  align = right style="height: 34px; width: 25%;" class="auto-style4" >
		S<font size="2">TO Delivery Number: </font></td>
<td align = left colspan="2" style="height: 34px" class="auto-style3"> 

<font face = arial size = 3 color = red>
<span class="style28">

<font face="Arial">  
	<input name="Load" size="15" value="<%= strLoad %>" tabindex="6"></td>
</tr>


       <tr>
		<td  align = right style="height: 34px; width: 25%;" class="auto-style3" ><font face="Arial" size="2">Date Received:&nbsp;</font></td>
<td align = left colspan="2" style="height: 34px" class="auto-style3"> 

<font face = arial size = 3 color = red>
<span class="style28">

<font face="Arial"> 
<input name="Date_Received" size="15" value = "<%= strDateReceived%>" style="font-weight: 700" tabindex="7"></font></td>
</tr>
              <tr>
          <td  align = right style="width: 25%;" class="auto-style3" >
   <font face="Arial" size="2">Site Shipped From:&nbsp;</font></td>
<td align = left colspan="2" class="auto-style6">
      <font face="Arial">
      <input name="Generator" size="25" value = "<%= strGenerator %>" style="font-weight: 700" tabindex="8"></font></TD></tr>
         <tr>
          <td  align = right style="height: 37px; width: 25%;" class="auto-style3" >
  <font face="Arial" size="2">Comments:&nbsp;</font></td >
   <td align = left colspan="2" style="height: 37px" class="auto-style6">   <font face="Arial">   
	<input name="Other_Comments" size="25" value = "<%= strOther%>" style="font-weight: 700; width: 364px;" tabindex="9">&nbsp;&nbsp;&nbsp;</td></tr>
<tr>
    <td class="auto-style2" style="height: 26px; width: 25%;">
	<font face = arial size = 3>
	<span class="style29">

	<font size="2">Location:&nbsp;</font></td>

    <td colspan="2" class="auto-style5" style="height: 26px">YARD</td>
  </tr>

    
  <tr>
    <td width="17%" colspan="3" class="auto-style1">

<font face = arial size = 3 color = red>
<span class="style28">

	<font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></td>

  </tr>

    </table>

</div>

</form>

<%  

Function SaveData()



	
	Dim strRightNow, strAud
	strRightnow = Now()
	
	
	strSAPWeight = REquest.form("SAP_Weight")
	strLoad = request.form("Load")
	strSpecies = "UNRESOLVED"
	
strload = request.form("Load")
	
			
	 

	
		strsql =  "UPDATE tblCars SET    Carrier = '" & strCarrier & "',  Date_received = '" & request.form("Date_Received") & "',  "_
&" Trailer = '" & strTrailer & "', Other_Comments = '" & strOther & "',  Tons_Received = " & strSapWeight  & ", Net = " & strSapWeight  & ","_
&"  Status = " & strSapWeight  & ", STO_Number = " & strLoad & ", Vendor = '" & request.form("Generator") & "', Generator = '" & request.form("Generator") & "' WHERE CID = " & strCID
 
	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
   	 




End Function %>
<!--#include file="Fiberfooter.inc"-->