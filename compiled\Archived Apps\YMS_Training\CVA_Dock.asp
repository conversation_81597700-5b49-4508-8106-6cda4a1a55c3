
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">
<TITLE>CVA/RF Dock Report</TITLE>

<!--#include file="classes/asp_cls_headerOYM.asp"-->
<!--#include file="classes/asp_cls_SessionStringOYM.asp"-->
 

<!--#include file="classes/asp_cls_DataAccessOYM.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

    <% Dim MyRec, strsql, strSCNN
   strDate = NOW()
   
     strsql = "SELECT  Count(Master_Table.TRAILER) AS [CountOfTrailer] FROM Master_Table "_
	&" WHERE Master_Table.Date_Time_In Is Not Null AND Master_Table.Carrier='SCNN'"
	Set MyRec = Server.CreateObject("ADODB.Recordset") 
   		MyRec.Open strSQL, Session("ConnectionString")
   		
strSCNN = MyRec.Fields("CountofTrailer")
MyRec.close
 %>

<body bgcolor = "#FCFBF8"><br><p align = center>
<b><font face="Arial">CVA/PM Dock Report as of <%= strdate%>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
SCNN Trailer Count:&nbsp; <%= strSCNN%></font></b></p><br>

           

 <table align = center width = 50%>
       <tr>
       <td><b><font size="2" face="Arial">Dock</font></b></td><td>
		<b>
		<font size="2" face="Arial">Carrier</font></b></td>
		<td align="left"><b><font size="2" face="Arial">Trailer</font></b></td>
	<td align="left"><b><font size="2" face="Arial">Attention Flag</font></b></td>




<%



   strsql = "SELECT  Carrier, TRAILER, CVA_Dock, Attention_Flag FROM Master_Table where [status] = 4 AND (CVA_Dock<47 Or CVA_Dock >80) "_
 &"  ORDER BY CVA_Dock"


		
		Set MyRec = Server.CreateObject("ADODB.Recordset") 
   		MyRec.Open strSQL, Session("ConnectionString")

do until  MyRec.eof %>

<tr>

			
		 
    
    <td><font face = Arial size = 2><%= MyRec.fields("CVA_Dock").value %></td>
        <td><font face = Arial size = 2><%= MyRec.fields("Carrier").value %></td>
 <td><font face = Arial size = 2><%= MyRec.fields("Trailer").value %></td>
     <td><font face = Arial size = 2><%= MyRec.fields("Attention_Flag").value %></td>


     </tr>
    <%  
       
 	 MyRec.MoveNext
	
loop
MyRec.close

      
    %>

  </table>    
                        





  <font size="2" face="Arial">    
                        





  </table>

</BODY></font><br>