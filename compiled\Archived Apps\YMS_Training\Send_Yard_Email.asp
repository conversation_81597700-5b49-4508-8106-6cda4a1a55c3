																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Send Email for Truck Report</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 

<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate

strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -7, strtdate)

strsql = "SELECT tblYardEmailDate.* FROM tblYardEmailDate WHERE E_Key = 1"
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body bgcolor="#FFFFFF">
<br><br><br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><b><font face="Arial">Send out Email for Yard Report and OF 
Recap (5:30 
AM)</font></b></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br><br><br>

<table border="1" width="50%" align = center id="table1" cellspacing="1" bordercolorlight="#808080" bordercolor="#000000">

	
		<tr>
		<td bordercolor="#C0C0C0" align="center" bgcolor="#FFFFDD"><b><font face="Arial">Last Time 
		Sent</font></b></td>
		
		<td bordercolor="#C0C0C0" align="center" bgcolor="#FFFFDD"><b><font face="Arial">Sent By</font></b></td>
		
	</tr>
			<tr>
	
		<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("Email_date")%></font></b></td>
	<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("Ename")%></font></b></td>
	
	</tr>
	</table>
	
	<p align = center><font face="Arial" size = 4><a href="Yard_OF_email.asp">Click Here to Send 
	Yard Report, OF Recap, Warehouse Summary, Broke Unload Summary, RFF Rail 
	Unload Report, OWB RF Report and Material Report Email</a></p>
	
	<p align = center><font face="Arial" size = 4><a href="Broke_email.asp?s=i">Click 
	Here to Send Broke Unload Summary </a>&nbsp; </p>

		<p align = center><font face="Arial" size = 4><a href="Yard_email.asp">Click Here to Send 
	Yard Report only</a></p>

		<p align = center><font face="Arial" size = 4><a href="Baldwin_email.asp">Click Here to Send Baldwin Email Only</a> 
		</font> </p>
		<p align = center><a href="KDF_email.asp?s=i">Click Here to Send 
		Materials Report Only</a> </p>
	<p align = center><font face="Arial" size = 4><a href="OF_email.asp?s=i">Click 
	Here to Send &quot;OF&quot; Recap Only</a>&nbsp; </p>
		<font face="Arial" size = 4>
	<p align = center><font face="Arial" size = 4><a href="Warehouse_RPT_email.asp?s=i">Click 
	Here to Send Warehouse Summary</a>&nbsp; </p>
	<p align = center><font face="Arial" size = 4><a href="Yard_rail_Email.asp?s=i">Click 
	Here to Send RFF Rail Unload Report</a>&nbsp; </p>
	<p align = center><font face="Arial" size = 4><a href="Yard_Email_BWH.asp?s=i">Click 
	Here to Send Broke loads identified for Warehouse Report</a>&nbsp; </p>

	<p align = center><font face="Arial" size = 4><a href="OYM_RF_Trailer_Inventory_Email.asp?s=i">Click 
	Here to Send RF Trailer Inventory Report Owensboro, KY</a>&nbsp; </p>
	