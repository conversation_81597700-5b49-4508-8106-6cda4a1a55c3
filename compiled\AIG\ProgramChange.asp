<% Option Explicit %>

<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Program Change Requests</TITLE>

<!--#include file="classes/asp_cls_DataAccessAIG.asp"-->
<!--#include file="classes/asp_cls_headerAIG.asp"-->
<!--#include file="classes/asp_cls_SessionStringAIG.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_ProcedureMRP.asp"-->


 <%  Dim objEPS
    Dim objGeneral
   dim   rstEPS
   dim intDirection
   dim intPageNumber
   dim strPageNav


   intPageNumber = 1

   
Buildscreen


Sub BuildScreen()
      set objGeneral = new ASP_CLS_General
       

  if objGeneral.IsSubmit() Then
    Call GetFormData()
  
        Call LoadSearchResults()
  
  else
    intDirection = 0
 
  end if


 %>



<script language="javascript">

  function Search()
  {
    
    document.forms["form1"].elements["Direction"].value = 1;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }

  function NextPage()
  {
    document.forms["form1"].elements["Direction"].value = 5;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }
  function PrevPage()
  {
    document.forms["form1"].elements["Direction"].value = 6;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }
</script>

<style type="text/css">
.style1 {
	font-family: Arial;
}
</style>
</head>

<form name="form1" action="ProgramChange.asp" method="post">
 <input type="hidden" name="Direction" value="2" >
  <input type="hidden" name=PageNumber value="<%=intPageNumber%>" >
<br>




<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>  
  <tr><td align = Center  ><font face = arial size = 2><B>Search for Program Change Request to View </b>
	</font></td>


	</tr></table>
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1>  

  <TR>
   <TD><font face="Arial" size="2"><b>Status</b></font>&nbsp;&nbsp;
     <font face="Arial">	<select size="2" name="Status">
	<option selected>All</option>
	<option>Approved</option>
	<option>Development Reviewed</option>
	<option>Implemented</option>
	</select>  &nbsp;&nbsp;&nbsp;
<input type="button" onClick="javascript:Search()" value="Search" caption="Search">
   </td>
 
       
      <TD align = center class="style1"> <a href="PCEdit.asp?action=add">
		<strong>Add New Request</strong></a></TD>
    
   </TR>
  
  </TABLE>


</form>

 
  <% if objGeneral.IsSubmit()  or request.querystring("action") = "return" Then %>
   <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class="tablecolor1" border=1>
    <tr><td colspan="17" bgcolor="white"><font face="Arial" size="2"><b>Search Results</b> - Page <%=intPageNumber%></font></td></tr>
    <tr><td colspan="17" bgcolor="white" align="right"><%=strPageNav%>&nbsp;</td></tr>
      <tr class="tableheader">
	
	<td><b><font face="Arial" size="2">Request</font></b></td>
	<td><b><font face="Arial" size="2">Request<br> Date</font></b></td>
	<td align = center><font face="Arial" size="2"><b>Requester</b></font></td>
	
	<td align = center><font face="Arial" size="2"><b>Approval<br> Date</b></font></td>	
	<td align = center><font face="Arial" size="2"><b>Approved</b></font></td>
 <td align = center><font face="Arial" size="2"><b>Edit</b></font></td>
	<td align = center><font face="Arial" size="2"><b>Review<br> Date</b></font></td>
	
	<td align = center><font face="Arial" size="2"><b>Review</b></font></td>
	<td align = center><font face="Arial" size="2"><b>Edit</b></font></td>
	
	<td align = center><font face="Arial" size="2"><b>Implementation<br> Date</b></font></td>
	
        <td align = center><font face="Arial" size="2"><b>Web Pages<br> Affected</b></font></td>
      
	<td align = center><font face="Arial" size="2"><b>Edit</b></font></td>
	
<td align = center><font face="Arial" size="2"><b>Awareness<br> Notes</b></font></td>
<td align = center><font face="Arial" size="2"><b>Edit</b></font></td>
	<td align = center><font face="Arial" size="2"><b>&nbsp;</b></font></td>
  	      
  	<% 
      Dim ii
       ii = 0
       while not rstEPS.Eof


    %>
    <% if ( ii mod 2) = 0 Then %>
		
       			<tr class=tablecolor1>
			
 	
    <% else %>
	
       			<tr class=tablecolor2>
			
	
    <% end if %>
	<td ><font face = "arial" size = "2"><%= rstEPS.fields("REquest") %></td>
	<td align = center><font face = "arial" size = "2"><%= rstEPS.fields("REquest_Date") %></td>
	<td align = center><font face = "arial" size = "2"><%= rstEPS.fields("REquestor") %></td>
	<td align = center><font face = "arial" size = "2"><%= rstEPS.fields("Approval_date") %>&nbsp;</td>
	<td align = center><font face = "arial" size = "2"><%= rstEPS.fields("Approval") %>&nbsp;</td>
<td align = center><font face = "arial" size = "2"><a href="PCEdit.asp?action=edit&id=<%= rstEPS.fields("ID") %>">Edit </a></td>

	<td align = center><font face = "arial" size = "2"><%= rstEPS.fields("Test_review_date") %>&nbsp;</td>
	<td align = center><font face = "arial" size = "2"><%= rstEPS.fields("Test_review") %>&nbsp;</td>
<td align = center><font face = "arial" size = "2">

<a href="PCEditR.asp?action=edit&id=<%= rstEPS.fields("ID") %>">Edit</a>
</td>
	
	<td align = center><font face = "arial" size = "2"><%= rstEPS.fields("Implement_Date") %>&nbsp;</td>
	
	<td align = center><font face = "arial" size = "2"><%= rstEPS.fields("Web_pages") %>&nbsp;</td>
 <td align = center><font face = "arial" size = "2">
<a href="PCEditI.asp?action=edit&id=<%= rstEPS.fields("ID") %>">Edit</a>
</td>
<td align = center><font face = "arial" size = "2"><%= rstEPS.fields("Awareness_notes") %>&nbsp;</td>
<td align = center><font face = "arial" size = "2"><a href="PCEditA.asp?action=edit&id=<%= rstEPS.fields("ID") %>">Edit</a></td>

<td align = center><font face = "arial" size = "2"><a href="PCEdit.asp?action=delete&id=<%= rstEPS.fields("ID") %>">Delete </a></td>


</tr>
    <%
       ii = ii + 1
       rstEPS.MoveNext
     Wend
    %>
   </table>
	<table>    <tr><td colspan="7" bgcolor="white" align="right"><%=strPageNav%>&nbsp;</td></tr></table>

  <% end if
End Sub%>    
   
 <% 
 

    Function GetFormData()
        intDirection = cint(Request.Form("Direction"))
    
       
      intPageNumber = cint(Request.Form("PageNumber"))
    
 

    End Function

 

    Function LoadSearchResults()
      Dim objEPSSearch


      if intDirection =1 Then
         intPageNumber = 1
      end if

      if intDirection = 5 Then
         intPageNumber = intPageNumber + 1
      end if
      if intDirection = 6 Then
         intPageNumber = intPageNumber - 1
      end if



      set objEPSSearch = new ASP_Cls_ProcedureMRP
     
	If request.form("Status") = "Approved" then
	set rstEPS = objEPSSearch.ESLSearchPCAPP(intPageNumber)
	elseif request.form("Status") = "Development Reviewed" then
	set rstEPS = objEPSSearch.ESLSearchPCDR(intPageNumber)
	elseif request.form("Status") = "Implemented" then
	set rstEPS = objEPSSearch.ESLSearchPCIMP(intPageNumber)
	else
	set rstEPS = objEPSSearch.ESLSearchPC(intPageNumber)
	end if
    
     if ( not rstEPS.Eof) Then
      if ( intPageNumber < rstEPS.fields("TotalPage").value ) Then
        strPageNav = "<a href=javascript:NextPage()><b>Next Page</b></a>"
      end if
      if ( intPageNumber > 1 ) Then
         strPageNav = "<a href=javascript:PrevPage()><b>Previous Page</b></a>&nbsp;&nbsp;&nbsp;&nbsp;" & strPageNav
      end if
     end if
    End Function


 %><!--#include file="AIGfooter.inc"-->