
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">


<TITLE>Select Release Number</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->

<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

 <%    Dim objDAC, strSQL, MyRec, objMOC, rstFiber, strRelease, strR, strID
	Dim gSpecies, gSap_Nbr, gVendor, gPO, gRelease, gOID, gGenerator, gCity, gState
       Dim objGeneral, strDate, MyConn, strduplicate
strDate = formatdatetime(Now(),2)
	
	
       set objGeneral = new ASP_CLS_General
  Call getData()   
if objGeneral.IsSubmit() Then

strR = Request.form("Release")

strsql = "SELECT Release from tblDuplicates"
    Set MyRec = Server.CreateObject("ADODB.Recordset") 

    MyRec.Open strSQL, Session("ConnectionString")
    While not MyRec.eof

 If ucase(MyRec.fields("Release")) = ucase(strR)  then
  strduplicate = "YES"
  end if
  
  MyRec.movenext
  wend
  
  If strduplicate = "YES" then
 Response.redirect("Generic_Receipt.asp?r=" & strR)
 else

if left(strR,1) = "B" then


	Response.redirect("EnterBrokeReceipt.asp?id=" & strR)
else

	Response.redirect("EnterReceipt.asp?id=" & strR)
	end if
	end if


End if
%>
<SCRIPT LANGUAGE="JavaScript">
function formHandler(Form1){
document.forms["Form1"].submit();
}
</SCRIPT>

<style type="text/css">
.style1 {
	border-width: 1px;
	background-color: #EAF1FF;
}
.style2 {
	text-align: center;
}
</style>
</head>

<body>
<form name="form1" action="SelectRelease.asp" method="post" >

<p>&nbsp;</p>
<table border="1" cellpadding="0" class = "tablecolor1" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="50%" id="AutoNumber2" align = center>

       <TD align = center class="style1">  <p>&nbsp;</p> <font face="Arial" size="3"><b>Select Release Number:&nbsp;&nbsp;</b></font>
	&nbsp;&nbsp;&nbsp;
     <font face="Arial">
     <select name="Release">
 	<option value="" selected>Release -- PO Nbr</option>
        				<% 	Do While Not rstFiber.EOF
					%>
            			
						<option VALUE="<%= rstFiber.fields("Release")%> ">
                				<%=rstFiber.fields("PRDisplay")%>
                				<% if left(rstFiber("Release"),1) = "C" Then  %>
                				&nbsp; <% end if %>
                				</option>

					<% 
        					rstFiber.MoveNext 
        					Loop         				
        					rstFiber.close
					%>

     </select><br>
		<br>
		<p>&nbsp;</p> 
<p align="center"><input type="submit" value="Continue" id=submit1 name=submit1><br>&nbsp;</TD></tr>

</table>
</form>


<p align="center">
     <font face="Arial">
     <b><a href="Generic_Receipt.asp">If Truck's Release Number is not an option, Click 
		Here for Species other than Broke</a> <br></p>
     <div class="style2">
     <br>

<font face="Arial" size="3">&nbsp; Non-Shuttle only:&nbsp;&nbsp; <a href="Broke_Receipt.asp">BROKE load with 
			unidentifiable or invalid release #</a></font><br>

</div>


<%

 Function GetData()
      Set rstFiber = Server.CreateObject("ADODB.Recordset")
      strtoday = formatdatetime(now(),2)
      
      
strsql2 = "SELECT tblOrder.Release, tblOrder.PO, tblOrder.Release+' - '+tblOrder.PO AS PRDisplay "_
&" FROM (tblOrder LEFT JOIN tblCars ON tblOrder.Release = tblCars.Release_nbr) LEFT JOIN tblVirginFiber ON tblOrder.Release = tblVirginFiber.Release "_
&" WHERE tblCars.Trailer Is Null AND tblOrder.import_month IN ("  & "'" & UCase(MonthName(Month(DateAdd("m",-2,Now())),True)) & " " & Right(Year(DateAdd("m",-2,Now())),2) & "'," & _
"'" & UCase(MonthName(Month(DateAdd("m",-1,Now())),True)) & " " & Right(Year(DateAdd("m",-1,Now())),2) & "'," & _
"'" & UCase(MonthName(Month(DateAdd("m",1,Now())),True)) & " " & Right(Year(DateAdd("m",1,Now())),2) & "'," & _

"'" & UCase(MonthName(Month(Now()),True)) & " " & Right(Year(Now()),2) & "') AND tblVirginFiber.Release Is Null "_
&" ORDER BY tblOrder.Release"
 
    rstFiber.Open strSQL2, Session("ConnectionString"), adOpenDynamic
     

    End Function  %><!--#include file="Fiberfooter.inc"-->