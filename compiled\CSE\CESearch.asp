

<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>CSE Search</TITLE>

<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_Procedure.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->

<%

   Dim objEPS
   Dim   strSortField, strSortType, rstTeam, strTeam, rstESL, rstWA, strWA,  strID, strStatus
   Dim objGeneral, strPNumber, strSource, rstSource, rstAsset,  strSOP, strST
   
   dim   rstEPS
   dim intDirection
   dim intPageNumber
   dim strPageNav, strAsset
Dim strE_name, strBID
	
strBID = Session("EmployeeID")
 strE_Name = ReturnEname(StrBID)
 strstatus = "1"
 

   intPageNumber = 1
      set objGeneral = new ASP_CLS_General

   



  if objGeneral.IsSubmit() Then
    Call GetFormData()
  
        Call LoadSearchResults()
  
  else
    intDirection = 0
  end if
Call getdata()
%>

<script language="javascript">
 


  function Search()
  {
    
    document.forms["form1"].elements["Direction"].value = 1;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }

  function NextPage()
  {
    document.forms["form1"].elements["Direction"].value = 5;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }
  function PrevPage()
  {
    document.forms["form1"].elements["Direction"].value = 6;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }
</script>


<style type="text/css">
.auto-style1 {
	border-width: 1px;
	background-color: #EDF7FE;
}
</style>
</head>


<form name="form1" action="CESearch.asp" method="post">
 <input type="hidden" name="Direction" value="1" >
 <input type="hidden" name="ID" value="" >
 <input type="hidden" name=PageNumber value="<%=intPageNumber%>" >

<TABLE borderColor=#C0C0C0 cellSpacing=0 cellPadding=0 width=100%  border=0>  
  <tr><td colspan="5" align = Center height="40" bordercolor="#C0C0C0"  >
	<font face = arial><B>Search for 
	Confined Space to View</b></td>


	</tr></table>
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1>  

  <TR>
   <TD bordercolor="#C0C0C0" class="auto-style1"><b><font face="Arial" size="2">&nbsp;</font><font face="Arial">Team&nbsp; </font> 
    <font face="Arial" size="2">&nbsp;&nbsp;&nbsp;&nbsp; </font>  </b>  <font face="Arial">    <select name="Team">
       <option value="">--- All ---</option>
       <% if strTeam = "LDC" then 
       strTeam = "LDC "
       end if %>
       <%= objGeneral.OptionListAsString(rstTeam, "Team", "Team", strTeam) %>
     </select></font></TD>
	    <TD bordercolor="#C0C0C0" class="auto-style1"><b>
		<font face="Arial" size="2">&nbsp;</font><font face="Arial">Work Area&nbsp; </font>
		<font face="Arial" size="2">&nbsp; </font>
		</b>
		<font face="Arial">  <select name="Workarea">
       <option value="">--- All ---</option>
       <%= objGeneral.OptionListAsString(rstWA, "WorkArea", "WorkArea", strWA) %>
     </select></font></TD>
	    <TD bordercolor="#C0C0C0" class="auto-style1">&nbsp; <font face="Arial">
		<b>Space ID&nbsp; </b>&nbsp;<input type="text" name="SOP" size="9" value="<%= strSOP%>" style="width: 110px"></font></TD>
		  <TD bordercolor="#C0C0C0" class="auto-style1">&nbsp;<b><font face="Arial">Space Type</font></b></TD>

	    <TD bordercolor="#C0C0C0" class="auto-style1">
		<font face="Arial">  <select name="Space_Type" size="1">
		           <option value = "">--All---</option>
       <option <% if strST = "CSE" then %> selected <% end if %> value="CSE">Permit Required Confined Space</option>
       <option <% if strST = "NP CSE" then%> selected <% end if %> value="NP CSE">Non-Permit Required Confined Space</option>
         <option <% if strST = "NOT CSE" then%> selected <% end if %> value="NOT CSE">Non-Confined Space</option>
 

     </select></font></TD>
	    <TD bordercolor="#C0C0C0" class="auto-style1">&nbsp;<b><font face="Arial">Space 
		Status</font></b></TD>
	    <TD bordercolor="#C0C0C0" class="auto-style1">
		<font face="Arial">  <select name="Space_status" size="1">
		
       <option <% if strstatus = "In Use" then %> selected <% end if %>>In Use</option>
       <option <% if strStatus = "Abandoned" then%> selected <% end if %>>Abandoned</option>
         <option <% if strStatus = "Not Used" then%> selected <% end if %>>Not Used</option>
           <option <% if strStatus = "Removed" then%> selected <% end if %>>Removed</option>
           <option   <% if strStatus = "" then%> selected <% end if %> value = "">--All---</option>
     </select></font></TD>
	    <TD bordercolor="#C0C0C0" class="auto-style1">&nbsp;</TD>
	    <TD bordercolor="#C0C0C0" class="auto-style1"><input type="button" onClick="javascript:Search()" value="Search" caption="Search"></TD>


</TR>

  </TABLE></form>


  <% if objGeneral.IsSubmit()  or request.querystring("action") = "return" Then %>
   <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class="tablecolor1" border=1>
    <tr><td colspan="17" bgcolor="white"><font face="Arial" size="2"><b>Search Results</b> - Page <%=intPageNumber%></font></td></tr>
    <tr><td colspan="17" bgcolor="white" align="right"><%=strPageNav%>&nbsp;</td></tr>
      <tr class="tableheader">
	
	<td height="16"><font face="Arial" size="1"><b>HA</b></font></td>
		<td height="16"><font face="Arial" size="1"><b>Approval<br>History</b></font></td>
	<td height="16"><font face="Arial" size="1"><b>Summary</b></font></td>
<td height="16"><font face="Arial" size="1"><b>Permit</b></font></td>
<td height="16" align="center"><font face="Arial" size="1" ><b>New Permit</b></font></td>
	<% if len(Request.form("Team")) > 1 then %>
	<% else %>
	
	<td height="16" ><font face="Arial" size="1"><b>Team<b></font></td>
	<% end if %>
	<% if len(Request.form("Workarea")) > 1 then %>
	<% else %>
	<td height="16" ><font face="Arial" size="1"><b>Work Area<b></font></td>
	<% end if %>
	<td height="16" ><font face="Arial" size="1"><b>Space ID</b></font></td>
	<td height="16" ><font face="Arial" size="1"><b>Description</b></font></td>
<td height="16" ><font face="Arial" size="1"><b>Location</b></font></td>
<td height="16" >
<p align="center"><font face="Arial" size="1"><b>HA Approval <br>Date</b></font></td>
<td height="16" ><font face="Arial" size="1"><b>Comments</b></font></td>
<td height="16" ><font face="Arial" size="1"><b>HA Status</b></font></td>
	
</tr>
      
        
      
  	<% 
      Dim ii
       ii = 0
       while not rstEPS.Eof 
    if ( ii mod 2) = 0 Then %>
       			<tr Bgcolor=#E6EEFF>
		
 	
    <% else %>

       			<tr class=tablecolor2>
			<% end if %>

<% if rstEPS.fields("HA_Status") = "APPROVED" and strE_Name = rstEPS.fields("HA_Approver_1") or strE_Name = rstEPS.fields("HA_Approver_2") or strBID = "C97338" or strBID = "B41792" then %>
<td align = center><font face = "arial" size = "1">&nbsp;<a href="Section1.asp?id=<%= rstEPS.fields("SOP_NO")%>">View</a>&nbsp;</td> 
<% elseif rstEPS.fields("HA_Status") = "APPROVED" then %>
<td align = center><font face = "arial" size = "1">&nbsp;<a href="Section_Reports.asp?id=<%= rstEPS.fields("SOP_NO")%>">View</a>&nbsp;</td> 
<% else %>
<td align = center><font face = "arial" size = "1">&nbsp;<a href="Section1.asp?id=<%= rstEPS.fields("SOP_NO")%>">View</a>&nbsp;</td> 
<% end if %> 
<td align = center><font face = "arial" size = "1">&nbsp;<a href="Change_History.asp?id=<%= rstEPS.fields("SOP_NO")%>&ha=<%=rstEPS.fields("HA_Status")%>">View</a>&nbsp;</td> 

<% 	strSQL = "SELECT * from tblAuthorizers where Work_Area = '" & strwa & "' and P_BID = '" & session("EmployeeID") & "' "
		        Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
			IF not MyRec.eof then %>

<td align = center><font face = "arial" size = "1">&nbsp;<a href="Summary_Details_edit.asp?id=<%= rstEPS.fields("SOP_NO")%>&sid=<%= rstEPS("SID") %>">Edit</a>&nbsp;</td> 
<% else %>
<td align = center><font face = "arial" size = "1">&nbsp;<a href="Summary_Details_view.asp?id=<%= rstEPS.fields("SOP_NO")%>">View</a>&nbsp;</td> 
<% end if
MyRec.close %>
<% if rstEPS.fields("HA_Status") = "APPROVED"  or rstEPS.fields("HA_Status") = "Approved" then %>
<td align = center><font face = "arial" size = "1">&nbsp;<a href="CSE_Permit_View.asp?id=<%= rstEPS.fields("SOP_NO")%>">View</a>

</td> 
<% else %>
<td align = center><font face = "arial" size = "1">&nbsp;</td> 
<% end if %>
<% if rstEPS.fields("HA_Status") = "APPROVED"  or rstEPS.fields("HA_Status") = "Approved" then %>
<td align = center><font face = "arial" size = "1">&nbsp;<a href="CSE_Permit_New.asp?id=<%= rstEPS.fields("SOP_NO")%>">View</a>

</td> 
<% else %>
<td align = center><font face = "arial" size = "1">&nbsp;</td> 
<% end if %>
	<% if len(Request.form("Team")) > 1 then %>
	<% else %>
<td align = left><font face = "arial" size = "1"><%=rstEPS.fields("TeamName")%>&nbsp;</td> 
   <% end if %>
   	<% if len(Request.form("Workarea")) > 1 then %>
	<% else %>
<td align = left><font face = "arial" size = "1"><%=rstEPS.fields("WorkArea")%>&nbsp;</td> 
<% end if %>
<td align = left><font face = "arial" size = "1"><%=rstEPS.fields("SOP_NO")%>&nbsp;</td>
<td align = left><font face = "arial" size = "1"><%=rstEPS.fields("SDescription")%>&nbsp;</td>
<td align = left><font face = "arial" size = "1"><%=rstEPS.fields("Location")%>&nbsp;</td>	
<% if len(rstEPS.fields("HA_Approval_date")) > 3 then %>
<td align = left><font face = "arial" size = "1"><%=rstEPS.fields("HA_Approval_date")%>&nbsp;</td>	
<% else %>
<td align = left><font face = "arial" size = "1"><%=rstEPS.fields("Sdate")%>&nbsp;</td>	
<% end if %>
<td align = left><font face = "arial" size = "1"><%=rstEPS.fields("Comment")%>&nbsp;</td>	
<td align = left><font face = "arial" size = "1"><%=rstEPS.fields("HA_Status")%>&nbsp;</td>
  </tr>
    <%
       ii = ii + 1
       rstEPS.MoveNext
     Wend
    %>
   </table>
	<table>    <tr><td colspan="7" bgcolor="white" align="right"><%=strPageNav%>&nbsp;</td></tr></table>

  <% end if %>    
      
 <%

    Function GetData()
        set objEPS = new ASP_CLS_ProcedureESL
        set rstTeam = objEPS.ReadTeamList()
 	set rstWA = objEPS.ReadWorkArea()
 
	

    End Function
    Function GetFormData()
      intDirection = cint(Request.Form("Direction"))
  


     
      intPageNumber = cint(Request.Form("PageNumber"))
      strTeam = Request.form("Team")
      strWA = Request.form("Workarea")
 	strSOP = Request.form("SOP")
  	strStatus = Request.form("Space_status")
  	strST = Request.form("Space_Type")

    End Function

    Function LoadSearchResults()
      Dim objEPSSearch


      if intDirection =1 Then
         intPageNumber = 1
      end if

      if intDirection = 5 Then
         intPageNumber = intPageNumber + 1
      end if
      if intDirection = 6 Then
         intPageNumber = intPageNumber - 1
      end if

Session("intDirection") = intDirection
Session("intPageNumber") = intPageNumber


   
      set objEPSSearch = new ASP_Cls_ProcedureESL
      
strTeam = Trim(strTeam)

if len(strST) > 1 then

set rstEPS = objEPSSearch.CESEarchR1(intPageNumber,  strTeam, strWA, strSOP, strStatus, strST)
else
set rstEPS = objEPSSearch.CESEarch(intPageNumber,  strTeam, strWA, strSOP, strStatus)
end if
    
     if ( not rstEPS.Eof) Then
      if ( intPageNumber < rstEPS.fields("TotalPage").value ) Then
        strPageNav = "<a href=javascript:NextPage()><b>Next Page</b></a>"
      end if
      if ( intPageNumber > 1 ) Then
         strPageNav = "<a href=javascript:PrevPage()><b>Previous Page</b></a>&nbsp;&nbsp;&nbsp;&nbsp;" & strPageNav
      end if
     end if
    End Function %><!--#include file="footer.inc"-->