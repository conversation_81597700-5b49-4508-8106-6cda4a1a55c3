<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Transfer Load with Release Number from PJ Warehouse</title>
<style type="text/css">
.style3 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
.style4 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	text-align: right;
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
}
.style5 {
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
}
.style6 {
	font-weight: bold;
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
}
.style7 {
	color: #000000;
}
.auto-style1 {
	font-weight: bold;
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	text-align: right;
}
.auto-style3 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.auto-style5 {
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
	text-align: center;
	font-family: Arial, Helvetica, sans-serif;
}
.auto-style6 {
	font-size: x-small;
}
</style>
</head>
<%  strId  = Request.QueryString("id")
    
		strsql = "SELECT tblCars.*  FROM tblCars WHERE CID = " & strid  

   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
	If not MyRec.eof then
	strSpecies = MyRec("Species")
	strTons = MyREc("Net")
	strPO = MyRec("PO")
	strORGCarrier = MyREc("Carrier")
	strVendor = MyRec("Vendor")
	strGenerator = MyRec("Generator")
	strCity = MyRec("Gen_City")
	strState = MyREc("Gen_State")
 
	strSAP = MyRec("SAP_NBR")
	strRelease = MyRec("Release_nbr")

end if

if strOrgCarrier = "RAIL" then
strTons = round(strTons/3,3)
end if

strcarrier = "BWIF"
        strTest = ""
        strTier = ""
        strsql2 = "SELECT tblTier.Tier, tblTier.Test "_
 &" FROM tblTier INNER JOIN tblOrder ON (tblTier.State = tblOrder.State) AND (tblTier.City = tblOrder.City) "_
 &"  AND (tblTier.Generator = tblOrder.Generator) AND (tblTier.Vendor = tblOrder.Vendor) AND (tblTier.Grade = tblOrder.Species) "_
&" where Release = '" & strRelease & "'"
         Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL2, Session("ConnectionString")
    If not MyRec2.eof then
    strTest = MyRec2("Test")
    strTier = MyRec2("Tier")
    end if
    MyRec2.close 
    
    if strTier = "1A" or strTier = "1B" then
    strTier = "1"
    end if 

 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then
 


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
 
		Call SaveData() 

	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to Transfer this load.</font></br>")
	MyRec.close
	end if
 
	end if

%>



<body><br>
<table width = 100%><tr> <td align = center ><b><font face="Arial" size="4">Transfer Load with Release Number <%= strRelease %> from PJ Warehouse</font></b></td></tr></table>



<form name="form1" action="Transfer_From_PJ_Release.asp?id=<%= strid %>" method="post">
<div align="center">
<table cellspacing="0" bordercolor="#C0C0C0" bgcolor="#FFFFE8" style="width: 75%;" cellpadding="0" class="style3">
<tr>
    <td class="style5" colspan="3">&nbsp;</td>

  </tr>


      <td align = right style="width: 361px" class="style6">
  <font face="Arial" size="2">Transfer Trailer Number:&nbsp;</font></td>
<td  align = left class="style5" colspan="2">

      <input type="text" name="Trans_Trailer" size="15" value = "<%= strTransTrailer%>" tabindex="1"></td></tr>
      
       <tr><td class="style5" colspan="3">&nbsp;</td>
  </tr>
<tr>
    <td style="width: 361px" class="style6">  
	<p align="right">  <font face="Arial" size="2">Select Carrier:&nbsp;</font></td>
    <td class="style5" colspan="2"> 
 	<select name="Carrier" style="font-weight: 700" tabindex="3">
 	<option value="" selected>  Select Carrier (Required)</option>
 	<% strsql = "Select Carrier,C_Description, Weight from tblCarrier order by Carrier"
 	   	 Set MyRec4 = Server.CreateObject("ADODB.Recordset")
   	 MyRec4.Open strSQL, Session("ConnectionString")
   	 while not MyRec4.eof %>
   	 <option <% if strCarrier = MyRec4("Carrier") then %> selected <% end if %> value="<%= MyRec4("Carrier") %>"><%= MyRec4("Carrier") %>&nbsp;<%= MyRec4("C_Description") %>&nbsp;<%= MyRec4("Weight") %></option>"

 <%MyRec4.Movenext
 wend
 MyRec4.close %>
     </select></td>
  </tr>
        <tr>
    <td class="style5" colspan="3">&nbsp;</td>

  </tr>
  <tr>
    <td class="style6"  >  
	<p align="right">  <font face="Arial" size="2">Species:&nbsp;</font></td>
    <td class="style5" s colspan="2" >  
	<b><font size="2" face = arial> <%= strSpecies %>
	</font></b>&nbsp; </td>
  </tr>
  <tr>
    <td class="auto-style1"  >  
	Tier Rating: </td>
    <td class="style5" colspan="2">  	<b><font size="2" face = arial> <%= strTier %>

	&nbsp;</td>
  </tr>
    <tr>
    <td class="auto-style1"  >  
	Vendor: </td>
    <td class="style5" colspan="2">  	<b><font size="2" face = arial> <%= strVendor %>

	&nbsp;</td>
  </tr>

  <tr>
    <td class="auto-style1"  >  
	Generator: </td>
    <td class="style5" colspan="2">  	<b><font size="2" face = arial> <%= strGenerator %>, <%= strCity %>&nbsp;<%= strState %>

	&nbsp;</td>
  </tr>


    <tr>
    <td align="right"  class="style6">
	<font face="Arial" size="2">Weight&nbsp;&nbsp; </font></td>

    <td  class="style5" colspan="2"><b><font size="2" face = arial> <%= strTons %>

     
      &nbsp;</td>

  </tr>
    <tr>
    <td class="style5" colspan="3">&nbsp;</td>

  </tr>





       <tr>
          <td  align = right height="27" style="width: 361px" class="style6" >
    <font face="Arial" size="2">Date Transferred:&nbsp;</font></td>
<td align = left height="27" class="style5" colspan="2">

      <input type="text" name="Date_Received" size="15" value = "<%= strDateReceived%>" tabindex="10"></td></tr>
<tr>
    <td class="style5" colspan="3">&nbsp;</td>

  </tr>

  <tr>
    <td style="width: 361px" class="style6">
	<p align="right"><font face="Arial" size="2">Print Movement Order:</font></td>

    <td align = left class="style5"> <font size="1" face="Verdana">  
	<input type="checkbox"  value="ON" name="Print_receipt" checked></font></td>

    <td class="auto-style5"> 
    
	<strong><span class="auto-style6">If SHRED, KBLD, PMX, OF3 or HBX going to 
	OCC, check here</span></strong><span class="auto-style6">:
	 </span>
	 <input type="checkbox"  value="ON" name="OCC" <% if strShredOCC = -1 then %> <% end if %> class="auto-style3"> 
 
	</td>
  </tr>
 <tr>
    <td style="width: 361px" class="style6">
	<p align="right"><font face="Arial" size="2"> </font></td>

    <td align = left class="style5"> <font size="1" face="Verdana">  
	 </font></td>

    <td class="auto-style5"> 
    
	<strong><span class="auto-style6">If USBS, LPSBS, HWM, SWL or MXP going to RF, check here:
	 </span></strong><span class="auto-style6">:
	 </span>
	 <input type="checkbox"  value="ON" name="RF"   class="auto-style3"> 
 
	</td>
  </tr>

  <tr>
    <td class="style5" colspan="3">&nbsp;</td>

  </tr>

  <tr>
    <td style="width: 361px" class="style5">&nbsp;</td>

    <td align = left class="style5" colspan="2"><Input name="Update" type="submit" Value="Submit" ></td>
  </tr>
</table>

</div>

</form>
</body>
<%

 Function SaveData()
 
strTrailer = Request.form("Trans_Trailer")
 
 
strCarrier = Replace(Request.form("Carrier"), "'", "''")

strDateReceived = Request.form("Date_received")

IF  request.form("OCC") = "ON" then
strOCC = -1
else
strOCC = 0
end if

 
IF  request.form("RF") = "ON" then
strRF = -1
else
strRF = 0
end if

strRightnow = Now()
 

	strsql =  "INSERT INTO tblCars ( PMO_Nbr, Date_Received, Trailer,  OID,  Transfer_Trailer_nbr, Transfer_Date, "_
	&" Trans_Carrier, Species, Grade, Location, SAP_NBR, Tons_Received,   Net, Entry_Time, Entry_BID, Entry_Page, "_
	&"  Tier_Rating, Vendor, Generator, Gen_City, Gen_State, PS, PO, Shred_OCC, Shred_RF  ) "_
	&" SELECT  'PJ WH', '" & strDateReceived & "', 'UNKNOWN',  0,'" & strTrailer & "', '" & strDateReceived & "', "_
	&" '" & strCarrier & "', '" & strSpecies & "', 'RF',  'YARD',  "_
	&"   '" & strSAP & "'," & strTons & ",   " & strTons & ", '" & strRightnow & "', '" & Session("EmployeeID") & "', 'Transfer_From_PJ_Release.asp', "_
	&"    '" & strTier & "', '" & strVendor & "', '" & strGenerator & "', '" & strCity & "', '" & strState & "', '" & strRelease & "', '" & strPO & "', "_
	&"  " & strOCC & ", " & strRF & " "
	
	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
	
	
	if strOrgCarrier = "RAIL" then
	
				strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars WHERE PS = '" & strRelease & "'"
   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
	If not MyRec.eof then
	strcount = MyREc("CountofCID")
	end if

	
			if strCount = 3 then

			
			strsql = "Update tblCars set Tally_sheet = 'N' where CID = " & strid
				Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
			end if
			
	else
					strsql = "Update tblCars set Tally_sheet = 'N' where CID = " & strid
				Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
			end if

			' Response.write ("strsql" & strsql)
 
         
      
   
Dim strlast
strsql = "Select Max(CID) as MAXofCID from tblCars"

   	 Set MyConn = Server.CreateObject("ADODB.Recordset")
   	 MyConn.Open strSQL, Session("ConnectionString")
   	 strlast = MyConn.fields("MaxofCID")
   	 MyConn.close

     
     
         
         
 If Request.form("Print_receipt") = "ON" then
 Response.redirect("Transfer_PJreceipt.asp?id=" & strlast)
 else        
  

Response.redirect ("Fiberindex.asp")
end if


End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->