<% Option Explicit %>

<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>CSE Search</TITLE>

<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_Procedure.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->

<%

   Dim objEPS
   Dim   strSortField, strSortType, rstTeam, strTeam, rstESL, rstWA, strWA,  strID, strStatus
   Dim objGeneral, strPNumber, strSource, rstSource, rstAsset,  strSOP
   
   dim   rstEPS
   dim intDirection
   dim intPageNumber
   dim strPageNav, strAsset


   intPageNumber = 1
      set objGeneral = new ASP_CLS_General

   



  if objGeneral.IsSubmit() Then
    Call GetFormData()
  
        Call LoadSearchResults()
  
  else
    intDirection = 0
  end if
Call getdata()
%>

<script language="javascript">
 


  function Search()
  {
    
    document.forms["form1"].elements["Direction"].value = 1;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }

  function NextPage()
  {
    document.forms["form1"].elements["Direction"].value = 5;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }
  function PrevPage()
  {
    document.forms["form1"].elements["Direction"].value = 6;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }
</script>


<style type="text/css">
.auto-style1 {
	border-width: 1px;
	background-color: #E2F3FE;
}
</style>
</head>


<form name="form1" action="Non_CESearch.asp" method="post">
 <input type="hidden" name="Direction" value="1" >
 <input type="hidden" name="ID" value="" >
 <input type="hidden" name=PageNumber value="<%=intPageNumber%>" >

<TABLE borderColor=#C0C0C0 cellSpacing=0 cellPadding=0 width=100%  border=0>  
  <tr><td colspan="5" align = Center height="40" bordercolor="#C0C0C0"  >
	<font face = arial><B>Search for 
	Space not identified as Confined</b></td>


	</tr></table>
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1>  

  <TR>
   <TD bordercolor="#C0C0C0" class="auto-style1"><b><font face="Arial" size="2">&nbsp;</font><font face="Arial">Team&nbsp; </font> 
    <font face="Arial" size="2">&nbsp;&nbsp;&nbsp;&nbsp; </font>  </b>  <font face="Arial">    <select name="Team">
       <option value="">--- All ---</option>
       <%= objGeneral.OptionListAsString(rstTeam, "Team", "Team", strTeam) %>
     </select></font></TD>
	    <TD bordercolor="#C0C0C0" class="auto-style1"><b>
		<font face="Arial" size="2">&nbsp;</font><font face="Arial">Work Area&nbsp; </font>
		<font face="Arial" size="2">&nbsp; </font>
		</b>
		<font face="Arial">  <select name="Workarea">
       <option value="">--- All ---</option>
       <%= objGeneral.OptionListAsString(rstWA, "WorkArea", "WorkArea", strWA) %>
     </select></font></TD>
	    <TD bordercolor="#C0C0C0" class="auto-style1">&nbsp; <b>
		<font face="Arial">Space</font></b><font face="Arial"><b> # </b>&nbsp;<input type="text" name="SOP" size="9" value="<%= strSOP%>" style="width: 157px"></font></TD>
	    <TD bordercolor="#C0C0C0" class="auto-style1">&nbsp;</TD>
	    <TD bordercolor="#C0C0C0" class="auto-style1"><input type="button" onClick="javascript:Search()" value="Search" caption="Search"></TD>


</TR>

  </TABLE></form>


  <% if objGeneral.IsSubmit()  or request.querystring("action") = "return" Then %>
   <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class="tablecolor1" border=1>
    <tr><td colspan="17" bgcolor="white"><font face="Arial" size="2"><b>Search Results</b> - Page <%=intPageNumber%></font></td></tr>
    <tr><td colspan="17" bgcolor="white" align="right"><%=strPageNav%>&nbsp;</td></tr>
      <tr class="tableheader">
	
<td height="16" ><font face="Arial" size="1"><b>&nbsp;<b></font></td>

	<% if len(Request.form("Team")) > 1 then %>
	<% else %>
	
	<td height="16" ><font face="Arial" size="1"><b>Team<b></font></td>
	<% end if %>
	<% if len(Request.form("Workarea")) > 1 then %>
	<% else %>
	<td height="16" ><font face="Arial" size="1"><b>Work Area<b></font></td>
	<% end if %>
	<td height="16" ><font face="Arial" size="1"><b>Space ID</b></font></td>
	<td height="16" ><font face="Arial" size="1"><b>Description</b></font></td>
<td height="16" ><font face="Arial" size="1"><b>Location</b></font></td>
<td height="16" ><font face="Arial" size="1"><b>Functional<br>Location</b></font></td>
<td height="16" ><font face="Arial" size="1"><b>Asset</b></font></td>
<td height="16" ><font face="Arial" size="1"><b>Date</b></font></td>
<td height="16" ><font face="Arial" size="1"><b>Comments</b></font></td>

	
</tr>
      
        
      
  	<% 
      Dim ii
       ii = 0
       while not rstEPS.Eof 
    if ( ii mod 2) = 0 Then %>
       			<tr Bgcolor=#E6EEFF>
		
 	
    <% else %>

       			<tr class=tablecolor2>
			<% end if %>
	
<% if Session("EmployeeID") = "C97338" or Session("EmployeeID") = "B72816" or Session("EmployeeID") = "U14601" then %>
<td align = center><font face = "arial" size = "1">&nbsp;<a href="Summary_Details_NCS_edit.asp?id=<%= rstEPS.fields("SOP_NO")%>">Edit</a>&nbsp;</td> 
<% else %>
<td align = center><font face = "arial" size = "1">&nbsp;</td> 
<% end if %>



	<% if len(Request.form("Team")) > 1 then %>
	<% else %>
<td align = left><font face = "arial" size = "1"><%=rstEPS.fields("TeamName")%>&nbsp;</td> 
   <% end if %>
   	<% if len(Request.form("Workarea")) > 1 then %>
	<% else %>
<td align = left><font face = "arial" size = "1"><%=rstEPS.fields("WorkArea")%>&nbsp;</td> 
<% end if %>
<td align = left><font face = "arial" size = "1"><%=rstEPS.fields("SOP_NO")%>&nbsp;</td>
<td align = left><font face = "arial" size = "1"><%=rstEPS.fields("SDescription")%>&nbsp;</td>
<td align = left><font face = "arial" size = "1"><%=rstEPS.fields("Location")%>&nbsp;</td>	
<td align = left><font face = "arial" size = "1"><%=rstEPS.fields("Functional_Location")%>&nbsp;</td>	
<td align = left><font face = "arial" size = "1"><%=rstEPS.fields("AssignedAsset")%>&nbsp;</td>	

<td align = left><font face = "arial" size = "1"><%=rstEPS.fields("Sdate")%>&nbsp;</td>	
<td align = left><font face = "arial" size = "1"><%=rstEPS.fields("Comment")%>&nbsp;</td>	

  </tr>
    <%
       ii = ii + 1
       rstEPS.MoveNext
     Wend
    %>
   </table>
	<table>    <tr><td colspan="7" bgcolor="white" align="right"><%=strPageNav%>&nbsp;</td></tr></table>

  <% end if %>    
      
 <%

    Function GetData()
        set objEPS = new ASP_CLS_ProcedureESL
        set rstTeam = objEPS.ReadTeamList()
 	set rstWA = objEPS.ReadWorkArea()


    End Function
    Function GetFormData()
      intDirection = cint(Request.Form("Direction"))
  


     
      intPageNumber = cint(Request.Form("PageNumber"))
      strTeam = Request.form("Team")
      strWA = Request.form("Workarea")
 strSOP = Request.form("SOP")
  

    End Function

    Function LoadSearchResults()
      Dim objEPSSearch


      if intDirection =1 Then
         intPageNumber = 1
      end if

      if intDirection = 5 Then
         intPageNumber = intPageNumber + 1
      end if
      if intDirection = 6 Then
         intPageNumber = intPageNumber - 1
      end if

Session("intDirection") = intDirection
Session("intPageNumber") = intPageNumber


   
      set objEPSSearch = new ASP_Cls_ProcedureESL
      


set rstEPS = objEPSSearch.Non_CESEarch(intPageNumber,  strTeam, strWA, strSOP)

    
     if ( not rstEPS.Eof) Then
      if ( intPageNumber < rstEPS.fields("TotalPage").value ) Then
        strPageNav = "<a href=javascript:NextPage()><b>Next Page</b></a>"
      end if
      if ( intPageNumber > 1 ) Then
         strPageNav = "<a href=javascript:PrevPage()><b>Previous Page</b></a>&nbsp;&nbsp;&nbsp;&nbsp;" & strPageNav
      end if
     end if
    End Function %><!--#include file="footer.inc"-->