																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Recovered Paper Yard Inventory Report</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->



<% Dim MyRec, strsql, strDays, strDate, gSpecies, gCount, gTCount, strsql3, MyRec3

strdate = formatdatetime(now(),2)
gcount = 0
gSpecies = ""

strsql = "SELECT tblCars.*,  [date_received] AS Sort_Date FROM tblCars WHERE (((tblCars.Date_received) Is Not Null) AND ((tblCars.Location)='Yard') AND ((tblCars.Trailer) Is Not Null)) ORDER BY tblCars.Species, Sort_Date "   
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
			<% Set objMail = Server.CreateObject("CDO.Message")		
			objMail.From = "<EMAIL>"
			objMail.To = "<EMAIL>"	
			objMail.Subject = "Yard Report"
			objMail.HTMLBody = "<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">	<td  ><font face="Arial" size="2"><b>Species</b></font></td>
		<td align = center  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td ><font face="Arial" size="2"><b>&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; Trailer</b></font></td>
		<td ><font face="Arial" size="2"><b>Carrier</b></font></td>
	<td ><font face="Arial" size="2"><b>REC Nbr</b></font></td>
	<td ><font face="Arial" size="2"><b>PO</b></font></td>
<td width="69" ><font face="Arial" size="2"><b>Vendor</b></font></td>
<td align = left>
<font face="Arial" size="2"><b>Audit</b></font></td>

<td align=center><font face="Arial" size="1"><b>To Date<br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="1"><b>Days <br>in Yard</b></font></td>

<td align="center" ><font face="Arial" size="1"><b>Other</b></font></td>
<td align = center ><font face="Arial" size="2"><b>Checkoff</b></font></td>
	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    
    <% if  ucase(MyRec.fields("Species")) = ucase(gSpecies) or gcount = 0 then %>


        
        <% if isnull(MyRec.fields("Transfer_date")) then %>
        <td>       <font size="2" face="Arial">       <%= MyRec.fields("Species")%></font></td>
	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td  > <font size="2" face="Arial"> &nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;<%= MyRec.fields("Trailer")%></font></td>
	<td  >   <font size="2" face="Arial">        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
	 <td  >  <font size="2" face="Arial">        <%= MyRec.fields("REC_number")%>&nbsp;</font></td>
        <% else %>
        	<td>       <font size="2" face="Arial">       <%= MyRec.fields("Species")%>&nbsp;&nbsp:SHUTTLE</font></td>
		<td align = center width="69" ><font size="2" face="Arial"><%= MyRec.fields("Transfer_Date")%></font></td>
	
			<td  > <font size="2" face="Arial">&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; <%= MyRec.fields("Transfer_trailer_nbr")%></font></td>
		<td  >   <font size="2" face="Arial">        <%= MyRec.fields("Trans_Carrier")%>&nbsp;</font></td>
		   <td  >  <font size="2" face="Arial">        <%= MyRec.fields("PMO_nbr")%>&nbsp;</font></td>
		<% end if %>
		

		
 
   <td  >  <font size="2" face="Arial">        <%= MyRec.fields("PO")%></font></td>
    <td  >  <font size="2" face="Arial">        <%= MyRec.fields("Vendor")%></font></td>
      <td  >  <font size="2" face="Arial"> 
      <% if MyRec.fields("Weigh_required") = "W" and MyRec.fields("Audit_tons") > 0  Then %>
      WC
       <% elseif (MyRec.fields("Weigh_required") = "W" and isnull(MyRec.fields("Audit_tons"))) or ( MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then %>W
        <% else %> &nbsp; <% end if %></td>
          
         <% if isnull(MyRec.fields("Transfer_date")) then
             strDays = round(Now()- cdate(MyRec.fields("Date_received")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

             else
             strDays = round(Now()- cdate(MyRec.fields("Transfer_date")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Trans_Carrier") & "' "
             end if  
          
 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
       If left(MyRec("Trailer"),4) = "GACX" then
    strFee = 0
    end if

    if strDays  > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(strDays) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"><%= strDays%></font></td>	
	
	<td><font size="2" face="Arial"><%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>
	<td ALIGN = CENTER><font size="2" face="Arial"><input type="checkbox" name="C1" value="ON">&nbsp;</font></td>
	</tr>

 <%    gcount = gcount + 1
       gTcount = gTcount + 1
 		gSpecies = ucase(MyRec.fields("Species"))
       ii = ii + 1
       MyRec.MoveNext
    
    %>
<% else %>
<td><font face = arial size = 2><b></b>Total:&nbsp;<%= gcount%></b></font></td>
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
   <td>&nbsp;</td>
   <td>&nbsp;</td>
       <td width="69">&nbsp;</td>
      <td>&nbsp;</td>
<td>&nbsp;</td>	
<td>&nbsp;</td>


	</tr>
	<TR><td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
   <td>&nbsp;</td>
   <td>&nbsp;</td>
       <td width="69">&nbsp;</td>
      <td>&nbsp;</td>
<td>&nbsp;</td>	
<td>&nbsp;</td>

	</tr>
	
	  <tr class=tablecolor2>
        
        <% if isnull(MyRec.fields("Transfer_date")) then %>
        <td>        <font size="2" face="Arial">         <%= MyRec.fields("Species")%></font></td>
        		<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
        		<td  > <font size="2" face="Arial">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <%= MyRec.fields("Trailer")%></font></td>
        		<td  >    <font size="2" face="Arial">        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
        		  <td> <font size="2" face="Arial">        <%= MyRec.fields("REC_number")%></font></td>
        <% else %>
        	<td>       <font size="2" face="Arial">       <%= MyRec.fields("Species")%>&nbsp;&nbsp:SHUTTLE</font></td>
		<td width="69"  ><font size="2" face="Arial"><%= MyRec.fields("Transfer_Date")%></font></td>
			<td  > <font size="2" face="Arial">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <%= MyRec.fields("Transfer_Trailer_nbr")%></font></td>
				<td  >  <font size="2" face="Arial">   <%= MyRec.fields("Trans_Carrier")%>&nbsp;</font></td>
				<td> <font size="2" face="Arial"><%= MyRec.fields("PMO_nbr")%>&nbsp;</font></td>
		


		<% end if %>
		
<td>

     
        <font size="2" face="Arial">
        <%= MyRec.fields("PO")%></font></td>
        <td  >
        <font size="2" face="Arial">
        <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> 
        
              <% if MyRec.fields("Weigh_required") = "W" and MyRec.fields("Audit_tons") > 0  Then %>
      WC
       <% elseif (MyRec.fields("Weigh_required") = "W" and isnull(MyRec.fields("Audit_tons"))) or ( MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then %>W
        <% else %> &nbsp; <% end if %></td>

         <% if isnull(MyRec.fields("Transfer_date")) then
             strDays = round(Now()- cdate(MyRec.fields("Date_received")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

             else
             strDays = round(Now()- cdate(MyRec.fields("Transfer_date")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Trans_Carrier") & "' "
             end if  
          
 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
       If left(MyRec("Trailer"),4) = "GACX" then
    strFee = 0
    end if

    if strDays  > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(strDays) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"><%= strDays%></font></td>

	

	<td><font size="2" face="Arial"><%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>
	<td ALIGN = CENTER><font size="2" face="Arial"><input type="checkbox" name="C1" value="ON">&nbsp;</font></td>
	</tr>
		
	<% gcount = 1
	 gTcount = gTcount + 1
	 gSpecies = ucase(MyRec.fields("Species"))
       ii = ii + 1
       MyRec.MoveNext
    
	 end if %>

<%  Wend %>
	  <tr class=tablecolor2>
<td><font face = arial size = 2><b></b>Total:&nbsp;<%= gcount%></b></font></td>
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
   <td>&nbsp;</td>
   <td>&nbsp;</td>
       <td width="69">&nbsp;</td>
      <td>&nbsp;</td>
<td>&nbsp;</td>	
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
	</tr>
	<TR><td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
   <td>&nbsp;</td>
   <td>&nbsp;</td>
       <td width="69">&nbsp;</td>
      <td>&nbsp;</td>
<td>&nbsp;</td>	


	</tr>
<TR><td><font face = arial size = 2><b></b>Grand Total:&nbsp;<%= gTcount%></b></font></td>

<td>&nbsp;</td>
<td>&nbsp;</td>
   <td>&nbsp;</td>
   <td>&nbsp;</td>
       <td>&nbsp;</td>
      <td width="69">&nbsp;</td>
<td>&nbsp;</td>	
<td>&nbsp;</td>

	</tr>

</table>"
		
			

 <% 		objMail.Send
			Set objMail = nothing %>