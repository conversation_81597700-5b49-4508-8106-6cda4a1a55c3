																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Unclassified Broke</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->




<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate

strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -7, strtdate)
strRdate = dateadd("d", -90, strdate)

    strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "' and User_type = 'A'"


    Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL3, Session("ConnectionString")
	If not MyRec2.eof then
	stradmin = "OK"
	else
	stradmin = ""
	end if
	MyRec2.close

strsql = "SELECT tblCars.* FROM tblCars WHERE (Grade = 'BROKE') AND Trailer Is Not Null AND (Location='yard' or Location = 'YARD') and Release_nbr Is Not Null order by Release_nbr"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<style type="text/css">
.style1 {
	text-align: center;
}
</style>
</head>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Broke Trailers to be Classified</b></font></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1>  
	 <tr class="tableheader">
<td>&nbsp;</td>
			<td  > <p align="center">       <font face="Arial" size="2">Receipt<br> Number</font></td>
		<td  > <p align="center">       <font face="Arial" size="2">Release<br> Number</font></td>
		<td  > <p align="center">       <font face="Arial" size="2">SAP #</font></td>	
		<td  ><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td  ><font face="Arial" size="2"><b>Carrier</b></font></td>    
		<td  ><font face="Arial" size="2">Vendor</font></td>
		<td  >       <font face="Arial" size="2">PO Number</font></td>
		<td  >
        <p align="center">
        <font face="Arial" size="2">Tons<br> Received</font></td>
		<td  >
        <font face="Arial" size="2">Date<br> Received</font></td>
		<td  ><font face="Arial" size="2">Broke<br> Description</font></td>
		<td  >  <font face="Arial" size="2">Classified</font></td>
			<td class="style1"  >  <font face="Arial" size="2">Originally<br>Classified<br> 
			Right?</font></td>
		<td  >
        <font face="Arial" size="2">Generator</font></td>
		<td  >
        <font face="Arial" size="2">Generator<br> City</font></td>
		<td  >
        <font face="Arial" size="2">Gen<br> State</font></td>
		<td  >
        <font size="2" face="Arial">Other</font></td>
	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>

    <td> <font size="2" face="Arial"><a href="EditUnclassifiedBroke.asp?id=<%= MyRec.fields("CID") %>">Edit</a></td>
	<td align = center> <font size="2" face="Arial"><%= MyRec.fields("CID")%></td>
	<td  ><font size="2" face="Arial"><%= MyRec.fields("Release_Nbr")%></font></td>
	<td  > <font size="2" face="Arial"><%= MyRec.fields("SAP_nbr")%></font></td>
	<td  > <font size="2" face="Arial"><b><%= MyRec.fields("Trailer")%></font></b></td>
			<td  ><font size="2" face="Arial"><b><%= MyRec.fields("Carrier")%>&nbsp;</font></b></td>
		<td><font size="2" face="Arial"><%= MyRec.fields("Vendor")%></font></td>
		<td  >        <font size="2" face="Arial">        <%= MyRec.fields("PO")%></font></td>	
		<td align = right <% if MyRec.fields("Tons_received") = 21  or MyRec.fields("Weigh_required") = "W" then %> bgcolor = pink <% end if %>  >
				 <font size="2" face="Arial">        <%= MyRec.fields("Tons_received")%>&nbsp;<%= MyRec.fields("Weigh_required") %></font></td>
		<td  >		 <font size="2" face="Arial">        <%= MyRec.fields("Date_Received")%></font></td>
       <td  >		 <font size="2" face="Arial">        <%= MyRec.fields("Broke_description")%></font></td>
       <% if MyRec.fields("Classified") = "YES" then %>
       <td  >		 <font size="2" face="Arial">YES</font></td>
       <% else %>
       <td  >		 <font size="2" face="Arial">NO</font></td>
       <% end if %>   
       
       <td  ><font size="2" face="Arial"> <%= MyRec.fields("Originally_Correct")%>&nbsp;</font></td>          
       <td  ><font size="2" face="Arial"><%= MyRec.fields("Generator")%></font></td>
		<td  >	 <font size="2" face="Arial"><%= MyRec.fields("Gen_City")%></font></td>
		<td  > <font size="2" face="Arial"> <%= MyRec.fields("Gen_State")%></font></td>
		<td  >	 <font size="2" face="Arial"> <%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>
	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>


<!--#include file="Fiberfooter.inc"-->