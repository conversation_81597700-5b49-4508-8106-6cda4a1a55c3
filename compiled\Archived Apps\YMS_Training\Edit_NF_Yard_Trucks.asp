																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Edit Non-Fiber Trucks in Yard</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->




<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate

strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -7, strtdate)

strsql = "SELECT tblCars.* FROM tblCars WHERE (((tblCars.Grade)='NF')  AND ((tblCars.Location)='yard')) order by Trailer"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Edit Non-Fiber Trailers in Yard</b></font></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1>  
	 <tr class="tableheader">
<td>&nbsp;</td>
	

	
		<td  ><font face="Arial" size="1"><b>Trailer</b></font></td>
		<td  ><font face="Arial" size="1"><b>Carrier</b></font></td>
		<td  ><font face="Arial" size="1">Commodity</font></td>
    
		<td  ><font face="Arial" size="1">Vendor</font></td>
		<td  >       <font face="Arial" size="1">PO Number</font></td>
		<td align = center>       <font face="Arial" size="1">REC Number</font></td>
		<td  >       <p align="center">       <font face="Arial" size="1">Quantity</font></td>
		<td  >       <p align="center">        <font face="Arial" size="1">UOM</font></td>

		<td align = center  >        <font face="Arial" size="1">Date<br> Received</font></td>
		<td  align = center >        <font face="Arial" size="1">Product<br> System</font></td>
		<td  align = center>        <font face="Arial" size="1">Asset<br> Team</font></td>
		<td align = center >       <font size="1" face="Arial">Other</font></td>
	<td  >        <font size="1" face="Arial">Delete<br>Receipt</font></td>

	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td> <font size="1" face="Arial"><a href="EditNF_Generic_receipt.asp?id=<%= MyRec.fields("CID") %>">Edit</a></td>



	<td  > <font size="1" face="Arial"><b><%= MyRec.fields("Trailer")%></font></b></td>
			<td  ><font size="1" face="Arial"><b><%= MyRec.fields("Carrier")%>&nbsp;</font></b></td>


		<td  ><font face="Arial" size="1"> <%= MyRec.fields("Species")%></font></td>
		<td><font size="1" face="Arial"><%= MyRec.fields("Vendor")%></font></td>
		<td  >  <font size="1" face="Arial">  <%= MyRec.fields("PO")%></font></td>
	<td  >  <font size="1" face="Arial">   <%= MyRec.fields("REC_Number")%></font></td>

		<td align = right  > <font size="1" face="Arial">  <%= MyRec.fields("Tons_received")%></font></td>
				<td align = center  > <font size="1" face="Arial">  <%= MyRec.fields("UOM")%></font></td>
		<td  ><font size="1" face="Arial">   <%= MyRec.fields("Date_Received")%></font></td>
       <td  >	<font size="1" face="Arial">  <%= MyRec.fields("PS")%></font></td>
		<td  >	<font size="1" face="Arial">  <%= MyRec.fields("Asset_team")%></font></td>

		<td  >	 <font size="1" face="Arial">        <%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>
<td align = center >	 <font size="1" face="Arial"><a href="ReceiptDelete.asp?id=<%= Myrec.fields("CID")%>"> Delete</a></font></td>
	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>


<!--#include file="Fiberfooter.inc"-->