<%
'Response.Write Request.Form & "<BR>"
%>
<html xmlns:m="http://schemas.microsoft.com/office/2004/12/omml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Edit STO Fiber Trailer Receipt</title>
<style type="text/css">
.style11 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	background-color: #FFFFEA;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style12 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-weight: bold;
	background-color: #FFFFEA;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style28 {
	color: #9F112E;
}
.style29 {
	font-weight: bold;
	font-family: Arial, Helvetica, sans-serif;
}
.style30 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	text-align: right;
		font-family: Arial, Helvetica, sans-serif;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style31 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial, Helvetica, sans-serif;
		font-size: x-small;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style32 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-weight: bold;
		text-align: center;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style34 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style35 {
	font-weight: bold;
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style36 {
	border: 1px solid #000000;
	border-collapse: collapse;
	}
.style38 {
	font-weight: bold;
	font-family: Arial;
	font-size: x-small;
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style39 {
	font-size: x-small;
	font-family: Arial, Helvetica, sans-serif;
}
.auto-style1 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-weight: bold;
	text-align: center;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #EAF1FF;
}
.auto-style2 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #EAF1FF;
}
.auto-style3 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	text-align: right;
	font-family: Arial, Helvetica, sans-serif;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #EAF1FF;
}
.auto-style4 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #EAF1FF;
}
.auto-style5 {
	font-weight: bold;
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #EAF1FF;
}
.auto-style6 {
	font-weight: bold;
	font-family: Arial;
	font-size: x-small;
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #EAF1FF;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID, strLoad, MyConn, objMOC, rstFiber, strLocation, strSAP, rstTrailer , strTrailerWeight , strTractor   
    Dim strTrailer, strCarrier, strSQL3, MyConn3, strVID, strUOM, strPounds, strRail, strDate_shipped
    Dim strLoadNbr, strTrailerTID, strBales, stralert
    Dim strRECNbr
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strGenerator
    Dim strGenCity
    Dim strGenState, gAuditStatus
    Dim strOther, strR,  gSpecies, gPO, gSAP_Nbr, gVendor, gOID, strNet, gWeight_required, gTrailerStatus, strKCWeighed
    Dim MyRec5, strsql5, strCarID

    strLoad  = Trim(Request.QueryString("id")) 
    strpage = request.querystring("p")
	
	Dim strCID

	strCID = Request("cid")

 	set objGeneral = new ASP_CLS_General



if objGeneral.IsSubmit() Then
   If request.form("Cancel") = "ON" then
      Response.redirect ("SelectSTO.asp")
   Else
     If Session("EmployeeID") = "F00390" Then
        Session("EmployeeID") = "B38763"
	 End If
   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"
   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")
	 If not Myrec.eof then
	    MyRec.close
	    strLoad  = Trim(Request.QueryString("id")) 
 	    strTrailer = Request.form("Trailer")
	    strOther = Replace(Request.form("Other_Comments"), "'", "''") 
	    strDateReceived = Request.form("Date_received")
	    strSAPWeight = request.form("SAP_Weight")
	
	    strCarrier = Request.form("Carrier")
	    strLocation = Request.form("Location")

	    strGenerator = Replace(Request.form("Generator"), "'", "''")
	
    	if len(Request.form("Tons_Received")) > 1 then
	       strPounds = Request.form("Tons_Received")
	       strTonsReceived = round(strPounds/2000,3)
	       strTrailerTID = 0
	
	    else
	       strTrailerTID = Request.form("Trailer_option")
		   if strTrailerTID <> 0 then
   		      strSQL3 = "Select weight from tblTrailerOptions where TID = " & strTrailerTID

   			  Set MyConn3 = Server.CreateObject("ADODB.Recordset")
   			  MyConn3.Open strSQL3, Session("ConnectionString")
   	 		  If not MyConn3.eof then
   	 		 	 strTrailerWeight = MyConn3.fields("Weight")
   	 			 strGrossWeight = Request.form("Gross")
   	 			 if len(Request.form("Gross")) > 0 then 
   	 				strTonsReceived = round((Request.form("Gross") - strTrailerweight)/2000,3)
   	 				strPounds = round((Request.form("Gross") - strTrailerweight),3)
   	 	   	 	 else
   	 				strTonsReceived = 0
   	 			 end if

   		 	  end if
   		      MyConn3.close
   		   end If 'if strTrailerTID <> 0 then
	    end If  ' if len(Request.form("Tons_Received")) > 1 then

        strNet = strTonsreceived
	    If isnull(strTrailer) or strTrailer = ""  or isnull(strCarrier) or strCarrier = ""  or len(strGenerator)<2 or isnull(strTonsReceived) or strTonsReceived = ""  then
	       Session("Trailer") = strTrailer
	       Session("Carrier") = strCarrier
	       Session("Generator") = strGenerator
	       Session("TrailerTID") = strTrailerTID
	       Session("GrossWeight") = strGrossWeight
	       Session("TonsReceived") = strTonsReceived
	       Session("Pounds") = strPounds
	       Session("SAPWeight") = strSAPWeight
	       Response.redirect("EnterSTOReceipt.asp?id=" & strLoad & "&n=T")
        else
	       Call SaveData() 
		end if	
     else
        Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to save this information.</font></br>")
        MyRec.close
     end If  'If not Myrec.eof then
 End If 'If request.form("Cancel") = "ON" then
 strDateReceived = formatdatetime(Now(),2)
end if
%>



<body>


<% if Request.querystring("n") = "T" or request.querystring("n") = "T1" then 
strTrailer = Session("Trailer")
strCarrier = Session("Carrier")
strTrailerTID = Session("TrailerTID")
strGrossWeight = Session("GrossWeight")

strGenerator = Session("Generator")
strTonsReceived = Session("TonsReceived")
strPounds = formatnumber(Session("Pounds"),0)

 %>
<p align = center><font face = arial size = 4 color="teal"><b>

<font face = arial size = 3 color = red>
<span class="style28">

<% if isnull(Session("Trailer")) or len(Session("Trailer"))< 1 then %>
You must enter a Trailer number.<br>
<% end if %>
<% if strCarrier = "" then %>
You must enter a Carrier<br>
<% end if %>
<% if len(strGenerator) < 2 then %>
You must enter the KC Site Load is Shipped From<br>
<% end if %>
<% if len(strSapWeight) < 2 then %>
You must enter the Weight from the Shipping Ticket<br>
<% end if %>


<% if request.querystring("n") = "T1" then
	Response.write("<br><font face=arial size=3 color=red><br>Receipt Number " & strPostCID & " already has this load number.  It could be you clicked the Submit button twice.</font><br>")
	end if
	
end if %><br>

<%strsql =  "select Trailer_weight, Trailer_TID, Tractor_weight,  Carrier, Species, Grade, SAP_Nbr, Vendor, "_
	&" Date_received, STO_Number, Location_unloaded, Location,  Generator,  Trailer, Other_Comments, Tons_Received, Net, Tons, Gross_weight, "_
	&" Tare_weight,   Entry_Time, Entry_BID, Entry_Page, Status, PO, BOL, Trailer_TID from tblCars where CID = " & strCID
	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
	 If not Myrec.eof Then
		strCarrier = MyRec("Carrier")
		strSpecies = MyRec("Species")
		strGrade = MyRec("Grade")
		strSAP_Nbr = MyRec("SAP_Nbr")
		strVendor = MyRec("Vendor")
		strDate_received = MyRec("Date_received")
		strSTO_Number = MyRec("STO_Number")
		strLocation_unloaded = MyRec("Location_unloaded")
		strLocation = MyRec("Location")
		strGenerator = MyRec("Generator")
		strTrailer = MyRec("Trailer")
		strOther_Comments = MyRec("Other_Comments")
		strTons_Received = MyRec("Tons_Received")
		strNet = MyRec("Net")
		strEntry_Time = MyRec("Entry_Time")
		strEntry_BID = MyRec("Entry_BID")
		strEntry_Page = MyRec("Entry_Page")
		strStatus = MyRec("Status")
		strPO = MyRec("PO")
		strBOL = MyRec("BOL")
		strTrailerTID = MyRec("Trailer_TID")

		strGrossWeight = MyRec("Gross_weight")
	 Else
	    strTrailerTID = ""
     End If
	 MyRec.close
	 Set MyRec = Nothing
%>
<table width = 100%><tr><td width = 33% style="height: 24px">
<font face="Arial" size="2"><b>Species: <%= strSpecies%>&nbsp;&nbsp; 

</td><td align = center style="height: 24px; width: 50%;"><b><font face="Arial" size="4">
Edit STO Fiber Trailer Receipt</font> </b></td>
	<td align = right width = 33% style="height: 24px"><b><font face = arial size = 2><a href="SelectSTO.asp">RETURN</a></font></b></td></tr></table>


<form name="form1" action="EditSTOReceipt.asp?cid=<%=strCID%>" method="post">
<input type="hidden" name="Species" value="<%= strSpecies %>">
<input type="hidden" name="SAP_Load" value="<%= strSap %>">
<input type="hidden" name="PO" value="<%= strPO %>">
<input type="hidden" name="BOL" value="<%= strBOL %>">
<div align="center">
<table cellspacing="0" bgcolor="#FFFFEA" style="width: 85%;" cellpadding="0" bordercolorlight="#C0C0C0" class="style36">
  <tr>
    <td  align = right style="height: 42px; width: 25%;" class="auto-style5" >
   <span class="style39">Material (SAP #)</span>:&nbsp; </td>
<td  align = left colspan="2" style="height: 42px" class="auto-style4">

		<font face="Arial" size="2"> <% if request.querystring("p") = "u" then %>
		<input type="text" name="SAP" value="" style="width: 135px">
		<% else %>
	<select name="SAP" style="font-weight: 700; width: 132px;" size="1" tabindex="1">
 	<option selected value="">  Select</option>
      <% strsql = "Select tblBrokeSAP.* from tblBrokeSAP where category = 'FIBER'"
      
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    while not MyRec.eof

       %>
        <option <% if strSAP_Nbr = MyRec("SAP") then %> selected <% end if %> ><%= MyRec("SAP") %></option>
        <% MyRec.movenext
        wend
        MyRec.close %>
  
     </select><% end if %>&nbsp; (Required)</font></td>
<td  align = left width="29%" style="height: 42px" class="auto-style5">&nbsp;</td></tr>
  <tr>
    <td  align = right style="height: 32px; width: 25%;" class="auto-style5" >
   <font face="Arial" size="2">Trailer:&nbsp;</font></td>
<td  align = left colspan="3" style="height: 32px" class="auto-style4">

      <font face="Arial">

      <input name="Trailer" size="15" value = "<%= strTrailer%>" style="font-weight: 700" tabindex="1"><b>
		</b>&nbsp;&nbsp;</font></td>
	</tr>
  <tr>

      <td align = right style="height: 33px; width: 25%;" class="auto-style5">
	<font face="Arial" size="2">Select Carrier:</font></td>
<td  align = left colspan="3" style="height: 33px" class="auto-style4">

      <font face="Arial">   
		<select name="Carrier" style="font-weight: 700">
 	<option value="" selected>  Select Carrier (Required)</option>
 	<% strsql = "Select Carrier,C_Description, Weight from tblCarrier order by Carrier"
 	   	 Set MyRec4 = Server.CreateObject("ADODB.Recordset")
   	 MyRec4.Open strSQL, Session("ConnectionString")
   	 while not MyRec4.eof %>
   	 <option <% if strCarrier = MyRec4("Carrier") then %> selected <% end if %> value="<%= MyRec4("Carrier") %>"><%= MyRec4("Carrier") %>&nbsp;<%= MyRec4("C_Description") %>&nbsp;<%= MyRec4("Weight") %></option>"

 <%MyRec4.Movenext
 wend
 MyRec4.close %>
     </select></font><font face="Arial" size="2"><b>    

      &nbsp;</td>
</tr>



        <tr>  
			<td align = right class="auto-style4" style="height: 40px; width: 25%;"> <b>
			<font face="Arial" size="2">Combined Trailer/Tractor</font></b><font face="Arial" size="2"><b> Weight:&nbsp;</b></font></td>
<td align = left colspan="3" class="auto-style4" style="height: 40px">    <font face="Arial"> 
<select name="Trailer_option" style="font-weight: 700" size="1" tabindex="3">
 	<option <%If strTrailerTID = "" Then %> selected <%End If%> value=0>  Select Trailer</option>
 	<% strsql2 = "SELECT  Trailer_Description, Audit_Weight, Trailer_Description + '-' + str([Audit_weight]) as toption, TID FROM tblTrailerOptions ORDER BY Trailer_description"
 	 	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL2, Session("ConnectionString")
while not MyRec.eof
%>
<option value=<%= MyRec("TID") %> <%If strTrailerTID = CStr(MyRec("TID")) Then %> selected <%End If%>><%= MyRec("Toption") %> </option>
 	
  <% MyRec.movenext
  wend
  MyRec.close %> 
     </select></td>
</tr>


<tr>
	<td class="auto-style5" style="height: 38px; width: 25%;">
	<p align="right"><font face="Arial" size="2">Gross Weight:</font></td>
    <td width="21%" class="auto-style4" style="height: 38px">    <font face="Arial">  
	<input name="Gross" size="15" value="<%= strGrossWeight%>" tabindex="4"></td>
    <td class="auto-style5" style="height: 38px">    
	<p align="right"> <font face="Arial" size="2">OR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	Pounds Received:</font></td>
    <td width="29%" class="auto-style4" style="height: 38px">    <font face="Arial">  
	<input name="Tons_Received" size="15" value = "<%= strTons_Received%>" style="float: left" tabindex="5"></td>
  </tr>

 

       <tr>
		<td  align = right style="height: 34px; width: 25%;" class="auto-style5" >
		<span class="style39">Shipping Ticket Weight</span>:&nbsp; </td>
<td align = left colspan="3" style="height: 34px" class="auto-style5"> 

<font face = arial size = 3 color = red>
<span class="style28">

<font face="Arial">  
	<input name="SAP_Weight" size="15" value="<%= strSAPWeight%>" tabindex="6"></td>
</tr>

       <tr>
		<td  align = right style="height: 14px; width: 25%;" class="auto-style6" >
		S<font size="2">TO Delivery Number: </font></td>
<td align = left colspan="3" style="height: 14px" class="auto-style5"> 

<font face = arial size = 3 color = red>
<span class="style28">

<font face="Arial">  
	<input name="Load" size="15" value="<%= strLoad %>" tabindex="6"></td>
</tr>


       <tr>
		<td  align = right style="height: 34px; width: 25%;" class="auto-style5" ><font face="Arial" size="2">Date Received:&nbsp;</font></td>
<td align = left colspan="3" style="height: 34px" class="auto-style5"> 

<font face = arial size = 3 color = red>
<span class="style28">

<font face="Arial"> 
<input name="Date_Received" size="15" value = "<%= strDateReceived%>" style="font-weight: 700" tabindex="7"></font></td>
</tr>
              <tr>
          <td  align = right style="width: 25%;" class="auto-style5" >
   <font face="Arial" size="2">Site Shipped From:&nbsp;</font></td>
<td align = left colspan="3" class="auto-style4">
      <font face="Arial">
      <input name="Generator" size="25" value = "<%= strGenerator %>" style="font-weight: 700" tabindex="8"></font></TD></tr>
         <tr>
          <td  align = right style="height: 37px; width: 25%;" class="auto-style5" >
  <font face="Arial" size="2">Comments:&nbsp;</font></td >
   <td align = left colspan="3" style="height: 37px" class="auto-style4">   <font face="Arial">   
	<input name="Other_Comments" size="25" value = "<%= strOther%>" style="font-weight: 700; width: 364px;" tabindex="9">&nbsp;&nbsp;&nbsp;</td></tr>
<tr>
    <td class="auto-style3" style="height: 26px; width: 25%;">
	<font face = arial size = 3>
	<span class="style29">

	<font size="2">Location:&nbsp;</font></td>

    <td colspan="3" class="auto-style2" style="height: 26px">YARD</td>
  </tr>

    
  <tr>
    <td width="17%" colspan="4" class="auto-style1">

<font face = arial size = 3 color = red>
<span class="style28">

	<font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></td>

  </tr>

    </table>

</div>

</form>

<%  

Function SaveData()

strDateReceived = formatdatetime(Now(),2)

	
	Dim strRightNow, strAud
	strRightnow = Now()
	
	
   	
if len(request.form("Tons_Received")) > 0 then
   strPounds = Request.form("Tons_received")
   if strPounds > 0 then
       if strPounds = 42000 then
		  strKCWeighed = "Yes"
	   else
		  strKCWeighed = ""
	   end if 
	   strTonsReceived = round(strPounds/2000,3)
	   strTrailerTID = 0
	   strTrailerweight = 0
	   strGrossWeight = 0
	   strTareWeight = 0
       strNet = strTonsReceived
   end if 
else
   strTrailerTID = request.form("Trailer_option")
   strSQL3 = "Select audit_weight from tblTrailerOptions where TID = " & strTrailerTID
   Set MyConn3 = Server.CreateObject("ADODB.Recordset")
   MyConn3.Open strSQL3, Session("ConnectionString")
   strTrailerWeight = MyConn3.fields("Audit_Weight")
   MyConn3.close
   strTonsReceived = round((Request.form("Gross") - strTrailerweight)/2000,3)
   strPounds = round((Request.form("Gross") - strTrailerweight),3)
   strTareWeight =  strTrailerweight
   strGrossWeight = Request.form("Gross")
   strNet = strTonsReceived
end if
	
If strTrailerweight > 100 then
	strTrailerweight = (strTrailerweight - 18780) ' subtract off the weight of the conventional tractor
	end if 
	
	strSAPWeight = REquest.form("SAP_Weight")
 
	strSpecies = request.form("Species")
	If request.querystring("p") = "u" then
	strSpecies = "UNRESOLVED"
	end if

	strSapWeight = request.form("Sap_Weight")
	
	if len(strSAPWeight) > 0 then
	strTonsReceived = strSapWeight
	end if
	
		strLoad = request.form("Load")
	if len(strLoad) > 0 then
	'do nothing
	else
	strLoad = 0
	end if

    If Len(strTareWeight) = 0 Then
       strTareWeight = "NULL"
	End If
	If Len(strSAPWEight) = 0 Then
	   strSAPWeight = "NULL"
	End If
	
	If Len(strTonsReceived) = 0 Then
	   strTonsReceived = "NULL"
	End If

	If Len(strGrossWeight) = 0 Then
       strGrossWeight = "NULL"
	End If
	
	strsql =  "UPDATE tblCars SET Trailer_weight = " & strTrailerWeight & ", Trailer_TID = " & strTrailerTID & ", Tractor_weight = 18780, " _
	& " Carrier = " & strCarrier & "', Species = '" & strSpecies & "', Grade = 'RF', SAP_Nbr = '" & Request.form("SAP") & "', Vendor = '" & strGenerator & "', "_
	& " Date_received = '" & strDateReceived & "', STO_Number = '" & strLoad & "', Location_unloaded = 'RF', Location = 'YARD', " _
	& " Generator = '" & strGenerator & "',  Trailer = '" & strTrailer & "', Other_Comments = '" & strOther & "', "_
	& " Tons_Received = " & strSAPWEight & ", Net = " & strSAPWEight & ", Tons = " & strTonsReceived & ", Gross_weight = " & strGrossWeight & ", "_
	& " Tare_weight = " & strTareWeight & ",   Entry_Time = '" & strRightNow & "', Entry_BID = '" & Session("EmployeeID") & "', Entry_Page = 'EditSTOReceipt', Status = '" & strSAPWEight & "', PO = '" & request.form("PO") & "', BOL = '" & request.form("BOL") & "'  Where CID = " & strCID

    'Response.Write strsql

			
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
            'Response.end


End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->