<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Truck Load Transfer from Yard to Merchants</title>
<style type="text/css">
.style1 {
	border-style: solid;
	border-width: 1px;
	text-align: right;
	font-weight: bold;
	background-color: #EAF1FF;
}
.style3 {
	font-family: Arial;
}
.style4 {
	font-family: Arial;
	font-weight: bold;
	font-size: x-small;
}
.style5 {
	font-size: x-small;
}
.style6 {
	font-family: Arial;
	font-size: medium;
}
.style7 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
}
.style8 {
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
}
.style9 {
	font-weight: bold;
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
}
.style10 {
	border-style: solid;
	border-width: 1px;
	font-family: Arial;
	font-weight: bold;
	font-size: x-small;
	background-color: #EAF1FF;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID, objMOC, rstFiber, strCarrier, strSpecies
      
    Dim strTrailer
    Dim strReleaseNbr
    Dim strRECNbr
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strGenerator
    Dim strGenCity
    Dim strGenState
    Dim strOther, strR, strsql3, strTransTrailer

       strId  = Trim(Request.QueryString("id")) 


 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
 
		Call SaveData() 

	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to Transfer a Load.</font></br>")
	MyRec.close
	end if

  
ELSE


strsql = "SELECT tblCars.* from tblCars WHERE tblCars.CID = " & strid

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

If MyRec("Trailer") = "UNKNOWN" then
	 strTrailer = MyRec("Transfer_Trailer_Nbr")
     strCarrier = MyRec.fields("Trans_Carrier")
else

    strTrailer = MyRec.fields("Trailer")
    strCarrier = MyRec.fields("Carrier")
    end if
        strSpecies = MyRec.fields("Species")

    

MyRec.close
Call getdata()
	end if

%>



<body><br>
<table width = 100%><tr><td width = 33%></td><td align = center width = 34%><b><font face="Arial" size="4">
Transfer Load from Yard to</font></b><p><b><font face="Arial" size="4">
&nbsp;</font><span class="style6">Merchants</span> </b></td><td align = right width = 33%><a href="SelectTruckYtoM.asp"><b><font face = arial size = 2>RETURN</a></font></b></td></tr></table>



<form name="form1" action="TransYtoM.asp?id=<%=strid%>&r=<%= strR%>" method="post">
<div align="center">
<table cellspacing="0" bordercolor="#C0C0C0" width="60%" cellpadding="0" class="style7">
<tr>
    <td width="276" class="style8" colspan="3">&nbsp;</td>

  </tr>
  <tr>
    <td  align = right width="276" class="style9" >
   <font face="Arial" size="2">Trailer:&nbsp;</font></td>
<td  align = left class="style10">

   <span class="style3"><span class="style5"> <%= strTrailer%>&nbsp;&nbsp;&nbsp;

 
 	</span></span>

   <font face = arial>

 
 <font face="Arial"><span class="style5">Carrier:</span></font>
    <span class="style3"><span class="style5">
    <%= strCarrier %>
   </span></span>
   </td>
<td  align = left class="style8">
<span class="style4">Species:&nbsp;</span><span class="style3"><span class="style5"><b><%= strSpecies%>
   &nbsp;</b></span></span></td></tr>
    <tr>

      <td align = right width="276" class="style8" colspan="3">
  &nbsp;</td>
</tr>
      
  <tr>
    <td width="276" class="style1">
    <font face="Arial" size="2">PMO #:</font></td>

    <td colspan="2" class="style8">

      <input type="text" name="PMO" size="23" style="width: 168px"></td>
  </tr>



  <tr>
    <td width="276" class="style8" colspan="3">&nbsp;</td>

  </tr>



       <tr>
          <td  align = right width="276" class="style9" >
    <font face="Arial" size="2">Date Transferred to Merchants:&nbsp;</font></td>
<td align = left colspan="2" class="style8">

      <input type="text" name="Date_Received" size="23" value = "<%= strDateReceived%>"></td></tr>
<tr>
    <td width="276" class="style8" style="height: 18px" colspan="3"></td>

  </tr>

  <tr>
	<td width="276" height="22" class="style9">
	<p align="right">
    <font face="Arial" size="2">&nbsp;Print Receipt:</font></td>
  <td height="22" colspan="2" class="style8"> <font size="1" face="Verdana">  
	<input type="checkbox"  value="ON" name="Print_receipt" checked></font></td>
</tr>
<tr>
	<td width="276" height="22" class="style8" colspan="3">&nbsp;</td>
</tr>

  <tr>
    <td width="276" class="style8">&nbsp;</td>

    <td align = left colspan="2" class="style8"><Input name="Update" type="submit" Value="Submit" ></td>
  </tr>
</table>

</div>

</form>
</body>
<%
 Function GetData()
  set objMOC = new ASP_CLS_FIBER
  
        strDateReceived = formatdatetime(Now(),0)
End Function



 Function SaveData()
 dim strNow
   strNow = formatdatetime(Now(),0)

strDateReceived = Request.form("Date_received")
IF len(Request.form("PMO")) > 0 then
strRECNbr = Request.form("PMO")
else
strRECNbr = strid & "A"
end if

         strSql = "Update tblCars Set TransYtoB_date = '" & strDateReceived & "', "_
         &" PNbr = '" & strRECNbr & "', Date_unloaded = '" & strDateReceived & "', Location = 'MERCHANTS' where CID = " & strid & ""

         
 	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
             strsql = "INSERT INTO tblMovement ( CID, DDate, Tdate, From_location, To_location, BID, Comment ) "_
		&" SELECT " & strid & ", '" & strDateReceived & "', '" & strnow & "', 'YARD', 'MERCHANTS', '" & Session("EmployeeID") & "', 'Trans Y to M'"
        
           set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql


  strSql="SELECT tblCars.* FROM tblCars WHERE CID= " & strID 
  
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString")
   
 

  strSpecies = MyRec.Fields("Species")
  strSAP_Nbr = MyRec.Fields("SAP_Nbr")
  strVendor = MyRec.Fields("Vendor")
  strPO = MyRec.Fields("PO")
  strRelease_Nbr = MyRec.Fields("Release_Nbr")
  strTrailer = MyRec.Fields("Trailer")
  strTons_Received = MyRec.Fields("Tons_Received")
  strGenerator = MyRec.Fields("Generator")
  strGen_City = MyRec.Fields("Gen_City")
  strGen_State = MyRec.Fields("Gen_State")
  strREC_Number = MyRec.Fields("REC_Number")
  strDateReceived = MyRec.Fields("Date_received")

  strDate_Unloaded = formatdatetime(Now(),0)
  strCarLocation = MyRec.fields("Location")
  strOther_Comments = MyRec.fields("Other_Comments")
  strCarrier = MyRec.fields("Carrier")
	strLocation = MyRec.fields("Location")
	strBales = MyRec("Bales_RF")

	strNet = MyRec.fields("Net")

  MyRec.close
  
if strTrailer = "UNKNOWN" then 
'skip
else

  
  If len(strPO) > 0 then

strsql = "Select Vendor from tblSAPAutoImport where Doc_nbr = " &   strPO

   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString")
If not MyRec.eof then
strSAPVendor = MyRec.fields("Vendor")
else
strSAPVendor = ""
end if
MyRec.close

If strSAPVendor <> "" then
strsql = "Select Email_address from tblVendors where Company_Name = '" & strSAPVEndor & "' "
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString")

If not MyRec.eof then
strVendorEmail = MyRec.fields("Email_address")
else
strVendorEmail = ""
end if
MyRec.close
end if

if len(strVendorEmail) > 0 then
' do nothing
else
strVendorEmail = ""
end if 

If strSAPVendor <> "" and strVEndorEmail <> "" then
         	strEmailTo = strVendorEmail
         	
            strBCC = "<EMAIL>"
     
                 
			Set objMail = Server.CreateObject("CDO.Message")		
			objMail.From = "<EMAIL>"
			objMail.To = strEmailTo
			'objMail.CC = strECC
			' objMail.BCC = strBCC
			objMail.Subject = "Grading Report for PO: " & strPO & "  Release: " & strRelease_nbr & " "
			
		
			strBody2 = strBody2 & "<br><br><br><table width=600><tr><td bgcolor=#F0F0FF bordercolor=#CCCCFF><font face=Arial size=2>Species</font></b></td>"
			strBody2 = strBody2 & "<td bgcolor=#F0F0FF bordercolor=#CCCCFF><font face=Arial size=2>SAP Number</font></b></td>"
	strBody2 = strBody2 & "<td bgcolor=#F0F0FF bordercolor=#CCCCFF><font face=Arial size=2>Vendor</font></b></td>"
	strBody2 = strBody2 & "<td bgcolor=#F0F0FF bordercolor=#CCCCFF><font face=Arial size=2>PO</font></b></td>"
	strBody2 = strBody2 & "<td bgcolor=#F0F0FF bordercolor=#CCCCFF><font face=Arial size=2>Release</font></b></td>"
	strBody2 = strBody2 & "<td bgcolor=#F0F0FF bordercolor=#CCCCFF><font face=Arial size=2>Car/Trailer</font></b></td>"
	strBody2 = strBody2 & "<td bgcolor=#F0F0FF bordercolor=#CCCCFF><font face=Arial size=2>Carrier</font></b></td></tr>"
	strBody2 = strBody2 & "<tr><td bgcolor=white bordercolor=#CCCCFF><font face=Arial size=2>" & strSpecies & "</font></b></td>"
	strBody2 = strBody2 & "<td bgcolor=white bordercolor=#CCCCFF><font face=Arial size=2>" & strSap_nbr & "</font></b></td>"
	strBody2 = strBody2 & "<td bgcolor=white bordercolor=#CCCCFF><font face=Arial size=2>" & strVendor & "</font></b></td>"
	strBody2 = strBody2 & "<td bgcolor=white bordercolor=#CCCCFF><font face=Arial size=2>" & strPO & "</font></b></td>"
	strBody2 = strBody2 & "<td bgcolor=white bordercolor=#CCCCFF><font face=Arial size=2>" & strRelease_nbr & "</font></b></td>"
	strBody2 = strBody2 & "<td bgcolor=white bordercolor=#CCCCFF><font face=Arial size=2>" & strTrailer & "</font></b></td>"
	strBody2 = strBody2 & "<td bgcolor=white bordercolor=#CCCCFF><font face=Arial size=2>" & strCarrier & "</font></b></td></tr>"

	strBody2 = strBody2 & "<tr><td bgcolor=#F0F0FF bordercolor=#CCCCFF><font face=Arial size=2>Generator</font></b></td>"
	strBody2 = strBody2 & "<td bgcolor=#F0F0FF bordercolor=#CCCCFF><font face=Arial size=2>Generator City</font></b></td>"
	strBody2 = strBody2 & "<td bgcolor=#F0F0FF bordercolor=#CCCCFF><font face=Arial size=2>Generator State</font></b></td>"
	strBody2 = strBody2 & "<td bgcolor=#F0F0FF bordercolor=#CCCCFF><font face=Arial size=2>Receipt Number</font></b></td>"
	strBody2 = strBody2 & "<td bgcolor=#F0F0FF bordercolor=#CCCCFF><font face=Arial size=2>Date Received</font></b></td>"
	strBody2 = strBody2 & "<td bgcolor=#F0F0FF bordercolor=#CCCCFF><font face=Arial size=2>Location</font></b></td>"
	strBody2 = strBody2 & "<td bgcolor=#F0F0FF bordercolor=#CCCCFF><font face=Arial size=2>&nbsp;</font></b></td></tr>"
	strBody2 = strBody2 & "<tr><td bgcolor=white bordercolor=#CCCCFF><font face=Arial size=2>" & strGenerator & "</font></b></td>"
	strBody2 = strBody2 & "<td bgcolor=white bordercolor=#CCCCFF><font face=Arial size=2>" & strGen_City & "</font></b></td>"
	strBody2 = strBody2 & "<td bgcolor=white bordercolor=#CCCCFF><font face=Arial size=2>" & strGen_State & "</font></b></td>"
	strBody2 = strBody2 & "<td bgcolor=white bordercolor=#CCCCFF><font face=Arial size=2>" & strRec_number & "</font></b></td>"
	strBody2 = strBody2 & "<td bgcolor=white bordercolor=#CCCCFF><font face=Arial size=2>" & strDateReceived & "</font></b></td>"
	strBody2 = strBody2 & "<td bgcolor=white bordercolor=#CCCCFF><font face=Arial size=2>" & strCarLocation & "</font></b></td>"
	strBody2 = strBody2 & "<td bgcolor=white bordercolor=#CCCCFF><font face=Arial size=2>&nbsp;</font></b></td></tr></table>"
	
	strBody2 = strBody2 & "<br><table width=600><tr><td bgcolor=#FFFFD7 bordercolor=#CCCCFF><font face=Arial size=2>Date Unloaded</font></b></td>"
	strBody2 = strBody2 & "<td bgcolor=#FFFFD7 bordercolor=#CCCCFF><font face=Arial size=2>Location<br> Unloaded</font></b></td>"
strBody2 = strBody2 & "<td bgcolor=#FFFFD7 bordercolor=#CCCCFF><font face=Arial size=2>Comments</font></b></td>"
strBody2 = strBody2 & "<td bgcolor=#FFFFD7 bordercolor=#CCCCFF><font face=Arial size=2>Total<br> Bales</font></b></td>"
strBody2 = strBody2 & "<td bgcolor=#FFFFD7 bordercolor=#CCCCFF><font face=Arial size=2>RF Tech</font></b></td>"
strBody2 = strBody2 & "<td bgcolor=#FFFFD7 bordercolor=#CCCCFF><font face=Arial size=2>Tons<br> Received</font></b></td>"
strBody2 = strBody2 & "<td bgcolor=#FFFFD7 bordercolor=#CCCCFF><font face=Arial size=2>Deduction</font></b></td>"
strBody2 = strBody2 & "<td bgcolor=#FFFFD7 bordercolor=#CCCCFF><font face=Arial size=2>Net</font></b></td></tr>"

	strBody2 = strBody2 & "<tr><td bgcolor=#FFFFD7 bordercolor=#CCCCFF><font face=Arial size=2>" & strDate_Unloaded & "</font></b></td>"
	strBody2 = strBody2 & "<td bgcolor=#FFFFD7 bordercolor=#CCCCFF><font face=Arial size=2>" & strLocation & "</font></b></td>"
strBody2 = strBody2 & "<td bgcolor=#FFFFD7 bordercolor=#CCCCFF><font face=Arial size=2>&nbsp;</font></b></td>"
strBody2 = strBody2 & "<td bgcolor=#FFFFD7 bordercolor=#CCCCFF><font face=Arial size=2>" & strBales & "</font></b></td>"

strBody2 = strBody2 & "<td bgcolor=#FFFFD7 bordercolor=#CCCCFF><font face=Arial size=2>&nbsp;</font></b></td>"
strBody2 = strBody2 & "<td bgcolor=#FFFFD7 bordercolor=#CCCCFF><font face=Arial size=2>" & strTons_received & "</font></b></td>"
strBody2 = strBody2 & "<td bgcolor=#FFFFD7 bordercolor=#CCCCFF><font face=Arial size=2>&nbsp;</font></b></td>"
strBody2 = strBody2 & "<td bgcolor=#FFFFD7 bordercolor=#CCCCFF><font face=Arial size=2>" & strNet & "</font></b></td></tr></table><br>"


objMail.HTMLBody = "<font face = arial size = 2>KC-Mobile Grading Report for Release Number: <b>" & strRelease_nbr & "</b><br><br><b><font face = arial size = 3>Transferred To Merchants Warehouse</b><br><i>Trailer load subject to quality evaluation whereby a second grading sheet may be sent</i></font><br>" & strbody2

strNow = formatdatetime(Now(),0)

			' objMail.Send
			Set objMail = nothing
			
			
		strsql = "Insert into tblGradeEmail (CID, Date_sent, sent_by, Total, Deduction, Net) "_
			&" Select " & strid & ", '" & strNow & "', '" & left(Session("Owner"), 15) & "', "_
			&" " & strTons_received & ", 0, " & strNet & ""
 		set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
		
end if
end if
end if

Response.redirect ("Transfer_receiptYtoM.asp?id=" & strid)



End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->