﻿
<html>
<head>
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">

<meta name="ProgId" content="FrontPage.Editor.Document">

<title>Kimberly<PERSON><PERSON> Automatic Invoice Generation </title>
<SCRIPT LANGUAGE="JavaScript1.2" SRC="scripts/menubar.js"></SCRIPT>
<link rel="stylesheet" href="scripts/KCStyle.css" type="text/css">

<style type="text/css">
.style1 {
	font-family: Arial, Helvetica, sans-serif;
}
.style2 {
	font-family: Arial, Helvetica, sans-serif;
	font-weight: bold;
}
</style>

</head>


<body>
<span class="style2">Kimberly-Clark         |      Automatic Invoice Generation 
</span><span class="style1"></font> </span> <br>
<div class="menuBar">
<img border="0" align="left" src="images/internal.gif" alt="Internal Security Level"> &nbsp;&nbsp;&nbsp;&nbsp;

<a class="menuButton" href="Index.asp">Home</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;

<a class="menuButton" href="" onclick="return buttonClick(event, 'menu2');" onmouseover="buttonMouseover(event, 'menu2);">Exceptions</a>

&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;
<a class="menuButton" href="AIG_users.asp">Maintain Users</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;



<a class="menuButton" href="" onclick="return buttonClick(event, 'menu1');" onmouseover="buttonMouseover(event, 'menu1);">Confirm Pricing</a>
&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;



<a class="menuButton" href="AIG_Vendor_Address.asp">Vendor Information</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;


<a class="menuButton" href="Invoice_Search.asp">Invoice Lookup</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;

<a class="menuButton" href="Vendor_Duplicates.asp">Duplicate Inbound Release Numbers</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;


<a class="menuButton" href="" onclick="return buttonClick(event, 'menu3');" onmouseover="buttonMouseover(event, 'menu3);">Reference</a>



</div>
<!-- Main menus. -->


<div id="menu1" class="menu" onmouseover="menuMouseover(event)">
<a class="menuItem" onclick="return false;" onmouseover="menuItemMouseover(event, 'menu1_1');"><span class="menuItemText"><b>Loudon</b></span><span class="menuItemArrow">&#9654;</span></a>

<a class="menuItem" onclick="return false;" onmouseover="menuItemMouseover(event, 'menu1_2');"><span class="menuItemText"><b>Mobile</b></span><span class="menuItemArrow">&#9654;</span></a>

<a class="menuItem" onclick="return false;" onmouseover="menuItemMouseover(event, 'menu1_3');"><span class="menuItemText"><b>Owensboro</b></span><span class="menuItemArrow">&#9654;</span></a>




</div>

<div id="menu1_1" class="menu" onmouseover="menuMouseover(event)">
<a class="menuItem" href="Loudon_pricing.asp">All Purchase Orders</a>
<a class="menuItem" href="Loudon_pricing_to_Confirm_Select.asp">POs to Confirm</a>
</div>

<div id="menu1_2" class="menu" onmouseover="menuMouseover(event)">
<a class="menuItem" href="Mobile_pricing.asp">All Purchase Orders</a>
<a class="menuItem" href="Mobile_pricing_to_Confirm_Select.asp">POs to Confirm</a>
</div>

<div id="menu1_3" class="menu" onmouseover="menuMouseover(event)">
<a class="menuItem" href="OWB_pricing.asp">All Purchase Orders</a>
<a class="menuItem" href="OWB_pricing_to_Confirm.asp">POs to Confirm</a>
</div>


<div id="menu2" class="menu" onmouseover="menuMouseover(event)">
<a class="menuItem" href="Exception_search.asp">Invoicing Exceptions</a>
<a class="menuItem" href="Vendor_duplicates.asp">Duplicates on Inbounds</a>


</div>

<div id="menu3" class="menu" onmouseover="menuMouseover(event)">
<a class="menuItem" href="Documents/AIG Security.docx">Security Roles</a>
<a class="menuItem" href="Documents/Data Recovery.docx">Data Recovery</a>
<a class="menuItem" href="Documents/AIG Process Steps.doc">AIG Process Manual</a>
<a class="menuItem" href="Documents/Manual Invoice.doc">Manual Invoice Process</a>
<a class="menuItem" href="Documents/RPmgmt process.vsd">KCP Recovered Paper Management Swimlane</a>
</div>
