ell<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Wadding Transfer from Merchants</title>
<style type="text/css">
.style3 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
.style4 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	text-align: right;
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
}
.style7 {
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
}
.style8 {
	font-weight: bold;
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
}
.style9 {
	color: #000000;
}
.auto-style1 {
	font-family: Arial;
	font-size: x-small;
}
.auto-style2 {
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
	text-align: right;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID, objMOC, rstFiber, strCarrier, strSAP, objEPS, strFactor
      
    Dim strTrailer, strSpecies, strESpecies, rstSpecies, strGrade
    Dim strReleaseNbr
    Dim strRECNbr
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strGenerator
    Dim strGenCity
    Dim strGenState
    Dim strOther, strR, strsql3, strTransTrailer, strBrokeDescription

       strId  = Trim(Request.QueryString("id")) 


 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
 
		Call SaveData() 

	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to Transfer this load.</font></br>")
	MyRec.close
	end if
ELSE


Call getdata()
	end if

%>



<body><br>
<table width = 100%><tr><td width = 33%></td><td align = center width = 34%><b><font face="Arial" size="4">
Transfer Wadding from Merchants' Warehouse</font></b></td><td align = right width = 33%>
	&nbsp;</td></tr></table>



<form name="form1" action="WAD_TSF_Merchants.asp" method="post">
<div align="center">
<table cellspacing="0" bordercolor="#C0C0C0" bgcolor="#FFFFE8" style="width: 75%;" cellpadding="0" class="style3">
<tr>
    <td style="width: 361px" class="style7">&nbsp;</td>

    <td class="style7">&nbsp;</td>
  </tr>


      <td align = right style="width: 361px" class="style8">
  	<span class="auto-style1">Warehouse Ticket Number</span><font face="Arial" size="2">:&nbsp;</font></td>
<td  align = left class="style7">

      <input type="text" name="WH_Ticket" size="15" value = " " tabindex="1"></td></tr>


<tr>
    <td style="width: 361px" class="style7">&nbsp;</td>

    <td class="style7">&nbsp;</td>
  </tr>


      <td align = right style="width: 361px" class="style8">
  <font face="Arial" size="2">Transfer Trailer Number:&nbsp;</font></td>
<td  align = left class="style7">

      <input type="text" name="Trans_Trailer" size="15" value = "<%= strTransTrailer%>" tabindex="2"></td></tr>
      
       <tr><td style="width: 361px" class="style7">&nbsp;</td>
  <td class="style7">&nbsp;</td></tr>
<tr>
    <td style="width: 361px" class="style8">  
	<p align="right">  <font face="Arial" size="2">Carrier:&nbsp;</font></td>
    <td class="style7">  <select name="Carrier" style="font-weight: 700" tabindex="3">
 	<option value="" selected>  Select Carrier (Required)</option>
 	<% strsql = "Select Carrier,C_Description, Weight from tblCarrier order by Carrier"
 	   	 Set MyRec4 = Server.CreateObject("ADODB.Recordset")
   	 MyRec4.Open strSQL, Session("ConnectionString")
   	 while not MyRec4.eof %>
   	 <option <% if strCarrier = MyRec4("Carrier") then %> selected <% end if %> value="<%= MyRec4("Carrier") %>"><%= MyRec4("Carrier") %>&nbsp;<%= MyRec4("C_Description") %>&nbsp;<%= MyRec4("Weight") %></option>"

 <%MyRec4.Movenext
 wend
 MyRec4.close %>
     </select></td>
  </tr>
        <tr>
    <td style="width: 361px" class="style7">&nbsp;</td>

    <td class="style7">&nbsp;</td>
  </tr>
  <tr>
    <td class="style8" style="width: 361px">  
	<p align="right">  <font face="Arial" size="2">SAP #:&nbsp;</font></td>
    <td class="style7">    <select name="SAP" tabindex="4">
  <% strsql = "Select SAP, Type from tblBrokeSAP where Category = 'WADDING' order by Type"
  
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    While not MyRec.eof %>
    <option value="<%= MyRec("SAP") %>"><%= MyRec("SAP") %>&nbsp;<%= MyREc("Type") %></option>
<% Myrec.movenext
wend
MyRec.close %>
	</select>   
 </td>
  </tr>
          <tr>
    <td style="width: 361px" class="style7">&nbsp;</td>

    <td class="style7">&nbsp;</td>
  </tr>

  <tr>
    <td class="style4" style="width: 361px"><strong>Rolls:&nbsp;&nbsp;&nbsp; </strong></td>

    <td class="style8"><font size="2" face = arial>
      <input type="text" name="Rolls" size="11" style="width: 36px" tabindex="6">&nbsp;&nbsp; </font></td>
  </tr>

    <tr>
    <td align="right" style="height: 50px; width: 361px" class="style8">
	<font face="Arial" size="2">Enter WEIGHT (lbs) from WH:&nbsp;&nbsp;&nbsp; </font></td>

    <td style="height: 50px" class="style7">

     
      <input type="text" name="Weight" size="11" style="width: 106px" tabindex="7"></td>

  </tr>
    <tr>
    <td style="width: 361px" class="auto-style2"><font face="Arial" size="2">
	<strong>If no&nbsp; WEIGHT, check:&nbsp;</strong></font></td>

    <td class="style7">
	<input type="checkbox" value="ON" name="No_Weight" style="width: 29px; height: 22px"></td>
  </tr>





    <tr>
    <td style="width: 361px" class="style7">&nbsp;</td>

    <td class="style7">&nbsp;</td>
  </tr>





       <tr>
          <td  align = right height="27" style="width: 361px" class="style8" >
    <font face="Arial" size="2">Date Transferred:&nbsp;</font></td>
<td align = left height="27" class="style7">

      <input type="text" name="Date_Received" size="15" value = "<%= strDateReceived%>" tabindex="8"></td></tr>
<tr>
    <td style="width: 361px" class="style7">&nbsp;</td>

    <td class="style7">&nbsp;</td>
  </tr>

  <tr>
    <td style="width: 361px" class="style8">
	<p align="right"><font face="Arial" size="2">Print Movement Order:</font></td>

    <td align = left class="style7"> <font size="1" face="Verdana">  
	<input type="checkbox"  value="ON" name="Print_receipt" checked></font></td>
  </tr>

  <tr>
    <td style="width: 361px" class="style7">&nbsp;</td>

    <td align = left class="style7">&nbsp;</td>
  </tr>
  <tr>
        <td  align = right height="27" style="width: 361px" class="style8" >
 <font face="Arial" size="2">Comments/Notes: </td>

    <td align = left class="style7">   <font face="Arial" color="teal" size="4">   
	<input name="Other_Comments" size="25" value = "" style="font-weight: 700; width: 563px;" tabindex="9"></font></td>
  </tr>

  <tr>
    <td style="width: 361px" class="style7">&nbsp;</td>

    <td align = left class="style7">&nbsp;</td>
  </tr>

  <tr>
    <td style="width: 361px" class="style7">&nbsp;</td>

    <td align = left class="style7"><Input name="Update" type="submit" Value="Submit" ></td>
  </tr>
</table>

</div>

</form>
</body>
<%
 Function GetData()

    
        strDateReceived = formatdatetime(Now(),2)  
          set objEPS = new ASP_CLS_Fiber      
End Function




 Function SaveData()
 strBales = 0
strTrailer = Request.form("Trans_Trailer")
strCarrier = Replace(Request.form("Carrier"), "'", "''")

strDateReceived = Request.form("Date_received")

 

Dim strRightnow, strTons
strRightnow = Now()
if len(Request.form("Weight")) > 0 then
strTons = Request.form("Weight")
else
strTons = 0
end if

 If len(request.form("Rolls")) > 0 then  
strRolls = request.form("Rolls")
else
strRolls = 0
end if

strsap = request.form("SAP")
 
  strsql = "Select  Type from tblBrokeSAP where SAP = '" & strSAP & "'"
  
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    If not MyRec.eof then
    strDescription = Replace(MyRec("Type"), "'", "''") 
    else
    strDescription = ""
    end if
 
 
	strOther = Replace(Request.form("Other_Comments"), "'", "''") 


	strsql =  "INSERT INTO tblCars ( PMO_Nbr, Date_Received, Trailer,  OID,  Transfer_Trailer_nbr, Transfer_Date, Trans_Carrier, Species, Grade, Location, SAP_NBR, "_
	&"  Tons_Received, Deduction, Net, Entry_Time, Entry_BID, Entry_Page, Roll_Count, WH_TIcket, WAD_Description, Other_Comments ) "_
	&" SELECT   'MERCHANTS', '" & strDateReceived & "', 'UNKNOWN',  0,'" & strTrailer & "', '" & strDateReceived & "', '" & strCarrier & "', '" & strDescription & "', 'WADDING',  'YARD',   "_
	&"   '" & strSap & "'," & strTons & ", 0, " & strTons & ", '" & strRightnow & "', '" & Session("EmployeeID") & "', "_
	&"  'WAD_TSF_Merchants.asp', " & strRolls & ", '" & request.form("WH_Ticket") & "', '" & strDescription & "', '" & strOther & "'"
	
	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
		 	MyConn.Execute strSQL
			MyConn.Close

Dim strlast
strsql = "Select Max(CID) as MAXofCID from tblCars"

   	 Set MyConn = Server.CreateObject("ADODB.Recordset")
   	 MyConn.Open strSQL, Session("ConnectionString")
   	 strlast = MyConn.fields("MaxofCID")
   	 MyConn.close

         
strWH = Request.form("WH_TIcket")
         
      
    if request.form("No_Weight") = "ON" then
      
                   strEmailTo = "<EMAIL>"       
            
         

            strECC = "<EMAIL> "
         
                 
			Set objMail = Server.CreateObject("CDO.Message")		
			objMail.From = "<EMAIL>"
			objMail.To = strEmailTo
			objMail.CC = strECC		 
	
If len(strWH) > 1 then	

			objMail.Subject = "Trailer " & strTrailer & " Ticket: " & strWH & " no weight in YMS"
			
			else
			
			objMail.Subject = "Trailer " & strTrailer & "  no weight in YMS"
			end if
	
				
		objMail.HTMLBody = "<font face = arial size = 2>Shuttle Load - Trailer <b>" & strCarrier & " " & strTrailer  & "</b>  arrived at KC Mobile, AL on " & strDateReceive  & " with " & strDescription  & " and No Weight was entered for the YMS Receipt " & strlast  & "."


			' objMail.Send
			Set objMail = nothing	  
    
end if    

If strCarrier = "SIX" then 

strsql = "Select Email_Add from tblCarrier where Carrier = '" & strCarrier & "'"
  Set MyConn = Server.CreateObject("ADODB.Recordset")
   	 MyConn.Open strSQL, Session("ConnectionString")
   	 strEmailTo = MyConn.fields("Email_Add")
   	 MyConn.close
     
          
         

            strECC = "<EMAIL> "
         
                 
			Set objMail = Server.CreateObject("CDO.Message")		
			objMail.From = "<EMAIL>"
			objMail.To = strEmailTo
			objMail.CC = strECC		 
		

			objMail.Subject = "Trailer " & strTrailer & " Ticket: " & strWH & " Arrived to Unload"
	
				
		objMail.HTMLBody = "<font face = arial size = 2>Shuttle Load - Trailer <b>" & strCarrier & " - " & strTrailer  & "</b>  arrived at " & strRightnow  & " with " & strDescription  & "."


			' objMail.Send
			Set objMail = nothing	  
    
end if   
     
         
         
 If Request.form("Print_receipt") = "ON" then
 Response.redirect("Transfer_Mreceipt_WAD.asp?id=" & strlast)
 else        
  

 Response.redirect ("Fiberindex.asp")
end if


End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->