<html>
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->

<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Summary Details</title>
</head>
<% dim strsql, MyRec, strid,  objGeneral, strDescription, strLocation, strComments, strDate, strSpaceID
dim objEPS, rstTeam, rstWA, MyConn, strTeam,  strWA, strFunctional, strAsset, strStatus,  strsql2, MyConn2
Dim strHAStatus, strApprover1, strApprover2, strApprovalDate

strid = Request.querystring("id")
strnow = dateadd("h", -5, now())
strDate = formatdatetime(strnow,2))


strsql2 = "SELECT tblHA.*  FROM tblHA  where SpaceID = '" & strid & "'"

Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strsql2, Session("ConnectionString")
  If not MyRec.eof then
  
strHazID = MyRec.fields("IDHazAssess")
strApprover1 = MyRec.fields("HA_Approver_1")
strApprover2 = MyRec.fields("HA_Approver_2")
strHAStatus = MyRec.fields("HA_Status")
strApprovalDate = MyRec.fields("HA_Approval_Date")
else
strHazID = 0
Myrec.close
end if
 
 strsql = "SELECT tblSOP.* from tblSOP where SOP_NO = '" & strid & "'"

Set MyConn = Server.CreateObject("ADODB.Recordset") 
   MyConn.Open strSQL, Session("ConnectionString")
  If not MyConn.eof then 
strSpaceID = MyConn.fields("SOP_NO")
strDescription = MyConn.fields("sDescription")
strLocation = MyConn.fields("Location")
strComments = MyConn.fields("Comment")
strDate = MyConn.fields("sDate")
strTeam = MyConn.fields("TeamName")

strWA = MyConn.fields("WorkArea")
strFunctional = MyConn.fields("Functional_Location")
strAsset = MyConn.fields("AssignedAsset")
strStatus = MyConn.fields("SpaceStatus")
end if
MyConn.Close



 %><body bgcolor="#FFFFFF"><br>
 <table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#FFFFFF">
   
  <tr>
 <td align="left"><b><font face="arial">SUMMARY DETAILS </font></b>
	
</td><td align = right><font face="arial"><b><a href="javascript:history.go(-1)">RETURN</a></b></td></tr></table>
	<form name="form1" action="CSE_Add.asp"  method="post" ID="Form1"  >
 
 <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#D9E1F9">
   
  <tr>

   
    <td bgcolor="#FFFFDD" align="center">
	<font face="Arial" size="2">Team</font></td>

   
    <td bgcolor="#FFFFDD" align="center">
	<font face="Arial" size="2">Work Area</font></td>

   
    <td   bgcolor="#FFFFDD">
	<p align="center"><font face="Arial" size="2">Space #</font></td>
    <td bgcolor="#FFFFDD" align="center">
	<font face="Arial" size="2">Description/Name</font></td>

   
    <td bgcolor="#FFFFDD" align="center">
	<font face="Arial" size="2">Physical Location</font></td>

   
  </tr>
  <tr>
	
    <td  bgcolor="#FFFFFF"  >
	<p align="center">  <font face="Arial" size="2">   <%= strTeam%>
     </select></font></td>
	
    <td  bgcolor="#FFFFFF"  >
	<p align="center">
		<font face="Arial" size="2">  <%=  strWA %>
     </select></font></td>
	
    <td align = center bgcolor="#FFFFFF" > 
	<font face="Arial" size="2">
	<%= strSpaceID %></font></td>
    <td  bgcolor="#FFFFFF"  align = center >
	<font face="Arial" size="2">
<%= strDescription %></font></td>
	
    <td  bgcolor="#FFFFFF" align = center  >
 <font face="Arial" size="2">
 <%= strLocation%> &nbsp;</font></td>
	
    </table> <br>
 
 <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#D9E1F9" id="table1" align = center>
   
  <tr>
    <td bgcolor="#FFFFDD" align="center" width="205" height="22">
	<font face="Arial" size="2">Assigned Asset</font></td>

   
    <td bgcolor="#FFFFDD" align="center" width="604" height="22">
	<font face="Arial" size="2">Functional Location</font></td>

   
    <td bgcolor="#FFFFDD" align="center" height="22">
	<font face="Arial" size="2">Space Status</font></td>

   
  </tr>
  <tr>
    <td  bgcolor="#FFFFFF" align = center >
	
		<font face="Arial" size="2"> <%= strAsset%>&nbsp;</font></td>
	
    <td  bgcolor="#FFFFFF" align = center  >
<font face="Arial" size="2">
	<%= strFunctional %></font></td>
	
    <td  bgcolor="#FFFFFF"  >
	<p align="center">
		<font face="Arial" size="2"> <%= strStatus %>&nbsp;

 </font></td>
	
  </table><br>
 
 <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#D9E1F9" id="table2" align = center>
   
  <tr>
    <td bgcolor="#FFFFDD" align="center" >
	<font face="Arial" size="2">Last HA Approval Date</font></td>

   
    <td bgcolor="#FFFFDD" align="center">
	<font face="Arial" size="2">Approver 1 (name)</font></td>

   
    <td bgcolor="#FFFFDD" align="center">
	<font face="Arial" size="2">Approver 2 (name)</font></td>

   
    <td bgcolor="#FFFFDD" align="center">
	<font face="Arial" size="2">Current HA Status</font></td>

   
  </tr>
  <tr>
    <td  bgcolor="#FFFFFF" align="center" >
	<font face="Arial" size="2"><%= strApprovalDate%>&nbsp;</td>
	
   <td  bgcolor="#FFFFFF" align="center" ><font face="Arial" size="2"><%= strApprover1%>
	&nbsp;</td>
	
     <td  bgcolor="#FFFFFF" align="center" ><font face="Arial" size="2"><%= strApprover2 %>
	&nbsp;</td>
	
 <td  bgcolor="#FFFFFF" align="center" ><font face="Arial" size="2"><%= strHAstatus %>
	&nbsp;</td>
	
  </table><br>
 <% 	If strHazID <> 0 then %>
 <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#D9E1F9" id="table3">
   
   <tr ><td bgcolor="#FFFFDD"><font face = arial size = 2 >
		<p align="center">ECP Procedures&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
 <font face="Arial" size="1"><b>
	<span style="background-color: #FFFFD7">&nbsp;</span></b></font></td>
</tr>

   
     <tr ><td bgcolor="#FFFFFF" height="40"><font face = arial size = 2 >
	<%	

	
	strsql = "Select ECP_ID, Task, EID from tblECP where Haz_Id = " & strHazID & ""

    Set MyConnC = Server.CreateObject("ADODB.Recordset") 
   		MyConnC.Open strSQL, Session("ConnectionString")
  If not MyConnC.eof then 
  
  	      
       ii = 0
       while not MyConnC.Eof %>
       <font face = arial size = 2>
&nbsp;&nbsp;
<%= MyConnC.fields("ECP_ID")%>&nbsp;
&nbsp;<%= MyConnc.fields("Task")%>

 <br>
    <%
       ii = ii + 1
       MyConnC.MoveNext
     Wend
     End if
     MyConnc.close
    %>

	
	
	
	
	&nbsp;</td></tr>
	

    	
    </table><br>
 
 <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#D9E1F9" id="table4">
   
  <tr>

   
    <td bgcolor="#FFFFDD" align="center">
	<font face="Arial" size="2">Rescue Plan Per Portal:</font></td>

   
    <td bgcolor="#FFFFDD" align="center">
	<font face="Arial" size="2">Portal Name:</font></td>

   
    <td   bgcolor="#FFFFDD">
	<p align="center"><font face="Arial" size="2">Revision Date</font></td>

   
  </tr>
  <tr>
	
    <td  bgcolor="#FFFFFF"  >
	<p align="center">  &nbsp;</td>
	
    <td  bgcolor="#FFFFFF"  >
	<p align="center">
		&nbsp;</td>
	
    <td align = center bgcolor="#FFFFFF" > 
	&nbsp;</td>
    	
    </table> <br>
 
 <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#D9E1F9" id="table5">
   
  <tr>

   
    <td bgcolor="#FFFFDD" align="center">
	<font face="Arial" size="2">Space Air Tests</font></td>

   
    <td bgcolor="#FFFFDD" align="center">
	<font face="Arial" size="2">Locations</font></td>

   
    <td   bgcolor="#FFFFDD">
	<p align="center"><font face="Arial" size="2">Technique</font></td>

   
  </tr>
  <tr>
	
    <td  bgcolor="#FFFFFF"  >
	<p align="center">  &nbsp;</td>
	
    <td  bgcolor="#FFFFFF"  >
	<p align="center">
		&nbsp;</td>
	
    <td align = center bgcolor="#FFFFFF" > 
	&nbsp;</td>
    	
    </table> <br>
 
 <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#D9E1F9" id="table6">
   
  <tr>

   
    <td bgcolor="#FFFFDD" align="center">
	<font face="Arial" size="2">Field Audit</font></td>

   
    <td bgcolor="#FFFFDD" align="center">
	<font face="Arial" size="2">Last Day Entered</font></td>

   
  </tr>
  <tr>
	
    <td  bgcolor="#FFFFFF"  >
	<p align="center">  &nbsp;</td>
	
    <td  bgcolor="#FFFFFF"  >
	<p align="center">
		&nbsp;</td>
	
    </table> <br>
 
 <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#D9E1F9" id="table7">
   
  <tr>

   
    <td bgcolor="#FFFFDD" align="center">
	<font face="Arial" size="2">Comments</font></td>

   
  </tr>
  <tr>
	
    <td  bgcolor="#FFFFFF"  >
	<p align="center"> <font face="Arial" size="2"><%= strComments%> &nbsp;</td>
	
    </table> 

	<p><font face="Arial" size="4">Action log: </font></p>
 
 <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%"  id="table8" align = center height="65">
   

  <% strsql = "Select *  from tblActionLog where Haz_ID = " & strHAZid & " order by [Type]"

Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")%>
  
        <tr class="tableheader">


	<td height="22" align = left bgcolor="#FFFFDD"><font face="Arial" size="2"><b>Type</b></font></td>
	<td height="22" align = left bgcolor="#FFFFDD"><font face="Arial" size="2">
	<b>BID</b></font></td>
<td height="22" align = left bgcolor="#FFFFDD"><font face="Arial" size="2"><b>Name</b></font></td>

<td height="22"align = left bgcolor="#FFFFDD"> <font face="Arial" size="2"><b>Date</b></font></td>


	
	
</tr>
      
        
      
  	<% 
      Dim ii
       ii = 0
       while not MyConn.Eof %>
<tr>
	



<td align = left><font face = "arial" size = "2"><%= MyConn.fields("Type")%>&nbsp;</td> 
<td align = left><font face = "arial" size = "2"><%= MyConn.fields("BID")%>&nbsp;</td>
<td align = left><font face = "arial" size = "2"><%= MyConn.fields("E_name")%>&nbsp;</td> 
<td align =leftr><font face = "arial" size = "2"><%= MyConn.fields("P_date")%>&nbsp;&nbsp;</td>
  </tr>
    <%
       ii = ii + 1
       MyConn.MoveNext
     Wend
     MyConn.close
    %>

	
  </table>
 

    <% end if %> 
	
  </table>
	
	<p align="left"><font face="Arial" size="4">Use Log:</font></p>
 
 <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#D9E1F9" id="table12" align = center >
   
  <tr>
    <td bgcolor="#FFFFDD" align="center"  height="22">
	<font face="Arial" size="2">Dates Used</font></td>

   
    <td bgcolor="#FFFFDD" align="center"  height="22">
	<font face="Arial" size="2">Requester</font></td>
   <td bgcolor="#FFFFDD" align="center"  height="22">
	<font face="Arial" size="2">Permit #</font></td>

   
    <td bgcolor="#FFFFDD" align="center"  height="22">
	<font face="Arial" size="2">Permit Status</font></td>

   
    <td bgcolor="#FFFFDD" align="center" height="22">
	<font face="Arial" size="2">Close out Date BID</font></td>

   
    <td bgcolor="#FFFFDD" align="center" >
	<font size="2" face="Arial">Safety Department Date</font></td>

   
  </tr>
  
  <% strsql2 = "SELECT tblPermit.*  FROM tblPermit  where Space_ID = '" & strid & "'"

Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strsql2, Session("ConnectionString")
  If not MyRec.eof then 
   while not MyRec.Eof%>
  <tr>
    <td  bgcolor="#FFFFFF"  >
	<p align="center">
		<font size="2" face="Arial"><%= MyRec.fields("Date_issued") %> - <%= MyRec.fields("Date_Expired") %></td>
	
    <td  align = center bgcolor="#FFFFFF"  >
	<font size="2" face="Arial">	<% if len(MyRec.fields("Author")) > 1 then %>
		<%= MyRec.fields("Author") %>
		<% else %>
	<%= MyRec.fields("BID") %>
	

	<% end if %></td>
	    <td  align = center bgcolor="#FFFFFF"  >
	<font size="2" face="Arial"><%= MyRec.fields("PID") %>&nbsp;</td>

    <td  align = center bgcolor="#FFFFFF"  >
	<font size="2" face="Arial"><%= MyRec.fields("permit_status") %>&nbsp;</td>
	
    <td  bgcolor="#FFFFFF"  >
	<p align="center"><% if len(MyRec.fields("Status_date")) > 3 then %>
	<font size="2" face="Arial"><%= MyRec.fields("STatus_date") %> - <%= MyRec.fields("CLose_BID") %>
	<% else %>
	<a href="Work_area_Summary.asp?id=<%= strWA %>"><font size="2" face="Arial">Close out</a><% end if %>
		&nbsp;</font></td>
	
      <td  bgcolor="#FFFFFF"  >
	<p align="center"><% if len(MyRec.fields("Safety_date")) > 3 then %><font face="Arial" size = 2>
	<%= MyRec.fields("Safety_date") %>&nbsp;&nbsp;<%= MyRec.fields("Safety_BID")%>
	<% elseif MyRec.fields("Permit_status") = "Permit Not Used" then %>
	&nbsp;
	<% elseif MyRec.fields("Permit_status") = "Review Complete" then  %>
	<font size="2" face="Arial"><a href="SD_Closeout.asp?id=<%= MyRec.fields("PID")%>">Close out</a>
		&nbsp;</font>
		<% else %>
		&nbsp;<% end if %>
		</td>

     <%
       ii = ii + 1
       MyRec.MoveNext
   wend
     MyRec.close
     end if
    %>
  </table>
	<p align="center">&nbsp;</p>
	<p>&nbsp;</p>
	</form>

 
 