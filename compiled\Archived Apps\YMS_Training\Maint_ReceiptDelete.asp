﻿<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->


<%

dim strID
strID = Request.querystring("id")

    set objGeneral = new ASP_CLS_General
  %>


   <form action="Maint_ReceiptDelete.asp?id=<%= strid%>" method=post name="ReceiptDelete">

                <table width = 100%> 
<tr><td></td><td colspan=2 align = right><font face="Arial"><a href="Sixty_day list.asp"><b>Return</b></a></font></td></tr>
<% Dim Myrec, strsql1, strDate, strTrailer, strTransTrailer, strComments
	Dim strsql, Myconn, strsql3



strsql1 = "SELECT tblCars.* FROM tblCars WHERE CID = " & strid & ""

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL1, Session("ConnectionString")

strTrailer = MyRec.fields("Trailer")
strTransTrailer = MyRec.fields("Transfer_Trailer_Nbr") %>


     <tr>
                        <td><Font size = 2 face = Arial><b> Are you sure you want to delete the Receipt?</b> <br><br> If so, click the button below.
                           
            </td>
            
        </tr>
  <tr>
<td><Font size = 2 face = Arial><br>
Receipt Number:  <%=MyRec("CID")%><br>
Trailer:  <%=MyRec("Trailer")%>  <br>
Transfer:  <%=MyRec("Transfer_Trailer_Nbr")%><br>
Release #:  <%=MyRec("Release_Nbr")%>
</td>


</tr>
      
    </table>
<% Myrec.close %>
<p>

<Input name="Update" type="submit" Value="Delete Receipt" >
</form>


  <% if objGeneral.IsSubmit() Then 


    strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL3, Session("ConnectionString")


If not Myrec.eof then
   
    strid = request.querystring("id")
    strDate = formatdatetime(Now(),0)
 
      
    
    
    strsql1 = "SELECT tblCars.* FROM tblCars WHERE CID = " & strid & ""

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL1, Session("ConnectionString")

	strTrailer = MyRec.fields("Trailer")
	strTransTrailer = MyRec.fields("Transfer_Trailer_Nbr") 
	strRelease = MyRec.fields("Release_nbr")
	strWeight = MyRec.fields("Net")

    If strTrailer = "UNKNOWN" then
    strTrailer = strTransTrailer
    
    end if 
    MyRec.close
    
    strcomments = "Trailer: " & strTrailer & " Net: " & strWeight & " Rel: " & strRelease
    
           strsql = "INSERT INTO tblMovement ( CID, DDate, From_location,  Comment, BID ) "_
		&" SELECT " & strid & ", '" & strDate & "', 'Deleted', '" & strComments & "', '" & Session("EmployeeID") & "'"
        
           set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql

   
    

	strSQL = "DELETE FROM tblCars where tblCars.CID = " &  strID & ""

			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close


if len(strRelease) > 3 and strTrailer = "UNKNOWN"  then 
		strsql = "Update tblCars set Tally_sheet =  Null where Release_nbr = '" & strRelease & "'"
				Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
			end if



Response.redirect ("Sixty_day list.asp") 
else
Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to delete a Receipt.</font></br>")
MyRec.close
end if




   End if 
%><!--#include file="Fiberfooter.inc"-->