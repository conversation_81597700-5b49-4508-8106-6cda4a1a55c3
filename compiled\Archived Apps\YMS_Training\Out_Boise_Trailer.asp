<script>window.history.go(1);</script>
<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Move Trailer of BOISE CASCADE</title>
</head>
<%
    Dim strSQL, MyRec, strID, objMOC, rst<PERSON><PERSON>, str<PERSON>arrier, strLocation
      
    Dim strTrailer, strSAP
    Dim strReleaseNbr
    Dim strRECNbr
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived, strDateSite
    Dim strGenerator
    Dim strGenCity
    Dim strGenState, strpost
    Dim strOther, strR, strsql3, strTransTrailer, strCurrentStatus, strFromDoor, strNFID

       strId  = Trim(Request.QueryString("id")) 


 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then
strPost = ""

   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	strPost = "OK"
	MyRec.close
 	end if
 	
If Session("EmployeeID") = "USMOMN22" then
strPost = "OK"
end if	

If strPost = "OK" then
		Call SaveData() 
		

	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to change the location of a Trailer.</font></br>")
	MyRec.close
	end if

  
ELSE


strsql = "SELECT tblCars.* from tblCars WHERE tblCars.CID = " & strid

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

	strCurrentStatus= MyRec.fields("Location")
    strTrailer = MyRec.fields("Trailer")
    strCarrier = MyRec.fields("Carrier")
        strDateSite = MyRec.fields("Date_received")


    strDateReceived = formatdatetime(Now(),0)

MyRec.close

	end if

%>



<body><br>
<table width = 100%><tr><td width = 33%></td><td align = center width = 34%>
	<b><font face="Arial">Move Trailer of BOISE CASCADE</font></b></td><td align = right>
	<font face="Arial"><b><a href="SelectTruckUnloadBOISE.asp">RETURN</a></b></font></td></tr></table>



<form name="form1" action="Out_BOISE_Trailer.asp?id=<%=strid%>" method="post">

<input type="hidden" name="Date_site" value="<%= strDateSite%>">
<input type="hidden" name="Carrier" value="<%= strCarrier %>">
<input type="hidden" name="Trailer" value="<%= strTrailer %>">

<div align="center">
<table border="1" cellspacing="0" width="60%" bgcolor="#FOFOFF" style="border-collapse: collapse" cellpadding="0" align = center>

  <tr>
      <td  align = center bgcolor="#F0F0FF"  > 
   <b>
   <font face="Arial" size="2">Trailer:&nbsp;</font></b>&nbsp;&nbsp;

   <font face = arial><b> <%= strTrailer%>&nbsp;&nbsp;&nbsp;

 
 <font face="Arial" size="2">Carrier:</font>
    <%= strCarrier %>&nbsp;&nbsp;&nbsp; <font face="Arial" size="2">Species:</font>
BOISE CASCADE
   </td></tr></table>
   </div>
<div align="center">
      <table border="2"  width="60%" bgcolor="#F0F0FF" style="border-collapse: collapse" cellpadding="2">

    <tr>

      <td  bgcolor="#FOFOFF" align = right bordercolor="#F0F0FF">
 <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF">

      &nbsp;</td></tr>
      
       <tr> <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF">&nbsp;</td>
 <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF">&nbsp;</td></tr>
  <tr>
   <td  bgcolor="#FOFOFF" align = right bordercolor="#F0F0FF">
  <font face="Arial" size="2"><b>New Location:&nbsp;</b></font></td>

      <td  bgcolor="#FOFOFF" align = LEFT bordercolor="#F0F0FF">
	<font face="Arial">	
	<select size="1" name="location">
	<option value="" >Select Location</option>
	<option>Broke Center</option>
	<option>DC</option>
	<option>DFF</option>
	<option>Fayard</option>
	<option>Merchants</option>
	</select></font></td>
</tr>

<tr>
   <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF">
	<p align="right">  <font face="Arial" size="2"><b>&nbsp;</b></font></td>
    <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF">  &nbsp;</td>
  </tr>
  <tr>
    <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF">&nbsp;</td>

 <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF">&nbsp;</td>
  </tr>



       <tr>
         <td  bgcolor="#FOFOFF" ALIGN = right bordercolor="#F0F0FF">
    <font face="Arial" size="2"><b>Date Unloaded:&nbsp;</b></font></td>
 <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF" align = LEFT>

      <input type="text" name="Date_Received" size="23" value = "<%= strDateReceived%>"></td></tr>
<tr>
  <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF">&nbsp;</td>

    <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF">&nbsp;</td>
  </tr>

  <tr>
   <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF">&nbsp;</td>

    <td  bgcolor="#FOFOFF" bordercolor="#F0F0FF" align = left ><Input name="Update" type="submit" Value="Submit" ></td>
  </tr>
</table>

</div>

</form>


</body>
<%
 Function SaveData()

 strCarrier = request.form("Carrier")

strLocation = Request.form("location")
dim strnow
strnow = formatdatetime(Now(),0)

strDateReceived = Request.form("Date_received")        

         strsql = "Update tblCars set Date_unloaded = '" & strDateReceived & "',   Location = '" & strLocation & "' where CID = " & strid & ""
 
 	     set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 

         strsql = "INSERT INTO tblMovement ( CID, Comment, DDate, Tdate, From_location, To_location, BID ) "_
		&" SELECT " & strid & ", 'BOISE', '" & strDateReceived & "', '" & strnow & "', 'YARD', '" & strlocation & "', '" & Session("EmployeeID") & "'"
        
           set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql


If strLocation = "EMPTY" then  

 strDateSite = request.form("Date_site")
 strDateReceived = formatdatetime(strDateReceived,2)
 
 Dim strEmailAdd
 strsql = "Select Carrier, Email_add  from tblCarrier where len(Email_add) > 0 and Carrier = '" & strCarrier & "'"
 
    	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")

	If not Myrec.eof then
	strEmailadd = MyRec.fields("Email_add")
end if
MyRec.close
 
 

              strEmailTo = strEmailadd
      
                 
			Set objMail = Server.CreateObject("CDO.Message")		
			objMail.From = "<EMAIL>"
			objMail.To = strEmailTo
		
                 
	
			objMail.Subject = "Trailer " & strTrailer & " Empty "
	objMail.HTMLBody = "<font face = arial size = 2>Trailer<b> " & strTrailer & "</b> arrived arrived at KC Mobile, AL on " & strDateSite & " with Boise Cascade and was emptied on " & strDateReceived & "."



			' objMail.Send
			Set objMail = nothing
		

end if
         
Response.redirect ("SelectTruckUnloadBOISE.asp")



End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->