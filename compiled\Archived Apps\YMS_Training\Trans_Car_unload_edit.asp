<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 14.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Recovered Paper Car Unload Entry</title>
<style type="text/css">
.style1 {
	font-family: Arial;
	font-size: xx-small;
	text-align: center;
}
.style2 {
	text-align: center;
}
</style>
</head>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->
<%

Dim strSQL, MyRec, strid, strDateReceived, strCarLocation, strFQE, strFQG, strFQP, strBQE, strBQG, strBQP, strMQD, strMQL, strMQM, strMQH
Dim strSpecies, strSAP_Nbr, strVendor, strPO, strRelease_Nbr, strTrailer, strTons_Received, strGenerator, strTime_unloaded
Dim strGen_City, strGen_State, strREC_Number, strCarDate_Received, strTotal_Bales, strOCC_Tech, strGradingDate_Received
Dim strDate_Unloaded, strLocation, strOther_Comments, strNet, strDeduction, strCarrier, strAdhesive
Dim strFQExcellent, strFQGood, strFQPoor, strFiber_Quality_Reject, strFQRating, strOCC_Location, strBQRating
Dim strBQExcellent, strBQGood, strBQPoor, strBale_Quality_Reject, strTransTrailer, strOFQ
Dim strMoisture_Dry, strMoisture_Light, strMoisture_Medium, strMoisture_Heavy, strWeight_Sample_Bale, strNbr_Wet_Bales, strBales_Rejected
Dim strGround_Wood, strCardboard, strNewsprint, strPlastics, strBrightness, strWet_Strength, strBeater_Dyed
Dim strEnvelopes, strTrash, strDirt, strFiberComments, strBale_Comments, strMoisture_Comments, strApproval
strid = Request.querystring("id")
Call getdata()


 set objGeneral = new ASP_CLS_General

  

if objGeneral.IsSubmit() Then


	Call SaveData() 

End if
%>
<body>
<form name="form1" action="Trans_Car_unload_edit.asp?id=<%=strid%>&t=<%=strTrailer%>&o=<%= strOFQ%>" method="post">
 <input type="hidden" name="TR" value="<%= strTons_Received%>" >
<table border="0" cellpadding="0" cellspacing="0" width = 100% style="border-collapse: collapse" bordercolor="000000" >
  <tr>
    <td  bgcolor="#FFFFFF" bordercolor="#CCCCFF" >
    <p align="center"><font face="Arial" size="4">Recovered Paper Car Unload 
    Entry</font></td>

    <td align = right bgcolor="#FFFFFF"><Input name="Submit" type="submit" Value="Submit" ></td>
  </tr>
</table>
<table border="1" cellpadding="0" cellspacing="0" width = 100% style="border-collapse: collapse" bordercolor="000000" >
  <tr>
    <td bgcolor="#F0F0FF" bordercolor="#CCCCFF" >
  
    <font face="Arial" size="1">Species</font></b></td>
    <td bgcolor="#F0F0FF" bordercolor="#CCCCFF" >

    <font face="Arial" size="1">SAP Number</font></b></td>
    <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" >
 
    <font size="1" face="Arial">Vendor</font></b></td>
    <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" >
 
    <font size="1" face="Arial">PO</font></b></td>
    <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" >
  
    <font size="1" face="Arial">Release</font></b></td>
    <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" >    <font size="1" face="Arial">Original Car/Trailer</font></b></td>
      <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" colspan="2" >    <font size="1" face="Arial">Transfer Trailer</font></b></td>

    </tr>
  <tr height = 18>
    <td  bgcolor="white" bordercolor="#CCCCFF" height="19">
   <font size="1" face="Arial"><%= strSpecies%></td>
    <td  bgcolor="white" bordercolor="#CCCCFF" height="19"><font size="1" face="Arial"><%= strSAP_Nbr%></td>
    <td  bgcolor="white"  bordercolor="#CCCCFF" height="19"><font size="1" face="Arial"><%= strVendor%></td>
    <td bgcolor="white" " bordercolor="#CCCCFF" height="19"><font size="1" face="Arial"><%= strPO%></td>
    <td bgcolor="white"  bordercolor="#CCCCFF" height="19"><font size="1" face="Arial"><%= strRelease_Nbr%></td>
    <td bgcolor="white"  bordercolor="#CCCCFF" height="19"><font size="1" face="Arial"><%= strTrailer%></td>
   <td bgcolor="white"  bordercolor="#CCCCFF" height="19" colspan="2"><font size="1" face="Arial"><b><%= strTransTrailer%></b></td>

  </tr>

  <tr>
    <td bgcolor="#F0F0FF" bordercolor="#CCCCFF" align="left" style="height: 21px"><b>
    <font size="1" face="Arial">Generator</font></b></td>
    <td bgcolor="#F0F0FF" bordercolor="#CCCCFF" align="left" style="height: 21px"><b>
    <font size="1" face="Arial">Generator City</font></b></td>
    <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" align="left" style="height: 21px"><b>
    <font size="1" face="Arial">Generator State</font></b></td>
    <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" align="left" style="height: 21px"><b>
    <font face="Arial" size="1">Receipt Number</font></b></td>
    <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" align="left" style="height: 21px"><b>
    <font face="Arial" size="1">Date Received</font></b></td>
 <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" style="height: 21px"><b>
    <font face="Arial" size="1">&nbsp;Location</font></b></td>
   <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" style="height: 21px"><b>
    <font face="Arial" size="1">&nbsp;</font></b><font size="1" face="Arial">Carrier</font></b></td>
  
   <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" class="style1" style="height: 21px">
	Deduct
	Approval</td>
  
  </tr>
  <tr>
    <td  bordercolor="#CCCCFF"><font face="Arial" size="1"><%= strGenerator%></font></td>
    <td  bordercolor="#CCCCFF"><font face="Arial" size="1"><%= strGen_City%></font></td>
    <td  bordercolor="#CCCCFF"><font face="Arial" size="1"><%= strGen_State%></font></td>
    <td  bordercolor="#CCCCFF"><font face="Arial" size="1"><%= strRec_Number%></font></td>
    <td  bordercolor="#CCCCFF"><font face="Arial" size="1"><%= strDateReceived%></font></td>
     <td  bordercolor="#CCCCFF"><font face="Arial" size="1"><%= strCarLocation%></font></td>
   <td bgcolor="white"  bordercolor="#CCCCFF" height="19"><font size="1" face="Arial"><%= strCarrier%></td>
   <td bgcolor="white"  bordercolor="#CCCCFF" height="19" class="style2">	
	<font face="Arial" size="1">	
	<% if Session("EmployeeID") = "C66556" or Session("EmployeeID") = "C97338" then %>
	<select size="1" name="Approval">
	<option <% if strApproval = "NO" then%> selected <% end if %>>NO</option>
	<option <% if strApproval = "YES" then %> selected <% end if %>>YES</option>	
	</select>
	<% else %>
	<%= strApproval %>
	<% end if %></font></td>
  </tr>
</table>
<table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" height="51" >
    <tr>
    <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="18"><b>	<font face="Arial" size="1">Date Unloaded</font></b></td>
    <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="18"><b>	<font face="Arial" size="1">Location Unloaded</font></b></td>
    <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="18"><b>	<font face="Arial" size="1">Other Comments</font></b></td>
       <td  bgcolor="#FF0000" bordercolor="#000000" height="18" align="center"><b>	
	<font face="Arial" size="1">&nbsp;</font><font color="#FFFFFF"><font face="Arial" size="1">Total Bales&nbsp;</font><font face="Arial" size="2"><br><font size = 1>Required</font></font></b></td>
   
    <td bgcolor="#F0F0FF" bordercolor="#CCCCFF" align="center"><b>	<font face="Arial" size="1">&nbsp;OCC Tech</font></b></td>
    <td align = center bgcolor="#D9FFEC" bordercolor="#CCCCFF" ><b><font size="1" face="Arial">&nbsp;Tons Received</font></b></td>
    <td align = center bgcolor="#D9FFEC" bordercolor="#CCCCFF" ><b>  <font face="Arial" size="1">Deduction</font></b> </td>
    <td  bgcolor="#D9FFEC" bordercolor="#CCCCFF" align = center>	<font face="Arial" size="1">&nbsp;Net&nbsp;</font></b></td>
   
  </tr>
  
    <tr>
    <td  bordercolor="#CCCCFF">	<font face="Arial">	<input type="text" name="Date_Unloaded" size="16" value = "<%= strDate_Unloaded%>"></font></td>
    <td bordercolor="#CCCCFF">	
	<font face="Arial">	
	<select size="1" name="OCC_Location">
	<option <% if strOCC_Location = "BALDWIN" then%> selected <% end if %>>BALDWIN</option>
	<option <% if strOCC_Location = "BROKE CENTER" then%> selected <% end if %>>BROKE CENTER</option>
	<option <% if strOCC_Location = "DFF" then%> selected <% end if %>>DFF</option>
	<option <% if strOCC_Location = "MERCHANTS" then%> selected <% end if %>>MERCHANTS</option>
	<option <% if strOCC_Location = "MMS" then%> selected <% end if %>>MMS</option>
	<option <% if strOCC_Location = "OCC" then%> selected <% end if %>>OCC</option>
	<option <% if strOCC_Location = "RF" then%> selected <% end if %>>RF</option>
	<option <% if strOCC_Location = "TM BASEMENT" then%> selected <% end if %>>TM BASEMENT</option>
	<option <% if strOCC_Location = "WHSE16" then%> selected <% end if %>>WHSE16</option>
	<option <% if strOCC_Location = "WHSE17" then%> selected <% end if %>>WHSE17</option>
	</select></font></td>
    <td bordercolor="#CCCCFF">	
	<font face="Arial">	
	<input type="text" name="Other_Comments" size="30" value = "<%= strOther_Comments%>"></font></td>
	   <td  bordercolor="#CCCCFF" align="center" bgcolor="#FF0000">	
	<font face="Arial">	
	<input type="text" name="Total_Bales" size="7" value = "<%= strTotal_bales%>"></font></td>
    
    <td  bordercolor="#CCCCFF" align="center">
	<font face="Arial" size="2">
	<%= strOCC_Tech%></font></td>
    <td  bgcolor="white" bordercolor="#CCCCFF">
	<p align="center">
	<font face="Arial">
	

	<%= strTons_received%>
	</font></td>
    <td  bgcolor="white"  bordercolor="#CCCCFF">
	<p align="center"><b> <font size="2" face="Arial">&nbsp;&nbsp;</font></b>
  <%= strDeduction%></font><b><font size="2" face="Arial">&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;</font></b></td>
    <td bgcolor="white"  bordercolor="#CCCCFF">	
	<p align="center">	
	<font face="Arial">	
	<%= strNet%></font></td>
    
  </tr>
  
  </table>
<table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" height="240">
  <tr>
    <td width="35%" bgcolor="#FFFFD7" bordercolor="#000000" height="19" colspan="6">
    <p align="center"><b><font size="2" face="Arial">Fiber Quality</font></b></td>
    <td width="21%" bgcolor="#FFFFD7" bordercolor="#000000" height="19" colspan="4">
    <p align="center"><b><font size="2" face="Arial">Bale Quality</font></b></td>
    <td width="48%" bgcolor="#FFFFD7" bordercolor="#000000" colspan="8" height="19">
    <p align="center"><b><font size="2" face="Arial">Moisture Rating</font></b></td>
  </tr>
  <tr>
  
    <td width="8%" bgcolor="#FFFFD7" bordercolor="#000000" colspan="2" height="36">
    <p align="center"><b>
    <font face="Arial" size="1"><br>Excellent</font></b></td>
    <td width="7%" bgcolor="#FFFFD7" bordercolor="#000000" colspan="2" height="36">
    <p align="center"><b>
    <font size="1" face="Arial"><br>Good</font></b></td>
    <td width="5%" bgcolor="#FFFFD7" bordercolor="#000000" height="36">
    <p align="center"><b>
    <font size="1" face="Arial"><br>Poor</font></b></td>
    <td width="7%" bgcolor="#FFFFD7" bordercolor="#000000" height="36">
    <p align="center">
    <font size="1" face="Arial">Down Grade Reject</font></td>
    <td width="5%" bgcolor="#FFFFD7" bordercolor="#000000" height="36">
    <p align="center"><b>
    <font face="Arial" size="1"><br>&nbsp;Excellent&nbsp;</font></b></td>
    <td width="6%" bgcolor="#FFFFD7" bordercolor="#000000" height="36">
    <p align="center"><b>
    <font size="1" face="Arial"><br>Good</font></b></td>
    <td width="4%" bgcolor="#FFFFD7" bordercolor="#000000" height="36">
    <p align="center"><b>
    <font size="1" face="Arial"><br>Poor</font></b></td>
    <td width="6%" bgcolor="#FFFFD7" bordercolor="#000000" height="36">
    <p align="center">
    <font size="1" face="Arial">Down Grade <br>Reject </font></td>
    <td width="8%" bgcolor="#FFFFD7" bordercolor="#000000" colspan="1" height="36" align="center"><b>
    <font size="1" face="Arial"><br>Dry</font></b></td>
    <td width="5%" bgcolor="#FFFFD7" bordercolor="#000000" height="36" align="center"><b>
    <font size="1" face="Arial"><br>Light</font></b></td>
    <td width="4%" bgcolor="#FFFFD7" bordercolor="#000000" height="36" align="center">
    <p align="center"><b><font size="1" face="Arial"><br>&nbsp;Medium&nbsp;</font></b></td>
        <td width="4%" bgcolor="#FFFFD7" bordercolor="#000000" height="36" align="center">
    <p align="center"><b><font size="1" face="Arial"><br>&nbsp;Heavy&nbsp;</font></b></td>
    <td width="29%" bgcolor="#FFFFD7" bordercolor="#000000" colspan="3" height="36">
	<p align="center"><font size = 2 face="Arial"><b>
    Heavy</b><font size = 1> - weigh a bale<br> to represent all wet bales 
    marked below</font></b></td>
  </tr>
  <tr>
   
    <td colspan = 5   align = center>
 <fieldset style="padding: 0" >
	
	&nbsp;&nbsp; <input type="radio" value="3" name="FQ" <% if strFQExcellent = -1 then %> checked<%end if %> >&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp; 
	<input type="radio" name="FQ" value="2" <% if strFQGood = -1 then %> checked <%end if %>>	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;
	<input type="radio" name="FQ" value="1" <% if strFQPoor = -1 then %>checked <%end if %>></fieldset></td>
  
    <td width="7%" height="27" align = center>
	<input type="text" name="Fiber_Quality_Reject" size="3" value = "<%= strFiber_Quality_Reject%>"></td>

    <td colspan = 3   align = center>
 <fieldset style="padding: 0">
	
	<input type="radio" value="3" name="BQ" <% if strBQExcellent = -1 then %> checked <%end if %>>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	<input type="radio" name="BQ" value="2" <% if strBQGood = -1 then %> checked<%end if %>>	&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;
	<input type="radio" name="BQ" value="1" <% if strBQPoor = -1 then %> checked <%end if %>></fieldset></td>
    <td width="6%" height="27">
	<p align="center">
	<input type="text" name="Bale_Quality_Reject" size="3" value = "<%= strBale_Quality_Reject%>"></td>
        <td colspan = 4   align = center>
 <fieldset style="padding: 0">
	
	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <input type="radio" value="3" name="MQ" <% if strMoisture_dry = -1 then %> checked <%end if %>>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	<input type="radio" name="MQ" value="2" <% if strMoisture_light = -1 then %> checked <%end if %>>	&nbsp;&nbsp;&nbsp; &nbsp;
	<input type="radio" name="MQ" value="1" <% if strMoisture_medium = -1 then %> checked <%end if %>>   &nbsp;&nbsp;&nbsp; &nbsp;
	<input type="radio" name="MQ" value="0" <% if strMoisture_heavy = -1 then %> checked <%end if %>></fieldset></td>
    <td width="15%" bgcolor="#FFFFD7" bordercolor="#000000" height="27" >
	<p align="center"><b>
    <font face="Arial" size="1">Weight of Sample Bales</font></b></td>
    <td width="12%" height="27" colspan = 2>
	<p align="center">
	<input type="text" name="Weight_Sample_Bale" size="5" value = "<%= strWeight_Sample_Bale%>"></td>
  </tr>
  <tr>
    <td width="35%" bgcolor="#FFFFD7" bordercolor="#000000" colspan="6" height="37"> <b>
    <font face="Arial" style="font-size: 9pt">Bale Count - Down Grade/Reject 
    </font></b></td>
    <td width="35%" bgcolor="#FFFFFF" bordercolor="#000000" colspan="2" height="37" align = center>
	
	<%= strBales_Rejected%></td>
 <td width="38%" bgcolor="#FFFFD7" bordercolor="#000000" colspan="6" height="37">&nbsp;</td>
    <td width="15%" bgcolor="#FFFFD7" bordercolor="#000000" height="37">
	<p align="center"><b>
    <font size="1" face="Arial">Number of Bales to Apply</font></b></td>
    <td width="12%" height="37" colspan = 2>
	<p align="center">
	<input type="text" name="Nbr_Wet_Bales" size="5" value = "<%= strNbr_Wet_Bales%>"></td>
  </tr>


  <tr>
    <td width="68%" colspan="14" bgcolor="#FFFFD7" height="25">
    <p align="center"><b><font face="Arial" style="font-size: 9pt">Contaminants</font></b></td>
    <td width="4%" bgcolor="#FFFFD7" height="25" bordercolor="#FFFFD7">&nbsp;</td>
    <td width="29%" bgcolor="#FFFFD7" bordercolor="#FFFFD7" colspan="2" height="25">&nbsp;</td>
  </tr>
  <tr>
    <td width="7%" bgcolor="#FFFFD7" height="40" align="center"><b><font face="Arial" size="1">
    Ground Wood</font></b></td>
    <td width="11%" colspan="2" bgcolor="#FFFFFF" height="40" align = center>
    <input type="checkbox" name="Ground_Wood" value="ON" <% if strGround_wood = -1 then%> checked <%end if %>></td>
    <td width="6%" colspan="2" bgcolor="#FFFFD7" height="40" align="center"><b><font face="Arial" size="1">
    Wet Strength</font></b></td>
    <td width="4%" colspan="1" bgcolor="#FFFFFF" height="40" align = center>
    <input type="checkbox" name="Wet_Strength" value="ON" <% if strWet_Strength = -1 then%> checked <%end if %>></td>
  
    <td width="7%" bgcolor="#FFFFD7" height="40" align="center"><b><font face="Arial" size="1">
    Cardboard</font></b></td>
    <td width="5%" colspan="1" bgcolor="#FFFFFF" height="40" align = center>
    <input type="checkbox" name="Cardboard" value="ON" <% if strCardboard = -1 then%> checked <% end if %>></td>
    <td width="6%" bgcolor="#FFFFD7" height="40" align="center"><b><font face="Arial" size="1">
    Beater Dyed</font></b></td>
    <td width="4%" colspan="1" bgcolor="#FFFFFF" height="40" align = center>
    <input type="checkbox" name="Beater_Dyed" value="ON" <% if strBeater_Dyed = -1 then%> checked <%end if %>></td>
  
    <td width="6%" bgcolor="#FFFFD7" height="40" align="center"><b><font face="Arial" size="1">
	Newsprint</font></b></td>
    <td width="3%" colspan="1" bgcolor="#FFFFFF" height="40" align = center>
    <input type="checkbox" name="Newsprint" value="ON" <% if strNewsprint = -1 then%> checked <%end if %>></td>
    <td width="5%" bgcolor="#FFFFD7" height="40" align="center" colspan="2">&nbsp;</td>
  <td width="5%" bgcolor="#FFFFD7" height="40" bordercolor="#FFFFD7">&nbsp;</td>
    <td width="4%" bgcolor="#FFFFD7" height="40" bordercolor="#FFFFD7">&nbsp;</td>
    <td width="29%" bgcolor="#FFFFD7" bordercolor="#FFFFD7" colspan="2" height="40">&nbsp;</td>
  </tr>

  <tr>
    <td width="7%" bgcolor="#FFFFD7" height="31" align="center"><b><font face="Arial" size="1">
    Plastic</font></b></td>
    <td width="11%" colspan="2" bgcolor="#FFFFFF" height="31" align = center>
    <input type="checkbox" name="Plastics" value="ON" <% if strPlastics = -1 then%> checked <%end if %>></td>
    <td width="6%" colspan="2" bgcolor="#FFFFD7" height="31" align="center"><b><font face="Arial" size="1">
    Trash</font></b></td>
    <td width="4%" colspan="1" bgcolor="#FFFFFF" height="31" align = center>
    <input type="checkbox" name="Trash" value="ON" <% if strTrash = -1 then%> checked <%end if %>></td>
    <td width="7%" bgcolor="#FFFFD7" height="31" align="center"><b>
	<font face="Arial" size="1">Brightness</font></b></td>
    <td width="5%" colspan="1" bgcolor="#FFFFFF" height="31" align = center>
    <input type="checkbox" name="Brightness" value="ON" <% if strBrightness = -1 then%> checked <%end if %>></td>
    <td width="6%" bgcolor="#FFFFD7" height="31" align="center"><b>
	<font face="Arial" size="1">Dirt</font></b></td>
    <td width="4%" colspan="1" bgcolor="#FFFFFF" height="31" align = center>
    <input type="checkbox" name="Dirt" value="ON" <% if strDirt = -1 then%> checked <%end if %>></td>
       <td width="5%" bgcolor="#FFFFD7" height="40" align="center"><b>
	<font face="Arial" size="1">Envelopes</font></b></td>
    <td width="5%" colspan="1" bgcolor="#FFFFFF" height="40" align = center>
    <input type="checkbox" name="Envelopes" value="ON" <% if strEnvelopes = -1 then%> checked <%end if %>></td>
     <td width="5%" bgcolor="#FFFFD7" height="40" align="center"><b>
	<font face="Arial" size="1">Adhesives</font></b></td>
   <td width="5%" colspan="1" bgcolor="#FFFFFF" height="40" align = center>
    <input type="checkbox" name="Adhesive" value="ON" <% if strAdhesive = -1 then%> checked <%end if %>></td>
    <td width="4%" bgcolor="#FFFFD7" height="40" bordercolor="#FFFFD7">&nbsp;</td>
    <td width="29%" bgcolor="#FFFFD7" bordercolor="#FFFFD7" height="40">&nbsp;</td>
  </tr>
</table>
   <table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" >
  <tr>
    <td  bgcolor="#FFFFD7" height="19" bordercolor="#000000" align = center>
  <b><font size="2" face="Arial">Fiber Quality Comments</font></b></td>
    <td  bgcolor="#FFFFD7" height="19" bordercolor="#000000" align = center>
   <b><font face="Arial" size="2">Bale Quality Comments</font></b></td>
       <td  bgcolor="#FFFFD7" height="19" bordercolor="#000000" align = center>
        <b><font size="2" face="Arial">Moisture Rating Comments</font></b></td>
  </tr>



  <tr>
    <td  bgcolor="#FFFFFF" height="45">
    <textarea rows="3" name="Fiber_Comments" cols="40"><%= strFiberComments%></textarea></td>
    
    

    <td bgcolor="#FFFFFF" bordercolor="#000000"  height="45">
    <textarea rows="3" name="Bale_Comments" cols="38"><%= strBaleComments%></textarea></td>
  
    <td  bgcolor="#FFFFFF" bordercolor="#000000" height="45">
      <p align="center">
      <textarea rows="3" name="Moisture_Comments" cols="35"><%= strMoistureComments%></textarea></td>

  </tr>

</table>
</form>
  <%
  Function getData() 
  strid = Request.querystring("id")
  strSql="SELECT tblCars.* FROM tblCars WHERE CID= '" & strid & "'" 
  
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString")
  
  strid = Request.querystring("id")
  strSpecies = MyRec.Fields("Species")
  strSAP_Nbr = MyRec.Fields("SAP_Nbr")
  strVendor = MyRec.Fields("Vendor")
  strPO = MyRec.Fields("PO")
  strRelease_Nbr = MyRec.Fields("Release_Nbr")
  strTrailer = MyRec.Fields("Trailer")
  strTons_Received = MyRec.Fields("Tons_Received")
  strGenerator = MyRec.Fields("Generator")
  strGen_City = MyRec.Fields("Gen_City")
  strGen_State = MyRec.Fields("Gen_State")
  strREC_Number = MyRec.Fields("REC_Number")
  strDateReceived = MyRec.Fields("Date_received")
  strOCC_Tech = Session("Ename")
  strDate_Unloaded = formatdatetime(Now(),2)
  strCarLocation = MyRec.fields("Location")
  strOther_Comments = MyRec.fields("Other_Comments")
  strCarrier = MyRec.fields("Carrier")
  strTons_received = MyRec.fields("Tons_received")
  strDeduction= MyRec.fields("Deduction")
  strNet= MyRec.fields("Net")
  strTransTrailer = MyRec.fields("Transfer_trailer_nbr")
      strOCC_Location = MyRec.fields("Location")
  
  MyRec.close
  
     strSql="SELECT tblOCCGrading.* FROM tblOCCGrading WHERE CID= '" & strid & "'" 
  
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
  strTotal_Bales = MyRec.fields("Trans_total_Bales")
  strOCC_Tech = MyRec.fields("Trans_OCC_Tech")
  strFiberComments = MyRec.fields("Trans_Fiber_comments")
  strBale_Comments = MyRec.fields("Trans_Bale_Comment")
  strMoisture_Comments = MyRec.fields("Trans_Moisture_Comment")
    
  strFiber_Quality_Reject = MyRec.fields("Trans_Fiber_Quality_Reject")
  strBale_Quality_Reject = MyRec.fields("Trans_Bale_Quality_Reject")
  strWeight_Sample_Bale = MyRec.fields("Trans_Weight_Sample_Bale")
  strNbr_wet_bales = MyRec.fields("Trans_Nbr_wet_bales")
  strBales_rejected = MyRec.fields("Trans_Bales_Rejected")
  strGround_Wood = MyRec.fields("Trans_Ground_Wood")
  strCardboard = MyRec.fields("Trans_Cardboard")
  strNewsprint = MyRec.fields("Trans_Newsprint")
  strPlastics = MyRec.fields("Trans_Plastics")
  strBrightness = MyRec.fields("Trans_Brightness")
  strWet_Strength = MyRec.fields("Trans_Wet_Strength")
  strBeater_Dyed = MyRec.fields("Trans_Beater_Dyed")
  strEnvelopes = MyRec.fields("Trans_Envelopes")
  strTrash = MyRec.fields("Trans_Trash")
  strDirt = MyRec.fields("Trans_Dirt")
  strAdhesive = MyRec.fields("Trans_Adhesive")
  strBQExcellent = MyRec.fields("Trans_BQ_Excellent")
  strBQGood = MyRec.fields("Trans_BQ_Good")
  strBQPoor = MyRec.fields("Trans_BQ_Poor")
  strFQExcellent = MyRec.fields("Trans_FQ_Excellent")
  strFQGood = MyRec.fields("Trans_FQ_Good")
  strFQPoor = MyRec.fields("Trans_FQ_Poor")
  strMoisture_Dry = MyRec.fields("Trans_Moisture_dry")
  strMoisture_Light= MyRec.fields("Trans_Moisture_Light")
  strMoisture_Medium= MyRec.fields("Trans_Moisture_Medium")
  strMoisture_Heavy= MyRec.fields("Trans_Moisture_Heavy")
  strApproval = MyRec.fields("Deduct_approval")
  strOFQ = cint(MyRec.fields("Fiber_Quality_Excellent")) + cint(MyRec.fields("Fiber_Quality_Good")) + cint(MyRec.fields("Fiber_Quality_Poor"))
  
  MyRec.close
  
  End Function
  
  Function SaveData()
  
    strid = Request.querystring("id")
  strTrailer = Request.querystring("t")
  
  strLocation = request.Form("OCC_Location")
  strOther_Comments = Replace(request.Form("Other_Comments"), "'", "''") 
  strGradingDate_Received = request.Form("Date_Unloaded")
  
 
    strOCC_Tech = Session("Ename")

  strFiber_Quality_Reject = request.Form("Fiber_Quality_Reject")
  strBale_Quality_Reject = request.Form("Bale_Quality_Reject")
  
  strWeight_Sample_Bale = request.Form("Weight_Sample_Bale")
  if strWeight_Sample_Bale = "" or isnull(strWeight_Sample_Bale) then
  strWeight_Sample_Bale = 0
  end if 
  
  strNbr_Wet_Bales = request.Form("Nbr_Wet_Bales")
  if strNbr_Wet_Bales = "" or isnull(strNbr_Wet_Bales) then
  strNbr_Wet_Bales = 0
  end if 
  
 

  strFiberComments = Replace(request.Form("Fiber_Comments"), "'", "''") 
  strBale_Comments = Replace(request.Form("Bale_Comments"), "'", "''") 
  strMoisture_Comments = Replace(request.Form("Moisture_Comments"), "'", "''") 
  
  If Request.form("FQ") = "3" then
  strFQExcellent = 3
  strFQE = 1
  else
  strFQExcellent = 0
  strFQE = 0
  end if 
  
  If Request.form("FQ") = "2" then
  strFQGood = 2
  strFQG = 1
  else
  strFQGood = 0
  strFQg = 0
  end if 
  
  If Request.form("FQ") = "1" then
  strFQPoor = 1
  strFQP = 1
  else
  strFQPoor = 0
  strFQP = 0
  end if 
  
  If Request.form("BQ") = "3" then
  strBQExcellent = 3
  strBQE = 1
  else
  strBQExcellent = 0
  strBQE = 0
  end if 
  
  If Request.form("BQ") = "2" then
  strBQGood = 2
  strBQG = 1
  else
  strBQGood = 0
  strBQG = 0
  end if 
  
  If Request.form("BQ") = "1" then
  strBQPoor = 1
  strBQP = 1
  else
  strBQPoor = 0
  strBQP = 0
  end if 
  
  
  
  If Request.form("MQ") = "3" then
  strMoisture_Dry = 3
  strMQD = 1
  else
  strMoisture_Dry = 0
  strMQD = 0
  end if 
  
  If Request.form("MQ") = "2" then
  strMoisture_Light = 2
  strMQL = 1
  else
  strMoisture_Light= 0
  strMQL= 0
  end if 
  
  If Request.form("MQ") = "1" then
  strMoisture_Medium  = 1
  strMQM = 1
  else
  strMoisture_Medium  = 0
  strMQM = 0
  end if 
  
  If Request.form("MQ") = "0" then
  strMoisture_Heavy  = 1
  strMQH = 1
  else
  strMoisture_Heavy  = 0
  strMQH = 0
  end if 
  

  If request.form("Ground_Wood") = "ON" then
  strGround_Wood = 1
  else
  strGround_Wood = 0
  end if
  
  If request.form("Cardboard") = "ON" then
  strCardboard = 1
  else
  strCardboard = 0
  end if
  
  If request.form("Newsprint") = "ON" then
   strNewsprint = 1
  else
   strNewsprint = 0
  end if
  
  If request.form("Plastics") = "ON" then
   strPlastics = 1
  else
   strPlastics = 0
  end if
  
    If request.form("Brightness") = "ON" then
   strBrightness = 1
  else
   strBrightness = 0
  end if
  
  If request.form("Adhesive") = "ON" then
   strAdhesive = 1
  else
   strAdhesive = 0
  end if
  
  If request.form("Wet_Strength") = "ON" then
   strWet_Strength = 1
  else
   strWet_Strength = 0
  end if
  
   If request.form("Beater_Dyed") = "ON" then
   strBeater_Dyed = 1
  else
   strBeater_Dyed = 0
  end if
 
 
  If request.form("Envelopes") = "ON" then
   strEnvelopes = 1
  else
   strEnvelopes = 0
  end if
  
   If request.form("Trash") = "ON" then
   strTrash = 1
  else
   strTrash = 0
  end if
  
   If request.form("Dirt") = "ON" then
   strDirt = 1
  else
   strDirt = 0
  end if
  
  strTotal_Bales = request.Form("Total_Bales")
  If strTotal_bales = "" or isnull(strTotal_Bales) then
  strTotal_Bales = 0
  end if
  
  If isnull(strFiber_Quality_Reject) or strFiber_Quality_Reject = "" then
  strFiber_Quality_Reject = 0
  end if 
  
  If isnull(strBale_Quality_Reject) or strBale_Quality_Reject = "" then
  strBale_Quality_Reject = 0
  end if 
  
  strFQRating = cint(StrFQExcellent) + cint(strFQGood) + cint(strFQPoor)
    strBQRating = cint(StrBQExcellent) + cint(strBQGood) + cint(strBQPoor)
  strBales_Rejected = cint(strFiber_Quality_Reject) + cint(strBale_Quality_Reject)
  
 strsql = "Update tblOCCGrading set trans_OCC_Tech = '" & strOCC_Tech & "', FQ_Rating = " & strFQRating & ", "_
  &" trans_Date_received = '" & strGradingDate_Received & "', trans_Fiber_Quality_Reject = " & strFiber_Quality_Reject & ", "_
  &" trans_Weight_Sample_Bale = " & strWeight_Sample_Bale & ", trans_Nbr_wet_bales = " & strNbr_wet_Bales & ", Trans_Total_Bales = " & cint(strTotal_Bales) & ", "_
  &" trans_Bale_Quality_Reject = " & strBale_Quality_Reject & ", trans_Fiber_Comments = '" & strFiberComments & "', "_ 
  &" trans_Bale_Comment = '" & strBale_Comments & "', trans_Moisture_Comment = '" & strMoisture_Comments & "', "_
  &" Trans_Fiber_Quality_Excellent = " & cint(strFQExcellent) & ", Trans_Fiber_Quality_Good = " & cint(strFQGood) & ", "_
  &" Trans_Fiber_Quality_Poor = " & cint(strFQPoor) & ",  Trans_Bale_Quality_Excellent =  " & cint(strBQExcellent) & ", "_
  &" Trans_Bale_Quality_Good = " & cint(strBQGood) & ", Trans_Bale_Quality_Poor = " & cint(strBQPoor) & ", Trans_Bales_rejected = " & cint(strBales_rejected) & ", "_
  &" Trans_FQ_Excellent = " & strFQE & ", Trans_FQ_Good = " & strFQG & ", Trans_FQ_Poor = " & strFQP & ", "_
  &" Trans_BQ_Excellent = " & strBQE & ", Trans_BQ_Good = " & strBQG & ", Trans_BQ_Poor = " & strBQP & ", "_
  &" Trans_Moisture_Dry = " & strMQD & ", Trans_Moisture_Light = " & strMQL & ", Trans_Moisture_Medium = " & strMQM & ", Trans_Moisture_Heavy = " & strMQH & ", "_
  &" Trans_Ground_Wood = " & strGround_wood & ", Trans_Cardboard = " & strCardboard & ", Trans_Newsprint = " & strNewsprint & ", Trans_Plastics = " & strPlastics & ", "_
  &" Trans_Brightness = " & strBrightness & ", Trans_Adhesive = " & strAdhesive & ", Trans_Wet_strength = " & strWet_strength & ", Trans_Beater_dyed = " & strBeater_dyed & ", "_
  &" Trans_Envelopes = " & strEnvelopes & ", Trans_Trash = " & strTrash & ", Trans_Dirt = " & strDirt & " where CID = " & strid & ""
  

  
  	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
  	 if Session("EmployeeID") = "C66556" or Session("EmployeeID") = "C97338" then 
  strApproval = Request.form("Approval")
 strsql = "Update tblOCCGrading set  Deduct_approval = '" & strApproval & "' where CID = " & strid & ""
   set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         end if

 If request.querystring("o") = 0 then
  strsql = "Update tblOCCGrading set BQ_Rating = " & strBQRating & "  where CID = " & strid & ""
  
  	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
end if

strsql = "Update tblCars set Trans_unload_date = '" & strDate_unloaded & "', Other_comments = '" & strOther_comments & "', Location = '" & strLocation & "' where CID = " & strid & ""
   	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
         If strLocation = "RF" or strLocation = "OCC" or  strLocation = "BROKE CENTER" or strLocation = "WHSE17" then
 strsql = "Update tblCars set Inv_depletion_date = '" &  strDate_unloaded & "' where CID = " & strid & ""
 
 	  set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql
         end if
         
         Response.redirect("RF_Grading_list.asp")
  End Function
  
 
   %>            
  

</body>

</html>
<!--#include file="Fiberfooter.inc"-->