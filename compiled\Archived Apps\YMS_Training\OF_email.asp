<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>OF Recap</title>
</head>
<%
dim strbody, strCID, strECarrier, strETrailer, strELoad, strEDoc, strEDC, strEDCName, strEAdd, strECity, strEState
dim strECC, strEBCC, strESpecies, MyRec, strsql, MyConn2


strNow = formatdatetime(Now(),2)
strnow = dateadd("d", -7, strNow)
strnow = datepart("y", strnow)
stryear = datepart("yyyy", now())

 
             strEmailTo = "<EMAIL>"      

 strEBCC = "<EMAIL>"

                 
                 
			Set objMail = Server.CreateObject("CDO.Message")		
			objMail.From = "<EMAIL>"
			objMail.To = strEmailTo
		 
		objMail.BCC = strEBCC
	
			objMail.Subject = "OF Recap"
			
strBody2 = "<table border=0  cellpadding=0 cellspacing=0 width=800><tr><td align = center><font face = arial size = 2>&nbsp; Date Consumed&nbsp; </td><td align = left><font face = arial size = 2># Loads&nbsp; </td><td align = left><font face = arial size = 2> % OF Total </td><td align = left><font face = arial size = 2> OF Tons </td><td align = left><font face = arial size = 2> Total RFF Tons</tr>"
			

strsql2 = "SELECT datepart(y, [Inv_depletion_date]) AS Expr1, Datepart(d, inv_depletion_date) as Inv_Date, Datepart(m, inv_depletion_date) as Inv_month, Sum(tblCars.Net) AS SumOfNet, Count(tblCars.CID) AS CarCount FROM tblCars "_
&" WHERE (Species='OF'  or Species = 'KCOP' or Species = 'PMX' or Species = 'KBLD' or Species = 'SHRED' or Species = 'OF3' or Species = 'HBX')  and RC_transload is null"_
&" GROUP BY datepart(y, [Inv_depletion_date]), Datepart(d, inv_depletion_date), Datepart(m, inv_depletion_date), datepart(yyyy, inv_depletion_date) "_
&" HAVING datepart(y, [Inv_depletion_date]) > '" & strNow & "' and datepart(yyyy, inv_depletion_date) = '" & stryear & "' "_
&" ORDER BY datepart(y, [Inv_depletion_date])"

   	 Set MyRec2 = Server.CreateObject("ADODB.Recordset")
   	 MyRec2.Open strSQL2, Session("ConnectionString")

If not MyRec2.eof then
				While not MyRec2.EOF
strConsumptionDate = MyRec2.fields("Inv_month")  & "/" & MyRec2.fields("Inv_date")
strKcopTons = round(myRec2.fields("SumofNet"),0)
strday = MyRec2.fields("Inv_date")
strMonth = MyRec2.fields("Inv_month")		
			
strsql = "SELECT datepart(y, [Inv_depletion_date]) AS Expr1, Datepart(d, inv_depletion_date) as Inv_Date, Datepart(m, inv_depletion_date) as Inv_month, Sum(tblCars.Net) AS SumOfNet, Count(tblCars.CID) AS CarCount FROM tblCars "_
&" WHERE (Species='OF'  or Species = 'OF3') "_
&" GROUP BY datepart(y, [Inv_depletion_date]), Datepart(d, inv_depletion_date), Datepart(m, inv_depletion_date), datepart(yyyy, inv_depletion_date) "_
&" HAVING Datepart(d, inv_depletion_date) = '" & strDay & "' and Datepart(m, inv_depletion_date) = '" & strMonth & "' and datepart(yyyy, inv_depletion_date) = '" & stryear & "'"

   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")	 
  	 
			
If not MyRec.eof then
     
    strOFTons = round(MyRec.fields("SumofNet"),0) 
     strLoads = MyRec.fields("CarCount")  
     strpct = round((strOFtons/strKcopTons)*100,0) & "%"
	else
	  

     strOFTons = "0" 
    strLoads = "0" 
    strpct = "0%"
	
	end if
	MyRec.close		

  strbody2 = strbody2 & " <tr><td align = center><font face = arial size = 2> " & strConsumptiondate &  "</td><td align = left><font face = arial size = 2>" & strLoads & "</td><td align = left><font face = arial size = 2>"  & strpct & "</td><td align = left><font face = arial size = 2>"  & strOFtons & "</td><td align = left><font face = arial size = 2>"  & strKCOPtons & "</td></tr> "
		    

	MyRec2.MoveNext
     			WEND
           		MyRec2.Close
           	
	
	objMail.HTMLBody = "<font face = arial size = 2>The following is a recap of the OF loads consumed in the past week:<br><br><p align=left><b>Note:  These numbers are based on the trailers/cars outed on shift by Fiber technicians.  The numbers can change based on validation work or subsequent changes to entries. </p></b><br><br>" & strbody2



		' 	objMail.Send
			Set objMail = nothing
	end if
 
  Response.redirect("Send_yard_email.asp")

 %>

