																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Adjust Receipt for Release Number</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->


<% Dim MyRec, strsql, MyRec2, strsql2,  MyConn3, strsql3,  strRelease, strVendor, strGenerator, strState, strCity, strBD

 

  set objGeneral = new ASP_CLS_General
  strid = Request.querystring("id")
  
  if objGeneral.IsSubmit() Then	
  
   strsql = "Select * from tblCars where RElease_nbr = '" & strid & "'"
  
      Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then	
strPO = MyRec("PO")
strNetOrg = MyRec("Net")
strTonsOrg = MyRec("Tons_received")

if len(MyRec("Deduction")) > 0 then
	strDeductionOrg = MyRec("Deduction")
	else
	strDeductionOrg = 0
	end if
 
end if
MyRec.close


 strCID = request.form("CID")
strNet = cdbl(Request.form("Net"))
strDeduction = cdbl(Request.form("Deduction"))
strTons = cdbl(Request.form("Tons_received"))
strReason = Replace(request.Form("Reason"), "'", "''")
strdate = formatdatetime(now(),2)
 
 
			
 
			strsql = "Update tblCars set Net = " & strNet & " , Deduction = " & strDeduction & ", Tons_Received = " & strTons & "  where CID = " & strCid
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			'response.write strsql
			MyConn.Close
		 
strsql = "Insert into tblChangeLog (CID, Emp_ID, Emp_Name, Net, Net_Changed,  Tons_Original, Tons, Deduction, Deduction_org, Release, PO, Reason, Change_date) "_
&" Select " & strCID & ", '" & Session("EmployeeID") & "', 'Use Employee ID', " & strNetOrg & ", " & strNet & ", " & strTonsOrg & ", " & strTons & ", "_
&" " & strDeduction & ", " & strDeductionOrg & ", '" & strid & "', '" & strPO & "', '" & strReason & "', '" & strdate & "'"

	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			'response.write strsql
			MyConn.Close

 
Response.redirect("Select_receipt_adj.asp")	
end if

 
%>

<style type="text/css">
.style3 {
	border: 1px solid #000000;
}
.style6 {
	border: 1px solid #808080;
	font-family: arial;
	font-size: x-small;
	background-color: #FFFFD7;
	text-align: center;
}
.style7 {
	border: 1px solid #808080;
	font-family: arial;
	font-size: x-small;
	background-color: #A4E3B9;
	text-align: center;
}
.style8 {
	text-align: center;
	border: 1px solid #808080;
	background-color: #A4E3B9;
}
.style9 {
	text-align: center;
	border: 1px solid #808080;
}
.style10 {
	font-family: Arial, Helvetica, sans-serif;
}
</style>
</head>

<body>
<br>
<% If session("EMployeeID") = "C97338" or Session("EmployeeID") = "U17097" or Session("EmployeeID") = "B55404" then %>	
<form name="form1" action="Release_adjust.asp?id=<%= strid%>" method="post" ID="Form1">
 

<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><strong>Adjust Receipt for Release Number<br>
<br>
Note:&nbsp; If you see more than one row here for the same Release number, do 
not us this web page - get the duplicate fixed.</strong></font></td>
<td align = right><font face="Arial"><a href="javascript:history.go(-1);">
<strong>RETURN</strong></a><strong>&nbsp;</strong></td>


</tr>
	    </table>



	<p>&nbsp;</p>
	<table id="table1" style="width: 95%" align="center" class="style3">
<tr bgcolor = #CCCCFF>
<td bgcolor="#FFFFD7" class="style6" style="height: 48px">Receipt ID</td>

		<td bgcolor="#FFFFD7" class="style6" style="height: 48px">PO #</td>
		<td bgcolor="#FFFFD7" class="style6" style="height: 48px">Release #</td>
		<td bgcolor="#FFFFD7" class="style6" style="height: 48px">Original Received 
		weight</td>
		<td bgcolor="#FFFFD7" class="style6" style="height: 48px">&nbsp;Net</td>
		<td bgcolor="#FFFFD7" class="style7" style="height: 48px">New Net</td>
		<td bgcolor="#FFFFD7" class="style6" style="height: 48px">Deduction</td>
		<td bgcolor="#FFFFD7" class="style7" style="height: 48px">New Deduction</td>
		<td bgcolor="#FFFFD7" class="style6" style="height: 48px">Tons Received</td>
		<td bgcolor="#FFFFD7" class="style7" style="height: 48px">New Tons 
		Received</td>
		<td bgcolor="#FFFFD7" class="style6" style="height: 48px">Reason for 
		Change</td>
	</tr>
	<%   strsql = "Select * from tblCars where RElease_nbr = '" & strid & "'"
  
      Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then	
	while not MyRec.eof
	if len(MyRec("Deduction")) > 0 then
	strDeduction = MyRec("Deduction")
	else
	strDeduction = 0
	end if %>
	<tr>
	<td class="style9"><font face = arial size = 2>	<input type="hidden" value='<%= MyRec("CID") %>' name="CID">	 <%= MyRec.fields("CID") %> </td>

		<td class="style9"><font face = arial size = 2>		 <%= MyRec.fields("PO") %> </td>
		<td class="style9"><font face = arial size = 2>		 <%= MyRec.fields("Release_nbr") %> </td>
		<td class="style9"><font face = arial size = 2>	 <%= MyRec.fields("Status") %> </td>
		<td class="style9"><font face = arial size = 2><%= MyRec.fields("Net") %>&nbsp;</td>
		<td class="style8"><font face = arial size = 2><input type="text" name="net" size="39" style="width: 65px" value='<%= MyRec.fields("Net") %>' ></td>
		<td class="style9"><font face = arial size = 2><%= MyRec.fields("Deduction") %>&nbsp;</td>
		<td class="style8">		<input type="text" name="Deduction" size="20" style="width: 81px" value='<%= strDeduction %>'  ></td>
		<td class="style9"><font face = arial size = 2><%= MyRec.fields("Tons_received") %>	&nbsp;</td>
		<td class="style8"> 	<input type="text" name="Tons_received" size="20" style="width: 81px" value='<%= MyRec.fields("Tons_received") %>'  ></td>
		<td class="style9"><font face="Arial" size="1"> <textarea name="Reason" rows="3"   style="width: 344px"> </textarea></font></td>
	</tr>
	<% Myrec.movenext
	wend
	MyRec.close
	end if  %>
</table>
<br>
		

<p align="center"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></p>
</form>
<% else %>
<p class="style10"><strong>You do not have authorization to this page</strong></p>
<% end if %><!--#include file="Fiberfooter.inc"-->