 

<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Fibers Waste Paper Consumption Report based on YMS Outings</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

<%  
	Dim strsQL, rstEquip

   	Dim objEquipSearch, rstTotals, strCount, strDed, strNet, strTons, rstTotalReceived, strTR, objNew, rstSpecies
  	Dim  strVendor, strBegDate, strEndDate, strSpecies, strDateReceived, strDateAdded, strAVG, strKCOPTotal, strOCCTotal
 	Dim  rstVendor, strFIQ, strBAQ, rstGenerator, strGenerator, strVendorname, strGeneratorname
  	Dim objGeneral, gcount, strCountTwo, strSum, strSum1, strNetSum, strDepdate, strEdate, strBdate, strBegTime, strEndTime
   	
   	Dim intDirection
  	Dim intPageNumber
   	Dim strPageNav

   intPageNumber = 1

   set objGeneral = new ASP_CLS_General

  if objGeneral.IsSubmit() Then
    Call GetFormData()
        Call LoadSearchResults()
  end if
Call GetData()
%>



<style type="text/css">
.style1 {
	font-size: x-small;
}
.style3 {
	font-weight: bold;
	border-width: 1px;
}
.style4 {
	font-family: Arial, Helvetica, sans-serif;
}
.style5 {
	font-family: Arial, Helvetica, sans-serif;
	border-width: 1px;
	background-color: #FFFFCC;
}
.style6 {
	font-family: Arial, Helvetica, sans-serif;
	border-width: 1px;
	background-color: #FFECEC;
}
.style7 {
	font-family: Arial, Helvetica, sans-serif;
	border-width: 1px;
	background-color: #EAFFFF;
}
.style8 {
	font-family: Arial, Helvetica, sans-serif;
	text-align: center;
}
.auto-style1 {
	font-family: Arial, Helvetica, sans-serif;
	border-width: 1px;
	background-color: #FFFFEA;
}
.auto-style3 {
	text-align: center;
}
.auto-style4 {
	text-align: left;
}
.auto-style5 {
	font-family: Arial, Helvetica, sans-serif;
	border-width: 1px;
	background-color: #FFFFEA;
	text-align: center;
}
</style>
</head>


<form name="form1" action="Trailer_Arrival_Summary.asp" method="post">
 
<TABLE borderColor=white cellSpacing=0 cellPadding=0  width = 100% border=1 >  
  <tr>
  <td class="auto-style3"><font face = "Arial"><b>Fibers Waste Paper 
  Consumption/Arrival Report</b> </font></td>
	<td align = right>&nbsp;</td></tr>
</table>

 

<TABLE>  

  <TR>
   
<TD class="style3" ><font face="Arial" size="2">Beg Date:</font></TD>
<TD class="style3" ><font face="Arial"><input name="Beg_Date" size="10" maxlength="10" value="<%=strBDate%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633">
<font face="Arial" size="2">	&nbsp;<strong> Time: </strong> &nbsp;
<select size="1" name="Beg_time">
<option <% if strBegTime = "1:00:00 AM" then %> selected <% end if %> >1:00:00 AM</option>
<option <% if strBegTime = "2:00 AM" then %> selected <% end if %> >2:00 AM</option>
<option <% if strBegTime = "3:00 AM" then %> selected <% end if %> >3:00 AM</option>
<option <% if strBegTime = "4:00 AM" then %> selected <% end if %> >4:00 AM</option>
<option <% if strBegTime = "5:00 AM" then %> selected <% end if %> >5:00 AM</option>
<option <% if strBegTime = "6:00 AM" then %> selected <% end if %> >6:00 AM</option>
<option <% if strBegTime = "7:00 AM" then %> selected <% end if %> >7:00 AM</option>
<option <% if strBegTime = "8:00 AM" then %> selected <% end if %> >8:00 AM</option>
<option <% if strBegTime = "9:00 AM" then %> selected <% end if %> >9:00 AM</option>
<option <% if strBegTime = "10:00 AM" then %> selected <% end if %> >10:00 AM</option>
<option <% if strBegTime = "11:00 AM" then %> selected <% end if %> >11:00 AM</option>
<option <% if strBegTime = "12:00 PM" then %> selected <% end if %> >12:00 PM</option>
<option <% if strBegTime = "1:00 PM" then %> selected <% end if %> >1:00 PM</option>
<option <% if strBegTime = "2:00 PM" then %> selected <% end if %> >2:00 PM</option>
<option <% if strBegTime = "3:00 PM" then  %> selected <% end if %> >3:00 PM</option>
<option <% if strBegTime = "4:00 PM" then %> selected <% end if %> >4:00 PM</option>
<option <% if strBegTime = "5:00 PM" then  %> selected <% end if %> >5:00 PM</option>
<option <% if strBegTime = "6:00 PM" then  %> selected <% end if %> >6:00 PM</option>
<option <% if strBegTime = "7:00 PM" then  %> selected <% end if %> >7:00 PM</option>
<option <% if strBegTime = "8:00 PM" then%> selected <% end if %> >8:00 PM</option>
<option <% if strBegTime = "9:00 PM" then%> selected <% end if %> >9:00 PM</option>
<option <% if strBegTime = "10:00 PM" then %> selected <% end if %> >10:00 PM</option>
<option <% if strBegTime = "11:00 PM" then %> selected <% end if %> >11:00 PM</option>
<option <% if strBegTime = "12:00 AM" then %> selected <% end if %> >12:00 AM</option>

     </select></TD></tr></table>
     <table><tr>
     <td class="style3"><font size="2" face="Arial">  End Date: </font></td>
   <TD class="style3"   ><font face="Arial"><input name="End_Date" size="10" maxlength="10" value="<%=strEDate%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633">&nbsp;&nbsp;&nbsp;<strong><span class="style1">Time</span></strong>:&nbsp;   
		<select size="1" name="End_time">
<option <% if strEndTime = "1:00:00 AM" then %> selected <% end if %> >1:00:00 AM</option>
<option <% if strEndTime = "2:00 AM" then %> selected <% end if %> >2:00 AM</option>
<option <% if strEndTime = "3:00 AM" then %> selected <% end if %> >3:00 AM</option>
<option <% if strEndTime = "4:00 AM" then %> selected <% end if %> >4:00 AM</option>
<option <% if strEndTime = "5:00 AM" then %> selected <% end if %> >5:00 AM</option>
<option <% if strEndTime = "6:00 AM" then %> selected <% end if %> >6:00 AM</option>
<option <% if strEndTime = "7:00 AM" then %> selected <% end if %> >7:00 AM</option>
<option <% if strEndTime = "8:00 AM" then %> selected <% end if %> >8:00 AM</option>
<option <% if strEndTime = "9:00 AM" then %> selected <% end if %> >9:00 AM</option>
<option <% if strEndTime = "10:00 AM" then %> selected <% end if %> >10:00 AM</option>
<option <% if strEndTime = "11:00 AM" then %> selected <% end if %> >11:00 AM</option>
<option <% if strEndTime = "12:00 PM" then %> selected <% end if %> >12:00 PM</option>
<option <% if strEndTime = "1:00 PM" then %> selected <% end if %> >1:00 PM</option>
<option <% if strEndTime = "2:00 PM" then %> selected <% end if %> >2:00 PM</option>
<option <% if strEndTime = "3:00 PM" then  %> selected <% end if %> >3:00 PM</option>
<option <% if strEndTime = "4:00 PM" then %> selected <% end if %> >4:00 PM</option>
<option <% if strEndTime = "5:00 PM" then  %> selected <% end if %> >5:00 PM</option>
<option <% if strEndTime = "6:00 PM" then  %> selected <% end if %> >6:00 PM</option>
<option <% if strEndTime = "7:00 PM" then  %> selected <% end if %> >7:00 PM</option>
<option <% if strEndTime = "8:00 PM" then%> selected <% end if %> >8:00 PM</option>
<option <% if strEndTime = "9:00 PM" then%> selected <% end if %> >9:00 PM</option>
<option <% if strEndTime = "10:00 PM" then %> selected <% end if %> >10:00 PM</option>
<option <% if strEndTime = "11:00 PM" then %> selected <% end if %> >11:00 PM</option>
<option <% if strEndTime = "12:00 AM" then %> selected <% end if %> >12:00 AM</option>
     </select></td></tr></table>
     <table><tr>

		<TD><b><font face="Arial" size="2">Species: &nbsp;</font></b></TD>
    <TD>  <font face="Arial">   <select size="1" name="Species">
     <option value="">--- All ---</option>
       <%= objGeneral.OptionListAsString(rstSpecies, "Species", "Species", strSpecies) %>
     </select></td>
</TR>

	</table>
<table width = 100% bgcolor = white><tr>

  
    <TD class="auto-style4"><input type="submit" value="Search"></TD>
 
</TR>


  </TABLE></form>


  <% if objGeneral.IsSubmit() Then 

%>
  
    
    <%   
    strHBX = 0
      strHBXOCC = 0
     strHWM = 0
     strHWMOCC = 0
     strKBLD = 0
     strKBLDOCC = 0
	 strLPSBS = 0
	 strLPSBCOCC = 0
	  strMXP = 0 
   	 strMXPOCC = 0 
   	 strOCC = 0
    strOF3 = 0
    strOF3OCC = 0   
     strPMX = 0
      strPMXOCC = 0
     strSHRED = 0 
     strSHREDOCC = 0 
    strSWL = 0
    strSWLOCC = 0
    strUSBS = 0
    strUSBSOCC = 0


    strPMorff = 0
    strOCCWL = 0
    strALPine = 0
    strAshdown = 0
    strFibria = 0
    strBroke = 0
    strBrokeHT = 0
    strCelgar = 0
    strDomtar = 0
    strDryden = 0
    strIrving = 0

 
 

    
    strsql = "SELECT Count(tblCars.CID) AS CountOfCID, Sum(tblCars.Net) AS SumOfNet, Species, Location FROM tblCars "_
	&" WHERE Date_Unloaded > '" & strBegDate & "' and Date_Unloaded  <= '" & strEndDate & "'  "_
	&" and Grade <> 'NF' and Grade <> 'WADDING' and location <> 'DC WHSE' and location <> 'MERCHANTS' and location <> 'DC' "_
	&" GROUP BY  Species, Location "_
	&" ORDER BY  Species, Location"
	
	   Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
    IF not MyConn.eof  then


    while not MyConn.eof 
    strSpecies = MyConn("Species")
    strLocation = MyConn("Location")
    Sumofnet = MyConn("SumofNet")
   
	 if strSpecies = "SHRED" and strLocation = "RF" Then 
    strShred = SumofNet
    end if
    
    if strSpecies = "SHRED" and strLocation = "OCC" Then 
    strShredOCC = SumofNet
    end if

   if strSpecies = "SWL" and strLocation = "RF" Then 
    strSWL= SumofNet
    end if

    if strSpecies = "SWL" and strLocation = "OCC" Then 
    strSWLOCC= SumofNet
    end if
    
    if strSpecies = "HBX" and strLocation = "RF" Then 
    strHBX= SumofNet
    end if

    if strSpecies = "HBX" and strLocation = "OCC" Then 
    strHBXOCC= SumofNet
    end if
    
    if strSpecies = "HWM" and strLocation = "RF" Then 
    strHWM= SumofNet
    end if

    if strSpecies = "HWM" and strLocation = "OCC" Then 
    strHWMOCC= SumofNet
    end if
    
    if strSpecies = "KBLD" and strLocation = "RF" Then 
    strKBLD= SumofNet
    end if

    if strSpecies = "KBLD" and strLocation = "OCC" Then 
    strKBLDOCC= SumofNet
    end if

        if strSpecies = "OF3" and strLocation = "RF" Then 
    strOF3= SumofNet
    end if

    if strSpecies = "OF3" and strLocation = "OCC" Then 
    strOF3OCC= SumofNet
    end if

    if strSpecies = "PMX" and strLocation = "RF" Then 
    strPMX= SumofNet
    end if

    if strSpecies = "PMX" and strLocation = "OCC" Then 
    strPMXOCC= SumofNet
    end if
    
    if strSpecies = "LPSBS" and strLocation = "RF" Then 
    strLPSBS= SumofNet
    end if

 	if strSpecies = "LPSBS" and strLocation = "OCC" Then 
    strLPSBSOCC= SumofNet
    end if
    
    if strSpecies = "MXP" and strLocation = "RF" Then 
    strMXP= SumofNet
    end if

 	if strSpecies = "MXP" and strLocation = "OCC" Then 
    strMXPOCC= SumofNet
    end if

    if strSpecies = "USBS" and strLocation = "RF" Then 
    strUSBS= SumofNet
    end if

 	if strSpecies = "USBS" and strLocation = "OCC" Then 
    strUSBSOCC= SumofNet
    end if

  if left(MyCOnn("Species"),5) = "BROKE"  then
    strBroke = MYConn("SumofNet")
     
	elseif     MyCOnn("Species") = "OCC"  then
    strOCC= MYConn("SumofNet")  

    elseif     MyCOnn("Species") = "P-MORFF"  then
    strPmorff= strPMorff + MYConn("SumofNet")
    elseif     MyCOnn("Species") = "OCC White WetLap"  then
    strOCCWL= strOCCWL + MYConn("SumofNet")    
    elseif     MyCOnn("Species") = "AL PINE"  then
    strALPine= strALPine + MYConn("SumofNet")
    elseif     MyCOnn("Species") = "Ashdown"  then
    strAshdown= strAshdown + MYConn("SumofNet")
     elseif     MyCOnn("Species") = "FIBRIA - LOW BRITE"  then
    strFibria= strFibria + MYConn("SumofNet")
    elseif     MyCOnn("Species") = "CELGAR"  then
    strCelgar= strCelgar + MYConn("SumofNet")
    elseif     MyCOnn("Species") = "Domtar Espanola L-3"  then
    strDomtar= strDomtar + MYConn("SumofNet")
     elseif     MyCOnn("Species") = "DRYDEN"  then
    strDryden = strDryden + MYConn("SumofNet")    
     elseif     MyCOnn("Species") = "Irving"  then
     strIrving= strIrving + MYConn("SumofNet")
    end if
 MyConn.movenext
    wend
    end if
    MyConn.close 
strKCOPTotal = strPMX  + strHBX + strSHRED + strKBLD + strOF3 + strSHRED + strSWL + strHWM + strUSBS + strMXP + strLPSBS
strOCCtotal = + strPMXOCC + strHBXOCC  + strSHREDOCC + strKBLDOCC  + strOF3OCC + strSHREDOCC +  strSWLOCC + strHWMOCC +  strUSBSOCC + strMXPOCC   + strLPSBS   + strOCC
    if len(strIrving) > 0 then 
 'do nothing
 else
 strIrving = 0
 end if
 
 
      astrHBX = 0
      astrHBXOCC = 0
     astrHWM = 0
     astrHWMOCC = 0
     astrKBLD = 0
     astrKBLDOCC = 0
	 astrLPSBS = 0
	 astrLPSBCOCC = 0
	  astrMXP = 0 
   	 astrMXPOCC = 0 
   	 astrOCC = 0
    astrOF3 = 0
    astrOF3OCC = 0   
     astrPMX = 0
      astrPMXOCC = 0
     astrSHRED = 0 
     astrSHREDOCC = 0 
    astrSWL = 0
    astrSWLOCC = 0
    astrUSBS = 0
    astrUSBSOCC = 0
  
  


    astrPMorff = 0
    astrOCCWL = 0
    astrALPine = 0
    astrAshdown = 0
    astrFibria = 0
    astrBroke = 0
    astrBrokeHT = 0
    astrCelgar = 0
    astrDomtar = 0
    astrDryden = 0
        astrIrving = 0

    strsql = "SELECT  Sum(tblCars.Net) AS SumOfNet, tblCars.Species FROM tblCars "_
	&" WHERE Date_Received > '" & strBegDate & "' and Date_received  <= '" & strEndDate & "' and Trailer <> 'UNKNOWN' GROUP BY tblCars.Species  ORDER BY tblCars.Species"
	
	   Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
    IF not MyConn.eof  then
while not MyConn.eof
 
    astrSpecies = MyConn("Species")
 
    Sumofnet = MyConn("SumofNet")
         if astrSpecies = "SHRED" Then 
    astrShred = SumofNet
    end if
   if astrSpecies = "SWL"  Then 
    astrSWL= SumofNet
    end if


  
if   Trim(MyConn("Species")) = "HBX"  then
   astrHBX= MYConn("SumofNet") 
elseif   Trim(MyConn("Species")) = "HWM"  then
   astrHWM= MYConn("SumofNet") 

    elseif left(MyCOnn("Species"),5) = "BROKE"  then
    astrBroke = MYConn("SumofNet")
 

  elseif      Trim(MyCOnn("Species")) = "MXP"  then
    astrMXP = MYConn("SumofNet")
    
    elseif     MyConn("Species") = "OF3"  then
    astrOF3 = MYConn("SumofNet")

    
  elseif    MyConn("Species") = "KBLD" then
    astrKBLD = MYConn("SumofNet")

elseif     left(MyCOnn("Species"),3) = "PMX"  then
    astrPMX = MYConn("SumofNet")
    
    
elseif     MyCOnn("Species") = "OCC"  then
    astrOCC= MYConn("SumofNet")
    
 
     elseif     MyCOnn("Species") = "USBS"  then
    astrUSBS= MYConn("SumofNet")
elseif     MyCOnn("Species") = "LPSBS"  then
    astrLPSBS= MYConn("SumofNet")
 
elseif     MyCOnn("Species") = "P-MORFF"  then
    astrPmorff= astrPMorff + MYConn("SumofNet")
elseif     MyCOnn("Species") = "OCC White WetLap"  then
    astrOCCWL= astrOCCWL + MYConn("SumofNet")
    
    elseif     MyCOnn("Species") = "AL PINE"  then
    astrALPine= astrALPine + MYConn("SumofNet")
elseif     MyCOnn("Species") = "Ashdown"  then
    astrAshdown= astrAshdown + MYConn("SumofNet")
elseif     MyCOnn("Species") = "FIBRIA - LOW BRITE"  then
    astrFibria= astrFibria + MYConn("SumofNet")

  elseif     MyCOnn("Species") = "CELGAR"  then
    astrCelgar= astrCelgar + MYConn("SumofNet")
    elseif     MyCOnn("Species") = "Domtar Espanola L-3"  then
    astrDomtar= astrDomtar + MYConn("SumofNet")
  elseif     MyCOnn("Species") = "DRYDEN"  then
    astrDryden = astrDryden + MYConn("SumofNet")
    
    elseif     MyCOnn("Species") = "Irving"  then
    astrIrving = astrIrving + MYConn("SumofNet")

 

    end if
 MyConn.movenext
 wend
 MyConn.close
 end if 
 
 
 astrKCOPTotal = astrPMX  + astrHBX + astrSHRED + astrKBLD + astrOF3 + astrSHRED + astrSWL + astrHWM + astrUSBS + astrMXP + astrLPSBS
astrOCCtotal = + astrPMXOCC + astrHBXOCC  + astrSHREDOCC + strKBLDOCC  + astrOF3OCC + astrSHREDOCC +  astrSWLOCC + astrHWMOCC +  astrUSBSOCC + astrMXPOCC   + astrLPSBS   + astrOCC
    %>
  
   <table width="100%"><tr><td width="33%">
   <table border="1">
   <tr><td class="auto-style1" style="height: 44px">Waste Paper</td>
	<td class="auto-style1" style="height: 44px">RF Consumed</td>
	<td class="auto-style1" style="height: 44px">OCC Consumed</td>
	<td class="auto-style5" style="height: 44px">Received</td></tr>
	
  <tr><td class="style4">HBX</td>  
  <td>	<span class="style4"><%= strHBX %></span>&nbsp;</td>
    <td>	<span class="style4"><%= strHBXOCC %></span>&nbsp;</td>

  <td class="style8">	<%= astrHBX %>	&nbsp;</td></tr>

   <tr><td class="style4">KBLD</td>
     <td>	<span class="style4"><%= strKBLD %></span>&nbsp;</td>
        <td>	<span class="style4"><%= strKBLDOCC %></span>&nbsp;</td>
	<td class="style8"><%= astrKBLD %>	&nbsp;</td></tr>
	
   <tr><td class="style4">MXP</td>
   <td>	   &nbsp;</td>
	 <td>	<span class="style4"><%= strMXP %></span>&nbsp;</td>
		<td class="style8"><%= astrMXP %>	&nbsp;</td></tr>   
   <tr><td class="style4">OCC</td>
   <td> &nbsp;</td>
	 <td class="style4"><%= strOCC %>&nbsp;</td>
	<td class="style8"><%= astrOCC %>	&nbsp;</td></tr>
	
   <tr><td class="style4">OF3</td>
     <td>	<span class="style4"><%= strOF3 %></span>&nbsp;</td>
      <td>	<span class="style4"><%= strOF3OCC %></span>&nbsp;</td> 
	<td class="style8"><%= astrOF3 %>	&nbsp;</td></tr>
	
   <tr><td class="style4">PMX</td>
      <td>	<span class="style4"><%= strPMX %></span>&nbsp;</td>
     <td>	<span class="style4"><%= strPMXOCC %></span>&nbsp;</td>

	<td class="style8"><%= astrPMX %>	&nbsp;</td></tr>
	
	  <tr><td class="style4">SHRED</td>
	  	  <td><span class="style4"><%= strSHRED %></span>&nbsp;</td>
	  	  <td><span class="style4"><%= strSHREDOCC %></span>&nbsp;</td>
	  <td class="style8">	<%= astrSHRED %></td></tr>


  <tr><td class="style4">SWL</td>
    <td>	<span class="style4"><%= strSWL %></span>&nbsp;</td>
      <td>	<span class="style4"><%= strSWLOCC %></span>&nbsp;</td>
  <td class="style8">	<%= astrSWL %>	&nbsp;</td></tr>

 
  <tr><td class="style4">HWM</td>
 	<td class="style4"> <%= strHWM %> &nbsp;</td>
      <td>	<span class="style4"><%= strHWMOCC %></span>&nbsp;</td>
  <td class="style8">	<%= astrHWM %>  &nbsp;</td></tr>

 
  <tr><td class="style4">LPSBS</td>
	  <td class="style4"><%= strLPSBS %>	  &nbsp;</td>
    <td>	<span class="style4"><%= strLPSBSOCC %></span>&nbsp;</td>
 	<td class="style8"><%= astrLPSBS %>	&nbsp;</td></tr>

 
  <tr><td class="style4">USBS</td>
   <td class="style4"><%= strUSBS %>	  &nbsp;</td>
     <td>	<span class="style4"><%= strUSBSOCC %></span>&nbsp;</td>
 	<td class="style8"><%= astrUSBS %>	&nbsp;</td></tr>

 
  <tr><td class="style4">TOTAL</td>
    <td>	<span class="style4"><%=  strKCOPTotal %></span>	 </td>
   <td>	<span class="style4"><%=  strOCCTotal %></span>	 </td>

 <td>	<span class="style4"><%=  astrKCOPTotal + astrOCCTotal  %>	</span> </td></tr>

 
   	</table></td>
   
  <td width="33%">  
      <table border="1">
   <tr><td class="style6">Wetlap</td>
	<td class="style6">Consumed</td>
	<td class="style6">Received</td></tr>
   <tr><td class="style4">P-Morff</td>
      <td>	<span class="style4"><%= strPmorff %></span>&nbsp;</td>
    <td class="style8"><span class="style4"><%= astrPmorff %></span> &nbsp;</td>
 </tr>  
   <tr><td class="style4">OCC White Wetlap</td>  
   <td>	<span class="style4"><%= strOCCWL %></span>&nbsp; </td>
	<td class="style8"><span class="style4"><%= astrOCCWL %></span> &nbsp;</td>
	 </tr>
  <tr><td class="style4">Total</td>
    <td>	<span class="style4"> <%=  strPmorff + strOCCWL  %></span>	 </td>
   <td>	<span class="style4"> <%= astrPmorff + astrOCCWL %>	</span> </td>

</tr>

   </table><br><br>
        <table border="1">
   <tr><td class="style6">SPSK</td>
	<td class="style6">Consumed</td>
	<td class="style6">Received</td></tr>
   <tr><td class="style4">AL Pine</td> 
    <td>	<span class="style4"><%= strALPine %></span>&nbsp;</td>
	  <td>	<span class="style4"><%= astrALPine %></span>&nbsp;</td>
	  </tr>
   <tr><td class="style4">Ashdown&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </td>
     <td>	<span class="style4"><%= strAshdown %></span>&nbsp;</td>
	   <td>	<span class="style4"><%= astrAshdown %></span>&nbsp;</td>
	  </tr>
  <tr><td class="style4">Total</td>
  <td>	<span class="style4"> <%=  strAlPine + strAshdoen  %></span>	 </td>
   <td>	<span class="style4"> <%=  astrAlPine + astrAshdoen  %></span>	 </td>
  </tr>

   </table><br><br>

        <table border="1">
   <tr><td class="style6">Eucalyptus</td>
	<td class="style6">Consumed</td>
	<td class="style6" style="width: 104px">Received</td></tr>
   <tr><td class="style4">Fibria - Low Brite</td>
 

	   <td style="width: 104px">	<span class="style4"><%= strFibria %></span>&nbsp;</td>
	 	   <td style="width: 104px">	<span class="style4"><%= astrFibria %></span>&nbsp;</td></tr>
</table>
   
   
   </td>

  <td width="33%">    <table border="1">
   <tr><td class="style7">Broke</td>
	<td class="style7">Consumed</td>
	<td class="style7">Received</td></tr>
   <tr><td class="style4">Broke</td><td class="style4"><%= strBroke %>	&nbsp;</td>
   <td class="style4"><%=  astrBroke %> &nbsp;</td></tr>
 
</table><br><br>
<table border="1">
   <tr><td class="style7">NBSK</td>
	<td class="style7">Consumed</td>
	<td class="style7">Received</td></tr>
   <tr><td class="style4">Celgar</td>
  <td>	<span class="style4"><%= strCelgar %></span>&nbsp;</td>
   <td>	<span class="style4"><%= astrCelgar %></span>&nbsp;</td></tr>
   <tr><td class="style4">Domtar Espanola L-3</td>
   <td>	<span class="style4"><%= strDomtar %></span>&nbsp;</td>
     <td>	<span class="style4"><%= astrDomtar %></span>&nbsp;</td>  </tr>
	       <tr><td class="style4">Dryden</td>
  <td>	<span class="style4"><%= strDryden %></span>&nbsp;</td>
   <td>	<span class="style4"><%= astrDryden %></span>&nbsp;</td>
  </tr>

	       <tr><td class="style4">Irving</td>
  <td>	<span class="style4"><%= strIrving %></span>&nbsp;</td>
   <td>	<span class="style4"><%= astrIrving %></span>&nbsp;</td>
  </tr>

 <tr><td class="style4">Total</td>
  <td>	<span class="style4"><%=  strCelgar + strDomtar + strDryden + strIrving  %> </span>	 </td>
  <td>	<span class="style4"><%=  astrCelgar + astrDomtar + astrDryden + astrIrving  %> </span>	 </td></tr>
</table></td>
 </tr>
</TABLE>

</td>
 </tr>
</TABLE> 

 

    
<p>
&nbsp;</p>

    
<% end if %><% Function GetData()

   set objNew = new ASP_Cls_Fiber
          

		set rstSpecies = objNew.FiberSpecies()


End Function
    Function GetFormData()
       
	
	strVendor = ""
	strGenerator = ""
	strBegDate = Request.form("Beg_date") & " " & request.form("Beg_time")
	strBDate = Request.form("Beg_date")
	strEndDate = Request.form("End_date")& " " & request.form("End_time")
	strEdate = Request.form("End_date")
	strSpecies = Request.form("Species")
	Session("Species") = strSpecies
	Session("Beg_Date") = strBegDate
	Session("End_date") = strEndDate
	strBegTime = request.form("Beg_time")
	strEndTime = request.form("End_time")




 
    End Function

    Function LoadSearchResults()
      Dim objEquipSearch, objTotals

	
	strSpecies = Request.form("Species")
      set objEquipSearch = new ASP_Cls_Fiber
    

 

If strSpecies = "OCC" then
set rstTotals = objEquipSearch.IRSearch_totalsOCCR1(1, strBegDate, strEndDate)


else
set rstTotals = objEquipSearch.IRSearch_totalsR1(1, strBegDate, strEndDate,  strSpecies)
end if

     if  not rstTotals.Eof Then
if not isnull(rstTotals.fields("SumofNet")) then   
  strNetSum = round(rstTotals.fields("SumofNet"),3)
  
end if
      
	else

	strNetSum = 0

      
      end if 


   
    End Function
 %><!--#include file="Fiberfooter.inc"-->