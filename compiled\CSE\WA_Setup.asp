
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Work Areas</TITLE>

<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
 
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<%    Dim   rstTeam, strTeam, rstESL, rstWA, strWA,  strID, objEPS, strName, strName2, strType

    set objGeneral = new ASP_CLS_General %>
	<style type="text/css">
.style1 {
	font-family: Arial;
	font-weight: bold;
}
	.auto-style1 {
		border-color: #C0C0C0;
		border-width: 1px;
		background-color: #EDF7FE;
	}
	.auto-style2 {
		font-size: x-small;
		border-color: #C0C0C0;
		border-width: 1px;
		background-color: #EDF7FE;
	}
	</style>
</head>

	<body>

<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% border=1>
  <tr><td colspan="4" bgcolor="white" class="subheader">&nbsp;</td></tr>

 <TD>
     <p align="left"><font size="3" face="Arial"><b><a href="WA_Add.asp">Add New</a></b>&nbsp;&nbsp;</font></td>
<td align = right><a href="WA_Setup.asp"><font face="Arial"><b>Return</b></font></td></tr>
	    </table><font face="Arial"><br>
	    <%  
	        Dim SCRIPT_NAME
		Dim strSQL
 
    		    
	strSQL = "SELECT * from tblAuthorizers order by work_area"
		        Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
			if not MyRec.eof then 
 
 

			strSQL = "SELECT WorkAreas.*, VA_Team_Name from WorkAreas INNER JOIN VA_Team_names on WorkAreas.VA_Team_ID = VA_Team_Names.VA_Team_ID order by WorkArea"
		        Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
			%>
			<table Border=1 bgcolor = #F0F0F0 BORDERCOLOR = #CCCCFF cellpadding = 3 width = 50% align = center>
			<thead>
			    <tr>
		
                	
                		<th class="auto-style1"> <font face="arial" size="2">Work Area</font></th>
	     		
           
                    
	     		
           
                       
                		<th class="auto-style2"> WA Abb</th>
	     		
           
                    
	     		
           
                       
                		<th class="auto-style2"> Team</th>
	     		
           
                       
              	    </tr>
			    </thead>
			    <tbody>
	
        		        <% While not MyRec.EOF %>
              			    <tr>
               <td bgcolor="#FFFFFF"><font face="Arial"><%= MyRec.Fields("Workarea").Value %></font></td>
             <td bgcolor="#FFFFFF"><font face="Arial"><%= MyRec.Fields("WA_Abb").Value %></font></td>   
               
               <td bgcolor="#FFFFFF"><font face="Arial"><%= MyRec.Fields("VA_Team_Name").Value %></font></td>
              		   
  						 </tr>
                            
              			    <% 
				        MyRec.MoveNext
                 			WEND
                 			MyRec.Close
              			    %>

		

            		</table>
	 
      	
      	  <%   

  
   else
    Response.write ("<font face = arial size = 3>You do not have authorization to view this page")
   
    end if  %><!--#include file="footer.inc"-->