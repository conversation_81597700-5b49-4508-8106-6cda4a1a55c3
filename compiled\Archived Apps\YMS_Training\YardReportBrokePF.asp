																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Broke Yard Inventory Report</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->



<% Dim MyRec, strsql, strDays, strDate, gSpecies, gCount, gTCount, strsql3, MyRec3, gTotalweight, MyRec2, strRelease

	strdate = formatdatetime(now(),2)
gcount = 0
gSpecies = ""
gTotalweight = 0

    strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "' and User_type = 'A'"


    Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL3, Session("ConnectionString")



If not Myrec2.eof then
strAdmin = "YES"
else
strAdmin = ""
end if
MyRec2.close


If Session("EmployeeID") = "B60405" or Session("EmployeeID") = "B53911" or Session("EmployeeID") = "B60423" or Session("EmployeeID") = "B48916" then 
strAdmin = "YES"
end if


IF Session("EmployeeID") = "D10480" then
strAdmin = "YES"
end if

strAdmin = "YES"
 %>

<style type="text/css">
.style1 {
	font-family: Arial;
	font-size: xx-small;
}
.style2 {
	font-family: Arial;
	font-weight: bold;
	font-size: xx-small;
}
.style5 {
	font-weight: bold;
	font-size: x-small;
	text-align: left;
}
.style6 {
	font-size: xx-small;
}
.style7 {
	font-weight: bold;
	font-size: xx-small;
}
.style8 {
	font-weight: bold;
	font-size: xx-small;
	text-align: left;
}
.style9 {
	font-size: x-small;
}
</style>
</head>
<body onload="if (window.print) {window.print()}">
<br>
	
<%	strsql = "SELECT tblCars.*,  [date_received] AS Sort_Date FROM tblCars WHERE (((tblCars.Date_received) Is Not Null) "_
&"  AND ((tblCars.Location)='Yard') AND ((tblCars.Trailer) Is Not Null))  and (Species='BROKE' or Grade = 'BROKE' or Species = 'WBROK') "_
&"  ORDER BY tblCars.Species, Sort_Date "   
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


%>

<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=0>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><b>
<font face="arial" size="4" >Broke Yard Inventory Report for <%= strDate%></font></b></span></td>
<td align = center>&nbsp;</td>


</tr>
	    </table>
<p align="right"><font face="Arial"><span class="style9">Yard Physically Checked by 
_______________________<br>Signature&nbsp;&nbsp;</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </font>&nbsp; </p>

	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "style6" border=0>  
	 <tr class="tableheader">


		<td align = center  ><font face="Arial" size="2" class="style6"><b>Date <br>Received</b></b></font></td>
    	<td class="style8" ><font face="Arial">Trailer</font></td>
		<td class="style7" ><font face="Arial">Carrier</font></td>
			<td class="style7" ><font face="Arial">Vendor</font></td>
	<td class="style5"><span class="style2">Receipt #</span><font face="Arial"><span class="style7">
	</span></font></td>
		<td class="style5"><span class="style2">Release #</span><font face="Arial"><span class="style7">
	</span></font></td>

<td align = left class="style1"><strong>Weight</strong></td>


<td align=center><font face="Arial" size="1" class="style7">To Date<br>Detention</font></td>
<td align="center" ><font face="Arial" size="1" class="style7">Days <br>in Yard</font></td>

<td align="center" class="style7" ><font face="Arial">Type</font></td>
<td align="center" class="style7" ><font face="Arial">Comments</font></td>
<td align = center class="style7" ><font face="Arial">Checkoff</font></td>
	</tr>

 <% Dim ii
       ii = 0
             strMT = 0
       strET = 0

       while not MyRec.Eof
           strRelease = MyRec.fields("Release_nbr")
                  strweight = ""
       strMetric = ""

    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
        
        <% if isnull(MyRec.fields("Transfer_date")) then %>
      
	<td align = center ><font face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align = left > <font size="2" face="Arial" class="style6"> &nbsp; <%= MyRec.fields("Trailer")%>&nbsp; </font></td>
	<td  >   <font size="2" face="Arial">  <span class="style6">  <%= MyRec.fields("Carrier")%></span>&nbsp;</font></td>
		<td  >   <font size="2" face="Arial">  <span class="style6">  <%= MyRec.fields("Vendor")%></span>&nbsp;</font></td>

	 <td  >  <font size="2" face="Arial"><span class="style6"><%= MyRec.fields("REC_number")%></span>&nbsp;</font></td>
	 
	 <td  >  <font size="2" face="Arial"><span class="style6"><%= MyRec.fields("Release_nbr")%></span>&nbsp;</font></td>

        <% else %>
     
		<td align = center ><font face="Arial"><%= MyRec.fields("Transfer_Date")%></font></td>
	
			<td align = left  > <font size="2" face="Arial" class="style6">&nbsp; <%= MyRec.fields("Transfer_trailer_nbr")%></font></td>
		<td  >   <font size="2" face="Arial"> <span class="style6"> <%= MyRec.fields("Trans_Carrier")%></span>&nbsp;</font></td>
				<td  >   <font face="Arial"> &nbsp;</font></td>

		   <td  >  <font size="2" face="Arial">        <span class="style6">        <%= MyRec.fields("PMO_nbr")%></span>&nbsp;</font></td>
		   	   <td  >  <font face="Arial">  &nbsp;</font></td>
		<% end if %>
		

		
 
            <td  >  <font size="2" face="Arial">   
          	<span class="style6">   
          <% 

if len(MyRec("Net")) > 0 then
strweight = MyRec.fields("Net")
else
strweight =MyRec("Tons_Received")
end if 

strSAP = MyREc("SAP_NBR")
strsql = "Select UOM from tblBrokeSAP where SAP = '" & strSAP & "'"
    Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL, Session("ConnectionString")
if not MyRec2.eof then
strUOM = MyREc2("UOM")
if strUOM = "T" then
 If strweight > 1000 then
 strMetric = round(strweight / 2204.6,3)
 else
 strMetric = round((strweight * 2000)/2204.6,3)
 end if
 strMT = strMT + strMetric
elseif strUOM = "TONS" and strweight > 1000 then

strweight  = round(strweight / 2000,3)
strET = strET + strweight
else
strET = strET + strweight
end if
end if
%> <% if strUOM = "T" then %>
<%= strMetric %> T
<% else %>
<%= strweight %> Tons
<% end if %>
&nbsp;</span></td>

                  
         <% if isnull(MyRec.fields("Transfer_date")) then
             strDays = round(Now()- cdate(MyRec.fields("Date_received")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

             else
             strDays = round(Now()- cdate(MyRec.fields("Transfer_date")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Trans_Carrier") & "' "
             end if  
          
 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    if strDays  > strFree then %>
    <td align = center><font size="2" face="Arial"> <span class="style6">$<%= (int(strDays) * strFee) - (strFee * strFree) %></span>&nbsp;</font></td>
	<% else %>
	  <td align = center><font face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font face="Arial"><%= strDays%></font></td>	



	
	<td align="center"><font size="2" face="Arial"><span class="style6"><%= MyRec("SAP_NBR")%>-<%= MyRec("Broke_Description") %></span>&nbsp;</font></td>

	
	<td><font size="2" face="Arial">
	    <span class="style6">
	    <% if MyRec.fields("Rejected") = "YES" then %>
       REJECTED&nbsp;&nbsp; 
        <% end if %>
	
	<%= MyRec.fields("Other_Comments")%></span>&nbsp;</font></td>
	<td ALIGN = CENTER><font size="2" face="Arial"><input type="checkbox" name="C1" value="ON">&nbsp;</font></td>
	</tr>

 <%    gcount = gcount + 1
       gTcount = gTcount + 1
       
 	
       ii = ii + 1
       MyRec.MoveNext
       wend
    MyRec.close
    %>
<tr>
<td><font face = arial size = 2 class="style7">Total:&nbsp;<%= gcount%></font></td>
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
   <td>&nbsp;</td>
   <td>&nbsp;</td>
       <td><font face = arial size = 2><b>
  		<span class="style6">
  <%= strET %> Tons<br>
  <%= strMT %> T</span></b><span class="style6"> &nbsp;</span></td>
<td>&nbsp;</td>	
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
   <td>&nbsp;</td>

	</tr>

</table>
