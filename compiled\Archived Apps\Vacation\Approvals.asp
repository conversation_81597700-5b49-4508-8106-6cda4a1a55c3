																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE> Area list</TITLE>
<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_sessionPLI.asp"--> 
<!--#include file="classes/ASP_CLS_General.asp"--> 
<!--#include file="classes/asp_cls_header.asp"-->


<% 

Dim MyRec, strsql, MyConn, strPost
 Dim MyS, MySQ, strOK, strsite

STRSQL = "Select EMP_ID from tblVac_Admin where EMP_ID = '" & Session("EmployeeiD") & "'  "

Set MyS = Server.CreateObject("ADODB.Recordset") 
   		Set MyS = Server.CreateObject("ADODB.RecordSet")
		MyS.Open strSQL, Session("ConnectionString")
If not MyS.eof then
strPost = "OK"
else 
strPost = ""
end if
MyS.close

if Session("EmployeeID") = "C97338" then 
strPost = "OK"
end if



%>
<style type="text/css">
.style1 {
	border: 1px solid #000000;
}
.style2 {
	border: 1px solid #C0C0C0;
}
.style3 {
	text-align: center;
	border: 1px solid #C0C0C0;
}
.style5 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style6 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	text-align: center;
}
.style7 {
	font-size: small;
}
.style8 {
	border-width: 1px;
}
.auto-style1 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	text-align: center;
	background-color: #E2F3FE;
}
.auto-style3 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	text-align: left;
	background-color: #E2F3FE;
}
</style>
</head>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=60%  border=1 align = center>
<tr>
 <TD align = left style="width: 15%" class="style8"><font size="2" face = arial>
	<span class="style7"><strong>
<% if strPost = "OK" then%>
 <% else %>
 &nbsp;</strong></span>
 <% end if %></font></td>
<td align = center><b>
<font face="arial" size="4" >  Approval List</font></b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br><br>
	
	
	<TABLE align = center style="width: 35%" class="style1">  
	 <tr bgcolor="#FFFFCC">
 
	<td  align="left" class="auto-style3" >  Area</td>
	<td  align="left" class="auto-style3" >  Shift</td>
	<td  align="left" class="auto-style3" >  Date</td>
	<td  align="left" class="auto-style1" >  # to Approve</td>



	</tr>

 <% strsql = "SELECT tblArea.Area, tblRequestedTimeOff.Shift, tblRequestedTimeOff.TimeoffDate, Count(tblRequestedTimeOff.ID) AS CountOfID FROM tblRequestedTimeOff INNER JOIN tblArea ON tblRequestedTimeOff.VacAreaID = tblArea.ID WHERE Approved IS NULL GROUP BY tblArea.Area, tblRequestedTimeOff.Shift, tblRequestedTimeOff.TimeoffDate, Approved  ORDER BY tblArea.Area , TimeoffDate"

'Response.write strsql
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

 
 Dim ii
       ii = 0
       while not MyRec.Eof
  
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
 
    
	<td  align="left" class="style2" > <font size="2" face="Arial"><a href="ApproveTimeOff.asp?area=<%= MyRec.fields("Area")%>"> <%= MyRec.fields("Area")%></a>&nbsp;</font></td> 

		<td  align="left" class="style2" > <font size="2" face="Arial"><a href="ApproveTimeOff.asp?Shift=<%= MyRec.fields("Shift")%>"> <%= MyRec.fields("Shift")%>&nbsp;</font></a></td> 
<td  align="left" class="style2" > <font size="2" face="Arial"><a href="ApproveTimeOff.asp?TimeoffDate=<%= MyRec.fields("TimeoffDate")%>"> <%= MyRec.fields("TimeoffDate")%>&nbsp;</a></font></td> 

		<td class="style3" > <font size="2" face="Arial"><a href="ApproveTimeOff.asp?area=<%= MyRec.fields("Area")%>&Shift=<%= MyRec.fields("Shift")%>&TimeoffDate=<%= MyRec.fields("TimeoffDate")%>"><%= MyRec.fields("CountofID")%></a>&nbsp;</font></td> 
 

	

    


	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
     MyRec.close
    %>
</table>




<!--#include file="footer.inc"-->

