<% Option Explicit %>

<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>CSE Search</TITLE>

<style type="text/css">
.auto-style1 {
	border-width: 0;
	background-color: #EDF7FE;
}
.auto-style2 {
	font-weight: bold;
	border-width: 0;
	background-color: #EDF7FE;
}
</style>
</head>

<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_Procedure.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->

<%

   Dim objEPS
   Dim  rstWA, strWA,  strID
   Dim objGeneral
   
   dim   rstEPS




      set objGeneral = new ASP_CLS_General

   



  if objGeneral.IsSubmit() Then
  strWA = Request.form("Workarea")
  Response.redirect("Work_area_Summary.asp?id=" & strWA)
  

  end if
Call getdata()
%><body>

	<form name="form1" action="Select_WA.asp?id=<%= strWA%>"  method="post" ID="Form1"  >
 

 

 
  
 <table border="1" align = center cellpadding="0" style="border-collapse: collapse" bordercolor="#808080" width="50%" bgcolor="#D9E1F9" id="table1" height="137">
   
  <tr align = center>
    <td class="auto-style2">
	
	<font face="Arial">SELECT WORK AREA</font></td>

   
  </tr>
  <tr align = center>
    <td class="auto-style1"  >
		<font face="Arial">  <select name="Workarea">
       <option value="">--- Select ---</option>
       <%= objGeneral.OptionListAsString(rstWA, "WorkArea", "WorkArea", strWA) %>
     </select></font><p>
	<INPUT TYPE="submit" value="Continue"></td></tr></table>
 

	</form>
	<%   Function GetData()
        set objEPS = new ASP_CLS_ProcedureESL
     
 	set rstWA = objEPS.ReadWorkArea()


    End Function%><!--#include file="footer.inc"-->