																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Boise Yard Inventory Report</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->



<% Dim MyRec, strsql, strDays, strDate, gSpecies, gCount, gTCount, strsql3, MyRec3, gTotalweight, MyRec2

	strdate = formatdatetime(now(),2)
gcount = 0
gSpecies = ""
gTotalweight = 0

    strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "' and User_type = 'A'"


    Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL3, Session("ConnectionString")



If not Myrec2.eof then
strAdmin = "YES"
else
strAdmin = ""
end if
MyRec2.close


If Session("EmployeeID") = "B60405" or Session("EmployeeID") = "B53911" or Session("EmployeeID") = "B60423" or Session("EmployeeID") = "B48916" then 
strAdmin = "YES"
end if


IF Session("EmployeeID") = "D10480" then
strAdmin = "YES"
end if

strAdmin = "YES"
 %>

<style type="text/css">
.style1 {
	font-family: Arial;
	font-size: x-small;
}
.style2 {
	font-family: Arial;
	font-weight: bold;
	font-size: x-small;
}
.style3 {
	font-weight: bold;
	font-size: x-small;
}
.style4 {
	font-size: x-small;
}
.style5 {
	font-weight: bold;
	font-size: x-small;
	text-align: left;
}
</style>
</head>

<body>
<br>
	
<%	strsql = "SELECT tblCars.*,  [date_received] AS Sort_Date FROM tblCars WHERE (((tblCars.Date_received) Is Not Null) AND ((tblCars.Location)='Yard') AND ((tblCars.Trailer) Is Not Null))  and Grade='Boise' ORDER BY tblCars.Species, Sort_Date "   
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=0>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><b>
<font face="arial" size="4" >Boise Cascade Yard Inventory Report for <%= strDate%></font></b></span></td>
<td align = center>&nbsp;</td>


</tr>
	    </table>
<p align="right"><font face="Arial">Yard Physically Checked by 
_______________________<br>Signature&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </font>&nbsp; </p>

	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">

	<td class="style3"  ><font face="Arial">Species</font></td>
		<td align = center  ><font face="Arial" size="2" class="style4"><b>Date <br>Received</b></b></font></td>
    	<td class="style5" ><font face="Arial">Trailer</font></td>
		<td class="style3" ><font face="Arial">Carrier</font></td>
	<td class="style5"><span class="style2">Receipt Number</span><font face="Arial" size="2"><b><span class="style4">
	</span></b></font></td>
<td align = left class="style1"><strong>W</strong><b>eight</b></td>


<td align=center><font face="Arial" size="1" class="style3">To Date<br>Detention</font></td>
<td align="center" ><font face="Arial" size="1" class="style3">Days <br>in Yard</font></td>

<td align="center" class="style3" ><font face="Arial">Other</font></td>
<td align = center class="style3" ><font face="Arial">Checkoff</font></td>
	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    
    <% if  UCASE(MyRec.fields("Species")) = UCASE(gSpecies) or gcount = 0 then %>


        
        <% if isnull(MyRec.fields("Transfer_date")) then %>
        <td>       <font size="2" face="Arial">       <%= MyRec.fields("Species")%>
        <% if MyRec.fields("Rejected") = "YES" then %>
        - Rejected
        <% end if %>
        
        </font></td>
	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td  > <font size="2" face="Arial"> &nbsp; <%= MyRec.fields("Trailer")%>&nbsp; </font></td>
	<td  >   <font size="2" face="Arial">  &nbsp; <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
	 <td  >  <font size="2" face="Arial">        <%= MyRec.fields("REC_number")%>&nbsp;</font></td>
        <% else %>
        	<td>       <font size="2" face="Arial">       <%= MyRec.fields("Species")%>&nbsp;&nbsp:SHUTTLE</font></td>
		<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Transfer_Date")%></font></td>
	
			<td  > <font size="2" face="Arial">&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; <%= MyRec.fields("Transfer_trailer_nbr")%></font></td>
		<td  >   <font size="2" face="Arial">        <%= MyRec.fields("Trans_Carrier")%>&nbsp;</font></td>
		   <td  >  <font size="2" face="Arial">        <%= MyRec.fields("PMO_nbr")%>&nbsp;</font></td>
		<% end if %>
		

		
 
            <td  >  <font size="2" face="Arial">        <%= MyRec.fields("Net")%></font></td>

                  
         <% if isnull(MyRec.fields("Transfer_date")) then
             strDays = round(Now()- cdate(MyRec.fields("Date_received")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

             else
             strDays = round(Now()- cdate(MyRec.fields("Transfer_date")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Trans_Carrier") & "' "
             end if  
          
 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    if strDays  > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(strDays) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"><%= strDays%></font></td>	
	
	<td><font size="2" face="Arial"><%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>
	<td ALIGN = CENTER><font size="2" face="Arial"><input type="checkbox" name="C1" value="ON">&nbsp;</font></td>
	</tr>

 <%    gcount = gcount + 1
       gTcount = gTcount + 1
       gTotalweight = gtotalweight + MyRec.fields("Net")
 		gSpecies = ucase(MyRec.fields("Species"))
       ii = ii + 1
       MyRec.MoveNext
    
    %>
<% else %>
<td><font face = arial size = 2><b></b>Total:&nbsp;<%= gcount%></b></font></td>
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
   <td>&nbsp;</td>
   <td>&nbsp;</td>
    <td>&nbsp;</td>
     <td>&nbsp;</td>
       <td >&nbsp;</td>
       <td><font face = arial size = 2>
       <% if gTotalWeight <> 0 then %>
       <%= round(gTotalweight,2) %>
       <% end if %> &nbsp;</td>
<td>&nbsp;</td>	
<td>&nbsp;</td>
<td>&nbsp;</td>

	</tr>
	<TR><td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
   <td>&nbsp;</td>
   <td>&nbsp;</td>
       <td >&nbsp;</td>
      <td>&nbsp;</td>
       <td>&nbsp;</td>
        <td>&nbsp;</td>
<td>&nbsp;</td>	
<td>&nbsp;</td>

	</tr>
	
	  <tr class=tablecolor2>
        
        <% if isnull(MyRec.fields("Transfer_date")) then %>
        <td>        <font size="2" face="Arial">         <%= MyRec.fields("Species")%>
        <% if MyRec.fields("Rejected") = "YES" then %>
        - Rejected
        <% end if %>
        </font></td>
        		<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
        		<td  > <font size="2" face="Arial">&nbsp; <%= MyRec.fields("Trailer")%>&nbsp; </font></td>
        		<td  >    <font size="2" face="Arial">  &nbsp;      <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
        		  <td> <font size="2" face="Arial">        <%= MyRec.fields("REC_number")%>&nbsp;</font></td>
        <% else %>
        	<td>       <font size="2" face="Arial">       <%= MyRec.fields("Species")%>&nbsp;&nbsp:SHUTTLE</font></td>
		<td  ><font size="2" face="Arial"><%= MyRec.fields("Transfer_Date")%></font></td>
			<td  > <font size="2" face="Arial">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <%= MyRec.fields("Transfer_Trailer_nbr")%></font></td>
				<td  >  <font size="2" face="Arial">   <%= MyRec.fields("Trans_Carrier")%>&nbsp;</font></td>
				<td> <font size="2" face="Arial"><%= MyRec.fields("PMO_nbr")%>&nbsp;</font></td>
		


		<% end if %>
		
                              <td  >  <font size="2" face="Arial">        <%= MyRec.fields("Net")%></font></td>

                  
         <% if isnull(MyRec.fields("Transfer_date")) then
             strDays = round(Now()- cdate(MyRec.fields("Date_received")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

             else
             strDays = round(Now()- cdate(MyRec.fields("Transfer_date")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Trans_Carrier") & "' "
             end if  
          
 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    if strDays  > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(strDays) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"><%= strDays%></font></td>

	

	<td><font size="2" face="Arial"><%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>
	<td ALIGN = CENTER><font size="2" face="Arial"><input type="checkbox" name="C1" value="ON">&nbsp;</font></td>
	</tr>
		
	<% gcount = 1
	 gTcount = gTcount + 1
	 gSpecies = UCASE(MyRec.fields("Species"))
	 gtotalweight = MyRec.fields("Net")
       ii = ii + 1
       MyRec.MoveNext
    
	 end if %>

<%  Wend %>
	  <tr class=tablecolor2>
<td><font face = arial size = 2><b></b>Total:&nbsp;<%= gcount%></b></font></td>
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
   <td>&nbsp;</td>
   <td>&nbsp;</td>
       <td >&nbsp;</td>
      <td>&nbsp;</td>
<td>&nbsp;</td>	
  <td><font face = arial size = 2><%= round(gTotalweight,2) %></td>
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
	</tr>
	<TR><td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
   <td>&nbsp;</td>
   <td>&nbsp;</td>
       <td >&nbsp;</td>
      <td>&nbsp;</td>
<td>&nbsp;</td>	


	</tr>
<TR><td><font face = arial size = 2><b></b>Grand Total:&nbsp;<%= gTcount%></b></font></td>

<td>&nbsp;</td>
<td>&nbsp;</td>
   <td>&nbsp;</td>
   <td>&nbsp;</td>
       <td>&nbsp;</td>
      <td >&nbsp;</td>
<td>&nbsp;</td>	
<td>&nbsp;</td>

	</tr>

</table>
