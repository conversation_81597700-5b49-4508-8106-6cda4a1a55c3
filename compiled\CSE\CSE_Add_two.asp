<html>
<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_Procedure.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionString.asp"-->
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Add Confined Space </title>
<style>
<!--
table.MsoTableGrid
	{border:1.0pt solid windowtext;
	font-size:10.0pt;
	font-family:"Times New Roman";
	}
.auto-style1 {
	border-color: #C0C0C0;
	border-width: 1px;
	background-color: #EDF7FE;
}
.auto-style2 {
	background-color: #EDF7FE;
}
-->
</style>
</head>
<% dim strsql, MyRec, strid, strecp, strTask, objGeneral, strDescription, strLocation, strComments, strDate, MyConn
Dim objEPS, rstTeam, rstWA, strArea, strWorkArea,  strSOP, strFunctional, strListone, strListtwo, strListthree, strType
Dim strQ1, strQ2, strQ3
strnow = dateadd("h", -5, now())
strDate = formatdatetime(strnow,2)
strid = Request.querystring("id")

strsql = "SELECT tblSOP.* from tblSOP where SID = " & strid & ""

Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
  If not MyConn.eof then
  
  strDescription = MyConn.fields("SDescription")
  else
  strDescription = ""
  end if
  Myconn.close

set objGeneral = new ASP_CLS_General
  		 if objGeneral.IsSubmit = True Then
  		
 	
strid = Request.querystring("id")

if isnull(Request.form("List_one")) then
strlistone = ""
else
strlistone= Replace(Request.form("List_one"), "'", "''") 
end if

if isnull(Request.form("List_two")) then
strlisttwo = ""
else
strlisttwo= Replace(Request.form("List_two"), "'", "''") 
end if

if isnull(Request.form("List_three")) then
strlistthree = ""
else
strlistthree= Replace(Request.form("List_three"), "'", "''") 
end if

strQ1 = Request.form("Q_one")
strQ2 = Request.form("Q_two")
strQ3 = Request.form("Q_three")

If Request.form("Q_one") = "No" and len(strListone) < 2 then
Response.write("<br><br><Font face = arial color = red size = 3><b> You must list why you answered No to Question #1</b></font>")
elseif Request.form("Q_two") = "No" and len(strListtwo) < 2 then
Response.write("<br><br><Font face = arial color = red size = 3><b> You must list why you answered No to Question #2</b></font>")
elseif Request.form("Q_three") = "No" and len(strListthree) < 2 then
Response.write("<br><br><Font face = arial color = red size = 3><b> You must list why you answered No to Question #3</b></font>")
else


If Request.form("Q_one") = "No" or Request.form("Q_two") = "No" or Request.form("Q_Three") = "No" then
strType = "NOT CSE"

strsql = "Update tblSOP set Q_one = '" & Request.form("Q_one") & "', Q_two = '" & Request.form("Q_two") & "',"_
&" Q_three = '" & Request.form("Q_three") & "', List_one = '" & strListone & "', List_two = '" & strListtwo & "', "_
&" List_three = '" & request.form("List_three") & "', Location_type = '" & strType & "' where SID = " & strid & ""
Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 
			Response.redirect("CSE_Determination.asp?id=" & strid)
else

strType = "CSE"

strsql = "Update tblSOP set Q_one = '" & Request.form("Q_one") & "', Q_two = '" & Request.form("Q_two") & "',"_
&" Q_three = '" & Request.form("Q_three") & "', List_one = '" & strListone & "', List_two = '" & strListtwo & "', "_
&" List_three = '" & request.form("List_three") & "', Location_type = '" & strType & "' where SID = " & strid & ""
Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 
			Response.redirect("CSE_Add_three.asp?id=" & strid)
end if 

end if
end if
 %><body>
 <p align="left"><b><font face = arial size = 3>Add New Confined Space:</font></b><font face="Arial">
 </font>
 </p>
	<form name="form1" action="CSE_Add_two.asp?id=<%= strid%>"  method="post" ID="Form1"  >
 
 

 
 <div align="center">

 

 
 <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#808080" width="100%" bgcolor="#D9E1F9" id="table1">
   
  <tr>
    <td align="center" class="auto-style1" >
	
	<font face="arial" size="2">Name of New Confined Space:&nbsp;&nbsp;<%=strDescription %>	&nbsp;</td>

</tr>
  
  </table>
  </div>
  <p><b><font face = arial size = 2>To evaluate your space, review the three following statements/questions and select the answer that applies to the situation. </font> 
	</b> </p>
	<table class="MsoTableGrid" border="0" cellpadding="0" width="98%" style="border-collapse: collapse; border: 0 none; " id="table5">
		<tr>
			<td valign="top" style="border: 1.0pt solid #FFFFFF; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in">
			<p class="MsoNormal" style="text-indent: -.25in; margin-left: .25in">
			<font face="Arial">Question</font></td>
			<td valign="top" style="width: 28%; border-left: medium none #FFFFFF; border-right: 1.0pt solid #FFFFFF; border-top: 1.0pt solid #FFFFFF; border-bottom: 1.0pt solid #FFFFFF; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in">
			<p class="MsoNormal" align="center"><font face="Arial">If no, list 
			why?</font></td>
		</tr>
	</table>
	<table class="MsoTableGrid" border="1" cellspacing="0" cellpadding="0" width="98%" style="width: 98.78%; border-collapse: collapse; border: medium none" id="table4">
		<tr>
			<td width="60%" valign="top" style="width: 60.92%; border: 1.0pt solid windowtext; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in" class="auto-style2">
			<p class="MsoNormal" style="text-indent: -.25in; margin-left: .25in">
			<font face="Arial">1.  Large enough and arranged so an employee could 
			fully enter the space and work:&nbsp; </font></td>
			<td valign="top" style="width: 3%; border-left: medium none; border-right: 1.0pt solid windowtext; border-top: 1.0pt solid windowtext; border-bottom: 1.0pt solid windowtext; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in" class="auto-style2">
			<p class="MsoNormal"><font face="Arial">&nbsp;<select name="Q_one" size="1">
       <option selected>Yes</option>
		<option <% if strQ1 = "No" then %> selected <% end if %>>No</option>
     </select>
			 &nbsp;</font></td>
			<td valign="top" style="width: 28%; border-left: medium none; border-right: 1.0pt solid windowtext; border-top: 1.0pt solid windowtext; border-bottom: 1.0pt solid windowtext; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in" class="auto-style2">
			<p class="MsoNormal">
		<font face = arial size = 2>
	<input name="List_one" size="42" style="float: left" value="<%= strListone%>" ></td>
		</tr>
		<tr>
			<td width="60%" valign="top" style="width: 60.92%; border-left: 1.0pt solid windowtext; border-right: 1.0pt solid windowtext; border-top: medium none; border-bottom: 1.0pt solid windowtext; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in" class="auto-style2">
			
			<font face="Arial">2.  Limited 	Access or Egress:&nbsp; Is access into or egress from the space limited 
			by the size, location, or configuration of the opening?</font><br><br></td>
			<td valign="top" style="width: 3%; border-left: medium none; border-right: 1.0pt solid windowtext; border-top: medium none; border-bottom: 1.0pt solid windowtext; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in" class="auto-style2">
			<p class="MsoNormal"><font face="Arial">&nbsp;<select name="Q_two" size="1">
       <option selected>Yes</option>
		<option <% if strQ2 = "No" then %> selected <% end if %>>No</option>
     </select></font></td>
			<td valign="top" style="width: 28%; border-left: medium none; border-right: 1.0pt solid windowtext; border-top: medium none; border-bottom: 1.0pt solid windowtext; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in" class="auto-style2">
			<p class="MsoNormal">
		<font face = arial size = 2>
	<input name="List_two" size="42" style="float: left" value="<%= strlisttwo%>" ></td>
		</tr>
		<tr>
			<td width="60%" valign="top" style="width: 60.92%; border-left: 1.0pt solid windowtext; border-right: 1.0pt solid windowtext; border-top: medium none; border-bottom: 1.0pt solid windowtext; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in" class="auto-style2">
	
			<font face="Arial">3.  </font>
			<span style="font-size:10.0pt;font-family:Arial">Not primarily 
			designed for human occupancy.&nbsp; Answer �no� if the space <u>was 
			designed</u> for continuous human occupancy. Consider entry and 
			egress, ventilation, lighting, temperature and other factors that 
			would be designed into a space if it were intended for continuous 
			occupancy.</span></td>
			<td valign="top" style="width: 3%; border-left: medium none; border-right: 1.0pt solid windowtext; border-top: medium none; border-bottom: 1.0pt solid windowtext; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in" class="auto-style2">
			<p class="MsoNormal"><font face="Arial">&nbsp;<select name="Q_three" size="1">
       <option selected>Yes</option>
		<option <% if strQ3 = "No" then %> selected <% end if %>>No</option>
     </select></font></td>
			<td valign="top" style="width: 28%; border-left: medium none; border-right: 1.0pt solid windowtext; border-top: medium none; border-bottom: 1.0pt solid windowtext; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in" class="auto-style2">
			<p class="MsoNormal">
		<font face = arial size = 2>
	<input name="List_three" size="42" style="float: left" value="<%= strListthree%>" ></td>
		</tr>
	</table>

	<p>&nbsp;<INPUT TYPE="submit" value="Submit"></p>
	</form>

<!--#include file="footer.inc"-->