																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Resolve Exceptions</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->




<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate

strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -95, strtdate)

strsql = "SELECT tblCars.* FROM tblCars WHERE Species = 'UNRESOLVED' and Entry_Page = 'Generic_receipt_VF'"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Resolve VF Trailer Exceptions</b></font></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1>  
	 <tr class="tableheader">
<td>&nbsp;</td>
			<td  > <p align="center">       <font face="Arial" size="1">Print<br> Receipt</font></td>
	
		<td  >       <font face="Arial" size="1">PO Number</font></td>
		<td  ><font face="Arial" size="1"><b>Trailer</b></font></td>
		<td  ><font face="Arial" size="1"><b>Carrier</b></font></td>
		<td  ><font face="Arial" size="1">Species</font></td>
    
		<td  ><font face="Arial" size="1">Vendor</font></td>
	
	<td>       <font face="Arial" size="1">Location</font></td>
		<td  >        <font face="Arial" size="1">Date<br> Received</font></td>
		<td  >      <font face="Arial" size="1">Date<br>Unloaded</font></td>
	
       <td> <p align="center">        <font face="Arial" size="1">Bales</font></td>
        
		
	
		<td  >
        <font size="1" face="Arial">Other</font></td>

	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td> <font size="1" face="Arial"><a href="Resolve_Exceptions_Edit_VF.asp?id=<%= MyRec.fields("CID") %>">Resolve</a></td>
	<td align = center> <font size="1" face="Arial"><a href="Truck_receipt_print.asp?id=<%= MyRec.fields("CID") %>"><%= MyRec.fields("CID")%></a></td>
	
	<td  >        <font size="1" face="Arial">        <%= MyRec.fields("PO")%>&nbsp;</font></td>
	<td  > <font size="1" face="Arial"><b><%= MyRec.fields("Trailer")%>&nbsp;</font></b></td>
			<td  ><font size="1" face="Arial"><b><%= MyRec.fields("Carrier")%>&nbsp;</font></b></td>


		<td  ><font face="Arial" size="1"> <%= MyRec.fields("Species")%>&nbsp;</font></td>
		<td><font size="1" face="Arial"><%= MyRec.fields("Vendor")%>&nbsp;</font></td>
	
	<td  >  <font size="1" face="Arial">        <%= MyRec.fields("Location")%>&nbsp;</font></td>
			<td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Date_Received")%>&nbsp;</font></td>
			<% if len(MyRec.fields("Date_unloaded")) > 0 then %>
		<td  >		 <font size="1" face="Arial">        <%= formatdatetime(MyRec.fields("Date_unloaded"),2)%>&nbsp;</font></td>
		<% else %>
			<td  >		 <font size="1" face="Arial"> &nbsp;</font></td>
		
		<% end if %>
		<td align = right>
				 <font size="1" face="Arial">        <%= MyRec.fields("Bales_VF")%>&nbsp;</font></td>
				

  
		<td  >	 <font size="1" face="Arial">        <%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>
	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>


<!--#include file="Fiberfooter.inc"-->