
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Add Name</TITLE>
<!--#include file="classes/asp_cls_SessionStringBadge.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_DataAccessBadge.asp"-->

<%

Dim strSQL, MyRec, objPRO, rstName, rstBID, strBID, strDisplay, strFirst, strLast, strActive, strMaster, strTraining, rstArea

 set objGeneral = new ASP_CLS_General
 

     
 

if objGeneral.IsSubmit() Then


	Call SaveData() 

End if

%>
<style type="text/css">
.style1 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
</style>
</head>

<body>
<form name="form1" action="EMS_Add.asp" method="post">
 
<table border="0" cellpadding="0" cellspacing="0" width = 60% style="border-collapse: collapse" bordercolor="000000" height="25" align = center >
  <tr>
    <td  bgcolor="#FFFFFF" bordercolor="#CCCCFF" height="25" >
    <p align="center"><font face="Arial" size="4">Add EMS/Fire Brigade Person</font></td><td align = center height="25"><font face="Arial"><b><a href="Ems.asp">RETURN</a></b></font></td>

    <td align = center bgcolor="#FFFFFF" height="25">
  <Input name="Submit" type="submit" Value="Submit" style="float: right" ></td>
  </tr>
</table><br><br>
<div align="center">
<table cellpadding="0" cellspacing="0" style="width: 50%;" bordercolor="#808080"  height="10" class="style1">
    <tr>
   
    <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="22" align="center" ><b>
	<font face="Arial">BID #</font></b></td>
   
   
    <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="22" align="center" ><b>
	<font face="Arial">Display Name</font></b></td>
   
   
    <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="22" align="center" ><b>
	<font face="Arial">Category</font></b></td>
   
   
  </tr>
  
    <tr>
    	

    <td  bordercolor="#CCCCFF" height="47"  align="center" >  	
		<font face="Arial">	
	<input type="text" name="BID" size="14"></font></td>   	

	

    <td  bordercolor="#CCCCFF" height="47"  align="center" >  	
		<font face="Arial">	
	<input type="text" name="Display" size="14" style="width: 220px"></font></td>   	

	

    <td  bordercolor="#CCCCFF" height="47"  align="center" >  	
		<font face="Arial">
	<select size="1" name="Category">
	<option selected>EMS</option>
		<option >EMS/FB</option>
		<option >COM</option>

	<option >Co-COM</option>
<option >FB</option>
	</select></font></td>   	

	

  </tr>
  </table>
</div>



</form>
   
  

</body>

 <%
 
  Function SaveData()

 

strBID = Trim(request.form("BID"))

strName = Replace(Request.form("Display"), "'", "''") 
strCategory = request.form("Category")

strsql = "Select ID from EMP where SSNO = '" & strBID & "'"
   Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionStringBadge")
    If not MyRec.eof then
    sEID = MyRec.fields("ID")
   MyREc.close
  strsql = "Insert into tblEMS (EID, S_Eid, Display_name, Category) Values('" & strBID & "', " & sEID & ", '" & strName & "', '" & strCategory & "')"

   
  	 set MyRec = new ASP_CLS_DataAccessBadge
         MyRec.ExecuteSql strSql 
         
          Response.redirect("EMS.asp")
              else 
                MyRec.close  %>
   <p align="center"> <Font face="arial" size="3"><b>A security number could not be found for that Employee ID number. 
    Please make sure that BID number is in the Security 101 software.</b></Font></p>
    
<% 

    
    end if      
   
  End Function
  
   %><!--#include file="footer.inc"-->