
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Recovered Paper Consumption</TITLE>


<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->


<%  
	Dim strsQL, rstEquip, rstMonth, objNew, strMonth, objGeneral
   	

   set objGeneral = new ASP_CLS_General

  if objGeneral.IsSubmit() Then
 	strMonth = request.form("Month")
 	strYear = request.form("Year")
 	If request.form("Version") = "Web" then
  Response.redirect("rptMobileReceipts.asp?id=" & strMonth & "&y=" & stryear)
  else
    Response.redirect("rptMobileReceipts_excel.asp?id=" & strMonth & "&y=" & stryear)
 
   end if 
  end if

%>



<style type="text/css">
.style1 {
	border-style: solid;
	border-width: 1px;
}
.auto-style2 {
	font-weight: bold;
	border: 1px solid #C0C0C0;
	background-color: #E7EBFE;
}
.auto-style3 {
	border: 1px solid #C0C0C0;
}
</style>
</head>


<form name="form1" action="RPC_Select_Month.asp" method="post">
	<div align="center">
<TABLE borderColor=#E7D1C2 cellSpacing=0 cellPadding=0 width="100%" border=0>  
  <tr><td colspan="5" size = "2" bordercolor="#FFFFFF"><b>
	<font face="Arial">Recovered Paper Order Consumption&nbsp;&nbsp;</font></td>
	</tr></table>
	<TABLE borderColor=#E7D1C2 cellSpacing=0 cellPadding=0 style="width: 50%" class="style1"> 
  <TR>
    <TD align="center" style="height: 46" class="auto-style2"  ><font face="Arial" size="2">Select Month</font></TD>

    <TD align="center" style="height: 46" class="auto-style2"  ><font face="Arial" size="2">Select Year</font></TD>

    <TD align="center" style="height: 46" class="auto-style2"  >
	<font face="Arial" size="2">Version</font></TD>

 </TR>

  <TR>
         <TD align="center" style="height: 72" class="auto-style3">
    <font face="Arial">
   <select size="1" name="Month">

<option>1</option>
<option>2</option>
<option>3</option>
<option>4</option>
<option>5</option>
<option>6</option>
<option>7</option>
<option>8</option>
<option>9</option>
<option>10</option>
<option>11</option>
<option>12</option>
      
     </select></font></TD>
         <TD align="center" style="height: 72" class="auto-style3">
    <font face="Arial">
   <select size="1" name="Year">
<option>2020</option>
<option>2019</option>

     </select></font></TD>
         <TD align="center" style="height: 72" class="auto-style3">
    <font face="Arial">
   <select size="1" name="Version">

     <option value="Web">Web Page</option>
     <option >Excel</option>
     </select></font></TD></tr>


     <TR>
    <TD align="center" colspan="3" class="auto-style2"  ><font face="Arial" size="2">
	<input type="submit"  value="View Report"  style="float: center"></font></TD>




 </TR>


  </TABLE></div>
</form>

<!--#include file="Fiberfooter.inc"-->