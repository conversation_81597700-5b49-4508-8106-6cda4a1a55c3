<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 6.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Recovered Paper Car Unload Entry</title>
</head>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->
<%

Dim strSQL, MyR<PERSON>, strid, strDateReceived, strCarLocation, strFQE, strFQG, strFQP, strBQE, strBQG, strBQP, strMQD, strMQL, strMQM, strMQH
Dim strSpecies, strSAP_Nbr, strVendor, strPO, strRelease_Nbr, strTrailer, strTons_Received, strGenerator, strTime_unloaded
Dim strGen_City, strGen_State, strREC_Number, strCarDate_Received, strTotal_Bales, strOCC_Tech, strGradingDate_Received
Dim strDate_Unloaded, strLocation, strOther_Comments, strNet, strDeduction, strCarrier, strAdhesive
Dim strFQExcellent, strFQGood, strFQPoor, strFiber_Quality_Reject, strFQRating, strBQRating
Dim strBQExcellent, strBQGood, strBQPoor, strBale_Quality_Reject
Dim strMoisture_Dry, strMoisture_Light, strMoisture_Medium, strMoisture_Heavy, strWeight_Sample_Bale, strNbr_Wet_Bales, strBales_Rejected
Dim strGround_Wood, strCardboard, strNewsprint, strPlastics, strBrightness, strWet_Strength, strBeater_Dyed
Dim strEnvelopes, strTrash, strDirt, strFiber_Comments, strBale_Comments, strMoisture_Comments
strid = Request.querystring("id")
Call getdata()


 set objGeneral = new ASP_CLS_General

  

if objGeneral.IsSubmit() Then


	Call SaveData() 

End if
%>
<body>
<form name="form1" action="Car_unload_entry.asp?id=<%=strid%>&t=<%=strTrailer%>" method="post">
 <input type="hidden" name="TR" value="<%= strTons_Received%>" >
<table border="0" cellpadding="0" cellspacing="0" width = 100% style="border-collapse: collapse" bordercolor="000000" >
  <tr>
    <td  bgcolor="#FFFFFF" bordercolor="#CCCCFF" >
    <p align="center"><font face="Arial" size="4">Recovered Paper Car Unload 
    Entry</font></td>

    <td align = right bgcolor="#FFFFFF"><Input name="Submit" type="submit" Value="Submit" ></td>
  </tr>
</table>
<table border="1" cellpadding="0" cellspacing="0" width = 100% style="border-collapse: collapse" bordercolor="000000" >
  <tr>
    <td bgcolor="#F0F0FF" bordercolor="#CCCCFF" >
  
    <font face="Arial" size="1">Species</font></b></td>
    <td bgcolor="#F0F0FF" bordercolor="#CCCCFF" >

    <font face="Arial" size="1">SAP Number</font></b></td>
    <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" >
 
    <font size="1" face="Arial">Vendor</font></b></td>
    <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" >
 
    <font size="1" face="Arial">PO</font></b></td>
    <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" >
  
    <font size="1" face="Arial">Release</font></b></td>
    <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" >
 
    <font size="1" face="Arial">Car/Trailer</font></b></td>
     <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" >
 
    <font size="1" face="Arial">Carrier</font></b></td>
    </tr>
  <tr height = 18>
    <td  bgcolor="white" bordercolor="#CCCCFF" height="19">
   <font size="1" face="Arial"><%= strSpecies%></td>
    <td  bgcolor="white" bordercolor="#CCCCFF" height="19"><font size="1" face="Arial"><%= strSAP_Nbr%></td>
    <td  bgcolor="white"  bordercolor="#CCCCFF" height="19"><font size="1" face="Arial"><%= strVendor%></td>
    <td bgcolor="white" " bordercolor="#CCCCFF" height="19"><font size="1" face="Arial"><%= strPO%></td>
    <td bgcolor="white"  bordercolor="#CCCCFF" height="19"><font size="1" face="Arial"><%= strRelease_Nbr%></td>
    <td bgcolor="white"  bordercolor="#CCCCFF" height="19"><font size="1" face="Arial"><%= strTrailer%></td>
  <td bgcolor="white"  bordercolor="#CCCCFF" height="19"><font size="1" face="Arial"><%= strCarrier%></td>
  </tr>

  <tr>
    <td bgcolor="#F0F0FF" bordercolor="#CCCCFF" height="21" align="left"><b>
    <font size="1" face="Arial">Generator</font></b></td>
    <td bgcolor="#F0F0FF" bordercolor="#CCCCFF" height="21" align="left"><b>
    <font size="1" face="Arial">Generator City</font></b></td>
    <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" height="21" align="left"><b>
    <font size="1" face="Arial">Generator State</font></b></td>
    <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" height="21" align="left"><b>
    <font face="Arial" size="1">Receipt Number</font></b></td>
    <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" height="21" align="left"><b>
    <font face="Arial" size="1">Date Received</font></b></td>
 <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" height="21"><b>
    <font face="Arial" size="1">&nbsp;Location</font></b></td>
   <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" height="21"><b>
    <font face="Arial" size="1">&nbsp;</font></b></td>
  </tr>
  <tr>
    <td  bordercolor="#CCCCFF"><font face="Arial" size="1"><%= strGenerator%></font></td>
    <td  bordercolor="#CCCCFF"><font face="Arial" size="1"><%= strGen_City%></font></td>
    <td  bordercolor="#CCCCFF"><font face="Arial" size="1"><%= strGen_State%></font></td>
    <td  bordercolor="#CCCCFF"><font face="Arial" size="1"><%= strRec_Number%></font></td>
    <td  bordercolor="#CCCCFF"><font face="Arial" size="1"><%= strDateReceived%></font></td>
     <td  bordercolor="#CCCCFF"><font face="Arial" size="1"><%= strCarLocation%></font></td>
   <td  bordercolor="#CCCCFF"><font face="Arial" size="1">&nbsp;</font></td>
  </tr>
</table>
<table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" height="51" >
    <tr>
    <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="18"><b>	<font face="Arial" size="1">Date Unloaded</font></b></td>
    <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="18"><b>	<font face="Arial" size="1">Location Unloaded</font></b></td>
    <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="18"><b>	<font face="Arial" size="1">Other Comments</font></b></td>
    <td  bgcolor="#FF0000" bordercolor="#000000" height="18" align="center"><b>	
	<font face="Arial" size="1">&nbsp;</font><font color="#FFFFFF"><font face="Arial" size="1">Total Bales&nbsp;</font><font face="Arial" size="2"><br><font size = 1>Required</font></font></b></td>
    <td bgcolor="#F0F0FF" bordercolor="#CCCCFF" align="center"><b>	<font face="Arial" size="1">&nbsp;OCC Tech</font></b></td>
    <td align = center bgcolor="#D9FFEC" bordercolor="#CCCCFF" >
	<b><font size="1" face="Arial">&nbsp;Tons Received</font></b></td>
    <td align = center bgcolor="#D9FFEC" bordercolor="#CCCCFF" >
<b>
    <font face="Arial" size="1">Deduction</font></b> </td>
    <td  bgcolor="#D9FFEC" bordercolor="#CCCCFF" align = center>
	
	<font face="Arial" size="1">&nbsp;Net&nbsp;</font></b></td>
   
  </tr>
  
    <tr>
    <td  bordercolor="#CCCCFF">	<font face="Arial">	<input type="text" name="Date_Unloaded" size="16" value = "<%= strDate_Unloaded%>"></font></td>
    <td bordercolor="#CCCCFF">	
	<font face="Arial">	
	<select size="1" name="OCC_Location">
	<option >BALDWIN</option>
	<option >BROKE CENTER</option>
	<option>DFF</option>
	<option>MERCHANTS</option>
	<option>MMS</option>
	<option <% if strSpecies = "OCC" or strSpecies = "DLK" then %>selected<% end if %>>OCC</option>
	<option <% if strSpecies = "KCOP" or strSpecies = "PMX"  or strSpecies = "SWL" then %>selected<% end if %>>RF</option>
	<option>TM BASEMENT</option>
	<option>WHSE16</option>
	<option>WHSE17</option>
	</select></font></td>
    <td bordercolor="#CCCCFF">	
	<font face="Arial">	
	<input type="text" name="Other_Comments" size="30" value = "<%= strOther_Comments%>"></font></td>
    <td  bordercolor="#CCCCFF" align="center" bgcolor="#FF0000">	
	<font face="Arial">	
	<input type="text" name="Total_Bales" size="7" value = "<%= Total_Bales%>"></font></td>
    <td  bordercolor="#CCCCFF" align="center">
	<font face="Arial" size="2">
	<%= strOCC_Tech%></font></td>
    <td  bgcolor="white" bordercolor="#CCCCFF">
	<p align="center">
	<font face="Arial">
	
	<% if Session("EmployeeID") = "B55403" or Session("EmployeeID") = "B44592" or Session("EmployeeID") = "C97338" then %>
	<input type="text" name="Tons_received" size="7" value = "<%= strTons_received%>">
	<% else %>
	<%= strTons_received%>
	<% end if %></font></td>
    <td  bgcolor="white"  bordercolor="#CCCCFF">
	<p align="center"><b> <font size="2" face="Arial">&nbsp;&nbsp;</font></b><font face="Arial"><font size="2">
	</font>
  <%= strDeduction%></font><b><font size="2" face="Arial">&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;</font></b></td>
    <td bgcolor="white"  bordercolor="#CCCCFF">	
	<p align="center">	
	<font face="Arial">	
	<%= strNet%></font></td>
    
  </tr>
  
  </table>



<table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" height="240">
  <tr>
    <td width="35%" bgcolor="#FFFFD7" bordercolor="#000000" height="19" colspan="6">
    <p align="center"><b><font size="2" face="Arial">Fiber Quality</font></b></td>
    <td width="21%" bgcolor="#FFFFD7" bordercolor="#000000" height="19" colspan="4">
    <p align="center"><b><font size="2" face="Arial">Bale Quality</font></b></td>
    <td width="48%" bgcolor="#FFFFD7" bordercolor="#000000" colspan="8" height="19">
    <p align="center"><b><font size="2" face="Arial">Moisture Rating</font></b></td>
  </tr>
  <tr>
  
    <td width="8%" bgcolor="#FFFFD7" bordercolor="#000000" colspan="2" height="36">
    <p align="center"><b>
    <font face="Arial" size="1"><br>Excellent</font></b></td>
    <td width="7%" bgcolor="#FFFFD7" bordercolor="#000000" colspan="2" height="36">
    <p align="center"><b>
    <font size="1" face="Arial"><br>Good</font></b></td>
    <td width="5%" bgcolor="#FFFFD7" bordercolor="#000000" height="36">
    <p align="center"><b>
    <font size="1" face="Arial"><br>Poor</font></b></td>
    <td width="7%" bgcolor="#FFFFD7" bordercolor="#000000" height="36">
    <p align="center">
    <font size="1" face="Arial">Down Grade Reject</font></td>
    <td width="7%" bgcolor="#FFFFD7" bordercolor="#000000" height="36">
    <p align="center"><b>
    <font face="Arial" size="1"><br>&nbsp;Excellent&nbsp;</font></b></td>
    <td width="6%" bgcolor="#FFFFD7" bordercolor="#000000" height="36">
    <p align="center"><b>
    <font size="1" face="Arial"><br>Good</font></b></td>
    <td width="6%" bgcolor="#FFFFD7" bordercolor="#000000" height="36">
    <p align="center"><b>
    <font size="1" face="Arial"><br>Poor</font></b></td>
    <td width="6%" bgcolor="#FFFFD7" bordercolor="#000000" height="36">
    <p align="center">
    <font size="1" face="Arial">Down Grade <br>Reject </font></td>
    <td width="8%" bgcolor="#FFFFD7" bordercolor="#000000" colspan="1" height="36" align="center"><b>
    <font size="1" face="Arial"><br>Dry</font></b></td>
    <td width="5%" bgcolor="#FFFFD7" bordercolor="#000000" height="36" align="center"><b>
    <font size="1" face="Arial"><br>Light</font></b></td>
    <td width="4%" bgcolor="#FFFFD7" bordercolor="#000000" height="36" align="center">
    <p align="center"><b><font size="1" face="Arial"><br>Medium</font></b></td>
        <td width="4%" bgcolor="#FFFFD7" bordercolor="#000000" height="36" align="center">
    <p align="center"><b><font size="1" face="Arial"><br>Heavy</font></b></td>
    <td width="27%" bgcolor="#FFFFD7" bordercolor="#000000" colspan="2" height="36">
	<p align="center"><font size = 2 face="Arial"><b>
    Heavy</b><font size = 1> - weigh a bale<br> to represent all wet bales 
    marked below</font></b></td>
  </tr>
  <tr>
   
    <td colspan = 5   align = center>
 <fieldset style="padding: 0" >
	
	&nbsp;&nbsp; &nbsp;<input type="radio" value="3" name="FQ" >&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	<input type="radio" name="FQ" value="2">	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	<input type="radio" name="FQ" value="1"></fieldset></td>
  
    <td width="7%" height="27" align = center>
	<input type="text" name="Fiber_Quality_Reject" size="3" value = "<%= strFiber_Quality_Reject%>"></td>

    <td colspan = 3   align = center>
 <fieldset style="padding: 0">
	
	<input type="radio" value="3" name="BQ">&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; 
	<input type="radio" name="BQ" value="2">	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	<input type="radio" name="BQ" value="1"></fieldset></td>
    <td width="6%" height="27">
	<p align="center">
	<input type="text" name="Bale_Quality_Reject" size="3" value = "<%= strBale_Quality_Reject%>"></td>
        <td colspan = 4   align = center>
 <fieldset style="padding: 0">
	
	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <input type="radio" value="3" name="MQ">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	<input type="radio" name="MQ" value="2">	&nbsp;&nbsp;&nbsp; &nbsp;
	<input type="radio" name="MQ" value="1">   &nbsp;&nbsp;&nbsp; &nbsp;
	<input type="radio" name="MQ" value="0"></fieldset></td>
    <td width="15%" bgcolor="#FFFFD7" bordercolor="#000000" height="27"><b>
    <font face="Arial" size="1">Weight of Sample Bales</font></b></td>
    <td width="12%" height="27">
	<p align="center">
	<input type="text" name="Weight_Sample_Bale" size="5" value = "<%= strWeight_Sample_Bale%>"></td>
  </tr>
  <tr>
    <td width="35%" bgcolor="#FFFFD7" bordercolor="#000000" colspan="6" height="37"> <b>
    <font face="Arial" style="font-size: 9pt">Bale Count - Down Grade/Reject 
    </font></b></td>
    <td width="35%" bgcolor="#FFFFFF" bordercolor="#000000" colspan="2" height="37" align = center>
	
	<%= strBales_Rejected%></td>
 <td width="33%" bgcolor="#FFFFD7" bordercolor="#000000" colspan="6" height="37">&nbsp;</td>
    <td width="15%" bgcolor="#FFFFD7" bordercolor="#000000" height="37"><b>
    <font size="1" face="Arial">Number of Bales to Apply</font></b></td>
    <td width="12%" height="37">
	<p align="center">
	<input type="text" name="Nbr_Wet_Bales" size="5" value = "<%= strNbr_Wet_Bales%>"></td>
  </tr>


  <tr>
    <td width="72%" colspan="14" bgcolor="#FFFFD7" height="25">
    <p align="center"><b><font face="Arial" style="font-size: 9pt">Contaminants</font></b></td>
    <td width="15%" bgcolor="#FFFFD7" height="25" bordercolor="#FFFFD7">&nbsp;</td>
    <td width="29%" bgcolor="#FFFFD7" bordercolor="#FFFFD7" colspan="2" height="25">&nbsp;</td>
  </tr>
  <tr>
    <td width="7%" bgcolor="#FFFFD7" height="40" align="center"><b><font face="Arial" size="1">
    Ground Wood</font></b></td>
    <td width="11%" colspan="2" bgcolor="#FFFFFF" height="40" align = center><input type="checkbox" name="Ground_Wood" value="ON"></td>
    <td width="6%" colspan="2" bgcolor="#FFFFD7" height="40" align="center"><b><font face="Arial" size="1">
    Wet Strength</font></b></td>
    <td width="7%" colspan="1" bgcolor="#FFFFFF" height="40" align = center><input type="checkbox" name="Wet_Strength" value="ON"></td>
  
    <td width="7%" bgcolor="#FFFFD7" height="40" align="center"><b><font face="Arial" size="1">
    Cardboard</font></b></td>
    <td width="6%" colspan="1" bgcolor="#FFFFFF" height="40" align = center><input type="checkbox" name="Cardboard" value="ON"></td>
    <td width="6%" bgcolor="#FFFFD7" height="40" align="center"><b><font face="Arial" size="1">
    Beater Dyed</font></b></td>
    <td width="6%" colspan="1" bgcolor="#FFFFFF" height="40" align = center><input type="checkbox" name="Beater_Dyed" value="ON"></td>
  
    <td width="8%" bgcolor="#FFFFD7" height="40" align="center"><b><font face="Arial" size="1">
	Newsprint</font></b></td>
    <td width="5%" colspan="1" bgcolor="#FFFFFF" height="40" align = center><input type="checkbox" name="Newsprint" value="ON"></td>
    <td width="9%" bgcolor="#FFFFD7" height="40" align="center" colspan="2">&nbsp;</td>
  <td width="15%" bgcolor="#FFFFD7" height="40" bordercolor="#FFFFD7">&nbsp;</td>
    <td width="12%" bgcolor="#FFFFD7" height="40" bordercolor="#FFFFD7">&nbsp;</td>
    <td width="1%" bgcolor="#FFFFD7" bordercolor="#FFFFD7" colspan="2" height="40">&nbsp;</td>
  </tr>

  <tr>
    <td width="7%" bgcolor="#FFFFD7" height="31" align="center"><b><font face="Arial" size="1">
    Plastic</font></b></td>
    <td width="11%" colspan="2" bgcolor="#FFFFFF" height="31" align = center><input type="checkbox" name="Plastics" value="ON"></td>
    <td width="6%" colspan="2" bgcolor="#FFFFD7" height="31" align="center"><b><font face="Arial" size="1">
    Trash</font></b></td>
    <td width="7%" colspan="1" bgcolor="#FFFFFF" height="31" align = center><input type="checkbox" name="Trash" value="ON"></td>
    <td width="7%" bgcolor="#FFFFD7" height="31" align="center"><b><font face="Arial" size="1">
	Brightness</font></b></td>
    <td width="6%" colspan="1" bgcolor="#FFFFFF" height="31" align = center><input type="checkbox" name="Brightness" value="ON"></td>
    <td width="6%" bgcolor="#FFFFD7" height="31" align="center"><b>
	<font face="Arial" size="1">Dirt</font></b></td>
    <td width="6%" colspan="1" bgcolor="#FFFFFF" height="31" align = center><input type="checkbox" name="Dirt" value="ON"></td>
       <td width="8%" bgcolor="#FFFFD7" height="40" align="center"><b>
	<font face="Arial" size="1">Envelopes</font></b></td>
    <td width="5%" colspan="1" bgcolor="#FFFFFF" height="40" align = center><input type="checkbox" name="Envelopes" value="ON"></td>
           <td width="5%" bgcolor="#FFFFD7" height="40" align="center"><b>
	<font face="Arial" size="1">Adhesives</font></b></td>
   <td width="5%" colspan="1" bgcolor="#FFFFFF" height="40" align = center>
    <input type="checkbox" name="Adhesive" value="ON" <% if strAdhesive = -1 then%> checked <%end if %>></td>
 
    <td width="12%" bgcolor="#FFFFD7" height="40" bordercolor="#FFFFD7">&nbsp;</td>
    <td width="1%" bgcolor="#FFFFD7" bordercolor="#FFFFD7" height="40">&nbsp;</td>
  </tr>
</table>
   <table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" >
  <tr>
    <td  bgcolor="#FFFFD7" height="19" bordercolor="#000000" align = center>
  <b><font size="2" face="Arial">Fiber Quality Comments</font></b></td>
    <td  bgcolor="#FFFFD7" height="19" bordercolor="#000000" align = center>
   <b><font face="Arial" size="2">Bale Quality Comments</font></b></td>
       <td  bgcolor="#FFFFD7" height="19" bordercolor="#000000" align = center>
        <b><font size="2" face="Arial">Moisture Rating Comments</font></b></td>
  </tr>



  <tr>
    <td  bgcolor="#FFFFFF" height="45">
    <textarea rows="3" name="Fiber_Comments" cols="40"><%= strFiber_Comments%></textarea></td>
    
    

    <td bgcolor="#FFFFFF" bordercolor="#000000"  height="45">
    <textarea rows="3" name="Bale_Comments" cols="38"><%= strBale_Comments%></textarea></td>
  
    <td  bgcolor="#FFFFFF" bordercolor="#000000" height="45">
      <p align="center">
      <textarea rows="3" name="Moisture_Comments" cols="35"><%= strMoisture_Comments%></textarea></td>

  </tr>

</table>
</form>
  <%
  Function getData() 
  strid = Request.querystring("id")
  strSql="SELECT tblCars.* FROM tblCars WHERE CID= '" & strid & "'" 
  
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString")
  
  strid = Request.querystring("id")
  strSpecies = MyRec.Fields("Species")
  strSAP_Nbr = MyRec.Fields("SAP_Nbr")
  strVendor = MyRec.Fields("Vendor")
  strPO = MyRec.Fields("PO")
  strRelease_Nbr = MyRec.Fields("Release_Nbr")
  strTrailer = MyRec.Fields("Trailer")
  strTons_Received = MyRec.Fields("Tons_Received")
  strGenerator = MyRec.Fields("Generator")
  strGen_City = MyRec.Fields("Gen_City")
  strGen_State = MyRec.Fields("Gen_State")
  strREC_Number = MyRec.Fields("REC_Number")
  strDateReceived = MyRec.Fields("Date_received")
  strOCC_Tech = Session("Ename")
  strDate_Unloaded = formatdatetime(Now(),2)
  strCarLocation = MyRec.fields("Location")
  strOther_Comments = MyRec.fields("Other_Comments")
  strCarrier = MyRec.fields("Carrier")
  MyRec.close
  
  End Function
  
  Function SaveData()
  strid = Request.querystring("id")
  strTrailer = Request.querystring("t")
  
 'If isdate("Date_Unloaded") = true then
  strDate_Unloaded = request.Form("Date_Unloaded")
 ' else
 ' strDate_unloaded =  formatdatetime(Now(),2)
 ' end if
  strLocation = request.Form("OCC_Location")
  strOther_Comments = Replace(request.Form("Other_Comments"), "'", "''") 
  strGradingDate_Received = request.Form("Date_Received")
  
  strTotal_Bales = request.Form("Total_Bales")
  If strTotal_bales = "" or isnull(strTotal_Bales) then
  strTotal_Bales = 0
  end if
  strOCC_Tech = request.Form("OCC_Tech")

  strFiber_Quality_Reject = request.Form("Fiber_Quality_Reject")
  strBale_Quality_Reject = request.Form("Bale_Quality_Reject")
  


  strWeight_Sample_Bale = request.Form("Weight_Sample_Bale")
  if strWeight_Sample_Bale = "" or isnull(strWeight_Sample_Bale) then
  strWeight_Sample_Bale = 0
  end if 
  
  strNbr_Wet_Bales = request.Form("Nbr_Wet_Bales")
  if strNbr_Wet_Bales = "" or isnull(strNbr_Wet_Bales) then
  strNbr_Wet_Bales = 0
  end if 
  
  strFiber_Comments = Replace(request.Form("Fiber_Comments"), "'", "''") 
  strBale_Comments = Replace(request.Form("Bale_Comments"), "'", "''") 
  strMoisture_Comments = Replace(request.Form("Moisture_Comments"), "'", "''") 

  If Request.form("FQ") = "3" then
  strFQExcellent = 3
  strFQE = 1
  else
  strFQExcellent = 0
  strFQE = 0
  end if 
  
  If Request.form("FQ") = "2" then
  strFQGood = 2
  strFQG = 1
  else
  strFQGood = 0
  strFQg = 0
  end if 
  
  If Request.form("FQ") = "1" then
  strFQPoor = 1
  strFQP = 1
  else
  strFQPoor = 0
  strFQP = 0
  end if 
  
  If Request.form("BQ") = "3" then
  strBQExcellent = 3
  strBQE =1
  else
  strBQExcellent = 0
  strBQE = 0
  end if 
  
  If Request.form("BQ") = "2" then
  strBQGood = 2
  strBQG = 1
  else
  strBQGood = 0
  strBQG = 0
  end if 
  
  If Request.form("BQ") = "1" then
  strBQPoor = 1
  strBQP = 1
  else
  strBQPoor = 0
  strBQP = 0
  end if 
  
  
  
  If Request.form("MQ") = "3" then
  strMoisture_Dry = 3
  strMQD = 1
  else
  strMoisture_Dry = 0
  strMQD = 0
  end if 
  
  If Request.form("MQ") = "2" then
  strMoisture_Light = 2
  strMQL = 1
  else
  strMoisture_Light= 0
  strMQL= 0
  end if 
  
  If Request.form("MQ") = "1" then
  strMoisture_Medium  = 1
  strMQM = 1
  else
  strMoisture_Medium  = 0
  strMQM = 0
  end if 
  
  If Request.form("MQ") = "0" then
  strMoisture_Heavy  = 1
  strMQH = 1
  else
  strMoisture_Heavy  = 0
  strMQH = 0
  end if 
  

  If request.form("Ground_Wood") = "ON" then
  strGround_Wood = 1
  else
  strGround_Wood = 0
  end if
  
  If request.form("Cardboard") = "ON" then
  strCardboard = 1
  else
  strCardboard = 0
  end if
  
  If request.form("Newsprint") = "ON" then
   strNewsprint = 1
  else
   strNewsprint = 0
  end if
  
  If request.form("Plastics") = "ON" then
   strPlastics = 1
  else
   strPlastics = 0
  end if
  
    If request.form("Brightness") = "ON" then
   strBrightness = 1
  else
   strBrightness = 0
  end if
  
  If request.form("Adhesive") = "ON" then
   strAdhesive = 1
  else
   strAdhesive = 0
  end if
  
  If request.form("Wet_Strength") = "ON" then
   strWet_Strength = 1
  else
   strWet_Strength = 0
  end if
  
   If request.form("Beater_Dyed") = "ON" then
   strBeater_Dyed = 1
  else
   strBeater_Dyed = 0
  end if
 
 
  If request.form("Envelopes") = "ON" then
   strEnvelopes = 1
  else
   strEnvelopes = 0
  end if
  
   If request.form("Trash") = "ON" then
   strTrash = 1
  else
   strTrash = 0
  end if
  
   If request.form("Dirt") = "ON" then
   strDirt = 1
  else
   strDirt = 0
  end if
  
  
  If isnull(strFiber_Quality_Reject) or strFiber_Quality_Reject = "" then
  strFiber_Quality_Reject = 0
  end if 
  
  If isnull(strBale_Quality_Reject) or strBale_Quality_Reject = "" then
  strBale_Quality_Reject = 0
  end if 
  
  strBales_Rejected = cint(strFiber_Quality_Reject) + cint(strBale_Quality_Reject)
 ' calculate deduction for rejected bales 
  If  strBales_rejected > 0 then
   		if Session("EmployeeID") = "B55403" or Session("EmployeeID") = "B44592" or Session("EmployeeID") = "C97338" then 
  		strTons_received = Request.form("Tons_received")
  		else  
 		strTons_received = Request.form("TR")
 		end if
	
	If strTons_Received  = "" or isnull("strTons_Received") then
	strDeduction = 0
	else
	strDeduction = round((cint(strBales_rejected) / cint(strTotal_Bales)) * (strTons_received),3)
	end if
else
 strDeduction = 0
end if


  
  
' calculate deduction with moisture
If  strMoisture_Medium = 1 then
	if Session("EmployeeID") = "B55403" or Session("EmployeeID") = "B44592" or Session("EmployeeID") = "C97338" then 
  		strTons_received = Request.form("Tons_received")
  		else  
 		strTons_received = Request.form("TR")
 		end if
	
	If strTons_Received  = "" or isnull("strTons_Received") then
	strDeduction = 0
	else
	strDeduction = round(strDeduction + (strTons_received * 0.1),3)
	end if
else
' do nothing
end if

	if Session("EmployeeID") = "B55403" or Session("EmployeeID") = "B44592" or Session("EmployeeID") = "C97338" then 
  		strTons_received = Request.form("Tons_received")
  		else  
 		strTons_received = Request.form("TR")
 		end if
	

  strNet = round (strTons_received - strDeduction, 3)
  strTime_unloaded = formatdatetime(Now(),2)
    strFQRating = cint(StrFQExcellent) + cint(strFQGood) + cint(strFQPoor)
    strBQRating = cint(StrBQExcellent) + cint(strBQGood) + cint(strBQPoor)
  
  strsql = "INSERT INTO tblOCCGrading (  FQ_Rating, BQ_Rating, CID, OCC_Tech, Trailer_number, Total_Bales, Date_received, Fiber_Quality_Reject, "_
  &" Weight_Sample_Bale, Nbr_wet_bales, Bale_Quality_Reject, Fiber_Comments, Bale_Comment, Moisture_Comment, Fiber_Quality_Excellent, "_
  &" Fiber_Quality_Good, Fiber_Quality_Poor, Bale_Quality_Excellent, Bale_Quality_Good, Bale_Quality_Poor, Bales_rejected, "_
  &" FQ_Excellent, FQ_Good, FQ_Poor, BQ_Excellent, BQ_Good, BQ_Poor, Moisture_Dry, Moisture_Light, Moisture_Medium, Moisture_Heavy, "_
  &" Ground_Wood, Cardboard, Newsprint, Plastics, Brightness, Adhesive,  Wet_strength, Beater_dyed, Envelopes, Trash, Dirt ) "_ 
  &" Select  " & strFQRating & "," & strBQRating & ",  " & strid & ", '" & session("Owner") & "', '" & strTrailer & "', "_
  &" " & cint(strTotal_Bales) & ", '" & strDate_unloaded & "', " & cint(strFiber_Quality_Reject) & ", " & strWeight_Sample_Bale & ", "_
  &" " & cint(strNbr_Wet_Bales) & ", " & cint(strBale_Quality_Reject) & ", '" & strFiber_Comments & "', '" & strBale_Comments & "', '" & strMoisture_Comments & "', "_
  &" " & cint(strFQExcellent) & ",  " & cint(strFQGood) & ", " & cint(strFQPoor) & ", " & cint(strBQExcellent) & ", " & cint(strBQGood) & ", " & cint(strBQPoor) & ", " & cint(strBales_rejected) & ", "_
  &" " & strFQE & ", " & strFQG & ", " & strFQP & ", " & strBQE & ", " & strBQG & ", " & strBQP & ", " & strMQD & ", " & strMQL & ", " & strMQM & ", " & strMQH & ", "_
  &" " & strGround_wood & ", " & strCardboard & ", " & strNewsprint & ", " & strPlastics & ", " & strBrightness & ", " & strAdhesive & ", " & strWet_strength & ", "_
  &" " & strBeater_dyed & ", " & strEnvelopes & ", " & strTrash & ", " & strDirt & ""

  
  	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
       

strsql = "Update tblCars set  Time_unloaded = '" & strTime_unloaded & "', Tons_received = " & strTons_received & ", Other_comments = '" & strOther_comments & "',  Deduction = " & strDeduction & ", Net = " & strNet & ",  Bales_RF = " & cint(strTotal_Bales) & " where CID = " & strid & ""
   	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
          If strLocation = "RF" or strLocation = "OCC" or  strLocation = "BROKE CENTER" or strLocation = "WHSE17" then
         strsql = "Update tblCars set Inv_depletion_date = '" &  strDate_unloaded & "' where CID = " & strid & ""
 
 	     set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql
         end if
         
          Response.redirect("Grade_Initial_Loads.asp")
  End Function
  
 
   %>            
  

</body>

</html>
<!--#include file="Fiberfooter.inc"-->