																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Edit Records</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->




<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate, strsql3

strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -60, strtdate)

    strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "' and User_type = 'A'"


    Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL3, Session("ConnectionString")


If not Myrec2.eof then


strsql = "SELECT tblCars.* FROM tblCars WHERE Date_received > '" & strdate & "' and (NFID = 0 or NFID is null ) and Trailer <> 'UNKNOWN' and Carrier <> 'RAIL'  and Grade = 'WADDING' order by CID desc"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<style type="text/css">
.style1 {
	text-align: center;
}
.style2 {
	font-size: x-small;
}
.style3 {
	font-size: x-small;
	font-weight: bold;
}
</style>
</head>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Wadding Loads Received in Last Sixty Days</b></font></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1>  
	 <tr class="tableheader">
<td>&nbsp;</td>
	<td>  <font face="Arial" size="1">Print<br>Receipt</td>
 
	
		<td  ><font face="Arial" size="1"><b>Trailer</b></font></td>
				 
		<td  ><font face="Arial" size="1"><b>Carrier</b></font></td>
		<td  ><font face="Arial" size="1">Species</font></td>
    
		<td  ><font face="Arial" size="1">Vendor</font></td>
		<td  >       <font face="Arial" size="1">PO Number</font></td>
	<td>       <font face="Arial" size="1">REC Number</font></td>

 
		<td  >        <p align="center">        <font face="Arial" size="1">Tons<br> Received</font></td>
	
 
		<td>        <font face="Arial" size="1">Date<br> Received</font></td>
		<td  >        <font face="Arial" size="1">Generator</font></td>
	 
			<td class="style1"  >        <font face="Arial" size="1">Location</font></td>
		<td  >        <font size="1" face="Arial">Other</font></td>
	<td  >        <font size="1" face="Arial">Delete<br>Receipt</font></td>
<td><font size="1" face="arial">Rejected</font></font></td>
	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    <%  if MyRec("Entry_page") = "EnterSTOWaddingReceipt"   then %>
        <td class="auto-style1"> <font size="1" face="Arial"><a href="EditSTOWadReceipt.asp?cid=<%= MyRec.fields("CID") %>">Edit</a></td>
        <% elseif MyRec("Entry_page") = "EnterSTOWADReceipt" or MyRec("Entry_page") = "EnterSTOReceipt" then %>
           <td class="auto-style1"> <font size="1" face="Arial"><a href="EditSTOWadReceipt.asp?cid=<%= MyRec.fields("CID") %>">Edit</a></td>
<% elseif MyRec("Entry_Page") = "STO_Broke_Receipt"  or MyRec("Entry_Page") = "STO_Receipt" then %>
 <td class="auto-style1"> <font size="1" face="Arial"><a href="Edit_STO_Broke_Receipt.asp?cid=<%= MyRec.fields("CID") %>">Edit</a></td>
 <% elseif MyRec("Entry_Page") = "Generic_STO_Receipt" then %>
 <td class="auto-style1"> <font size="1" face="Arial"><a href="Edit_Generic_STO_Receipt.asp?cid=<%= MyRec.fields("CID") %>">Edit</a></td>
 <% elseif MyRec("Entry_Page") = "Generic_STO_WAD_Receipt" then %>
 <td class="auto-style1"> <font size="1" face="Arial"><a href="Edit_Generic_STO_WAD_Receipt.asp?cid=<%= MyRec.fields("CID") %>">Edit</a></td>

<% end if %>


	<td align = center> <font size="1" face="Arial"><a href="STO_Wadding_Truck_receipt.asp?id=<%= MyRec.fields("CID") %>"><%= MyRec.fields("CID")%></a></td>
 
 

	<td class="style3"  > <font face="Arial"><%= MyRec.fields("Trailer")%></font></td>
	
 
			<td  ><b><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Carrier")%></span>&nbsp;</font></b></td>


		<td class="style2"  ><font face="Arial"> <%= MyRec.fields("Species")%></font></td>
		<td><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Vendor")%></span>&nbsp;</font></td>
		<td  >        <font size="1" face="Arial">        <span class="style2">        <%= MyRec.fields("PO")%></span>&nbsp;</font></td>
		<% if isnull(MyRec.fields("REC_Number")) then %>
			<td class="style2"  >  <font face="Arial">        <%= MyRec.fields("CID")%></font></td>
			<% else %>
	<td class="style2"  >  <font face="Arial">        <%= MyRec.fields("REC_Number")%></font></td>
	<% end if %>
	
		<td align = right <% if MyRec.fields("Tons_received") = 21  or MyRec.fields("Weigh_required") = "W" then %>bgcolor = pink <% end if %>  >
				 <font size="1" face="Arial" class="style2">        <%= MyRec.fields("Tons_received")%>&nbsp;<%= MyRec.fields("Weigh_required") %></font></td>
				 

		<td class="style2"  >		 <font face="Arial">        <%= MyRec.fields("Date_Received")%></font></td>
       <td  >		 <font size="1" face="Arial">        <span class="style2">        <%= MyRec.fields("Generator")%></span>&nbsp;</font></td>
 
		<td align="center"  > <font size="1" face="Arial"><span class="style2"> <a href="Change_location.asp?id=<%= MyRec("CID") %>"> <%= MyRec.fields("Location")%></a>&nbsp;</font></td>

		<td  >	 <font size="1" face="Arial">        <%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>
<td align = center >	 <font size="1" face="Arial"><a href="Maint_ReceiptDelete.asp?id=<%= Myrec.fields("CID")%>"> Delete</a></font></td>
<% 
if MyRec.fields("Rejected") = "YES" then %>
<td align = center >	 <font size="1" face="Arial">
<a href="Rejected_load.asp?id=<%= Myrec.fields("CID")%>">YES</a></font></td>
<% else %>
<td align = center >	 <font size="1" face="Arial">

<a href="Rejected_load.asp?id=<%= Myrec.fields("CID")%>">NO</a></font></td>
<% end if %>
	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>

<% Myrec2.close 
end if %><!--#include file="Fiberfooter.inc"-->