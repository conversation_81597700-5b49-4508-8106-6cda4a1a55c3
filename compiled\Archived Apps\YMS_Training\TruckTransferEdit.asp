<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Truck Load Transfer from Baldwin</title>
</head>
<%
    Dim strSQL, MyRec, strID, objMOC, rstFiber, strCarrier
      
    Dim strTrailer, strSpecies, strESpecies
    Dim strReleaseNbr
    Dim strRECNbr
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strGenerator
    Dim strGenCity
    Dim strGenState
    Dim strOther, strR, strsql3, strTransTrailer

       strId  = Trim(Request.QueryString("id")) 


 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
 
		Call SaveData() 

	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to delete a Receipt.</font></br>")
	MyRec.close
	end if

  
ELSE


strsql = "SELECT tblCars.* from tblCars WHERE tblCars.CID = " & strid

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


    strTrailer = MyRec.fields("Trailer")
    strSpecies = MyRec.fields("Species")

    

MyRec.close
Call getdata()
	end if

%>



<body><br>
<table width = 100%><tr><td width = 33%></td><td align = center width = 34%><b><font face="Arial" size="4">
Transfer Load from Baldwin</font> </b></td><td align = right width = 33%><a href="Transfer_Trucks.asp"><b><font face = arial size = 2>RETURN</a></font></b></td></tr></table>



<form name="form1" action="TruckTransferEdit.asp?id=<%=strid%>&r=<%= strR%>&s=<%= strSpecies%>" method="post">
<table border="1" cellspacing="0" bordercolor="#CCCCFF" width="60%" bgcolor="#CCCCFF" style="border-collapse: collapse" cellpadding="0" align = center>
<tr>
    <td bgcolor="#CCCCFF">&nbsp;</td>

    <td  bgcolor="#CCCCFF">&nbsp;</td>
  </tr>
  <tr>
    <td  align = right bgcolor="#CCCCFF" >
   <b>
   <font face="Arial" size="2">Original Trailer:&nbsp;</font></b></td>
<td  align = left><font face = arial>

    <%= strTrailer%></td></tr>

 
  <tr><td>&nbsp;</td>
  <td>&nbsp;</td></tr>
    <tr>

      <td  bgcolor="#CCCCFF" align = right>
  <font face="Arial" size="2"><b>Transfer Trailer Number:&nbsp;</b></font></td>
<td  align = left>

      <input type="text" name="Trans_Trailer" size="15" value = "<%= strTransTrailer%>"></td></tr>
      
       <tr><td>&nbsp;</td>
  <td>&nbsp;</td></tr>
    <tr>
  <tr>

      <td  bgcolor="#CCCCFF" align = right>
  <font face="Arial" size="2"><b>Receipt Number:&nbsp;</b></font></td>
<td  align = left>

      <input type="text" name="REC_Number" size="15" value = "<%= strRECNbr%>"></td></tr>
      <tr>
    <td  bgcolor="#CCCCFF">&nbsp;</td>

    <td bgcolor="#CCCCFF">&nbsp;</td>
  </tr>
<tr>
    <td  bgcolor="#CCCCFF">  
	<p align="right">  <font face="Arial" size="2"><b>Select Carrier:&nbsp;</b></font></td>
    <td bgcolor="#CCCCFF">   <select name="Carrier">
 	<option value="BWIF" selected>BWIF</option>
      <%= objGeneral.OptionListAsString(rstFiber, "Carrier", "CDesc", strCarrier) %>
     </select></td>
  </tr>
  <tr>
    <td  bgcolor="#CCCCFF">&nbsp;</td>

    <td bgcolor="#CCCCFF">&nbsp;</td>
  </tr>



       <tr>
          <td  align = right bgcolor="#CCCCFF" >
    <font face="Arial" size="2"><b>Date Transferred:&nbsp;</b></font></td>
<td align = left>

      <input type="text" name="Date_Received" size="15" value = "<%= strDateReceived%>"></td></tr>
<tr>
    <td  bgcolor="#CCCCFF">&nbsp;</td>

    <td bgcolor="#CCCCFF">&nbsp;</td>
  </tr>

  <tr>
    <td bgcolor="#CCCCFF">&nbsp;</td>

    <td align = left bgcolor="#CCCCFF"><Input name="Update" type="submit" Value="Submit" ></td>
  </tr>
</table>

</form>
</body>
<%
 Function GetData()
  set objMOC = new ASP_CLS_FIBER
        set rstFiber = objMOC.FiberCarrier()
        strDateReceived = formatdatetime(Now(),2)
End Function



 Function SaveData()
strTrailer = Request.form("Trans_Trailer")
strESpecies = Request.querystring("s")
strCarrier = Replace(Request.form("Carrier"), "'", "''")

strDateReceived = Request.form("Date_received")
strRECNbr = Request.form("Rec_Number")

         strSql = "Update tblCars Set Trans_Carrier = '" & strCarrier & "',  Transfer_Trailer_nbr = '" & strTrailer & "', "_
         &" Transfer_date = '" & strDateReceived & "', "_
         &" PMO_Nbr = '" & strRECNbr & "', Location = 'Yard' where CID = " & strid & ""

         
 	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
 dim strbody, strbody2, strEmailTo, strECC, strEBCC      
         


	       strEmailTo = "<EMAIL>, <EMAIL>"
	        
          
      

            strEBCC = "<EMAIL> "
         
                 
			Set objMail = Server.CreateObject("CDO.Message")		
			objMail.From = "<EMAIL>"
			objMail.To = strEmailTo
			objMail.CC = strECC
			'objMail.BCC = strEBCC
		

			objMail.Subject = "Truck of Recycle Fiber Arrived FROM Baldwin going to Yard"
			strBody2 = "<table border=0  cellpadding=0 cellspacing=0 width=800><tr><td align = center><font face = arial size = 2>&nbsp; Carrier&nbsp; </td><td align = left><font face = arial size = 2>Trailer&nbsp; </td><td align = left><font face = arial size = 2>Species&nbsp; </td><td align = left><font face = arial size = 2>Internal Control Number&nbsp; </td></tr>"
			
          strbody2 = strbody2 & " <tr><td align = center><font face = arial size = 1> " & strCarrier &  "</td><td align = left><font face = arial size = 1>" & strTrailer & "</td><td align = left><font face = arial size = 1>" & strESpecies & "</td><td align = left><font face = arial size = 1>" & strid & "</td></tr> "
   
				
		objMail.HTMLBody = "<font face = arial size = 2>Truck of Recycle Fiber Arrived FROM Baldwin going to Yard:<br><br>" & strbody2



			' objMail.Send
			Set objMail = nothing	       
         
         
     
         
     
         
         
         
         

Response.redirect ("Transfer_Trucks.asp")



End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->