<html xmlns:m="http://schemas.microsoft.com/office/2004/12/omml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 14.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Enter Trailer Receipt</title>
<style type="text/css">
.style1 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.style2 {
	font-family: Arial, Helvetica, sans-serif;
	font-weight: bold;
	font-size: x-small;
}
.style3 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	padding: 5px;
}
.style4 {
	text-align: left;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID, strRelease, MyConn, objMOC, rstFiber, strLocation, strSAP
    Dim strTrailer, strCarrier, strSQL3, MyConn3, strVID, strUOM, strPounds, strRail, strDate_shipped
    Dim strReleaseNbr, strTrailerTID, strBales, stralert
    Dim strRECNbr
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strGenerator
    Dim strGenCity
    Dim strGenState, strOrigin
    Dim strOther, strR,  gSpecies, gPO, gSAP_Nbr, gVendor
    Dim MyRec5, strsql5, strCarID

    strid  = Request.QueryString("id") 


 	set objGeneral = new ASP_CLS_General



if objGeneral.IsSubmit() Then

If Session("EmployeeID") = "U03085" OR Session("EmployeeID") = "B06916" OR Session("EmployeeID") = "B53909" OR Session("EmployeeID") = "B55548" OR Session("EmployeeID") = "C97338" OR Session("EmployeeID") = "C47346" then

		Call SaveData() 
		Response.redirect ("Unclassified_broke.asp")
	
	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to save this information.</font></br>")
	MyRec.close
	end if

  
ELSE

	strsql = "SELECT tblCars.* FROM tblCars WHERE CID = " & strid & ""

   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
gSpecies = MyRec.fields("Species")
	gSap_Nbr = MyRec.fields("SAP_Nbr")
	gVendor = MyRec.fields("Vendor")
	gPO = MyRec.fields("PO")
	strReleaseNbr = MyRec.fields("Release_nbr")
	strSap = MyRec.fields("Sap_nbr")
	strGenerator = Myrec.fields("Generator")
	strGenCity  = MyRec.fields("Gen_City")
	strGenState = Myrec.fields("Gen_State")	
   	 strTrailer = MyRec.fields("Trailer")
   	 strTonsReceived = MyRec.fields("Tons_received")
   	 strRail = round(MyRec.fields("Tons_received"),3)
     strBrokeDescription = MyRec.fields("Broke_description")
   	 strCarrier = MyRec("Carrier")   
   	 strDate_shipped = MyRec.fields("Date_shipped")
   	 strOther = MyRec("Other_comments")
   	 strClassified = MyRec("Classified")
   	 strOrigin = MyRec("Originally_Correct")
   	 MyRec.close
   	 
   		strDateReceived = formatdatetime(Now(),2) 

end if
%>



<body>

<div class="style4">

<br>
<table width = 100%><tr><td width = 33% style="height: 24px"></td>

<td align = center width = 34% style="height: 24px"><b><font face="Arial" size="4">
Classify BROKE Trailer/Rail Car</font> </b></td>
	<td align = right width = 33% style="height: 24px"><a href="Unclassified_broke.asp"><b><font face = arial size = 2>RETURN</a></font></b></td></tr></table>




<form name="form1" action="EditUnclassifiedBroke.asp?id=<%=strid%>" method="post">

<div align="center">
<table border="1" cellspacing="0" width="40%" bgcolor="#FFFFEA" style="border-collapse: collapse" cellpadding="0" bordercolorlight="#C0C0C0">
  <tr>
    <td  align = right bgcolor="#FFFFEA" style="padding: 5px; width: 29%;" >
   <b>
   <font face="Arial" size="2">Trailer:&nbsp;</font></b></td>
<td  align = left bgcolor="#FFFFEA" style="padding: 5px;">

      <font face="Arial" size="2"><b>

      <%= strTrailer%>
		</b>&nbsp;&nbsp;</font></td>
</tr>
  <tr>

      <td  bgcolor="#FFFFEA" align = right style="padding: 5px; width: 29%;">
	<font face="Arial" size="2"><b>Carrier:</b></font></td>
<td  align = left bgcolor="#FFFFEA" style="padding: 5px;">

      <font face="Arial" size="2"><b><%=strCarrier%>   </b></font> 

      &nbsp;</td>
</tr>

  <tr>

      <td  bgcolor="#FFFFEA" align = right style="padding: 5px; width: 29%;">
	<font face="Arial" size="2"><b>Vendor:</b></font></td>
<td  align = left bgcolor="#FFFFEA" style="padding: 5px;">

      <font face="Arial" size="2"><b><%=gVendor%>   </b></font> 

      &nbsp;</td>
</tr>

  <tr>

      <td  bgcolor="#FFFFEA" align = right style="padding: 5px; width: 29%;">
	<font face="Arial" size="2"><b>Release Number:</b></font></td>
<td  align = left bgcolor="#FFFFEA" style="padding: 5px;">

      <font face="Arial" size="2"><b><%=strReleaseNbr%>   </b></font> 

      &nbsp;</td>
</tr>

  <tr>

      <td  bgcolor="#FFFFEA" align = right style="padding: 5px; width: 29%;">
	<font face="Arial" size="2"><b>SAP# :</b></font></td>
    <td bgcolor="#FFFFEA" style="padding: 5px;"> 	
	<select name="SAP" style="font-weight: 700;" size="1" tabindex="3" >
 	<option selected value="">  Select</option>
      <% strsql = "Select tblBrokeSAP.* from tblBrokeSAP where category = 'BROKE'"
      
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    while not MyRec.eof

       %>
        <option <% if strSAP = MyRec("SAP") then %> selected <% end if %> value="<%= MyRec("SAP") %>" ><%= MyRec("SAP") %>-<%= MyRec("Type") %></option>
        <% MyRec.movenext
        wend
        MyRec.close %>
  
     </select>&nbsp;
  &nbsp;</td>
  
</tr>

  <tr>

      <td  bgcolor="#FFFFEA" align = right style="padding: 5px; width: 29%;" class="style2">
	Classified :</td>
    <td bgcolor="#FFFFEA" class="style3"> 	
	<select name="Classified" style="font-weight: 700;" size="1" tabindex="3" >
    <option selected value="NO" >NO</option>
    <option <% if strClassified = "YES" then %> selected <% end if %> value="YES" >YES</option>

     </select>&nbsp;
  &nbsp;</td>
  
</tr>
<tr>
    <td bgcolor="#FFFFEA" class="style1" style="width: 29%"><strong>Was it 
	originally classified Correctly?</strong></td>

    <td align = left bgcolor="#FFFFEA" style="padding: 5px;"> 	
	<select name="Origin" style="font-weight: 700;" size="1" tabindex="3" >
	   <option value="">Select</option>

    <option <% if strOrigin = "NO" then %> selected <% end if %>  value="NO" >NO</option>
    <option <% if strOrigin = "YES" then %> selected <% end if %> value="YES" >YES</option>

     </select></td>
  </tr>
<tr>
    <td bgcolor="#FFFFEA" style="width: 29%">&nbsp;</td>

    <td align = center bgcolor="#FFFFEA" style="padding: 5px;"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></td>
  </tr>
</table>

</div>

</form>
</div>
</body>
<% 

Function SaveData()
dim strbody, strCID, strECarrier, strETrailer, strELoad, strEDoc, strEDC, strEDCName, strEAdd, strECity, strEState, MyConn2, strsql4
dim strECC, strEBCC, strESpecies


	

	
	
	
	Dim strRightNow, strAud
	strRightnow = Now()
	strClassified = Request.form("Classified")
	
	if len(Request.form("SAP")) > 0 then
	strSAP = request.form("SAP")
	strsql = "Select Type from tblBrokeSAP where SAP = '" & strSAP & "'"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
   	StrType = MyRec("Type")
	MyRec.close

	strsql =  "Update tblCars set SAP_nbr = '" & strSAP & "', Broke_description = '" & strType & "', "_
	&" Classified = '" & strClassified & "', Originally_Correct = '" & request.form("Origin") & "' where CID = " & strid & ""
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
    else
    
 	strsql =  "Update tblCars set SAP_nbr = NULL, Broke_description = NULL, Classified = '" & strClassified & "', Originally_Correct = '" & request.form("Origin") & "' where CID = " & strid & ""
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
   
    end if
		







End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->