<html xmlns:m="http://schemas.microsoft.com/office/2004/12/omml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Enter Audit Weights</title>
<style type="text/css">
.style1 {
	border: 1px solid #C0C0C0;
	border-collapse: collapse;
			background-color: #FFFFCE;
}
.style2 {
	border-style: solid;
	border-color: #FFFFCE;
	background-color: #FFFFCE;
}
.style3 {
	border-style: solid;
	border-color: #FFFFCE;
	font-weight: bold;
		background-color: #FFFFCE;
}
.style6 {
	border-left-color: #C0C0C0;
	border-top-color: #C0C0C0;
	background-color: #FFFFCE;
}
.style7 {
	border: 1px solid #FFFFCE;
	background-color: #FFFFCE;
		border-color: #FFFFCE;
}
.style8 {
	border-style: solid;
	border-color: #FFFFCE;
	background-color: #FFFFCE;
	text-align: right;
}
.style9 {
	border: 1px solid #C0C0C0;
	background-color: #E5E5E5;
}
.style11 {
	border-style: solid;
	border-color: #C0C0C0;
	background-color: #D8D8D8;
	text-align: right;
}
.style12 {
	font-size: x-small;
}
.style13 {
	border-style: solid;
	border-color: #FFFFCE;
	font-weight: bold;
	background-color: #E5E5E5;
	font-family: Arial;
	font-size: x-small;
	text-align: center;
}
.style15 {
	border-style: solid;
	border-color: #FFFFCE;
	font-weight: bold;
	background-color: #E5E5E5;
	text-align: center;
}
.style18 {
	border: 1px solid #FFFFCE;
	background-color: #FFFFCE;
	font-family: Arial;
	font-size: x-small;
}
.style19 {
	text-align: center;
	font-size: small;
}
.style20 {
	font-size: small;
}
.style21 {
	background-color: #D8D8D8;
	border-left-style: solid;
	border-left-color: #C0C0C0;
	border-right-style: solid;
	border-right-color: #FFFFCE;
	border-top-style: solid;
	border-top-color: #C0C0C0;
	border-bottom-style: solid;
	border-bottom-color: #FFFFCE;
}
.style22 {
	border-style: solid;
	border-color: #FFFFCE;
	font-weight: bold;
	background-color: #E5E5E5;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID, objMOC, rstFiber, strCarrier, strTrailerWeight, rstTrailerLight, strTrailerTIDLight
      
    Dim strTrailer
    Dim strReleaseNbr
    Dim strRECNbr
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived, rstTrailer, strTrailerID
    Dim strGenerator
    Dim strGenCity
    Dim strGenState
    Dim strOther, strR, strsql3

       strId  = Trim(Request.QueryString("id")) 


 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
	
	Dim strcount
	strcount = 0
		If request.form("Trailer_option") > 1 then
 			strcount = 1
 			end if
 		If Request.form("Trailer_option_light") > 1 then
 		strcount = strcount + 1
 		end if
 		If len(request.form("Tare_weight")) > 2 then
 		strcount = strcount + 1
 		end if
 
 		If strcount = 1 then
 
		Call SaveData() 
		else
		response.redirect("Truck_audit_weights_Sys.asp?id=" & strid & "&n=T")
		end if
	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to delete a Receipt.</font></br>")
	MyRec.close
	end if

  
ELSE


strsql = "SELECT tblCars.* from tblCars WHERE tblCars.CID = " & strid

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


    strTrailer = MyRec.fields("Trailer")
    strReleaseNbr = MyRec.fields("Release_nbr")
    strRECNbr = MyRec.fields("rec_number")
    strGrossWeight = MyRec.fields("Audit_Gross")
    strTareWeight = MyRec.fields("Audit_tare")
    strTonsReceived = MyRec.fields("Audit_tons")
    strDateReceived = MyRec.fields("Date_received")
    strGenerator = MyRec.fields("Generator")
    strGenCity = MyRec.fields("Gen_City")
    strGenState = MyRec.fields("Gen_state")
    strOther = MyRec.fields("OTher_comments")
    strCarrier = MyRec.fields("Carrier")
    strR = left(strReleaseNbr,1)
    
    If isnull(strGrossWeight) then 
    strGrossWeight = 0
    end if
    If isnull(strTareWeight) then
    strTareWeight = 0
    end if
    

MyRec.close
Call getdata()
	end if

%>



<body>

<table width = 100%><tr><td width = 33%></td><td align = center width = 34%><b><font face="Arial" size="4">
	Enter Audit Weight</font> </b></td><td align = right width = 33%><a href="AssignAudit_SYS.asp"><b><font face = arial size = 2>RETURN</a></font></b></td></tr></table>

<% if request.querystring("n") = "T" then %>
	<br><font size=3 color= red face=arial><b>You must select one of the combined trailer weights or enter the tare weight</b></font><br>
	<% end if %>	

<form name="form1" action="Truck_Audit_weights.asp?id=<%=strid%>&r=<%= strR%>" method="post">
<table cellspacing="0" cellpadding="0" align = center  class="style1" style="width: 90%">
<tr>
    <td class="style9">&nbsp;</td>

    <td class="style9" colspan="3">&nbsp;</td>
  </tr>
  <tr>
    <td  align = right class="style3" >
   <font face="Arial" size="2">Trailer:&nbsp;</font></td>
<td  align = left class="style7" colspan="3">

    <font face="Arial" size="2">   <%= strTrailer%></td></tr>

  </tr>
  <tr>

      <td align = right class="style3">
  <font face="Arial" size="2">Receipt Number:&nbsp;</font></td>
<td  align = left class="style7" colspan="3"><font face="Arial" size="2">

<%= strRECNbr%></td></tr>
<tr>
    <td class="style3">  
	<p align="right">  <font face="Arial" size="2">Carrier:&nbsp;</font></td>
    <td class="style7" colspan="3"> <font face="Arial" size="2"> <%= strCarrier %>
    </td>
  </tr>
  
 

  <tr>
    <td class="style15" colspan="4">  
	<p align="right" class="style19">  <font face="Arial">Select Combined Tractor/Trailer and enter Gross Weight&nbsp;</font></td>
  </tr>

    <td class="style8" style="height: 58px"> <font face="Arial" size="2">
	<span style="mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-bidi-font-family: &quot;Times New Roman&quot;; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA" class="style12">
	<strong>Day Cab (Heavy)<br>Combined Trailer/Tractor</strong></span><b>:</b></font></td>

    <td class="style7" style="height: 58px">    <font face="Arial">
     <select name="Trailer_option" style="font-weight: 700" size="1">
 	<option selected value=0>  Select Trailer</option>
      <%= objGeneral.OptionListAsString(rstTrailer, "TID", "Toption", strTrailerTID) %>
     </select> <strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</strong></td>

    <td class="style18" style="height: 58px">  <strong>Regular Spotting Tractor (Light) <br>Combined Trailer/Tractor</span>  &nbsp;</strong></td>

    <td class="style7" style="height: 58px">    
	<select name="Trailer_option_light" style="font-weight: 700" size="1">
 	<option selected value=0>  Select Trailer</option>
      <%= objGeneral.OptionListAsString(rstTrailerLight, "TID", "Toption", strTrailerTIDLight) %>
     </select></font></td>
  </tr>
<tr>

          <td class="style13" colspan="4">
   <span class="style20">OR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	Enter Tare Weight&nbsp;&nbsp;&nbsp;</span><font size="2">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </font></td>
</tr
<tr>
    <td align = center class="style3" style="height: 42px" colspan="4">
   <font face="Arial" size="2"><strong>Tare Weight:</strong></font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;

   <input type="text" name="Tare_Weight" size="15" value = <%= strTareWeight%>></td></tr>

      <tr>    <td align=right class="style22" colspan="4">
   <font face="Arial" size="2">&nbsp;</font></td>
</tr>
<tr>
    <td class="style8">
   <font face="Arial" size="2"><strong>Gross Weight:&nbsp;</strong></font></td>

    <td class="style7" colspan="3">

      <input type="text" name="Gross_Weight" size="15" value = <%= strGrossWeight%>></td>
  </tr>
 <tr>
    <td class="style3">  
</font></td>
    <td class="style3" colspan="3"> &nbsp;</td>
  </tr>
      <tr>    <td align=right class="style3">
   <font face="Arial" size="2">Tons:&nbsp;</font></td>
<td align = left class="style7" colspan="3">

     <font face="Arial" size="2"> <%= strTonsReceived%>

</td></tr>


       <tr>
          <td  align = right class="style3" >
    <font face="Arial" size="2">Date Received:&nbsp;</font></td>
<td align = left class="style7" colspan="3"><font face="Arial" size="2">

   <%= strDateReceived%></td></tr>
              <tr>
          <td  align = right class="style3" style="height: 20px" >
   <font face="Arial" size="2">Generator:&nbsp;</font></td>
<td align = left class="style7" colspan="3" style="height: 20px"><font face="Arial" size="2">
     <%= strGenerator %>,  <%= strGenCity%>, <%= strGenState%></TD></tr>
         <tr>
          <td  align = right class="style3" >
  <font face="Arial" size="2">Other:&nbsp;</font></td >
   <td align = left class="style6" colspan="3">   <input type="text" name="Other_Comments" size="25" value = "<%= strOther%>"></td></tr>
   
<tr>
    <td class="style2">
	&nbsp;</td>

    <td class="style2" colspan="3">&nbsp;</td>
  </tr>

  <tr>
    <td class="style21">&nbsp;</td>

    <td class="style11" colspan="3">
	<strong>
	<Input name="Update" type="submit" Value="Submit" style="float: center" ></strong></td>
  </tr>
</table>

</form>
</body>
<%
 Function GetData()
  set objMOC = new ASP_CLS_FIBER
        set rstFiber = objMOC.FiberCarrier()
        set rstTrailer = objMOC.TrailerOptionsAudit()
         set rstTrailerlight = objMOC.TrailerOptionsAuditLight()

End Function





 Function SaveData()

strOther = Replace(Request.form("Other_Comments"), "'", "''") 

If request.form("Trailer_option") > 1 then
	strTrailerTID = request.form("Trailer_option")
   	 strSQL3 = "Select audit_weight from tblTrailerOptions where TID = " & strTrailerTID

   	 Set MyConn3 = Server.CreateObject("ADODB.Recordset")
   	 MyConn3.Open strSQL3, Session("ConnectionString")
   	 		 strTrailerWeight = MyConn3.fields("Audit_Weight")
   
   	 	strTareWeight =  strTrailerweight
   	 		 MyConn3.close
   	elseif request.form("Trailer_option_light") > 1 then 
   	strTrailerTIDlight = request.form("Trailer_option_light")
   		 strSQL3 = "Select light_audit_weight from tblTrailerOptions where TID = " & strTrailerTIDlight
	
   	 	  	 Set MyConn3 = Server.CreateObject("ADODB.Recordset")
   	 MyConn3.Open strSQL3, Session("ConnectionString")
   	 		 strTrailerWeight = MyConn3.fields("Light_Audit_Weight")
   
   	 	strTareWeight =  strTrailerweight

   	 		 MyConn3.close
   	 	
	else
	strTareWeight = Request.form("Tare_Weight")
	end if

   
   
   	 
	

strGrossWeight = Request.form("Gross_Weight")

strTonsReceived = round((strgrossweight - strTareweight)/2000,3)

         strSql = "Update tblCars Set Other_Comments = '" & strOther & "', "_
         
         &" Audit_gross = " & strGrossWeight & ", "_
         &" Audit_tare = " & strTareWeight & ", "_
         &" Audit_tons = " & strTonsReceived & " where CID = " & strid & ""


         
 	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
       
      
      	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         Dim strnow
         strnow = Formatdatetime(Now(),0)
         
         
                  strsql = "INSERT INTO tblMovement ( CID, Comment, DDate,  From_location,  BID ) "_
		&" SELECT " & strid & ", 'AUDIT WEIGHT',  '" & strnow & "', '" & strTonsReceived & "',  '" & Session("EmployeeID") & "'"
        
           set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql

         
         
      Response.redirect ("AssignAudit_SYS.asp")

End Function
 %>

</html>
<!--#include file="Fiberfooter.inc"-->