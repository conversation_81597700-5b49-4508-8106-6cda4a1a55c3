																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Add Plant </TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->


<% Dim MyRec, strsql, MyRec2, strsql2,  MyConn3, strsql3,  strFee



  set objGeneral = new ASP_CLS_General
  if objGeneral.IsSubmit() Then	
 
  
  	strPlant = request.form("Plant")
  	

	     strPlantD =  Replace(Request.form("PlantD"), "'", "''")

 
  	
     	
  	
	strsql =  "INSERT INTO tblPlant (Plant_number, Plant_name) "_
	&" SELECT  '" & strPlant & "',  '" & strPlantD & "'"
 	
	
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
Response.redirect("Plant.asp")		
end if

	
%>

<style type="text/css">
.style3 {
	border-style: solid;
	border-width: 1px;
}
.style4 {
	font-size: x-small;
}
.style5 {
	font-family: arial;
	font-size: x-small;
}
.style6 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	text-align: center;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #FFFFFF;
}
.style9 {
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
}
.style10 {
	font-family: arial;
	font-size: small;
}
.style11 {
	font-size: small;
}
.style12 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #FFFFFF;
}
.style13 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #EAF1FF;
}
.style14 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: arial;
	font-size: small;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #EAF1FF;
}
.style15 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style16 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	text-align: center;
}
</style>
</head>

<body>
<br>
	
<form name="form1" action="Plant_Add.asp" method="post" ID="Form1">
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Add Plant</b></font></td>
<td align = right><font face="Arial"><a href="Plant.asp">Return</a>&nbsp;</td>


</tr>
	    </table>



	<p>&nbsp;</p>
	<table id="table1" style="width: 45%" align="center" class="style3">
<tr bgcolor = #CCCCFF>
		<td class="style14" style="height: 32px">Plant</td>
		<td class="style14" style="height: 32px">Plant Description</td>
	 
	</tr>
	<tr>
		<td class="style15"><font face = arial size = 1>		<span class="style4">		
		<input type="text" name="Plant" size="20" style="width: 77px" tabindex="1"></span></td>
		<td class="style15"><font face = arial size = 1>		<span class="style4">		
		<input type="text" name="PlantD" size="20" style="width: 479px" tabindex="2"></span></td>
 
	</tr>
</table>
<span class="style4">
<br>
		
		</span>
		

<p align="center"><font face="Arial">
	<span class="style4">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></span></font></p>
</form>

<!--#include file="Fiberfooter.inc"-->