 

<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">
<TITLE>Rail Cars Received</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
 
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->
 

<%  	strBeg  = request.querystring("b")
	strEnd = request.querystring("e")
Response.ContentType = "application/vnd.ms-excel"

 

   set objGeneral = new ASP_CLS_General

 
%>

 


<style type="text/css">
.style3 {
	font-weight: bold;
	border-width: 1px;
}
.style4 {
	font-family: Arial;
}
.style5 {
	background-color: #F0F8FF;
}
.style6 {
	font-weight: bold;
	border-width: 1px;
	background-color: #F0F8FF;
}
.style7 {
	border: 1px solid #C0C0C0;
}
</style>
</head>
 
   <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>


 <td bgcolor = white align="center" >&nbsp;</font></td>

     <td bgcolor = white  align="right" ><font face="Arial" size = 2> <b>
 <a href="Rail_Cars_Received_Excel.asp?b=<%= strBeg %>&e=<%= strEnd %>">Excel</a>  &nbsp;&nbsp;&nbsp;
        </b></font></td>   </tr></table>
    	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1 align = center>  
	 <tr class="tableheader">
	<td  align = left style="height: 12px">     <font face="Arial" size="1">	CID</font></td>
				<td  align = left style="height: 12px">     <font face="Arial" size="1">	Rail Car</font></td>
		<td  align = left style="height: 12px">     <font face="Arial" size="1">Release Number</font></b></td>
	
		<td  align = left style="height: 12px">     <font face="Arial" size="1">Species</font></td>
		<td  align = left style="height: 12px">     <font face="Arial" size="1"> Date Received</td>
	 
		<td  align = left style="height: 12px">     <font face="Arial" size="1">Vendor</font></td>
		<td style="height: 12px"  ><b><font face="Arial" size="1">Bales</font></b></td>
			<td  align = left style="height: 12px">     <font face="Arial" size="1">Tons</font></b></td>
 
		
	</tr>

    <%     Dim ii
       ii = 0
    
    strsql = "SELECT  OID,  CID,  Trailer,  Species,  Date_received,  Release_Nbr, Vendor,  Bales_VF,  Tons_received FROM tblCars "_
&" WHERE (Species = 'KCOP' or Species = 'OF' or Species = 'PMX' or Species = 'MXP' or Species = 'OCC' or Species = 'SHRED' or Species = 'HBX' or Species = 'ROCC')"_
&"  AND Date_received >= '" & strBeg  & "' and Date_received <= '" & strEnd & "' and Carrier = 'RAIL' ORDER BY  Species,  Date_received"	
	   Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    IF not MyRec.eof  then
  

    while not MyRec.eof 
  
  if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>

	<td  ><font size="1" face="Arial"><%= MyRec.fields("CID")%> </font></td> 

	<td  ><font size="1" face="Arial"><%= MyRec.fields("Trailer")%>&nbsp;</font></td>
		<td  ><font size="1" face="Arial"><%= MyRec.fields("Release_Nbr") %>&nbsp;</font></td>
	<td  ><font size="1" face="Arial"><%= MyRec.fields("Species")%>&nbsp;</font></td>
	<td  ><font size="1" face="Arial"><%= MyRec.fields("Date_received")%>&nbsp;</font></td>
	<td  ><font size="1" face="Arial"><%= MyRec.fields("Vendor")%>&nbsp;</font></td>
	<td  ><font size="1" face="Arial"><%= MyRec.fields("Bales_VF")%>&nbsp;</font></td>
	<td  ><font size="1" face="Arial"><%= MyRec.fields("Tons_received")%> </font></td>
 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
     end if
    
    %>
</table>
 