﻿
   <%

     Session("ConnectionPolaris")="Provider=SQLOLEDB;Initial Catalog = pli0nedb;Data Source=ustcas41;User ID = pli0neadm; Password = **************;ConnectionTimeout = 600;" 
  Session("ConnectionString")="Provider=SQLOLEDB;Initial Catalog = pli0nedb;Data Source=ustcas41;User ID = pli0neadm; Password = **************;ConnectionTimeout = 600;"

       	If len(Session("Login_ID")) = 6 then
     	   strEID =  Session("Login_ID")
     	else
     	   x = Request.Servervariables("LOGON_USER")
		   strEID = ""
		   If InStr(x,"\") then
              y = SPLIT(x,"\")
              strEID = y(1)
           Else
              strEID = x
           End If
		end if

     	

         Session("EmployeeID") = ucase(strEID)
 %>