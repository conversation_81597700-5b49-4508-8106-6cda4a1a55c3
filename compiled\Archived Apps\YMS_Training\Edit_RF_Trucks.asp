																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Edit RF Truck Destination in Yard</TITLE>
<style type="text/css">
.auto-style1 {
	font-family: Arial, Helvetica, sans-serif;
}
.auto-style2 {
	font-family: Arial, Helvetica, sans-serif;
	font-weight: bold;
	font-size: x-small;
}
.auto-style3 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.auto-style4 {
	font-size: x-small;
}
.auto-style5 {
	text-align: center;
}
.auto-style6 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	text-align: center;
}
.auto-style7 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	text-align: left;
}
</style>
</head>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->




<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate
 

strsql = "SELECT tblCars.* FROM tblCars WHERE "_
&"  (Species = 'SHRED' or Species = 'USBS' or Species = 'SWL' or Species = 'MXP'  or Species = 'LPSBS' or Species = 'HWM') "_
&"  AND  Location ='YARD' order by Species, Trailer"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Designated Location to Consume SHRED, LPSBS, MXP, SWL, 
HWM, USBS Trailers in Yard</b></font></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1>  
	 <tr class="tableheader">
<td class="auto-style3">&nbsp;</td>
	<td class="auto-style2"  >Species</td>

<td class="auto-style2"  >Location</td>
	
		<td class="auto-style2"  >Trailer</td>
		<td class="auto-style2"  >Carrier</td>
			<td class="auto-style5"  >       <p align="center" class="auto-style3">       Quantity</td>
		<td align = center  >        <font size="1" class="auto-style3">Date<br> Received</font></td>
   
		<td class="auto-style3"  >Vendor</td>
			<td class="auto-style3"  >Generator</td>
				<td class="auto-style3" > City</td>
		<td class="auto-style3"  >       PO Number</td>
			<td class="auto-style3"  >       Release Number</td>
		<td class="auto-style7">       REC Number</td>
	
 
 

	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if
   if MyRec("Shred_RF") = 1 or MyRec("Shred_RF") = -1 then
   strc ="r"
   else
   strc = "o"
   end if 
 %>
<td class="auto-style3"> <a href="Shred_RF.asp?id=<%= MyRec.fields("CID") %>&c=<%= strc %>">Change</a></td>
<td class="auto-style3">  <%= MyRec("Species") %></td>
<td class="auto-style3"> <% if MyRec("SHRED_RF") = 1 or MyRec("SHRED_RF") = -1  then %>RF <% else %>OCC <% end if %></td>


	<td class="auto-style2"  ><% if MyRec("Trailer") = "UNKNOWN" Then %> <%= MyRec("Transfer_Trailer_Nbr") %> <% else %> <%= MyRec.fields("Trailer")%><% end if %></td>
			<td  ><b><font size="1" face="Arial"><span class="auto-style1">
			<span class="auto-style4"><% if MyRec("Trailer") = "UNKNOWN" Then %> <%= MyRec("Trans_Carrier") %> <% else %><%= MyRec.fields("Carrier")%><% end if %>&nbsp;</span></span></font></b></td>
	<td class="auto-style6"  >  <%= MyRec.fields("Tons_received")%></td>
	
		<td class="auto-style6"  >   <%= MyRec.fields("Date_Received")%></td>
		<td class="auto-style3"><%= MyRec.fields("Vendor")%></td>
		<td class="auto-style3"><%= MyRec.fields("Generator")%></td>
			<td class="auto-style3"><%= MyRec.fields("Gen_City")%></td>
		<td class="auto-style3"  >  <%= MyRec.fields("PO")%></td>
				<td class="auto-style3"  ><% if MyRec("Trailer") = "UNKNOWN" Then %> <%= MyRec("PS") %> <% else %> <%= MyRec.fields("Release_nbr")%><% end if %>&nbsp;</td>
	<td class="auto-style3"  >   <%= MyRec.fields("REC_Number")%>&nbsp;</td>
		
	
	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>


<!--#include file="Fiberfooter.inc"-->