																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>SAPISM Items</TITLE>


<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 



<% Dim MyRec, strsql, MyRec2, strsql2,  MyConn3, strsql3,  strSchedule_Agreement

strdate = formatdatetime(Now(),2)

  set objGeneral = new ASP_CLS_General
  strid = Request.querystring("id")
  
  strsql = "Select * from tblBrokeSap where ID = " & strid
  
      Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

  

  if objGeneral.IsSubmit() Then	
  Dim strSpecies
  

  	

  	
  	If len(request.form("SAP")) > 0 then
  	strSAP = request.form("SAP")
  	else
  	strSAP = ""
  	end if 
  	
 
        	
  	
	strsql =  "Update tblBrokeSAP  set uom = '" & request.form("UOM") & "', Type = '" & request.form("type") & "', "_
	&"   SAP = '" & strSAP & "', Category = '" & Request.form("Category") & "' "_
	&" where ID = " & strid
	
	
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
Response.redirect("SAPISM_Species.asp")		
end if

	
%>

<style type="text/css">
.style3 {
	border-left: 1px solid #808080;
	border-top: 1px solid #808080;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style5 {
	border-right: 1px solid #808080;
	border-bottom: 1px solid #808080;
	text-align: center;
	border-left-style: solid;
	border-left-width: 1px;
	border-top-style: solid;
	border-top-width: 1px;
}
.style6 {
	border-right: 1px solid #808080;
	border-bottom: 1px solid #808080;
	font-family: arial;
	font-size: x-small;
		text-align: center;
	border-left-style: solid;
	border-left-width: 1px;
	border-top-style: solid;
	border-top-width: 1px;
}
.style7 {
	border-right: 1px solid #808080;
	border-bottom: 1px solid #808080;
	font-family: arial;
	font-size: x-small;
	text-align: center;
	border-left-style: solid;
	border-left-width: 1px;
	border-top-style: solid;
	border-top-width: 1px;
	background-color: #FFFFD7;
}
.auto-style1 {
	border-right: 1px solid #808080;
	border-bottom: 1px solid #808080;
	font-family: arial;
	font-size: x-small;
	text-align: center;
	border-left-style: solid;
	border-left-width: 1px;
	border-top-style: solid;
	border-top-width: 1px;
	background-color: #DDECFF;
}
</style>
</head>

<body>
<br>
	
<form name="form1" action="SAPISM_Edit.asp?id=<%= strid%>" method="post" ID="Form1">
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Edit Item </b></font></td>
<td align = right><font face="Arial"><a href="Sapism_Species.asp">Return</a>&nbsp;</td>


</tr>
	    </table>



	<p>&nbsp;</p>
	<table id="table1" style="width: 75%" align="center" class="style3">
<tr bgcolor = #CCCCFF>
		<td bgcolor="#FFFFD7" class="auto-style1" style="height: 70">Description</td>
		<td class="auto-style1" style="height: 70">SAP #</td>
		<td class="auto-style1" style="height: 70">UOM</td>
		<td class="auto-style1" style="height: 70">Category</td>
	</tr>
	<tr>
		<td class="style5"><font face = arial size = 1>
		<input type="text" name="Type" size="20" style="width: 501px" value='<%= MyRec.fields("Type") %>'></td>
		<td class="style5"><font face = arial size = 1>
		<input type="text" name="SAP" size="20" value='<%= MyRec.fields("SAP") %>' style="width: 115px"></td>
			<td class="style5"><font face = arial size = 1><select name="UOM">
		<option value="">--Select--</option>
		<option <% if MyRec("UOM") = "TONS" then %> selected <% end if %>>TONS</option>
			<option <% if MyRec("UOM") = "T" then %> selected <% end if %>>T</option>
			<option <% if MyRec("UOM") = "EA" then %> selected <% end if %>>EA</option>

		</select>
			
		</td>

		<td class="style5"><font face = arial size = 1><select name="Category">
		<option value="">--Select--</option>
		<option <% if MyRec("Category") = "BROKE" then %> selected <% end if %>>BROKE</option>
			<option <% if MyRec("Category") = "FIBER" then %> selected <% end if %>>FIBER</option>
			<option <% if MyRec("Category") = "KDF" then %> selected <% end if %>>KDF</option>
		<option <% if MyRec("Category") = "OTHER" then %> selected <% end if %>>OTHER</option>
		<option <% if MyRec("Category") = "WADDING" then %> selected <% end if %>>WADDING</option>
		</select>
		
		
		</td>
	</tr>
</table>
<br>
		

<p align="center"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></p>
</form>
<% MyRec.close %><!--#include file="Fiberfooter.inc"-->