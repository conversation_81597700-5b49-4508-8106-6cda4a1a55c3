																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Broke Release Number List</TITLE>

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 





<% Dim strid, strleft, strright, strOID, MyRec, MyRec2, strsql, strsql2, strpo

strpo = request.querystring("po")
strRelease = request.querystring("r")

If len(strpo) > 0 then

strsql = "SELECT tblorder.* from tblORder where PO = '" & strpo & "'"
else
strsql = "Select tblorder.* from tblOrder where Release = '" & strRelease & "'"
end if
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<style type="text/css">
.style1 {
	font-family: Arial;
	font-size: x-small;
}
</style>
</head>

<body>

<% if Session("EmployeeID") = "C97338" or Session("EmployeeID") = "B48888" or Session("EmployeeID") = "B06916"  or Session("EmployeeID") = "B53909" or Session("EmployeeID") = "B55548" then %>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>


<td align = left><b><font face="Arial">Release # List</font></b></td>
<td align = right>&nbsp;</td>

<td align = right><font face="Arial"><a href="Request_release.asp">RETURN</a>&nbsp;</td>


</tr>
	    </table>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=90% class = "tablecolor1" border=1 align = center>  
	 <tr class="tableheader">
<td>&nbsp;</td>
<td  align = left>     <font face="Arial" size="2">Trailer <br>Received</td>
				<td  align = left>     <font face="Arial" size="2">	PO</font></td>
		<td class="style1"  > <font face="Arial" size="2">Release</font></td>
		<td  align = left class="style1">  <font face="Arial" size="2"> Species</font></td>
		<td  align = left class="style1">  <font face="Arial" size="2"> Grade</font></td>
		<td  align = left class="style1">  <font face="Arial" size="2"> SAP</font></td>
			<td  align = left class="style1">  <font face="Arial" size="2">Broke Type</font></td>
		<td  align = left class="style1">  <font face="Arial" size="2"> Vendor</font></td>

<td  align = left class="style1">  <font face="Arial" size="2"> Generator</font></td>

<td  align = left class="style1">  <font face="Arial" size="2"> City</font></td>
<td  align = left class="style1">  <font face="Arial" size="2"> State</font></td>

	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td> <font size="2" face="Arial"><a href="Broke_Type_Edit.asp?r=<%= request.querystring("r") %>&o=<%= request.querystring("po")%>&id=<%= MyRec.fields("OID") %>">Edit</a>&nbsp;&nbsp;</td>

<% strOID = MyRec.fields("OID")
strsql2 = "Select Date_received from tblCars where OID = " & strOID
    Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL2, Session("ConnectionString")
If not MyRec2.eof then %>

<td  ><font size="2" face="Arial"><%= MyRec2.fields("Date_received")%></font></td>
<% else %> 
<td  ><font size="2" face="Arial">&nbsp;</font></td>
<% end if 
MyRec2.close%>

	<td  ><font size="2" face="Arial"><%= MyRec.fields("PO")%></font></td>
	
<td  ><font size="2" face="Arial"><%= MyRec.fields("Release")%></font></td>
	<td  ><font size="2" face="Arial"><%= MyRec.fields("Species")%></font></td>
		<td  ><font size="2" face="Arial"><%= MyRec.fields("Grade")%>&nbsp;</font></td>
	<td  ><font size="2" face="Arial"><%= MyRec.fields("SAP_Nbr")%>&nbsp;</font></td>
		<td  ><font size="2" face="Arial"><%= MyRec.fields("Broke_Description")%>&nbsp;</font></td>
	<td  ><font size="2" face="Arial"><%= MyRec.fields("Vendor")%>&nbsp;</font></td>

<td  ><font size="2" face="Arial"><%= MyRec.fields("Generator")%>&nbsp;</font></td>

<td  ><font size="2" face="Arial"><%= MyRec.fields("City")%>&nbsp;</font></td>
<td  ><font size="2" face="Arial"><%= MyRec.fields("State")%>&nbsp;</font></td>



	
</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>
<% else %>
<p align="center"><font face="arial" size="3"><b>You do not have authorization to view this page</b></font></p>
<% end if %><!--#include file="Fiberfooter.inc"-->