																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Broke Consumption Projections </TITLE>

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 



<% Dim MyRec, strsql, strstartdate
strStartdate = formatdatetime(now(),2)

strsql = "SELECT * from tblBrokeConsumption where Inv_Date > '" & strStartdate & "' order by Inv_date"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>

<% if Session("EmployeeID") = "C97338"  or Session("EmployeeID") = "B96138" or Session("EmployeeID") = "B48888" or Session("EmployeeID") = "B55548" or Session("EmployeeID") = "C66556" or Session("EmployeeID") = "B53909" or Session("EmployeeID") = "B99660" then %>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>


<td align = left><b><font face="Arial">Consumption Projections for Broke</font></b></td>
<td align = right><b><font face="Arial"><a href="BC_default.asp">Change Defaults</a></font></b></td>

<td align = right><font face="Arial"><a href="BC_add.asp"><b>Add New</b></a>&nbsp;</td>


</tr>
	    </table>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=40% class = "tablecolor1" border=1 align = center>  
	 <tr class="tableheader"><td>&nbsp;</td>
		<td  align = left> <font face="Arial" size="2">	<b>Inventory Date</b></font></td>
		<td  ><b><font face="Arial" size="2">Tissue Bales</font></b></td>
			<td  ><b><font face="Arial" size="2">Tissue Gaylords</font></b></td>
		<td  align = left>  <b>   <font face="Arial" size="2">White Towel Bales</font></b></td>
				<td  align = left>  <b>   <font face="Arial" size="2">Brown Towel Bales</font></b></td>
	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td> <font size="2" face="Arial"><a href="BC_edit.asp?id=<%= MyRec.fields("ID") %>">Edit</a></td>

	<td  ><font size="2" face="Arial"><%= MyRec.fields("INV_Date")%></font></td>	
<td  ><font size="2" face="Arial"><%= MyRec.fields("Tissue_bales")%>&nbsp;</font></td>
<td  ><font size="2" face="Arial"><%= MyRec.fields("Tissue_gaylords")%>&nbsp;</font></td>
	<td  ><font size="2" face="Arial"><%= MyRec.fields("White_towel_Bales")%>&nbsp;</font></td>
<td  ><font size="2" face="Arial"><%= MyRec.fields("Brown_towel_bales")%>&nbsp;</font></td>
</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>
<% else %>
<p align="center"><font face="arial" size="3"><b>You do not have authorization to view this page</b></font></p>
<% end if %><!--#include file="Fiberfooter.inc"-->