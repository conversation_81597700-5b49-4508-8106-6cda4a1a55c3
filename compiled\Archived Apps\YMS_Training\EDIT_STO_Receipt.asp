<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 14.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Enter Broke Trailer Receipt</title>
<style type="text/css">
.style1 {
	font-size: x-small;
}
.style25 {
	border-style: solid;
	font-weight: bold;
	border-width: 1px;
	background-color: #EAF1FF;
}
.style26 {
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
}
.style28 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
.style29 {
	border-style: solid;
	border-width: 1px;
	text-align: right;
	background-color: #EAF1FF;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID,  MyConn, objMOC, rstFiber, rstSpecies, strKCWeighed, strPounds  
    Dim strTrailer, strCarrier, strLocation, MyRec5, strsql5, stralert
    Dim rstTrailer , strTrailerWeight , strTractor, strTrailerTID, strSAP, strerror, strType
 
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strOther, strsql3, strSpecies, strNet, strPO, strR, objGeneral

strid = request.querystring("id")
	
  call getdata()
  
  	strsql = "SELECT tblCars.* FROM tblCars WHERE CID = " & strid & ""

   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
	gSap_Nbr = MyRec.fields("SAP_Nbr")
	strSap = MyRec.fields("Sap_nbr")
   	 strTrailer = MyRec.fields("Trailer")
   	 strTonsReceived = MyRec.fields("Tons_received")
   	
   
   	 strCarrier = MyRec("Carrier")   
   	 strDate_shipped = MyRec.fields("Date_shipped")
   	 strOther = MyRec("Other_comments")
   	 strGrossWeight = MyRec("Gross_weight")
   	 strTrailerTID = MyRec("Trailer_TID")
   	 strPounds = MyRec("Tons_received")* 2000
   	 strDateReceived = MyRec("Date_received")
   	 MyRec.close
   	



  set objGeneral = new ASP_CLS_General
if objGeneral.IsSubmit() Then
strerror = 0

   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
	
	

	strSpecies ="BROKE"
	
 	strTrailer = Request.form("Trailer")
	strOther = Replace(Request.form("Other_Comments"), "'", "''") 
	strDateReceived = Request.form("Date_received")
	strGrossweight = Request.form("Gross_Weight")
	strPounds = Request.form("Tons_Received")
	strCarrier = Request.form("Carrier")

	strTrailerTID = request.form("Trailer_option")
	strSAP = request.form("SAP")
	
	 if  len(strTrailer)< 1 then
	 strerror = 1 %>
	 <font face="arial" size="3" color="red"><b>You must enter a Trailer number.</b></font><br>
<% end if %>
<% if strCarrier = "" then
strerror = 1 %>
 <font face="arial" size="3" color="red"><b>You must enter a Carrier</b></font><br>
<% end if %>
<% if strSAP= "" then
strerror = 1  %>
 <font face="arial" size="3" color="red"><b>You must select a SAP #</b></font><br>
<% end if

	if strerror = 0 then
	Call SaveData() 
	end if
	
	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to save this information.</font></br>")
	MyRec.close
	end if ' if you don't have authorization
    
   
end if ' if they did not submit
%>

<% if Request.querystring("n") = "T" then 
strTrailer = Session("Trailer")
strCarrier = Session("Carrier")
strTrailerTID = Session("TrailerTID")
strGrossWeight = Session("GrossWeight")

strPounds = Session("Pounds")
strOther = Session("Other")
strSAP = Session("SAP")
 %>
<p align = center><font face = arial size = 3 color = red><b>



<% if isnull(Session("Trailer")) or len(Session("Trailer"))< 1 then %>
You must enter a Trailer number.<br>
<% end if %>
<% if strCarrier = "" then %>
You must enter a Carrier</span><br>
<% end if %>
<% if strSAP= "" then %>
You must select a SAP #</span><br>
<% end if %>

</p></b></font>
<% else %>
&nbsp;
<% end if %>


<body>
<table width = 100%><tr><td width = 33%>
&nbsp;</td><td align = center width = 34%><b><font face="Arial" size="4">
Edit STO Broke Trailer Receipt </b></font> </b></td></tr></table>




<form name="form1" action="Edit_STO_Receipt.asp?id=<%=strid%>" method="post">
<div align="center">
<table cellspacing="0" bordercolor="#C0C0C0" bgcolor="#FFFFEA" style="width: 80%;" cellpadding="0" class="style28">

<tr>
    <td align="right" class="style25"><font face="Arial" size="2">&nbsp;</font></td>

    <td colspan="2" class="style29">&nbsp; 
	<font face = arial size = 4 color="teal"><b><font face = arial size = 2><a href="Edit_Yard_trucks.asp">RETURN</a></font></b></td>
  </tr><tr>
    <td align="right" class="style25"><font face="Arial" size="2">Species:</font></td>

    <td class="style26" colspan="2">  
     <font face="Arial">
  	<span class="style1"><strong>&nbsp;BROKE</strong></span></td>

  </tr>
  
    <tr>
    <td align="right" style="height: 23px" class="style25"><font face="Arial" size="2">Material #:</td>

    <td colspan="2" style="height: 23px" class="style25"> 
		<font face="Arial" size="2"> 
	<select name="SAP" style="font-weight: 700; width: 132px;" size="1" tabindex="1">
 	<option selected value="">  Select</option>
      <% strsql = "Select tblBrokeSAP.* from tblBrokeSAP"
      
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    while not MyRec.eof

       %>
        <option <% if strSAP = MyRec("SAP") then %> selected <% end if %> ><%= MyRec("SAP") %></option>
        <% MyRec.movenext
        wend
        MyRec.close %>
  
     </select>&nbsp;
		&nbsp;&nbsp;&nbsp; (Required)</font></td>
  </tr>
  
  <tr>
    <td  align = right height="29" class="style25" >
   <font face="Arial" size="2">Trailer:&nbsp;</font></td>
<td  align = left colspan="2" height="29" class="style26">

      <input type="text" name="Trailer" size="15" value="<%= strTrailer %>" tabindex="2" >&nbsp;
		<font face="Arial" size="2"><b>(Required)</b></font></td></tr>
  <tr>

      <td align = right height="28" class="style25">
	<font face="Arial" size="2">Select Carrier: </font></td>
<td  align = left height="28" class="style26" colspan="2">

      <select name="Carrier" tabindex="3">
 	<option selected>  Select Carrier - Required</option>
      <%= objGeneral.OptionListAsString(rstFiber, "Carrier", "CDesc", strCarrier) %>
     </select><font face="Arial" size="2"><b>&nbsp; </b></font>&nbsp; </td>
</tr>
<tr>
    <td class="style25">  
	<p align="right">  <font face="Arial" size="2">&nbsp;</font></td>
    <td colspan="2" class="style26">   &nbsp;</td>
  </tr>

    <tr>
		<td align = right width="17%" class="style26" style="height: 11px"> 
		<p align="center"> <b>
			<font face="Arial" size="2">Combined Trailer/Tractor</font></b><font face="Arial" size="2"><b> 
		Weight:&nbsp;</b></font></td>
<td align = left colspan="2" class="style26" style="height: 11px">    <font face="Arial"> 
<select name="Trailer_option" style="font-weight: 700" size="1" tabindex="4">
 	<option selected>  Select Trailer</option>
<% strsql2 = "SELECT  Trailer_Description, Audit_Weight, Trailer_Description + '-' + str([Audit_weight]) as toption, TID FROM tblTrailerOptions ORDER BY Trailer_description"
 	 	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL2, Session("ConnectionString")
while not MyRec.eof
%>
<option value=<%= MyRec("TID") %>><%= MyRec("Toption") %> </option>
 	
  <% MyRec.movenext
  wend
  MyRec.close %> 
        
     </select></td>
</tr>
<tr>
	<td height="22" align="right" width="17%" class="style26">
	&nbsp;</td>
    <td colspan="2" height="22" class="style26">         
	&nbsp;</td>
  </tr>
<tr>
	<td height="22" width="17%" class="style25">
	<p align="right"><font face="Arial" size="2">Gross Weight:</font></td>
    <td height="22" width="21%" class="style26">      
	
      <input type="text" name="Gross_Weight" size="15" style="height: 22px" value="<%= strGrossweight %>" tabindex="5" ></td>
    <td height="22" style="width: 44%" class="style25">    
	 <font face="Arial" size="2">OR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
		Pounds Received:&nbsp;&nbsp; 
		<input name="Tons_Received" size="15" value = "<%= strPounds%>" tabindex="6" ></font></td>
  </tr>
<tr>
	<td height="22" width="17%" class="style26">&nbsp;</td>
    <td colspan="2" height="22" class="style26">&nbsp;</td>
  </tr>

       <tr><td  align = right class="style25" ><font face="Arial" size="2">Date Received:&nbsp;</font></td>
<td align = left colspan="2" class="style26"> 
<input type="text" name="Date_Received" size="15" value = <%= strDateReceived %> style="width: 136px" tabindex="7"></td></tr>
             
         <tr>
          <td  align = right class="style25" >
  <font face="Arial" size="2">Comments:&nbsp;</font></td >
   <td align = left colspan="2" class="style26">   
	<input type="text" name="Other_Comments" size="25" style="width: 278px" value="<%= strOther %>" tabindex="8">
	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font face="Arial"><font size="2">&nbsp;Location: </font>   
YARD</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	</td></tr>
	
	

<tr>
    <td class="style26">&nbsp;</td>

    <td colspan="2" class="style26"> 
	<Input name="Update" type="submit" Value="Submit"  ></td>
  </tr>


</table>

</div>

</form>
</body>
<% Function GetData()
  set objMOC = new ASP_CLS_FIBER
        set rstFiber = objMOC.FiberCarrier()
    
        set rstTrailer = objMOC.TrailerOptions()
           
End Function

Function SaveData()
dim strbody, strCID, strECarrier, strETrailer, strELoad, strEDoc, strEDC, strEDCName, strEAdd, strECity, strEState, MyConn2, strsql4
dim strECC, strEBCC
	

	if len(request.form("Tons_Received")) > 0 then
		
		strPounds = Request.form("Tons_received")
	strTonsReceived = round(strPounds/2000,3)



	strTrailerTID = 0
	strTrailerweight = 0
	strGrossWeight = 0
	strTareWeight = 0
    strNet = strTonsReceived
	end if 


	
	If request.form("Gross_Weight") > 0 then
	strTrailerTID = request.form("Trailer_option")
   	 strSQL3 = "Select audit_weight from tblTrailerOptions where TID = " & strTrailerTID

   	 Set MyConn3 = Server.CreateObject("ADODB.Recordset")
   	 MyConn3.Open strSQL3, Session("ConnectionString")
   	 
   	 strTrailerWeight = MyConn3.fields("Audit_Weight")
   	 
   	 MyConn3.close
   	 
   	   	 
	strTonsReceived = round((Request.form("Gross_Weight") - strTrailerweight)/2000,3)
	strPounds = round((Request.form("Gross_Weight") - strTrailerweight),3)
	strTareWeight =  strTrailerweight
   	 

	strGrossWeight = Request.form("Gross_Weight")
		strNet = strTonsReceived
	end if
	

	
If strTrailerweight > 100 then
	strTrailerweight = (strTrailerweight - 18780) ' subtract off the weight of the conventional tractor
	end if 
	

	
	Dim strRightNow
	strRightnow = now()
	
	strSAP = request.form("SAP")
	strsql = "Select Type from tblBrokeSAP where SAP = '" & strSAP & "'"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
   	StrType = MyRec("Type")
	MyRec.close

	
	
	
strsql =  "Update tblCars set SAP_Nbr = '" & strSAP & "', Broke_Description = '" & strType & "', Trailer_weight = " & strTrailerWeight & ", "_
&" Trailer_TID = " & strTrailerTID & ",    Carrier = '" & strCarrier & "',  "_
&"   Date_received = '" & strDateReceived & "', Trailer = '" & strTrailer & "', Other_Comments = '" & strOther & "',  Tons_Received = " & strTonsReceived & ", "_
&"  Net = " & strTonsReceived & ", Gross_weight = " & strGrossWeight & ", Tare_weight = " & strTareWeight & ", Status = " & strTonsReceived & " "_
	&"  where CID = " & strid & ""

	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
   	 
   	 


		Response.redirect ("Edit_Yard_trucks.asp")
	

End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->