																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Edit Shred Trucks in Yard</TITLE>
<style type="text/css">
.auto-style1 {
	font-family: Arial, Helvetica, sans-serif;
}
.auto-style3 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.auto-style5 {
	text-align: center;
}
.auto-style6 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	text-align: center;
}
.auto-style7 {
	font-size: x-small;
}
</style>
</head>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->




<% Dim <PERSON>, strsql, MyRec2, strsql2, strtdate, strdate
 
  strsql = "SELECT tblInbound.* FROM tblInbound where Destination_city = 'MOBILE' and '" & strdate & "' <= date_to "_
&" and (left(release,1) = 'M' or  left(release,1) = 'm' or left(release,1) = 'U' or  left(release,1) = 'u'   "_
&" or left(release,1) = 'S' or  left(release,1) = 's'    "_
&"   or left(release,1) = 'L' or  left(release,1) = 'l' or  left(release,1) = 'W' or  left(release,1) = 'w')"_
&" order by left(release,1), date_to" 
'response.write("strsql: " & strsql)
   Set MyRec = Server.CreateObject("ADODB.Recordset")
	 MyRec.Open strSQL, Session("ConnectionString") 
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>
<tr> 
 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Designated Location to Consume MXP, USBS, LPSBS, SWL, 
&amp; HWM  Trailers Inbound</b></font></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1>  
	 <tr class="tableheader">
<td class="auto-style3">&nbsp;</td>
	<td class="auto-style3">Species</td>

<td class="auto-style3"  >Location</td>
	<td class="auto-style6"  >Status</td>
		<td class="auto-style6"  >Trailer</td>
		<td class="auto-style3"  >Carrier</td>
		<td align = center  >        <font size="1" class="auto-style3">Delivery Date</font></td>
   
 	<td  align="left" class="auto-style6">Vendor Name</td>
<td  align="left" class="auto-style6">Vendor City</td>
<td  align="center" class="auto-style6">PO Number</td>
<td  align="center" class="auto-style6">Release</td>
	 
	
 
 

	</tr>

 <% Dim ii
       ii = 0
       

       while not MyRec.Eof
       
             strReleaseNo = MyREc("release")
             
       strsql3 = "Select CID from tblCars where Release_nbr = '" & strReleaseNo & "'"
          Set MyRec3 = Server.CreateObject("ADODB.Recordset")
	 MyRec3.Open strSQL3, Session("ConnectionString") 	
	 if not MyRec3.eof then
	 'skip display
	 else

          
       
       strsql2 = "Select OID, RF_SHRD from tblOrder where Release = '" & strReleaseNo & "'"
          Set MyRec2 = Server.CreateObject("ADODB.Recordset")
	 MyRec2.Open strSQL2, Session("ConnectionString") 	
	 if not MyRec2.eof then
	 
	    if MyRec2("RF_SHRD") = "Y" then
   strc ="r"
   else
   strc = "o"
   end if 


    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if
 
 %>
<td class="auto-style3"> <a href="SHRED_OCC_Inbound.asp?id=<%= MyRec2.fields("OID") %>&c=<%= strc %>&r=<%= strReleaseNo %>">Change</a></td>
<td class="auto-style3"> 
<% if left(Myrec("release"),1) = "W"  then %>HWM
<% elseif left(Myrec("release"),1) = "S"  then %>SWL
<% elseif left(Myrec("release"),1) = "M"  then %>MXP
<% elseif left(Myrec("release"),1) = "L"  then %>LPSBS
<% elseif left(Myrec("release"),1) = "U"  then %>USBS


<% end if %>
<td class="auto-style3"><% if MyREc2("RF_SHRD") = "Y" then %> RF <% else %> OCC <% end if %> </td>
 <td  align="center" class="auto-style6"><font face="Arial" size = 1>
	   <span class="auto-style5"><span class="auto-style1">
 <span class="auto-style7"><%= MyRec.fields("Ship_status") %>&nbsp;</span></span></span></font></td>
      <td  align="left" class="auto-style6"><font face="Arial" size = 1>
				  <span class="auto-style5"><span class="auto-style1">
	  <span class="auto-style7"><%= MyRec.fields("Trailer")%>&nbsp;</span></span></span></font></td>


	<td class="auto-style3"  ><%= MyRec.fields("carrier")%></td>
	 
		<td class="auto-style6"  >  <%= formatdatetime(MyRec.fields("date_to"),2)%></td>
	     <td  align="left" class="auto-style6"><font face="Arial" size = 1><span class="auto-style5">
		 <span class="auto-style1"><span class="auto-style7"><%= MyRec.fields("ship_from_name")%>&nbsp;</span></span></span></font></td>
     <td  align="left" class="auto-style6"><font face="Arial" size = 1><span class="auto-style5">
	 <span class="auto-style1"><span class="auto-style7"><%= MyRec.fields("ship_from_city")%>&nbsp;</span></span></span></font></td>
     <td  align="center" class="auto-style6"><font face="Arial" size = 1><span class="auto-style5">
	 <span class="auto-style1"><span class="auto-style7"><%= MyRec.fields("po")%>&nbsp;</span></span></span></font></td>
   
     <td  align="center" class="auto-style6"><font face="Arial" size = 1><span class="auto-style5">
	 <span class="auto-style1"><span class="auto-style7"><%= MyRec.fields("release")%>&nbsp;</span></span></span></font></td>
		
		
	
	</tr>

 <%  end if
 end if ' if not received already
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>


<!--#include file="Fiberfooter.inc"-->