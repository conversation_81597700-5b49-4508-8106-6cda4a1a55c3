																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Purchase Order List </TITLE>

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 



<% Dim strid, strleft, strright, strOID, MyRec, MyRec2, strsql, strsql2
strleft = left(request.querystring("id"),3)
strright = right(request.querystring("id"),2)

strsql = "SELECT tblorder.* from tblORder where left(Import_month,3) = '" & strleft & "' and right(Import_month,2) = '" & strright & "' ORDER BY PO, RELEASE"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<style type="text/css">
.style1 {
	font-family: Arial;
	font-size: x-small;
}
.style2 {
	font-family: Arial;
	font-size: x-small;
	text-align: center;
}
</style>
</head>

<body>

<% if Session("EmployeeID") = "C97338" or Session("EmployeeID") = "U04211"  or Session("EmployeeID") = "B96138" then %>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>


<td align = left><b><font face="Arial">PO List for <%= request.querystring("id") %></font></b></td>
<td align = right>&nbsp;</td>

<td align = right><font face="Arial">&nbsp;</td>


</tr>
	    </table>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=90% class = "tablecolor1" border=1 align = center>  
	 <tr class="tableheader">
<td>&nbsp;</td>
<td  align = left>     <font face="Arial" size="2">Trailer <br>Received</td>
				<td  align = left>     <font face="Arial" size="2">	PO</font></td>
		<td class="style1"  > <font face="Arial" size="2">Release</font></td>
		<td  align = left class="style1">  <font face="Arial" size="2"> Species</font></td>
		<td  align = left class="style1">  <font face="Arial" size="2"> Grade</font></td>
	
		<td  align = left class="style1">  <font face="Arial" size="2"> SAP</font></td>
			<td  align = left class="style1">  <font face="Arial" size="2"> Type</font></td>
		<td  align = left class="style1">  <font face="Arial" size="2"> Vendor</font></td>

<td  align = left class="style1">  <font face="Arial" size="2"> Generator</font></td>

<td  align = left class="style1">  <font face="Arial" size="2"> City</font></td>
<td  align = left class="style1">  <font face="Arial" size="2"> State</font></td>
<td class="style2">  <font face="Arial" size="2"> Ship <br>Week</font></td>
<td  align = left class="style1">  <font face="Arial" size="2"> Import Month</font></td>





<td  align = left class="style1">  OID</td>





	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td> <font size="2" face="Arial"><a href="Release_Edit.asp?p=i&m=<%= request.querystring("id")%>&id=<%= MyRec.fields("OID") %>">Edit</a>&nbsp;&nbsp;</td>

<% strOID = MyRec.fields("OID")
strsql2 = "Select Date_received from tblCars where OID = " & strOID
    Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL2, Session("ConnectionString")
If not MyRec2.eof then %>

<td  ><font size="2" face="Arial"><%= MyRec2.fields("Date_received")%></font></td>
<% else %> 
<td  ><font size="2" face="Arial">&nbsp;</font></td>
<% end if 
MyRec2.close%>
	<td  ><font size="2" face="Arial"><%= MyRec.fields("PO")%></font></td>
<% strRelease = MyRec("Release")
 strsql = "Select count(OID) as sCount from tblOrder where Release = '" & strRelease & "'"
 Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL, Session("ConnectionString")
    if MyRec2("SCount") > 1 then  %>
    <td bgcolor="yellow" ><font size="2" face="Arial"><%= MyRec.fields("Release")%></font></td>
    <% else %>
    <td  ><font size="2" face="Arial"><%= MyRec.fields("Release")%></font></td>
   <% End if  %>

	

	<td  ><font size="2" face="Arial"><%= MyRec.fields("Species")%></font></td>
		<td  ><font size="2" face="Arial"><%= MyRec.fields("Grade")%>&nbsp;</font></td>
	<td  ><font size="2" face="Arial"><%= MyRec.fields("SAP_Nbr")%>&nbsp;</font></td>
		<td  ><font size="2" face="Arial"><%= MyRec.fields("Broke_Description")%>&nbsp;</font></td>
	<td  ><font size="2" face="Arial"><%= MyRec.fields("Vendor")%>&nbsp;</font></td>

<td  ><font size="2" face="Arial"><%= MyRec.fields("Generator")%>&nbsp;</font></td>

<td  ><font size="2" face="Arial"><%= MyRec.fields("City")%>&nbsp;</font></td>
<td  ><font size="2" face="Arial"><%= MyRec.fields("State")%>&nbsp;</font></td>

<td  ><font size="2" face="Arial"><%= MyRec.fields("Req_ship_week")%>&nbsp;</font></td>

<td  ><font size="2" face="Arial"><%= MyRec.fields("Import_month")%>&nbsp;</font></td>

<td  ><font size="2" face="Arial"><%= MyRec.fields("OID")%>&nbsp;</font></td>


	
</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>
<% else %>
<p align="center"><font face="arial" size="3"><b>You do not have authorization to view this page</b></font></p>
<% end if %><!--#include file="Fiberfooter.inc"-->