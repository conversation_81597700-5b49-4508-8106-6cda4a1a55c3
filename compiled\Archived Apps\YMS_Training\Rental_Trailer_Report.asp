																

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>YMS Yard Exception Report</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->





<%
Dim <PERSON>Rec, strsql, MyRec2, strsql2, strtdate, strdate, strsql3

strtdate = formatdatetime(Now(),2)
  %>
  
  
	<style type="text/css">
.style1 {
	border: 1px solid #000000;
}
.style2 {
	font-family: Arial, Helvetica, sans-serif;
}
.style5 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.style8 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #FFFFFF;
}
.style10 {
	font-size: small;
	font-family: Arial, Helvetica, sans-serif;
}
.style11 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #FFFFFF;
	text-align: center;
}
.style12 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	text-align: center;
}
.style13 {
	color: #FF0000;
}
</style>
  
  
	<span class="style2">
  
  
	<br>
</span>
 <% strtoday = formatdatetime(now(),0)
 strdate = formatdatetime(now(),2)
 

 %>
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1><tr>

 <TD align = left class="style10"><strong>Rental Trailer Report: <%= strdate %></strong></td> </tr>
	    </table><br>
	    <%  
strCountLonger = 0
 strsql = "SELECT tblRentedTrailer.Trailer_Nbr, tblLazer.Trailer "_
&" FROM tblRentedTrailer inner JOIN tblLazer ON tblRentedTrailer.Trailer_Nbr = tblLazer.Trailer WHERE tblLazer.Status = 'EMPTY' and datediff(d, Audit_Date,'" & strtoday & "') > 0"
   Set MyConn2 = Server.CreateObject("ADODB.Recordset")
    MyConn2.Open strSQL, Session("ConnectionString")
    If not MyConn2.eof then 
    while not MyConn2.eof
strCountLonger = strCountLonger + 1
 MyConn2.movenext 
wend
end if
MyConn2.close 

strTransTrailer = ""
strCountNU = 0
strCountRF = 0

 strsql = "SELECT  Species, Date_received, trans_Carrier, species, PMO_Nbr,  transfer_trailer_nbr  FROM tblCars where datediff(d, date_received, '" & strtoday & "') = 1 and len(PMO_Nbr) > 2"_
 &" and Transfer_trailer_nbr Not like '%8834681' and Transfer_trailer_nbr Not like '%7004838' and Transfer_trailer_nbr Not like '%CONT%' "
    Set MyConn2 = Server.CreateObject("ADODB.Recordset")
    MyConn2.Open strSQL, Session("ConnectionString")
    If not MyConn2.eof then 
    while not MyConn2.eof 
    if MyCOnn2("PMO_Nbr") = "RAIL CAR" then
    'skip
    else
    strTransTrailer = MyConn2("Transfer_Trailer_nbr")
    
    strsql2 ="Select trailer_nbr from tblRentedTrailer where Trailer_nbr = '" & strTransTrailer & "'"
      Set MyC = Server.CreateObject("ADODB.Recordset")
    	MyC.Open strSQL2, Session("ConnectionString")
    	If not MyC.eof then
    	strCountRF = strCountRF + 1
    	else
    	strCountNU = strCountNU + 1
    	end if
    	MyC.close
    	end if
    	MyConn2.movenext
    	wend
    	end if
    	MyConn2.close
    	
strsql = "Select ID from tblMIssed where Log_Date = '" & strdate & "'"
    Set MyConn2 = Server.CreateObject("ADODB.Recordset")
    MyConn2.Open strSQL, Session("ConnectionString")
    If not MyConn2.eof then 
'skip
else
strsql2 = "Insert into tblMissed (Log_date, Missed) select '" & strdate & "', " & strCountNU & ""
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL2
			MyConn.Close

end if
MyConn2.close
strMTD = 0

strsql = "Select sum(Missed) as MTD_Missed from tblMissed where datepart(m, Log_Date) = datepart(m, '" & strdate & "')"
    Set MyConn2 = Server.CreateObject("ADODB.Recordset")
    MyConn2.Open strSQL, Session("ConnectionString")
    If not MyConn2.eof then 
strMTD = MyConn2("MTD_Missed")
else
strMTD = 0
end if
MyConn2.close
 %>
    	
 
  <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <tr><TD align = left class="style10"><strong>Trailers from Yesterday</strong></td> </tr>
  <tr><TD align = left class="style10">Non-RF: <%= strCountNU %> </td> </tr>
   <tr><TD align = left class="style10">RF: <%= strCountRF %></td> </tr>
	    </table><br>

<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>
<tr> <TD align = left class="style10"><strong><span class="style13">Missed Trailer Rental Opportunity Yesterday: <%= strCountNU %></span></strong></td> </tr>
<tr> <TD align = left class="style10"><strong><span class="style13">&nbsp;</span></strong></td> </tr>

<tr> <TD align = left class="style10"><strong>Missed Trailer Rental Opportunity MTD: <%= strMTD %> </strong></td> </tr>

<tr> <TD align = left class="style10"><br><strong>Rental Trailers sitting empty longer than one day: <%= strCountLonger %></strong> </td> </tr>
 
	    </table><br>

	<TABLE borderColor=white cellSpacing=0 cellPadding=0 class = "style1" style="width: 15%">  

	 <tr class="tableheader">
		<td class="style11"> <p class="style12">       Trailer</td>
		<td class="style11"> <p class="style12">       Audit Date</td>


	</tr>
 
<%  strtoday = formatdatetime(now(),0)

 strsql = "SELECT tblRentedTrailer.Trailer_Nbr, tblLazer.Trailer, AUdit_date "_
&" FROM tblRentedTrailer inner JOIN tblLazer ON tblRentedTrailer.Trailer_Nbr = tblLazer.Trailer WHERE tblLazer.Status = 'EMPTY' and datediff(d, Audit_Date,'" & strtoday & "') > 0"
   Set MyConn2 = Server.CreateObject("ADODB.Recordset")
    MyConn2.Open strSQL, Session("ConnectionString")
    If not MyConn2.eof then 
    while not MyConn2.eof%>

       <tr bgcolor="EEF2F6">
	<td class="style11"  ><font size="2" face="Arial"><span class="style2"><%= MyConn2("Trailer_nbr") %></span>&nbsp;</font></td>
		<td class="style11"  ><font size="2" face="Arial"><span class="style2"><%= MyConn2("Audit_Date") %></span>&nbsp;</font></td>

	</tr>
<% MyConn2.movenext 
wend
end if
MyConn2.close %>

  </table>	<br>
  
  


  
  <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1><tr>

 <TD align = left class="style10"><strong>Rented Trailers not on SIX Report</strong></td> </tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 class = "style1" style="width: 15%">  

	 <tr class="tableheader">
		<td class="style8"> <p class="style5">       Trailer</td>


		<td class="style11"> Last YMS Transaction</td>


	</tr>


<% strsql = "SELECT tblRentedTrailer.Trailer_Nbr, tblLazer.Trailer "_
&" FROM tblRentedTrailer LEFT JOIN tblLazer ON tblRentedTrailer.Trailer_Nbr = tblLazer.Trailer WHERE tblLazer.Trailer Is Null and OOS is null"
   Set MyConn2 = Server.CreateObject("ADODB.Recordset")
    MyConn2.Open strSQL, Session("ConnectionString")
    If not MyConn2.eof then 
    while not MyConn2.eof
    strTrailer = MyConn2("Trailer_Nbr") %>

       <tr bgcolor="EEF2F6">
	<td class="style8"  ><font size="1" face="Arial"><span class="style5"><%= MyConn2("Trailer_nbr") %></span>&nbsp;</font></td>
	<%    strsql2 = "Select Max(Date_unloaded) as MDR from tblCars where right(Trailer,5) = right('" & strTrailer & "',5) "
      Set MyConn = Server.CreateObject("ADODB.Recordset")
  MyConn.Open  strsql2, Session("ConnectionString")
    If not MyConn.eof then    
    strDateUnloaded = MyConn("MDR")
	end if 
	MyConn.close
	
   strsql2 = "Select Max(Trans_Unload_Date) as MDT from tblCars where  right(Transfer_Trailer_nbr,5) = right('" & strTrailer & "',5)"
      Set MyConn = Server.CreateObject("ADODB.Recordset")
  MyConn.Open  strsql2, Session("ConnectionString")
    If not MyConn.eof then    
    strDateUnloadedT = MyConn("MDT")
	end if 
	MyConn.close
	
	if strDateUnloadedT > strDateUnloaded then
	strUseDate = strDateUnloadedT
	else
	strUseDate = strDateUnloaded
	end if
%>
	<td class="style8"  ><%= strUseDate %>&nbsp;</td>
	
	</tr>
<% MyConn2.movenext 
wend
end if
MyConn2.close %>

  </table>
  
	
 
	
	
  
  
     <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1><tr>

 <TD align = left class="style10"><br>
	<strong>Transfers from Yesterday</strong></td> </tr>
	    </table><br>
	

	<TABLE borderColor=white cellSpacing=0 cellPadding=0 class = "style1" style="width: 15%">  

	 <tr class="tableheader">
		<td class="style8"> <p class="style5">       Trailer</td>
		<td class="style8"> <p class="style5">      Date</td>
	<td class="style8"> <p class="style5">      From Location</td>
		<td class="style8"> <p class="style5">    To Location</td>
<td class="style8"> <p class="style5">     Carrier</td>
<td class="style8"> <p class="style5">  Species</td>

	</tr>
 
<%  strtoday = formatdatetime(now(),0)

 strsql = "SELECT  Species, Date_received, trans_Carrier, species, Location, PMO_Nbr,  transfer_trailer_nbr  FROM tblCars where datediff(d, date_received, '" & strtoday & "') = 1 and len(PMO_Nbr) > 2"
    Set MyConn2 = Server.CreateObject("ADODB.Recordset")
    MyConn2.Open strSQL, Session("ConnectionString")
    If not MyConn2.eof then 
    while not MyConn2.eof
   %>

       <tr bgcolor="EEF2F6">
	<td class="style8"  ><font size="1" face="Arial"><span class="style5"><%= MyConn2("Transfer_Trailer_nbr") %></span>&nbsp;</font></td>
		<td class="style8"  ><font size="1" face="Arial"><span class="style5"><%= MyConn2("Date_Received") %></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style5"><%= MyConn2("PMO_Nbr") %></span>&nbsp;</font></td>
		<td class="style8"  ><font size="1" face="Arial"><span class="style5"><%= MyConn2("Location") %></span>&nbsp;</font></td>
<td class="style8"  ><font size="1" face="Arial"><span class="style5"><%= MyConn2("Trans_Carrier") %></span>&nbsp;</font></td>
<td class="style8"  ><font size="1" face="Arial"><span class="style5"><%= MyConn2("Species") %></span>&nbsp;</font></td>
	</tr>
<% 
MyConn2.movenext 
wend
end if
MyConn2.close %>

  </table>
  
  <br>
 
   


 
