<% option explicit %>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=iso-8859-1">
	<META HTTP-EQUIV="Content-Language" CONTENT="en-FX">

<title> Calendar </title>
	<META NAME="copyright" CONTENT="(c) 2000, <PERSON><PERSON>, myLittleTools.net"">

	<LINK REL="stylesheet" HREF="css/mylittlecalendar.css" TYPE="text/css">
</HEAD>

<DIV ALIGN=CENTER>
<%
	Dim myElt
	myElt = Request.QueryString("elt")

	Dim mlcObj, myDate
	If Request.Form("mlcYear") <> "" Then
		myDate = DateSerial(Request.Form("mlcYear"), Request.Form("mlcMonth"), Request.Form("mlcDay"))
	End If
'	Set mlcObj = Server.CreateObject("myLittleCalendar.WSC")
	Set mlcObj = GetObject("script:" & Server.MapPath(".\component\myLittleCalendar.wsc"))

	If isDate(myDate) Then mlcObj.mlcDate = myDate
	With mlcObj
		.mlcActionURL = Request.ServerVariables("SCRIPT_NAME") & "?" & Request.ServerVariables("QUERY_STRING") 
		.mlcYearRange = 50
	End With
	mlcObj.displayCalendar()
	Set mlcObj = Nothing
%>

	<SCRIPT LANGUAGE="JavaScript">
	<!--
		function setInfo(pStr)
		{
			if (window.opener.document.form1)
				window.opener.document.form1.<%=myElt%>.value = pStr;
				window.close();
		}
	//-->
	</SCRIPT>
	

	<% If Request.Form("mlcDayChosen")  <> "" Then %>
		<SCRIPT LANGUAGE="JavaScript">
		<!--
			setInfo(document.myLittleCalendar.mlcDate.value);
		//-->
		</SCRIPT>
	<% End If %>

</DIV>
</BODY>
</HTML>
