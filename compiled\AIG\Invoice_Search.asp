
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>AIG Invoice Search</TITLE>

<!--#include file="classes/asp_cls_DataAccessAIG.asp"-->
<!--#include file="classes/asp_cls_headerAIG.asp"-->
<!--#include file="classes/asp_cls_SessionStringAIG.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->


 <%  Dim objEPS
    Dim objGeneral
   dim   rst
 
   dim strPageNav, strMill,  strsql, MyConn, strUserType, strPO, strRelease, strVendor, strSpecies, strBegDate, strEndDate
 
      set objGeneral = new ASP_CLS_General
       
	   strMill = request.form("Mill")
  	   strVendor = request.form("Vendor")
  	   strSpecies = request.form("Species")
  	   strBegDate = request.form("Beg_Date")
  	   strEndDate = request.form("End_Date")

 
 
strsql = "SELECT tblAIGUserType.* FROM tblAIGUserType where (BID = '" & Session("EmployeeID") & "') " 

    Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
    If not MyConn.eof then
    struserType = MyConn.fields("User_type")
    else
    strUserType = ""
    end if
    If Session("EmployeeID") = "C97338" or session("EmployeeID") = "U17097" then
    strUserType = "B"
    end if
    If strUserType = "" then

    Response.write ("<br><br><font face = arial size = 3><b>You do not have authorization to view this page</b></font>")
   else
    BuildScreen()
    end if 
   Sub Buildscreen()  
   strPO = request.form("PO")
   strRelease = request.form("Release")
 %>


<style type="text/css">
.auto-style1 {
	border-color: #C0C0C0;
	border-width: 1px;
	text-align: left;
	background-color: #FFFFFF;
	font-size: x-small;
}
.auto-style2 {
	border-color: #C0C0C0;
	border-width: 1px;
	font-weight: bold;
	text-align: left;
	background-color: #E7EBFE;
}
.auto-style3 {
	border-color: #C0C0C0;
	border-width: 1px;
	background-color: #FFFFFF;
	font-size: x-small;
}
.auto-style4 {
	border-color: #C0C0C0;
	font-weight: bold;
	border-width: 1px;
	background-color: #E7EBFE;
	font-size: medium;
	font-family: Calibri;
}
.auto-style6 {
	border-color: #C0C0C0;
	border-width: 1px;
	font-weight: bold;
	text-align: left;
	background-color: #E7EBFE;
	font-size: medium;
	font-family: Calibri;
}
.auto-style7 {
	border: 1px solid #C0C0C0;
	background-color: #E7EBFE;
}
.auto-style8 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-size: medium;
	font-weight: bold;
	font-family: Calibri;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.auto-style9 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.auto-style10 {
	font-family: Arial, Helvetica, sans-serif;
}
.auto-style11 {
	font-size: x-small;
	font-family: Arial, Helvetica, sans-serif;
}
.auto-style12 {
	font-family: Calibri;
	font-size: medium;
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.auto-style13 {
	font-size: medium;
	font-family: Calibri;
}
.auto-style14 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial, Helvetica, sans-serif;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.auto-style15 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial, Helvetica, sans-serif;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	text-align: center;
}
.auto-style16 {
	font-family: Calibri;
	font-size: medium;
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	text-align: center;
}
.auto-style17 {
	border-color: #C0C0C0;
	border-width: 1px;
	font-weight: bold;
	text-align: center;
	background-color: #E7EBFE;
	font-size: medium;
	font-family: Calibri;
}
</style>
</head>


<form name="form1" action="Invoice_search.asp" method="post">
 
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>  
  <tr><td align = Center  ><font face = arial size = 2><B>Search for Invoices to View </b>
	</font></td>


	</tr></table>
<TABLE cellSpacing=0 cellPadding=0 width=100% class="auto-style7">  

  <TR>
   <TD class="auto-style12"><strong>Release </strong>#<strong>&nbsp;&nbsp;&nbsp;

   </strong>

   </td>
 
       
   <TD class="auto-style8"> <strong>PO#</strong></td>
 
       
   <TD class="auto-style12"><strong>Vendor</strong></td>
 
       
   <TD class="auto-style12"><strong>Species</strong></td>
 
       
   <TD class="auto-style12"><strong>Site</strong></td>
 
       
   <TD class="auto-style16"><strong>Beg Invoice Date</strong><br>(mm/dd/yyyy)</td>
 
       
   <TD class="auto-style16"><strong>End Invoice Date</strong><br>(mm/dd/yyyy)</td>
 
       
      <TD align = center rowspan="2" class="auto-style9"><input  name="Submit" type="submit" Value="Submit"></TD>
    
   </TR>
  
  <TR>
   <TD class="auto-style14">
     <font face="Arial">	
	 <input type="text" name="Release" size="18" value="<%= strRelease %>" class="auto-style11" style="width: 112px"></td>
 
       
   <TD class="auto-style9">
   <input type="text" name="PO" size="18" value="<%= strPO%>" style="width: 120px" class="auto-style10"></td>
 
       
   <TD class="auto-style14">
<select name="Vendor" >
<option value="">Select</option>
<% 
strsql3 = "Select Distinct Vendor_Name from tblInvoice order by Vendor_name"

Set MyRec3 = Server.CreateObject("ADODB.Recordset")
  MyRec3.Open strSQL3, Session("ConnectionString") 
  While not MyRec3.eof %> 
<option <% if strVendor = MyREc3("VEndor_name") then %> selected <% end if %>>  <%= MyRec3("Vendor_name") %></option>	
 
<%   
MyRec3.movenext
wend 
MyRec3.Close
%>
</select></td>
 
       
   <TD class="auto-style14">
<select name="Species" >
<option value="">Select</option>
<% 
strsql3 = "Select Distinct Species from tblInvoice order by Species"

Set MyRec3 = Server.CreateObject("ADODB.Recordset")
  MyRec3.Open strSQL3, Session("ConnectionString") 
  While not MyRec3.eof %> 
<option <% if strSpecies = MyREc3("Species") then %> selected <% end if %>>  <%= MyRec3("Species") %></option>	
 
<%   
MyRec3.movenext
wend 
MyRec3.Close
%>
</select></td>
 
       
   <TD class="auto-style14"><select name="Mill" >
<option value="">Select</option>
<% 
strsql3 = "Select Distinct Mill from tblInvoice order by Mill"

Set MyRec3 = Server.CreateObject("ADODB.Recordset")
  MyRec3.Open strSQL3, Session("ConnectionString") 
  While not MyRec3.eof %> 
<option <% if strMill = MyREc3("Mill") then %> selected <% end if %>> <%= MyRec3("Mill") %></option>	
 
<%   
MyRec3.movenext
wend 
MyRec3.Close
%>
</select></td>
 
       
   <TD class="auto-style15">
     <font face="Arial">	
	 <input type="text" name="Beg_Date" size="18" value="<%= strBegDate %>" class="auto-style11" style="width: 79px"> </td>
 
       
   <TD class="auto-style15">
     <font face="Arial">	
	 <input type="text" name="End_Date" size="18" value="<%= strEndDate %>" class="auto-style11" style="width: 79px">
   </td>
 
       
   </TR>
  
  </TABLE>


</form>

 
  <% if objGeneral.IsSubmit()  Then
 
    
 %>
   <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>
    <tr><td colspan="17" bgcolor="white" style="height: 31px"><font face="Arial" size="2"><b>Search Results</b> </font></td></tr>
   
      <tr >
      <td align = center class="auto-style4">Site</td>
	<td align = center class="auto-style4">PO</td>
	<td class="auto-style4">Release</td>
	<td class="auto-style6">Vendor</td>
	 <td class="auto-style6">Generator</td>   
	<td class="auto-style6">Origin</td>
	<td class="auto-style6">Species</td>
	 <td class="auto-style6">Trailer</td>	
	<td class="auto-style6">Carrier</td>

		<td class="auto-style17">SAP Doc<br> ID</td>
	
	<td class="auto-style6">Weight</td>
	<td class="auto-style6">PPT</td>
	
	<td class="auto-style2"><font size="1" class="auto-style13">Invoice <br>Amount</font></td>
	<td class="auto-style2"><font size="1" class="auto-style13">Date<br> Trans.</font></td>
	   <td class="auto-style2"><font size="1" class="auto-style13">Invoice<br>Date</font></td>   </tr>
  	<% 
  	
  	   strRelease = Request.form("Release")
  	   strMill = request.form("Mill")
  	   strVendor = request.form("Vendor")
  	   strSpecies = request.form("Species")
  	   
  	    strsql = "Select tblInvoice.* from tblInvoice where INV_Nbr > 0  "
  	 if len(request.form("PO")) > 3 then
  	 	strPO = cdbl(Request.form("PO"))
   		strsql = strsql  & " and PO = " & strPO & " "
   		end if
	if len(strRelease) > 3 then 
   strsql = strsql & " and Release_nbr = '" & strRelease & "' "
   end if
	
		if len(strMill) > 1then 
   strsql = strsql & " and Mill = '" & strMill & "' "
   end if
   
   	if len(strVendor) > 3 then 
   strsql = strsql & " and Vendor_Name = '" & strVendor & "' "
   end if
   
     	if len(strSpecies) > 1 then 
   strsql = strsql & " and Species = '" & strSpecies & "' "
   end if
   
  if len(request.form("Beg_Date")) > 4 then
  strsql = strsql & " and Inv_Date >= '" & strBegDate & "'"
  end if
  
    if len(request.form("End_Date")) > 4 then
  strsql = strsql & " and Inv_Date <= '" & strEndDate & "'"
  end if

Set rst = Server.CreateObject("ADODB.Recordset")
    rst.Open strSQL, Session("ConnectionString")
    If not rst.eof then

      Dim ii
       ii = 0
       while not rst.Eof


    %>
 <tr>
    	<td align = center class="auto-style3"><font face="Arial"><%= rst.fields("Mill")%></font></td>
	<td align = center class="auto-style3"><font face="Arial"><%= rst.fields("PO")%></font></td>
	<td class="auto-style3"><font face="Arial"><%= rst.fields("Release_nbr")%></font></td>
	<td class="auto-style1"><font face="Arial"><%= rst.fields("Vendor_name")%></font></td>
	 	<td align = left class="auto-style3"><font face="Arial"><%= rst.fields("Generator")%></font></td>  
	 		<td align = left class="auto-style3"><font face="Arial"><%= rst.fields("Origin")%></font></td>
	 				<td align = left class="auto-style3"><font face="Arial"><%= rst.fields("Species")%></font></td>
	<td align = left class="auto-style3"><font face="Arial"><%= rst.fields("Trailer")%></font></td>
		<td align = left class="auto-style3"><font face="Arial"><%= rst.fields("Carrier")%></font></td>
		<td class="auto-style1"><font face="Arial"><%= rst.fields("Doc_ID")%></font></td>
					<td class="auto-style1"><font face="Arial"><%= rst.fields("Weight")%></font></td>
					
	<td class="auto-style1"><font face="Arial"><%= rst.fields("PPT")%></font></td>
	<td class="auto-style1"><font face="Arial"><%= formatcurrency(rst.fields("Inv_amount"),2)%></font></td>
			<td class="auto-style1"><font face="Arial"><%= rst.fields("Transmitted")%></font></td>

<td class="auto-style1"><font face="Arial"><%= formatdatetime(rst.fields("Inv_date"),2)%></font></td>
		

	
	

</tr>
    <%
      
       rst.MoveNext
     Wend
     end if
    %>
   </table>
 <%  
 end if ' if submit
 end sub %>
    
<!--#include file="AIGfooter.inc"-->