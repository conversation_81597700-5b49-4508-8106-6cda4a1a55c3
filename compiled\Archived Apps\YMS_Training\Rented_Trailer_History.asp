

<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">
<TITLE>Rented Trailer Movement</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->


<%  
	Dim strsQL, rstEquip

   	Dim objEquipSearch, rstTotals, strCount, strDed, strNet, strTons, rstTotalReceived, strTR, objNew, rstSpecies
  	Dim  strVendor, strBegDate, strEndDate, strSpecies, strMonth, strDateReceived, strDateAdded, rst<PERSON><PERSON><PERSON>
 	Dim  rstVendor, rstNF
  	Dim objGeneral, strPO, gcount, strDays, strBeg, strEnd, what
  	
  	Dim strTrailer, strAT, strStype, objMOC
   	
   	Dim intDirection
  	Dim intPageNumber
   	Dim strPageNav

   intPageNumber = 1

   set objGeneral = new ASP_CLS_General

  if objGeneral.IsSubmit() Then
    Call GetFormData()
    Select Case intDirection
      Case 10: 'goto mocinfo.asp
  
      Case Else:
        'Call LoadSearchResults()
    End Select
  else
    intDirection = 0
  end if
Call GetData()

Function StripOut(From, What) 

    Dim i 
	 
    StripOut = From
    for i = 1 to len(What)
	StripOut = Replace(StripOut, mid(What, i, 1), "")
    next 
	 
	End Function
	
	What = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
%>

<script language="javascript">
 
  function GotoMOC(MOCID)
  {
    document.forms["form1"].elements["tbl_ID"].value = MOCID;
    document.forms["form1"].elements["Direction"].value = 10;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }

  function Search()
  {
    
    document.forms["form1"].elements["Direction"].value = 1;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }

</script>


<style type="text/css">
.style1 {
	text-align: right;
}
.style2 {
	border-style: solid;
	border-width: 1px;
}
</style>
</head>


<form name="form1" action="Rented_Trailer_History.asp" method="post">
 <input type="hidden" name="Direction" value="1" >

  <TABLE borderColor=white cellSpacing=0 cellPadding=0 style="width: 50%" align="center" class="style2" >  <tr>
    <TD class="style1" ><b><font face="Arial" size="2">Beginning Date:&nbsp; &nbsp;</font></b></TD>
    <TD>   <font face="Arial">  
	<input type="text" name="Beg" size="15" value = "<%= strBeg %>" style="width: 111px"></font></td>
	  
	    <TD class="style1" ><b><font face="Arial" size="2">Ending Date:&nbsp;&nbsp;</font></b></TD>
    <TD>   <font face="Arial">	  
	<input type="text" name="End" size="15" value = "<%= strEnd %>" style="width: 108px"></font></td>
  
<td>	  <input type="button" onClick="javascript:Search()" value="Search" caption="Search"></TD> 


 </TR>



  </TABLE></form>
    
    <% if objGeneral.IsSubmit() Then %>

&nbsp;<TABLE borderColor=white cellSpacing=0 cellPadding=0 class="style2" align = center style="width: 50%">
	 <tr class="tableheader">
	 	<td  ><b><font face="Arial" size="2">Trailer</font></b></td>
	<td  ><b><font face="Arial" size="2">Date_received</font></b></td>
		<td align = left><b><font face="Arial" size="2">Date_unloaded</font></b></td>
 	<td align = left><b><font face="Arial" size="2">Species</font></b></td>
	<td align = left><b><font face="Arial" size="2"> From Rail Car</font></b></td>
<td><b><font face="Arial" size="2">Comments</font></b></td>
<td><b><font face="Arial" size="2"><a href="Rented_Trailer_History_Excel.asp?b=<%= strbeg %>&e=<%= strend %>">Export</a></font></b></td>

	</tr>



      
  	<% 
      Dim ii
       ii = 0
       strTcount = 0
       
           Set MyRec2 = Server.CreateObject("ADODB.Recordset")    
      
	strsql = "select tblRentedTrailer.* from tblRentedTrailer order by Trailer_nbr"
 
    MyRec2.Open strSQL, Session("ConnectionString"), adOpenDynamic

       while not MyRec2.Eof
       if MyRec2("OOS") = "Y" then
       'skip
       else
       strcount = 1
       
        strTrailer = MyRec2("Trailer_nbr")  
   		 strT2 = stripout(strTrailer,What)
   		 
strBeg = Request.form("Beg")
strEnd = Request.form("End") %>
   		
    
    

   <tr bgcolor="white">   <td colspan="8"  >  <font size="3" face="Arial"><b>        <%= MyRec2.fields("Trailer_nbr")%></b></font></td>
   
    </tr>
  <tr>  
   		 

   <% strsql = "SELECT Entry_Bid, tblcars.cid, species,  tblcars.location, carrier, Transfer_date, trans_unload_date, tblcars.species, "_
   &"  transfer_trailer_nbr, tblCars.release_nbr,  tblCars.Trailer,  tblCars.Date_Received, tblCars.Date_unloaded, "_
   &"   tblcars.other_comments, tblCars.Tons_received, RC_CID FROM  tblCars  "_
        &"  where datediff(d, '" & strBeg & "', Date_received) >= 0 and datediff(d, Date_received, '" & strEnd & "') >= 0 and"_
      &"  ( Trailer = '" & strTrailer & "' or Trailer = '" & strT2 & "' "_
        &"  or Transfer_Trailer_Nbr = '" & strTrailer & "' or Transfer_Trailer_Nbr = '" & strT2 & "') "
       
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    
    If not MyRec.eof then
    while not MyRec.eof

    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr class=tablecolor1>
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    <% if MyRec("Trailer") = "UNKNOWN" then %>
     
      <td  >  <font size="2" face="Arial">        <%= MyRec.fields("Transfer_trailer_nbr")%></font></td>
<% else %>
   <td  >  <font size="2" face="Arial">        <%= MyRec.fields("Trailer")%></font></td>
<% end if %>
      

      <td  >  <font size="2" face="Arial">  &nbsp;      <%= MyRec.fields("Date_received")%></font></td>
 
    
     <td  >  <font size="2" face="Arial">        <%= MyRec.fields("Date_unloaded")%></font></td>

    	<td><font size="2" face="Arial"><%= MyRec.fields("Species")%>&nbsp;</font></td>
    		<td><font size="2" face="Arial"><% if len(MyRec("RC_CID")) > 1 then %> YES <% end if %>&nbsp;</font></td>
	<td><font size="2" face="Arial"><%= MyRec.fields("Other_comments")%>&nbsp;</font></td>
	<td><font size="2" face="Arial"><%= strCount %>&nbsp;</font></td>
  
   </tr>
    <% 
       ii = ii + 1
 
    
strcount = strcount + 1
strTcount = strTcount + 1

       MyRec.MoveNext
     Wend
     end if 
     
     end if
     MyRec2.movenext
     wend
    %></table>
    <p align="center"><font face="arial" size="2"><b>Total:  <%= strTcount %></b></font></p>
    <%  end if  
    
    
    Function GetFormData()
      intDirection = cint(Request.Form("Direction"))
  
    
     strBeg= request.form("Beg")
     strEnd = request.form("End")


	
End Function

 Function GetData()   
    
     strBeg= request.form("Beg")
     strEnd = request.form("End")

    End Function 
    

 %><!--#include file="Fiberfooter.inc"-->