																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE> Area list</TITLE>
<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_sessionPLI.asp"--> 
<!--#include file="classes/ASP_CLS_General.asp"--> 
<!--#include file="classes/asp_cls_header.asp"-->


<% 

'Response.Write Request.Form

If Request("btnSubmit") = "Submit" Then
   For Each Item In Request.Form
       If Left(Item,3) = "cbx" Then
	      ary = Split(Request.Form(Item),"|")
          strsql = "Update tblRequestedTimeOff SET Approved = " & ary(1) & " WHERE ID = " & ary(0)
	      'Response.Write strsql & "<BR>"
          Set MyUpdate = Server.CreateObject("ADODB.Connection")
   	      MyUpdate.Open Session("ConnectionPolaris")
	      MyUpdate.Execute strSQL
	      MyUpdate.Close 
	      Set MyUpdate = Nothing
	   End If
   Next
   Response.Redirect "Approvals.asp"
End If

Dim MyRec, strsql, MyConn, strPost
 Dim MyS, MySQ, strOK, strsite

STRSQL = "Select EMP_ID from tblVac_Admin where EMP_ID = '" & Session("EmployeeiD") & "'  "

Set MyS = Server.CreateObject("ADODB.Recordset") 
   		Set MyS = Server.CreateObject("ADODB.RecordSet")
		MyS.Open strSQL, Session("ConnectionString")
If not MyS.eof then
strPost = "OK"
else 
strPost = ""
end if
MyS.close

if Session("EmployeeID") = "C97338" then 
strPost = "OK"
end if



%>
<style type="text/css">
.style1 {
	border: 1px solid #000000;
}
.style2 {
	border: 1px solid #C0C0C0;
}
.style3 {
	text-align: center;
	border: 1px solid #C0C0C0;
}
.style5 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style6 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	text-align: center;
}
.style7 {
	font-size: small;
}
.style8 {
	border-width: 1px;
}
.auto-style1 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	text-align: center;
	background-color: #E2F3FE;
}
.auto-style3 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	text-align: left;
	background-color: #E2F3FE;
}
</style>
</head>

<body>
<br>
<form name="form1" action="ApproveTimeOff.asp" method="post">
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=60%  border=1 align = center>
<tr>
 <TD align = left style="width: 15%" class="style8"><font size="2" face = arial>
	<span class="style7"><strong>
<% if strPost = "OK" then%>
 <% else %>
 &nbsp;</strong></span>
 <% end if %></font></td>
<td align = center><b>
<font face="arial" size="4" >  Approval List</font></b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br><br>

	<table align="center">
	
		<tr><td colspan="6" align="right"><input type="submit" name="btnSubmit" value="Submit"><br><br></td></tr>
     </table>
	 <br>
	<TABLE align = center style="width: 35%" class="style1">  
	 <tr bgcolor="#FFFFCC">
	<td  align="left" class="auto-style3" >  Technician</td>
	<td  align="left" class="auto-style3" >  Area</td>
	<td  align="left" class="auto-style3" >  Shift</td>
	<td  align="left" class="auto-style3" >  Date</td>
	<td  align="left" class="auto-style1" >  Check to Approve  <input type="checkbox" value="ON" name="CheckAll" id="CheckAll" onclick="checkAll()"></td>
	<td  align="left" class="auto-style1" >  Check to Disapprove  </td>



	</tr>

 <% strsql = "select DISTINCT tblRequestedTimeOff.ID, tblTechnician.Technician, tblTechnician.KCID, tblArea.Area, Shift, TimeOffDate FROM tblRequestedTimeOff INNER JOIN tblAreaVacArea ON tblRequestedTimeOff.VacAreaID = tblAreaVacArea.VacAreaID INNER JOIN tblArea ON tblAreaVacArea.AreaID = tblArea.ID  INNER JOIN tblVacArea ON tblAreaVacArea.VacAreaID = tblVacArea.ID INNER JOIN tblTechnician ON tblRequestedTimeOff.TechnicianID = tblTechnician.ID  Where Approved IS NULL " 

If Len(Request("area")) > 0 Then
   strsql = strsql &  " AND tblArea.Area = '" & Request("area") & "'" 
End If

If Len(Request("Shift")) > 0 Then
   strsql = strsql & " AND Shift = '" & Request("Shift") & "'" 
End If

If Len(Request("TimeoffDate")) > 0 Then
   strsql = strsql & " AND DATEDIFF(d,TimeOffDate,'" & Request("TimeoffDate") & "') = 0"
End If
'Response.Write strsql


    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

 
 Dim ii
       ii = 0
       while not MyRec.Eof
  
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
 
	<td  align="left" class="style2" > <font size="2" face="Arial"> <%= MyRec.fields("Technician")%>&nbsp;</font></td> 
    
	<td  align="left" class="style2" > <font size="2" face="Arial"> <%= MyRec.fields("Area")%>&nbsp;</font></td> 

		<td  align="left" class="style2" > <font size="2" face="Arial"> <%= MyRec.fields("Shift")%>&nbsp;</font></td> 
<td  align="left" class="style2" > <font size="2" face="Arial"> <%= MyRec.fields("TimeoffDate")%>&nbsp;</font></td> 

		<td bgcolor="green" align="center" > <font size="2" face="Arial">
           <input type="radio" name="cbx<%=ii%>" id="cbx<%=ii%>" value="<%= MyRec.fields("ID")%>|1">&nbsp;</font></td> 
		<td bgcolor="red" align="center" > <font size="2" face="Arial">
           <input type="radio" name="cbx<%=ii%>" id="cbx<%=ii%>" value="<%= MyRec.fields("ID")%>|0">&nbsp;</font></td>  

	

    


	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
     MyRec.close
    %>
</table>
<script type="text/javascript">
function checkAll(){
   if (document.getElementById("CheckAll").checked) {
   <%for i = 0 to ii - 1%>
      document.getElementById("cbx<%=i%>").checked = true;
   <%next%>
   }
   else {
   <%for i = 0 to ii - 1%>
            document.getElementById("cbx<%=i%>").checked = false;
   <%next%>
   }
}
</script>

</form>

<!--#include file="footer.inc"-->

