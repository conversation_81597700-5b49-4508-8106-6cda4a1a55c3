																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Add Carrier </TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->


<% Dim MyRec, strsql, MyRec2, strsql2,  MyConn3, strsql3,  strFee



  set objGeneral = new ASP_CLS_General
  if objGeneral.IsSubmit() Then	
  Dim strCarrier,  strEmaill_add, strFree_Days
  
  	strCarrier = request.form("Carrier")
  	

  	
  	If len(request.form("C_Description")) > 0 then
  	strC_Description = request.form("C_Description")
  	else
  	strC_Description = ""
  	end if 
  	
  	If len(request.form("Free_Days")) > 0 then
  	strFree_Days = request.form("Free_Days")
  	else
  	strFree_Days = 0
  	end if
  	
  
  		
  	If len(request.form("Fee")) > 0 then
  	strFee= request.form("Fee")
  	else
  	strFee = 0
  	end if
 
  	
  If len(request.form("Email_add")) > 1 then	
 strEmail_add = Request.form("Email_add")
 else
 strEmail_add = ""
 end if 
 
  if Session("EmployeeID") = "C97338" or Session("EmployeeID") = "B55404" or Session("EmployeeID") = "B55548" then 
  if len(Request.form("Weight")) > 0 then 
  
  strsql =  "INSERT INTO tblCarrier (Carrier,  C_Description, Free_Days, Fee, Email_add) "_
	&" SELECT  '" & strCarrier & "',  '" & strC_Description & "', " & strFree_Days & ", " & strFee & ",'" & strEmail_add & "'"

  else
  
  strsql =  "INSERT INTO tblCarrier (Carrier,  C_Description, Free_Days, Fee, Email_add) "_
	&" SELECT  '" & strCarrier & "',  '" & strC_Description & "', " & strFree_Days & ", " & strFee & ",'" & strEmail_add & "'"

  
  end if

 
 else       	
  	
	strsql =  "INSERT INTO tblCarrier (Carrier,  C_Description, Free_Days, Fee, Email_add) "_
	&" SELECT  '" & strCarrier & "',  '" & strC_Description & "', " & strFree_Days & ", " & strFee & ",'" & strEmail_add & "'"
end if	
	
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
Response.redirect("Carrier.asp")		
end if

	
%>

<style type="text/css">
.style3 {
	border-style: solid;
	border-width: 1px;
}
.style4 {
	font-size: x-small;
}
.style5 {
	font-family: arial;
	font-size: x-small;
}
.style6 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	text-align: center;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #FFFFFF;
}
.style9 {
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
}
.style10 {
	font-family: arial;
	font-size: small;
}
.style11 {
	font-size: small;
}
.style12 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #FFFFFF;
}
.style13 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #EAF1FF;
}
.style14 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: arial;
	font-size: small;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #EAF1FF;
}
.style15 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style16 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	text-align: center;
}
</style>
</head>

<body>
<br>
	
<form name="form1" action="Carrier_Add.asp" method="post" ID="Form1">
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Add Carrier</b></font></td>
<td align = right><font face="Arial"><a href="Carrier.asp">Return</a>&nbsp;</td>


</tr>
	    </table>



	<p>&nbsp;</p>
	<table id="table1" style="width: 65%" align="center" class="style3">
<tr bgcolor = #CCCCFF>
		<td class="style14" style="height: 32px">Carrier</td>
		<td class="style14" style="height: 32px">Carrier Description</td>
				<% if Session("EmployeeID") = "C97338" or Session("EmployeeID") = "B55404" or Session("EmployeeID") = "B55548" then %>
<td class="style13" style="height: 32px"><p align="center" class="style10">Trailer Weight</td>
<% end if %>
		<td class="style13" style="height: 32px"><p align="center" class="style10">Free Days</td>
	</tr>
	<tr>
		<td class="style15"><font face = arial size = 1>		<span class="style4">		<input type="text" name="Carrier" size="20" style="width: 122px"></span></td>
		<td class="style15"><font face = arial size = 1>		<span class="style4">		<input type="text" name="C_Description" size="20" style="width: 479px"></span></td>
<% if Session("EmployeeID") = "C97338" or Session("EmployeeID") = "B55404" or Session("EmployeeID") = "B55548" then %>	
	<td class="style16"><font face = arial size = 1>		<span class="style4">		
	<input type="text" name="Weight" size="20" style="width: 90px"></span></td>
<% end if %>
	<td class="style15"><p align="center"><font face = arial size = 1>		<span class="style4">		<input type="text" name="Free_Days" size="20" style="width: 29px; height: 22px;"></span></td>
	</tr>
</table>
<span class="style4">
<br>
		
		</span>
		
		<table cellpadding="0" id="table4" style="width: 85%" align="center" class="style9" >
			<tr bgcolor = #CCCCFF>

		<td class="style14" style="height: 43px"><span class="style11">F</span><span class="style10">ee 
		(</span><span class="style5"><font face = arial size = 1><span class="style11">e.g. 40)</span></span></td>

	<td style="font-family: Arial; height: 43px;" class="style13">
	<p align="center" class="style11">Email Addresses - Separate multiple 
	addresses with semi-colon </td>
	
	</tr>
	
	<td class="style6"><font face = arial size = 1>
	<span class="style4">
<input type="text" name="Fee" size="39" style="width: 44px"> </span></td>


<td height="18" class="style12">
<p align="center"><font face = arial size = 1>
<span class="style4">
<input type="text" name="Email_add" size="39" style="width: 1029px"></span></td>


</tr></table>


<p align="center"><font face="Arial">
	<span class="style4">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></span></font></p>
</form>

<!--#include file="Fiberfooter.inc"-->