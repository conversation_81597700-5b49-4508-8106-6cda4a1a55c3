﻿<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->


<%

dim strID, strCarrier
strID = Request.querystring("id")
 strPage = request.querystring("p")
 strMonth = request.querystring("m")
 strPO = request.querystring("o")
 strToggle = request.querystring("t")

    set objGeneral = new ASP_CLS_General
    
    
 
	strSQL = "Update tblOrder set V_weight = '" & strToggle & "' where OID = " & strid
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
 'Response.write ("strsql" & strsql)

If strPage = "p" then
 

 Response.redirect("PO_List_Verify.asp?id=" & strPO)	
elseif strpage = "i" then
Response.redirect("Import_month_list_Verify.asp?id=" & strMonth)
end if 
 
%><!--#include file="Fiberfooter.inc"-->