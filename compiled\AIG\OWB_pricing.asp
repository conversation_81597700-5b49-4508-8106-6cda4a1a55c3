																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Owensboro Pricing</TITLE>
<!--#include file="classes/asp_cls_DataAccessAIG.asp"-->
<!--#include file="classes/asp_cls_headerAIG.asp"-->
<!--#include file="classes/asp_cls_SessionStringAIG.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->


<% Dim MyRec, strsql, MyConn, strUserType


 
    If Session("EmployeeID") = "C97338" or Session("EmployeeID") = "B63398" or Session("EmployeeID") = "B17796"  then
    strUserType = "B"
    end if
    If strUserType = "" then

    Response.write ("<br><br><font face = arial size = 3><b>You do not have authorization to view this page</b></font>")
   else
    BuildScreen()
    end if 

    %>
    <%Sub Buildscreen



strsql = "SELECT top 1000 tblPricing.* FROM tblPricing where Site = 'OWB' and len(Price)>1 and do_not_invoice is null order by ID desc  " 
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>


<td align = center><b>
<font face="arial" size="4" >Owensboro Recovered Paper Pricing List</font></b></span></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=75% class = "tablecolor1" border=1 align = center>  
	 <tr class="tableheader">

	<td  >  <font face="Arial" size="2">PO #</font></td>
	<td  >  <font face="Arial" size="2">Vendor Number</font></td>
		<td  >  <font face="Arial" size="2">Vendor Name</font></td>
		<td  >  <font face="Arial" size="2">Generator</font></td>
        <td  > <font face="Arial" size="2">Price</font></td>
             <td  > <font face="Arial" size="2">Confirmed</font></td>
	<td>&nbsp;</td>
		


	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>

	<td  >      <font size="2" face="Arial">        <%= MyRec.fields("PO")%>&nbsp;</font></td>
      	<td  >      <font size="2" face="Arial">        <%= MyRec.fields("Vendor_nbr")%>&nbsp;</font></td>
      		<td  >      <font size="2" face="Arial">        <%= MyRec.fields("SAP_Vendor")%>&nbsp;</font></td>
      			<td  >      <font size="2" face="Arial">        <%= MyRec.fields("Generator")%>&nbsp;</font></td>
			<td  >        <font size="2" face="Arial">        <%= MyRec.fields("Price")%>&nbsp;</font></td>
			<td  >        <font size="2" face="Arial">        <%= MyRec.fields("Confirmed")%>&nbsp;</font></td>
						<% if strUsertype = "D" then %>
			<td>   <font size="2" face="Arial"> &nbsp;</font></td>
			<% else %>
	<td> <font size="2" face="Arial"><a href="Confirm.asp?id=<%= MyRec.fields("ID") %>&p=O&c=<%= MyRec.fields("Confirmed")%>">Confirm</a></td>	
	<% end if %></tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
     MyRec.close
    %>
</table>

<% End Sub %><!--#include file="AIGfooter.inc"-->