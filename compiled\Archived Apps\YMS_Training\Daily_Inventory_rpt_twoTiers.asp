
<html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">
<TITLE>Daily Inventory Report</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->
 
<!--#include file="classes/asp_cls_sessionPolaris.asp"-->
<!--#include file="classes/asp_cls_sessionOWB.asp"-->
	
	
	   <style type="text/css">
.style1 {
	font-family: Arial;
	font-size: x-small;
}
.style4 {
	border: 1px solid #F0F0F0;
}
.style14 {
	border: 1px solid #F0F0F0;
	text-align: center;
	background-color: #F3F3F3;
	font-size: xx-small;
}
.style15 {
	font-family: Arial;
}
.style16 {
	font-size: x-small;
}
.style17 {
	border: 1px solid #F0F0F0;
	text-align: center;
		font-family: Arial;
	background-color: #FFD7FF;
}
.style18 {
	border: 1px solid #000000;
}
.style21 {
	border: 1px solid #F0F0F0;
	font-family: Arial;
	font-size: x-small;
	text-align: center;
	background-color: #FFFFCC;
}
.style29 {
	border-left-color: #C0C0C0;
	border-left-width: 1px;
	border-right-color: #000080;
	border-right-width: 1px;
	border-top-color: #C0C0C0;
	border-top-width: 1px;
	border-bottom-color: #000080;
	border-bottom-width: 1px;
	background-color: #F0F0F0;
}
.style31 {
	border-width: 1px;
	background-color: #E8E8FF;
}
.style34 {
	border-color: #C0C0C0;
	border-width: 1px;
	text-align: center;
	font-family: Arial;
	font-size: x-small;
	background-color: #FAFBBF;
}
.style38 {
	border-color: #C0C0C0;
	border-width: 1px;
	text-align: center;
	font-family: Arial;
	font-size: x-small;
	background-color: #FFD7FF;
}
.style40 {
	border-left: 1px solid #C0C0C0;
	border-right: 1px solid #000080;
	border-top: 1px solid #C0C0C0;
	border-bottom: 1px solid #000080;
	background-color: #E8E8FF;
}
.style41 {
	border: 1px solid #808080;
	font-size: x-small;
}
.style44 {
	border: 1px solid #C0C0C0;
	text-align: center;
		font-family: Arial;
		font-size: x-small;
	background-color: #F3F3F3;
}
.style47 {
	border: 1px solid #F0F0F0;
	text-align: center;
	font-family: Arial;
	background-color: #F3F3F3;
	font-size: xx-small;
}
.style48 {
	border: 1px solid #F0F0F0;
	font-family: Arial;
	font-size: x-small;
	text-align: center;
	background-color: #FFD7FF;
}
.style50 {
	border: 1px solid #F0F0F0;
	background-color: #E8E8FF;
}
.style52 {
	border-left-color: #C0C0C0;
	border-left-width: 1px;
	border-right-color: #000080;
	border-right-width: 1px;
	border-top-color: #C0C0C0;
	border-top-width: 1px;
	border-bottom-color: #000080;
	border-bottom-width: 1px;
	background-color: #FFFFFF;
}
.style53 {
	border-left-color: #C0C0C0;
	border-left-width: 1px;
	border-right-color: #000080;
	border-right-width: 1px;
	border-top-color: #C0C0C0;
	border-top-width: 1px;
	border-bottom-color: #000080;
	border-bottom-width: 1px;
	background-color: #FFECFF;
}
.style54 {
	border-width: 1px;
	background-color: #FFECFF;
}
.style55 {
	border: 1px solid #FFFFFF;
	text-align: center;
	font-family: Arial;
	font-size: x-small;
	background-color: #FCFDDB;
}
.style56 {
	border-width: 1px;
	background-color: #FCFDDB;
	text-align: center;
}
.style62 {
	border-width: 1px;
	background-color: #FFECFF;
	font-size: x-small;
	text-align: center;
}
.style67 {
	border: 1px solid #FFFFFF;
	background-color: #E8E8FF;
}
.style70 {
	border: 1px solid #FFFFFF;
	font-size: x-small;
	background-color: #FAFBBF;
}
.style71 {
	border-style: solid;
	border-width: 1px;
}
.style72 {
	border: 1px solid #000000;
	font-size: x-small;
	background-color: #FFFFDD;
	text-align: center;
}
.style73 {
	border: 1px solid #808080;
	font-size: x-small;
	background-color: #FFFFDD;
	text-align: center;
}
.style77 {
	border-left: 1px solid #C0C0C0;
	border-right: 1px solid #808080;
	border-top: 1px solid #C0C0C0;
	border-bottom: 1px solid #808080;
	font-size: x-small;
	text-align: center;
}
.style80 {
	border: 1px solid #808080;
	background-color: #FFFFDD;
	font-size: x-small;
}
.style81 {
	color: black;
	font-size: 10.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: 1.0pt solid windowtext;
	border-bottom: .5pt solid windowtext;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #E8E8FF;
}
.style82 {
	color: black;
	font-size: 10.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right: 1.0pt solid windowtext;
	border-top: 1.0pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #E8E8FF;
}
.style83 {
	color: black;
	font-size: 10.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: .5pt solid windowtext;
	border-bottom: 1.0pt solid windowtext;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #FCFBF5;
}
.style84 {
	color: black;
	font-size: 10.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left-style: none;
	border-left-color: inherit;
	border-left-width: medium;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: .5pt solid windowtext;
	border-bottom: 1.0pt solid windowtext;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #FCFBF5;
}
.style85 {
	color: black;
	font-size: 10.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left-style: none;
	border-left-color: inherit;
	border-left-width: medium;
	border-right: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom: 1.0pt solid windowtext;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #FCFBF5;
}
.style86 {
	color: black;
	font-size: 10.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom: 1.0pt solid windowtext;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #FCFBF5;
}
.style87 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top-style: none;
	border-top-color: inherit;
	border-top-width: medium;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #00B0F0;
}
.style88 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-style: none;
	border-color: inherit;
	border-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #00B0F0;
}
.style89 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left-style: none;
	border-left-color: inherit;
	border-left-width: medium;
	border-right: 1.0pt solid windowtext;
	border-top-style: none;
	border-top-color: inherit;
	border-top-width: medium;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #00B0F0;
}
.style90 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right: 1.0pt solid windowtext;
	border-top-style: none;
	border-top-color: inherit;
	border-top-width: medium;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #00B0F0;
}
.style91 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #92D050;
}
.style92 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left-style: none;
	border-left-color: inherit;
	border-left-width: medium;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #92D050;
}
.style93 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left-style: none;
	border-left-color: inherit;
	border-left-width: medium;
	border-right: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #92D050;
}
.style94 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #92D050;
}
.style95 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: yellow;
}
.style96 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left-style: none;
	border-left-color: inherit;
	border-left-width: medium;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: yellow;
}
.style97 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left-style: none;
	border-left-color: inherit;
	border-left-width: medium;
	border-right: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: yellow;
}
.style98 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: yellow;
}
.style99 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: red;
}
.style100 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left-style: none;
	border-left-color: inherit;
	border-left-width: medium;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: red;
}
.style101 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left-style: none;
	border-left-color: inherit;
	border-left-width: medium;
	border-right: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: red;
}
.style102 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: red;
}
.style103 {
	font-size: xx-small;
}
.style104 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #E8E8FF;
	text-align: center;
	font-size: x-small;
	font-weight: bold;
}
.style105 {
	border: 1px solid #E1E1E1;
	font-size: x-small;
	text-align: center;
}
.style106 {
	border: 1px solid #E1E1E1;
	font-size: x-small;
	text-align: center;
	font-weight: bold;
}
.style107 {
	font-size: x-small;
	font-weight: bold;
}
.style108 {
	border: 1px solid #F0F0F0;
	font-family: Arial;
	font-size: x-small;
	text-align: center;
	background-color: #FFD7FF;
	font-weight: bold;
}
.style109 {
	border-width: 1px;
	background-color: #FCFDDB;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	text-align: center;
}
.style112 {
	border-width: 1px;
	background-color: #FCFDDB;
	font-size: x-small;
	text-align: center;
}
.style114 {
	border: 1px solid #F0F0F0;
	text-align: center;
	font-family: Arial;
	background-color: #F3F3F3;
	font-size: x-small;
	font-weight: bold;
}
.style115 {
	text-align: center;
}
.style116 {
	border: 1px solid #F0F0F0;
	font-family: Arial;
	background-color: #F3F3F3;
	font-size: xx-small;
}
.style117 {
	border-right: 1px solid #000000;
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: red;
}
.style118 {
	border-right: 1px solid #000000;
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: yellow;
}
.style119 {
	border-right: 1px solid #000000;
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #92D050;
}
.style120 {
	border-right: 1px solid #000000;
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-top-style: none;
	border-top-color: inherit;
	border-top-width: medium;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #00B0F0;
}
.style121 {
	border-right: 1px solid #000000;
	color: black;
	font-size: 10.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom: 1.0pt solid windowtext;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #FCFBF5;
}
.style122 {
	text-align: center;
	font-size: x-small;
}
.style123 {
	border: 1px solid #F0F0F0;
	text-align: center;
	background-color: #F3F3F3;
	font-size: xx-small;
	font-weight: bold;
}
.style124 {
	border: 1px solid #F0F0F0;
	text-align: center;
	font-family: Arial;
	background-color: #F3F3F3;
	font-size: xx-small;
	font-weight: bold;
}
.style125 {
	font-family: Arial;
	font-size: xx-small;
}
</style>
</head>
<%  
	Dim strsQL, rstDaily, strBegDate, strEndDate, strcount, objPro, strCdate, strKCOP, strOther, strOCC, strMonthName, strMonthDay, strDayofweek
	Dim strdate, objDAC, strOCCRail, strOtherRail, strKCOPRail, strOCCAvg,  strKCOPAvg, strnow, MyConn, strTday7
	Dim strdate1, strYdate
	Dim strOB, MyRec2, strsql2
	Dim KCOP_one, KCOP_Two, KCOP_Three, KCOP_Four, KCOP_Five, KCOP_Six, KCOP_Seven
	DIm OCC_one, OCC_Two, OCC_Three, OCC_Four, OCC_Five, OCC_six, OCC_Seven, MyRec8, strSQL8
	Dim strMXP, MXP_One, MXP_Two, MXP_three, MXP_Four, MXP_Five, MXP_Six, MXP_Seven, strMXPRail, strMXPAvg
	
	strnow = formatdatetime(Now(),2)
	strBegdate = dateadd("d", -7, strnow)
	strEnddate = formatdatetime(now())
	strTomorrow = dateadd("d", 1, strnow)
	
		
			strYdate = dateadd("d", -3, now())
		
 

	strsql = "Update tblCars set  tblCars.Tier_rating = [tblTier].[Tier] "_
&" FROM tblCars INNER JOIN tblTier ON (tblTier.Grade = tblCars.Species) AND (tblTier.State = tblCars.Gen_state) AND  "_
&" (tblTier.City = tblCars.Gen_City) AND (tblTier.Generator = tblCars.Generator) AND (tblTier.Vendor = tblCars.Vendor)  "_
  &" WHERE (((tblCars.Tier_rating) Is Null) AND ((tblTier.Tier) Is Not Null) AND DateDiff(d,[date_received],'" & strnow & "') < 20 AND ((tblTier.Grade)='KCOP' Or (tblTier.Grade)='OF'))"
	     	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
 
	%>

<p class="style1"><strong>KC MOBILE 
Daily RF REPORT<br><%= Now()%> 
</strong></p>
<font face="arial" size="1"><table class="style4" style="width: 70%">
<tr><td class="style80" rowspan="2">&nbsp;</td><td class="style80" rowspan="2"><strong>Previous Days Consumption for Reference</strong></td>
	<td class="style72" colspan="5"><font face="arial">KCOP</font><font face="arial" size="1">&nbsp;
	<font face="arial">&nbsp;</font> Trailers/Rail</td>
			<td class="style72" colspan="4">OF&nbsp;<font face="arial" size="1">&nbsp; 
			Trailers/Rail</td>

	<td class="style72">PMX <span class="style103">Trailers/Rail</span></td>

	<td class="style72">RCC</td>
	<td class="style72" colspan="2">OCC</td>
<td class="style72">USBS</td>
<td class="style72">OCC</td>
	</tr>
<tr><td class="style73">1A</td>
	<td class="style73">1B</td>
	<td class="style73">2</td>
	<td class="style73">T5</td>
	<td class="style73">NG</td>
	
			<td class="style73">&nbsp;1&nbsp; </td>

	 
			<td class="style73">&nbsp;2&nbsp;</td>

	 
	<td class="style73">&nbsp;3&nbsp;</td>
	<td class="style73">NG</td>
	<td class="style73">PMX</td>
	<td class="style73">&nbsp;</td>
	<td class="style73">OCC</td>

	<td class="style73">MXP</td>
<td class="style72">USBS</td>
<td class="style72">&nbsp;</td>
	</tr>
	<tr><td class="style41"></td>
  <td   class="style77">Last 30 Day Avg (In TL)</td>
  <% str30Date = dateadd("d", -31, date())
    strTodayDate = date()
    str07Date = dateadd("d", -7, date())

   
  
  strsql = "SELECT tblCars.Species, Count(tblCars.CID) AS CCID FROM tblCars "_
&" WHERE  Inv_depletion_date >'" & str30Date & "' and  Inv_depletion_date < '" & strTodayDate & "' "_
&" GROUP BY tblCars.Species ORDER BY tblCars.Species"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
While not MyRec.eof  
If MyRec("Species") = "OCC" then 
str30OCC = MyRec("CCID")
elseif MyRec("Species") = "ROCC" then
str30ROCC = MyRec("CCID")

elseif MyRec("Species") = "KCOP" then
str30KCOP = MyRec("CCID")
elseif MyRec("Species") = "MXP" then
str30MXP= MyRec("CCID")
elseif MyRec("Species") = "OF" then
str30OF = MyRec("CCID")
elseif MyRec("Species") = "PMX" then
str30PMX = MyRec("CCID")
elseif MyRec("Species") = "SWL" then
str30SWL = MyRec("CCID")
elseif MyRec("Species") = "USBS" then
str30USBS = MyRec("CCID")
end if
MyRec.movenext
wend
MyRec.close


  strsql = "SELECT tblCars.Species, Count(tblCars.CID) AS CCID FROM tblCars "_
&" WHERE  Inv_depletion_date >'" & str30Date & "' and  Inv_depletion_date < '" & strTodayDate & "' and carrier = 'RAIL' "_
&" GROUP BY tblCars.Species ORDER BY tblCars.Species"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
While not MyRec.eof  
If MyRec("Species") = "OCC" then 
strR30OCC = MyRec("CCID")
elseif MyRec("Species") = "ROCC" then
strR30ROCC = MyRec("CCID")

elseif MyRec("Species") = "KCOP" then
strR30KCOP = MyRec("CCID")
elseif MyRec("Species") = "MXP" then
strR30MXP= MyRec("CCID")
elseif MyRec("Species") = "OF" then
strR30OF = MyRec("CCID")
elseif MyRec("Species") = "PMX" then
strR30PMX = MyRec("CCID")
elseif MyRec("Species") = "SWL" then
strR30SWL = MyRec("CCID")
elseif MyRec("Species") = "USBS" then
strR30USBS = MyRec("CCID")
end if
MyRec.movenext
wend
MyRec.close

  strsql = "SELECT tblCars.Species, Count(tblCars.CID) AS CCID FROM tblCars "_
&" WHERE  Inv_depletion_date >'" & str07Date & "' and  Inv_depletion_date < '" & strTodayDate & "' "_
&" GROUP BY tblCars.Species ORDER BY tblCars.Species"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
While not MyRec.eof  
If MyRec("Species") = "OCC" then 
str07OCC = MyRec("CCID")
elseif MyRec("Species") = "ROCC" then
str07ROCC = MyRec("CCID")

elseif MyRec("Species") = "KCOP" then
str07KCOP = MyRec("CCID")
elseif MyRec("Species") = "MXP" then
str07MXP= MyRec("CCID")
elseif MyRec("Species") = "OF" then
str07OF = MyRec("CCID")
elseif MyRec("Species") = "PMX" then
str07PMX = MyRec("CCID")
elseif MyRec("Species") = "SWL" then
str07SWL = MyRec("CCID")
elseif MyRec("Species") = "USBS" then
str07USBS = MyRec("CCID")
end if
MyRec.movenext
wend
MyRec.close


  strsql = "SELECT tblCars.Species, Count(tblCars.CID) AS CCID FROM tblCars "_
&" WHERE  Inv_depletion_date >'" & str07Date & "' and  Inv_depletion_date < '" & strTodayDate & "' and carrier = 'RAIL' "_
&" GROUP BY tblCars.Species ORDER BY tblCars.Species"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
While not MyRec.eof  
If MyRec("Species") = "OCC" then 
strR07OCC = MyRec("CCID")
elseif MyRec("Species") = "ROCC" then
strR07ROCC = MyRec("CCID")

elseif MyRec("Species") = "KCOP" then
strR07KCOP = MyRec("CCID")
elseif MyRec("Species") = "MXP" then
strR07MXP= MyRec("CCID")
elseif MyRec("Species") = "OF" then
strR07OF = MyRec("CCID")
elseif MyRec("Species") = "PMX" then
strR07PMX = MyRec("CCID")
elseif MyRec("Species") = "SWL" then
strR07SWL = MyRec("CCID")
elseif MyRec("Species") = "USBS" then
strR07USBS = MyRec("CCID")
end if
MyRec.movenext
wend
MyRec.close

strsql = "SELECT  Species,  Tier_rating, Count(CID) AS CCID FROM tblCars "_
&" WHERE  Inv_depletion_date >'" & str30Date & "' and  Inv_depletion_date < '" & strTodayDate & "' "_
&" GROUP BY Species, Tier_rating HAVING  Species ='KCOP' Or Species='OF'"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
While not MyRec.eof  
If MyRec("Species") = "KCOP"  and MyRec("Tier_Rating") = "1A" then 
str30KCOP1A = MyRec("CCID")
elseif MyRec("Species") = "KCOP"  and MyRec("Tier_Rating") = "1B" then 
str30KCOP1B = MyRec("CCID")
elseif MyRec("Species") = "KCOP"  and MyRec("Tier_Rating") = "2" then 
str30KCOP2 = MyRec("CCID")
elseif MyRec("Species") = "KCOP"  and MyRec("Tier_Rating") = "T5" then 
str30KCOPT5 = MyRec("CCID")
elseif MyRec("Species") = "OF"  and MyRec("Tier_Rating") = "1" then 
str30OF1A = MyRec("CCID")
elseif MyRec("Species") = "OF"  and MyRec("Tier_Rating") = "2" then 
str30OF1B = MyRec("CCID")
elseif MyRec("Species") = "OF"  and MyRec("Tier_Rating") = "3" then 
str30OF3 = MyRec("CCID")
end if
MyRec.movenext
wend
MyRec.close


strsql = "SELECT  Species,  Tier_rating, Count(CID) AS CCID FROM tblCars "_
&" WHERE  Inv_depletion_date >'" & str30Date & "' and  Inv_depletion_date < '" & strTodayDate & "'  and carrier = 'RAIL'"_
&" GROUP BY Species, Tier_rating HAVING  Species ='KCOP' Or Species='OF'"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
While not MyRec.eof  
If MyRec("Species") = "KCOP"  and MyRec("Tier_Rating") = "1A" then 
strR30KCOP1A = MyRec("CCID")
elseif MyRec("Species") = "KCOP"  and MyRec("Tier_Rating") = "1B" then 
strR30KCOP1B = MyRec("CCID")
elseif MyRec("Species") = "KCOP"  and MyRec("Tier_Rating") = "2" then
strR30KCOP2 = MyRec("CCID")
elseif MyRec("Species") = "KCOP"  and MyRec("Tier_Rating") = "T5" then
strR30KCOPT5 = MyRec("CCID")
elseif MyRec("Species") = "OF"  and MyRec("Tier_Rating") = "1" then 
strR30OF1A = MyRec("CCID")
elseif MyRec("Species") = "OF"  and MyRec("Tier_Rating") = "2" then  
strR30OF1B = MyRec("CCID")
elseif MyRec("Species") = "OF"  and MyRec("Tier_Rating") = "3" then 
strR30OF3 = MyRec("CCID")
end if
MyRec.movenext
wend
MyRec.close


strsql = "SELECT  Species,  Tier_rating, Count(CID) AS CCID FROM tblCars "_
&" WHERE  Inv_depletion_date >'" & str07Date & "' and  Inv_depletion_date < '" & strTodayDate & "' "_
&" GROUP BY Species, Tier_rating HAVING  Species ='KCOP' Or Species='OF'"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
While not MyRec.eof  
If MyRec("Species") = "KCOP"  and MyRec("Tier_Rating") = "1A" then 
str07KCOP1A = MyRec("CCID")
elseif MyRec("Species") = "KCOP"  and MyRec("Tier_Rating") = "1B" then 
str07KCOP1B = MyRec("CCID")
elseif MyRec("Species") = "KCOP"  and MyRec("Tier_Rating") = "2" then
str07KCOP2 = MyRec("CCID")
elseif MyRec("Species") = "KCOP"  and MyRec("Tier_Rating") = "T5" then 
str07KCOPT5 = MyRec("CCID")
elseif MyRec("Species") = "OF"  and MyRec("Tier_Rating") = "1" then 
str07OF1A = MyRec("CCID")
elseif MyRec("Species") = "OF"  and MyRec("Tier_Rating") = "2" then 
str07OF1B = MyRec("CCID")
elseif MyRec("Species") = "OF"  and MyRec("Tier_Rating") = "3" then 
str07OF3 = MyRec("CCID")
end if
MyRec.movenext
wend
MyRec.close

strsql = "SELECT  Species,  Tier_rating, Count(CID) AS CCID FROM tblCars "_
&" WHERE  Inv_depletion_date >'" & str07Date & "' and  Inv_depletion_date < '" & strTodayDate & "' AND CARRIER = 'RAIL' "_
&" GROUP BY Species, Tier_rating HAVING  Species ='KCOP' Or Species='OF'"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
While not MyRec.eof  
If MyRec("Species") = "KCOP"  and MyRec("Tier_Rating") = "1A" then  
strR07KCOP1A = MyRec("CCID")
elseif MyRec("Species") = "KCOP"  and MyRec("Tier_Rating") = "1B" then 
strR07KCOP1B = MyRec("CCID")
elseif MyRec("Species") = "KCOP"  and MyRec("Tier_Rating") = "2" then
strR07KCOP2 = MyRec("CCID")
elseif MyRec("Species") = "KCOP"  and MyRec("Tier_Rating") = "T5" then 
strR07KCOPT5 = MyRec("CCID")
elseif MyRec("Species") = "OF"  and MyRec("Tier_Rating") = "1" then 
strR07OF1A = MyRec("CCID")
elseif MyRec("Species") = "OF"  and MyRec("Tier_Rating") = "2" then  
strR07OF1B = MyRec("CCID")
elseif MyRec("Species") = "OF"  and MyRec("Tier_Rating") = "3" then 
strR07OF3 = MyRec("CCID")
end if
MyRec.movenext
wend
MyRec.close






%>

<td class="style77"> <%= round((str30KCOP1A + (strR30KCOP1A * 2))/30,1) %> &nbsp;   </td>
<td class="style77"> <%= round((str30KCOP1B + (strR30KCOP1B * 2))/30,1) %> &nbsp;   </td>
<td class="style77"> <%= round((str30KCOP2 + (strR30KCOP2 * 2))/30,1) %> &nbsp;   </td>
<td class="style77"> <%= round((str30KCOPT5 + (strR30KCOPT5 * 2))/30,1) %> &nbsp;   </td> 
<% strKCOPRail = strR30KCOP - strR30KCOP1A - strR30KCOP1B - strR30KCOP2 - strR30KCOPT5
strKCOPTrailer = str30KCOP - str30KCOP1A - str30KCOP1B - str30KCOP2 - str30KCOPT5  %>
 <td class="style77"><%=  round((strKCOPTrailer + strKCOPRail)/30,1) %> &nbsp;	</td>

<td class="style77"> <%= round((str30OF1A + (strR30OF1A * 2))/30,1) %> &nbsp;   </td>
<td class="style77"> <%= round((str30OF1B + (strR30OF1B * 2))/30,1) %> &nbsp;   </td>
<td class="style77"> <%= round((str30OF3 + (strR30OF3 * 2))/30,1) %> &nbsp;   </td>
<% strOFRail = strR30OF - strR30OF1A - strR30OF1B - strR30OF3  
strOFTrailer = str30OF - str30OF1A - str30OF1B - str30OF3  %>
 <td class="style77"><%=  round((strOFTrailer + strOFRail)/30,1) %> &nbsp;	</td>

<td class="style77">
<% if ((strR30PMX * 2) + str30PMx) > 0 then %>
<%= round(((strR30PMX * 2) + str30PMx)/30,1) %>
<% end if %> &nbsp; </td>


<td class="style77"><%= round((((strR30KCOP + strR30OF + strR30PMX) * 2) + str30KCOP + str30OF + str30PMX )/30,1) %>
&nbsp;</td>
<td class="style77"> 
<% if (((strR30OCC + strR30ROCC) * 2) + str30OCC + str30ROCC) > 0 then %>
<%= round((((strR30OCC + strR30ROCC) * 2) + str30OCC + str30ROCC)/30,1) %>
<% end if %> &nbsp;</td>
 
<td class="style77">
<% if ((strR30MXP * 2) + str30MXP) > 0 then %>
<%= round(((strR30MXP * 2) + str30MXP)/30,1) %>
<% end if %> &nbsp;</td>

<td class="style77">
<% if ((strR30USBS * 2) + str30USBS) > 0 then %>
<%= round(((strR30USBS * 2) + str30USBS)/30,1) %>
<% end if %> &nbsp;</td>


<td class="style77"><%= round((((strR30USBS + strR30MXP + strR30OCC + strR30ROCC) * 2) + str30USBS + str30MXP + str30OCC + str30ROCC)/30,1) %>
&nbsp;</td>

 


</tr>
<tr><td class="style41"></td>
  <td   class="style77">Last 7 Day Avg (In TL)</td>
<td class="style77"> <%= round((str07KCOP1A + (strR07KCOP1A * 2))/7,1) %> &nbsp;   </td>
<td class="style77"> <%= round((str07KCOP1B + (strR07KCOP1B * 2))/7,1) %> &nbsp;   </td>
<td class="style77"> <%= round((str07KCOP2 + (strR07KCOP2 * 2))/7,1) %> &nbsp;   </td>
<td class="style77"> <%= round((str07KCOPT5 + (strR07KCOPT5 * 2))/7,1) %> &nbsp;   </td> 
<% strKCOPRail = strR07KCOP - strR07KCOP1A - strR07KCOP1B - strR07KCOP2 - strR07KCOPT5
strKCOPTrailer = str07KCOP - str07KCOP1A - str07KCOP1B - str07KCOP2 - str07KCOPT5  %>
 <td class="style77"><%=  round((strKCOPTrailer + (strKCOPRail * 2))/7,1) %> &nbsp;	</td>

<td class="style77"> <%= round((str07OF1A + (strR07OF1A * 2))/7,1) %> &nbsp;   </td>
<td class="style77"> <%= round((str07OF1B + (strR07OF1B * 2))/7,1) %> &nbsp;   </td>
<td class="style77"> <%= round((str07OF3 + (strR07OF3 * 2))/7,1) %> &nbsp;   </td>
<% strOFRail = strR07OF - strR07OF1A - strR07OF1B - strR07OF3  
strOFTrailer = str07OF - str07OF1A - str07OF1B - str07OF3  %>
 <td class="style77"><%=  round((strOFTrailer + (strOFRail * 2))/7,1) %> &nbsp; 	</td>
 
 
<td class="style77">
<% if ((strR07PMX * 2) + str07PMx) > 0 then %>
<%= round(((strR07PMX * 2) + str07PMx)/07,1) %>
<% end if %> &nbsp; </td>


<td class="style77">
<% sTrailer = str07KCOP + str07OF + str07PMX
sRail = (strR07KCOP + strR07OF + strR07PMX) * 2 %>

<%= round( (sTrailer + sRail)/7,1) %>
&nbsp; </td>
<td class="style77"> 
<% if (((strR07OCC + strR07ROCC) * 2) + str07OCC + str07ROCC) > 0 then %>
<%= round((((strR07OCC + strR07ROCC) * 2) + str07OCC + str07ROCC)/07,1) %>
<% end if %> &nbsp;</td>
 
<td class="style77">
<% if ((strR07MXP * 2) + str07MXP) > 0 then %>
<%= round(((strR07MXP * 2) + str07MXP)/07,1) %>
<% end if %> &nbsp;</td>

<td class="style77">
<% if ((strR07USBS * 2) + str07USBS) > 0 then %>
<%= round(((strR07USBS * 2) + str07USBS)/07,1) %>
<% end if %> &nbsp;</td>


<td class="style77"><%= round((((strR07USBS + strR07MXP + strR07OCC + strR07ROCC) * 2) + str07USBS + str07MXP + str07OCC + str07ROCC)/07,1) %>
&nbsp;</td>
 


</tr>

<%   	strnow = formatdatetime(Now(),2)
	strBegdate7 = dateadd("d", -7, strnow)
	strBegDate6 = dateadd("d", -6, strnow)
	strBegDate5 = dateadd("d", -5, strnow)
	strBegDate4 = dateadd("d", -4, strnow)
	strBegDate3 = dateadd("d", -3, strnow)
	strBegDate2 = dateadd("d", -2, strnow)
	strBegDate1 = dateadd("d", -1, strnow)

	
	strEnddate = formatdatetime(now())
	
	Function getweekday(strD)

 	if weekday(strD)  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif  weekday(strD)   = 2 then
 	 strDayofweek =  "Monday" 
 	elseif   weekday(strD)   = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif  weekday(strD)   = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif   weekday(strD)  = 5 then
 	 strDayofweek =  "Thursday"
 	elseif   weekday(strD)   = 6 then
 	 strDayofweek =  "Friday"
 	elseif  weekday(strD)  = 7 then
 	 strDayofweek =  "Saturday"
 	end if
 	
 	end Function
 	
 	call  getweekday(strBegdate7)
  
 	 call getrow( strBegDate7, strBegDate6)
 	 	call  getweekday(strBegdate6)
 	 call getrow(strBegDate6, strBegDate5)
 	 	call  getweekday(strBegdate5)
 	 call getrow(strBegDate5, strBegDate4)
 	 	call  getweekday(strBegdate4)
 	 call getrow(strBegDate4, strBegDate3)
 	 	call  getweekday(strBegdate3)
 	 call getrow(strBegDate3, strBegDate2)
 	 	call  getweekday(strBegdate2)
 	 call getrow(strBegDate2, strBegDate1)
 	 	call  getweekday(strBegdate1)
 	 call getrow(strBegDate1, strnow)
 	 	call  getweekday(strnow)
 	 call getrow(strnow, strTomorrow)
 	 
 	Function  getRow( strDA, strDA1)
 	
%>
<tr><td class="style41"><font face="arial"><%= strDA %></td>
  <% If datepart("d", strDA) = datepart("d", strnow) then %>
  <td   class="style77"><font face="arial">Consumed since midnight TODAY </td>
  <% else %>
<td class="style77"><font face="arial"><%= strDayofWeek %>  </td>
<% end if %>
 <td class="style77">
<% strCount1A = 0
strcount1AR = 0
strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'KCOP' and Tier_rating = '1A' and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and (carrier <> 'RAIL' or carrier is null)"
 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then 
strcount1A = MyRec("CountofCID")
end if

strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'KCOP' and Tier_rating = '1A' and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and carrier = 'RAIL'"
 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then 
strcount1AR =   MyRec("CountofCID")  
end if
%>

<% if strCOunt1A = 0 then %>
&nbsp;
<% else %> 
<%= strCount1A %>  
<% end if %>
<% if strcount1AR > 0 then %>
/  <%= strCount1AR %>
<% end if %>

</td>
 <td class="style77">
<% strCount1B = 0
strCount1BR = 0
strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'KCOP' and Tier_rating = '1B' and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and (carrier <> 'RAIL' or carrier is null)"
 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
strCount1B = MyREc("CountofCID")
end if 
strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'KCOP' and Tier_rating = '1B' and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and carrier = 'RAIL'"
 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
strCount1BR =  MyREc("CountofCID") 
end if 


%>
<% if strCount1B = 0 then %>
&nbsp;
<% else %>
<%= strCount1B %>  
<% end if %>
<% if strCount1BR > 0 then %>
/  <%= strCount1BR %>
<% end if %></td>

 <td class="style77">
<% strCount2 = 0
strCOunt2R = 0

strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'KCOP' and Tier_rating = '2' and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and (carrier <> 'RAIL' or carrier is null)"
 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
strCount2 = MyREc("CountofCID")
end if

strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'KCOP' and Tier_rating = '2' and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and carrier = 'RAIL'"
 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
strCount2R =  MyREc("CountofCID") 
end if

 %>
<% if strCOunt2 = 0 then %>
&nbsp;
<% else %>
<%= strCount2 %>  
<% end if %>
<% if strCount2R > 0 then %>
/  <%= strCount2R %>
<% end if %></td>
 

 <td class="style77">
<% strCountT5 = 0
strCountT5R = 0

strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'KCOP' and Tier_rating = 'T5' and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "'and (carrier <> 'RAIL' or carrier is null)"
 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
strCountT5 = MyREc("CountofCID")
end if

 strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'KCOP' and Tier_rating = 'T5' and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and carrier ='RAIL'"

   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then 
sstrCountT5R =  MyREc("CountofCID")  
end if

%>
<% if strCOuntT5 = 0 then %>
&nbsp;
<% else %>
<%= strCountT5 %>  
<% end if %>
<% if strCountT5R > 0 then %>
/  <%= strCountT5R %>
<% end if %></td>

 <td class="style77">
<% strCountNG = 0
strCountNGR = 0

strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'KCOP' and (Tier_rating is null or tier_rating = '') and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and (carrier <> 'RAIL' or carrier is null)"
 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
strCountNG = MyREc("CountofCID")
end if

 strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'KCOP' and (Tier_rating is null or tier_rating = '') and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and carrier ='RAIL'"

   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then 
strCountNGR =  MyREc("CountofCID")  
end if

%>
<% if strCOuntNG = 0 then %>
&nbsp;
<% else %>
<%= strCountNG %>  
<% end if %>
<% if strCountNGR> 0 then %>
/  <%= strCountNGR %>
<% end if %></td>


 

 <td class="style77">

<% strCountOF1A = 0
strCountOF1AR = 0

strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'OF' and Tier_rating = '1' and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and (carrier <> 'RAIL' or carrier is null)"
 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
strCountOF1A = MyREc("CountofCID")
end if

 strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'OF' and (Tier_rating = '1' ) and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and carrier ='RAIL'"

   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then 
strCountOF1AR =  MyREc("CountofCID")  
end if

%>
<% if strCOuntOF1A = 0 then %>
&nbsp;
<% else %>
<%= strCountOF1A %>  
<% end if %>
<% if strCountOF1AR > 0 then %>
/  <%= strCountOF1AR %>
<% end if %></td>
 <td class="style77">

<% strCountOF1B = 0
strCountOF1BR = 0

strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'OF' and (  Tier_rating = '2') and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and (carrier <> 'RAIL' or carrier is null)"
 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
strCountOF1B = MyREc("CountofCID")
end if

 strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'OF' and ( Tier_rating = '2') and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and carrier ='RAIL'"

   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then 
strCountOF1BR =  MyREc("CountofCID")  
end if

%>
<% if strCOuntOF1B = 0 then %>
&nbsp;
<% else %>
<%= strCountOF1B %>  
<% end if %>
<% if strCountOF1BR > 0 then %>
/  <%= strCountOF1BR %>
<% end if %></td>

<td class="style77">
<% strCountOF3 = 0
 strCountOF3R = 0

strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'OF' and Tier_rating = '3' and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and (carrier <> 'RAIL' or carrier is null)"
 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
strCountOF3 = MyREc("CountofCID")
end if

 strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'OF' and Tier_rating = '3' and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and carrier ='RAIL'"

   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then 
 strCountOF3R =  MyREc("CountofCID")  
end if

%>
<% if strCOuntOF3 = 0 then %>
&nbsp;
<% else %>
<%= strCountOF3 %>  
<% end if %>
<% if strCountOF3R > 0 then %>
/  <%= strCountOF3R %>
<% end if %></td>

<td class="style77">
<% strCountOFNG = 0
strCountOFNGR = 0

strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'OF' and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and (carrier <> 'RAIL' or carrier is null)"
 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
strCountOFNG= MyREc("CountofCID")
end if

 strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'OF'   and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and carrier ='RAIL'"

   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then 
strCountOFNGR =  MyREc("CountofCID")  
end if

%>
<% if (strCountOFNG - strCountOF1A - strCountOF1B - strCountOF3) > 0 then %>
<%= strCountOFNG - strCountOF1A - strCountOF1B - strCountOF3 %>  
<% else %>
&nbsp;
<% end if %>
<% if  (strCountOFNGR - strCountOF1AR - strCountOF1BR - strCountOF3R) > 0 then %>
/  <%= strCountOFNGR - strCountOF1AR - strCountOF1BR - strCountOF3R %>
<% end if %>  </td>




<td class="style77">
<% strCountPMX = 0
strCountPMXR = 0

strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'PMX'  and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and (carrier <> 'RAIL' or carrier is null)"
 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
strCountPMX = MyREc("CountofCID")
end if

 strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'PMX'  and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and carrier ='RAIL'"

   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then 
strCountPMXR =  MyREc("CountofCID")  
end if

%>
<% if strCOuntPMX = 0 then %>
&nbsp;
<% else %>
<%= strCountPMX %>  
<% end if %>
<% if strCountPMXR > 0 then %>
/  <%= strCountPMXR %>
<% end if %></td>
<% cKCOPTrailer = strcount1A + strCount1B + strCount2 + strcountT5 + strCountOFNG  + strCOuntPMX + strCountNG
cKCOPRail =  3 * (strcount1AR + strCount1BR + strCount2R + strcountT5R + strCountNGR +   strCountOFNGR + strCOuntPMXR) %>
<td class="style77"><%=  cKCOPTrailer + cKCOPRail  %>
&nbsp;  </td>


<td class="style77">
<% strCountOCC = 0
 

strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where (Species = 'OCC' or Species = 'ROCC') and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "'  "
 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
strCountOCC= MyREc("CountofCID")
end if


%>
<% if strCOuntOCC = 0 then %>
&nbsp;
<% else %>
<%= strCountOCC %>  
<% end if %>
&nbsp;</td>

<td class="style77">
<% strCountMXP = 0
 

strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'MXP'   and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "'  "
 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
strCountMXP= MyREc("CountofCID")
end if


%>
<% if strCOuntMXP = 0 then %>
&nbsp;
<% else %>
<%= strCountMXP %>  
<% end if %>
&nbsp;</td>


<td class="style77">
<% strCountUSBS = 0
 

strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'USBS'   and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "'  "
 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
strCountUSBS= MyREc("CountofCID")
end if


%>
<% if strCOuntUSBS = 0 then %>
&nbsp;
<% else %>
<%= strCountUSBS %>  
<% end if %>
&nbsp;</td>

<td class="style77"><%= strCountOCC + strCountMXP + strCountUSBS %>
&nbsp;</td>


</tr>
<% end function %>
 


</tr>
</table><p class="style1"><strong>

 <%   Dim strKCOP1, strPMX1, strOCC1, strKCOP2, strPMX2, strOCC2, strKCOP3, strPMX3, strOCC3, strKCOP4, strPMX4, strOCC4, strsql9
  Dim strKCOP5, strPMX5, strOCC5, strKCOP6, strPMX6, strOCC6, strKCOP7, strPMX7, strOCC7, strTday, strTD, strTM, strYear
  Dim strTD1, strTM1, strTday1
  strTday = formatdatetime(now(),2)
  strYear = datepart("yyyy", strTday)
  strTD = datepart("d", strTday)
  strTM = datepart("m", strTDay)
  strTday1 = dateadd("d", 1, strTday)
    strTD1 = datepart("d", strTday1)
  strTM1 = datepart("m", strTDay1)
  strTday2 = dateadd("d", 2, strTday)
    strTD2 = datepart("d", strTday2)
  strTM2 = datepart("m", strTDay2)
  strTday3 = dateadd("d", 3, strTday)
    strTD3 = datepart("d", strTday3)
  strTM3 = datepart("m", strTDay3)
  
    strTday4 = dateadd("d", 4, strTday)
    strTD4 = datepart("d", strTday4)
  strTM4 = datepart("m", strTDay4)


  strTday5 = dateadd("d", 5, strTday)
    strTD5 = datepart("d", strTday5)
  strTM5 = datepart("m", strTDay5)


  strTday6 = dateadd("d", 6, strTday)
    strTD6 = datepart("d", strTday6)
  strTM6 = datepart("m", strTDay6)
  
      strsql = "Select Total_OCC from tblTempYardTotals where INV_Date = '" & strTday6 & "'"
     Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
if not MyRec.eof then
' do nothing
else

  	
  		strsql8 = "Select tblTempYardTotals.* from tblTempYardTotals where ID = 16 "
  	    Set MyRec8 = Server.CreateObject("ADODB.Recordset")
   MyRec8.Open strSQL8, Session("ConnectionString") 
   RFDefault = MyRec8.fields("Total_RF")
   OCCDefault = MyRec8.fields("Total_OCC")
   OFDefault = MyRec8.fields("Yard_KCOP")
   MXPDefault = MyRec8.fields("Total_MXP")
   
   KCOPT1Default = MYRec8.fields("Kcop_Tier1")
   KCOPT2Default = MYRec8.fields("Kcop_Tier2")
   KCOPT3Default = MYRec8.fields("Kcop_Tier3")
   KCOPT4Default = MYRec8.fields("Kcop_Tier4")
   OFT1Default = MyRec8.fields("OF_Tier1")
   OFT2Default = MyRec8.fields("OF_Tier2")
	OFT3Default = MyRec8.fields("OF_Tier3")
	PMXDefault = MyRec8.fields("PMX")

   KCOPT1Default = MYRec8.fields("Kcop_Tier1")
   KCOPT2Default = MYRec8.fields("Kcop_Tier2")
   KCOPT3Default = MYRec8.fields("Kcop_Tier3")
   KCOPT4Default = MYRec8.fields("Kcop_Tier4")
   OFT1Default = MyRec8.fields("OF_Tier1")
   OFT2Default = MyRec8.fields("OF_Tier2")
	OFT3Default = MyRec8.fields("OF_Tier3")
	PMXDefault = MyRec8.fields("PMX")

   
   MyRec8.close



  	
	strsql9 =  "INSERT INTO tblTempYardTotals ( INV_Date, Total_RF, Yard_KCOP, total_OCC, total_MXP, KCOP_Tier1, KCOP_Tier2, KCOP_Tier3, KCOP_Tier4, OF_Tier1, OF_Tier2, OF_Tier3, PMX) "_
	&" SELECT '" & strTday6 & "', " & RFDefault & ", " & OFDefault & ",  " & OCCDefault & ", " & MXPDefault & ", " & KCOPT1Default & ", " & KCOPT2Default & ", " & KcopT3Default & ", " & KCOPT4Default & ", "_
	&" " & OFT1Default & ", " & OFT2Default & ",  " & OFT3Default & ", " & PMXDefault & ""
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL9
			MyConn.Close

  
end if
MyRec.close


  
  strTday7 = dateadd("d", 7, strTday )
  


  
  strsql = "Select Total_OCC from tblTempYardTotals where INV_Date = '" & strTday7 & "'"
     Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
if not MyRec.eof then
' do nothing
else

  	
	strsql8 = "Select tblTempYardTotals.* from tblTempYardTotals where ID = 16 "
  	    Set MyRec8 = Server.CreateObject("ADODB.Recordset")
   MyRec8.Open strSQL8, Session("ConnectionString") 
   RFDefault = MyRec8.fields("Total_RF")
   OCCDefault = MyRec8.fields("Total_OCC")
   OFDefault = MyRec8.fields("Yard_KCOP")
   MXPDefault = MyRec8.fields("Total_MXP")
   KCOPT1Default = MYRec8.fields("Kcop_Tier1")
   KCOPT2Default = MYRec8.fields("Kcop_Tier2")
   KCOPT3Default = MYRec8.fields("Kcop_Tier3")
   KCOPT4Default = MYRec8.fields("Kcop_Tier4")
   OFT1Default = MyRec8.fields("OF_Tier1")
   OFT2Default = MyRec8.fields("OF_Tier2")
	OFT3Default = MyRec8.fields("OF_Tier3")
	PMXDefault = MyRec8.fields("PMX")

   
   MyRec8.close



  	
	strsql9 =  "INSERT INTO tblTempYardTotals ( INV_Date, Total_RF, Yard_KCOP, total_OCC, total_MXP, KCOP_Tier1, KCOP_Tier2, KCOP_Tier3, KCOP_Tier4, OF_Tier1, OF_Tier2, OF_Tier3, PMX) "_
	&" SELECT '" & strTday7 & "', " & RFDefault & ", " & OFDefault & ",  " & OCCDefault & ", " & MXPDefault & ", " & KCOPT1Default & ", " & KCOPT2Default & ", " & KcopT3Default & ", " & KCOPT4Default & ", "_
	&" " & OFT1Default & ", " & OFT2Default & ",  " & OFT3Default & ", " & PMXDefault & ""
	
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL9
			MyConn.Close

  
end if
MyRec.close
   
     strTday7 = dateadd("d", 8, strTday )
  

  
  strsql = "Select Total_OCC from tblTempYardTotals where INV_Date = '" & strTday7 & "'"
     Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
if not MyRec.eof then
' do nothing
else

  	
	strsql8 = "Select tblTempYardTotals.* from tblTempYardTotals where ID = 16 "
  	    Set MyRec8 = Server.CreateObject("ADODB.Recordset")
   MyRec8.Open strSQL8, Session("ConnectionString") 
   RFDefault = MyRec8.fields("Total_RF")
   OCCDefault = MyRec8.fields("Total_OCC")
     OFDefault = MyRec8.fields("Yard_KCOP")
     MXPDefault = MyRec8.fields("Total_MXP")
     KCOPT1Default = MYRec8.fields("Kcop_Tier1")
   KCOPT2Default = MYRec8.fields("Kcop_Tier2")
   KCOPT3Default = MYRec8.fields("Kcop_Tier3")
   KCOPT4Default = MYRec8.fields("Kcop_Tier4")
   OFT1Default = MyRec8.fields("OF_Tier1")
   OFT2Default = MyRec8.fields("OF_Tier2")
	OFT3Default = MyRec8.fields("OF_Tier3")
	PMXDefault = MyRec8.fields("PMX")

   
   MyRec8.close



  	
	strsql9 =  "INSERT INTO tblTempYardTotals ( INV_Date, Total_RF, Yard_KCOP, total_OCC, total_MXP, KCOP_Tier1, KCOP_Tier2, KCOP_Tier3, KCOP_Tier4, OF_Tier1, OF_Tier2, OF_Tier3, PMX) "_
	&" SELECT '" & strTday7 & "', " & RFDefault & ", " & OFDefault & ",  " & OCCDefault & ", " & MXPDefault & ", " & KCOPT1Default & ", " & KCOPT2Default & ", " & KcopT3Default & ", " & KCOPT4Default & ", "_
	&" " & OFT1Default & ", " & OFT2Default & ",  " & OFT3Default & ", " & PMXDefault & ""
	
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL9
			MyConn.Close

  
end if
MyRec.close
   
   
     strTday7 = dateadd("d", 9, strTday )
  

  
  strsql = "Select Total_OCC from tblTempYardTotals where INV_Date = '" & strTday7 & "'"
     Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
if not MyRec.eof then
' do nothing
else

  	
  	strsql8 = "Select tblTempYardTotals.* from tblTempYardTotals where ID = 16 "
  	    Set MyRec8 = Server.CreateObject("ADODB.Recordset")
   MyRec8.Open strSQL8, Session("ConnectionString") 
   RFDefault = MyRec8.fields("Total_RF")
   OCCDefault = MyRec8.fields("Total_OCC")
    OFDefault = MyRec8.fields("Yard_KCOP")
         MXPDefault = MyRec8.fields("Total_MXP")
     KCOPT1Default = MYRec8.fields("Kcop_Tier1")
   KCOPT2Default = MYRec8.fields("Kcop_Tier2")
   KCOPT3Default = MYRec8.fields("Kcop_Tier3")
   KCOPT4Default = MYRec8.fields("Kcop_Tier4")
   OFT1Default = MyRec8.fields("OF_Tier1")
   OFT2Default = MyRec8.fields("OF_Tier2")
	OFT3Default = MyRec8.fields("OF_Tier3")
	PMXDefault = MyRec8.fields("PMX")

   
   MyRec8.close



  	
	strsql9 =  "INSERT INTO tblTempYardTotals ( INV_Date, Total_RF, Yard_KCOP, total_OCC, total_MXP, KCOP_Tier1, KCOP_Tier2, KCOP_Tier3, KCOP_Tier4, OF_Tier1, OF_Tier2, OF_Tier3, PMX) "_
	&" SELECT '" & strTday7 & "', " & RFDefault & ", " & OFDefault & ",  " & OCCDefault & ", " & MXPDefault & ", " & KCOPT1Default & ", " & KCOPT2Default & ", " & KcopT3Default & ", " & KCOPT4Default & ", "_
	&" " & OFT1Default & ", " & OFT2Default & ",  " & OFT3Default & ", " & PMXDefault & ""
	
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL9
			MyConn.Close

  
end if
MyRec.close
   
strOCC3 = 0
strKCOP3 = 0
strOCC4 = 0
strKCOP4 = 0
strOCC5 = 0
strKCOP5 = 0
strOCC6 = 0
strKCOP6 = 0
strOCC7 = 0
strKCOP7 = 0

strOF3 = 0
strOF4 = 0
strOF5 = 0
strOF6 = 0
strOF7 = 0

	
 		strCountK1A3 = 0
 		strCountK1B3 = 0
 		strCountK23 = 0
 		strCountKT53 = 0
 		strCountKNG3 = 0
 		strCountOF1A3 = 0
 		strCountOFB3 = 0
 		strCountOF33 = 0
 		strCountOFNG3 = 0
 		
 		strCountK1A4 = 0
 		strCountK1B4 = 0
 		strCountK24 = 0
 		strCountKT54 = 0
 		strCountKNG4 = 0
 		strCountOF1A4 = 0
 		strCountOFB4 = 0
 		strCountOF34 = 0
 		strCountOFNG4 = 0
 		
 		strCountK1A5 = 0
 		strCountK1B5 = 0
 		strCountK25 = 0
 		strCountKT55 = 0
 		strCountKNG5 = 0
 		strCountOF1A5 = 0
 		strCountOFB5 = 0
 		strCountOF35 = 0
 		strCountOFNG5 = 0
 		
 		strCountK1A6 = 0
 		strCountK1B6 = 0
 		strCountK26 = 0
 		strCountKT56 = 0
 		strCountKNG6 = 0
 		strCountOF1A6 = 0
 		strCountOFB6 = 0
 		strCountOF36 = 0
 		strCountOFNG6 = 0
 		
 		strCountK1A7 = 0
 		strCountK1B7 = 0
 		strCountK27 = 0
 		strCountKT57 = 0
 		strCountKNG7 = 0
 		strCountOF1A7 = 0
 		strCountOFB7 = 0
 		strCountOF37 = 0
 		strCountOFNG7 = 0





   
        strsql = "SELECT  count(CID) as Countoford_id, left(release,1) as Species,  dateadd(dd, datediff(dd,0,Date_to),0) as Delivery_date "_
  			&" FROM tblInbound "_
			&" WHERE Destination_City ='MOBILE' and Release <> '0' "_								
			&"GROUP BY dateadd(dd, datediff(dd,0,Date_to),0), left(Release,1) "_			
			&" HAVING '" & strdate & "' <= dateadd(dd, datediff(dd,0,Date_to),0) "_
			&" ORDER BY dateadd(dd, datediff(dd,0,Date_to),0), left(Release,1)"



   		Set MyRec = Server.CreateObject("ADODB.Recordset")
   		 MyRec.Open strSQL, Session("ConnectionString") 


While not MyRec.eof

If strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2 and (MyRec.fields("Species") = "P" or MyRec.fields("Species") = "p" or MyRec.fields("Species") = "s" or  MyRec.fields("Species") = "S") then   
strPMX3 = MyRec.fields("countoford_id") + strPMX3
elseif strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "C"  or MyRec.fields("Species") = "c" or MyRec.fields("Species") = "r" or MyRec.fields("Species") = "R"  ) then 
strOCC3 = strOCC3 + MyRec.fields("countoford_id")
elseif strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "M"  or MyRec.fields("Species") = "m" ) then 
strMXP3 = strMXP3 + MyRec.fields("countoford_id")

elseIf strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3 and  (MyRec.fields("Species") = "P" or MyRec.fields("Species") = "p" or MyRec.fields("Species") = "s" or  MyRec.fields("Species") = "S") then 
strPMX4 = MyRec.fields("countoford_id") + strPMX4
elseif strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3  and (MyRec.fields("Species") = "C"  or MyRec.fields("Species") = "c" or myRec.fields("Species") = "R" or myRec.fields("Species")="r" ) then 
strOCC4 = strOCC4 + MyRec.fields("countoford_id")
elseif strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3  and (MyRec.fields("Species") = "M"  or MyRec.fields("Species") = "m" ) then 
strMXP4 = strMXP4 + MyRec.fields("countoford_id")


elseIf strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4 and  (MyRec.fields("Species") = "P" or MyRec.fields("Species") = "p" or MyRec.fields("Species") = "s" or  MyRec.fields("Species") = "S") then       
strPMX5 = MyRec.fields("countoford_id") + strPMX5
elseif strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4  and  (MyRec.fields("Species") = "C"  or MyRec.fields("Species") = "c" or myRec.fields("Species") = "R" or myRec.fields("Species")="r") then 
 strOCC5 = strOCC5 +  MyRec.fields("countoford_id")
elseif strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4  and  (MyRec.fields("Species") = "M"  or MyRec.fields("Species") = "m" ) then 
 strMXP5 = strMXP5 + MyRec.fields("countoford_id")


elseIf strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5 and  (MyRec.fields("Species") = "P" or MyRec.fields("Species") = "p" or MyRec.fields("Species") = "s" or  MyRec.fields("Species") = "S") then        
strPMX6 = MyRec.fields("countoford_id") + strPMX6
elseif strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5  and  (MyRec.fields("Species") = "C" or MyRec.fields("Species") = "c"  or myRec.fields("Species") = "R" or myRec.fields("Species")="r") then 
strOCC6 = strOCC6 +  MyRec.fields("countoford_id")
elseif strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5  and  (MyRec.fields("Species") = "M" or MyRec.fields("Species") = "m" ) then 
strMXP6 = strMXP6 + MyRec.fields("countoford_id")


elseIf strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6 and  (MyRec.fields("Species") = "P" or MyRec.fields("Species") = "p" or MyRec.fields("Species") = "s" or  MyRec.fields("Species") = "S") then          
strPMX7 = MyRec.fields("countoford_id") + strPMX7

elseif strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6  and  (MyRec.fields("Species") = "C" or MyRec.fields("Species") = "c"  or myRec.fields("Species") = "R" or myRec.fields("Species")="r") then  
strOCC7 = strOCC7 +  MyRec.fields("countoford_id")
elseif strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6  and  (MyRec.fields("Species") = "M" or MyRec.fields("Species") = "m" ) then  
strMXP7 = strMXP7 + MyRec.fields("countoford_id")


end if
   MyRec.MoveNext
     Wend
     MyRec.close

Dim strOWBCount, strOWBCount2
strOWBCount = 0
strOWBCount2 = 0

		'	strsql = "Select [Date_Time_Out], [Outbound_BOL] from [Master_Table_Backup] where Destination = 'Mobile, Al' and "_
			'&" outbound_Product <> 'Owensboro Wetlap'   and outbound_Product <> 'LL19' and outbound_product <> 'll19' and [Date_Time_out] > '" & strYdate & "'"
			
      '	Set MyRec = Server.CreateObject("ADODB.Recordset")
  		'MyRec.Open strSQL, Session("ConnectionOWB") 
  		'while not MyRec.eof 
  	
		
		'strOP = MyRec.fields("Outbound_BOL")
		'strsql2 = "Select [Date_received] from tblCars where [Release_nbr] = '" & strOP & "'"
 		'Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    '	MyRec2.Open strSQL2, Session("ConnectionString")
   	 
   	 	'if not MyRec2.eof then 
   	 	'strOWBCount = strOWBCount
  		'strOWBCount1 =strOWBCount1
		'else
   	 '	If datepart("h", MyRec.fields("date_time_out")) > 11 and datepart("d", Myrec.fields("Date_time_out")) = datepart("d", Now()) then 
			'strOWBCount1 = strOWBcount1 + 1
 	
   	 	' elseif datepart("h", MyRec.fields("date_time_out")) < 12 and datepart("d", Myrec.fields("Date_time_out")) = datepart("d", Now()) or datepart("d", Myrec.fields("Date_time_out")) < datepart("d", Now()) then
   	 	'strOWBCount = strOWBCount + 1    	 	
 
   	 	' end if
  '	end if
		'MyRec2.close
 
      ' MyRec.MoveNext
    	' Wend
    	' MyRec.close


 Dim gKCOP, gOCC, gPMX, strRailCars
gKCOP = 0
gOCC = 0
gPMX = 0
gOF = 0
gMXP = 0
strsql = "SELECT tblCars.*,  [date_received] AS Sort_Date FROM tblCars WHERE Location = 'YARD' and Rejected is null "_
&" and Net > 0 ORDER BY tblCars.Species, Sort_Date "   
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    while not MyRec.eof
    If MyRec.fields("Species") = "KCOP"  then
    gKCOP = gKCOP + 1
    elseif MyRec.fields("Species") = "PMX"  or MyRec.fields("Species") = "SWL" then
    gPMX = gPMX + 1
    elseif MyRec.fields("Species") = "OCC"  or  MyRec.fields("Species") = "ROCC" Then
    gOCC = gOCC + 1
    
    elseif MyRec.fields("Species") = "MXP" then
    gMXP = gMXP + 1
        elseif MyRec.fields("Species") = "OF"  Then
    gOF = gOF + 1

    end if
    MyRec.movenext
    wend
    MyRec.close
    
    StrRailCars = 0
    
 strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (tblCars.Location='YARD' AND tblCars.Carrier='RAIL' AND (tblCars.Species='KCOP' ))"
   Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then
    strRailCars = MyRec.fields("CountofCID")
gKCOP = gKCOP + (MyRec.Fields("CountofCID") * 2)
end if
    Myrec.close
    
     strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (tblCars.Location='YARD' AND tblCars.Carrier='RAIL' AND ( tblCars.Species='OF'))"
   Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then
    strRailCars = strRailCars + MyRec.fields("CountofCID")
gOF = gOF + (MyRec.Fields("CountofCID") * 2)
end if
    Myrec.close

 strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (tblCars.Location='YARD' AND tblCars.Carrier='RAIL' AND (tblCars.Species='PMX'  or tblCars.Species = 'SWL' ))"
   Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then
    strRailCars = strRailCars + MyRec.fields("CountofCID")
    gPMX = gPMX + (MyRec.Fields("CountofCID") * 2)
    end if
    Myrec.close
    
     strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (tblCars.Location='YARD' AND tblCars.Carrier='RAIL' AND (tblCars.Species='OCC' or tblCars.Species = 'ROCC'  ))"
   Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then
    strRailCars = strRailCars + MyRec.fields("CountofCID")
    gOCC = gOCC + (MyRec.Fields("CountofCID") * 2)
    end if
    Myrec.close
    
         strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (tblCars.Location='YARD' AND tblCars.Carrier='RAIL' AND (tblCars.Species='MXP'))"
   Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then
    strRailCars = strRailCars + MyRec.fields("CountofCID")
    gMXP = gMXP + (MyRec.Fields("CountofCID") * 2)
    end if
    Myrec.close

''''''''''''''''''''''''''''' This starts the routine to count Inbound Loads  
      
    dim strUse
    strUse = date()
  strDay2 = dateadd("d", -3, strUse)
  strDay0 = dateadd("d", 1, struse)
  
  Dim strTomorrow
  strTomorrow = strDay0
  
   	Dim strOB2, countKOP, countPMX, countOCC, countKCOP2, countPMX2, countOCC2, countMXP, countMXP2
 		countKCOP = 0
 		countPMX = 0
 		countOCC = 0
 		countMXP = 0
 		countMXP2 = 0
 		countKCOP2 = 0
 		countPMX2 = 0
 		countOCC2 = 0
 		countOF = 0
 		countOF2 = 0
 		strCountK1A = 0
 		strCountK1B = 0
 		strCountK2 = 0
 		strCountKT5 = 0
 		strCountKNG = 0
 		strCountOF1A = 0
 		strCountOFB = 0
 		strCountOF3 = 0
 		strCountOFNG = 0
 		strCountK1A2 = 0
 		strCountK1B2 = 0
 		strCountK22 = 0
 		strCountKT52 = 0
 		strCountKNG2 = 0
 		strCountOF1A2 = 0
 		strCountOFB2 = 0
 		strCountOF32 = 0
 		strCountOFNG2 = 0


  	     strsql = "SELECT  left(release,1) as Species, Release, dateadd(dd, datediff(dd,0,Date_to),0) as Delivery_date "_
  			&" FROM tblInbound "_
			&" WHERE Destination_City ='MOBILE' and Release <> '0'"_	
			&" and dateadd(dd, datediff(dd,0,Date_to),0) > '" & strDay2 & "' "_
			&" and dateadd(dd, datediff(dd,0,Date_to),0) <= '" & strUse & "' "_
			&" and Ship_Status <> 'Tender Accepted' "_

			&" ORDER BY Date_to" 	

 	
   		Set MyRec = Server.CreateObject("ADODB.Recordset")
  		 MyRec.Open strSQL, Session("ConnectionString") 
		While not MyRec.eof 

 		
		strOB2 = MyRec.fields("Release") 
	

		
		strsql2 = "Select [Date_received] from tblCars where [Release_nbr] = '" & strOB2 & "'"
 		Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    	MyRec2.Open strSQL2, Session("ConnectionString")
   	 	if not MyRec2.eof then  
   	 	
  		else
  			if left(MyRec.fields("Release"), 1)  = "p"  or left(MyRec.fields("Release"), 1)  = "P" or left(MyRec.fields("Release"), 1)  = "S"  or left(MyRec.fields("Release"), 1)  = "s" then
				countPMX = countPMX + 1
				elseif left(MyRec.fields("Release"), 1)  = "c" or left(MyRec.fields("Release"), 1)  = "C" or left(MyRec.fields("Release"), 1)  = "R" or left(MyRec.fields("Release"), 1)  = "r" then
				countOCC = countOCC + 1
				elseif  left(MyRec.fields("Release"), 1)  = "M" or left(MyRec.fields("Release"), 1)  = "m" then
				countMXP = countMXP + 1
	     elseif left(MyRec.fields("Release"), 1)  = "k" or left(MyRec.fields("Release"), 1)  = "K"  then
					
					strsql4 = "Select Tier from tblOrder where Release = '" & strOB2 & "'"
					Set MyRec4 = Server.CreateObject("ADODB.Recordset")
  					 MyRec4.Open strSQL4, Session("ConnectionString") 
					If not MyRec4.eof then
					strTierP = MyRec4("Tier")
				
					
					if MyRec4("Tier") = "1A" then
					strCountK1A = strCountK1A + 1
					elseif MyRec4("Tier") = "1B" then
					strCountK1B =  strCountK1B + 1
					elseif MyRec4("Tier") = "2" then
					strCountK2 =  strCountK2 + 1
					elseif MyRec4("Tier") = "T5" then
					strCountKT5 =  strCountKT5 + 1
					else
					strCountKNG = strCountKNG + 1
					end if
					
					else
					strCountKNG = strCountKNG + 1
					MyRec4.close
					end if

									
				countKCOP = countKCOP + 1
				
					elseif left(MyRec.fields("Release"), 1)  = "F" or left(MyRec.fields("Release"), 1)  = "f" then
					
				strsql4 = "Select Tier from tblOrder where Release = '" & strOB2 & "'"
					Set MyRec4 = Server.CreateObject("ADODB.Recordset")
  					 MyRec4.Open strSQL4, Session("ConnectionString") 
					If not MyRec4.eof then
					if MyRec4("Tier") = "1" then
					strCountOF1A = strCountOF1A + 1
					elseif MyRec4("Tier") = "2" then
					strCountOFB =  strCountOFB + 1
						elseif MyRec4("Tier") = "3" then
					strCountOF3 =  strCountOF3 + 1
					
					else
					strCountOFNG = strCountOFNG + 1
					end if
					else
					strCountofNG = strCountofNG + 1
					MyRec4.close
					end if ' If not MyRec4
	
					
					
					
				countOF = countOF + 1

				end if ' if release number found
				 end if ' if still inbound
		MyRec2.close
 
      	 MyRec.MoveNext
    	 Wend
    	 MyRec.close

       strsql = "SELECT  left(release,1) as Species, Release, dateadd(dd, datediff(dd,0,Date_to),0) as Delivery_date "_
  			&" FROM tblInbound "_
			&" WHERE Destination_City ='MOBILE' and Release <> '0' and  dateadd(dd, datediff(dd,0,Date_to),0) = '" & strTomorrow & "' "_
				&" and Ship_Status <> 'Tender Accepted' "_

			&" ORDER BY Date_to, left(release,1)" 	


   		Set MyRec = Server.CreateObject("ADODB.Recordset")
  	 MyRec.Open strSQL, Session("ConnectionString") 
		While not MyRec.eof 

 		
		strOB2 = MyRec.fields("Release")
		strsql2 = "Select [Date_received] from tblCars where [Release_nbr] = '" & strOB2 & "'"
 		Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    	MyRec2.Open strSQL2, Session("ConnectionString")
   	 	if not MyRec2.eof then  
  		else
  			if left(MyRec.fields("Release"), 1)  = "p"  or left(MyRec.fields("Release"), 1)  = "P" or left(MyRec.fields("Release"), 1)  = "s"  or left(MyRec.fields("Release"), 1)  = "S" then
				countPMX2 = countPMX2 + 1
				elseif left(MyRec.fields("Release"), 1)  = "c" or left(MyRec.fields("Release"), 1)  = "C" or left(MyRec.fields("Release"), 1)  = "R" or left(MyRec.fields("Release"), 1)  = "r" then
				countOCC2 = countOCC2 + 1
				elseif  left(MyRec.fields("Release"), 1)  = "M" or left(MyRec.fields("Release"), 1)  = "m" then
				countMXP2 = countMXP2 + 1

			elseif left(MyRec.fields("Release"), 1)  = "k" or left(MyRec.fields("Release"), 1)  = "K"  then
				
strsql4 = "Select Tier from tblOrder where Release = '" & strOB2 & "'"
					Set MyRec4 = Server.CreateObject("ADODB.Recordset")
  					 MyRec4.Open strSQL4, Session("ConnectionString") 
					If not MyRec4.eof then
					strTierP = MyRec4("Tier")
				
					
					if MyRec4("Tier") = "1A" then
					strCountK1A2 = strCountK1A2 + 1
					elseif MyRec4("Tier") = "1B" then
					strCountK1B2 =  strCountK1B2 + 1
					elseif MyRec4("Tier") = "2" then
					strCountK22 =  strCountK22 + 1
					elseif MyRec4("Tier") = "T5" then
					strCountKT52 =  strCountKT52 + 1
					else
					strCountKNG2 = strCountKNG2 + 1
					end if
					
					else
					strCountKNG2 = strCountKNG2 + 1
					MyRec4.close
					end if
				
				countKCOP2 = countKCOP2 + 1
					elseif left(MyRec.fields("Release"), 1)  = "f" or left(MyRec.fields("Release"), 1)  = "F"  then
						strsql4 = "Select Tier from tblOrder where Release = '" & strOB2 & "'"
					Set MyRec4 = Server.CreateObject("ADODB.Recordset")
  					 MyRec4.Open strSQL4, Session("ConnectionString") 
					If not MyRec4.eof then
					if MyRec4("Tier") = "1" then
					strCountOF1A2 = strCountOF1A2 + 1
					elseif MyRec4("Tier") = "2" then
					strCountOFB2 =  strCountOFB2 + 1
						elseif MyRec4("Tier") = "3" then
					strCountOF32 =  strCountOF32 + 1
					
					else
					strCountOFNG2 = strCountOFNG2 + 1
					end if
					else
					strCountofNG2 = strCountofNG2 + 1
					MyRec4.close
					end if ' If not MyRec4
	
			
				countOF2 = countOF2 + 1

				end if
		end if
		MyRec2.close
 
      	 MyRec.MoveNext
    	 Wend
    	 MyRec.close
    	 
    	 strday3 = dateadd("d", 1, strTomorrow)

       strsql = "SELECT  left(release,1) as Species, Release, dateadd(dd, datediff(dd,0,Date_to),0) as Delivery_date "_
  			&" FROM tblInbound "_
			&" WHERE Destination_City ='MOBILE' and Release <> '0' and  dateadd(dd, datediff(dd,0,Date_to),0) = '" & strday3 & "' "_
			 

			&" ORDER BY Date_to, left(release,1)" 	


   		Set MyRec = Server.CreateObject("ADODB.Recordset")
  	 MyRec.Open strSQL, Session("ConnectionString") 
		While not MyRec.eof 

 		
		strOB2 = MyRec.fields("Release")
		strsql2 = "Select [Date_received] from tblCars where [Release_nbr] = '" & strOB2 & "'"
 		Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    	MyRec2.Open strSQL2, Session("ConnectionString")
   	 	if not MyRec2.eof then  
  		else
  			

			if left(MyRec.fields("Release"), 1)  = "k" or left(MyRec.fields("Release"), 1)  = "K"  then
			 
				
strsql4 = "Select Tier from tblOrder where Release = '" & strOB2 & "'"
					Set MyRec4 = Server.CreateObject("ADODB.Recordset")
  					 MyRec4.Open strSQL4, Session("ConnectionString") 
					If not MyRec4.eof then
					strTierP = MyRec4("Tier")
				
					
					if MyRec4("Tier") = "1A" then
					strCountK1A3 = strCountK1A3 + 1
					elseif MyRec4("Tier") = "1B" then
					strCountK1B3 =  strCountK1B3 + 1
					elseif MyRec4("Tier") = "2" then
					strCountK23 =  strCountK23 + 1
					elseif MyRec4("Tier") = "T5" then
					strCountKT53 =  strCountKT53 + 1
					else
					strCountKNG3 = strCountKNG3 + 1
					end if
					
					else
					strCountKNG3 = strCountKNG3 + 1
					MyRec4.close
					end if
				
			
					elseif left(MyRec.fields("Release"), 1)  = "f" or left(MyRec.fields("Release"), 1)  = "F"  then
						strsql4 = "Select Tier from tblOrder where Release = '" & strOB2 & "'"
					Set MyRec4 = Server.CreateObject("ADODB.Recordset")
  					 MyRec4.Open strSQL4, Session("ConnectionString") 
					If not MyRec4.eof then
					if MyRec4("Tier") = "1" then
					strCountOF1A3 = strCountOF1A3 + 1
					elseif MyRec4("Tier") = "2" then
					strCountOFB3 =  strCountOFB3 + 1
						elseif MyRec4("Tier") = "3" then
					strCountOF33 =  strCountOF33 + 1
					
					else
					strCountOFNG3 = strCountOFNG3 + 1
					end if
					else
					strCountofNG3 = strCountofNG3 + 1
					MyRec4.close
					end if ' If not MyRec4
	
			
				end if
		end if
		MyRec2.close
 
      	 MyRec.MoveNext
    	 Wend
    	 MyRec.close

     strday4 = dateadd("d", 1, strday3)

       strsql = "SELECT  left(release,1) as Species, Release, dateadd(dd, datediff(dd,0,Date_to),0) as Delivery_date "_
  			&" FROM tblInbound "_
			&" WHERE Destination_City ='MOBILE' and Release <> '0' and  dateadd(dd, datediff(dd,0,Date_to),0) = '" & strday4 & "' "_
			 

			&" ORDER BY Date_to, left(release,1)" 	


   		Set MyRec = Server.CreateObject("ADODB.Recordset")
  	 MyRec.Open strSQL, Session("ConnectionString") 
		While not MyRec.eof 

 		
		strOB2 = MyRec.fields("Release")
		strsql2 = "Select [Date_received] from tblCars where [Release_nbr] = '" & strOB2 & "'"
 		Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    	MyRec2.Open strSQL2, Session("ConnectionString")
   	 	if not MyRec2.eof then  
  		else
  			

			if left(MyRec.fields("Release"), 1)  = "k" or left(MyRec.fields("Release"), 1)  = "K"  then
		
				
strsql4 = "Select Tier from tblOrder where Release = '" & strOB2 & "'"
					Set MyRec4 = Server.CreateObject("ADODB.Recordset")
  					 MyRec4.Open strSQL4, Session("ConnectionString") 
					If not MyRec4.eof then
					strTierP = MyRec4("Tier")
				
					
					if MyRec4("Tier") = "1A" then
					strCountK1A4 = strCountK1A4 + 1
					elseif MyRec4("Tier") = "1B" then
					strCountK1B4 =  strCountK1B4 + 1
					elseif MyRec4("Tier") = "2" then
					strCountK24 =  strCountK24 + 1
					elseif MyRec4("Tier") = "T5" then
					strCountKT54 =  strCountKT54 + 1
					else
					strCountKNG4 = strCountKNG4 + 1
					end if
					
					else
					strCountKNG4 = strCountKNG4 + 1
					MyRec4.close
					end if
				
			
					elseif left(MyRec.fields("Release"), 1)  = "f" or left(MyRec.fields("Release"), 1)  = "F"  then
					
						strsql4 = "Select Tier from tblOrder where Release = '" & strOB2 & "'"
					Set MyRec4 = Server.CreateObject("ADODB.Recordset")
  					 MyRec4.Open strSQL4, Session("ConnectionString") 
					If not MyRec4.eof then
					if MyRec4("Tier") = "1" then
					strCountOF1A4 = strCountOF1A4 + 1
					elseif MyRec4("Tier") = "2" then
					strCountOFB4=  strCountOFB4 + 1
						elseif MyRec4("Tier") = "3" then
					strCountOF34 =  strCountOF34 + 1
					
					else
					strCountOFNG4 = strCountOFNG4 + 1
					end if
					else
					strCountofNG4 = strCountofNG4 + 1
					MyRec4.close
					end if ' If not MyRec4
	
			
				end if
		end if
		MyRec2.close
 
      	 MyRec.MoveNext
    	 Wend
    	 MyRec.close
    	 
    	  strday5 = dateadd("d", 1, strday4)

       strsql = "SELECT  left(release,1) as Species, Release, dateadd(dd, datediff(dd,0,Date_to),0) as Delivery_date "_
  			&" FROM tblInbound "_
			&" WHERE Destination_City ='MOBILE' and Release <> '0' and  dateadd(dd, datediff(dd,0,Date_to),0) = '" & strday5 & "' "_
			 

			&" ORDER BY Date_to, left(release,1)" 	


   		Set MyRec = Server.CreateObject("ADODB.Recordset")
  	 MyRec.Open strSQL, Session("ConnectionString") 
		While not MyRec.eof 

 		
		strOB2 = MyRec.fields("Release")
		strsql2 = "Select [Date_received] from tblCars where [Release_nbr] = '" & strOB2 & "'"
 		Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    	MyRec2.Open strSQL2, Session("ConnectionString")
   	 	if not MyRec2.eof then  
  		else
  			

			if left(MyRec.fields("Release"), 1)  = "k" or left(MyRec.fields("Release"), 1)  = "K"  then
			
				
strsql4 = "Select Tier from tblOrder where Release = '" & strOB2 & "'"
					Set MyRec4 = Server.CreateObject("ADODB.Recordset")
  					 MyRec4.Open strSQL4, Session("ConnectionString") 
					If not MyRec4.eof then
					strTierP = MyRec4("Tier")
				
					
					if MyRec4("Tier") = "1A" then
					strCountK1A5 = strCountK1A5 + 1
					elseif MyRec4("Tier") = "1B" then
					strCountK1B5 =  strCountK1B5 + 1
					elseif MyRec4("Tier") = "2" then
					strCountK25 =  strCountK25 + 1
					elseif MyRec4("Tier") = "T5" then
					strCountKT55 =  strCountKT55 + 1
					else
					strCountKNG5 = strCountKNG5 + 1
					end if
					
					else
					strCountKNG5 = strCountKNG5 + 1
					MyRec4.close
					end if
				
			
					elseif left(MyRec.fields("Release"), 1)  = "f" or left(MyRec.fields("Release"), 1)  = "F"  then
					
						strsql4 = "Select Tier from tblOrder where Release = '" & strOB2 & "'"
					Set MyRec4 = Server.CreateObject("ADODB.Recordset")
  					 MyRec4.Open strSQL4, Session("ConnectionString") 
					If not MyRec4.eof then
					if MyRec4("Tier") = "1" then
					strCountOF1A5 = strCountOF1A5 + 1
					elseif MyRec4("Tier") = "2" then
					strCountOFB5=  strCountOFB5 + 1
						elseif MyRec4("Tier") = "3" then
					strCountOF35 =  strCountOF35 + 1
					
					else
					strCountOFNG5 = strCountOFNG5 + 1
					end if
					else
					strCountofNG5 = strCountofNG5 + 1
					MyRec4.close
					end if ' If not MyRec4
	
			
				end if
		end if
		MyRec2.close
 
      	 MyRec.MoveNext
    	 Wend
    	 MyRec.close
 
    	 
   strday6 = dateadd("d", 1, strday5)

       strsql = "SELECT  left(release,1) as Species, Release, dateadd(dd, datediff(dd,0,Date_to),0) as Delivery_date "_
  			&" FROM tblInbound "_
			&" WHERE Destination_City ='MOBILE' and Release <> '0' and  dateadd(dd, datediff(dd,0,Date_to),0) = '" & strday6 & "' "_
			&" ORDER BY Date_to, left(release,1)" 	


   		Set MyRec = Server.CreateObject("ADODB.Recordset")
  	 MyRec.Open strSQL, Session("ConnectionString") 
		While not MyRec.eof 

 		
		strOB2 = MyRec.fields("Release")
		strsql2 = "Select [Date_received] from tblCars where [Release_nbr] = '" & strOB2 & "'"
 		Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    	MyRec2.Open strSQL2, Session("ConnectionString")
   	 	if not MyRec2.eof then  
  		else
  			

			if left(MyRec.fields("Release"), 1)  = "k" or left(MyRec.fields("Release"), 1)  = "K"  then
			
				
strsql4 = "Select Tier from tblOrder where Release = '" & strOB2 & "'"
					Set MyRec4 = Server.CreateObject("ADODB.Recordset")
  					 MyRec4.Open strSQL4, Session("ConnectionString") 
					If not MyRec4.eof then
					strTierP = MyRec4("Tier")
				
					
					if MyRec4("Tier") = "1A" then
					strCountK1A6 = strCountK1A6 + 1
					elseif MyRec4("Tier") = "1B" then
					strCountK1B6 =  strCountK1B6 + 1
					elseif MyRec4("Tier") = "2" then
					strCountK26 =  strCountK26 + 1
					elseif MyRec4("Tier") = "T5" then
					strCountKT56 =  strCountKT56 + 1
					else
					strCountKNG6 = strCountKNG6 + 1
					end if
					
					else
					strCountKNG6 = strCountKNG6 + 1
					MyRec4.close
					end if
				
			
					elseif left(MyRec.fields("Release"), 1)  = "f" or left(MyRec.fields("Release"), 1)  = "F"  then
					
						strsql4 = "Select Tier from tblOrder where Release = '" & strOB2 & "'"
					Set MyRec4 = Server.CreateObject("ADODB.Recordset")
  					 MyRec4.Open strSQL4, Session("ConnectionString") 
					If not MyRec4.eof then
					if MyRec4("Tier") = "1" then
					strCountOF1A6 = strCountOF1A6 + 1
					elseif MyRec4("Tier") = "2" then
					strCountOFB6=  strCountOFB6 + 1
						elseif MyRec4("Tier") = "3" then
					strCountOF36 =  strCountOF36 + 1
					
					else
					strCountOFNG6 = strCountOFNG6 + 1
					end if
					else
					strCountofNG6 = strCountofNG6 + 1
					MyRec4.close
					end if ' If not MyRec4
	
			
				end if
		end if
		MyRec2.close
 
      	 MyRec.MoveNext
    	 Wend
    	 MyRec.close
    	 
    	 
    	  strday7 = dateadd("d", 1, strday6)

       strsql = "SELECT  left(release,1) as Species, Release, dateadd(dd, datediff(dd,0,Date_to),0) as Delivery_date "_
  			&" FROM tblInbound "_
			&" WHERE Destination_City ='MOBILE' and Release <> '0' and  dateadd(dd, datediff(dd,0,Date_to),0) = '" & strday7 & "' "_
			&" ORDER BY Date_to, left(release,1)" 	


   		Set MyRec = Server.CreateObject("ADODB.Recordset")
  	 MyRec.Open strSQL, Session("ConnectionString") 
		While not MyRec.eof 

 		
		strOB2 = MyRec.fields("Release")
		strsql2 = "Select [Date_received] from tblCars where [Release_nbr] = '" & strOB2 & "'"
 		Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    	MyRec2.Open strSQL2, Session("ConnectionString")
   	 	if not MyRec2.eof then  
  		else
  			

			if left(MyRec.fields("Release"), 1)  = "k" or left(MyRec.fields("Release"), 1)  = "K"  then
			
				
strsql4 = "Select Tier from tblOrder where Release = '" & strOB2 & "'"
					Set MyRec4 = Server.CreateObject("ADODB.Recordset")
  					 MyRec4.Open strSQL4, Session("ConnectionString") 
					If not MyRec4.eof then
					strTierP = MyRec4("Tier")
				
					
					if MyRec4("Tier") = "1A" then
					strCountK1A7 = strCountK1A7 + 1
					elseif MyRec4("Tier") = "1B" then
					strCountK1B7 =  strCountK1B7 + 1
					elseif MyRec4("Tier") = "2" then
					strCountK27 =  strCountK27 + 1
					elseif MyRec4("Tier") = "T5" then
					strCountKT57 =  strCountKT57 + 1
					else
					strCountKNG7 = strCountKNG7 + 1
					end if
					
					else
					strCountKNG7 = strCountKNG7 + 1
					MyRec4.close
					end if
				
			
					elseif left(MyRec.fields("Release"), 1)  = "f" or left(MyRec.fields("Release"), 1)  = "F"  then
					
						strsql4 = "Select Tier from tblOrder where Release = '" & strOB2 & "'"
					Set MyRec4 = Server.CreateObject("ADODB.Recordset")
  					 MyRec4.Open strSQL4, Session("ConnectionString") 
					If not MyRec4.eof then
					if MyRec4("Tier") = "1" then
					strCountOF1A7 = strCountOF1A7 + 1
					elseif MyRec4("Tier") = "2" then
					strCountOFB7=  strCountOFB7 + 1
						elseif MyRec4("Tier") = "3" then
					strCountOF37 =  strCountOF37 + 1
					
					else
					strCountOFNG7 = strCountOFNG7 + 1
					end if
					else
					strCountofNG7 = strCountofNG7 + 1
					MyRec4.close
					end if ' If not MyRec4
	
			
				end if
		end if
		MyRec2.close
 
      	 MyRec.MoveNext
    	 Wend
    	 MyRec.close
 

 
  	 
    	 

    strKCOP1 = countKCOP + strOWBCount  
    strPMX1 = countPMX
    strOCC1 = CountOCC
    strOF1 = countOF
    strMXP1 = countMXP
    
    strKCOP2 = countKCOP2 + strOWBCount1
    strPMX2 = countPMX2
    strOCC2 = countOCC2
    strOF2 = countOF2
    strMXP2 = countMXP2
 strsql = "Select tblTempYardTotals.* from tblTempYardTotals where INV_Date = '" & strnow & "'"
		
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
KCOP_One = MyRec.fields("Total_RF")
OCC_One = MyRec.fields("Total_OCC")
MXP_One = MyRec.fields("TOtal_MXP")
OF_One = MyRec.fields("Yard_KCOP")
KCOP_T1_one = MyRec("KCOP_Tier1")
KCOP_T2_one	= MyRec("KCOP_Tier2")
KCOP_T3_One = Myrec("KCOP_Tier3")
KCOP_T4_One = MyREc("KCOP_Tier4")
OF_T1_one = MyRec("OF_Tier1")
OF_T2_one = MYRec("OF_Tier2")
OF_T3_one = MyRec("OF_Tier3")
PMX_one = MyRec("PMX")
else
KCOP_One = 0
OCC_One = 0
MXP_One = 0
OF_One = 0
KCOP_T1_one = 0
KCOP_T2_one	= 0
KCOP_T3_One = 0
KCOP_T4_One =0
OF_T1_one = 0
OF_T2_one = 0
OF_T3_one = 0
PMX_one = 0

end if
MyRec.close

  strSelectdate = dateadd("d", 1, strnow)
				strsql = "Select  tblTempYardTotals.* from tblTempYardTotals where INV_Date = '" & strSelectdate & "'"
		
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
KCOP_two = MyRec.fields("Total_RF")
OCC_two = MyRec.fields("Total_OCC")
OF_two = MyRec.fields("Yard_KCOP")
MXP_two = MyRec.fields("Total_MXP")

KCOP_T1_two = MyRec("KCOP_Tier1")
KCOP_T2_two = MyRec("KCOP_Tier2")
KCOP_T3_two = Myrec("KCOP_Tier3")
KCOP_T4_two = MyREc("KCOP_Tier4")
OF_T1_two = MyRec("OF_Tier1")
OF_T2_two = MYRec("OF_Tier2")
OF_T3_two = MyRec("OF_Tier3")
PMX_two = MyRec("PMX")
else
KCOP_T1_two = 0
KCOP_T2_two	= 0
KCOP_T3_two = 0
KCOP_T4_two =0
OF_T1_two = 0
OF_T2_two = 0
OF_T3_two = 0
PMX_two = 0
KCOP_two = 0
OCC_two = 0
OF_two = 0
MXP_Two = 0

end if
MyRec.close 
strSelectdate = dateadd("d", 2, strnow)

				strsql = "Select  tblTempYardTotals.* from tblTempYardTotals where INV_Date = '" & strSelectdate & "'"
		
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
KCOP_three = MyRec.fields("Total_RF")
OCC_three = MyRec.fields("Total_OCC")
OF_Three = MyRec.fields("Yard_KCOP")
MXP_Three = MyRec.fields("Total_MXP")
KCOP_T1_Three = MyRec("KCOP_Tier1")
KCOP_T2_Three = MyRec("KCOP_Tier2")
KCOP_T3_Three = Myrec("KCOP_Tier3")
KCOP_T4_Three = MyREc("KCOP_Tier4")
OF_T1_Three = MyRec("OF_Tier1")
OF_T2_Three = MYRec("OF_Tier2")
OF_T3_Three = MyRec("OF_Tier3")
PMX_Three = MyRec("PMX")

else
KCOP_three = 0
OCC_three = 0
OF_three = 0
MXP_three = 0
KCOP_T1_Three = 0
KCOP_T2_Three	= 0
KCOP_T3_Three = 0
KCOP_T4_Three =0
OF_T1_Three = 0
OF_T2_Three = 0
OF_T3_Three = 0
PMX_Three = 0
end if
MyRec.close 

strSelectdate = dateadd("d", 3, strnow)
				strsql = "Select  tblTempYardTotals.* from tblTempYardTotals where INV_Date = '" & strSelectdate & "'"
		
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
KCOP_four= MyRec.fields("Total_RF")
OCC_four = MyRec.fields("Total_OCC")
OF_four = MyRec.fields("Yard_KCOP")
MXP_four = MyRec.fields("TOtal_MXP")
KCOP_T1_four = MyRec("KCOP_Tier1")
KCOP_T2_four = MyRec("KCOP_Tier2")
KCOP_T3_four = Myrec("KCOP_Tier3")
KCOP_T4_four = MyREc("KCOP_Tier4")
OF_T1_four = MyRec("OF_Tier1")
OF_T2_four = MYRec("OF_Tier2")
OF_T3_four = MyRec("OF_Tier3")
PMX_four = MyRec("PMX")
else
KCOP_four = 0
OCC_four = 0
OF_Four = 0
MXP_four = 0
KCOP_T1_four = 0
KCOP_T2_four	= 0
KCOP_T3_four = 0
KCOP_T4_four =0
OF_T1_four = 0
OF_T2_four = 0
OF_T3_four = 0
PMX_four = 0
end if
MyRec.close 
 strSelectdate = dateadd("d", 4, strnow)
				strsql = "Select  tblTempYardTotals.* from tblTempYardTotals where INV_Date = '" & strSelectdate & "'"
		
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
KCOP_five = MyRec.fields("Total_RF")
OCC_five = MyRec.fields("Total_OCC")
OF_five = MyRec.fields("Yard_KCOP")
MXP_five = MyRec.fields("Total_MXP")
KCOP_T1_five = MyRec("KCOP_Tier1")
KCOP_T2_five = MyRec("KCOP_Tier2")
KCOP_T3_five = Myrec("KCOP_Tier3")
KCOP_T4_five = MyREc("KCOP_Tier4")
OF_T1_five = MyRec("OF_Tier1")
OF_T2_five = MYRec("OF_Tier2")
OF_T3_five = MyRec("OF_Tier3")
PMX_five = MyRec("PMX")

else
KCOP_five = 0
OCC_five = 0
OF_Five = 0
MXP_five = 0
KCOP_T1_five = 0
KCOP_T2_five	= 0
KCOP_T3_five = 0
KCOP_T4_five =0
OF_T1_five = 0
OF_T2_five = 0
OF_T3_five = 0
PMX_five = 0
end if
MyRec.close
 strSelectdate = dateadd("d", 5, strnow)
				strsql = "Select  tblTempYardTotals.* from tblTempYardTotals where INV_Date = '" & strSelectdate & "'"
		
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
KCOP_six = MyRec.fields("Total_RF")
OCC_six = MyRec.fields("Total_OCC")
OF_six = MyRec.fields("Yard_KCOP")
MXP_six = MyRec.fields("Total_MXP")
KCOP_T1_six = MyRec("KCOP_Tier1")
KCOP_T2_six = MyRec("KCOP_Tier2")
KCOP_T3_six = Myrec("KCOP_Tier3")
KCOP_T4_six = MyREc("KCOP_Tier4")
OF_T1_six = MyRec("OF_Tier1")
OF_T2_six = MYRec("OF_Tier2")
OF_T3_six = MyRec("OF_Tier3")
PMX_six = MyRec("PMX")

else
KCOP_six = 0
OCC_six = 0
OF_six = 0
MXP_six = 0
KCOP_T1_six = 0
KCOP_T2_six	= 0
KCOP_T3_six = 0
KCOP_T4_six =0
OF_T1_six = 0
OF_T2_six = 0
OF_T3_six = 0
PMX_six = 0
end if
MyRec.close 
 strSelectdate = dateadd("d", 6, strnow)
				strsql = "Select  tblTempYardTotals.* from tblTempYardTotals where INV_Date = '" & strSelectdate & "'"
		
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
KCOP_seven = MyRec.fields("Total_RF")
OCC_seven = MyRec.fields("Total_OCC")
OF_seven = MyRec.fields("Yard_KCOP")
MXP_seven = MyRec.fields("Total_MXP")
KCOP_T1_seven = MyRec("KCOP_Tier1")
KCOP_T2_seven = MyRec("KCOP_Tier2")
KCOP_T3_seven = Myrec("KCOP_Tier3")
KCOP_T4_seven = MyREc("KCOP_Tier4")
OF_T1_seven = MyRec("OF_Tier1")
OF_T2_seven = MYRec("OF_Tier2")
OF_T3_seven = MyRec("OF_Tier3")
PMX_seven = MyRec("PMX")
else
KCOP_seven = 0
OCC_seven = 0
OF_seven = 0
MXP_seven = 0
KCOP_T1_seven = 0
KCOP_T2_seven	= 0
KCOP_T3_seven = 0
KCOP_T4_seven =0
OF_T1_seven = 0
OF_T2_seven = 0
OF_T3_seven = 0
PMX_seven = 0
end if
MyRec.close %>





<% strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars WHERE Species = 'KCOP' and (Carrier <> 'RAIL' or Carrier is null)   and Rejected is null and Net > 0 and  Location = 'YARD'"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
XKCOPTotal = MyRec("CountofCID")
end if
MyRec.close

XKCOP1A = 0
XKCOP1B = 0
XKCOP2 = 0
XKCOPT5 = 0
 
strsql = "SELECT  Tier_rating, Count(tblCars.CID) AS CountOfCID FROM tblCars   WHERE Species = 'KCOP' and (Carrier <> 'RAIL' or Carrier is null)   and Rejected is null and Net > 0 and  Location = 'YARD' GROUP BY tblCars.Tier_rating"
 Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
while not MyRec.eof
if MyRec("Tier_Rating") = "1A" then
XKCOP1A = MyRec("CountofCID")
elseif MyRec("Tier_Rating") = "1B" then
XKCOP1B =  MyRec("CountofCID")
elseif MyRec("Tier_Rating") = "2" then
XKCOP2 =  MyRec("CountofCID")
elseif MyRec("Tier_Rating") = "T5" then
XKCOPT5 = MyRec("CountofCID")
end if
MyRec.movenext
wend
MyRec.close
end if
XKCOPNG = XKCOPTotal - XKCOP1A - XKCOP1B - XKCOP2 - XKCOPT5

 strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars WHERE Species = 'KCOP' and Carrier = 'RAIL'   and Rejected is null and Net > 0 and  Location = 'YARD'"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
RXKCOPTotal = MyRec("CountofCID")
end if
MyRec.close

RXKCOP1A = 0
RXKCOP1B = 0
RXKCOP2 = 0
RXKCOPT5 = 0
 
strsql = "SELECT  Tier_rating, Count(tblCars.CID) AS CountOfCID FROM tblCars   WHERE Species = 'KCOP' and Carrier = 'RAIL'   and Rejected is null and Net > 0 and  Location = 'YARD' GROUP BY tblCars.Tier_rating"
 Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
while not MyRec.eof
if MyRec("Tier_Rating") = "1A" then
RXKCOP1A = MyRec("CountofCID")
elseif MyRec("Tier_Rating") = "1B" then
RXKCOP1B =  MyRec("CountofCID")
elseif MyRec("Tier_Rating") = "2" then
RXKCOP2 =  MyRec("CountofCID")
elseif MyRec("Tier_Rating") = "T5" then
RXKCOPT5 = MyRec("CountofCID")
end if
MyRec.movenext
wend
MyRec.close
end if
RXKCOPNG = RXKCOPTotal - RXKCOP1A - RXKCOP1B - RXKCOP2 - RXKCOPT5




strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars WHERE Species = 'OF' and (Carrier <> 'RAIL' or Carrier is null)   and Rejected is null and Net > 0 and  Location = 'YARD'"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
XOFTotal = MyRec("CountofCID")
end if
MyRec.close

XOF1A = 0
XOFB = 0
XOF3 = 0
 
 
strsql = "SELECT  Tier_rating, Count(tblCars.CID) AS CountOfCID FROM tblCars   WHERE Species = 'OF' and (Carrier <> 'RAIL' or Carrier is null)   and Rejected is null and Net > 0 and  Location = 'YARD' GROUP BY tblCars.Tier_rating"
 Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
while not MyRec.eof
if MyRec("Tier_Rating") = "1" then
XOF1A = MyRec("CountofCID")
elseif MyRec("Tier_Rating") = "2" then
XOFB =  MyRec("CountofCID")
elseif MyRec("Tier_Rating") = "3" then
XOF3 =  MyRec("CountofCID")

end if
MyRec.movenext
wend
MyRec.close
end if
XOFNG = XOFTotal - XOF1A - XOFB - XOF3  

 strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars WHERE Species = 'OF' and Carrier = 'RAIL'   and Rejected is null and Net > 0 and  Location = 'YARD'"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
RXOFTotal = MyRec("CountofCID")
end if
MyRec.close

RXOF1A = 0
RXOFB = 0
RXOF3 = 0
 
 
strsql = "SELECT  Tier_rating, Count(tblCars.CID) AS CountOfCID FROM tblCars   WHERE Species = 'OF' and Carrier = 'RAIL'   and Rejected is null and Net > 0 and  Location = 'YARD' GROUP BY tblCars.Tier_rating"
 Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
while not MyRec.eof
if MyRec("Tier_Rating") = "1" then
RXOF1A = MyRec("CountofCID")
elseif MyRec("Tier_Rating") = "2" then
RXOFB =  MyRec("CountofCID")
elseif MyRec("Tier_Rating") = "3" then
RXOF3 =  MyRec("CountofCID")
end if
MyRec.movenext
wend
MyRec.close
end if
RXOFNG = RXOFTotal - RXOF1A - RXOFB - RXOF3


strsql = "SELECT    Count(tblCars.CID) AS CountOfCID FROM tblCars   WHERE (Species = 'PMX' or Species = 'SWL') and (Carrier <> 'RAIL' or Carrier is null)   and Rejected is null and Net > 0 and  Location = 'YARD' "
 Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
XPMX = MyRec("CountofCID")
else
XPMX = 0
End if

strsql = "SELECT    Count(tblCars.CID) AS CountOfCID FROM tblCars   WHERE (Species = 'PMX' or Species = 'SWL') and Carrier = 'RAIL'   and Rejected is null and Net > 0 and  Location = 'YARD' "
 Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
RXPMX = MyRec("CountofCID")
else
RXPMX = 0
End if

strsql = "SELECT    Count(tblCars.CID) AS CountOfCID FROM tblCars   WHERE (Species = 'OCC' or Species = 'ROCC') and (Carrier <> 'RAIL' or Carrier is null)   and Rejected is null and Net > 0 and  Location = 'YARD' "
 Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
XOCC = MyRec("CountofCID")
else
XOCC = 0
End if

strsql = "SELECT    Count(tblCars.CID) AS CountOfCID FROM tblCars   WHERE (Species = 'OCC' or Species = 'ROCC') and Carrier = 'RAIL'   and Rejected is null and Net > 0 and  Location = 'YARD' "
 Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
RXOCC = MyRec("CountofCID")
else
RXOCC = 0
End if

strsql = "SELECT   Count(tblCars.CID) AS CountOfCID FROM tblCars   WHERE Species = 'MXP' and (Carrier <> 'RAIL' or Carrier is null)   and Rejected is null and Net > 0 and  Location = 'YARD'"
 Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
XMXP = MyRec("CountofCID")
else
XMXP = 0
End if

strsql = "SELECT    Count(tblCars.CID) AS CountOfCID FROM tblCars   WHERE Species = 'MXP' and Carrier = 'RAIL'   and Rejected is null and Net > 0 and  Location = 'YARD'"
 Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
RXMXP = MyRec("CountofCID")
else
RXMXP = 0
End if



%>


 <br>
<font face="arial" size="2"><b>Legend - Yard Targets</b></font>
<table border="0" cellpadding="0" cellspacing="0" width="550" style="border-collapse:
 collapse;width:410pt">
	<colgroup>
		<col width="55" span="10" style="mso-width-source:userset;mso-width-alt:2011;
 width:41pt">
	</colgroup>
	<tr height="20">
		<td colspan="4" height="20"  style="height: 15.0pt; " class="style81">
		&nbsp;&nbsp;&nbsp; KCOP</td>
		<td colspan="3"    class="style82">&nbsp;&nbsp; OF</td>
		<td     class="style82">PMX</td>
		<td colspan="2" width="110" style="width: 82pt" class="style81">&nbsp; OCC</td>
	</tr>
	<tr height="21">
		<td height="21" width="55" style="height: 15.75pt; width: 41pt" class="style83">1-A</td>
		<td width="55" style="width: 41pt" class="style84">1-B</td>
		<td width="55" style="width: 41pt" class="style84">2</td>
		<td width="55" style="width: 41pt" class="style85">T-5</td>
		<td width="55" style="width: 41pt" class="style83">1</td>
		<td width="55" style="width: 41pt" class="style121">2</td>
		<td width="55" style="width: 41pt" class="style85">3</td>
		<td width="55" style="width: 41pt" class="style86">PMX</td>
		<td width="55" style="width: 41pt" class="style83">OCC<span style="mso-spacerun:yes">&nbsp;</span></td>
		<td width="55" style="width: 41pt" class="style85">MXP<span style="mso-spacerun:yes">&nbsp;</span></td>
	</tr>
	<tr height="20">
		<td height="20" width="55" style="height: 15.0pt; width: 41pt" class="style87">
		&gt;5</td>
		<td width="55" style="width: 41pt" class="style88">&gt;8</td>
		<td width="55" style="width: 41pt" class="style88">&gt;2</td>
		<td width="55" style="width: 41pt" class="style89">&gt;2</td>
		<td width="55" style="width: 41pt" class="style87">&gt;6</td>
		<td width="55" style="width: 41pt" class="style120">&gt;2</td>
		<td width="55" style="width: 41pt" class="style89">&gt;2</td>
		<td width="55" style="width: 41pt" class="style90">&gt;3</td>
		<td width="55" style="width: 41pt" class="style87">&gt;15</td>
		<td width="55" style="width: 41pt" class="style89">&gt;3</td>
	</tr>
	<tr height="20">
		<td height="20" width="55" style="height: 15.0pt; width: 41pt" class="style91">
		4-5</td>
		<td width="55" style="width: 41pt" class="style92">6-8</td>
		<td width="55" style="width: 41pt" class="style92">2</td>
		<td width="55" style="width: 41pt" class="style93">2</td>
		<td width="55" style="width: 41pt" class="style91">5-6</td>
		<td width="55" style="width: 41pt" class="style119">2</td>
		<td width="55" style="width: 41pt" class="style93">2</td>
		<td width="55" style="width: 41pt" class="style94">3</td>
		<td width="55" style="width: 41pt" class="style91">11-15</td>
		<td width="55" style="width: 41pt" class="style93">3</td>
	</tr>
	<tr height="20">
		<td height="20" width="55" style="height: 15.0pt; width: 41pt" class="style95">
		1-3</td>
		<td width="55" style="width: 41pt" class="style96">1-5</td>
		<td width="55" style="width: 41pt" class="style96">1</td>
		<td width="55" style="width: 41pt" class="style97">1</td>
		<td width="55" style="width: 41pt" class="style95">1-4</td>
		<td width="55" style="width: 41pt" class="style118">1</td>
		<td width="55" style="width: 41pt" class="style97">1</td>
		<td width="55" style="width: 41pt" class="style98">1-2</td>
		<td width="55" style="width: 41pt" class="style95">1-10</td>
		<td width="55" style="width: 41pt" class="style97">1-2</td>
	</tr>
	<tr height="20">
		<td height="20" width="55" style="height: 15.0pt; width: 41pt" class="style99">
		0</td>
		<td width="55" style="width: 41pt" class="style100">0</td>
		<td width="55" style="width: 41pt" class="style100">0</td>
		<td width="55" style="width: 41pt" class="style101">0</td>
		<td width="55" style="width: 41pt" class="style99">0</td>
		<td width="55" style="width: 41pt" class="style117">0</td>
		<td width="55" style="width: 41pt" class="style101">0</td>
		<td width="55" style="width: 41pt" class="style102">0</td>
		<td width="55" style="width: 41pt" class="style99">0</td>
		<td width="55" style="width: 41pt" class="style101">0</td>
	</tr>
</table><br>
Current Yard Inventory
<table style="width: 50%" class="style18" cellspacing="0">
	<tr>
		 
		<td colspan="13" class="style104" style="height: 25px">Trailers</td>
	 
		<td colspan="2" class="style104" style="height: 25px">Inventory</td> </tr>
		<td colspan="6" class="style106">KCOP</td>
	<td colspan="4" class="style106">OF</td><td class="style106">PMX</td>
	<td colspan="2" class="style106">OCC</td>
 
		<td class="style106">RFF</td><td class="style106">OCC</td></tr>
		<tr><td class="style105">&nbsp;</td><td class="style105">1A</td><td class="style105">1B</td>
			<td class="style105">2</td><td class="style105">T5</td>
			<td class="style105">NG</td>
			<td class="style105">1</td>
			<td class="style105">2</td>
			<td class="style105">3</td>
			<td class="style105">NG</td><td class="style105">PMX</td>
			<td class="style105">OCC</td><td class="style105">MXP</td>
			<td class="style105">Total</td><td class="style105">Total</td>
		</tr>
		<tr><td class="style105">Trailers</td>
			<td class="style105"><%= XKCOP1A %>&nbsp;</td>
		<td class="style105"><%= XKCOP1B %>&nbsp;</td>
			<td class="style105"><%= XKCOP2 %>&nbsp;</td>
			<td class="style105"><%= XKCOPT5 %>&nbsp;</td>
			
			<td class="style105"><%= XKCOPNG %>&nbsp;</td>
			<td class="style105"><%= XOF1A %>&nbsp; </td>
			<td class="style105"  ><%=  XOFB %>&nbsp;</td>
		<td  class="style105"><%= XOF3 %>&nbsp;</td>
			 
			<td class="style105"><%= XOFNG %>&nbsp;</td>
			<td class="style105"><%= XPMX %>&nbsp;</td>
			<td class="style105"><%= XOCC %>&nbsp;</td>
			<td class="style105"><%= XMXP %>&nbsp;</td>
	
			<td class="style105"><%= XKCOPTotal + (RXKCOPTotal * 3) + XOFTotal + (RXOFTotal*3) + XPMX + (RXPMX * 3)%>&nbsp;</td>
			<td class="style105"><%= XOCC + (RXOCC * 3) + XMXP + (RXMXP*3)  %>&nbsp;</td>		</tr> 
 
		    <td class="style105">Rail Cars</td>
 
		    <td class="style105"><%= RXKCOP1A %>&nbsp;</td>
		<td class="style105"><%= RXKCOP1B %>&nbsp;</td>
			<td class="style105"><%= RXKCOP2 %>&nbsp;</td>
			<td class="style105"><%= RXKCOPT5 %>&nbsp;</td>
			
			<td class="style105"><%= RXKCOPNG %>&nbsp;</td>
			   <td  class="style105"><%= RXOF1A  %>&nbsp;</td>
	 
			   <td class="style105"><%=  RXOFB %>&nbsp;</td>
	 
			<td class="style105"><%= RXOF3 %>&nbsp;</td>
			<td class="style105"><%= RXOFNG %>&nbsp;</td>
			<td class="style105"><%= RXPMX %>&nbsp;</td>
				<td class="style105"><%= RXOCC%>&nbsp;</td>
				<td class="style105"><%= RXMXP %>&nbsp;</td>
	
			 
	</tr></table>



&nbsp;<table style="width: 80%" class="style18">
	<tr>
		<td colspan="2" style="height: 40px" class="style40"></td>
		<font face="arial" size="1">
		<td colspan="12" class="style44" style="height: 40px"><strong>Inventory BOD</strong></td>

		<td colspan="12" class="style34" style="height: 40px"><strong>Inbounds</strong></td>
		<td style="height: 40px" colspan="10" class="style38"><strong>Consumption</strong></td>
	</tr>
	<tr>

		<td class="style29" colspan="2"></td>
		<font face="arial" size="1">
		<td class="style52" colspan="12"></td>

		<td colspan="12" class="style55"></td>
		<td class="style53" colspan="4"></td>
		<td class="style53" colspan="3"></td>
		<td class="style53"></td>
		<td class="style53" style="width: 30px"></td>
<td class="style53"></td>
		<tr>
		<td class="style50" style="height: 24px" ></td>
		<td class="style50" style="height: 24px" ></td>

		<td class="style114" colspan="5" style="height: 24px"><strong>
		<span class="style125">KCOP </span></strong>
		<span class="style125"><strong>&nbsp;</strong></span></td>

		<td class="style124" colspan="4" style="height: 24px">OF</td>
		<td class="style123" style="height: 24px">PMX</td>
	

		<td class="style123" colspan="2" style="height: 24px">OCC</td>
	

		<td class="style21" colspan="5" style="height: 24px" ><strong>KCOP</strong></td>
		<td class="style21" colspan="4" style="height: 24px" ><strong>OF</strong></td>
		<td class="style21" rowspan="2"><strong>PMX</strong></td>
			<td class="style21" rowspan="2"><strong>OCC</strong></td>
		
			<td class="style21" rowspan="2" ><strong>MXP</strong></td>

		<td  class="style48" colspan="4" style="height: 24px"><strong><span class="style16">
		KCOP</span></strong></td>
			<td  class="style48" colspan="3" style="height: 24px"><strong><span class="style16">
		OF</span></strong><span class="style16">&nbsp;</span></td>

		<td class="style108" rowspan="2" >PMX</td>

		<td class="style17" rowspan="2" style="width: 30px" ><strong><span class="style16">
		OCC</span></strong><span class="style107">&nbsp;</span></td>
		<td class="style17" rowspan="2" ><strong><span class="style16">
		MXP</span></strong><span class="style16">&nbsp;</span></td>



	</tr>
		<tr>
		<td class="style50" ></td>
		<td class="style50" ></td>

		<td class="style47">1A&nbsp;</td>

		<td class="style47">1B&nbsp;</td>

		<td class="style47">2&nbsp;&nbsp;</td>

		<td class="style47">T5&nbsp;</td>

		<td class="style116">NG&nbsp;</td>

		<td class="style47"  >1&nbsp;  </td>
		<td class="style47" >2&nbsp; </td>  		

		<td class="style47"> 3&nbsp;</td>

		<td class="style47">NG&nbsp;</td>
		<td class="style14">PMX</td>
	

		<td class="style14">OCC</td>
	

		<td class="style14">MXP</td>
	

		<td class="style21" style="width: 4px" >1A</td>

		<td class="style21" >1B</td>

		<td class="style21" >2</td>

		<td class="style21" >T5</td>

		<td class="style21" >NG</td>
		<td   class="style21" >1 </td>
 <td   class="style21" >2</td>
		<td class="style21" >3</td>
		<td class="style21" >NG</td>

		<td  class="style48">1A</td>

		<td  class="style48">1B</td>

		<td  class="style48">2</td>

		<td  class="style48">T5</td>

			<td   class="style48">1 </td>
<td   class="style48">2 </td>
			 

			<td  class="style48" style="width: 6px">3</td>



	</tr>
	<tr> 
		<td style="height: 22px" class="style67"><font face="arial" size="2"><%= strNow %></td>
		<% strDate1 = datepart("w", strNow)
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

	
		<td  class="style31" class="style42"><font face="arial" size="2"><%= strDayofWeek %></td>
			
		<td style="height: 22px" align="center"
<% strGroup1 = xKCOP1A + 3*(RXKCOP1a) 
strGroupK1A = strGroup1 %>
			<% if strGroup1 > 5 then %>
		
		bgcolor="#00B0F0">
				<span class="style16">
				<% elseif strGroup1  < 6 and strGroup1 > 3 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 4 and strGroup1 > 0 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
			<td style="height: 22px"  align="center"
<% strGroup1 = xKCOP1B + 3*(RXKCOP1B)
strGroupK1B = strGroup1  %>
<% if strGroup1 > 8 then %>
		
		bgcolor="#00B0F0">
				<span class="style16">
				<% elseif strGroup1  < 9 and strGroup1 > 5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 6 and strGroup1 > 0 then %>
		bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>

		<td style="height: 22px" align="center"
<% strGroup1 = xKCOP2 + 3*(RXKCOP2) 
strGroupK2 = strGroup1 %>
		<% if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" class="style115">
				<span class="style16">
				<% elseif strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  = 1 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
			<td style="height: 22px" align="center"
<% strGroup1 = xKCOPT5 + 3*(RXKCOPT5) 
strGroupKT5 = strGroup1 %>
		<% if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" class="style115">
				<span class="style16">
				<% elseif   strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
		<td style="height: 22px" align="center">
<span class="style16">
<% strGroup1 = XKCOPNG + (3 * RXKCOPNG)
strGroupKNG = strGroup1 %></span> <span class="style16">
	<%= strGroup1 %>
				&nbsp;</span></td>
				
				
	<td   style="height: 22px" class="style115"
<% strGroup1 = xOF1A + 3*(RXOF1A) 
strGroupOF1A = strGroup1 %>
		<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1  =5 or  strGroup1 =6 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  > 0 and strGroup1 < 5 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
	 
	<td   style="height: 22px" class="style115"
<% strGroup1 =  xOFB + 3*(RXOFB)
strGroupOF1B = strGroup1 %>
		<% if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1 =2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>

	<td style="height: 22px" class="style115"
<% strGroup1 = xOF3 + 3*(RXOF3)
strGroupOF3 = strGroup1  %>
		<% if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1  = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>

		<td style="height: 22px" class="style115">
<span class="style16">
<% strGroup1 = xOFNG + 3*(RXOFNG)
strGroupOFNG = strGroup1  %></span> <span class="style16">
			<%= strGroup1 %>	&nbsp;</span></td>
				
				
				
	<td style="height: 22px" class="style115"
<% strGroup1 = xPMX + 3*(RXPMX) 
strGroupPMX = strGroup1  
	  if strGroup1 > 3 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1  = 3 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 or strGroup1 = 2 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
		
		
	
<td style="height: 22px" class="style115"
<% strGroup1 = xOCC + 3*(RXOCC) 
strGroupOCC = strGroup1 %>
		<% if strGroup1 >15 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1 > 10 and strGroup1 < 16 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 > 0 and strGroup < 11 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
		
		
	
	
<td style="height: 22px" class="style115"
<% strGroup1 = xMXP + 3*(RXMXP)
strGroupMXP = strGroup1  %>
		<% if strGroup1 >3 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1 = 3    then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
		
		
	
		<td   class="style112"><%= strCOuntK1A %></span> </td>
	
		<td   class="style112"><%= strCOuntK1B %></td>
	
		<td   class="style112"><%= strCOuntK2 %></td>
	
		<td   class="style112"><%= strCOuntKT5 %></td>
	
		<td   class="style112"><%= strCOuntKNG %></td>
		<td    class="style112"><%= strCountOF1A   %></td>
	 <td    class="style112"><%=  strCountOFB  %></td>
		<td   class="style112"><%= strCountOF3 %></td>
		<td   class="style112"><%= strCountOFNG %></span></td>
	
		<td   class="style109"><%= strPMX1 %></td>
		
		<td   class="style109"><%= strOCC1 %></td>
		
		<td   class="style109"><%= strMXP1 %></td>
		
		
		<td class="style62"><%= KCOP_T1_One %></td>
		
		
		<td class="style62"><%= KCOP_T2_One %></td>
		
		
		<td class="style62"><%= KCOP_T3_One %></td>
		
		
		<td class="style62"><%= KCOP_T4_One %></td>
		
		
				<td   class="style62"><%= OF_T1_One   %></td>
		
				<td   class="style62"><%=   OF_T2_One %></td> 
		
				<td class="style62" style="width: 6px"><%= OF_T3_One %></td>
		
		<td style="height: 22px" class="style62"><%= PMX_One %></td>
		
		<td style="height: 22px; width: 30px;" class="style62"><%= OCC_one %></td>
					<td style="height: 22px" class="style62"><%= MXP_one %></td>
	

	</tr>
	<tr>
		<td  class="style31"><font face="arial" size="2"><%= dateadd("d", 1, strnow)%></td>
				<% strDate1 = datepart("w", dateadd("d", 1, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

		<td  class="style31" ><font face="arial" size="2"><%= strDayofWeek %></td>
		
		<td   align="center"
	
		<% strGroup1 = strGroupK1A + strCountK1A - KCOP_T1_One
		strGroup2K1A = strGroup1		
		  if strGroup1 > 5 then %>
		
		bgcolor="#00B0F0" class="style16">
				<% elseif strGroup1  < 6 and strGroup1 > 3 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 4 and strGroup1 > 0 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><font size=2><%= strGroup1  %></font> </td>
		
		<td   align="center"
<% strGroup1 =  strGroupK1B + strCountK1B - KCOP_T2_One
strGroup2K1B = strGroup1  %>
		<% if strGroup1 > 8 then %>
		
		bgcolor="#00B0F0">
				<span class="style16">
				<% elseif strGroup1  < 9 and strGroup1 > 5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 6 and strGroup1 > 0 then %>	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>

		<td  align="center"
<% strGroup1 = strGroupK2 + strCountK2 - KCOP_T3_One
strGroup2K2 = strGroup1 %>
		<% if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" class="style115">
				<span class="style16">
				<% elseif strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  = 1 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
			<td   align="center"
<% strGroup1 = strGroupKT5 + strCountKT5 - KCOP_T4_One
strGroup2KT5 = strGroup1 %>
		<% if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" class="style115">
				<span class="style16">
				<% elseif   strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
		<td align="center">
<span class="style16">
<% strGroup1 = strGroupKNG + strCountKNG 

strGroup2KNG = strGroup1 %></span> <span class="style16">
	<%= strGroup1 %>
				&nbsp;</span></td>
				
				
	<td    class="style115"
<% strGroup1 = strGroupOF1A + strCountOF1A - OF_T1_One 

strGroup2OF1A = strGroup1 %>
		<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1  =5 or  strGroup1 =6 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  > 0 and strGroup1 < 5 then %>
		bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>

	<td    class="style115"
<% strGroup1 =   strGroupOF1B +  strCountOFB - OF_T2_One

strGroup2OF1B = strGroup1 %>
	<% if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1 =2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 then %>	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>

			
	<td  class="style115"
<% strGroup1 = strGroupOF3 + strCountOF3 - OF_T3_One

strGroup2OF3 = strGroup1  %>
			<% if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1  = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>

		<td  class="style115">
<span class="style16">
<% strGroup1 = strGroupOFNG + strcountOFNG
strGroup2OFNG = strGroup1  %></span> <span class="style16">
			<%= strGroup1 %>	&nbsp;</span></td>
				
				
				
	<td  class="style115"
<% strGroup1 = strGroupPMX + strPMX1 - PMX_one
strGroup2PMX = strGroup1 
	  if strGroup1 > 3 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1  = 3 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 or strGroup1 = 2 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
		
		
	
<td  class="style115"
<% strGroup1 = strGroupOCC + strOCC1 - OCC_One
strGroup2OCC = strGroup1 %>
			<% if strGroup1 >15 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1 > 10 and strGroup1 < 16 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 > 0 and strGroup < 11 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
		
		
	
	
<td  class="style115"
<% strGroup1 = strGroupMXP + strMXP1 - MXP_one
strGroup2MXP = strGroup1  %>
		<% if strGroup1 >3 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1=3    then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
		
	
	
		<td   class="style109" ><span class="style16"><%= strCOuntK1A2 %></span> </td>
	
		<td   class="style112" ><%= strCOuntK1B2 %></td>
	
		<td   class="style112" ><%= strCOuntK22 %></td>
	
		<td   class="style112" ><%= strCOuntKT52 %></td>
	
		<td   class="style112" ><%= strCOuntKNG2 %></td>
		<td   class="style112" ><%= strCountOF1A2   %></td>
		 	<td   class="style112" ><%=  strCountOFB2 %></td>
		<td   class="style112" ><%= strCountOF32 %></td>
		<td   class="style112" ><%= strCountOFNG2 %></span></td>
	
		<td   class="style56" ><font face="arial" size="2"><%= strPMX2 %></td>
		
		

		<td   class="style56" ><font face="arial" size="2"><%= strOCC2 %></td>

		
				<td   class="style56" ><font face="arial" size="2"><%= strMXP2 %></td>
		<td class="style62" ><%= KCOP_T1_two %></td>		
		<td class="style62" ><%= KCOP_T2_two %></td>		
		<td class="style62" ><%= KCOP_T3_two %></td>		
		<td class="style62" ><%= KCOP_T4_two %></td>		
		<td   class="style62" ><%= OF_T1_two  %></td>
 <td   class="style62" ><%=  OF_T2_two %></td>
		<td class="style62" style="width: 6px; height: 28px;"><%= OF_T3_two %></td>
		<td  class="style62"><%= PMX_two %></td>
		<td style="height: 28px; width: 30px;" class="style62"><%= OCC_two %></td>
		<td  class="style62"><%= MXP_two %></td>

	
	</tr>
	<tr>
		<td class="style31" style="height: 17px"><font face="arial" size="2"><%= dateadd("d", 2, strnow)%>&nbsp;</td>
						<% strDate1 = datepart("w", dateadd("d", 2, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

		<td class="style31" style="height: 17px"><font face="arial" size="2"><%= strDayofWeek %>&nbsp;</td>

			<td style="height: 22px"  align="center"
	
		<% strGroup1 = strGroup2K1A + strCountK1A2 - KCOP_T1_two
		strGroup3K1A = strGroup1
	   if strGroup1 > 5 then %>
		
		bgcolor="#00B0F0" class="style122">
				<% elseif strGroup1  < 6 and strGroup1 > 3 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 4 and strGroup1 > 0 then %>		
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><font size=2><%= strGroup1  %></font> </td>
		
		<td style="height: 22px" align="center"
<% strGroup1 =  strGroup2K1B + strCountK1B2 - KCOP_T2_two
strGroup3K1B = strGroup1  %>
<% if strGroup1 > 8 then %>
		
		bgcolor="#00B0F0">
				<span class="style16">
				<% elseif strGroup1  < 9 and strGroup1 > 5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 6 and strGroup1 > 0 then %>	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>

		<td style="height: 22px" align="center"
<% strGroup1 = strGroup2K2 + strCountK23 - KCOP_T3_two
strGroup3K2 = strGroup1 %>
		<% if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" class="style115">
				<span class="style16">
				<% elseif strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  = 1 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
			<td style="height: 22px"  align="center"
<% strGroup1 = strGroup2KT5 + strCountKT52 - KCOP_T4_two
strGroup3KT5 = strGroup1 %>
			<% if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" class="style115">
				<span class="style16">
				<% elseif   strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
		<td style="height: 22px" align="center">
<span class="style16">
<% strGroup1 = strGroup2KNG + strCountKNG2 

strGroup3KNG = strGroup1 %></span> <span class="style16">
	<%= strGroup1 %>
				&nbsp;</span></td>
				
				
	<td  style="height: 22px" class="style115"
<% strGroup1 = strGroup2OF1A + strCountOF1A2 - OF_T1_two  

strGroup3OF1A = strGroup1 %>
	<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1  =5 or  strGroup1 =6 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  > 0 and strGroup1 < 5 then %>	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
	
	<td   style="height: 22px" class="style115"
<% strGroup1 = strGroup2OF1B   + strCountOFB2 - OF_T2_two

strGroup3OF1B = strGroup1 %>
		<% if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1 =2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 then %>	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>





		
	<td style="height: 22px" class="style115"
<% strGroup1 = strGroup2OF3 + strCountOF32 - OF_T3_two

strGroup3OF3 = strGroup1  %>
		<% if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1  = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>

		<td style="height: 22px" class="style115">
<span class="style16">
<% strGroup1 = strGroup2OFNG + strcountOFNG2
strGroup3OFNG = strGroup1  %></span> <span class="style16">
			<%= strGroup1 %>	&nbsp;</span></td>
				
				
				
	<td style="height: 22px" class="style115"
<% strGroup1 = strGroup2PMX + strPMX2 - PMX_two
strGroup3PMX = strGroup1 
	  if strGroup1 > 3 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1  = 3 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 or strGroup1 = 2 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
		
		
	
<td style="height: 22px" class="style115"
<% strGroup1 = strGroup2OCC + strOCC2 - OCC_two
strGroup3OCC = strGroup1 %>
				<% if strGroup1 >15 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1 > 10 and strGroup1 < 16 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 > 0 and strGroup < 11 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
		
		
	
	
<td style="height: 22px" class="style115"
<% strGroup1 = strGroup2MXP + strMXP2 - MXP_two
strGroup3MXP = strGroup1  %>
		<% if strGroup1 >3 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1=3    then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
		
			<td   class="style109"><span class="style16"><%= strCOuntK1A3 %></span> </td>
	
		<td   class="style112"><%= strCountK1B3 %></td>
	
		<td   class="style112"><%= strCOuntK23 %></td>
	
		<td   class="style112"><%= strCOuntKT53 %></td>
	
		<td   class="style112"><%= strCOuntKNG3%></td>
		<td    class="style112"><%= strCountOF1A3  %></td>
	 <td    class="style112"><%=  strCountOFB3 %></td>
		<td   class="style112"><%= strCountOF33%></td>
		<td   class="style112"><%= strCountOFNG3 %></span></td>
		<td   class="style56"><font face="arial" size="2"><%= strPMX3 %></td>
		
		<td   class="style56"><font face="arial" size="2"><%= strOCC3 %></td>
		
		<td   class="style56"><font face="arial" size="2"><%= strMXP3 %></td>

	<td class="style62"><%= KCOP_T1_three %></td>		
<td class="style62"><%= KCOP_T2_three %></td>		
<td class="style62"><%= KCOP_T3_three %></td>		
<td class="style62"><%= KCOP_T4_three %></td>		
<td   class="style62"><%= OF_T1_three  %></td>
 <td   class="style62"><%=   OF_T2_three %></td>
<td class="style62" style="width: 6px"><%= OF_T3_three %></td>
<td style="height: 22px" class="style62"><%= PMX_three %></td>
<td style="height: 22px; width: 30px;" class="style62"><%= OCC_three %></td>
<td style="height: 22px" class="style62"><%= MXP_three %></td>
	</tr>
	<tr>
		<td class="style31"><font face="arial" size="2"><%= dateadd("d", 3, strnow)%>&nbsp;</td>
								<% strDate1 = datepart("w", dateadd("d", 3, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

		<td class="style31"><font face="arial" size="2"><%= strDayofWeek %>&nbsp;</td>

		
							<td style="height: 22px"  align="center"
	
		<% strGroup1 = strGroup3K1A + strCountK1A3 - KCOP_T1_three
		strGroup4K1A = strGroup1
	  if strGroup1 > 5 then %>
		
		bgcolor="#00B0F0" class="style122">
				<% elseif strGroup1  < 6 and strGroup1 > 3 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 4 and strGroup1 > 0 then %>	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><font size=2><%= strGroup1  %></font> </td>
		
		<td style="height: 22px" align="center"
<% strGroup1 =  strGroup3K1B + strCountK1B3 - KCOP_T2_three
strGroup4K1B = strGroup1  %>
<% if strGroup1 > 8 then %>
		
		bgcolor="#00B0F0">
				<span class="style16">
				<% elseif strGroup1  < 9 and strGroup1 > 5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 6 and strGroup1 > 0 then %>	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>

		<td style="height: 22px" align="center"
<% strGroup1 = strGroup3K2 + strCountK23 - KCOP_T3_three
strGroup4K2 = strGroup1 %>
	<% if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" class="style115">
				<span class="style16">
				<% elseif strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  = 1 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
			<td style="height: 22px"  align="center"
<% strGroup1 = strGroup3KT5 + strCountKT53 - KCOP_T4_three
strGroup4KT5 = strGroup1 %>
		<% if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" class="style115">
				<span class="style16">
				<% elseif   strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
		<td style="height: 22px" align="center">
<span class="style16">
<% strGroup1 = strGroup3KNG + strCountKNG3 

strGroup4KNG = strGroup1 %></span> <span class="style16">
	<%= strGroup1 %>
				&nbsp;</span></td>
				
				
	<td   style="height: 22px" class="style115"
<% strGroup1 = strGroup3OF1A + strCountOF1A3 - OF_T1_three  

strGroup4OF1A = strGroup1 %>
		<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1  =5 or  strGroup1 =6 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  > 0 and strGroup1 < 5 then %>		
	 
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>

	<td   style="height: 22px" class="style115"
<% strGroup1 = strGroup3OF1B  + strCountOFB3 - OF_T2_three

strGroup4OF1B = strGroup1 %>
			<% if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1 =2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 then %>	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>




	<td style="height: 22px" class="style115"
<% strGroup1 = strGroup3OF3 + strCountOF33 - OF_T3_three

strGroup4OF3 = strGroup1  %>
			<% if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1  = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  > 0 and strGroup < 3 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>

		<td style="height: 22px" class="style115">
<span class="style16">
<% strGroup1 = strGroup3OFNG + strcountOFNG3
strGroup4OFNG = strGroup1  %></span> <span class="style16">
			<%= strGroup1 %>	&nbsp;</span></td>
				
				
				
	<td style="height: 22px" class="style115"
<% strGroup1 = strGroup3PMX + strPMX3 - PMX_three
strGroup4PMX = strGroup1 
	  if strGroup1 > 3 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1  = 3 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 or strGroup1 = 2 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
		
		
	
<td style="height: 22px" class="style115"
<% strGroup1 = strGroup3OCC + strOCC3 - OCC_three
strGroup4OCC = strGroup1 %>
		<% if strGroup1 >15 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1 > 10 and strGroup1 < 16 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 > 0 and strGroup < 11 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
		
		
	
	
<td style="height: 22px" class="style115"
<% strGroup1 = strGroup3MXP + strMXP3 - MXP_three
strGroup4MXP = strGroup1  %>
			<% if strGroup1 >3 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1=3    then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>

	<td   class="style109"><span class="style16"><%= strCOuntK1A4 %></span> </td>
	
		<td   class="style112"><%= strCOuntK1B4 %></td>
	
		<td   class="style112"><%= strCOuntK24 %></td>
	
		<td   class="style112"><%= strCOuntKT54 %></td>
	
		<td   class="style112"><%= strCOuntKNG4%></td>
		<td    class="style112"><%= strCountOF1A4   %></td>
	 <td    class="style112"><%=  strCountOFB4  %></td>
		<td   class="style112"><%= strCountOF34%></td>
		<td   class="style112"><%= strCountOFNG4 %></span></td>
		<td   class="style56"><font face="arial" size="2"><%= strPMX4 %></td>
	
		<td   class="style56"><font face="arial" size="2"><%= strOCC4 %></td>
		
				<td   class="style56"><font face="arial" size="2"><%= strMXP4 %></td>

		<td class="style62"><%= KCOP_T1_four %></td>		
<td class="style62"><%= KCOP_T2_four %></td>		
<td class="style62"><%= KCOP_T3_four %></td>		
<td class="style62"><%= KCOP_T4_four %></td>		
<td   class="style62"><%= OF_T1_four  %></td> 
<td   class="style62"><%=  OF_T2_four %></td> 
<td class="style62" style="width: 6px"><%= OF_T3_four %></td>
<td style="height: 22px" class="style62"><%= PMX_four %></td>
<td style="height: 22px; width: 30px;" class="style62"><%= OCC_four %></td>
<td style="height: 22px" class="style62"><%= MXP_four %></td>

	</tr>
	<tr>
		<td class="style31"><font face="arial" size="2"><%= dateadd("d", 4, strnow)%>&nbsp;</td>
								<% strDate1 = datepart("w", dateadd("d", 4, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

		<td class="style31"><font face="arial" size="2"><%= strDayofWeek %></td>


							<td style="height: 22px"  align="center"
	
		<% strGroup1 = strGroup4K1A + strCountK1A4 - KCOP_T1_four
		strGroup5K1A = strGroup1

		  if strGroup1 > 5 then %>
		
		bgcolor="#00B0F0" class="style122">
				<% elseif strGroup1  < 6 and strGroup1 > 3 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 4 and strGroup1 > 0 then %>	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><font size=2><%= strGroup1  %></font> </td>
		
		<td style="height: 22px" align="center"
<% strGroup1 =  strGroup4K1B + strCountK1B4 - KCOP_T2_four
strGroup5K1B = strGroup1  %>
	<% if strGroup1 > 8 then %>
		
		bgcolor="#00B0F0">
				<span class="style16">
				<% elseif strGroup1  < 9 and strGroup1 > 5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 6 and strGroup1 > 0 then %>	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>

		<td style="height: 22px" align="center"
<% strGroup1 = strGroup4K2 + strCountK24 - KCOP_T3_four
strGroup5K2 = strGroup1 %>
	<% if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" class="style115">
				<span class="style16">
				<% elseif strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  = 1 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
			<td style="height: 22px"  align="center"
			<% strGroup1 = strGroup4KT5 + strCountKT54 - KCOP_T4_four
strGroup5KT5 = strGroup1 %>
		<% if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" class="style115">
				<span class="style16">
				<% elseif   strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
		<td style="height: 22px" align="center">
<span class="style16">
<% strGroup1 = strGroup4KNG + strCountKNG4

strGroup5KNG = strGroup1 %></span> <span class="style16">
	<%= strGroup1 %>
				&nbsp;</span></td>
				
				
	<td    style="height: 22px" class="style115"
<% strGroup1 = strGroup4OF1A + strCountOF1A4 - OF_T1_four  

strGroup5OF1A = strGroup1 %>
				<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1  =5 or  strGroup1 =6 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  > 0 and strGroup1 < 5 then %>	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>

	<td    style="height: 22px" class="style115"
<% strGroup1 = strGroup4OF1B   + strCountOFB4 - OF_T2_four

strGroup5OF1B = strGroup1 %>
		<% if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1 =2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 then %>	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>


	<td style="height: 22px" class="style115"
<% strGroup1 = strGroup4OF3 + strCountOF34 - OF_T3_four

strGroup5OF3 = strGroup1  %>
			<% if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1  = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>

		<td style="height: 22px" class="style115">
<span class="style16">
<% strGroup1 = strGroup4OFNG + strcountOFNG4
strGroup5OFNG = strGroup1  %></span> <span class="style16">
			<%= strGroup1 %>	&nbsp;</span></td>
				
				
				
	<td style="height: 22px" class="style115"
<% strGroup1 = strGroup4PMX + strPMX4 - PMX_four
strGroup5PMX = strGroup1  
		  if strGroup1 > 3 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1  = 3 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 or strGroup1 = 2 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
		
		
	
<td style="height: 22px" class="style115"
<% strGroup1 = strGroup4OCC + strOCC4 - OCC_four
strGroup5OCC = strGroup1 %>
			<% if strGroup1 >15 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1 > 10 and strGroup1 < 16 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 > 0 and strGroup < 11 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
		
		
	
	
<td style="height: 22px" class="style115"
<% strGroup1 = strGroup4MXP + strMXP4 - MXP_four
strGroup5MXP = strGroup1  %>
		<% if strGroup1 >3 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1=3    then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>

	<td   class="style109"><span class="style16"><%= strCOuntK1A5 %></span> </td>
	
		<td   class="style112"><%= strCOuntK1B5 %></td>
	
		<td   class="style112"><%= strCOuntK25 %></td>
	
		<td   class="style112"><%= strCOuntKT55 %></td>
	
		<td   class="style112"><%= strCOuntKNG5%></td>
		<td    class="style112"><%= strCountOF1A5   %></td>
		 <td    class="style112"><%=   strCountOFB5 %></td>
		<td   class="style112"><%= strCountOF35%></td>
		<td   class="style112"><%= strCountOFNG5 %></span></td>

		<td   class="style56"><font face="arial" size="2"><%= strPMX5 %></td>
		
		<td   class="style56"><font face="arial" size="2"><%= strOCC5 %></td>
		<td   class="style56"><font face="arial" size="2"><%= strMXP5 %></td>

		<td class="style62"><%= KCOP_T1_five %></td>		
<td class="style62"><%= KCOP_T2_five %></td>		
<td class="style62"><%= KCOP_T3_five %></td>		
<td class="style62"><%= KCOP_T4_five %></td>		
<td   class="style62"><%= OF_T1_five   %></td>
 <td   class="style62"><%=  OF_T2_five %></td>

<td class="style62" style="width: 6px"><%= OF_T3_five %></td>
<td style="height: 22px" class="style62"><%= PMX_five %></td>
<td style="height: 22px; width: 30px;" class="style62"><%= OCC_five %></td>
<td style="height: 22px" class="style62"><%= MXP_five %></td>
	</tr>
	<tr>
		<td class="style31" style="height: 22px"><font face="arial" size="2"><%= dateadd("d", 5, strnow)%>&nbsp;</td>
				<% strDate1 = datepart("w", dateadd("d", 5, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

		<td class="style31" style="height: 22px"><font face="arial" size="2"><%= strDayofWeek %>&nbsp;</td>

			
	<td style="height: 22px"  align="center"
	
		<% strGroup1 = strGroup5K1A + strCountK1A5 - KCOP_T1_five
		strGroup6K1A = strGroup1

		 if strGroup1 > 5 then %>
		
		bgcolor="#00B0F0" class="style122">
				<% elseif strGroup1  < 6 and strGroup1 > 3 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 4 and strGroup1 > 0 then %>	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><font size=2><%= strGroup1  %></font> </td>
		
		<td style="height: 22px" align="center"
<% strGroup1 =  strGroup5K1B + strCountK1B5 - KCOP_T2_five
strGroup6K1B = strGroup1  %>
	<% if strGroup1 > 8 then %>
		
		bgcolor="#00B0F0">
				<span class="style16">
				<% elseif strGroup1  < 9 and strGroup1 > 5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 6 and strGroup1 > 0 then %>
		bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>

		<td style="height: 22px" align="center"
<% strGroup1 = strGroup5K2 + strCountK25 - KCOP_T3_five
strGroup6K2 = strGroup1 %>
	<% if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" class="style115">
				<span class="style16">
				<% elseif strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  = 1 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
			<td style="height: 22px"  align="center"
<% strGroup1 = strGroup5KT5 + strCountKT55 - KCOP_T4_five
strGroup6KT5 = strGroup1 %>
		<% if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" class="style115">
				<span class="style16">
				<% elseif   strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
		<td style="height: 22px" align="center">
<span class="style16">
<% strGroup1 = strGroup5KNG + strCountKNG5

strGroup6KNG = strGroup1 %></span> <span class="style16">
	<%= strGroup1 %>
				&nbsp;</span></td>
				
				
	<td   style="height: 22px" class="style115"
<% strGroup1 = strGroup5OF1A + strCountOF1A5 - OF_T1_five  

strGroup6OF1A = strGroup1 %>
			<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1  =5 or  strGroup1 =6 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  > 0 and strGroup1 < 5 then %>	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
			 
	<td   style="height: 22px" class="style115"
<% strGroup1 = strGroup5OF1B  + strCountOFB5 - OF_T2_five

strGroup6OF1B = strGroup1 %>
		<% if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1 =2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 then %>
		bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>

	<td style="height: 22px" class="style115"
<% strGroup1 = strGroup5OF3 + strCountOF35 - OF_T3_five

strGroup6OF3 = strGroup1  %>
				<% if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1  = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>

		<td style="height: 22px" class="style115">
<span class="style16">
<% strGroup1 = strGroup5OFNG + strcountOFNG5
strGroup6OFNG = strGroup1  %></span> <span class="style16">
			<%= strGroup1 %>	&nbsp;</span></td>
				
				
				
	<td style="height: 22px" class="style115"
<% strGroup1 = strGroup5PMX + strPMX5 - PMX_five
strGroup6PMX = strGroup1  
			  if strGroup1 > 3 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1  = 3 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 or strGroup1 = 2 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
		
		
	
<td style="height: 22px" class="style115"
<% strGroup1 = strGroup5OCC + strOCC5 - OCC_five
strGroup6OCC = strGroup1 %>
		<% if strGroup1 >15 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1 > 10 and strGroup1 < 16 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 > 0 and strGroup < 11 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
		
		
	
	
<td style="height: 22px" class="style115"
<% strGroup1 = strGroup5MXP + strMXP5 - MXP_five
strGroup6MXP = strGroup1  %>
		<% if strGroup1 >3 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1=3    then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
	<td   class="style109"><span class="style16"><%= strCOuntK1A6 %></span> </td>
	
		<td   class="style112"><%= strCOuntK1B6 %></td>
	
		<td   class="style112"><%= strCOuntK26 %></td>
	
		<td   class="style112"><%= strCOuntKT56 %></td>
	
		<td   class="style112"><%= strCOuntKNG6%></td>
		<td  class="style112"><%= strCountOF1A6   %></td>
		 <td  class="style112"><%=  strCountOFB6 %></td>

		<td   class="style112"><%= strCountOF36%></td>
		<td   class="style112"><%= strCountOFNG6 %></span></td>

		<td   class="style56" style="height: 22px"><font face="arial" size="2"><%= strPMX6 %></td>
		
	

		<td   class="style56" style="height: 22px"><font face="arial" size="2"><%= strOCC6 %></td>
		<td   class="style56" style="height: 22px"><font face="arial" size="2"><%= strMXP6 %></td>
		<td class="style62"><%= KCOP_T1_six %></td>		
<td class="style62"><%= KCOP_T2_six %></td>		
<td class="style62"><%= KCOP_T3_six %></td>		
<td class="style62"><%= KCOP_T4_six %></td>		
<td   class="style62"><%= OF_T1_six   %></td>
 <td   class="style62"><%=   OF_T2_six  %></td>

<td class="style62" style="width: 6px"><%= OF_T3_six %></td>
<td style="height: 22px" class="style62"><%= PMX_six %></td>
<td style="height: 22px; width: 30px;" class="style62"><%= OCC_six %></td>
<td style="height: 22px" class="style62"><%= MXP_six %></td>	</tr>
	<tr>
		<td class="style31"><font face="arial" size="2"><%= dateadd("d", 6, strnow)%></td>
				<% strDate1 = datepart("w", dateadd("d", 6, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

		<td  class="style31"><font face="arial" size="2"><%= strDayofWeek %></td>

	<td style="height: 22px"  align="center"
	
		<% strGroup1 = strGroup6K1A + strCountK1A6 - KCOP_T1_six
		strGroup7K1A = strGroup1
	 if strGroup1 > 5 then %>
		
		bgcolor="#00B0F0" class="style122">
				<% elseif strGroup1  < 6 and strGroup1 > 3 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 4 and strGroup1 > 0 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><font size=2><%= strGroup1  %></font> </td>
		
		<td style="height: 22px" align="center"
<% strGroup1 =  strGroup6K1B + strCountK1B6 - KCOP_T2_six
strGroup7K1B = strGroup1  %>
		<% if strGroup1 > 8 then %>
		
		bgcolor="#00B0F0">
				<span class="style16">
				<% elseif strGroup1  < 9 and strGroup1 > 5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 6 and strGroup1 > 0 then %>	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>

		<td style="height: 22px" align="center"
<% strGroup1 = strGroup6K2 + strCountK26 - KCOP_T3_six
strGroup7K2 = strGroup1 %>
		<% if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" class="style115">
				<span class="style16">
				<% elseif strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  = 1 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
			<td style="height: 22px"  align="center"
<% strGroup1 = strGroup6KT5 + strCountKT56 - KCOP_T4_six
strGroup7KT5 = strGroup1 %>
		<% if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" class="style115">
				<span class="style16">
				<% elseif   strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
		<td style="height: 22px" align="center">
<span class="style16">
<% strGroup1 = strGroup6KNG + strCountKNG6

strGroup7KNG = strGroup1 %></span> <span class="style16">
	<%= strGroup1 %>
				&nbsp;</span></td>
				
				
	<td   style="height: 22px" class="style115"
<% strGroup1 = strGroup6OF1A + strCountOF1A6 - OF_T1_six  


strGroup7OF1A = strGroup1 %>
	<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1  =5 or  strGroup1 =6 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  > 0 and strGroup1 < 5 then %>
		bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
			 
	<td   style="height: 22px" class="style115"
<% strGroup1 = strGroup6OF1B  + strCountOFB6 - OF_T2_six


strGroup7OF1B = strGroup1 %>
		<% if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1 =2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 then %>
		bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>

	<td style="height: 22px" class="style115"
<% strGroup1 = strGroup6OF3 + strCountOF36 - OF_T3_six

strGroup7OF3 = strGroup1  %>
			<% if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1  = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>

		<td style="height: 22px" class="style115">
<span class="style16">
<% strGroup1 = strGroup6OFNG + strcountOFNG6
strGroup7OFNG = strGroup1  %></span> <span class="style16">
			<%= strGroup1 %>	&nbsp;</span></td>
				
				
				
	<td style="height: 22px" class="style115"
<% strGroup1 = strGroup6PMX + strPMX6 - PMX_six
strGroup7PMX = strGroup1  
			  if strGroup1 > 3 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1  = 3 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 or strGroup1 = 2 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
		
		
	
<td style="height: 22px" class="style115"
<% strGroup1 = strGroup6OCC + strOCC6 - OCC_six
strGroup7OCC = strGroup1 %>
		<% if strGroup1 >15 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1 > 10 and strGroup1 < 16 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 > 0 and strGroup < 11 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
		
		
	
	
<td style="height: 22px" class="style115"
<% strGroup1 = strGroup6MXP + strMXP6 - MXP_six
strGroup7MXP = strGroup1  %>
		<% if strGroup1 >3 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1=3    then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
	<td   class="style109"><span class="style16"><%= strCOuntK1A7 %></span> </td>
	
		<td   class="style112"><%= strCOuntK1B7 %></td>
	
		<td   class="style112"><%= strCOuntK27 %></td>
	
		<td   class="style112"><%= strCOuntKT57 %></td>
	
		<td   class="style112"><%= strCOuntKNG7%></td>
		<td    class="style112"><%= strCountOF1A7  %></td>
	 <td    class="style112"><%=   strCountOFB7 %></td>
		<td   class="style112"><%= strCountOF37%></td>
		<td   class="style112"><%= strCountOFNG7 %></span></td>
		<td   class="style56"><font face="arial" size="2"><%= strPMX7 %></td>
		
		<td   class="style56"><font face="arial" size="2"><%= strOCC7 %></td>
		
<td   class="style56"><font face="arial" size="2"><%= strMXP7 %></td>
		<td class="style62"><%= KCOP_T1_seven %></td>		
<td class="style62"><%= KCOP_T2_seven %></td>		
<td class="style62"><%= KCOP_T3_seven %></td>		
<td class="style62"><%= KCOP_T4_seven %></td>		
<td   class="style62"><%= OF_T1_seven   %></td>
 <td   class="style62"><%=   OF_T2_seven %></td>
<td class="style62" style="width: 6px"><%= OF_T3_seven %></td>
<td style="height: 22px" class="style62"><%= PMX_seven %></td>
<td style="height: 22px; width: 30px;" class="style62"><%= OCC_seven %></td>
<td style="height: 22px" class="style62"><%= MXP_seven %></td>	</tr>
	<tr>
		<td class="style31"></td>
		<td class="style31">  </td>

									<td style="height: 22px"  align="center"
	
		<% strGroup1 = strGroup7K1A + strCountK1A7- KCOP_T1_seven

  if strGroup1 > 5 then %>
		
		bgcolor="#00B0F0" class="style122">
				<% elseif strGroup1  < 6 and strGroup1 > 3 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 4 and strGroup1 > 0 then %>	bgcolor="yellow">

	<% else %>
		bgcolor="red">


		<% end if %><font size=2><%= strGroup1  %></font> </td>
		
		<td style="height: 22px" align="center"
<% strGroup1 =  strGroup7K1B + strCountK1B7 - KCOP_T2_seven
  if strGroup1 > 8 then %>
		
		bgcolor="#00B0F0">
				<span class="style16">
				<% elseif strGroup1  < 9 and strGroup1 > 5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  < 6 and strGroup1 > 0 then %>	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>

		<td style="height: 22px" align="center"
<% strGroup1 = strGroup7K2 + strCountK27 - KCOP_T3_seven
	  if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" class="style115">
				<span class="style16">
				<% elseif strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  = 1 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
			<td style="height: 22px"  align="center"
<% strGroup1 = strGroup7KT5 + strCountKT57 - KCOP_T4_seven
	  if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" class="style115">
				<span class="style16">
				<% elseif   strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
		<td style="height: 22px" align="center">
<span class="style16">
<% strGroup1 = strGroup7KNG + strCountKNG7
  %></span> <span class="style16">
	<%= strGroup1 %>
				&nbsp;</span></td>
				
				
	<td   style="height: 22px" class="style115"
<% strGroup1 = strGroup7OF1A + strCountOF1A7 - OF_T1_seven  %>
<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1  =5 or  strGroup1 =6 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1  > 0 and strGroup1 < 5 then %>	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>

<td   style="height: 22px" class="style115"
<% strGroup1 = strGroup7OF1B  + strCountOFB7 - OF_T2_seven  %>
	<% if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1 =2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 then %>	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
	

	<td style="height: 22px" class="style115"
<% strGroup1 = strGroup7OF3 + strCountOF37 - OF_T3_seven
		 if strGroup1 > 2 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1  = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>

		<td style="height: 22px" class="style115">
<span class="style16">
<% strGroup1 = strGroup7OFNG + strcountOFNG7
  %></span> <span class="style16">
			<%= strGroup1 %>	&nbsp;</span></td>
				
				
				
	<td style="height: 22px" class="style115"
<% strGroup1 = strGroup7PMX + strPMX7 - PMX_seven
		  if strGroup1 > 3 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1  = 3 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 = 1 or strGroup1 = 2 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
		
		
	
<td style="height: 22px" class="style115"
<% strGroup1 = strGroup7OCC + strOCC7 - OCC_seven
		  if strGroup1 >15 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1 > 10 and strGroup1 < 16 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 > 0 and strGroup < 11 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
		
		
	
	
<td style="height: 22px" class="style115"
<% strGroup1 = strGroup7MXP + strMXP7 - MXP_seven
	 if strGroup1 >3 then %>
		
		bgcolor="#00B0F0" >
				<span class="style16">
				<% elseif strGroup1=3    then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %></span> <span class="style16">
<%= strGroup1 %></span></td>
		<td  class="style56" style="width: 4px"></td>
		<td  class="style56"></td>
		<td  class="style56"></td>
		<td  class="style56"></td>
		<td  class="style56"></td>
		<td class="style56"></td>
		<td class="style56"></td>
			<td class="style56"></td>
			<td class="style56"></td>
			<td class="style56"></td>
			<td class="style56"></td>
				<td class="style56"></td>
		<td  class="style54"></td>

		<td  class="style54"></td>

		<td  class="style54"></td>

		<td  class="style54"></td>

		<td class="style54"></td>

		<td class="style54"></td>

		<td class="style54" style="width: 6px"></td>

			<td  class="style54"></td>
			<td  class="style54" style="width: 30px"></td>
		<td class="style54"></td>
	</tr>
</table>
<p class="style16">
<strong><span class="style15">Planned Loads from OWB

<table style="width: 25%" class="style71">
     <tr>
    

      <td  align="left"  class="style70"><font face="Arial">Scheduled Date</font></td>
         <td  align="left"  class="style70"><font face="Arial">Material #</font></td>
      <td  align="center"  class="style70"><font face="Arial">Count</font></td>
      </tr>
      
      <% strsql = "SELECT Count(tblSapAutoImport.ID) AS CountOfID, tblSapAutoImport.Delivery_Date, Right([Material],8) AS Mat FROM tblSapAutoImport "_
&" WHERE (((tblSapAutoImport.Vendor)='OWENSBORO GLOBAL SALES MILL') AND ((tblSapAutoImport.Received_qty)=0)) "_
&" GROUP BY tblSapAutoImport.Delivery_Date, Right([Material],8) "_
&" HAVING (((tblSapAutoImport.Delivery_Date)>20130109) AND ((Right([Material],8))='70000166' Or (Right([Material],8))='70000071')) "_
&" ORDER BY tblSapAutoImport.Delivery_Date"

       Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
  
   %>
         
  	<% 
    
       ii = 0
       while not MyRec.Eof
    
    %>
    <% if ( ii mod 2) = 0 Then %>
           <tr class=tablecolor1>
    <% else %>
       <tr class=tablecolor2>
    <% end if %>

         <td  align="left"  class="style16">
			<font face="Arial" size = 1 class="style16"><%= mid(MyRec.fields("Delivery_Date"),5,2)%>-<%= right(MyRec.fields("Delivery_Date"),2)%></font></td>
         <td  align="left"  class="style16"><font face="Arial"><%= MyRec.fields("Mat")%></font></td>
      <td  align="center"  class="style16"><font face="Arial"><%= MyRec.fields("countofid")%></font></td>

   </tr>
    <% 
       ii = ii + 1
 
       MyRec.MoveNext
     Wend
     MyRec.close
    %>


   </table>







<p class="style1"><strong>SAP Inventory<br>To get real-time inventory use SAP, Transaction MB52</strong></p>
<% Dim strSAPdate
strsql = "SELECT Max(tblInventoryHistory.Rpt_date) AS MaxOfRpt_date FROM tblInventoryHistory"

     Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionPolaris") 

strSAPdate = MyRec.fields("MaxofRpt_Date")
MyRec.close

strsql = "SELECT tblInventoryHistory.Rpt_date, tblInventoryHistory.SAP_nbr, tblReference.Commodity, tblReference.Description, tblInventoryHistory.Location, tblInventoryHistory.UOM, tblInventoryHistory.Value "_
&" FROM tblInventoryHistory INNER JOIN tblReference ON tblInventoryHistory.SAP_nbr = tblReference.SAP "_
&" WHERE Rpt_date='" & strSAPdate & "' AND (tblReference.Commodity='OCC' Or "_
&" tblReference.Commodity='Recycle Fiber' Or tblReference.Commodity='Broke') AND "_
&"  (Location='PLP1' Or Location='PLP2' Or Location='PLP4' "_
&"  Or Location='PLP3'  Or Location='WAD1' Or Location='WAD2' "_
&"   Or Location='WAD3' Or Location='WAD4') order by Commodity, SAP_Nbr, Location"

       Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionPolaris") 


 %>
<table width="60%" border="1">
     <tr bgcolor="#D9D9FF">
    

      <td  align="left" colspan="2" class="style16">
		<font face="Arial" size = 1 class="style16">Last Downloaded<br> from SAP </font></td>
         <td  align="left" colspan="2" class="style16"><font face="Arial">Material #</font></td>
      <td  align="center" colspan="2" class="style16"><font face="Arial">Location</font></td>
         <td  align="center" colspan="2" class="style16"><font face="Arial">Commodity</font></td>

   <td  align="center" colspan="2" class="style16"><font face="Arial">Description</font></td>
   <td  align="center" colspan="2" class="style16"><font face="Arial">Qty</font></td>
   <td  align="center" colspan="2" class="style16"><font face="Arial">UOM</font></td>

      </tr>
           <%  ii = 0
       while not MyRec.Eof
    
    %>
<tr>
      
      <td  align="left" colspan="2" class="style16"><font face="Arial"><%= strSAPDate %></font></td>
         <td  align="left" colspan="2" class="style16"><font face="Arial"><%= MyRec.fields("SAP_nbr") %></font></td>
      <td  align="center" colspan="2" class="style16"><font face="Arial"><%= MyRec.fields("Location") %></font></td>
         <td  align="center" colspan="2" class="style16"><font face="Arial"><%= MyRec.fields("Commodity") %></font></td>
   <td  align="center" colspan="2" class="style16"><font face="Arial"><%= MyRec.fields("Description") %></font></td>
   <td  align="center" colspan="2" class="style16"><font face="Arial"><%= round(MyRec.fields("Value"),0) %></font></td>
   <td  align="center" colspan="2" class="style16"><font face="Arial"><%= MyRec.fields("UOM") %></font></td>

</tr>
<%   MyRec.MoveNext
     Wend
     MyRec.close %>
</table>
<br>
<table width="50%" border="1">
     <tr bgcolor="#D9D9FF">
    

      <td  align="left" class="style16">Material #</td>     
      <td  align="left" class="style16">Commodity</td> 
<td  align="left" class="style16">Description</td> 
 <td  align="left" class="style16">Total</td><td  align="left" class="style16">UOM</td></tr>

<% strsql = "SELECT tblInventoryHistory.SAP_nbr, UOM, Description, Commodity, Sum(tblInventoryHistory.Value) AS SumOfValue "_
&" FROM tblInventoryHistory INNER JOIN tblReference ON tblInventoryHistory.SAP_nbr = tblReference.SAP "_
&" WHERE Rpt_date='" & strSAPdate & "' AND (tblReference.Commodity='OCC' Or "_
&" tblReference.Commodity='Recycle Fiber' Or tblReference.Commodity='Broke') AND "_
&"  (Location='PLP1' Or Location='PLP2' Or Location='PLP4' "_
&"  Or Location='PLP3'  Or Location='WAD1' Or Location='WAD2' "_
&"   Or Location='WAD3' Or Location='WAD4') GROUP BY tblInventoryHistory.SAP_nbr, Description, Commodity, UOM order by Commodity"
       Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionPolaris") 
   
   While not MyRec.eof %>
   <tr>         <td  align="left"  class="style16"><font face="Arial"><%= MyRec.fields("SAP_nbr") %></font></td>
   <td  align="left"  class="style16"><font face="Arial"><%= MyRec.fields("Commodity") %></font></td>
   <td  align="left"  class="style16"><font face="Arial"><%= MyRec.fields("Description") %></font></td>
      <td  align="left"  class="style16"><font face="Arial"><%= round(MyRec.fields("sumofValue"),0) %></font></td>
      <td  align="left"  class="style16"><font face="Arial"><%= MyRec.fields("UOM") %></font></td>
   
   
   </tr>
   
   <% MyRec.movenext
   wend
   MyRec.close
   %>
   </table>
   <br>
<table width="10%" border="1">
     <tr bgcolor="#D9D9FF">
    

      <td  align="left" class="style16">Location</td>      <td  align="left" class="style16">Total</td><td  align="left" class="style16">UOM</td></tr>

<% 


strsql = "SELECT tblInventoryHistory.Location, Sum(Case when UOM='TON' then[value]*1.1023 when UOM='T' then [Value] end) AS Expr1 "_
&" FROM tblInventoryHistory INNER JOIN tblReference ON tblInventoryHistory.SAP_nbr = tblReference.SAP "_
&" WHERE (((tblInventoryHistory.[Rpt_date])='" & strSAPdate & "') AND ((tblReference.Commodity)='OCC' "_
&" Or (tblReference.Commodity)='Recycle Fiber' Or (tblReference.Commodity)='Broke') AND ((tblInventoryHistory.[Location])='PLP1' "_
&"  Or (tblInventoryHistory.[Location])='PLP2' Or (tblInventoryHistory.[Location])='PLP4' Or (tblInventoryHistory.[Location])='PLP3' "_
&"  Or (tblInventoryHistory.[Location])='WAD1' Or (tblInventoryHistory.[Location])='WAD2' "_
&"  Or (tblInventoryHistory.[Location])='WAD3' Or (tblInventoryHistory.[Location])='WAD4')) GROUP BY tblInventoryHistory.Location"


       Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionPolaris") 
   
   While not MyRec.eof %>
   <tr>         <td  align="left"  class="style16"><font face="Arial"><%= MyRec.fields("Location") %></font></td>
      <td  align="left"  class="style16"><font face="Arial"><%= round(MyRec.fields("Expr1"),0) %></font></td>
        <td  align="Center"  class="style16"><font face="Arial">T</font></td>
   </tr>
   
   <% MyRec.movenext
   wend
   MyRec.close
   %>