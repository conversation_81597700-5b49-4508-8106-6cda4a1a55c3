 

<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<meta http-equiv="REFRESH" content="60">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">
<TITLE>Inbound Load by Facility</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
 
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

<%  
	Dim strsQL, rstEquip, objGeneral, objNew, rstSpecies, strSpecies, MyRec, strCommodity, strDigit


 	Dim  strdate, strDelDate, strcount, strPO, ii


strdate = formatdatetime(now(),2)

   set objGeneral = new ASP_CLS_General
   


   
  strsql = "SELECT Count(tbl_Inbound_Shipment_Data.ord_id) AS CountOford_id, left(po_release_nbr,1) as Species, dateadd(d, round(cast((transit_miles * 1.0 / 500) as decimal(6,2)),0), pickup_dte) as Delivery_date "_
  &" FROM tbl_Inbound_Shipment_Data "_
			&" WHERE [shipto_city_nme]='MOBILE' and Commodity_desc = 'SECONDARY FIBER'"_
			
			&"GROUP BY dateadd(d, round(cast((transit_miles * 1.0 / 500) as decimal(6,2)),0), pickup_dte), left(po_release_nbr,1) "_
			
			&" HAVING '" & strdate & "' <= dateadd(d, round(cast((transit_miles * 1.0 / 500) as decimal(6,2)),0), pickup_dte) "_
			&" ORDER BY dateadd(d, round(cast((transit_miles * 1.0 / 500) as decimal(6,2)),0), pickup_dte), left(po_release_nbr,1)"



   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString2") 




   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString2") 

  
  %>
 

</head>


<table border="0" cellpadding="0" cellspacing="0" width="100%" >


		 
      <tr class="tableheader"> 
      <td  valign = "top" width = 27%><font face = arial size = 2>  
  
  <b>Count by Secondary Fiber Species</b></font></b><font face = arial size = 1><br>
      <Table width = 100% align = center><tr>
      <tr class="tableheader">
      <td  align="left"><font face="Arial" size = 1>Date</font></td>
       <td  align="center"><font face="Arial" size = 1>Species</font></td>
      <td  align="center"><font face="Arial" size = 1>Count</font></td>
      
  	<% 
    
       ii = 0
       while not MyRec.Eof
    
    %>
    <% if ( ii mod 2) = 0 Then %>
        <tr bgcolor=#FAFEC7>
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
         <td  align="left"><font face="Arial" size = 1>     <%= WeekDayName(weekday(MyRec.fields("Delivery_date")), true) & " " &  MonthName(month(MyRec.fields("Delivery_date")), true) & " " & Day(MyRec.fields("Delivery_date")) %>
&nbsp;</font></td>
     
          <td  align="left"><font face="Arial" size = 1>
          <%        
          if MyRec.fields("species") = "2" then %>
              OCC
              <% elseif MyRec.fields("species") = "3" then %>  
               KCOP
              <% elseif MyRec.fields("species") = "4" then %>  
             SWL
              <% elseif MyRec.fields("species") = "5" then %>  
              PMX
               <% elseif MyRec.fields("species") = "6" then %>  
             IGS
                 <% elseif MyRec.fields("species") = "7" then %>  
             SBS
                  <% elseif MyRec.fields("species") = "8" then %>  
            WLS 
                <% elseif MyRec.fields("species") = "9" then %>  
          OF <% else %>
            OTHER <% end if %>
          
          &nbsp;</font></td>
       
     <td  align="center"><font face="Arial" size = 1><%= MyRec.fields("countoford_id")%>&nbsp;</font></td>

     

   </tr>
    <% 
       ii = ii + 1
     
       MyRec.MoveNext
     Wend
    %>
   </table>


<%
MyRec.close

 %> 
</td><td valign = "top" width = "2%" bgcolor = #C8C8C8>&nbsp;</td>
<td valign = "top" width = 13% >
<% 
   
  strsql = "SELECT Count(tbl_Inbound_Shipment_Data.ord_id) AS CountOford_id, left(po_release_nbr,1) as Species, dateadd(d, round(cast((transit_miles * 1.0 / 500) as decimal(6,2)),0), pickup_dte) as Delivery_date "_
  &" FROM tbl_Inbound_Shipment_Data "_
			&" WHERE [shipto_city_nme]='MOBILE' and Commodity_desc = 'SECONDARY FIBER'"_
			
			&"GROUP BY dateadd(d, round(cast((transit_miles * 1.0 / 500) as decimal(6,2)),0), pickup_dte), left(po_release_nbr,1) "_
			
			&" HAVING '" & strdate & "' <= dateadd(d, round(cast((transit_miles * 1.0 / 500) as decimal(6,2)),0), pickup_dte) "_
			&" and (left(po_release_nbr,1) = '3' or  left(po_release_nbr,1) = '4'  or left(po_release_nbr,1) = '5' "_
				&" or left(po_release_nbr,1) = '6' or  left(po_release_nbr,1) = '7'  or left(po_release_nbr,1) = '9') "_
			&" ORDER BY dateadd(d, round(cast((transit_miles * 1.0 / 500) as decimal(6,2)),0), pickup_dte), left(po_release_nbr,1)"



   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString2") 


  
  %>   <TABLE >
  
  <font face = arial size = 2>  
  
  <b>Count of Inbounds to RF</b></font><br>


 
      <tr class="tableheader">
     <td  align="left"><font face="Arial" size = 1>Date</font></td>
       <td  align="center"><font face="Arial" size = 1>Species</font></td>
      <td  align="center"><font face="Arial" size = 1>Count</font></td>
      
  	<% 
    
       ii = 0
       while not MyRec.Eof
    
    %>
    <% if ( ii mod 2) = 0 Then %>
     <tr bgcolor=#DFF9EA>
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
         <td  align="left"><font face="Arial" size = 1>     <%= WeekDayName(weekday(MyRec.fields("Delivery_date")), true) & " " &  MonthName(month(MyRec.fields("Delivery_date")), true) & " " & Day(MyRec.fields("Delivery_date")) %>
&nbsp;</font></td>
     
          <td  align="left"><font face="Arial" size = 1>
          <%        
          if MyRec.fields("species") = "3" then %>  
               KCOP
              <% elseif MyRec.fields("species") = "4" then %>  
             SWL
              <% elseif MyRec.fields("species") = "5" then %>  
              PMX
               <% elseif MyRec.fields("species") = "6" then %>  
             IGS
                 <% elseif MyRec.fields("species") = "7" then %>  
            SBS
                  <% elseif MyRec.fields("species") = "9" then %>  
            OF <% end if %>
          
          &nbsp;</font></td>
       
     <td  align="center"><font face="Arial" size = 1><%= MyRec.fields("countoford_id")%>&nbsp;</font></td>

     

   </tr>
    <% 
       ii = ii + 1
     
       MyRec.MoveNext
     Wend
    %>
   </table>


<%
MyRec.close

 %> 

</td><td  valign = "top" width = "2%" bgcolor=#C8C8C8></td>
  <td  valign = "top" width = 15%>
  <table width = 100%>
 <% 
   
  strsql = "SELECT Count(tbl_Inbound_Shipment_Data.ord_id) AS CountOford_id, left(po_release_nbr,1) as Species, dateadd(d, round(cast((transit_miles * 1.0 / 500) as decimal(6,2)),0), pickup_dte) as Delivery_date "_
  &" FROM tbl_Inbound_Shipment_Data "_
			&" WHERE [shipto_city_nme]='MOBILE' and Commodity_desc = 'SECONDARY FIBER' "_
			
			&"GROUP BY dateadd(d, round(cast((transit_miles * 1.0 / 500) as decimal(6,2)),0), pickup_dte), left(po_release_nbr,1) "_
			
			&" HAVING '" & strdate & "' <= dateadd(d, round(cast((transit_miles * 1.0 / 500) as decimal(6,2)),0), pickup_dte) "_
				&" and left(po_release_nbr,1) = '2' "_
			&" ORDER BY dateadd(d, round(cast((transit_miles * 1.0 / 500) as decimal(6,2)),0), pickup_dte)"



   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString2") 



  
  %>  
  
  <b><font face = arial size = 2>Count of Inbounds to OCC</font></b><br>
 
      <tr class="tableheader">
      <td  align="left"><font face="Arial" size = 1>Date</font></td>
       <td  align="center"><font face="Arial" size = 1>Species</font></td>
      <td  align="center"><font face="Arial" size = 1>Count</font></td>
      
  	<% 
    
       ii = 0
       while not MyRec.Eof
    
    %>
    <% if ( ii mod 2) = 0 Then %>
         <tr bgcolor=#FFEADD  >
        
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
         <td  align="left"><font face="Arial" size = 1>     <%= WeekDayName(weekday(MyRec.fields("Delivery_date")), true) & " " &  MonthName(month(MyRec.fields("Delivery_date")), true) & " " & Day(MyRec.fields("Delivery_date")) %>
&nbsp;</font></td>
     
          <td  align="left"><font face="Arial" size = 1>
   
               OCC
   
          
          &nbsp;</font></td>
       
     <td  align="center"><font face="Arial" size = 1><%= MyRec.fields("countoford_id")%>&nbsp;</font></td>

     

   </tr>
    <% 
       ii = ii + 1
     
       MyRec.MoveNext
     Wend
    %>
   </table>


<%
MyRec.close

 %> 
  
  

  
  <td  valign = "top" width = "2%" bgcolor=#C8C8C8></td>
  <td  valign = "top" width = 12%>
 
   <% 
   
  strsql = "SELECT Count(tbl_Inbound_Shipment_Data.ord_id) AS CountOford_id, left(po_release_nbr,1) as Species, dateadd(d, round(cast((transit_miles * 1.0 / 500) as decimal(6,2)),0), pickup_dte) as Delivery_date "_
  &" FROM tbl_Inbound_Shipment_Data "_
			&" WHERE [shipto_city_nme]='MOBILE' and Commodity_desc = 'SECONDARY FIBER' "_
			
			&"GROUP BY dateadd(d, round(cast((transit_miles * 1.0 / 500) as decimal(6,2)),0), pickup_dte), left(po_release_nbr,1) "_
			
			&" HAVING '" & strdate & "' <= dateadd(d, round(cast((transit_miles * 1.0 / 500) as decimal(6,2)),0), pickup_dte) "_
			&" and left(po_release_nbr,1) = '8' "_
			&" ORDER BY dateadd(d, round(cast((transit_miles * 1.0 / 500) as decimal(6,2)),0), pickup_dte), left(po_release_nbr,1)"



   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString2") 



  
  %>  
 <table width = 100%>
  
  <font face = arial size = 2><b>Count of Inbounds to DF </b></font><br>


 
      <tr class="tableheader">
     <td  align="left"><font face="Arial" size = 1>Date</font></td>
       <td  align="center"><font face="Arial" size = 1>Species</font></td>
      <td  align="center"><font face="Arial" size = 1>Count</font></td>
      
  	<% 
    
       ii = 0
       while not MyRec.Eof
    
    %>
    <% if ( ii mod 2) = 0 Then %>
        <tr bgcolor=#FAFEC7>
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
         <td  align="left"><font face="Arial" size = 1>     <%= WeekDayName(weekday(MyRec.fields("Delivery_date")), true) & " " &  MonthName(month(MyRec.fields("Delivery_date")), true) & " " & Day(MyRec.fields("Delivery_date")) %>
&nbsp;</font></td>
     
          <td  align="left"><font face="Arial" size = 1>
        WLS         
          &nbsp;</font></td>
       
     <td  align="center"><font face="Arial" size = 1><%= MyRec.fields("countoford_id")%>&nbsp;</font></td>

     

   </tr>
    <% 
       ii = ii + 1
     
       MyRec.MoveNext
     Wend
    %>
   </table>


<%
MyRec.close

 %> 
  
</td>  
  </tr></table>

