<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Receipt Edit</title>
<style type="text/css">
.style1 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.style2 {
	text-align: right;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID, objMOC, rstFiber, strCarrier, objGeneral
      
    Dim strTrailer
    Dim strReleaseNbr
    Dim strRECNbr
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strGenerator
    Dim strGenCity
    Dim strGenState
    Dim strOther, strR, strsql3, strSpecies, MyRec2, strsql2, rstSpecies, strBales

  strId  = Trim(Request.QueryString("id")) 


 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
 
		Call SaveData() 
	

	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to edit a Receipt.</font></br>")
	MyRec.close
	end if

  
ELSE


strsql = "SELECT tblCars.* from tblCars WHERE tblCars.CID = " & strid

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


    strTrailer = MyRec.fields("Trailer")
    strReleaseNbr = MyRec.fields("Release_nbr")
    strRECNbr = MyRec.fields("rec_number")
    strGrossWeight = MyRec.fields("Gross_weight")
     strTareWeight = MyRec.fields("Tare_weight")
     strTonsReceived = MyRec.fields("Tons_Received")
    strDateReceived = MyRec.fields("Date_received")
    strGenerator = MyRec.fields("Generator")
    strGenCity = MyRec.fields("Gen_City")
     strGenState = MyRec.fields("Gen_state")
    strOther = MyRec.fields("OTher_comments")
    strCarrier = MyRec.fields("Carrier")
    strR = left(strReleaseNbr,1)
    strBales = MyRec.fields("Bales_VF")
    strGrade = MyRec.fields("Grade")
    strFormat = MyRec("Format_Pkg")
    
    strSpecies = MyRec.fields("Species")
    strDateUnloaded = MyRec("Date_unloaded")
    strInventory = MyRec("Inv_Depletion_Date")
    strLocation = MyRec("Location")

MyRec.close
Call getdata()
	end if

%>



<body>
<table width = 100%><tr><td width = 33%></td><td align = center width = 34%><b><font face="Arial" size="4">
Edit Trailer Receipt</font> </b></td><td align = right width = 33%><a href="Sixty_Day list.asp"><b><font face = arial size = 2>RETURN</a></font></b></td></tr></table>




<form name="form1" action="TruckReceiptYardEdit_sys.asp?id=<%=strid%>&r=<%= strR %>" method="post">
<input type="hidden" name="Location_Org" value="<%= strLocation %>">
<div align="center">
<table border="1" cellspacing="0" bordercolor="#C0C0C0" width="60%" bgcolor="#EAF1FF" style="border-collapse: collapse" cellpadding="0">
<tr>
    <td bgcolor="#EAF1FF">&nbsp;</td>

    <td  bgcolor="#EAF1FF">&nbsp;</td>
  </tr>
  <tr>
    <td  align = right bgcolor="#EAF1FF" >
   <b>
   <font face="Arial" size="2">Trailer:&nbsp;</font></b></td>
<td  align = left bgcolor="#EAF1FF">

      <input type="text" name="Trailer" size="15" value = "<%= strTrailer%>" tabindex="1"></td></tr>

  </tr>
  <tr>

      <td  bgcolor="#EAF1FF" align = right>
  <font face="Arial" size="2"><b>Receipt Number:&nbsp;</b></font></td>
<td  align = left bgcolor="#EAF1FF">

      <input type="text" name="REC_Number" size="15" value = "<%= strRECNbr%>" tabindex="2"></td></tr>
<tr>
    <td  bgcolor="#EAF1FF">  
	<p align="right">  <font face="Arial" size="2"><b>Select Carrier:&nbsp;</b></font></td>
    <td bgcolor="#EAF1FF">   <select name="Carrier" tabindex="3">
 	<option value="" selected>  Select Carrier (Required)</option>
      <%= objGeneral.OptionListAsString(rstFiber, "Carrier", "CDesc", strCarrier) %>
     </select></td>
  </tr><tr><td  align="right" bgcolor="#EAF1FF">  <font face="Arial" size="2"><b>Species:&nbsp;</b></font&nbsp;</td>

    <td bgcolor="#EAF1FF">
     <select size="1" name="Species" tabindex="4">
     <option value="">---Select ---</option>
<% if strGrade = "BROKE" then %>

    <option <% if strSpecies = "BROKE" then %> selected <% end if %>>BROKE</option>
    <option <% if strSpecies = "Tissue Broke" then %> selected <% end if %>>Tissue Broke</option>
     <option <% if strSpecies = "Towel Broke" then %> selected <% end if %>>Towel Broke</option>
      <option <% if strSpecies = "Brown Towel Broke" then %> selected <% end if %>>Brown Towel Broke</option>
     <% else %>
       <%= objGeneral.OptionListAsString(rstSpecies, "Fiber_species", "Fiber_species", strSpecies) %>
       <% end if %>   
     </select> &nbsp;</td>
  </tr>
   
<tr>
    <td  bgcolor="#EAF1FF">&nbsp;</td>

    <td  bgcolor="#EAF1FF">&nbsp;</td>
  </tr>

  <tr>
      <td  bgcolor="#EAF1FF" colspan="2" class="style1"><strong>Enter Either 
		Tons Received or Gross and Tare </strong></td>

  </tr>

<% If left(strReleaseNbr,1) = "2" or left(strReleaseNbr,1) = "C" then %>
<% else %>
       <tr>
    <td align = right bgcolor="#EAF1FF" >
   <font face="Arial" size="2"><b>Gross Weight:&nbsp;</b></font></td>
<td align = left bgcolor="#EAF1FF">

      <input type="text" name="Gross_Weight" size="15" value = "<%= strGrossWeight%>" style="width: 90px" tabindex="5"></td></tr>
      <tr>
          <td  bgcolor="#EAF1FF" align=right>
   <font face="Arial" size="2"><b>Tare Weight:&nbsp;</b></font></td>
<td align = left bgcolor="#EAF1FF">

      <input type="text" name="Tare_Weight" size="15" value = "<%= strTareWeight%>" style="width: 88px" tabindex="6">

</td></tr>

<% end if %>

 
        <tr>
          <td  bgcolor="#EAF1FF" align = right>
   <font face="Arial" size="2"><b>Tons Received:&nbsp;</b></font></td>
<td align = left bgcolor="#EAF1FF">
<font face="Arial" size="2">
      <input type="text" name="Tons_Received" size="15" value = "<%= strTonsReceived%>" style="width: 88px" tabindex="7">&nbsp;

</td></tr>

 
<tr>
    <td  bgcolor="#EAF1FF" class="style2"><font face="Arial" size="2"><b>VF Bales:</b></font></td>

    <td  bgcolor="#EAF1FF">
<font face="Arial" size="2">
      <input type="text" name="Bales" size="15" value = "<%= strBales%>" style="width: 88px" tabindex="7"></td>
  </tr>
  
    <tr>
    <td  bgcolor="#EAF1FF" class="style2"><font face="Arial" size="2"><b>RF Bales:</b></font></td>

    <td  bgcolor="#EAF1FF">
<font face="Arial" size="2">
      <input type="text" name="BalesRF" size="15" value = "<%= strBalesRF%>" style="width: 88px" tabindex="7"></td>
    </tr>
  
     <tr>
    <td  bgcolor="#EAF1FF" class="style2"><font face="Arial" size="2"><b>Format:</b></font></td>

    <td  bgcolor="#EAF1FF">
    <select name="Format" style="font-weight: 700" size="1">

	<option selected value="">  Select</option>
 	<option  <% if strFormat = "Bales" then %> selected <% end if %>>  Bales</option>
<option  <% if strFormat = "Rolls" then %> selected <% end if %>>Rolls</option>
<option <% if strFormat = "Gaylords" then %> selected <% end if %>>Gaylords</option>

 	</select>
</td>
    </tr>

  <tr>
    <td  bgcolor="#EAF1FF" class="style2"><font face="Arial" size="2"><b>&nbsp;</b></font></td>

    <td  bgcolor="#EAF1FF">
<font face="Arial" size="2">
      &nbsp;</td>
  </tr>

       <tr>
          <td  align = right bgcolor="#EAF1FF" >
    <font face="Arial" size="2"><b>Date Received:&nbsp;</b></font></td>
<td align = left bgcolor="#EAF1FF">

      <input type="text" name="Date_Received" size="15" value = "<%= strDateReceived%>" tabindex="8"></td></tr>

       <tr>
          <td  align = right bgcolor="#EAF1FF" class="style1" >
    	  <strong>Date/Time Unloaded: </strong></td>
<td align = left bgcolor="#EAF1FF">

      <input type="text" name="Date_Unloaded" size="25" value = "<%= strDateUnloaded%>" tabindex="9" ></td></tr>

       <tr>
          <td  align = right bgcolor="#EAF1FF" class="style1" >
    	  <strong>Inventory Depletion Date</strong>: </td>
<td align = left bgcolor="#EAF1FF">

      <input type="text" name="Inventory" size="25" value = "<%= strInventory %>" tabindex="10" ></td></tr>

       <tr>
          <td  align = right bgcolor="#EAF1FF" class="style1" >
    	  <strong>Location Unloaded:</strong> </td>
<td align = left bgcolor="#EAF1FF">

      <input type="text" name="Location" size="25" value = "<%= strLocation %>" tabindex="11"  ></td></tr>

       <tr>
          <td  align = right bgcolor="#EAF1FF" class="style1" >
    	  &nbsp;</td>
<td align = left bgcolor="#EAF1FF">

      &nbsp;</td></tr>
              <tr>
          <td  align = right bgcolor="#EAF1FF" >
   <font face="Arial" size="2"><b>Generator:&nbsp;</b></font></td>
<td align = left bgcolor="#EAF1FF">
      <input type="text" name="Generator" size="25" value = "<%= strGenerator %>" tabindex="12"></TD></tr>
        <tr>
          <td  align = right bgcolor="#EAF1FF" >
    <font face="Arial" size="2"><b>Generator City:&nbsp;</b></font></td>
<td align = left bgcolor="#EAF1FF">
      <input type="text" name="Gen_City" size="15" value ="<%= strGenCity%>" tabindex="13"></td></tr>

           <tr>
          <td align = right bgcolor="#EAF1FF" >
   <font face="Arial" size="2"><b>Generator State:&nbsp;</b></font></td>
<td align = left bgcolor="#EAF1FF">
      <input type="text" name="Gen_State" size="15" value = "<%= strGenState%>" tabindex="14"></td></tr>
         <tr>
          <td  align = right bgcolor="#EAF1FF" >
  <font face="Arial" size="2"><b>Other:&nbsp;</b></font></td >
   <td align = left bgcolor="#EAF1FF">   
	<input type="text" name="Other_Comments" size="25" value = "<%= strOther%>" tabindex="15" style="width: 592px"></td></tr>
<tr>
    <td  bgcolor="#EAF1FF">&nbsp;</td>

    <td bgcolor="#EAF1FF">&nbsp;</td>
  </tr>

  <tr>
    <td bgcolor="#EAF1FF" height="34">&nbsp;</td>

    <td align = left bgcolor="#EAF1FF" height="34"><Input name="Update" type="submit" Value="Submit" ></td>
  </tr>
</table>

</div>

</form>
</body>
<%
 Function GetData()
  set objMOC = new ASP_CLS_FIBER
  set rstFiber = objMOC.FiberCarrier()
    set rstSpecies = objMOC.VFSpecies()

End Function


Function SaveData()
strid = request.querystring("id")
strTrailer = Request.form("Trailer")
strCarrier = Replace(Request.form("Carrier"), "'", "''")
strOther = Replace(Request.form("Other_Comments"), "'", "''") 
strDateReceived = Request.form("Date_received")
strRECNbr = Request.form("Rec_Number")
strSpecies = Request.form("Species")
strGenerator = Replace(Request.form("Generator"), "'", "''")
strGenCity = Replace(Request.form("Gen_City"), "'", "''")
strGenState = Request.form("Gen_State")
If len(Request.form("Tons_received")) > 0 then
strTonsReceived = Request.form("Tons_received")





         strSql = "Update tblCars Set Carrier = '" & strCarrier & "', Other_Comments = '" & strOther & "', Trailer = '" & strTrailer & "', "_
         &" Date_Received = '" & strDateReceived & "', Species = '" & strSpecies & "', "_
         &" Rec_Number = '" & strRECNbr & "',  format_Pkg = '" & request.form("Format") & "', "_
    
         &" Tons_Received = " & strTonsReceived & ", Net = " & strTonsReceived & ", "_
         &" Generator = '" & strGenerator & "', "_
         &" Gen_City = '" & strGenCity & "', "_
         &" Gen_State = '" & strGenState & "' where CID = " & strid & ""
else
If len(request.form("Gross_Weight")) > 0 then
strGrossWeight = Request.form("Gross_Weight")
else
strGrossWeight = 0
end if
if len(request.form("Tare_Weight")) > 0 then 
strTareWeight = Request.form("Tare_Weight")
else
strTareWeight = 0
end if
if strgrossweight = 0 or strTareweight = 0 then
strTonsReceived = 0
else
strTonsReceived = round((strgrossweight - strTareweight)/2000,3)
end if

         strSql = "Update tblCars Set Carrier = '" & strCarrier & "', Other_Comments = '" & strOther & "', Trailer = '" & strTrailer & "', "_
         &" Date_Received = '" & strDateReceived & "', Species = '" & strSpecies & "', "_
         &" Rec_Number = '" & strRECNbr & "',   format_Pkg = '" & request.form("Format") & "', "_

       
         &" Gross_Weight = " & strGrossWeight & ", "_
         &" Tare_Weight = " & strTareWeight & ", "_
         &" Tons_Received = " & strTonsReceived & ", Net = " & strTonsReceived & ", "_
         &" Generator = '" & strGenerator & "', "_
         &" Gen_City = '" & strGenCity & "', "_
         &" Gen_State = '" & strGenState & "' where CID = " & strid & ""

end if
         
 	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
         
         If len(Request.form("Bales")) > 0 then
         strBales = Request.form("Bales")
            strSql = "Update tblCars Set Bales_VF = " & strBales & " where CID = " & strid & ""
 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 

end if

         If len(Request.form("BalesRF")) > 0 then
         strBalesRF = Request.form("BalesRF")
            strSql = "Update tblCars Set Bales_RF = " & strBalesRF & " where CID = " & strid & ""
 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 

end if

   If len(request.form("Date_Unloaded")) > 3 then
   strsql = "UPdate tblCars set Date_Unloaded = '" & request.form("Date_Unloaded") & "' where     CID = " & strid & "" 
   set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
   end if
   
     If len(request.form("Inventory")) > 3 then
   strsql = "UPdate tblCars set Inv_Depletion_date = '" & request.form("Inventory") & "' where     CID = " & strid & "" 
   set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
   end if
   
   IF request.form("Location") <> request.form("Location_Org") then 
      strsql = "UPdate tblCars set Location = '" & request.form("Location") & "' where     CID = " & strid & "" 
   set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
strnow = formatdatetime(Now(),0)


strsql = "Insert into tblMovement (CID, DDate, TDate, From_Location, To_Location, BID, Comment) "_
 &" SELECT " & strid & ", '" & strnow & "', '" & strnow & "',  '" & request.form("Location_Org") & "', '" & request.form("Location")  & "', '" & Session("EmployeeID") & "' "_
 &" 'Trailer Edit Page'"
   end if

Response.redirect ("Sixty_Day list.asp")



End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->