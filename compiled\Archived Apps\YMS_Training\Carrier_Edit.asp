																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Edit Carrier </TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->


<% Dim MyRec, strsql, MyRec2, strsql2,  MyConn3, strsql3,  strFee

 

  set objGeneral = new ASP_CLS_General
  strid = Request.querystring("id")
  
  strsql = "Select * from tblCarrier where ID = " & strid
  
      Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

  

  if objGeneral.IsSubmit() Then	
  Dim strCarrier,  strEmail_add, strFree_Days
  
  	strCarrier = request.form("Carrier")
  	

  	
  	If len(request.form("C_Description")) > 0 then
  	strC_Description = request.form("C_Description")
  	else
  	strC_Description = ""
  	end if 
  	
  	If len(request.form("Free_Days")) > 0 then
  	strFree_Days = request.form("Free_Days")
  	else
  	strFree_Days = 0
  	end if
  	
  
  		
  	If len(request.form("Fee")) > 0 then
  	strFee= request.form("Fee")
  	else
  	strFee = 0
  	end if
 
  	
  If len(request.form("Email_add")) > 1 then	
 strEmail_add = Request.form("Email_add")
 else
 strEmail_add = ""
 end if 
 
        	
  	
	strsql =  "Update tblCarrier  set Carrier = '" & strCarrier & "',  C_Description = '" & strC_Description & "', Free_Days = " & strFree_Days & ", "_
	&" Fee = " & strFee & ", Email_add = '" & strEmail_add & "' where ID = " & strid
	
	
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
  if Session("EmployeeID") = "C97338" or Session("EmployeeID") = "B55404" or Session("EmployeeID") = "B55548" then  

			
if len(request.form("Weight")) > 0 then
  	
	strsql =  "Update tblCarrier  set Weight = " & Request.form("Weight") & " where ID = " & strid
	else
	  	
	strsql =  "Update tblCarrier  set Weight = Null where ID = " & strid

	end if
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

end if

 
Response.redirect("Carrier.asp")		
end if

	
%>

<style type="text/css">
.style3 {
	border: 1px solid #808080;
}
.style4 {
	font-family: arial;
	font-size: small;
}
.style6 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: arial;
	font-size: small;
	background-color: #EAF1FF;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style7 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	background-color: #EAF1FF;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style8 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
</style>
</head>

<body>
<br>
	
<form name="form1" action="Carrier_Edit.asp?id=<%= strid%>" method="post" ID="Form1">
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Edit Carrier </b></font></td>
<td align = right><font face="Arial"><a href="Carrier.asp">Return</a>&nbsp;</td>


</tr>
	    </table>



	<p>&nbsp;</p>
	<table id="table1" style="width: 90%" align="center" class="style3">
<tr bgcolor = #CCCCFF>
		<td bgcolor="#FFFFD7" class="style6" style="height: 42px">

Carrier</td>
		<td class="style6" style="height: 42px">

Carrier Description</td>
<% if Session("EmployeeID") = "C97338" or Session("EmployeeID") = "B55404" or Session("EmployeeID") = "B55548" then %>
<td class="style6" style="height: 42px">Trailer Weight </td>
<% end if %>




		<td class="style6" style="height: 42px">

Free Days</td>
		<td class="style7" style="height: 42px">

<p align="center" class="style4">Fee</td>
		<td bgcolor="#FFFFD7" class="style6" style="height: 42px">

Email Addresses (Separate by semi-colon)</td>
	</tr>
	<tr>
		<td class="style8"><font face = arial size = 1>
		<input type="text" name="Carrier" size="20" style="width: 81px" value='<%= MyRec.fields("Carrier") %>'></td>
		<td class="style8"><font face = arial size = 1>
		<input type="text" name="C_Description" size="20" value='<%= MyRec.fields("C_Description") %>' style="width: 235px"></td>
		<% if Session("EmployeeID") = "C97338" or Session("EmployeeID") = "B55404" or Session("EmployeeID") = "B55548" then %>
		<td class="style8"><font face = arial size = 1>
		<input type="text" name="Weight" size="20" style="width: 43px" value='<%= MyRec.fields("Weight") %>'></td>

<% end if %>
		<td class="style8"><font face = arial size = 1>
		<input type="text" name="Free_Days" size="20" style="width: 32px" value='<%= MyRec.fields("Free_Days") %>'></td>
		<td class="style8">
		<p align="center"><font face = arial size = 1>
<input type="text" name="Fee" size="39" value='<%= MyRec.fields("Fee") %>' style="width: 31px"></td>
		<td class="style8">
		<font face = arial size = 1>
<input type="text" name="Email_add" size="39" style="width: 768px" value='<%= MyRec.fields("Email_add") %>'></td>
	</tr>
</table>
<br>
		

<p align="center"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></p>
</form>
<% MyRec.close %><!--#include file="Fiberfooter.inc"-->