<html>
<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_Procedure.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 6.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Change Log</title>
<style>
<!--
table.MsoTableGrid
	{border:1.0pt solid windowtext;
	font-size:10.0pt;
	font-family:"Times New Roman";
	}
-->
</style>
</head>
<% dim strsql, MyRec, strid, strecp, strTask, objGeneral, strDescription, strLocation, strComments, strDate, strTeam, strAsset
Dim objEPS, rstTeam, rstWA, strArea, strWorkArea,  strSOP, strFunctional, strListone, strListtwo, strListthree, strType

			
Dim strBID, strE_name, strP_Date, strSpaceType, strcid, strCompartmentname
strpid = Request.querystring("pid")
			
strBID = Session("EmployeeID")
strE_name = Session("Ename")
strnow = dateadd("h", -5, now())
strP_date = formatdatetime(strnow,2)
strCompartmentname = Request.querystring("cn")

strid = Request.querystring("id")
strDate = formatdatetime(strnow,2)


set objGeneral = new ASP_CLS_General
  		 if objGeneral.IsSubmit = True Then
  		
 	strCompartmentName = Replace(Request.form("Portal_name"), "'", "''") 
if isnull(Request.form("Change_description")) then
strDescription = ""
else
strDescription = Replace(Request.form("Change_description"), "'", "''") 
end IF

 strsql =  "INSERT INTO tblChangeLog (Section, Portal_name, Change_name, Change_Bid, Change_date, SOP_NO, Change_Desc) "_
 &" SELECT '3', '" & strCompartmentName & "', '" & strE_name & "', '" & strBID & "', '" & strP_Date & "', '" & strid & "',  '" & strDescription & "'"
  Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 
			
			strsql = "Update tblHA set HA_Status = 'Change Proposal' where SpaceID = '" & strid & "'"
				MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 
			
				
		   Dim strWA
      strsql = "Select Workarea from tblSOP where SOP_NO = '" & strid & "'"
       Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
   		strWA = MyConn.fields("Workarea")
   		
   		MyConn.close 	
   		   Dim strFrom, strTo, strEmailTo, strEmailType,  objMail
			
	 strSQL = "SELECT tblApprovers.* from tblApprovers where Work_area = '" & strWA & "'"
      Set Myrec = Server.CreateObject("ADODB.Recordset") 
   		MyRec.Open strSQL, Session("ConnectionString")
   		 While not MyRec.eof 
   		 
			strBID = MyRec.fields("P_BID")

   

            strTo = strBID
            strEmailTo = ReturnEmail(strTo)
                 
			Set objMail = Server.CreateObject("KC.Message")		
			objMail.From = "<EMAIL>"
			objMail.To = strEmailTo
			objMail.Subject = "Hazard ID:  " & strid & " Section 3 Change."
         
			
			objMail.HTMLBody = "Here is a description of the change: <br>" & strDescription & "<br><br>You can use the link below to view the Approval History<br>http://www.dev.app.kcc.com/EvtSafetyLog/CSE/Change_History.asp?id=" & strid

			objMail.Send
			Set objMail = nothing
					
			MyRec.movenext
			wend
			MyRec.close



Response.redirect("Section3.asp?pid=" & strpid & "&id=" & strid)			

end if 

 %><body>
 <p align="left"><b><font face="arial">Description of Change to the Portal, Section 3</font><font face = arial size = 3>:</font></b><font face = arial size = 3>
 </font>
 </p>
	<form name="form1" action="Change_log_Portal.asp?id=<%= strid %>&pid=<%= strpid%>"  method="post" ID="Form1"  >
 <input type = "hidden" name = "Portal_name" value = <%= strCompartmentName%>
 <table border="1" cellpadding="0" style="border-collapse: collapse" align = center bordercolor="#111111" width="80%" bgcolor="#D9E1F9" height="60">
   
  <tr>
    <td bgcolor="#FFFFDD" align="center" bordercolordark="#000000" bordercolor="#FFFFDD">
	&nbsp;</td>

   
    <td bgcolor="#FFFFDD" align="center" bordercolordark="#000000" bordercolor="#FFFFDD" rowspan="2">
	<p align="center"><font face="Arial">Please describe the change you made for Space ID: <%= strid%></font></p>
	<p align="center">
		<textarea rows="6" name="Change_description" cols="72"></textarea><p align="center">
		&nbsp;</td>

   
  </tr>
  <tr>
    <td  bgcolor="#FFFFDD" bordercolor="#FFFFDD"  >
	<p align="center"> &nbsp;</td>
	
    </table> 

 


  <p>&nbsp;</p>

	<p align="center">&nbsp;<INPUT TYPE="submit" value="Continue"></p>
	</form>
<!--#include file="footer.inc"-->