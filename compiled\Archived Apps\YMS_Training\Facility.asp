																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Consumption Projections </TITLE>

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 



<% Dim MyRec, strsql, strstartdate
strStartdate = formatdatetime(now(),2)

strsql = "SELECT * from tblTempYardTotals where Inv_Date > '" & strStartdate & "' order by Inv_date"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<style type="text/css">
.style1 {
	border-color: #C0C0C0;
	border-width: 1px;
}
.style2 {
	font-weight: bold;
	border: 1px solid #C0C0C0;
}
.style3 {
	font-weight: bold;
	font-family: Arial;
	font-size: x-small;
	text-align: center;
	border: 1px solid #C0C0C0;
}
.style4 {
	font-weight: bold;
	text-align: center;
	border: 1px solid #C0C0C0;
}
.style5 {
	border: 1px solid #000000;
}
.style6 {
	border: 1px solid #C0C0C0;
	background-color: #FFFFFF;
}
.style7 {
	border: 1px solid #C0C0C0;
	text-align: center;
	background-color: #FFFFFF;
}
.style8 {
	font-family: Arial;
	font-size: x-small;
}
.style9 {
	font-weight: bold;
	text-align: center;
	border: 1px solid #C0C0C0;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
</style>
</head>

<body>

<% if Session("EmployeeID") = "C97338" or Session("EmployeeID") = "B96138" or Session("EmployeeID") = "B48888" or Session("EmployeeID") = "B55548" or Session("EmployeeID") = "C66556" or Session("EmployeeID") = "B53909" or Session("EmployeeID") = "B99660" then %>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>


<td align = left><b><font face="Arial">Consumption Projections for RF Inventory</font></b></td>
<td align = right><b><font face="Arial"><a href="Facility_default.asp">Change Defaults</a></font></b></td>

<td align = right><font face="Arial"><a href="Facility_add.asp"><b>Add New</b></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </td>


</tr>
	    </table>
	<br><br>
	
	<TABLE cellSpacing=0 cellPadding=0 class = "style5" align = center style="width: 50%">  
	 <tr class="tableheader">
<td class="style1" rowspan="2">&nbsp;</td>

				<td  align = left class="style2" rowspan="2">     <font face="Arial" size="2">	Inventory Date</font></td>
		 
				<td class="style3" rowspan="2"  >KBLD</td>

				<td class="style3" rowspan="2"  >OF3</td>

				<td class="style3" colspan="4"  >&nbsp;&nbsp;&nbsp; KCOP</td>

				<td class="style3" colspan="3"  >&nbsp;&nbsp; OF</td>

			<td class="style4"  ><font face="Arial" size="2">PMX</font></td>

			<td class="style9" colspan="2"  >SHRED</td>

			<td class="style9"  >HBX</td>
		<td class="style3" colspan="2">  &nbsp; OCC</td>
		<td class="style3" colspan="2">  &nbsp; RFF & OF</td>

	</tr>

	 <tr class="tableheader">
		 
				<td class="style4" style="height: 24px"  ><font face="Arial" size="2">1-A</font></td>

		<td class="style4" style="height: 24px"  ><font face="Arial" size="2">1-B</font></td>

		<td class="style4" style="height: 24px; width: 22px"  ><font face="Arial" size="2">2</font></td>
		<td class="style4" style="height: 24px"  ><font face="Arial" size="2">T-5</font></td>
 
				<td class="style4" style="height: 24px"  ><font face="Arial" size="2">1</font><span class="style8">
				</span></td>

		<td class="style3" style="height: 24px"  >2</td>

		<td class="style4" style="height: 24px; width: 26px"  ><font face="Arial" size="2">3</font></td>

			<td class="style4" style="height: 24px"  ><font face="Arial" size="2">PMX</font></td>

			<td class="style9" style="height: 24px"  >1</td>

			<td class="style9" style="height: 24px"  >T5</td>

			<td class="style9" style="height: 24px"  >HBX</td>
		<td class="style4" style="height: 24px">   <font face="Arial" size="2">OCC </font></td>
				<td class="style4" style="height: 24px">   <font face="Arial" size="2">MXP </font></td>
						<td class="style4" style="height: 24px">   <font face="Arial" size="2">RFF</font></td>
				<td class="style4" style="height: 24px">   <font face="Arial" size="2">OF </font></td>

	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td class="style6"> <font size="2" face="Arial">

<a href="Facility_edit.asp?id=<%= MyRec.fields("ID") %>">

Edit</a></td>

	<td class="style6"  ><font size="2" face="Arial"><%= MyRec.fields("INV_Date")%></font></td>
	
<td class="style7"  ><font size="2" face="Arial"><%= MyRec.fields("KBLD")%>&nbsp;</font></td>
	
<td class="style7"  ><font size="2" face="Arial"><%= MyRec.fields("OF3")%>&nbsp;</font></td>
	
<td class="style7"  ><font size="2" face="Arial"><%= MyRec.fields("KCOP_Tier1")%>&nbsp;</font></td>
<td class="style7"  ><font size="2" face="Arial"><%= MyRec.fields("KCOP_Tier2")%>&nbsp;</font></td>
	<td class="style7" style="width: 22px"  ><font size="2" face="Arial"><%= MyRec.fields("KCOP_Tier3")%>&nbsp;</font></td>
<td class="style7"  ><font size="2" face="Arial"><%= MyRec.fields("KCOP_Tier4")%>&nbsp;</font></td>
<td class="style7"  ><font size="2" face="Arial"><%= MyRec.fields("OF_Tier1")%>&nbsp;</font></td>
<td class="style7"  ><font size="2" face="Arial"><%= MyRec.fields("OF_Tier2")%>&nbsp;</font></td>
	<td class="style7" style="width: 26px"  ><font size="2" face="Arial"><%= MyRec.fields("OF_Tier3")%>&nbsp;</font></td>
<td class="style7"  ><font size="2" face="Arial"><%= MyRec.fields("PMX")%>&nbsp;</font></td>

<td class="style7"  ><font size="2" face="Arial"><%= MyRec.fields("SHRED_T1")%>&nbsp;</font></td>
<td class="style7"  ><font size="2" face="Arial"><%= MyRec.fields("SHRED_T5")%>&nbsp;</font></td>
<td class="style7"  ><font size="2" face="Arial"><%= MyRec.fields("HBX")%>&nbsp;</font></td>
<td class="style7"  ><font size="2" face="Arial"><%= MyRec.fields("Total_OCC")%>&nbsp;</font></td>
<td class="style7"  ><font size="2" face="Arial"><%= MyRec.fields("Total_MXP")%>&nbsp;</font></td>
 
 <td class="style7"  ><font size="2" face="Arial"><%= MyRec.fields("Total_RF")%>&nbsp;</font></td>
<td class="style7"  ><font size="2" face="Arial"><%= MyRec.fields("Yard_KCOP")%>&nbsp;</font></td>
 


</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>
<% else %>
<p align="center"><font face="arial" size="3"><b>You do not have authorization to view this page</b></font></p>
<% end if %><!--#include file="Fiberfooter.inc"-->