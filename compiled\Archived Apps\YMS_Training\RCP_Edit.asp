																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Rail Target Edit</TITLE>
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->



<% Dim MyRec, strsql, strid, strOF
  Dim strdate, strRF, strOCC, strMXP
  
strid = request.querystring("id")

strsql = "Select * from tblRailTarget where ID = " & strid
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
strTarget = MyRec.fields("Target")
strdate = MyRec.fields("Inv_Date")
MyRec.close


  set objGeneral = new ASP_CLS_General
  if objGeneral.IsSubmit() Then	

  

  	
  	If len(request.form("Target")) > 0 then
  	strTarget = request.form("Target")
  	else
  	strTarget = 0
  	end if
  	

  	
   	 strdate = request.form("Date")
   	 
  
        	
  	
	strsql =  "Update tblRailTarget set INV_Date = '" & strDate & "', Target = " & strTarget & " where ID = " & strid

	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
Response.redirect("RC_Projections.asp")		
end if
	
%>

<style type="text/css">
.style2 {
	font-family: arial;
	font-size: x-small;
}
.style4 {
	border-style: solid;
	border-width: 1px;
}
</style>
</head>

<body>
<br>
	
<form name="form1" action="RCP_Edit.asp?id=<%= strid%>" method="post" ID="Form1">
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Modify Rail Car Target</b></font></td>
<td align = right><font face="Arial"><a href="RC_Projections.asp">Return</a>&nbsp;</td>


</tr>
	    </table>



	<p>&nbsp;</p>
	<table id="table1" style="width: 35%" class="style4" align="center">
		<tr bgcolor = #CCCCFF><td bgcolor="#FFFFD7" class="style2"> <strong>Date</strong></td>
		<td bgcolor="#FFFFD7" class="style2"><strong>Target</strong></td>
			
	</tr>
	<tr>
		<td><font face = arial size = 1>
		<input type="text" name="Date" size="20" style="width: 112px" value="<%= strDate%>"></td>
		<td><font face = arial size = 1>
		<input type="text" name="Target" size="20" style="width: 69px" value="<%= strTarget %>"></td>

	</tr>
</table>


<p align="center"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></p>
</form>
<!--#include file="Fiberfooter.inc"-->