﻿
<html>
<head>
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">

<meta name="ProgId" content="FrontPage.Editor.Document">

<title>Kimberly-Clark Mobile Stores Access Application </title>
<SCRIPT LANGUAGE="JavaScript1.2" SRC="scripts/menubar.js"></SCRIPT>
<link rel="stylesheet" href="scripts/KCStyle.css" type="text/css">

</head>

<p><img border="0" align="left" src="images/KC.png" height="25"> <font face=Arial>&nbsp;    <b>                |     &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Stores Access Application </b> 


</font> </p>

<body>

 
<div class="menuBar">
 
<a class="menuButton" href="Index.asp">HOME</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;

<a class="menuButton" href="" onclick="return buttonClick(event, 'menu2');" onmouseover="buttonMouseover(event, 'menu2');">&nbsp;&nbsp;Reports</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;

<a class="menuButton" href="" onclick="return buttonClick(event, 'menu1');" onmouseover="buttonMouseover(event, 'menu1');">&nbsp;&nbsp;Admin</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;

 
 
</div>

 
<div id="menu1" class="menu" onmouseover="menuMouseover(event)">
<a class="menuItem" href="Area_list.asp">Areas</a>
<a class="menuItem" href="Position_list.asp">Positions</a>
<a class="menuItem" href="P_list.asp">Personnel</a>

<a class="menuItem" href="T_list.asp">Training List</a>

</div>

<div id="menu2" class="menu" onmouseover="menuMouseover(event)">
<a class="menuItem" href="Rpt_Needs_Training.asp">People Needing Training</a>
 <a class="menuItem" href="Reader_list.asp">Reader List</a>

</div>

