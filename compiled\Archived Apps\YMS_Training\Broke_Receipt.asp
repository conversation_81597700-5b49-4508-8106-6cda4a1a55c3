<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 14.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Enter Broke Trailer Receipt</title>
<style type="text/css">
.style1 {
	font-size: x-small;
}
.style28 {
	border: 1px solid #000000;
	border-collapse: collapse;
		background-color: #EAF1FF;
}
.style29 {
	font-family: Arial;
	font-size: x-small;
}
.style3 {
	font-family: Arial, Helvetica, sans-serif;
}
.style31 {
	color: #000000;
	font-weight: bold;
}
.style32 {
	font-weight: bold;
}
.style33 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial, Helvetica, sans-serif;
		font-size: x-small;
		font-weight: bold;
		background-color: #EAF1FF;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style36 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	background-color: #EAF1FF;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style37 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-weight: bold;
	background-color: #EAF1FF;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style38 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	background-color: #EAF1FF;
		text-align: right;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style39 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.style40 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style41 {
	color: #000000;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID,  MyConn, objMOC, rstFiber, rstSpecies, strKCWeighed, strPounds  
    Dim strTrailer, strCarrier, strLocation, MyRec5, strsql5, stralert
    Dim rstTrailer , strTrailerWeight ,  strTrailerTID
 
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strOther, strsql3, strSpecies, strNet, strPO, strR, objGeneral

		strother = ""
	 
  set objGeneral = new ASP_CLS_General
if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
	
	

	strSpecies ="BROKE"
	
 	strTrailer = Request.form("Trailer")
	strOther = Replace(Request.form("Other_Comments"), "'", "''") 
	strDateReceived = Request.form("Date_received")
	strGrossweight = Request.form("Gross_Weight")
	strCarrier = Request.form("Carrier")
	strLocation = Request.form("Location")
	strTrailerTID = request.form("Trailer_option")
	strBalesRF = request.form("BalesRF")
		strCPounds = request.form("C_Pounds")
		strCTPounds = request.form("CT_Pounds")
	
	Call SaveData() 
	
	
	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to save this information.</font></br>")
	MyRec.close
	end if ' if you don't have authorization

end if ' if they did not submit
%>

<% if Request.querystring("n") = "T" then 
	Session("TrailerTID") = strTrailerTID
	Session("GrossWeight") = strGrossWeight
 
	Session("Trailer") = strTrailer
	Session("Carrier") = strCarrier
	Session("Other") = strOther
	Session("BalesRF") = strBalesRF
	Session("CPounds") = strCpounds
	Session("CTPounds") = strCTPounds
 %>
<p align = center><font face = arial size = 3 color = red><b>



<% if isnull(Session("Trailer")) or len(Session("Trailer"))< 1 then %>
You must enter a Trailer number.<br>
<% end if %>
<% if strCarrier = "" then %>
You must enter a Carrier</span><br>
<% end if %>

 </b></font>
<% else %>
&nbsp;
<% end if %>


<body>
<table width = 100%><tr><td width = 33%>
&nbsp;</td><td align = center width = 34%><b><font face="Arial" size="4">
Enter Broke Trailer Receipt </b></font> </b></td></tr></table>




<form name="form1" action="Broke_Receipt.asp?id=<%=strid%>" method="post">
<div align="center">
<table cellspacing="0"  style="width: 80%;" cellpadding="0" class="style28">

<tr>
    <td align="right" class="style37" style="height: 16px"><font face="Arial" size="2">&nbsp;</font></td>

    <td  align = center colspan="2" class="style37" style="height: 16px">&nbsp; 
	</td>
  </tr><tr>
    <td align="right" class="style37"><font face="Arial" size="2">Species:</font></td>

    <td class="style36">  
     <font face="Arial">
  	<span class="style1"><strong>&nbsp;BROKE</strong></span></td>

    <td style="width: 44%" class="style36"> 
	<p align="center"><font face="Arial"><font size="2" face="Arial"><b>Check 
	to Print Receipt:&nbsp;
</b></font> <input type="checkbox" name="Print_receipt" value="ON" checked></td>

  </tr>
  
    <tr>
    <td align="right" style="height: 23px" class="style33">
	Vendor Weight: </td>

    <td colspan="2" style="height: 23px" class="style36"> 

      <font face="Arial" size="4" color="teal">

      <span class="style3"><span class="style1">

      <input name="DOT" size="15" value = "<%= strDOT%>" style="width: 110px;" tabindex="1" class="style32"><span class="style31"> 
		lbs&nbsp; (Required)</span></span></span></font></td>
  </tr>
  
  <tr>
    <td  align = right height="29" class="style37" >
   <font face="Arial" size="2">Trailer:&nbsp;</font></td>
<td  align = left colspan="2" height="29" class="style36">

      <input type="text" name="Trailer" size="15" value="<%= strTrailer %>" tabindex="2" >&nbsp;
		<font face="Arial" size="2"><b>(Required)</b></font></td></tr>
  <tr>

      <td align = right style="height: 1px" class="style37">
	<font face="Arial" size="2">Select Carrier: </font></td>
<td  align = left colspan="2" style="height: 1px" class="style36">

      <select name="Carrier" tabindex="3">
 	<option value="" selected>  Select Carrier (Required)</option>
  	<% strsql = "Select Carrier,C_Description, Weight from tblCarrier order by Carrier"
 	   	 Set MyRec4 = Server.CreateObject("ADODB.Recordset")
   	 MyRec4.Open strSQL, Session("ConnectionString")
   	 while not MyRec4.eof %>
   	 <option <% if strCarrier = MyRec4("Carrier") then %> selected <% end if %> value="<%= MyRec4("Carrier") %>"><%= MyRec4("Carrier") %>&nbsp;<%= MyRec4("C_Description") %>&nbsp;<%= MyRec4("Weight") %></option>"

 <%MyRec4.Movenext
 wend
 MyRec4.close %>
     </select></td></tr>
<tr>
    <td class="style36">  
	<p align="right" class="style29">  <strong>S</strong><font size="2"><strong>elect 
	SAP #:</strong></font></td>
    <td colspan="2" class="style36"> 	
	<select name="SAP" style="font-weight: 700; width: 174px;" size="1" tabindex="4" >
 	<option selected value="">  Select</option>
      <% strsql = "Select tblBrokeSAP.* from tblBrokeSAP where category = 'BROKE'"
      
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    while not MyRec.eof

       %>
        <option <% if strSAP = MyRec("SAP") then %> selected <% end if %> value="<%= MyRec("SAP") %>" ><%= MyRec("SAP") %>-<%= MyRec("Type") %></option>
        <% MyRec.movenext
        wend
        MyRec.close %>
  
     </select>&nbsp;
  &nbsp;</td>
  </tr>
<tr>
	<td height="22" width="17%" class="style38"><span class="style39"><strong>
	Format</strong></span>:</td>
    <td colspan="2" height="22" class="style36"><font face = arial size = 4 color="teal">
    <select name="Format" style="font-weight: 700" size="1" tabindex="5">
 	<option selected value="">  Select</option>
 	<option   >Bales</option>
<option   >Rolls</option>
<option  >Gaylords</option>

 	</select><span class="style39"><strong>&nbsp;&nbsp;&nbsp;&nbsp;<span class="style41"> Bales:</span></strong></span>&nbsp;<input name="BalesRF" size="15" value = "<%= strBalesRF%>" style="width: 41px" tabindex="6"  >
 	
   	</td>
  </tr>

<tr>
	<td height="22" width="17%" class="style36">&nbsp;</td>
    <td colspan="2" height="22" class="style36">&nbsp;</td>
  </tr>

<tr>
	<td width="17%" class="style36" style="height: 51px">
	<p align="right"><font face="Arial" size="2"><strong>Scale Gross</strong>:</font></td>
    <td class="style36" style="width: 19%; height: 51px;">    <font face="Arial">  
	<strong><span class="style1">  
	<input name="Gross" size="15" value="<%= strGrossWeight%>" style="width: 105px" tabindex="7"></span>
	</strong> 
	<span class="style1"><strong>lbs</strong></span></td>
    <td class="style36" style="height: 51px">    
   <font face="Arial" size="2"  >  <strong><span class="style1">Scale out (Cab Only):&nbsp;
	<input name="C_Pounds" size="15" value = "<%= strCPounds%>" style="width: 75px" tabindex="8"  >lbs&nbsp;&nbsp;&nbsp; OR&nbsp;&nbsp;&nbsp;&nbsp; 
Scale out (Cab and Trailer):&nbsp; 
 
	<input name="CT_Pounds" size="15" value = "<%= strCTPounds%>" style="width: 78px" tabindex="9"  >lbs</span></strong></td>
  </tr>
  <tr>
	<td width="17%" class="style40" style="height: 26px">
	</td>
    <td class="style40" colspan="2" style="height: 26px">    <font face = arial size = 4 color="teal">
	<input type="checkbox" name="Axle" value="Y"  > 
	<span class="style13"><span class="style8"><span class="style1">Check here if total Truck &amp; Trailer did not fit on scale at same time  
	</span>  </span>
	</span><span class="style1"></span></span>&nbsp;</td>
  </tr>

<tr>
	<td height="22" width="17%" class="style40">&nbsp;</td>
    <td colspan="2" height="22" class="style40">&nbsp;</td>
  </tr>

       <tr><td  align = right class="style40" ><font face="Arial" size="2">Date Received:&nbsp;</font></td>
<td align = left colspan="2" class="style40"> 
<input type="text" name="Date_Received" size="15" value = <%= formatdatetime(Now(),2)%> style="width: 136px" tabindex="10"></td></tr>
             
         <tr>
          <td  align = right class="style40" >
  <font face="Arial" size="2">Comments:&nbsp;</font></td >
   <td align = left colspan="2" class="style40">   
	<input type="text" name="Other_Comments" size="25" style="width: 278px" value="<%= strOther %>" tabindex="11">
	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font face="Arial"><font size="2">&nbsp;Location: </font>   
YARD</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	</td></tr>
	
	

<tr>
    <td class="style40">&nbsp;</td>

    <td colspan="2" class="style40"> 
	<Input name="Update" type="submit" Value="Submit"  ></td>
  </tr>


</table>

</div>

</form>
</body>
<%  

Function SaveData()
dim strbody, strCID, strECarrier, strETrailer, strELoad, strEDoc, strEDC, strEDCName, strEAdd, strECity, strEState, MyConn2, strsql4
dim strECC, strEBCC
	

 



		strTrailerTID = 0
	strTrailerweight = 0
	strGrossWeight = 0
	strTareWeight = 0
 
 If len(request.form("CT_Pounds")) > 2 then
	strCTPounds = request.form("CT_Pounds")
	strGrossWeight = Request.form("Gross")

	strTonsReceived = round((strGrossWeight - strCTPounds)/2000,3)
	strPounds = round((Request.form("Gross") - strCTPounds),3)
		strTareWeight =  strCTPounds
	strNet = strTonsReceived

elseif 	len(request.form("C_Pounds")) > 2 then
	strCPounds = Request.form("C_Pounds")
	strTrailerWeight = 14460

   	 strSQL3 = "Select weight from tblCarrier   where Carrier = '" & strCarrier & "'"

   	 Set MyConn3 = Server.CreateObject("ADODB.Recordset")
   	 MyConn3.Open strSQL3, Session("ConnectionString")
   	 if not MyConn3.eof then
   	 
   	 strTrailerWeight = MyConn3.fields("Weight")
   	 end if
   	 MyConn3.close
   	 
   	 if len(strTrailerweight) > 3 then
   	 ' do nothing
   	 else
   	 strTrailerWeight = 14460
   	 end if
   	 
	strTonsReceived = round((Request.form("Gross") - strCPounds - strTrailerweight)/2000,3)
	strPounds = round((Request.form("Gross") - strCpounds - strTrailerweight),3)
	strTareWeight =  strTrailerweight + strCpounds
	strGrossWeight = Request.form("Gross")
	strNet = strTonsReceived
 
	 
	 strTrailerTID = 0
	strTonsReceived = round((Request.form("Gross") - strCPounds - strTrailerweight)/2000,3)
	strPounds = round((Request.form("Gross") - strCpounds - strTrailerweight),3)
		strTareWeight =  strTrailerweight + strCpounds
	strGrossWeight = Request.form("Gross")
	strNet = strTonsReceived
	 end if
	strSAP = request.form("SAP")
	strsql = "Select Type from tblBrokeSAP where SAP = '" & strSAP & "'"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
   	StrType = MyRec("Type")
	MyRec.close

	
		If len(request.form("BalesRF")) > 0 then
	strBalesRF = request.form("BalesRF")
	else
	strBalesRF = 0
	end if

	
	Dim strRightNow
	strRightnow = now()
	
strsql =  "INSERT INTO tblCars ( Bales_RF, Trailer_weight, Trailer_TID,    Carrier, Species, Grade,    Date_received, Location,  Trailer, Other_Comments,  Tons_Received, Net,"_
&"  Gross_weight, Tare_weight,  Entry_Time, Entry_BID, Entry_Page, Status, SAP_Nbr, Broke_Description, Format_PKG ) "_
	&" SELECT " & strBalesRF & ",  " & strTrailerWeight & ", " & strTrailerTID & ",   '" & strCarrier & "', 'BROKE', 'BROKE', "_
	&"   '" & strDateReceived & "',   'YARD',  "_
	&" '" & strTrailer & "', '" & strOther & "',  " & strTonsReceived & ", " & strTonsReceived & ", "_
	&"  " & strGrossWeight & ", " & strTareWeight & ", '" & strRightNow & "', '" & Session("EmployeeID") & "', "_
	&"  'Broke_receipt', " & strTonsReceived & ", '" & strSAP & "', '" & strtype & "',  '" & request.form("Foramat") & "'"

	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
 	
				
				strsql5 = "SELECT Max(tblCars.CID) AS MaxOfCID FROM tblCars WHERE tblCars.Trailer = '" & strTrailer & "'"
			
   	 Set MyRec5 = Server.CreateObject("ADODB.Recordset")
   	 MyRec5.Open strSQL5, Session("ConnectionString")
   	 strcarID = MyRec5.fields("MaxofCID")
   	 MyRec5.close
   	 
   	 
      	 				if len(Request.form("DOT")) > 0 then
		strDOT = request.form("DOT")
				strsql = "Update tblCars set Audit_Tons = " & strDOT & " where CID = " & strCarID & ""
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
	end if
	 

	If request.form("Axle") = "Y" then 
		strsql = "Update tblCars set Double_Axle = 'Y' where CID = " & strCarID & ""
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
		 	MyConn.Execute strSQL
			MyConn.Close

	 end if


		If Request.form("Print_receipt") =  "ON" then
		
	
	
	
		 	Response.redirect("Truck_receipt.asp?id=" & strCarID & "&p=" & strpounds)
			

	
		else
		 Response.redirect ("Broke_receipt.asp")
		end if

End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->