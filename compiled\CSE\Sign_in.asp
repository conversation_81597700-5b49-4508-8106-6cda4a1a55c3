<html>
<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Sign In </title>
<style type="text/css">
.style1 {
	color: #000080;
}
</style>
</head>
<% 
       set objGeneral = new ASP_CLS_General

 if objGeneral.IsSubmit() Then
strLoginid = Request.form("Login_ID")
strLoginPwd = Request.form("Login_pwd")
  Response.redirect("Homecheck.asp?id=" & strloginid & "&p=" &  strLoginPwd)
 end if


 %>
<p align="center" class="style1"><font face="arial" size="3"><strong>Please sign in with your ID Number</strong></font></p>


			<form name="form1" action="Sign_in.asp" method="post" >
			<p align="center" class="style1">
			<font face="Arial" size="2"><strong>Please enter your Employee ID:&nbsp;</strong></font><strong></font></strong><br>
	
			<input type="text" name="Login_ID" size="10" value = "<%= strLoginID%>"><br> 
			<br>
			<font face="Arial" size="2"><strong>Please enter your Password:&nbsp;</strong></font><strong></font></strong><br>
			<input type="password" name="Login_Pwd" size="10" value = "<%= strLoginPwd%>"><br>
			<Input name="Login" type="submit" Value="Login" ></p>
			</form>
