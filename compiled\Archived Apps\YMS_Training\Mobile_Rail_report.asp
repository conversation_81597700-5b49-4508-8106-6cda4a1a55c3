																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Rail Cars </TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->


<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate

strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -7, strtdate)
strDateREceived = "12/31/2014"

strsql = "SELECT * from tblCars where carrier = 'RAIL' and date_received > '" & strDateReceived & "' and date_unloaded > dateadd(d, 7, date_received)"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<style type="text/css">
.style1 {
	text-align: right;
	font-family: Arial, Helvetica, sans-serif;
}
</style>
</head>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>


<td align = "left"><b><font face="Arial">Rail Cars</font></b></td>
 


<td class="style1"><strong><a href="Mobile_Rail_report_Excel.asp">Send to Excel</a></strong></td>
 


</tr>
	    </table>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1 align = center>  
	 <tr class="tableheader">
				<td  align = left>     <font face="Arial"  size="2" >	Car</font></td>
		<td  ><font face="Arial"  size="2" >Date Received</font></td>
		<td  ><font face="Arial"  size="2" >Date Unloaded</font></td>
			<td  align = left>     <font face="Arial"  size="2" >Day of Week Received</font></td>
		<td  align = left>     <font face="Arial"  size="2" >Day of Week Unloaded</font></td>
		<td  align = left>     <font face="Arial"  size="2" >Number of Days</font></td>
		<td  align = left>     <font face="Arial"  size="2" >	Species</font></td>
	
		<td  align = left>     <font face="Arial"  size="2" >Transload</font></td>
	
	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>

	<td  ><font size="2" face="Arial"><%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	
<td  ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%>&nbsp;</font></td>

	<td  ><font size="2" face="Arial"><%= MyRec.fields("Date_unloaded")%>&nbsp;</font></td>
	
	<td  ><font size="2" face="Arial">
	<% strDay = "" %>
<% strDay =  datepart("w", MyRec("Date_Received"))	
	if strDay = 1 then %> Sunday <% end if %>
<% if strDay = 2 then %> Monday <% end if %>
<% if strDay = 3 then %> Tuesday <% end if %>
<% if strDay = 4 then %> Wednesday <% end if %>
<% if strDay = 5 then %> Thursday <% end if %>
<% if strDay = 6 then %> Friday <% end if %>
<% if strDay = 7 then %> Saturday <% end if %>
  &nbsp;</font></td>
<% strDay = "" %>
	<td  ><font size="2" face="Arial"><% strDay =  datepart("w", MyRec("Date_Unloaded"))%>
	<% if strDay = 1 then %> Sunday <% end if %>
<% if strDay = 2 then %> Monday <% end if %>
<% if strDay = 3 then %> Tuesday <% end if %>
<% if strDay = 4 then %> Wednesday <% end if %>
<% if strDay = 5 then %> Thursday <% end if %>
<% if strDay = 6 then %> Friday <% end if %>
<% if strDay = 7 then %> Saturday <% end if %>
  &nbsp;</font></td>
	<td><font size="2" face="Arial"><%= datediff("d", MyRec("Date_received"), MyRec.fields("Date_unloaded"))%>&nbsp;</td>
<td  ><font size="2" face="Arial"><%= MyRec.fields("Species")%>&nbsp;</font></td>
<td  ><font size="2" face="Arial"><%= MyRec.fields("RC_Transload")%>&nbsp;</font></td>
</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>

<!--#include file="Fiberfooter.inc"-->