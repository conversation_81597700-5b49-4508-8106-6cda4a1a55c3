
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">


<TITLE>Select Release Number</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

 <%    Dim objDAC, strSQL, MyRec, objMOC, rstFiber, strCID, rstFiberTrans, rstNF, strNFCID
		Dim rstKCOP, rstOCC, rstOF, rstOther, rstPMX, rstSBS, rstSWL, rstWLS
       Dim objGeneral, strDate, MyConn, strTCID, rstVFS, rstKCOPS, rstOCCS

	strCID = ""
	strTCID = ""
	
       set objGeneral = new ASP_CLS_General
   
if objGeneral.IsSubmit() Then

dim i
i = 0


if len(Request.form("KCOP")) > 1 then
i = i + 1
strCID = Request.form("KCOP")
end if

if len(Request.form("OCC")) > 1 then
i = i + 1
strCID = Request.form("OCC")
end if

if len(Request.form("VF")) > 1 then
i = i + 1
strCID = Request.form("VF")
end if

if len(Request.form("KCOP_Shuttle")) > 1 then
strTCID = REquest.form("KCOP_Shuttle")
i = i + 1
end if

if len(Request.form("OCC_Shuttle")) > 1 then
strTCID = REquest.form("OCC_Shuttle")
i = i + 1
end if

if len(Request.form("VF_Shuttle")) > 1 then
strTCID = REquest.form("VF_Shuttle")
i = i + 1
end if


if i > 1 then
Response.write ("<br><font face = arial size = 3 color = red><b>Please select only one Trailer.</b></font>")
elseif i = 0 then
Response.write ("<br><font face = arial size = 3 color = red><b>Please select a Trailer.</b></font>")
else
If len(strTCID) > 1 then
 	Response.redirect("Out_Trailer.asp?id=" & strTCID)
 	Session("Trailer") = strTCID
 else
	Response.redirect("Out_IB_Trailer.asp?id=" & strCID)
	Session("Trailer") = ""

end if
end if

End if
%>
<SCRIPT LANGUAGE="JavaScript">
function formHandler(Form1){
document.forms["Form1"].submit();
}
</SCRIPT>

<style type="text/css">
.style1 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	background-color: #EAF1FF;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style3 {
	border: 1px solid #800000;
	border-collapse: collapse;
}
.style5 {
	text-align: left;
}
</style>
</head>

<body>
<form name="form1" action="SelectTruckUnload.asp" method="post" >

<p align="center"><font face="Arial"><b>CAR-OUT TRAILER</b></font></p>
<table cellpadding="2" class = "style3" style="width: 90%;" align="center">

       <TD bgcolor="#FFFFE8" class="style5" >  <font face="Arial" size="3"><b>Select Inbound Trailer&nbsp;</b></font></td>
	<td  bgcolor="#FFFFE8" class="style5"> <font face="Arial" size="3"><b>&nbsp;Select Shuttle</b></font></td></tr>
	<td class="style1">		
       
     <select name="KCOP" size="1">
 	<option value = "">Trailer Number</option>
 	 	<% strsql = "SELECT CID, Species, Trailer, Carrier FROM tblCars "_
&" WHERE (location = 'YARD' or location = 'Yard') and (Trailer <> 'UNKNOWN') AND (Date_received Is Not Null) AND (Grade='RF') "_
&" 	and (SPECIES = 'KBLD' or species = 'PMX'   or Species = 'OF3' or Species = 'HBX' or Species = 'SHRED' and (Shred_OCC = 0 or Shred_OCC = Null))   ORDER BY carrier, tblCars.Trailer"

	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
	
	While Not MyRec.EOF %>
<option value="<%= MyRec("CID") %>"><%= MyRec("Trailer") %>--<%= MyRec("Carrier") %>--<%= MyRec("Species") %></option>
<% MyRec.movenext
wend
MyRec.close %>
     </select>&nbsp;<b><font face="Arial" size="2">KBLD/OF3/PMX/HBX/SHRED</td>
		<Td class="style1">		
       
     <select name="KCOP_Shuttle" size="1">
 	<option value = "">Trailer Number</option>
     	
 	<% strsql = "SELECT Species, Date_received, Net, CID, [Transfer_Trailer_Nbr]   + '--' + Trans_Carrier as TC FROM tblCars "_
&" WHERE Trans_Unload_date Is Null AND Transfer_Date Is Not Null "_
&" AND (  species = 'KBLD' or species = 'PMX'  or Species = 'OF3' or Species = 'HBX' or Species = 'SHRED' and (Shred_OCC = 0 or Shred_OCC = Null))   ORDER BY tblCars.Transfer_Trailer_Nbr,CID"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
	If not MyRec.eof then
	While Not MyRec.EOF %>
<option value="<%= MyRec("CID") %>"><%= MyRec("TC") %>-<%= MyRec("Species") %>&nbsp;<%= MyRec("Date_Received") %>&nbsp;Net: <%= MyRec("Net") %></option>
<% MyRec.movenext
wend
end if
MyRec.close %>
     </select>&nbsp;&nbsp; <b><font face="Arial" size="2">KBLD/OF3/PMX/HBX/SHRED</td></tr><tr>

		<Td class="style1">  			
       
     <select name="OCC">
 	<option value="" selected>Trailer Number</option>
 	
 	<% strsql = "SELECT CID, Species, Trailer, Date_received, Net, Carrier FROM tblCars "_
&" WHERE (location = 'YARD' or location = 'Yard') and (Trailer <> 'UNKNOWN') AND (Date_received Is Not Null) AND (Grade='RF') "_
&" 	and ((species = 'OCC' or species = 'MXP'  or species = 'USBS' or species = 'SWL' or Species = 'LPSBS' or Species = 'HWM') or ( Shred_OCC = -1 or Shred_OCC = 1)) ORDER BY carrier, tblCars.Trailer"

	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
	
	While Not MyRec.EOF %>
<option value="<%= MyRec("CID") %>"><%= MyRec("Trailer") %>--<%= MyRec("Carrier") %>--<%= MyRec("Species") %>&nbsp;<%= MyRec("Date_Received") %>&nbsp;Net: <%= MyRec("Net") %></option>
<% MyRec.movenext
wend
MyRec.close %>
     </select>&nbsp; <b><font face="Arial" size="2">UNLOADED AT OCC </font></b>&nbsp;
		</td>
		    <Td class="style1"> <select name="OCC_Shuttle">
 	<option value="" selected>Trailer Number</option>
 	<% strsql = "SELECT Species, Date_Received, Net, CID, [Transfer_Trailer_Nbr]   + '--' + Trans_Carrier as TC FROM tblCars "_
&" WHERE Trans_Unload_date Is Null AND Transfer_Date Is Not Null and (location = 'YARD' or location = 'Yard')"_
&" AND ((Species='OCC' or Species = 'MXP' or Species = 'SWL' or Species = 'USBS'   or Species = 'LPSBS' or Species = 'HWM') or ( Shred_OCC = -1 or Shred_OCC = 1))  ORDER BY tblCars.Transfer_Trailer_Nbr,CID"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
	If not MyRec.eof then
	While Not MyRec.EOF %>
<option value="<%= MyRec("CID") %>"><%= MyRec("TC") %>-<%= MyREc("Species") %>&nbsp;<%= MyRec("Date_Received") %>&nbsp;Net: <%= MyRec("Net") %></option>

  <% MyRec.movenext
  wend
  end if
  MyRec.close %>
     </select>&nbsp;&nbsp;  <font face="Arial" size="2"><strong>UNLOADED AT OCC</strong></font></td>
</tr><tr>
				<td style="height: 31px" class="style1">
  
     <select name="VF">
 	<option value="" selected>Trailer Number</option>
 	 	<% strsql = "SELECT CID, Species, Trailer, Carrier FROM tblCars "_
&" WHERE (location = 'YARD' or location = 'Yard') and (Trailer <> 'UNKNOWN') AND (Date_received Is Not Null) AND (Grade='RF') "_
&" 	and (tblcars.species <> 'OCC'  and species <> 'PMX' and species <> 'KBLD' and species <> 'HBX' and species <> 'SHRED' and species <> 'HWM'  "_
&"  and species <> 'OF3' and species <> 'MXP' and species <> 'SWL' and species <> 'USBS' and species <> 'LPSBS' and Species <> 'Unresolved') ORDER BY carrier, tblCars.Trailer"

	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
	
	While Not MyRec.EOF %>
<option value="<%= MyRec("CID") %>"><%= MyRec("Trailer") %>--<%= MyRec("Carrier") %>--<%= MyRec("Species") %></option>
<% MyRec.movenext
wend
MyRec.close %>

 
    
     </select><font size="2"> </font>
        <b><font face="Arial" size="2">&nbsp;OTHER
		</td>
 <Td style="height: 31px" class="style1"> <select name="VF_Shuttle">
 	<option value="" selected>Trailer Number</option>
 <% strsql = "SELECT CID, Species,  [Transfer_Trailer_Nbr]   + '--' + Trans_Carrier as TC FROM tblCars "_
&" WHERE (Date_unloaded) Is Null and Location = 'YARD' AND Transfer_Date Is Not Null "_
&" AND (Grade = 'VF' or (Grade = 'RF' and Species='Wetlap')or Species = 'RTOP' or (Species='OWN Wetlap') or (Species='OCC Wetlap')) "_
&" ORDER BY tblCars.Transfer_Trailer_Nbr"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
	
	While Not MyRec.EOF %>
<option value="<%= MyRec("CID") %>"><%= MyRec("TC") %>--<%= MyRec("Species") %></option>
<% MyRec.movenext
wend
MyRec.close %>
     </select>&nbsp;&nbsp; </font>
        <b><font face="Arial" size="2">OTHER</tr>
        <tr><td class="style1">
   <b><font face="Arial" size="2">&nbsp;</font></b><font face="Arial"><input type="submit" value="Continue" id=submit1 name=submit1></td>


<Td class="style1"><input type="submit" value="Continue" id=submit1 name=submit1></Td>

</tr>

</table>
 
</form>


 <!--#include file="Fiberfooter.inc"-->