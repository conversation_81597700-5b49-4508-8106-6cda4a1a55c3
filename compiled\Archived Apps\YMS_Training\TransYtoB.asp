<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 14.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Truck Load Transfer from Baldwin</title>
</head>
<%
    Dim strSQL, MyRec, strID, objMOC, rstFiber, strCarrier
      
    Dim strTrailer
    Dim strReleaseNbr
    Dim strRECNbr
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strGenerator
    Dim strGenCity
    Dim strGenState
    Dim strOther, strR, strsql3, strTransTrailer

       strId  = Trim(Request.QueryString("id")) 


 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
 
		Call SaveData() 

	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to Transfer a Load.</font></br>")
	MyRec.close
	end if

  
ELSE


strsql = "SELECT tblCars.* from tblCars WHERE tblCars.CID = " & strid

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


    strTrailer = MyRec.fields("Trailer")
        strCarrier = MyRec.fields("Carrier")

    

MyRec.close
Call getdata()
	end if

%>



<body><br>
<table width = 100%><tr><td width = 33%></td><td align = center width = 34%><b><font face="Arial" size="4">
Transfer Load from Yard to Baldwin</font> </b></td><td align = right width = 33%><a href="SelectTruckYtoB.asp"><b><font face = arial size = 2>RETURN</a></font></b></td></tr></table>



<form name="form1" action="TransYtoB.asp?id=<%=strid%>&r=<%= strR%>" method="post">
<div align="center">
<table border="1" cellspacing="0" bordercolor="#C0C0C0" width="60%" bgcolor="#FFFFE8" style="border-collapse: collapse" cellpadding="0">
<tr>
    <td bgcolor="#FFFFE8" width="276">&nbsp;</td>

    <td  bgcolor="#FFFFE8">&nbsp;</td>
  </tr>
  <tr>
    <td  align = right bgcolor="#FFFFE8" width="276" >
   <b>
   <font face="Arial" size="2">Trailer:&nbsp;</font></b></td>
<td  align = left bgcolor="#FFFFE8">

   <font face = arial><b> <%= strTrailer%>&nbsp;&nbsp;&nbsp;

 
 <font face="Arial" size="2">Carrier:</font>
    <%= strCarrier %>
   </td></tr>
    <tr>

      <td  bgcolor="#FFFFE8" align = right width="276">
  &nbsp;</td>
<td  align = left bgcolor="#FFFFE8">

      &nbsp;</td></tr>
      
  <tr>
    <td  bgcolor="#FFFFE8" width="276">&nbsp;</td>

    <td bgcolor="#FFFFE8">&nbsp;</td>
  </tr>



       <tr>
          <td  align = right bgcolor="#FFFFE8" width="276" >
    <font face="Arial" size="2"><b>Date Transferred to Baldwin:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFE8">

      <input type="text" name="Date_Received" size="23" value = "<%= strDateReceived%>"></td></tr>
<tr>
    <td  bgcolor="#FFFFE8" width="276">&nbsp;</td>

    <td bgcolor="#FFFFE8">&nbsp;</td>
  </tr>

  <tr>
	<td width="276" height="22" bgcolor="#FFFFE8">
	<p align="right">
    <font face="Arial" size="2"><b>&nbsp;Print Receipt:</b></font></td>
  <td height="22" bgcolor="#FFFFE8"> <font size="1" face="Verdana">  
	<input type="checkbox"  value="ON" name="Print_receipt" checked></font></td>
</tr>
<tr>
	<td width="276" height="22" bgcolor="#FFFFE8">&nbsp;</td>
  <td height="22" bgcolor="#FFFFE8">&nbsp;</td>
</tr>

  <tr>
    <td bgcolor="#FFFFE8" width="276">&nbsp;</td>

    <td align = left bgcolor="#FFFFE8"><Input name="Update" type="submit" Value="Submit" ></td>
  </tr>
</table>

</div>

</form>
</body>
<%
 Function GetData()
  set objMOC = new ASP_CLS_FIBER
        set rstFiber = objMOC.FiberCarrier()
        strDateReceived = formatdatetime(Now(),0)
End Function



 Function SaveData()
 dim strNow
   strNow = formatdatetime(Now(),0)
 
strTrailer = Request.form("Trans_Trailer")
strCarrier = Replace(Request.form("Carrier"), "'", "''")

strDateReceived = Request.form("Date_received")
strRECNbr = strid & "A"

         strSql = "Update tblCars Set TransYtoB_date = '" & strDateReceived & "', "_
         &" PNbr = '" & strRECNbr & "', Date_unloaded = '" & strDateReceived & "', Location = 'BALDWIN' where CID = " & strid & ""

         
 	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
             strsql = "INSERT INTO tblMovement ( CID, DDate, Tdate, From_location, To_location, BID ) "_
		&" SELECT " & strid & ", '" & strDateReceived & "', '" & strnow & "', 'BALDWIN', 'YARD', '" & Session("EmployeeID") & "'"
        
           set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql

Response.redirect ("Transfer_receiptYtoB.asp?id=" & strid)



End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->