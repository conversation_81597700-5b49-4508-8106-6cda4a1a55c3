<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Resolve Exceptions</title>
<style type="text/css">
.style1 {
	border: 1px solid #E4E4E4;
	border-collapse: collapse;
		background-color: #FFFFCC;
}
.style3 {
	font-weight: bold;
	border-style: solid;
	border-color: #FFFFCC;
	background-color: #FFFFCC;
}
.style4 {
	font-weight: bold;
	border-style: solid;
	border-color: #FFFFCC;
	background-color: #FFFFCC;
	font-family: Arial;
	font-size: x-small;
}
.style25 {
	font-family: Verdana;
}
.style10 {
	font-size: xx-small;
}
.auto-style1 {
	border-color: #E7EBFE;
	border-width: 1px;
	background-color: #E7EBFE;
}
.auto-style2 {
	border: 1px solid #E7EBFE;
	font-weight: bold;
	background-color: #E7EBFE;
}
.auto-style3 {
	border: 1px solid #E7EBFE;
	font-weight: bold;
	background-color: #E7EBFE;
	font-family: Arial;
	font-size: x-small;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID, objMOC, rstFiber, strCarrier
      
    Dim strTrailer
    Dim strReleaseNbr
    Dim strRECNbr
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strGenerator
    Dim strGenCity
    Dim strGenState
    Dim strOther, strR, strsql3

       strId  = Trim(Request.QueryString("id")) 


 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
 
strNewRelease = request.form("Release")
Response.redirect("Resolve_Exception_detail_two.asp?r=" & strNewRelease & "&id=" & strid)

	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to Change data.</font></br>")
	MyRec.close
	end if

  
ELSE


strsql = "SELECT tblCars.* from tblCars WHERE tblCars.CID = " & strid

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


    strTrailer = MyRec.fields("Trailer")
    strRelease = MyRec.fields("Release_nbr")
    strVendor = MyRec.fields("Vendor")
     strNet = MyRec.fields("Net")
     strTonsReceived = MyRec.fields("Tons_Received")
     strDateReceived = MyRec.fields("Date_received")
    strComments = MyRec.fields("OTher_comments")
    strCarrier = MyRec.fields("Carrier")
    strPO = MyRec.fields("PO")
   
    

MyRec.close

	end if

%>



<body>
<table width = 100%><tr><td width = 33%></td><td align = center width = 34%><b><font face="Arial" size="4">
Resolve Exception for Receipt # <%= strid %></font> </b></td><td align = right width = 33%><a href="Resolve_Exceptions.asp?id=<%= strid %>"><b><font face = arial size = 2>RETURN</a></font></b></td></tr></table>



<form name="form1" action="Resolve_Exception_Detail.asp?id=<%=strid%>" method="post">
<table cellspacing="0" width="100%" align = center class="style1">
    <tr>

      <td align = right class="auto-style2">  &nbsp;</td>
<td  align = left class="auto-style1"> &nbsp;</td>
<td  align = left class="auto-style1"> &nbsp;</td>
<td  align = left class="auto-style1"> &nbsp;</td>
    <td align = right class="auto-style2">  &nbsp;</td>
<td  align = left class="auto-style1"> &nbsp;</td>
<td  align = left class="auto-style1"> &nbsp;</td>
<td  align = left class="auto-style1"> &nbsp;</td>
    <td align = right class="auto-style2">  &nbsp;</td>
<td  align = left class="auto-style1"> &nbsp;</td>
<td  align = left class="auto-style1"> &nbsp;</td>
<td  align = left class="auto-style1"> &nbsp;</td>
    <td align = right class="auto-style2">  &nbsp;</td>
<td  align = left class="auto-style1"> &nbsp;</td>
</tr>

  <tr>
   <td  align = right class="auto-style2" >   <font face="Arial" size="2">Trailer:&nbsp;</font></td>
<td  align = left class="auto-style1"><font face="Arial" size="2"><%= strTrailer%></td>
    <td  align = right class="auto-style2" >   <font face="Arial" size="2">Carrier:&nbsp;</font></td>
<td  align = left class="auto-style1"><font face="Arial" size="2"><%= strCarrier %></td>  
    <td  align = right class="auto-style2" >   <font face="Arial" size="2">Release Nbr:&nbsp;</font></td>
<td  align = left class="auto-style1"><font face="Arial" size="2"><%= strRelease%></td>
    <td  align = right class="auto-style2" >   <font face="Arial" size="2">PO:&nbsp;</font></td>
<td  align = left class="auto-style1"><font face="Arial" size="2"><%= strPO %></td>
  <td  align = right class="auto-style2" >   <font face="Arial" size="2">Vendor:&nbsp;</font></td>
<td  align = left class="auto-style1"><font face="Arial" size="2"><%= strVendor%></td>
    <td  align = right class="auto-style2" >   <font face="Arial" size="2">Net:&nbsp;</font></td>
<td  align = left class="auto-style1"><font face="Arial" size="2"><%= strNet %></td> 
<td  align = right class="auto-style2" >   <font face="Arial" size="2">Comments:&nbsp;</font></td>
<td  align = left class="auto-style1"><font face="Arial" size="2"><%= strComments%></td> </tr>

 
      <tr>

      <td align = right class="auto-style2">  &nbsp;</td>
<td  align = left class="auto-style1"> &nbsp;</td>
<td  align = left class="auto-style1"> &nbsp;</td>
<td  align = left class="auto-style1"> &nbsp;</td>
    <td align = right class="auto-style2">  &nbsp;</td>
<td  align = left class="auto-style1"> &nbsp;</td>
<td  align = left class="auto-style1"> &nbsp;</td>
<td  align = left class="auto-style1"> &nbsp;</td>
    <td align = right class="auto-style2">  &nbsp;</td>
<td  align = left class="auto-style1"> &nbsp;</td>
<td  align = left class="auto-style1"> &nbsp;</td>
<td  align = left class="auto-style1"> &nbsp;</td>
    <td align = right class="auto-style2">  &nbsp;</td>
<td  align = left class="auto-style1"> &nbsp;</td>
</tr>
      <tr>

      <td align = right class="auto-style2">  &nbsp;</td>
<td  align = left class="auto-style1"> &nbsp;</td>
<td  align = left class="auto-style1"> &nbsp;</td>
<td  align = right colspan="2" class="auto-style3">  Correct Release Nbr:</td>
<td  align = left class="auto-style1">  <font size="1" face="Arial">  
		<span class="style25"><span class="style10">  
		<input type = text name = "Release" size="50" style="width: 108px" ></span></span></font></td>
<td  align = left class="auto-style1"> &nbsp;</td>
<td  align = left class="auto-style1"> <Input name="Update" type="submit" Value="Verify" ></td>
    <td align = right class="auto-style2">  &nbsp;</td>
<td  align = left class="auto-style1"> &nbsp;</td>
<td  align = left class="auto-style1"> &nbsp;</td>
<td  align = left class="auto-style1"> &nbsp;</td>
    <td align = right class="auto-style2">  &nbsp;</td>
<td  align = left class="auto-style1"> &nbsp;</td>
</table>

</form>
</body>

</html>
<!--#include file="Fiberfooter.inc"-->