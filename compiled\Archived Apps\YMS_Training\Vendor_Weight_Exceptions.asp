<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Vendor Weights</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->



<% Dim MyRec, strsql, strNow, MyRec2, MyRec3
strSpecies = request.form("Species")
 Set MyRec = Server.CreateObject("ADODB.Recordset")
strsql = "SELECT Min(Date_Received) as 'Session_Min' , Max(Date_Received) as 'Session_Max' from tblCars WHERE len(Audit_Tons) >= 0 and CID > 213328 "
MyRec.Open strSQL, Session("ConnectionString"), adOpenDynamic
If NOT MyRec.EOF Then
   Session_Min = MyRec("Session_Min")
   Session_Max = MyRec("Session_Max")
End If
MyRec.Close

strdate = dateadd("d", -90, now())

strsql = " FROM tblCars WHERE    len(Audit_Tons) >= 0  and CID > 213328"
If Len(Request("Species")) > 0 Then
	if request("Species") = "RF - All" then
	  strsql = strsql & " AND (Species = 'KBLD' or Species = 'OF3' or SPecies = 'SHRED' or Species = 'HBX' or Species = 'PMX')"
	elseif request("Species") = "OCC - All" then
		  strsql = strsql & " AND (Species = 'OCC' or Species = 'ROCC' or Species = 'MXP' or Species = 'USBS' or Species = 'LPSBS')"
	elseif request("Species") = "Broke - All" then
  strsql = strsql & " AND (Species = 'Broke' or Species = 'WBroke' or Species = 'BROKE' or Species = 'WBROKE' )"
	else
   strsql = strsql & " AND Species = '" & request("Species") & "'"
   end if
End If
If Len(Request("selStartDate")) > 0 AND Len(Request("selEndDate")) > 0 Then
   strsql = strsql & " AND Date_Received BETWEEN '" & Request("selStartDate") & "' AND '" & Request("selEndDate") & "' "
ElseIf Len(Request("selStartDate")) > 0 Then
   strsql = strsql & " AND Date_Received >= '" & Request("selStartDate") & "' "
ElseIf Len(Request("selEndDate")) > 0 Then
   strsql = strsql & " AND Date_Received <= '" & Request("selEndDate") & "' "
End If

strsql2 = "SELECT COUNT(CID) as 'Counter', SUM(CASE WHEN ((Tons_received * 2000) - Audit_tons) >= 2000 THEN 1 WHEN ((Tons_received * 2000) - Audit_tons) <= -2000 THEN 1 ELSE 0 END ) as 'PlusMinus2000'"    & strsql
'Response.Write strsql2
Set MyRec3 = Server.CreateObject("ADODB.Recordset")
MyRec3.Open strSQL2, Session("ConnectionString"), adOpenDynamic
If NOT MyRec3.EOF Then
   intPlusMinus2000 = MyRec3("PlusMinus2000")
   intCounter = MyRec3("Counter")
End If
MyRec3.Close


strsql = "SELECT tblCars.* " & strsql & " order by CID desc"
'Response.Write strsql

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
 
%>

<style type="text/css">
.style1 {
	font-family: Arial, Helvetica, sans-serif;
}
.style2 {
	text-align: center;
}
</style>
</head>
<form name="form1" action="Vendor_Weight_Exceptions.asp" method="post">

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>
<tr><TD align = left width="10%"><font size="2" face = arial>&nbsp;</font></td>
<td align = center><b>
<font face="arial" size="4" >Trucks with Vendor Weight</font></b></td>
<td align = center  width="10%"><span class="style1"><a href="Vendor_Weight_Excel.asp?Species=<%= Request("Species") %>&selStartDate=<%=Request("selStartDate")%>&selEndDate=<%=Request("selEndDate")%>">EXCEL</a></span>&nbsp;</td>


</tr>
<tr>
<td colspan="4" align="center">
<font face="arial" size="3" >Species:</font>

<select name="Species"  >
 
<option value="">--Select--</option>
<option <% if strSpecies = "RF - All" then %> selected <% end if %>>RF - All</option>
<option <% if strSpecies = "KBLD" then %> selected <% end if %>>KBLD</option>
<option <% if strSpecies = "OF3" then %> selected <% end if %>>OF3</option>
<option <% if strSpecies = "SHRED" then %> selected <% end if %>>SHRED</option>
<option <% if strSpecies = "HBX" then %> selected <% end if %>>HBX</option>
<option <% if strSpecies = "PMX" then %> selected <% end if %>>PMX</option>
<option <% if strSpecies = "OCC - All" then %> selected <% end if %>>OCC - All</option>
<option <% if strSpecies = "OCC" then %> selected <% end if %>>OCC</option>
<option <% if strSpecies = "MXP" then %> selected <% end if %>>MXP</option>
<option <% if strSpecies = "USBS" then %> selected <% end if %>>USBS</option>
<option <% if strSpecies = "LPSBS" then %> selected <% end if %>>LPSBS</option>
<option <% if strSpecies = "Broke - All" then %> selected <% end if %>>Broke - All</option>
<option <% if strSpecies = "Broke" then %> selected <% end if %>>Broke</option>
<option <% if strSpecies = "WBroke" then %> selected <% end if %>>WBroke</option>
</select>

&nbsp;&nbsp;&nbsp;<font face="arial" size="3" >Start Date Received:</font>
<select name="selStartDate">
<option value="">---Select---</option>

<%
sDate = Session_Max
While sDate >= Session_Min
   If Len(Request("selStartDate")) > 0 Then
      If CDate(Request("selStartDate")) = sDate Then
         strSelected = " selected "
      Else
         strSelected = ""
      End If
   Else
      strSelected = ""
   End If
%>
<option value="<%=sDate%>" <%=strSelected%>><%=sDate%></option>
<%
   sDate = DateAdd("d",-1,sDate)
WEnd
%>
</select><font face="arial" size="3" > &nbsp;&nbsp;&nbsp; End Date Received:</font>
<select name="selEndDate">
<option value="">---Select---</option>

<%
sDate = Session_Max
While sDate >= Session_Min
   If Len(Request("selEndDate")) > 0 Then
      If CDate(Request("selEndDate")) = sDate Then
         strSelected = " selected "
      Else
         strSelected = ""
      End If
   Else
      strSelected = ""
   End If
%>
<option value="<%=sDate%>" <%=strSelected%>><%=sDate%></option>
<%
   sDate = DateAdd("d",-1,sDate)
WEnd
%>
</select> &nbsp;&nbsp;&nbsp; <input type="submit" name="btnSubmit" value="Submit">
 </td>
</tr>
 </table>
 
 <%set objGeneral = new ASP_CLS_General

if objGeneral.IsSubmit() Then %>

 <table><tr>
<td><font face="arial" size="2" >
# Total Loads:&nbsp;&nbsp;<%=intCounter%><br>
# Loads +/-2000 lb Difference:&nbsp;&nbsp;<%=intPlusMinus2000%><br>
% Loads +/-2000 lb Difference:&nbsp;&nbsp;
<% if intCounter > 0 then %>
<%=Round(intPlusMinus2000/intCounter * 100,0)%>
<% else %> 0 <% end if %>%<br>
</td>
<td></td>
</tr>



	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1>  
	 <tr class="tableheader">
<td  > &nbsp;</td>
	<td  > <font face="Arial" size="2"><b>Trailer</b></font></td>
 


		<td  ><font face="Arial" size="1"><b>Carrier</b></font></td>
		<td  ><font face="Arial" size="1">Species</font></td>
    
		<td  ><font face="Arial" size="1">Vendor</font></td>
		<td  >
        <font face="Arial" size="1">PO Number</font></td>
		<td  >
        <p align="center">
        <font face="Arial" size="1">Release<br> Number</font></td>
	
		<td  >
        <font face="Arial" size="1">REC <br>Number</font></td>

		<td  >
        <p align="center">
        <font face="Arial" size="1">Gross<br> Weight</font></td>
		<td  align="center"> <font face="Arial" size="1">Tare<br> Weight</font></td>
			<td  align="center"> <font face="Arial" size="1">Trailer<br> Weight</font></td>
		<td  >        <p align="center">        <font face="Arial" size="1">Tons<br> Received</font></td>
				<td  >        <p align="center">        <font face="Arial" size="1">Pounds<br> Received</font></td>
 	     <td  >        <p align="center">        <font face="Arial" size="1">Vendor<br>Weight</font></td>
   <td align="center">        <font face="Arial" size="1">Diff.</font></td>
     <td class="style2"  >        <font face="Arial" size="1">Secondary<br> Weight</font></td>
       <td class="style2"  >        <font face="Arial" size="1">Floor Scale<br> Weight</font></td>
  <td  >        <font face="Arial" size="1">Trailer<br> Double</font></td>
  		<td  >        <font face="Arial" size="1">Date<br> Received</font></td>
        <td  >        <font face="Arial" size="1">Date<br> Unloaded</font></td>

		<td  >        <font face="Arial" size="1">Generator</font></td>
		<td  >        <font face="Arial" size="1">Generator<br> City</font></td>
		<td  >        <font face="Arial" size="1">Gen<br> State</font></td>
		<td  >        <font size="1" face="Arial">Other</font></td>

	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
       
       strTrailerWeight = 0
       
       strTrailerTID = MyRec("Trailer_TID")
       
          	 strSQL3 = "Select weight from tblTrailerOptions where TID = " & strTrailerTID

   	 Set MyConn3 = Server.CreateObject("ADODB.Recordset")
   	 MyConn3.Open strSQL3, Session("ConnectionString")
   	 if not MyConn3.eof then 
   	 
   	 strTrailerWeight = MyConn3.fields("Weight")
   	 end if
   	 
   	 MyConn3.close

    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    <td><font size="1" face="Arial">
    <% if session("EmployeeID") = "C97338" or Session("EmployeeID") = "B55548"  or Session("EmployeeID") = "B53909" or Session("EmployeeID") = "C28802" or Session("EmployeeID") = "B55404" or Session("EmployeeID") = "B96138"   then %>
    
    <a href="Receipt_Release_Edit.asp?id=<%= MyRec("CID") %>&p=vw">Edit</a>
    <% end if %></font></td>
<% if isnull(MyRec.fields("Date_unloaded")) then %>
<td bgcolor="pink">
<% else %>
	<td  >   
	<% end if %>     <font size="1" face="Arial"><b>        <%= MyRec.fields("Trailer")%></font></b></td>
 
	
			<td  >
        <font size="1" face="Arial"><b>
        <%= MyRec.fields("Carrier")%>&nbsp;</font></b></td>


		<td  >
        <font face="Arial" size="1">
        <%= MyRec.fields("Species")%></font></td>
		<td>
        <font size="1" face="Arial">
        <%= MyRec.fields("Vendor")%></font></td>
		<td  >
        <font size="1" face="Arial">
        <%= MyRec.fields("PO")%></font></td>
		<td  >
        <font size="1" face="Arial">
        <%= MyRec.fields("Release_Nbr")%></font></td>
	<td>
		 <font size="1" face="Arial">
        <%= MyRec.fields("REC_Number")%></font></td>

		<td align = right >		 <font size="1" face="Arial">        <%= MyRec.fields("Gross_Weight")%>&nbsp;</font></td>
		<td align = right >		 <font size="1" face="Arial">        <%= MyRec.fields("Tare_Weight")%>&nbsp;</font></td>
			<td align = right >		 <font size="1" face="Arial">        <%= strTrailerWeight %>&nbsp;</font></td>
		<td align = right  >	<font size="1" face="Arial">        <%= MyRec.fields("Tons_received")%>&nbsp;</font></td>
				<td align = right  >	<font size="1" face="Arial">        <%= MyRec.fields("Tons_received") * 2000%>&nbsp;</font></td>

		<% strPounds = myRec("Tons_received") * 2000
		
		if (strPounds - MyRec("Audit_tons") > 2000) or (MyRec("Audit_Tons") - strPounds > 2000) then %>
		<td align = right bgcolor="yellow"><font size="1" face="Arial">        <%= MyRec.fields("Audit_Tons")%>&nbsp;</font></td>
		<% else %>
	
		<td align = right  ><font size="1" face="Arial">        <%= MyRec.fields("Audit_Tons")%>&nbsp;</font></td>
		<% end if %>
		 
			<td  align="right" >		 <font size="1" face="Arial">        <%= strPounds - MyRec("Audit_tons") %></font></td>
				<td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Secondary_Weight")%>&nbsp;</font></td>

	<td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Floor_Scale_Weight")%>&nbsp;</font></td>

		<td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Double_Axle")%>&nbsp;</font></td>
		<td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Date_Received")%></font></td>
        <td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Date_Unloaded")%>&nbsp;</font></td>

       <td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Generator")%></font></td>
		<td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Gen_City")%></font></td>
		<td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Gen_State")%></font></td>
		<td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>
	
	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>

<% end if %>

</form>
<!--#include file="Fiberfooter.inc"-->