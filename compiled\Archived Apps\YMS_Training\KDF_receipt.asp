																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>KDF Receipt</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->




<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate, strid, strTrailer, strDateReceived


strid = request.querystring("id")

strsql = "SELECT tblSAPOpenPO.Item, tblCars.* FROM tblCars LEFT JOIN tblSAPOpenPO ON tblCars.NFID = tblSAPOpenPO.OID "_
&" WHERE CID = " & strid
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>
<style type="text/css">
.style1 {
	text-align: left;
}
</style>
</head>

<body onload="if (window.print) {window.print()}">

<p align="center">
<img height="40" src="kcc40white2.gif" width="450"><br>
	
</p>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD align = left style="width: 213px"><font size="2" face = arial><img src="images/j0251997.wmf" width="193" height="172" style="float: left">
&nbsp;</font></td>
<td class="style1"><b><font face="Arial">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Receipt for Load at Kimberly-Clark 
<a href="SelectWeyInbound.asp">Mobile</a></font></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br><br><br>
	
<table width = 100%>
<Tr>	<td align="left"><b><font face="Arial">Receipt Nbr:&nbsp;<%= MyRec.fields("CID")%></font></b></td>
	<td align="right"><b><font face="Arial">Date Received:&nbsp;<%= MyRec.fields("Date_Received")%></font></b></td></Tr>


</table>	<br><br><br><br>

<table border="1" width="100%" id="table1" cellspacing="1" bordercolorlight="#808080" bordercolor="#000000">

	
		<tr>
		<td bordercolor="#808080" align="center"><b><font face="Arial">Carrier</font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">Trailer #</font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">Species</font></b></td>
		<td bordercolor="#808080" align="center"><b><font face="Arial">PO #</font></b></td>
		<td bordercolor="#808080" align="center"><b><font face="Arial">Vendor <br>
		Receipt #</font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">Quantity</font></b></td>
		
	</tr>
			<tr>
	
		<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("Carrier")%></font></b></td>
			<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("Trailer")%></font></b></td>
		
		<td align="center"><b><font face="Arial"><%= MyRec.fields("Species")%>&nbsp;</font></b></td>
		<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("PO")%>-<%= MyRec.fields("Item")%></font></b></td>
			<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("Rec_Number")%></font></b></td>
			<td align="center"><font face="Arial"><b><%= MyRec.fields("Tons_received")%></b></font></td>
	</tr>
	</table>
	<table>
	<tr>
		<td colspan = 6>&nbsp;</td>
	</tr>
		<tr>
		<td colspan = 6>&nbsp;</td>
	</tr></table>
	
	<table width = 50% align = center>
	<tr>
		<td valign="top"><b><font face="Arial">Vendor:&nbsp;&nbsp;
		<%= MyRec.fields("Vendor")%></font></b></td>		
		
		</td>
	
	</tr></table>
<br><br>
<br><br>

		<% if len(MyRec.fields("Other_comments")) > 0 then %>
		<table width = 100% align = center><tr><td align = center>
	<b><font face="Arial"><%= MyRec.fields("Other_Comments")%></td></tr></table>
		<% end if %>
	&nbsp;<p align = center>&nbsp;</p>
		<div align="center">
		<table width = 80% border="1">
	<tr><td align="center">
		<p><b><font face="Arial">SAP DOC ID</font></b></td>
		<td align="center"><b><font face="Arial">Receiver Name</font></b></td>
		
		<td align="center"><b><font face="Arial">Initials</font></b></td>
		
	</tr>
	
	<tr>
		<td align="center"><b><font face="Arial"><%= MyRec.fields("SAP_DOC_ID") %>&nbsp;</font></b></td>
		<td align="center"><font face = Arial><b><%= session("Ename") %></td>
	
		<td align="center">&nbsp;</td>
	</tr>
	
</table></div>
	</div>