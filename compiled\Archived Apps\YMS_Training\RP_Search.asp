 
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Recovered Paper Orders</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

<%  
	Dim strsQL, rstEquip

   	Dim objEquipSearch, rstTotals, strCount, strDed, strNet, strTons, rstTotalReceived, strTR, objNew, rstSpecies
  	Dim  strVendor, strBegDate, strEndDate, strSpecies, strM<PERSON>h, strDateReceived, strDateAdded, rstMonth, strShipWeek
 	Dim  rstVendor
  	Dim objGeneral, strPO, gcount
   	
   	Dim intDirection
  	Dim intPageNumber
   	Dim strPageNav

   intPageNumber = 1

   set objGeneral = new ASP_CLS_General

  if objGeneral.IsSubmit() Then
    Call GetFormData()
    Select Case intDirection
      Case 10: 'goto mocinfo.asp
  
      Case Else:
        Call LoadSearchResults()
    End Select
  else
    intDirection = 0
  end if
Call GetData()
%>

<script language="javascript">
 
  function GotoMOC(MOCID)
  {
    document.forms["form1"].elements["tbl_ID"].value = MOCID;
    document.forms["form1"].elements["Direction"].value = 10;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }

  function Search()
  {
    
    document.forms["form1"].elements["Direction"].value = 1;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }

  function NextPage()
  {
    document.forms["form1"].elements["Direction"].value = 5;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }
  function PrevPage()
  {
    document.forms["form1"].elements["Direction"].value = 6;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }
</script>


<style type="text/css">
.auto-style3 {
	border-color: #C0C0C0;
	font-weight: bold;
	border-width: 1px;
	background-color: #E7EBFE;
}
.auto-style4 {
	border-color: #C0C0C0;
	border-width: 1px;
	background-color: #E7EBFE;
}
</style>
</head>


<form name="form1" action="RP_Search.asp" method="post">
 <input type="hidden" name="Direction" value="1" >
 <input type="hidden" name="tbl_ID" value="" >
 <input type="hidden" name=PageNumber value="<%=intPageNumber%>" >
	<div align="center">
<TABLE borderColor=#E7D1C2 cellSpacing=0 cellPadding=0 width="100%" border=0>  
  <tr><td colspan="5" size = "2" bordercolor="#FFFFFF"><b>
	<font face="Arial">Recovered Paper Orders&nbsp;&nbsp;</font></td>
	<td align = "RIght" bordercolor="#FFFFFF">
	<font face="Arial"><b><a href="Loginscreen.asp">HOME</a></b></font></td></tr></table>
	<TABLE borderColor=#E7D1C2 cellSpacing=0 cellPadding=0 width="80%" border=1> 
  <TR>
    <TD align="center" class="auto-style3"><font face="Arial" size="2">Month</font></TD>
    <TD align="center" class="auto-style4">
    &nbsp;<b><font face="Arial" size="2">Species</font></TD>    
    <TD align="center" class="auto-style3">
    <font face="Arial" size="2">PO</font></TD>    
    <TD align="center" class="auto-style3">
    <font face="Arial" size="2">Vendor</font></TD>    




    <TD align="center" class="auto-style4">
    &nbsp;</TD>    




 </TR>

  <TR>
         <TD bordercolor="#FFFFFF" align="center">
    <font face="Arial">
   <select size="1" name="Month">

     <option value="">--- All ---</option>
       <%= objGeneral.OptionListAsString(rstMonth, "Import_month", "Import_month", strMonth) %>
     </select></font></TD>
    <TD bordercolor="#FFFFFF" align="center">
     <font face="Arial">
   <select size="1" name="Species">

     <option value="">--- All ---</option>
       <%= objGeneral.OptionListAsString(rstSpecies, "Species", "Species", strSpecies) %>
     </select></font>
    </TD>


    <TD bordercolor="#FFFFFF" align="center">
   <font face="Arial"><input name="PO" size="10" maxlength="10" value="<%=strPO%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633"></TD>


    <TD bordercolor="#FFFFFF" align="center">
     <font face="Arial"><input name="Vendor" size="10" maxlength="10" value="<%=strVendor%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633"></TD>


    <TD bordercolor="#FFFFFF" align="center">
	<input type="button" onClick="javascript:Search()" value="Search" caption="Search" style="float: right"></td>      
</TR>

  </TABLE></div>
</form>


  <% if objGeneral.IsSubmit() Then 
  	strPO = request.form("PO")
	strVendor = Request.form("Vendor")
	strSpecies = request.form("Species")
	strMonth = request.form("Month")


%>
   <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% border=1 align = center>
    <tr><td colspan="6" bgcolor="white"><font face="Arial" size="2"><b>Search Results</b> - <a href="RP_Search_Excel.asp?p=<%= strPO %>&v=<%= strVendor %>&s=<%= strSpecies %>&m=<%= strmonth %>">EXCEL</a></font></td>
    <td  align="center"><font face="Arial" size = 2>Ordered:<br>&nbsp;<%=strCount%></font></td>
    <td  align="center"><font face="Arial" size = 2>Received:<br>&nbsp;<%=strTR%></font></td>
    
        <td  align="center"><font face="Arial" size = 2>Fill Rate:<br>&nbsp;<% if strCount = 0 then %> 0%&nbsp;<% else %><%=round((strTR/strCount)*100,1)%>%<% end if %></font></td>
    <td  align="center"><font face="Arial" size = 2>Tons Received:<br>&nbsp;<% if isnull(strTons) then %>0 <% else %> <%=round(strTons,3)%><% end if %></font></td>
        <td  align="center"><font face="Arial" size = 2>Deduction:<br>&nbsp;<% if isnull(strDed) then %> 0 <% else %> <%= round(strDed,3) %><% end if %></font></td>
        <td  align="center"><font face="Arial" size = 2>Net:<br><% if isnull(strNet) then %>0<% else %>&nbsp;<%=round(strNet,3)%><% end if %></font></td>
    
    </tr>
    <tr><td colspan="13" bgcolor="white" align="right"><%=strPageNav%>&nbsp;</td></tr>
      <tr class="tableheader">
 	<td  align="center"><font face="Arial" size = 1>Species</font></td>

	<td  align="center"><font face="Arial" size = 1>PO</font></td>
	<td  align="center"><font face="Arial" size = 1>Trailer</font></td>
		<td  align="center"><font face="Arial" size = 1>Carrier</font></td>
	<td  align="center"><font face="Arial" size = 1>Date<br> Received</font></td>
	<td  align="center"><font face="Arial" size = 1>Date<br> Unloaded</font></td>
	<td  align="center"><font face="Arial" size = 1>Release</font></td>
	<td  align="center"><font face="Arial" size = 1>Vendor</font></td>
	<td  align="center"><font face="Arial" size = 1>Generator</font></td>
	
	<td  align="center"><font face="Arial" size = 1>Month</font></td>
	<td  align="center"><font face="Arial" size = 1>Tons</font></td>
	<td  align="center"><font face="Arial" size = 1>Deduction</font></td>
	<td  align="center"><font face="Arial" size = 1>Net</font></td>

      
  	<% 
      Dim ii
       ii = 0
       while not rstEquip.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr class=tablecolor1>
    <% else %>
       <tr class=tablecolor2>
    <% end if %>

<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Species")%>&nbsp; </td>


<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("PO")%>&nbsp;</td>

<td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("Trailer")%>&nbsp;</td>
<td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("Carrier")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("date_received")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("date_unloaded")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Release")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Vendor")%>&nbsp;</td>
<% if len(rstEquip.fields("Generator")) > 0 then %>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Generator")%>&nbsp;</td>
<% else %>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Generator")%>&nbsp;</td>
<% end if %>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Import_month")%>&nbsp;</td>
	<td  align="right"><font face="Arial" size = 1><%= rstEquip.fields("Tons_received")%>&nbsp;</font></td>
	<% if len(rstEquip.fields("Deduction")) > 0 then %>
	<td  align="right"><font face="Arial" size = 1><%= round(rstEquip.fields("Deduction"),3) %>&nbsp;</font></td>
	
	<% else%>
	<td  align="right"><font face="Arial" size = 1><%= rstEquip.fields("Deduction") %>&nbsp;</font></td>
	<% end if %>
<% if rstEquip.fields("Net") > 0 then %>
	<td  align="right"><font face="Arial" size = 1><%= round(rstEquip.fields("Net"),3)%>&nbsp;</font></td>
<% else %>
	<td  align="right"><font face="Arial" size = 1>0&nbsp;</font></td>
<% end if %>

  
   </tr>
    <% 
       ii = ii + 1
       rstEquip.MoveNext
     Wend
    %>
   </table>
<table>    <tr><td colspan="7" bgcolor="white" align="right"><%=strPageNav%>&nbsp;</td></tr></table>
<% end if %>


 <% Function GetData()

   set objNew = new ASP_Cls_Fiber
          
	set rstSpecies = objNew.FiberSpecies()
	set rstMonth= objNew.FiberMonth()



End Function
    Function GetFormData()
      intDirection = cint(Request.Form("Direction"))

     
    
	strPO = request.form("PO")
	strVendor = Request.form("Vendor")
	strSpecies = request.form("Species")
	strMonth = request.form("Month")
	strDateAdded = request.form("Date_added")
	strDateReceived = request.form("Date_received")


      intPageNumber = cint(Request.Form("PageNumber"))

 
    End Function

    Function LoadSearchResults()
      Dim objEquipSearch, objTotals

      if intDirection =1 Then
         intPageNumber = 1
      end if

      if intDirection = 5 Then
         intPageNumber = intPageNumber + 1
      end if
      if intDirection = 6 Then
         intPageNumber = intPageNumber - 1
      end if

	strPO = request.form("PO")
	strVendor = Request.form("Vendor")
	strSpecies = request.form("Species")
	strMonth = request.form("Month")


      set objEquipSearch = new ASP_Cls_Fiber
    
  
  strsql = "SELECT tblOrder.oid, tblCars.Carrier, tblcars.date_unloaded, tblOrder.species, tblCars.Generator, tblCars.trailer, "_
  &"  tblOrder.vendor, tblorder.sap_nbr, tblOrder.PO, req_ship_week, release, import_month, tblCars.Date_received, "_
  &"  tblCars.Tons_received, tblcars.net, tblCars.Deduction, tblOrder.Generator "_
  &" FROM tblOrder LEFT JOIN tblCars ON tblOrder.Release = tblCars.Release_nbr  where tblOrder.OID > 0 "
  
  if len(strVendor) > 1 then 
  strVendor = "%" & strVendor & "%"
  strsql = strsql & " and tblOrder.vendor like '" & strVendor & "'"
  end if
  
  if len(strMonth) > 2 then
    strsql = strsql & " and tblOrder.Import_month = '" & strMonth & "'"
    end if
  
  if len(strPO) > 4 then
   strsql = strsql & " and tblOrder.PO = '" & strPO & "'"
    end if
 if len(strspecies) > 1 then
   strsql = strsql & " and tblOrder.Species = '" & strSpecies & "'"
    end if




strsql = strsql & " ORDER BY tblOrder.Import_month DESC, tblOrder.Req_ship_week"

    Set rstEquip = Server.CreateObject("ADODB.Recordset")
    rstEquip.Open strSQL, Session("ConnectionString")

' set rstEquip = objEquipSearch.RPSearch(strmonth, intPageNumber, strSpecies, strPO, strVendor)
set rstTotals = objEquipSearch.RPTotals(strmonth, strSpecies, strPO, strVendor)
set rstTotalReceived = objEquipSearch.RPTotalReceived(strmonth, strSpecies, strPO, strVendor)

     if  not rstTotals.Eof Then
      strCount = rstTotals.fields("CountofOID").value
      strDed = rstTotals.fields("SumofDeduction").value
      strNet = rstTotals.fields("SumofNet").value
      strTons = rstTotals.fields("SumofTons_received").value
      
      end if 

    ' if ( not rstEquip.Eof) Then
     ' if ( intPageNumber < rstEquip.fields("TotalPage").value ) Then
       ' strPageNav = "<a href=javascript:NextPage()><B>Next Page</b></a>"
     ' end if
     ' if ( intPageNumber > 1 ) Then
        ' strPageNav = "<a href=javascript:PrevPage()><b>Previous Page</b></a>&nbsp;&nbsp;&nbsp;&nbsp;" & strPageNav
     ' end if
    ' end if

        if ( not rstTotalReceived.Eof) Then
     strTR = rstTotalReceived.fields("countofoid").value
     end if
    End Function
 %><!--#include file="Fiberfooter.inc"-->