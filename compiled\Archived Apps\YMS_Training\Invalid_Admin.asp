
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">


<TITLE>Invalid Admin</TITLE>



<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<%      
              



	
	Function ReturnEmail(strCheck)
		Dim objADUserQuery,objADU
		Dim strFirstName, strLastName
		Set objADUserQuery = New ActiveDirectoryUserQuery
		objADUserQuery.targetDomain = "kcc.com"
			objADUserQuery.addFilter "cn",strCheck
		objADUserQuery.executeQuery
		If(objADUserQuery.resultsCount = 0) Then
			ReturnEmail=""
		ElseIf(objADUserQuery.resultsCount = 1) Then
			set objADU=objADUserQuery.results(0)
			ReturnEmail=objADU.mail
		Else
			ReturnEmail=""
		end if
		Set objADUserQuery = Nothing
		Set objADU=Nothing
	End Function




 Dim strSql
  Dim objGeneral
  Server.ScriptTimeout =600
  set objGeneral = new ASP_CLS_General
  
  if objGeneral.IsSubmit() Then
   Dim strFrom, strTo, strEmailTo, strEmailType, strMOCKey, strMOCID, strNoticeDate, strOwner, strMill, strSite
   strSite = request.form("Site")
   strNoticedate = FormatDateTime(Now(),2) %>
   
           	<style type="text/css">
.style1 {
	border-left: 1px solid #808080;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #808080;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style2 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style3 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #FFFFCC;
}
</style>
</head>
<p><font face="arial" size="2"><a href="Invalid_Admin_Delete.asp"><b>CLICK HERE TO DELETE ALL LISTED BELOW</b></a></font></p>
           	<table width="25%" align="left" class="style1"><tr>
				<td class="style3"><font face=arial size=2>Last Name</td>
				<td class="style3"><font face=arial size=2>First Name</td>
				<td class="style3"><font face=arial size=2>User ID</td></tr>
			
			<% strSQL = "SELECT EID, Firstname, Lastname from tblAdmin where len(EID) < 8 order by Lastname "
		   
			Set MyRec = Server.CreateObject("ADODB.Recordset")
			
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
	
If not MyRec.eof then
			While not MyRec.EOF
			
		
			
                 	 strEmailTo = "Unknown Owner"
                 	 strTo = MyRec.fields("EID")
 		 			
                 	 strOwner = MyRec.Fields("Firstname")
                 	 strMill = MyRec.Fields("Lastname")

                  	strEmailTo = ReturnEmail(strTo)
                  	If strEmailTo = "" then %>
          
			<tr><td class="style2"><font face=arial size=2><%= strMill %></td>
				<td class="style2"><font face=arial size=2><%= strOwner %></td>
				<td class="style2"><font face=arial size=2><%= strTo %></td></tr>

          <%        
		
			End if
			

	
	            MyRec.MoveNext
     			WEND %>
     			</table>
     			<%
     			
     				else
           		Response.write ("<font face=arial size=2>There are no records to display</font>")
           		MyRec.Close 
           		
           	
  End if
end if
%>

<BODY>

    
<form action="Invalid_Admin.asp" method="post" >
        <BR>


        <TABLE BORDER=0  align="center" width = "25%"><Tr><td align="center">
    <font size="3" face="Arial"><b>Search for Invalid Admin Records</b>&nbsp;&nbsp;</font></td>
</tr>
            <TR><td>&nbsp;</td></tr><tr><td align="center">
     <input type="hidden" name="Anything" value="">
     
     <%
         strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "' and User_type = 'A'"


    Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL3, Session("ConnectionString")


If not Myrec2.eof then %>

	<Input name="Submit" type="submit" Value="Submit" >
	<% end if %>
            </td></TR>

        </TABLE>
        &nbsp;<p>&nbsp;</p>
		<p>&nbsp;</p>
		<p>&nbsp;</p>
		<p>
        <BR>

		&nbsp;</p>

