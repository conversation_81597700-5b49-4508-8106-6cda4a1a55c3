																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Vendor Weights</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->



<% Dim MyRec, strsql, strNow
   Response.ContentType = "application/vnd.ms-excel"

 
strsql = "SELECT tblCars.* FROM tblCars WHERE    len(Audit_Tons) >= 0  and CID > 154000 "
If Len(Request("Species")) > 0 Then
	if request("Species") = "RF - All" then
	  strsql = strsql & " AND (Species = 'KCOP' or Species = 'OF' or SPecies = 'SHRED' or Species = 'HBX' or Species = 'PMX')"
	elseif request("Species") = "OCC - All" then
		  strsql = strsql & " AND (Species = 'OCC' or Species = 'ROCC' or Species = 'MXP')"
	elseif request("Species") = "Broke - All" then
  strsql = strsql & " AND (Species = 'Broke' or Species = 'WBroke' or Species = 'BROKE' or Species = 'WBROKE' )"
	else
   strsql = strsql & " AND Species = '" & request("Species") & "'"
   end if
End If
strsql = strsql & " order by CID desc"


    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<style type="text/css">
.style1 {
	font-family: Arial, Helvetica, sans-serif;
}
</style>
</head>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <tr> 
<td align = center><b>
<font face="arial" size="4" >Trucks with Vendor Weight</font></b></td>
 


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1>  
	 <tr class="tableheader">

	<td  > <font face="Arial" size="2"><b>Trailer</b></font></td>
 


		<td  ><font face="Arial" size="1"><b>Carrier</b></font></td>
		<td  ><font face="Arial" size="1">Species</font></td>
    
		<td  ><font face="Arial" size="1">Vendor</font></td>
		<td  >
        <font face="Arial" size="1">PO Number</font></td>
		<td  >
        <p align="center">
        <font face="Arial" size="1">Release<br> Number</font></td>
	
		<td  >
        <font face="Arial" size="1">REC <br>Number</font></td>

		<td  >
        <p align="center">
        <font face="Arial" size="1">Gross<br> Weight</font></td>
		<td  align="center"> <font face="Arial" size="1">Tare<br> Weight</font></td>
			<td  align="center"> <font face="Arial" size="1">Trailer<br> Weight</font></td>
		<td  >        <p align="center">        <font face="Arial" size="1">Tons<br> Received</font></td>
				<td  >        <p align="center">        <font face="Arial" size="1">Pounds<br> Received</font></td>
 	     <td  >        <p align="center">        <font face="Arial" size="1">Vendor<br>Weight</font></td>
   <td   align="center">        <font face="Arial" size="1">Diff.</font></td>
     <td  >        <font face="Arial" size="1">Trailer<br> Double</font></td>
		<td  >        <font face="Arial" size="1">Date<br> Received</font></td>
        <td  >        <font face="Arial" size="1">Date<br> Unloaded</font></td>

		<td  >        <font face="Arial" size="1">Generator</font></td>
		<td  >        <font face="Arial" size="1">Generator<br> City</font></td>
		<td  >        <font face="Arial" size="1">Gen<br> State</font></td>
		<td  >        <font size="1" face="Arial">Other</font></td>

	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
       
       strTrailerWeight = 0
       
       strTrailerTID = MyRec("Trailer_TID")
       
          	 strSQL3 = "Select weight from tblTrailerOptions where TID = " & strTrailerTID

   	 Set MyConn3 = Server.CreateObject("ADODB.Recordset")
   	 MyConn3.Open strSQL3, Session("ConnectionString")
   	 if not MyConn3.eof then 
   	 
   	 strTrailerWeight = MyConn3.fields("Weight")
   	 end if
   	 
   	 MyConn3.close

    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<% if isnull(MyRec.fields("Date_unloaded")) then %>
<td bgcolor="pink">
<% else %>
	<td  >   
	<% end if %>     <font size="2" face="Arial"><b>        <%= MyRec.fields("Trailer")%></font></b></td>
 
	
			<td  >
        <font size="1" face="Arial"><b>
        <%= MyRec.fields("Carrier")%>&nbsp;</font></b></td>


		<td  >
        <font face="Arial" size="1">
        <%= MyRec.fields("Species")%></font></td>
		<td>
        <font size="1" face="Arial">
        <%= MyRec.fields("Vendor")%></font></td>
		<td  >
        <font size="1" face="Arial">
        <%= MyRec.fields("PO")%></font></td>
		<td  >
        <font size="1" face="Arial">
        <%= MyRec.fields("Release_Nbr")%></font></td>
	<td>
		 <font size="1" face="Arial">
        <%= MyRec.fields("REC_Number")%></font></td>

		<td align = right >		 <font size="1" face="Arial">        <%= MyRec.fields("Gross_Weight")%>&nbsp;</font></td>
		<td align = right >		 <font size="1" face="Arial">        <%= MyRec.fields("Tare_Weight")%>&nbsp;</font></td>
			<td align = right >		 <font size="1" face="Arial">        <%= strTrailerWeight %>&nbsp;</font></td>
		<td align = right  >	<font size="1" face="Arial">        <%= MyRec.fields("Tons_received")%>&nbsp;</font></td>
				<td align = right  >	<font size="1" face="Arial">        <%= MyRec.fields("Tons_received") * 2000%>&nbsp;</font></td>

		<% strPounds = myRec("Tons_received") * 2000
		
		if (strPounds - MyRec("Audit_tons") > 2000) or (MyRec("Audit_Tons") - strPounds > 2000) then %>
		<td align = right bgcolor="yellow"><font size="1" face="Arial">        <%= MyRec.fields("Audit_Tons")%>&nbsp;</font></td>
		<% else %>
		<td align = right  ><font size="1" face="Arial">        <%= MyRec.fields("Audit_Tons")%>&nbsp;</font></td>
		<% end if %>
			<td  align="right" >		 <font size="1" face="Arial">        <%= strPounds - MyRec("Audit_tons") %></font></td>
		<td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Double_Axle")%>&nbsp;</font></td>
		<td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Date_Received")%></font></td>
        <td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Date_Unloaded")%></font></td>

       <td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Generator")%></font></td>
		<td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Gen_City")%></font></td>
		<td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Gen_State")%></font></td>
		<td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>
	
	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>


