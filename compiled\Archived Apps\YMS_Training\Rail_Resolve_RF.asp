<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 14.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Enter Trailer Receipt</title>
</head>
<%
    Dim strSQL, MyRec, strID, strRelease, MyConn, objMOC, rstVendor, rstSpecies, strBales, strTons
    Dim strTrailer, strCarrier, strLocation, strSAP, strPO, strETA,  strDateShipped, rstFiber
    Dim strReleaseNbr, strUOM, strCID, strOtherComment
 
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived

    Dim strOther, strR, strsql3, strSpecies, strVendor,  strNet

strID = Request.querystring("id")
strCID = request.querystring("c")
 	set objGeneral = new ASP_CLS_General
 	strDatereceived = formatdatetime(now,2)

  call getdata()

if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
	strRelease  = strid
	strR = left(strRelease,1)
	
	strPO = Request.form("PO")
	strSAP_Nbr = Request.form("SAP")
	strSpecies = Request.form("Species")
	
 	strTrailer = Request.form("Trailer")
	strOther = Replace(Request.form("Other_Comments"), "'", "''") 
	strDateReceived = Request.form("Date_received")
	
	strCarrier = Request.form("Carrier")
	strVendor = Replace(Request.form("Vendor"), "'", "''")

	strLocation = Request.form("Location")
	

	strGrossWeight = Request.form("Gross_Weight")
	strTareWeight = Request.form("Tare_Weight")
	If strGrossWeight <> "" and strTareWeight <> "" then
	strTonsReceived = round((strgrossweight - strTareweight)/2000,3)
	else
	strGrossWeight = 0
	strTareWeight = 0
	
	If Request.form("Tons_received") <> "" then
	strTonsReceived = Request.form("Tons_received")
	else
	strTonsReceived = 0

	end if
	end if
	
	

	Call SaveData() 
		If Request.form("Print_receipt") =  "ON" then

		Response.redirect("Truck_receiptVF.asp?id=" & strid &"&t=" & strTrailer & "&d=" & strDateReceived)
		else
		Response.redirect ("Resolve_Exceptions_Rail.asp")
		end if
	
	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to save this information.</font></br>")
	MyRec.close
	end if

  

end if
%>



<body>



<table width = 100%><tr><td width = "67%" align="center">
<font face="Arial" size="4">
	<b>Log In-Transit Load </b></font> </b></td></tr></table>




<form name="form1" action="Rail_Resolve.asp?r=<%=strid%>&id=<%= strid%>&c=<%= strcid%>" method="post">
<div align="center">
<table border="1" cellspacing="0" bordercolor="#C0C0C0" width="75%" bgcolor="#FFFFE8" style="border-collapse: collapse" cellpadding="0">

<tr>
    <td bgcolor="#FFFFE8" align="right"><b><font face="Arial" size="2">&nbsp;</font></b></td>

    <td  align = center bgcolor="#FFFFE8" colspan="2">&nbsp; 
	</td>
  </tr><tr>
    <td bgcolor="#FFFFE8" align="right"><b><font face="Arial" size="2">
	Trailer/Car:&nbsp;</font></b></td>

    <td  bgcolor="#FFFFE8"> 
	<input type="text" name="Trailer" size="15" value="<%= strTrailer%>"> </td>

    <td  bgcolor="#FFFFE8"> 
	<font face="Arial"><font size="2"><b>Check to Print Receipt:</b></font><input type="checkbox" name="Print_receipt" value="ON" checked></td>

  </tr>
  
  <tr>
    <td bgcolor="#FFFFE8" align="right"><b><font face="Arial" size="2">Date 
	Shipped:&nbsp;</font></b></td>

    <td  bgcolor="#FFFFE8" colspan="2"> <font face="Arial" size="2">
	<%= strDateShipped%></td>
  </tr>
  
    <tr>
    <td bgcolor="#FFFFE8" align="right"><b><font face="Arial" size="2">Species:&nbsp;</font></b></td>

    <td  bgcolor="#FFFFE8"> 
     <font face = arial size = 1>
<select name="Species" size="1" >
 <option value="">--Select --</option>
       <%= objGeneral.OptionListAsString(rstSpecies, "Fiber_species", "Fiber_species", strSpecies) %>
     </select></font> </td>

    <td  bgcolor="#FFFFE8"> 
     <b><font face="Arial" size="2">Comments:</b> <%= strOtherComment%></font></td>
  </tr>
  
    <tr>
    <td bgcolor="#FFFFE8" align="right"><b><font face="Arial" size="2">SAP Number:&nbsp;</font></b></td>

    <td  bgcolor="#FFFFE8" colspan="2"> 
	<input type="text" name="SAP" size="15" value="<%= strSAP %>"> </td>

  </tr>
    <tr>

      <td  bgcolor="#FFFFE8" align = right height="28">
  <b><font face="Arial" size="2">Carrier:</font></b><font face="Arial" size="2"><b>&nbsp;</b></font></td>
<td  align = left height="28" bgcolor="#FFFFE8" colspan="2">

      <font face="Arial">   
	<select name="Carrier" style="font-weight: 700">
 	<option value="" selected>  Select Carrier (Required)</option>
      <%= objGeneral.OptionListAsString(rstFiber, "Carrier", "CDesc", strCarrier) %>
     </select></font></td></tr>
  <tr>

      <td  bgcolor="#FFFFE8" align = right height="28">
  <b><font face="Arial" size="2">Date 
	Received:&nbsp;</font></b><font face="Arial" size="2"><b>&nbsp;</b></font></td>
<td  align = left height="28" bgcolor="#FFFFE8" colspan="2">

      <input type="text" name="Date_received" size="15" value="<%= strDateReceived%>" ></td></tr>


       <tr>
    <td align = right bgcolor="#FFFFE8" >
   <font face="Arial" size="2"><b>Units:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFE8" colspan="2">

      <input type="text" name="Bales" size="15" value="<%= strBales%>" ></td></tr>
      <tr>
          <td  bgcolor="#FFFFE8" align=right>
   <font face="Arial" size="2"><b>Unit of Measure:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFE8" colspan="2">

      <input type="text" name="UOM" size="15" value="<%= strUOM%>">

</td></tr>





        <tr>  <td  bgcolor="#FFFFE8" align = right> <font face="Arial" size="2">
			<b>Weight&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFE8" colspan="2">    
<input type="text" name="Tons_Received" size="15" value="<%= strTons%>" ></td></tr>

<tr><td  bgcolor="#FFFFE8">&nbsp;</td>
    <td  bgcolor="#FFFFE8" colspan="2">&nbsp;</td>
  </tr>
             
  <tr>
    <td align = right bgcolor="#FFFFE8">
	<b><font face="Arial" size="2">Vendor:&nbsp; </font></b></td>

    <td  bgcolor="#FFFFE8" colspan="2"> <font face = arial size = 1><select name="Vendor" size="1" >
 <option value="">--Select --</option>
       <%= objGeneral.OptionListAsString(rstVendor, "Vendor", "Vendor", strVendor) %>
     </select></td>
  </tr>




 <tr>
          <td  align = right bgcolor="#FFFFE8" >
   <b><font face="Arial" size="2">PO:</font></b></td>
<td align = left bgcolor="#FFFFE8" colspan="2">
      <input type="text" name="PO" size="17" value="<%= strPO%>" ></TD></tr>
   

         <tr>
          <td  align = right bgcolor="#FFFFE8" >
  <b><font face="Arial" size="2">Location:</font></b></td >
   <td align = left bgcolor="#FFFFE8" colspan="2"><font face="Arial"><select name="Location" style="font-weight: 700" size="1">

      <option selected>YARD</option>
	<option>BALDWIN</option>
 	<option>DC</option>
     </select></font>&nbsp;&nbsp;&nbsp;<font size="2" face="Arial"> </font>   
	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	</td></tr>
	   <tr>
          <td  align = right bgcolor="#FFFFE8" >
  &nbsp;</td >
   <td align = left bgcolor="#FFFFE8" colspan="2">   &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font size="2" face="Arial"> </font>   
	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	</td></tr>
<tr>
    <td  bgcolor="#FFFFE8">&nbsp;</td>

    <td bgcolor="#FFFFE8" colspan="2"> 
	<Input name="Update" type="submit" Value="Submit"  ></td>
  </tr>


</table>

</div>

</form>
</body>
<% Function GetData()
  set objMOC = new ASP_CLS_FIBER
          set rstVendor = objMOC.VFVendor()
            set rstSpecies = objMOC.VFSpecies()
          set rstFiber = objMOC.FiberCarrier()
        
        strsql = "Select tblVirginFiber.* from tblVirginFiber where VID = " & strid
     
   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
   	 
   strSAP = MyRec.fields("SAP_nbr")
   strPO = MyRec.fields("PO")
   strETA = MyRec.fields("ETA")
   strDateshipped = MyRec.fields("Date_shipped")
   strTrailer = MyRec.fields("Trailer")
   strSpecies = MyRec.fields("Species")
   strVendor = MyRec.fields("Vendor")
   strUOM = MyRec.fields("UOM")
   strBales = MyRec.fields("Bales_VF")
   strTons = Myrec.fields("Tons")
   strCarrier = "RAIL"
    strOtherComment = MyRec.fields("Other_comment")    
End Function

Function SaveData()

Dim strdate
strdate = formatdatetime(now(),0)

if cint(strTonsReceived) = 21 then
strKCWeighed = "Yes"
else
strKCWeighed = ""
end if

strLocation = Request.form("Location")


	strSAP = Request.form("SAP")	
	
		strsql =  "Update tblCars set Grade = 'VF',  SAP_Nbr = '" & strSAP & "', Carrier = '" & strCarrier & "', Species = '" & strSpecies & "', "_
		&" Trailer = '" & strTrailer & "', Bales_VF =  " & strBales & ", Vendor = '" & strVendor & "', PO = '" & strPO & "', "_
		&" Location_unloaded = '" & strLocation & "', Location = '" & strLocation & "', Tons_Received = " & strTons & ", "_
		&" UOM = '" & strUOM & "', VID = " & strID & " where CID = " & strcid
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
	
	
	
		strsql = "UPDATE tblVirginFiber SET tblVirginFiber.Status = 'Received' where tblVirginFiber.VID = " & strID & " "	                  
	
	
	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
	strsql = "Select CID from tblCars where VID = " & strid		
			
   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
   	 
   	 strCID = Myrec.fields("CID")
   	 MyRec.close
   	 
	strsql = "Update tblcars set Rec_number = '" & strCID & "' where CID = " & strCID
		
	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
   
End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->