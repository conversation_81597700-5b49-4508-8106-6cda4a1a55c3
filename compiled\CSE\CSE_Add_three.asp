<html>
<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_Procedure.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionString.asp"-->
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Add Confined Space </title>
<style>
<!--
table.MsoTableGrid
	{border:1.0pt solid windowtext;
	font-size:10.0pt;
	font-family:"Times New Roman";
	}
.auto-style1 {
	border-color: #C0C0C0;
	border-width: 1px;
	background-color: #EDF7FE;
}
.auto-style2 {
	background-color: #EDF7FE;
}
-->
</style>
</head>
<% dim strsql, MyRec, strid, strecp, strTask, objGeneral, strDescription, strLocation, strComments, strDate, MyConn
Dim objEPS, rstTeam, rstWA, strArea, strWorkArea,  strSOP, strFunctional, strListfour, strListfive, strListsix, strType
Dim strQ4, strQ5, strQ6, strQ7, strQ8, strQ9
Dim strListseven, strListeight, strListnine
strnow = dateadd("h", -5, now())
strDate = formatdatetime(strnow,2)
strid = Request.querystring("id")

strsql = "SELECT tblSOP.* from tblSOP where SID = " & strid & ""

Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
  If not MyConn.eof then
  
  strDescription = MyConn.fields("SDescription")
  else
  strDescription = ""
  end if
  Myconn.close

set objGeneral = new ASP_CLS_General
  		 if objGeneral.IsSubmit = True Then
  		
 	
strid = Request.querystring("id")

if isnull(Request.form("List_four")) then
strlistfour = ""
else
strlistfour= Replace(Request.form("List_four"), "'", "''") 
end if

if isnull(Request.form("List_five")) then
strlistfive = ""
else
strlistfive= Replace(Request.form("List_five"), "'", "''") 
end if

if isnull(Request.form("List_six")) then
strlistsix = ""
else
strlistsix= Replace(Request.form("List_six"), "'", "''") 
end if

if isnull(Request.form("List_seven")) then
strlistseven = ""
else
strlistseven= Replace(Request.form("List_seven"), "'", "''") 
end if

if isnull(Request.form("List_eight")) then
strlisteight = ""
else
strlisteight= Replace(Request.form("List_eight"), "'", "''") 
end if

if isnull(Request.form("List_nine")) then
strlistnine = ""
else
strlistnine= Replace(Request.form("List_nine"), "'", "''") 
end if


strQ4 = Request.form("Q_four")
strQ5 = Request.form("Q_five")
strQ6 = Request.form("Q_six")
strQ7 = Request.form("Q_seven")
strQ8 = Request.form("Q_eight")
strQ9 = Request.form("Q_nine")

If Request.form("Q_four") = "Yes" and len(strlistfour) < 2 then
Response.write("<br><br><Font face = arial color = red size = 3><b> You must explain why you selected Yes to Question #4</b></font>")
elseif Request.form("Q_five") = "Yes"and len(strlistfive) < 2 then
Response.write("<br><br><Font face = arial color = red size = 3><b> You must explain why you selected Yes  to Question #5</b></font>")
elseif Request.form("Q_six") = "Yes" and len(strlistsix) < 2 then
Response.write("<br><br><Font face = arial color = red size = 3><b> You must explain why you selected Yes  to Question #6</b></font>")
elseif Request.form("Q_seven") = "Yes" and len(strlistseven) < 2 then
Response.write("<br><br><Font face = arial color = red size = 3><b> You must explain why you selected Yes  to Question #7</b></font>")
elseif Request.form("Q_eight") = "Yes" and len(strlisteight) < 2 then
Response.write("<br><br><Font face = arial color = red size = 3><b> You must explain why you selected Yes to Question #8 ( Impair the ability to self rescue ) (</b></font>")
elseif Request.form("Q_nine") = "Yes" and len(strlistnine) < 2 then
Response.write("<br><br><Font face = arial color = red size = 3><b> You must explain why you selected Yes to Question #8 ( Result in a situation that presents an immediate danger to life or health. ) (</b></font>")



else


If Request.form("Q_four") = "Yes" or Request.form("Q_five") = "Yes" or Request.form("Q_six") = "Yes" or Request.form("Q_seven") = "Yes" or Request.form("Q_eight") = "Yes" or Request.form("Q_nine") = "Yes" then
strType = "CSE"

strsql = "Update tblSOP set  Q_four = '" & Request.form("Q_four") & "', Q_five = '" & Request.form("Q_five") & "',"_
&" Q_six = '" & Request.form("Q_six") & "', List_four = '" & strlistfour & "', List_five = '" & strlistfive & "', "_
&" List_six = '" & strListsix & "', List_seven = '" & strlistseven & "', List_eight = '" & strlisteight & "', "_
&" List_nine = '" & strListnine & "', Location_type = '" & strType & "', "_ 
&" Q_seven = '" & Request.form("Q_seven") & "', Q_eight = '" & Request.form("Q_eight") & "', "_
&" Q_nine = '" & Request.form("Q_nine") & "' where SID = " & strid & ""

Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 
			
			Response.redirect("CSE_Determination.asp?id=y")
else

strType = "NP CSE"

strsql = "Update tblSOP set Location_type = '" & strType & "', Q_four = '" & Request.form("Q_four") & "', "_
&" Q_five = '" & Request.form("Q_five") & "', Q_six = '" & Request.form("Q_six") & "', "_
&" Q_seven = '" & Request.form("Q_seven") & "', Q_eight = '" & Request.form("Q_eight") & "', "_
&" Q_nine = '" & Request.form("Q_nine") & "' where SID = " & strid & ""
Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 
			
			Response.redirect("CSE_Determination.asp?id=n")
end if 

end if
end if
 %><body>
 <p align="left"><b><font face = arial size = 3>Add New Confined Space:</font></b><font face="Arial">
 </font>
 </p>
	<form name="form1" action="CSE_Add_three.asp?id=<%= strid%>"  method="post" ID="Form1"  >
 
 

 
 <div align="center">

 

 
 <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#808080" width="100%" bgcolor="#D9E1F9" id="table1">
   
  <tr>
    <td align="center" class="auto-style1" >
	
	<font face="arial" size="2">Name of New Confined Space:&nbsp;&nbsp;<%=strDescription %>	&nbsp;</td>

</tr>
  
  </table>
  </div>
	<p class="MsoNormal"><b><font face="Arial" size="2">Determine whether the 
	characteristics of the Confined Space are capable of causing death or 
	serious physical harm:&nbsp; Answer &quot;Yes&quot; to all that apply and briefly explain why they 
	are applicable.</font></b></p>
	<table class="MsoTableGrid" border="0" cellpadding="0" width="98%" style="border-collapse: collapse; border: 0 none; " id="table5">
		<tr>
			<td valign="top" style="border: 1.0pt solid #FFFFFF; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in">
			<p class="MsoNormal" style="text-indent: -.25in; margin-left: .25in">
			<font face="Arial">Question</font></td>
			<td valign="top" style="width: 28%; border-left: medium none #FFFFFF; border-right: 1.0pt solid #FFFFFF; border-top: 1.0pt solid #FFFFFF; border-bottom: 1.0pt solid #FFFFFF; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in">
			<p class="MsoNormal" align="left"><font face="Arial">If Yes, Please 
			Explain</font></td>
		</tr>
	</table>
	<table class="MsoTableGrid" border="1" cellspacing="0" cellpadding="0" width="98%" style="width: 98.78%; border-collapse: collapse; border: medium none" id="table4">
		<tr>
			<td width="60%" valign="top" style="width: 60.92%; border: 1.0pt solid windowtext; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in" class="auto-style2">
			<p class="MsoNormal" style="text-indent: -.25in; margin-left: .25in">
			<font face="Arial">4. Contains or has a potential to contain a hazardous 
			atmosphere</font></span><font face="Arial">:&nbsp; </font></td>
			<td style="width: 3%; border-left: medium none; border-right: 1.0pt solid windowtext; border-top: 1.0pt solid windowtext; border-bottom: 1.0pt solid windowtext; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in" class="auto-style2">
			<p class="MsoNormal"><font face="Arial">&nbsp;<select name="Q_four" size="1">
       <option selected>Yes</option>
		<option <% if strQ4 = "No" then %> selected <% end if %>>No</option>
     </select>
			 &nbsp;</font></td>
			<td valign="top" style="width: 28%; border-left: medium none; border-right: 1.0pt solid windowtext; border-top: 1.0pt solid windowtext; border-bottom: 1.0pt solid windowtext; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in" class="auto-style2">
			<p class="MsoNormal">
		<font face = arial size = 2>
	<input name="List_four" size="42" style="float: left" value="<%= strListfour%>" ></td>
		</tr>
		<tr>
			<td width="60%" valign="top" style="width: 60.92%; border-left: 1.0pt solid windowtext; border-right: 1.0pt solid windowtext; border-top: medium none; border-bottom: 1.0pt solid windowtext; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in" class="auto-style2">
			
			<font face="Arial">5. Contains a material with the potential for engulfing someone who enters the 
			space</font></span><br><br></td>
			<td valign="top" style="width: 3%; border-left: medium none; border-right: 1.0pt solid windowtext; border-top: medium none; border-bottom: 1.0pt solid windowtext; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in" class="auto-style2">
			<p class="MsoNormal"><font face="Arial">&nbsp;<select name="Q_five" size="1">
       <option selected>Yes</option>
		<option <% if strQ5 = "No" then %> selected <% end if %>>No</option>
     </select></font></td>
			<td valign="top" style="width: 28%; border-left: medium none; border-right: 1.0pt solid windowtext; border-top: medium none; border-bottom: 1.0pt solid windowtext; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in" class="auto-style2">
			<p class="MsoNormal">
		<font face = arial size = 2>
	<input name="List_five" size="42" style="float: left" value="<%= strlistfive%>" ></td>
		</tr>
			<tr>
			<td width="60%" valign="top" style="border-left: 1.0pt solid windowtext; border-right: 1.0pt solid windowtext; border-top: medium none; border-bottom: 1.0pt solid windowtext; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in" height="55" class="auto-style2">
			
			<font face="Arial">6. Has an internal configuration that could allow someone entering to be trapped or asphyxiated by inward converging walls or by a floor, with slopes downward and tapers to a smaller cross-section
			</font></span><br></td>
			<td style="width: 3%; border-left: medium none; border-right: 1.0pt solid windowtext; border-top: medium none; border-bottom: 1.0pt solid windowtext; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in" class="auto-style2">
			<p class="MsoNormal"><font face="Arial">&nbsp;<select name="Q_six" size="1">
       <option selected>Yes</option>
		<option <% if strQ6 = "No" then %> selected <% end if %>>No</option>
     </select></font></td>
			<td valign="top" style="width: 28%; border-left: medium none; border-right: 1.0pt solid windowtext; border-top: medium none; border-bottom: 1.0pt solid windowtext; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in" class="auto-style2">
			<p class="MsoNormal">
		<font face = arial size = 2>
	<input name="List_six" size="42" style="float: left" value="<%= strlistsix%>" ></td>
		</tr>
				<tr>
			<td width="60%" valign="top" style="width: 60.92%; border-left: 1.0pt solid windowtext; border-right: 1.0pt solid windowtext; border-top: medium none; border-bottom: 1.0pt solid windowtext; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in" class="auto-style2">
			
			<font face="Arial">7. Contains any physical hazard.  This includes any recognized health or safety hazards including engulfment in solid or liquid material, electrical shock, or moving parts (does anything need to be locked out?)
			</font></span><br></td>
			<td style="width: 3%; border-left: medium none; border-right: 1.0pt solid windowtext; border-top: medium none; border-bottom: 1.0pt solid windowtext; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in" height="55" class="auto-style2">
			<p class="MsoNormal"><font face="Arial">&nbsp;<select name="Q_seven" size="1">
       <option selected>Yes</option>
		<option <% if strQ7 = "No" then %> selected <% end if %>>No</option>
     </select></font></td>
			<td valign="top" style="width: 28%; border-left: medium none; border-right: 1.0pt solid windowtext; border-top: medium none; border-bottom: 1.0pt solid windowtext; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in" class="auto-style2">
			<p class="MsoNormal">
		<font face = arial size = 2>
	<input name="List_seven" size="42" style="float: left" value="<%= strlistseven%>" ></td>
		</tr>
				<tr>
			<td valign="top" style="border-left: 1.0pt solid windowtext; border-right: 1.0pt solid windowtext; border-top: medium none; border-bottom: 1.0pt solid windowtext; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in" colspan="3" height="30" class="auto-style2">
			
			<font face="Arial">8. Contains any other recognized safety or health hazard that could either:
			</span>
			</font></td>
		</tr>
		<tr>
			<td width="60%" valign="top" style="border-left: 1.0pt solid windowtext; border-right: 1.0pt solid windowtext; border-top: medium none; border-bottom: 1.0pt solid windowtext; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in" height="25" class="auto-style2">
	
			<font face="Arial">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Impair the 
			ability to self rescue </font></td>
			<td valign="top" style="width: 3%; border-left: medium none; border-right: 1.0pt solid windowtext; border-top: medium none; border-bottom: 1.0pt solid windowtext; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in" class="auto-style2">
			<p class="MsoNormal"><font face="Arial">&nbsp;<select name="Q_eight" size="1">
       <option selected>Yes</option>
		<option <% if strQ8 = "No" then %> selected <% end if %>>No</option>
     </select></font></td>
			<td valign="top" style="width: 28%; border-left: medium none; border-right: 1.0pt solid windowtext; border-top: medium none; border-bottom: 1.0pt solid windowtext; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in" class="auto-style2">
			<p class="MsoNormal">
		<font face = arial size = 2>
	<input name="List_eight" size="42" style="float: left" value="<%= strListeight%>" ></td>
		</tr>
		
				<tr>
			<td width="60%" valign="top" style="border-left: 1.0pt solid windowtext; border-right: 1.0pt solid windowtext; border-top: medium none; border-bottom: 1.0pt solid windowtext; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in" height="25" class="auto-style2">
	
			<font face="Arial">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Result in a situation that presents an immediate danger to life or health. </font></td>
			<td valign="top" style="width: 3%; border-left: medium none; border-right: 1.0pt solid windowtext; border-top: medium none; border-bottom: 1.0pt solid windowtext; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in" class="auto-style2">
			<p class="MsoNormal"><font face="Arial">&nbsp;<select name="Q_nine" size="1">
       <option selected>Yes</option>
		<option <% if strQ9 = "No" then %> selected <% end if %>>No</option>
     </select></font></td>
			<td valign="top" style="width: 28%; border-left: medium none; border-right: 1.0pt solid windowtext; border-top: medium none; border-bottom: 1.0pt solid windowtext; padding-left: 5.4pt; padding-right: 5.4pt; padding-top: 0in; padding-bottom: 0in" class="auto-style2">
			<p class="MsoNormal">
		<font face = arial size = 2>
	<input name="List_nine" size="42" style="float: left" value="<%= strListnine%>" ></td>
		</tr>
	</table>

	<p>&nbsp;<INPUT TYPE="submit" value="Submit"></p>
	</form>

<!--#include file="footer.inc"-->