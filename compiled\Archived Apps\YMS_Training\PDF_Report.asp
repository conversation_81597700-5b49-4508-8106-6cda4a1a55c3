	<html><head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">
<TITLE>Loudon Fiber Delivery Report</TITLE>

<!--#include file="classes/asp_cls_DataAccessfiber.asp"-->
<!--#include file="classes/asp_cls_SessionStringFiber.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_Procedurefiber.asp"-->
</head><body>
<%  
	Dim strsQL, rstEquip, objGeneral, objNew, rstSpecies, strSpecies, MyRec, strCommodity, strDigit, strDelDate, strCount


 	Dim  strdate


strdate = formatdatetime(now(),2)
strdate = dateadd("d", -1, strdate)

if Request.querystring("q") <> "y" then
Response.redirect("Loudon_Delivery.asp")
end if
%>

<% ' Header - working properly %>
<br>
			<table border="0" width="80%">
			<tr style="border:0;">			
			<td width="1000%" align="center"><font face="Arial" size="5">Fiber Delivery Date</font></td></tr>
			<tr><td width="1000%" align="center"><font face="Arial" size="2"><%= Now() %></font></td></tr></table><br>
			<%
			strCurrGrade = ""
			strCurrDate = 1/1/1999
			strCount = 0
			strCount2 = 0
			strCount3 = 0 
			strRecCount = 0
			
' SELECT statement - ordered by delivery date, grade, then release nbr - not sure if working properly 

			  strsql2 = "SELECT COUNT(id) AS CountID FROM tblTempLoudon"
			 Set MyRec2 = Server.CreateObject("ADODB.Recordset")
			 MyRec2.Open strSQL2, Session("ConnectionString")			

			 	 'MyRec.Open strSQL, Session("ConnectionString")			 	 
			 	 if not MyRec2.eof then
			     strMyRecCount = MyRec2("CountID")
			     end if
			     MyRec2.close

		
			  strsql = "SELECT tblTempLoudon.* FROM tblTempLoudon where left(release_nbr,1) = 1 ORDER BY datepart(yyyy, delivery_date), datepart(m, delivery_date), datepart(d, delivery_date), grade, release_nbr "
			 Set MyRec = Server.CreateObject("ADODB.Recordset")
			  MyRec.Open strSQL, Session("ConnectionString")
			
while not MyRec.eof
			 	
			    %>
<% ' If the Delivery Date is not the same delivery date as before, make a new table for that delivery date - this part seems to be working properly %>
			
			<% if formatdatetime(MyRec("Delivery_date"),2) <> strCurrDate then %>

<% ' If it is not the first record, end the table before it, and put table for number of delieveries for that date - strCount set on line 67 %>

			<% if strCount > 0 then %>
			</table>
			<table border="1" bordercolor="black" width="80%"><tr style="border:0;"><td align="right" width="20%" style="border:0;"><font face="Arial" size="2">
				<strong># of Deliveries&nbsp;&nbsp;</strong></font></td><td align="left" style="border:0;"><font face="Arial" size="2">
				<strong><%= strCount3 %></strong></font></td></tr></table><br><br>
			<% strCount3 = 0
			strCurrGrade = "" %>
			<% end if %>
			<table border="0" style="border-bottom:1 black"><tr><td><font face="Arial" size="3"><b><u><%= formatdatetime(MyRec("Delivery_date"),2) %></u></b></font></td></tr></table>
			<table border="1" width="80%"><tr>
			</tr></table>
			<% strCurrDate = formatdatetime(MyRec("Delivery_date"),2)
			strCount = 1 %>
			<% end if %>
			
			<% if MyRec("grade") <> strCurrGrade then %>
			<% if strCount2 > 0 then %>
			</table>
			<% end if %>
			<% strCount2 = 1 %>
			<% strCurrGrade = MyRec("grade") %>
			<table border="1" bordercolor="black" width="80%" style="border-collapse:collapse;">
			<tr style="border:0;"><td align="left" colspan="4" style="border:0;"><font face="Arial" size="3">&nbsp;<%= MyRec("grade") %></font></td></tr>
			<tr style="border:0;">
			<td align="right" style="border:0;" width="20%"><font face="Arial" size="2">
			<strong>Release #</strong></font></td>
			<td align="right" style="border:0;" width="15%"><font face="Arial" size="2">
			<strong>Tier</strong></font></td>
			<td align="center" style="border:0;" width="20%"><font face="Arial" size="2">
			<strong>Carrier</strong></font></td>
			<td align="left" style="border:0;" width="55%"><font face="Arial" size="2">
			<strong>Generator</strong></font></td></tr>
			<% end if %>
			<tr>
				<td align="right" style="border:0;" width="20%"><font face="Arial" size="2"><%= MyRec("Release_nbr")%></font></td>
				<td align="right" style="border:0;" width="15%"><font face="Arial" size="2"><%= MyRec("Tier") %></font></td>
				<td align="center" style="border:0;" width="20%"><font face="Arial" size="2"><%= MyRec("Carrier") %></font></td>
				<td align="left" style="border:0;" width="55%"><font face="Arial" size="2"><%= MyRec("Generator") %></font></td>
			</tr>
			
			
			<% strCount3 = strCount3 + 1 %>
			 
			<% strRecCount = strRecCount + 1 %> 
			
			<% if strRecCount = strMyRecCount then %>
						</table>
			<table border="1" bordercolor="black" width="80%"><tr style="border:0;"><td align="right" width="20%" style="border:0;"><font face="Arial" size="2">
				<strong># of Deliveries&nbsp;&nbsp;</strong></font></td><td align="left" style="border:0;"><font face="Arial" size="2">
				<strong><%= strCount3 %></strong></font></td></tr></table><br><br>
			<% end if %><% MyRec.movenext
			wend %>