																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Transfer Merchants Receipt</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->




<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate, strid, strTrailer, strDateReceived, strMySpecies, strSAP, MyConn, strsql3

strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -7, strtdate)
strid = request.querystring("id")




strsql = "SELECT tblCars.* FROM tblCars WHERE CID = " & strid & "" 

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    strBrokeD = MyRec("Broke_Description")

%>
<style type="text/css">
.style1 {
	font-family: Arial;
	font-weight: bold;
}
.style2 {
	font-family: Arial, Helvetica, sans-serif;
}
</style>
</head>

<body onload="if (window.print) {window.print()}">

<p align="center"><img height="40" src="kcc40white2.gif" width="450"><br><br><br>
	
</p>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><b><font face="Arial">Material Movement Order at Kimberly-Clark 
<a href="Trans_MWH_Select.asp"><b>Mobile</b></a></font></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br><br><br>
	
<table width = 100%>
<Tr>	<td align="left"><b><font face="Arial">Receipt Nbr:&nbsp;<%= MyRec.fields("CID")%></font></b></td>
	<td align="right"><b><font face="Arial">Date Shipped:&nbsp;<%= MyRec.fields("Date_Received")%></font></b></td></Tr>


</table>	<br><br><br><br>

<table border="1" width="100%" id="table1" cellspacing="1" bordercolorlight="#808080" bordercolor="#000000">

	
		<tr>
		<td bordercolor="#808080" align="center"><b><font face="Arial">Ship From</font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">Ship To</font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">
		Trailer/Car #</font></b></td>
		<td bordercolor="#808080" align="center"><b><font face="Arial">Carrier</font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">Quantity 
		</font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">Sap #</font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">	Species</font></b></td>
		
		<td bordercolor="#808080" align="center" class="style2"><strong>Tier Rating</strong></td>
		<td bordercolor="#808080" align="center" class="style2"><strong>Release #</strong></td>

	</tr>
			<tr>
	
		<td align="center"><b><font face="Arial">Merchants Warehouse</font></b></td>
			<td align="center"><b><font face="Arial">&nbsp;KC</font></b></td>
		
		<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("Transfer_Trailer_NBR")%></font></b></td>
			<td align="center">&nbsp;<font face="Arial"><b><%= MyRec.fields("Trans_Carrier")%></b></td>
			<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("Net")%></font></b></td>
				<% strMYSpecies = MyRec.fields("Species")
			strsql3 = "Select SAP from tblVFSpecies where Fiber_Species = '" & strMySpecies & "'"
	Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL3, Session("ConnectionString")
    If not MyConn.EOF Then
    strSAP = MyConn.fields("SAP")
    else
    strSAP = ""
    end if
    MyConn.close 
    If strSAP = "70000111" then
       
	strsql = "Select tblBrokeSAP.* from tblBrokeSAP where Type = '" & strBrokeD & "'"
      
    Set MyRec6 = Server.CreateObject("ADODB.Recordset")
    MyRec6.Open strSQL, Session("ConnectionString")
    If not MyRec6.eof then
    strSAP = MyRec6("SAP") 
    end if
    end if %>
		
			<td align="center"><font face="Arial"><b><%= strSAP %></b>&nbsp;</td>
<% if len(strBrokeD) > 2 then %>
			<td align="center"><font face="Arial"><b><%= MyRec.fields("Species")%> - <%= strBrokeD %></b></font></td>
<% else %>
			
			<td align="center"><font face="Arial"><b><%= MyRec.fields("Species")%></b></font></td>
			<% end if %>
			<td align="center"><font face="Arial"><b><%= MyRec.fields("Tier_Rating")%></b></font></td>
	<td align="center"><font face="Arial"><b><%= MyRec.fields("PS")%>&nbsp;</b></font></td>

	</tr>
	</table>
	<p>
<br><br>

		<% if len(MyRec.fields("Other_comments")) > 0 then %>
		</p>
		<table width = 100% align = center><tr><td align = center>
	<b><font face="Arial"><%= MyRec.fields("Other_Comments")%></td></tr></table>
		<% end if %>
	&nbsp;<p align = center>&nbsp;</p>
		<div align="center">
		<table width = 80% border="1">
	<tr><td align="center">
		<p><b><font face="Arial">SAP </font></b><span class="style1">Doc ID</span></td>
		<td align="center"><b><font face="Arial">Shipment Loaded By</font></b></td>
		
		<td align="center"><b><font face="Arial">Receiver's Name</font></b></td>
		
		<td align="center"><b><font face="Arial">Initials</font></b></td>
		
	</tr>
	
	<tr>
				<td align="center"><b><font face="Arial"><%= MyRec.fields("SAP_DOC_ID") %>&nbsp;</font></b></td>
		<td align="center"><font face = Arial><b>&nbsp;</td>
	
		<td align="center"><font face="Arial"><%= Session("Ename")%>&nbsp;</td>
	
		<td align="center">&nbsp;</td>
	</tr>
	
</table></div>
	</div>