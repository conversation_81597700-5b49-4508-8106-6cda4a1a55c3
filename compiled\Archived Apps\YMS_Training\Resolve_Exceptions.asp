																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Resolve Exceptions</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->




<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate

strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -95, strtdate)

strsql = "SELECT tblCars.* FROM tblCars WHERE Entry_Page <> 'Generic_receipt_VF' and entry_page <> 'Generic_receipt_rail'  and entry_page <> 'DC_Receipt.asp' and entry_page <> 'Merchants_Receipt.asp'"_
&" and entry_page <> 'Transfer_From_DC.asp' and entry_page <> 'STO_Receipt'  and entry_page <> 'TransRtoY.asp' and entry_page <> 'STO_Broke_Receipt' and entry_page <> 'EnterSTOWADReceipt' and Entry_page <> 'EnterSTOReceipt'"_
&"   and (Grade='RF' or Grade = 'BROKE') "_
&" AND Date_received >'" & strdate & "' AND Trailer Is Not Null And Trailer<>'UNKNOWN'  and CID <> 65303"_
&" AND OID Is Null and (Species = 'UNRESOLVED' or Species = 'ROCC' or Species = 'OCC' or Species = 'MXP' or Species = 'LPSBS' or Species = 'HWM' "_
&"  or Species = 'SWL' or Species = 'PMX'  or Species = 'OF' or Species = 'OF3' or Species = 'BROKE' or Species = 'WBROK' or Species = 'BBROK' or Species = 'SHRED' or Species = 'HBX' or Species = 'KBLD') "

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Resolve Recovered Paper Exceptions</b></font></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1>  
	 <tr class="tableheader">
<td>&nbsp;</td>
			<td  > <p align="center">       <font face="Arial" size="1">Print<br> Receipt</font></td>
		<td  > <p align="center">       <font face="Arial" size="1">Release<br> Number</font></td>
		<td  >       <font face="Arial" size="1">PO Number</font></td>
		<td  ><font face="Arial" size="1"><b>Trailer</b></font></td>
		<td  ><font face="Arial" size="1"><b>Carrier</b></font></td>
		<td  ><font face="Arial" size="1">Species</font></td>
    	<td  ><font face="Arial" size="1">Grade</font></td>

		<td  ><font face="Arial" size="1">Vendor</font></td>
	
	<td>       <font face="Arial" size="1">Location</font></td>
		<td  >        <font face="Arial" size="1">Date<br> Received</font></td>
		<td  >      <font face="Arial" size="1">Date<br>Unloaded</font></td>
	
       <td> <p align="center">        <font face="Arial" size="1">Tons<br> Received</font></td>
        	<td  >   <p align="center">        <font face="Arial" size="1">Net<br> Weight</font></td>
		
	
		<td  >
        <font face="Arial" size="1">Generator</font></td>
		<td  >
        <font face="Arial" size="1">Generator<br> City</font></td>
		<td  >
        <font face="Arial" size="1">Gen<br> State</font></td>
		<td  >
        <font size="1" face="Arial">Other</font></td>

	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td> <font size="1" face="Arial"><a href="Resolve_Exception_Detail.asp?id=<%= MyRec.fields("CID") %>">Resolve</a></td>
	<td align = center> <font size="1" face="Arial"><a href="Truck_receipt_print.asp?id=<%= MyRec.fields("CID") %>"><%= MyRec.fields("CID")%></a></td>
	<td  ><font size="1" face="Arial"><%= MyRec.fields("Release_Nbr")%>&nbsp;</font></td>
	<td  >        <font size="1" face="Arial">        <%= MyRec.fields("PO")%>&nbsp;</font></td>
	<td  > <font size="1" face="Arial"><b><%= MyRec.fields("Trailer")%>&nbsp;</font></b></td>
			<td  ><font size="1" face="Arial"><b><%= MyRec.fields("Carrier")%>&nbsp;</font></b></td>


		<td  ><font face="Arial" size="1"> <%= MyRec.fields("Species")%>&nbsp;</font></td>
		<% if MyRec("Species") = "BROKE" and MyRec("Grade") <> "BROKE" then %>
		<td bgcolor="red">
		<% else %>
				<td  >
				<% end if %><font face="Arial" size="1"> <%= MyRec.fields("Grade")%>&nbsp;</font></td>

		<td><font size="1" face="Arial"><%= MyRec.fields("Vendor")%>&nbsp;</font></td>
	
	<td  >  <font size="1" face="Arial">        <%= MyRec.fields("Location")%>&nbsp;</font></td>
			<td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Date_Received")%>&nbsp;</font></td>
			<% if len(MyRec.fields("Date_unloaded")) > 0 then %>
		<td  >		 <font size="1" face="Arial">        <%= formatdatetime(MyRec.fields("Date_unloaded"),2)%>&nbsp;</font></td>
		<% else %>
			<td  >		 <font size="1" face="Arial"> &nbsp;</font></td>
		
		<% end if %>
		<td align = right>
				 <font size="1" face="Arial">        <%= MyRec.fields("Tons_received")%>&nbsp;</font></td>
				 <td align = right >		 <font size="1" face="Arial">        <%= MyRec.fields("Net")%>&nbsp;</font></td>

       <td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Generator")%>&nbsp;</font></td>
		<td  >	 <font size="1" face="Arial">        <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
		<td  > <font size="1" face="Arial"> <%= MyRec.fields("Gen_State")%>&nbsp;</font></td>
		<td  >	 <font size="1" face="Arial">        <%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>
	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>


<!--#include file="Fiberfooter.inc"-->