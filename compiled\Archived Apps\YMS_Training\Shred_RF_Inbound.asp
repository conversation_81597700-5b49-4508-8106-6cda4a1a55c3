																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE> OCC Inbound </TITLE>

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 



<% strid = request.querystring("id")
strc = request.querystring("c")
strRelease = request.querystring("r")

if strc = "o" then
strsql = "Update tblOrder set RF_SHRD = 'Y' where OID = " & strid

else 

strsql = "Update tblOrder set RF_SHRD = 'N' where OID = " & strid
end if
 Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
if strc = "o" then			
	strsql = "Update tblInbound set RF_SHRED = 'Y' where Release = '" & strRelease & "'"		
	else
		strsql = "Update tblInbound set RF_SHRED = 'N' where Release = '" & strRelease & "'"	
end if		
Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
			
  Response.redirect("Edit_OCC_Inbound.asp")
  %>