<%

Const GENERAL_ERROR_NOACCESS = 12345   '-- used when user does not have access to pages.
Const GENERAL_AMP_XSLQSTRING = "&"


Class ASP_CLS_General

 'These constants are not used very ofen,
 'so use property instead of defining
 'global constants
Public Property Get ERROR_INFOMATION
   ERROR_INFOMATION = 1
End Property

Public Property Get ERROR_WARNING
   ERROR_WARNING= 2
End Property

Public Property Get ERROR_ERROR
   ERROR_ERROR = 3
End Property

Public Property Get ERROR_LOGANDRAISEERROR
   ERROR_LOGANDRAISEERROR = 4
End Property

Public Function IsSubmit()
 'Make sure it is POST method and from current site.
	IsSubmit = (Request.Form.Count > 0)
End Function




Public Sub CheckError()
'---------------------------------------
'This function is to check error
'and goto to error page
'---------------------------------------
  Dim lngErrNumber
  Dim strErrDesc
  Dim strErrSource

 If Err.Number <> 0 Then
   lngErrNumber = Err.Number
   strErrDesc = Server.URLEncode(Err.Description)
  ' strErrSource = Server.URLEncode(cstr(Request.ServerVariables("REMOTE_HOST"))) & Server.URLEncode(cstr(Request.ServerVariables("URL")))

   '-- Goto Error Page
   Response.Redirect "/Error/Info.asp?ErrSource=" & GetErrorPath() & "&ErrDesc=" & strErrDesc & "&ErrNumber=" & lngErrNumber
 End if
End Sub

Public Function GetErrorPath()
		GetErrorPath = Server.URLEncode(cstr(Request.ServerVariables("REMOTE_HOST"))) & Server.URLEncode(cstr(Request.ServerVariables("URL"))) &"?"& Server.URLEncode(cstr(Request.ServerVariables("QUERY_STRING")))
End Function  

Public Function OptionListAsString(rstRecordset,strValueField, strDisplayField,vntSelectedValue)
  '-------------------------------------------------------------------------------------------------------------------
  'Purpose:   This method returns a string which can be stuffed in between <SELECT> and </SELECT> tags in HTML/ASP page
  'Input:
  '   rstRecordset: Valid disconnected Recordset
  '   strValueField: Name of value field
  '   strDisplayField: Name of display field
  '--------------------------------------------------------------------------------------------------------------------
	Const OPTION_START_TAG  = "<OPTION"
    Const VALUE_ATTRIBUTE   = " VALUE="
    Const TAG_POSTFIX       = ">"
    Const QUOTE             = """"
    Const SELECTED_TEXT     = " SELECTED"
    Const OPTION_END_TAG    = "</OPTION>"

    Dim lngRecordCount, lngRecordIndex
    Dim strOptionList
    Dim strSelected
    Dim strValue, strDisplay

    lngRecordCount = rstRecordset.RecordCount
    If (lngRecordCount > 0) Then
        ' move the pointer to the first record
        rstRecordset.MoveFirst
    End If
    If isNull(vntSelectedValue) Then
       vntSelectedValue =""
    End if
    ' traverse recordset
    For lngRecordIndex = 0 To lngRecordCount - 1
            strValue = IIF(IsNull(rstRecordset.fields(strValueField).value), "", rstRecordset.fields(strValueField).value)
            strDisplay = IIF(IsNUll(rstRecordset.fields(strDisplayField).value), "",  rstRecordset.fields(strDisplayField).value)
            strOptionList = strOptionList & _
                            OPTION_START_TAG & VALUE_ATTRIBUTE & _
                            QUOTE & strValue & QUOTE & _
                            IIF(cstr(vntSelectedValue) = cstr(strValue), SELECTED_TEXT, "") & _
                             TAG_POSTFIX & strDisplay & OPTION_END_TAG & vbCrLf
        rstRecordset.MoveNext
    Next
    OptionListAsString = strOptionList
   ' Exit Function
End Function

Public Function IIF(exp1,ifTrue,ifFalse)
	'ASP version of Intermediate If statement
	If exp1 Then
		IIF = ifTrue
	Else
		IIF = ifFalse
	End If
End Function

Public Function RowColor(intResultsCounter)
	'Change row background color every other row
	If Eval(intResultsCounter\2 = intResultsCounter/2) Then
		RowColor = "tablecolor1"
	Else
		RowColor = "tablecolor2"
	End if
End Function


       
      
        
End Class
%>