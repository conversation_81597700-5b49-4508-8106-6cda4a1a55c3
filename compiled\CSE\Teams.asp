
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Team Names</TITLE>

<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_Procedure.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<%    Dim   rstTeam, strTeam, rstESL, rstWA, strWA,  strID, objEPS, strName, strName2, strType

    set objGeneral = new ASP_CLS_General %>
	<style type="text/css">
.style1 {
	font-family: Arial;
	font-weight: bold;
}
	.auto-style1 {
		border: 1px solid #C0C0C0;
	}
	.auto-style3 {
		border: 1px solid #C0C0C0;
		background-color: #EDF7FE;
	}
	.auto-style4 {
		text-align: left;
	}
	</style>
</head>

	<body>

<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% border=1>
  <tr><td colspan="4" bgcolor="white" class="subheader">&nbsp;</td></tr>

 <TD>
     <p align="left"><font size="3" face="Arial"><a href="Team_Add.asp">Add New</a></font></td>
<td align = right> </td></tr>
	    </table><font face="Arial"><br>
	    <%  
	        Dim SCRIPT_NAME
		Dim strSQL
 
    		    
	strSQL = "SELECT * from tblAuthorizers order by work_area"
		        Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
			if not MyRec.eof then 
 
 

			strSQL = "SELECT VA_Team_Names.* from VA_Team_names   order by VA_Team_Name"
		        Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
			%>
			<table bgcolor = #F0F0F0 align = center class="auto-style1" style="width: 20%" cellspacing="1">
			<thead>
			    <tr>
  
                		<th class="auto-style3"> Team</th>
	     		
                		<th class="auto-style3"> Abb</th>
	     		
              	    </tr>
			    </thead>
			    <tbody>
	
        		        <% While not MyRec.EOF %>
              			    <tr>
         
 
               <td bgcolor="#FFFFFF" class="auto-style4"><font face="Arial"><%= MyRec.Fields("VA_Team_Name").Value %></font></td>              		   
              	                		   
        <td bgcolor="#FFFFFF"><font face="Arial"><%= MyRec.Fields("Team_Abb").Value %></font></td>
              		   
  						 </tr>
                            
              			    <% 
				        MyRec.MoveNext
                 			WEND
                 			MyRec.Close
              			    %>

		

            		</table>
	 
      	
      	  <%   

  
   else
    Response.write ("<font face = arial size = 3>You do not have authorization to view this page")
   
    end if  %><!--#include file="footer.inc"-->