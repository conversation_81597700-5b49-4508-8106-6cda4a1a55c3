

<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Trailer Unload Report</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

<%  
	Dim strsQL, rstEquip

   	Dim objEquipSearch, rstTotals, strCount, strDed, strNet, strTons, rstTotalReceived, strTR, objNew, rstSpecies
  	Dim  strVendor, strBegDate, strEndDate, strSpecies, strMonth, strDateReceived, strDateAdded, rstMonth, strDays
 	Dim  rstVendor, rstEquip3, strsql3, strFee, strFree, strLocation, strCarrier, rstEquipCount
  	Dim objGeneral, MyRec
   	
   	Dim intDirection
  	Dim intPageNumber
   	Dim strPageNav

   intPageNumber = 1

   set objGeneral = new ASP_CLS_General

  if objGeneral.IsSubmit() Then
    Call GetFormData()
    Select Case intDirection
      Case 10: 'goto mocinfo.asp
  
      Case Else:
        Call LoadSearchResults()
    End Select
  else
    intDirection = 0
  end if
Call GetData()
%>

<script language="javascript">
 
  function GotoMOC(MOCID)
  {
    document.forms["form1"].elements["tbl_ID"].value = MOCID;
    document.forms["form1"].elements["Direction"].value = 10;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }

  function Search()
  {
    
    document.forms["form1"].elements["Direction"].value = 1;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }

  function NextPage()
  {
    document.forms["form1"].elements["Direction"].value = 5;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }
  function PrevPage()
  {
    document.forms["form1"].elements["Direction"].value = 6;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }
</script>


<style type="text/css">
.style1 {
	font-family: Arial;
	font-size: x-small;
}
.style2 {
	text-align: left;
}
</style>
</head>


<div class="style2">


<form name="form1" action="Trailer_Unload_report.asp" method="post">
 <input type="hidden" name="Direction" value="1" >
 <input type="hidden" name="tbl_ID" value="" >
 <input type="hidden" name=PageNumber value="<%=intPageNumber%>" >
 
 
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=80%  border=1 align = CENTER>  
  <tr><td  align = left><b><font face="Arial">Trailer/Rail&nbsp; Unload Report</font></b></td>
	<td align = "RIght"><font face="Arial"><b><a href="Loginscreen.asp">HOME</a></b></font></td></tr></table>
	<div align="center">
<TABLE borderColor=#C0C0C0 cellSpacing=0 cellPadding=0 width=80% class="tablecolor1" border=1>  	
	
  <TR>
    <TD bgcolor="#F4F4FF" bordercolor="#F4F4FF"><font face="Arial" size="2"><b>Beginning 
	Unload Date:</b></font></TD>
   <TD bgcolor="#F4F4FF" bordercolor="#F4F4FF">
   <font face="Arial"><input name="BegDate" size="10" maxlength="10" value="<%=strBegDate%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633"></td>

    <TD bgcolor="#F4F4FF" bordercolor="#F4F4FF"><font face="Arial" size="2"><b>Ending 
	Unload Date:</b></font></TD>

    <TD bgcolor="#F4F4FF" bordercolor="#F4F4FF" colspan="2">
   <font face="Arial"><input name="EndDate" size="10" maxlength="10" value="<%=strEndDate%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633"></TD>
    <TD bgcolor="#F4F4FF" bordercolor="#F4F4FF" colspan="2">
   <font face="Arial">&nbsp;</td></tr>
   
  <TR > <TD bgcolor="#F4F4FF" bordercolor="#F4F4FF"><font face="Arial" size="2"><b>Species:</b></font></TD>
  <TD bgcolor="#F4F4FF" bordercolor="#F4F4FF"> <select size="1" name="Species">

     <option value="">--- All ---</option>
  <% strsql = "SELECT distinct  Species from tblCars where Date_received > '1/1/2016'and  Species is not null" 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
while not MyRec.eof %>
            <option <% if strSpecies = MyRec("Species") then %> selected <% end if %>><%= MyRec("Species") %></option>
 
  <% MyRec.movenext
  wend
  MyRec.close %>

     </select></td><td bgcolor="#F4F4FF" bordercolor="#F4F4FF" class="style1">
	<strong>Location:</strong></td>
	<td bgcolor="#F4F4FF" bordercolor="#F4F4FF" class="style1" >
	<select  name="Location">
	     <option value="">--- All ---</option>
	     <% strsql = "Select distinct location from tblcars where date_received > '7/1/2013' order by location"
	        Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
while not MyRec.eof %>     
	     
	            <option <% if strLocation = MyRec("Location") then %> selected <% end if %>><%= MyRec("Location") %></option>
  <% MyRec.movenext
  wend
  MyRec.close %>
       
	</select></td>
	<td bgcolor="#F4F4FF" bordercolor="#F4F4FF" class="style1"> &nbsp;<strong>Carrier:&nbsp;&nbsp;&nbsp;&nbsp;
	</strong>
	<select  name="Carrier">
	     <option value="">--- All ---</option>
	     <option value="R">RAIL/GACX</option>
	     <% strsql = "Select distinct carrier from tblcars where  len(Carrier) > 1 and date_received > '7/1/2013' order by Carrier"
	        Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
while not MyRec.eof %>  
	     
	            <option <% if strCarrier = MyRec("Carrier") then %> selected <% end if %>><%= MyRec("Carrier") %></option>
  <% MyRec.movenext
  wend
  MyRec.close %>
       
	</select> </td>
	
   
    <TD align="center" bgcolor = "#F4F4FF" bordercolor="#F4F4FF">
	<input type="button" onClick="javascript:Search()" value="Search" caption="Search" style="float: right"></TD></TR>
  </TABLE></div>
</form>


  <% if objGeneral.IsSubmit() Then 

%>
   <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=80% border=1 align = center>
    <tr><td colspan="6" bgcolor="white"><font face="Arial" size="2"><b>Search Results</b> - Page <%=intPageNumber%></font></td>
    
    <%   
    strcount = 0
    while not rstEquipCount.Eof
    strcount = strcount + 1
    rstEquipCount.movenext
    wend %>

    <td  align="center"><font face="Arial" size = 2>Total:&nbsp;<%=strCount%></font></td>

    </tr>
    <tr><td colspan="9" bgcolor="white" align="right"><%=strPageNav%>&nbsp;</td></tr>
      <tr class="tableheader">
      	<td  align="center" height="30"><font face="Arial" size = 1>Date<br>Unloaded</font></td>
      	<td  align="center" height="30"><font face="Arial" size = 1>Location</font></td>
      		<td  align="center" height="30"><font face="Arial" size = 1>Release</font></td>
      		<td  align="center" height="30"><font face="Arial" size = 1>SAP #</font></td>
      		

      				<td  align="center" height="30"><font face="Arial" size = 1>Carrier</font></td>
      			<td  align="center" height="30"><font face="Arial" size = 1>Trailer</font></td>
      			      					<td  align="center" height="30"><font face="Arial" size = 1>Shuttle<br>Carrier</font></td>
      				<td  align="center" height="30"><font face="Arial" size = 1>Shuttle<br>Trailer</font></td>
      				
      					<td  align="center" height="30"><font face="Arial" size = 1>From Rail Car</font></td>
      				 	<td  align="center" height="30"><font face="Arial" size = 1>Species</font></td>
      				<td  align="center" height="30"><font face="Arial" size = 1>Weight</font></td>
      			<td  align="center" height="30"><font face="Arial" size = 1>Date<br> Received</font></td>

	<td  align="center" height="30"><font face="Arial" size = 1>Days on Yard</font></td>

	<td  align="center" height="30"><font face="Arial" size = 1>Detention <br>To Date</font></td>


      
  	<% 
      Dim ii
       ii = 0
       strTotalweight = 0
       while not rstEquip.Eof
      strCID = rstEquip("OID")
      strBrokeD = ""
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr class=tablecolor1>
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
  
    
<td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("Date_unloaded")%>&nbsp;</td>	
<td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("Location")%>&nbsp;</td>
<td  align="left"><font face = "arial" size = "1">
<% if len(strCID) > 1 then

strsql = "Select Release_nbr, Broke_Description from tblcars where OID = " & strCID & ""
Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		if not MyRec.eof then
		strRelease = MyREc("Release_nbr")
		strBrokeD = MyRec("Broke_Description")
		MyRec.close %>
	<%= strRelease %>
		<% else %>
		&nbsp;
		<% end if %>
<% else %>
&nbsp;
<% end if %>
</td>
<td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("SAP_NBR")%>&nbsp;</td>



<td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("Carrier")%>&nbsp;</td>
<% if rstEquip.fields("Trailer") = "UNKNOWN" then %>
<td  align="left"><font face = "arial" size = "1">&nbsp;</td>
<% else %>
<td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("Trailer")%>&nbsp;</td>


<% end if %>

<td  >   <font size="1" face="Arial">        <%= rstEquip.fields("Trans_Carrier")%>&nbsp;</font></td>
<td  > <font size="1" face="Arial"> <%= rstEquip.fields("Transfer_trailer_nbr")%></font></td>

<td  align="left"><font face = "arial" size = "1">
<% if rstEquip("RC_CID") > 0 then
strRCID = rstEquip("RC_CID")
strsql = "Select Trailer from tblcars where CID = " & strRCID & ""
Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		strTrailer = MyREc("Trailer")
		MyRec.close %>
		<%= strTrailer %>
		<% else %>
		&nbsp;
		<% end if %>
&nbsp;</td>

<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Species")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1">
<% if len(rstEquip("Net")) > 0 then %>
<%= rstEquip.fields("Net")%>

<% strTotalWeight = strTotalWeight + rstEquip("Net")
 else %>
<%= rstEquip("Tons_Received")%>
<% strTotalWeight = strTotalWeight + rstEquip("Tons_Received")

end if %>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("date_recejved")%>&nbsp;</td>





<% 	if isnull(rstEquip.fields("Date_unloaded")) then 
    strDays = int(Now()- cdate(rstEquip.fields("Date_recejved"))+ .5)
  		
  		else
		strdays =   round(datediff("d", rstEquip.fields("date_recejved"), rstEquip.fields("Date_unloaded")),0)
		end if  

if len(rstEquip.fields("Trans_Carrier")) > 1 then
  	
		 strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & rstEquip.fields("Trans_Carrier") & "' "
 else 		 
           strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & rstEquip.fields("Carrier") & "' " 
end if %>
<td  align="center"><font face = "arial" size = "1"><%= strdays  %>&nbsp;</td>


         <%           
 Set rstEquip3 = Server.CreateObject("ADODB.Recordset")
    rstEquip3.Open strSQL3, Session("ConnectionString")  
    If Not rstEquip3.eof then
    strFee = rstEquip3.fields("Fee")
    strFree = rstEquip3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    rstEquip3.close
    if strDays  > strFree then %>
    <td align = center><font face = "arial" size = "1"> $<%= (int(strDays) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font face = "arial" size = "1">$0&nbsp;</font></td>
	<% end if %>

	
  
   </tr>
    <% 
       ii = ii + 1
       rstEquip.MoveNext
     Wend
    %>
    <tr><td colspan="9">&nbsp;</td><td><font face="arial" size="2"><b>Total Weight:</b></font></td>
    <td><font face="arial" size="2"> <%= round(strTotalWeight,2) %></b></font></td>
    <td colspan="3">&nbsp;</td>
</tr>
   </table>
<table>    <tr>
<td colspan="7" bgcolor="white" align="right"><%=strPageNav%>&nbsp;</td></tr></table>
</div>

<% end if %>


 <% Function GetData()

   set objNew = new ASP_Cls_Fiber
          
	set rstSpecies = objNew.FiberSpecies()
	strSpecies = request.form("Species")
		strLocation = request.form("Location")

End Function
    Function GetFormData()
      intDirection = cint(Request.Form("Direction"))

 	strBegDate = request.form("BegDate")
	strEndDate = request.form("EndDate")
		strSpecies = request.form("Species")
	strLocation = request.form("Location")
	strCarrier = request.form("Carrier")

      intPageNumber = cint(Request.Form("PageNumber"))

 
    End Function

    Function LoadSearchResults()
      Dim objEquipSearch, objTotals

      if intDirection =1 Then
         intPageNumber = 1
      end if

      if intDirection = 5 Then
         intPageNumber = intPageNumber + 1
      end if
      if intDirection = 6 Then
         intPageNumber = intPageNumber - 1
      end if


	strBegDate= request.form("BegDate")
	strEndDate = request.form("EndDate")
	strSpecies = request.form("Species")
	strLocation = request.form("Location")
	strCarrier = request.form("Carrier")


      set objEquipSearch = new ASP_Cls_Fiber
 
 If strCarrier = "R" then 
  set rstEquip = objEquipSearch.UnloadRail(strBegDate, intPageNumber, strEndDate,  strSpecies, strLocation)
    set rstEquipCount = objEquipSearch.UnloadRail(strBegDate, intPageNumber, strEndDate,  strSpecies, strLocation)

   set rstTotals = objEquipSearch.UnloadRailTotals(strBegDate, strEndDate, strSpecies, strLocation)
  else
 set rstEquip = objEquipSearch.Unload_All(strBegDate, intPageNumber, strEndDate, strCarrier, strSpecies,  strLocation)
  set rstEquipCount = objEquipSearch.Unload_All(strBegDate, intPageNumber, strEndDate, strCarrier, strSpecies,  strLocation)
 set rstTotals = objEquipSearch.UnloadTotals(strBegDate, strEndDate, strCarrier, strSpecies, strLocation)
end if
    ' if  not rstTotals.Eof Then
     ' strCount = rstTotals.fields("CountofOID").value

      
    '  end if 

     if ( not rstEquip.Eof) Then
      if ( intPageNumber < rstEquip.fields("TotalPage").value ) Then
        strPageNav = "<a href=javascript:NextPage()><B><font face=arial>Next Page</b></a>"
      end if
      if ( intPageNumber > 1 ) Then
         strPageNav = "<a href=javascript:PrevPage()><b><font face=arial>Previous Page</b></a>&nbsp;&nbsp;&nbsp;&nbsp;" & strPageNav
      end if
     end if

        if ( not rstTotals.Eof) Then
     strTR = rstTotals.fields("countofoid").value
     end if
    End Function
 %><!--#include file="Fiberfooter.inc"-->