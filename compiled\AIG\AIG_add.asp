<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 6.0">

<TITLE>Add AIG User</TITLE>
<!--#include file="classes/asp_cls_DataAccessAIG.asp"-->
<!--#include file="classes/asp_cls_headerAIG.asp"-->
<!--#include file="classes/asp_cls_SessionStringAIG.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<%

Dim strSQL, MyRec

 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then


	Call SaveData() 

End if
%>
<body>
<form name="form1" action="AIG_add.asp?id=<%=strid%>" method="post">
 
<table border="0" cellpadding="0" cellspacing="0" width = 60% style="border-collapse: collapse" bordercolor="000000" height="25" align = center >
  <tr>
    <td  bgcolor="#FFFFFF" bordercolor="#CCCCFF" height="25" >
    <p align="center"><font face="Arial" size="4">Add AIG User  &nbsp;&nbsp;&nbsp; 
    </font>  	</td><td align = center height="25"><font face="Arial"><b><a href="AIG_Users.asp">RETURN</a></b></font></td>

    <td align = center bgcolor="#FFFFFF" height="25">
  <Input name="Submit" type="submit" Value="Submit" style="float: right" ></td>
  </tr>
</table><br><br>
<div align="center">
<table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#808080"  height="10" width="75%">
    <tr>
    <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="22" align="center" ><b>
	<font face="Arial" size="2">User BID</font></b></td>
	 <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="22" align="center" ><b>
	<font face="Arial" size="2">User Name</font></b></td>
    <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="22" align="center" ><b>
	<font face="Arial" size="2">User Type</font></b></td>
   
  </tr>
  
    <tr>
    <td  bordercolor="#CCCCFF" height="47" width = "25%" align="center" >  	<font face="Arial">	
	<input type="text" name="BID" size="13"></font></td>
	   <td  bordercolor="#CCCCFF" height="47" width = "25%" align="center" >  	<font face="Arial">	
	<input type="text" name="User_name" size="25"></font></td>
    <td bordercolor="#CCCCFF" height="47" width = "25%" align="center" >	
		
	<select size="1" name="User_Type">
	<option selected>Select User Type</option>
	<option value="B">Buyer</option>
	<option value="D">Display Only</option>
	<option value="O">Owner</option>
	<option value="V">Vendor Maintainer</option>
	</select></td>
    	

  </tr>
  
 


  </table>



</div>



</form>
   
  

</body>

</html> <%
  
  Function SaveData()
 dim strUsername
 
 if isnull(Request.form("User_name")) then
 strUsername =  ""
else
 strUsername =  Replace(Request.form("User_name"), "'", "''") 
end if
  
  strsql = "Insert into tblAIGUserType (BID, User_Type, User_name) "_
  &" Values('" & Request.form("BID") & "', '" & Request.form("User_Type") & "', '" & strUsername & "')"

   
  	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
          Response.redirect("AIG_Users.asp")
  End Function
  
   %><!--#include file="AIGfooter.inc"-->