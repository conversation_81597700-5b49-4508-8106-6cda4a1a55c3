<html xmlns:m="http://schemas.microsoft.com/office/2004/12/omml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Enter STO Fiber Trailer Receipt</title>
<style type="text/css">
.style28 {
	color: #9F112E;
}
.style29 {
	font-weight: bold;
	font-family: Arial, Helvetica, sans-serif;
}
.style30 {
	text-align: right;
	font-family: Arial, Helvetica, sans-serif;
}
.style31 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.style32 {
	font-weight: bold;
	text-align: center;
}
.style33 {
	color: #000000;
}
.auto-style1 {
	border-width: 1px;
	font-weight: bold;
	text-align: center;
	background-color: #EAF1FF;
}
.auto-style2 {
	border-width: 1px;
	text-align: right;
	font-family: Arial, Helvetica, sans-serif;
	background-color: #EAF1FF;
}
.auto-style3 {
	font-weight: bold;
	border-width: 1px;
	background-color: #EAF1FF;
}
.auto-style4 {
	border-width: 1px;
	background-color: #EAF1FF;
}
.auto-style5 {
	border-width: 1px;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	background-color: #EAF1FF;
}
.auto-style6 {
	color: #000000;
	font-size: x-small;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID, strLoad, MyConn, objMOC, rstFiber, strLocation, strSAP, rstTrailer , strTrailerWeight , strTractor   
    Dim strTrailer, strCarrier, strSQL3, MyConn3, strVID, strUOM, strPounds, strRail, strDate_shipped
    Dim strLoadNbr, strTrailerTID, strBales, stralert
    Dim strRECNbr
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strGenerator
    Dim strGenCity
    Dim strGenState, gAuditStatus
    Dim strOther, strR,  gSpecies, gPO, gSAP_Nbr, gVendor, gOID, strNet, gWeight_required, gTrailerStatus, strKCWeighed
    Dim MyRec5, strsql5, strCarID

    strLoad  = Trim(Request.QueryString("id")) 
	

 	set objGeneral = new ASP_CLS_General



if objGeneral.IsSubmit() Then

If request.form("Cancel") = "ON" then
Response.redirect ("SelectSTO.asp")
else
   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
	strLoad  = Trim(Request.QueryString("id")) 
 	strTrailer = Request.form("Trailer")
	strOther = Replace(Request.form("Other_Comments"), "'", "''") 
	strDateReceived = Request.form("Date_received")
	strSAPWeight = request.form("SAP_Weight")
	
	strCarrier = Request.form("Carrier")
	strLocation = Request.form("Location")

	strGenerator = Replace(Request.form("Generator"), "'", "''")
	
	

If isnull(strTrailer) or strTrailer = ""  or isnull(strCarrier) or strCarrier = ""  or len(strGenerator)<2  or strSAPWeight = ""  then
	Session("Trailer") = strTrailer
	Session("Carrier") = strCarrier
	Session("Generator") = strGenerator
	Session("TrailerTID") = strTrailerTID

	Session("SAPWeight") = strSAPWeight






	Response.redirect("EnterSTOWaddingReceipt.asp?id=" & strLoad & "&n=T")
	else
	strLoad = request.form("Load")
		strsql = "SELECT CID FROM tblCars WHERE STO_Number = " & strLoad & ""

   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
	If not MyRec.eof then
	strPOST = "NO"
	strPostCID = MyRec.fields("CID")
	else 
	strPOST = "OK"
	end if
	MyRec.close
	If strPOST = "OK" then

	

				Call SaveData() 
		If Request.form("Print_receipt") =  "ON" then
			
				Response.redirect("STO_Wadding_Truck_receipt.asp?id=" & strCarID)
				
		else
		Response.redirect ("SelectSTO.asp")
		end if
		

	else
	
	Response.redirect("EnterSTOWaddingReceipt.asp?id=" & strLoad & "&n=T1")
	end if	
		
	end if
else
Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to save this information.</font></br>")
MyRec.close
end if
end if
  
ELSE
 
	if len(strLoad) > 4 then
	strsql = "Select tblSTOInbound.* from tblSTOInbound where Load_nbr = " & strLoad & ""
		 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
  If not MyRec.eof then 
  strTrailer = MyRec.fields("Ship_From_State")
  strCarrier = MyRec.fields("Ship_From_CIty")
  strLoad = MyRec("Load_Nbr")
  strGenerator = MyRec("Ship_From_Name")
  strSAP = MyREc("Sap_NBr")
  strPO = MyREc("PO")
  strBOL = MyREc("BOL")
  end if
  MyRec.close
  else
  strSAP = request.querystring("s")
  end if
  strsql = "SELECT * from tblBrokeSap where SAP = '" & strSAP & "'"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not myrec.eof then
    strSpecies = MyREc("Type")
    end if
    MyRec.close

	
	strDateReceived = formatdatetime(Now(),2)
	
   	 
   	 

end if
%>



<body>


<% if Request.querystring("n") = "T" or request.querystring("n") = "T1" then 
strTrailer = Session("Trailer")
strCarrier = Session("Carrier")
strGenerator = Session("Generator")
strSAPWeight = Session("SAP_Weight")
 %>
<p align = center><font face = arial size = 4 color="teal"><b>

<font face = arial size = 3 color = red>
<span class="style28">

<% if isnull(Session("Trailer")) or len(Session("Trailer"))< 1 then %>
You must enter a Trailer number.<br>
<% end if %>
<% if strCarrier = "" then %>
You must enter a Carrier<br>
<% end if %>
<% if len(strGenerator) < 2 then %>
You must enter the KC Site Load is Shipped From<br>
<% end if %>
<% if len(strSapWeight) < 2 then %>
You must enter the Weight from the Shipping Ticket<br>
<% end if %>


<% if request.querystring("n") = "T1" then
	Response.write("<br><font face=arial size=3 color=red><br>Receipt Number " & strPostCID & " already has this load number.  It could be you clicked the Submit button twice.</font><br>")
	end if
	
end if %><br>
<table width = 100%><tr><td width = 33% style="height: 24px">
<font face="Arial" size="2"><b>Species: <%= strSpecies%>&nbsp;&nbsp; SAP Nbr: <%= strSAP %></b>

</td><td align = center style="height: 24px; width: 50%;"><b><font face="Arial" size="4">
Enter Trailer Receipt for STO Delivery Number:&nbsp;<%= strLoad%></font> </b></td>
	<td align = right width = 33% style="height: 24px"><b><font face = arial size = 2><a href="SelectSTO.asp">RETURN</a></font></b></td></tr></table>




<form name="form1" action="EnterSTOWaddingReceipt.asp?id=<%=strLoad%>" method="post">
<input type="hidden" name="Species" value="<%= strSpecies %>">
<input type="hidden" name="SAP_Load" value="<%= strSap %>">
<input type="hidden" name="PO" value="<%= strPO %>">
<input type="hidden" name="BOL" value="<%= strBOL %>">
<div align="center">
<table border="1" cellspacing="0" width="70%" bgcolor="#FFFFEA" style="border-collapse: collapse" cellpadding="0" bordercolorlight="#C0C0C0">
  <tr>
    <td  align = right width="17%" style="height: 42px" class="auto-style3" >
   <font face="Arial" size="2">Trailer:&nbsp;</font></td>
<td  align = left style="height: 42px" class="auto-style4">

      <font face="Arial">

      <input name="Trailer" size="15" value = "<%= strTrailer%>" style="font-weight: 700; width: 133px;" tabindex="1"><b>
		</b>&nbsp;&nbsp;</font></td>
<td  align = left width="29%" style="height: 42px" class="auto-style4"> <font face="Arial"><font size="2"><b>Check to Print Receipt:</b></font><input type="checkbox" name="Print_receipt" value="ON" checked></td></tr>
  <tr>

      <td align = right width="17%" style="height: 33px" class="auto-style3">
	<font face="Arial" size="2">Select Carrier:</font></td>
<td  align = left colspan="2" style="height: 33px" class="auto-style4">

      <font face="Arial">   
	<select name="Carrier" style="font-weight: 700">
 	<option value="" selected>  Select Carrier (Required)</option>
 	<% strsql = "Select Carrier,C_Description, Weight from tblCarrier order by Carrier"
 	   	 Set MyRec4 = Server.CreateObject("ADODB.Recordset")
   	 MyRec4.Open strSQL, Session("ConnectionString")
   	 while not MyRec4.eof %>
   	 <option <% if strCarrier = MyRec4("Carrier") then %> selected <% end if %> value="<%= MyRec4("Carrier") %>"><%= MyRec4("Carrier") %>&nbsp;<%= MyRec4("C_Description") %>&nbsp;<%= MyRec4("Weight") %></option>"

 <%MyRec4.Movenext
 wend
 MyRec4.close %>
     </select></font><font face="Arial" size="2"><b>    

      &nbsp;</td>
</tr>



       <tr>
		<td  align = right width="17%" style="height: 34px" class="auto-style3" ><font face = arial size = 4 color="teal">

<span class="style28">

		<font face="Arial" size="2" color="red">
		<span class="style33">Weight from&nbsp; Shipping Ticket</span>:</font></td>
<td align = left colspan="2" style="height: 34px" class="auto-style4"> 

<font face = arial size = 3 color = red>

<font face="Arial">  
<span class="style28">

	<strong>  
	<input name="SAP_Weight" size="15" value="<%= strSAPWeight%>" tabindex="6"></strong></span><strong><span class="auto-style6"> 
lbs</strong></td>
</tr>

       <tr>
		<td  align = right width="17%" style="height: 34px" class="auto-style4" >

<font face = arial size = 3 color = red>
		<b>

<span class="style28">

		<font face="Arial" size="2" color="red">
		<span class="style33">STO Delivery #</span></font></span></b><span class="style28"><font face="Arial" size="2" color="red"><span class="style33">: </span></font></td>
<td align = left colspan="2" style="height: 34px" class="auto-style3"> 

<font face = arial size = 3 color = red>
<span class="style28">

<font face="Arial">  
	<input name="Load" size="15" value="<%= strLoad %>" tabindex="6"></td>
</tr>

       <tr>
		<td  align = right width="17%" style="height: 34px" class="auto-style3" ><font face="Arial" size="2">Date Received:&nbsp;</font></td>
<td align = left colspan="2" style="height: 34px" class="auto-style4"> <font face="Arial"> 
<input name="Date_Received" size="15" value = "<%= strDateReceived%>" style="font-weight: 700" tabindex="7"></font></td>
</tr>
              <tr>
          <td  align = right width="17%" style="height: 38px" class="auto-style3" >
   <font face="Arial" size="2">Site Shipped From:&nbsp;</font></td>
<td align = left colspan="2" style="height: 38px" class="auto-style4">
      <font face="Arial">
      <input name="Generator" size="25" value = "<%= strGenerator %>" style="font-weight: 700" tabindex="8"></font></TD></tr>
         <tr>
          <td  align = right width="17%" style="height: 37px" class="auto-style3" >
  <font face="Arial" size="2">Comments:&nbsp;</font></td >
   <td align = left colspan="2" style="height: 37px" class="auto-style4">   <font face="Arial">   
	<input name="Other_Comments" size="25" value = "<%= strOther%>" style="font-weight: 700; width: 364px;" tabindex="9">&nbsp;&nbsp;&nbsp;</td></tr>
<tr>
    <td width="17%" class="auto-style2" style="height: 29px">
	<font face = arial size = 3>
	<span class="style29">

	<font size="2">Location:&nbsp;</font></td>

    <td colspan="2" class="auto-style5" style="height: 29px">YARD</td>
  </tr>

    
  <tr>
    <td width="17%" colspan="3" class="auto-style1">

<font face = arial size = 3 color = red>
<span class="style28">

	<font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></td>

  </tr>

    </table>

</div>

</form>

<%  

Function SaveData()
	
	Dim strRightNow, strAud
	strRightnow = Now()	
	
	strSAPWeight = round(REquest.form("SAP_Weight")/2000,2)
	strLoad = request.form("Load")
	
	strsql =  "INSERT INTO tblCars (Carrier, Species, Grade, SAP_Nbr, Vendor, Date_received, STO_Number, Location_unloaded, Location, "_
	&"  Generator,  Trailer, Other_Comments, Tons_Received, Net,   Entry_Time, Entry_BID, Entry_Page, Status, PO, BOL) "_
	&" SELECT   '" & strCarrier & "', '" & request.form("Species") & "', 'WADDING', '" & Request.form("SAP_Load") & "', '" & strGenerator & "',  '" & strDateReceived & "', '" & strLoad & "', 'RF', 'YARD',  '" & strGenerator & "',   "_
	&" '" & strTrailer & "', '" & strOther & "',  " & strSAPWEight & ", " & strSAPWEight & ",   '" & strRightNow & "', "_
	&"  '" & Session("EmployeeID") & "', 'EnterSTOWaddingReceipt', '" & strSAPWEight & "', '" & request.form("PO") & "','" & request.form("BOL") & "' "
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
			strsql5 = "SELECT Max(tblCars.CID) AS MaxOfCID FROM tblCars WHERE tblCars.Trailer = '" & strTrailer & "'"
			
   	 Set MyRec5 = Server.CreateObject("ADODB.Recordset")
   	 MyRec5.Open strSQL5, Session("ConnectionString")
   	 strCarID = MyRec5.fields("MaxofCID")
   	 MyRec5.close

			

End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->