
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Delete Administrator</TITLE>
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_Session.asp"-->
<!--#include file="classes/asp_cls_DataAccess.asp"-->

<% 

Dim strSQL, MyRec

 set objGeneral = new ASP_CLS_General
 Dim strid
 
 strid= request.querystring("id")
 strSite = request.querystring("m")
 
 strsql= "Select tblOT_Admin.* from tblOT_Admin where ID = " & strid
 
     Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    strEmployee = MyRec.Fields("Emp_Name")
    strBID = MyRec.Fields("EMP_ID")
    MyRec.close
 

if objGeneral.IsSubmit() Then

	Call SaveData() 
	end if

%>
<style type="text/css">
.style1 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
.style2 {
	border: 1px solid #C0C0C0;
}
.style3 {
	font-family: Arial;
}
</style>
</head>

<body>
<form name="form1" action="Site_Admin_Delete.asp?id=<%= strid%>" method="post">
 
<table border="0" cellpadding="0" cellspacing="0" width = 60% style="border-collapse: collapse" bordercolor="000000" height="25" align = center >
  <tr>
    <td  bgcolor="#FFFFFF" bordercolor="#CCCCFF" height="25" >
    <p align="center"><font face="Arial" size="4">Delete Administrator</font></td><td align = center height="25"><font face="Arial"><b><a href="Site_Admins.asp">RETURN</a></b></font></td>

    <td align = center bgcolor="#FFFFFF" height="25">
  <Input name="Submit" type="submit" Value="Click to Delete" style="float: right" ></td>
  </tr>
</table><br><br>
<div align="center">
<table cellpadding="0" cellspacing="0" style="width: 45%;" bordercolor="#808080"  height="10" class="style1">
    <tr>
        <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="22" align="center" ><b>
	<font face="Arial">Administrator</font></b></td>
   
    <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="22" align="center" class="style3" >
	<strong>Employee ID</strong></td>
   
  </tr>
  
    <tr>
        <td height="47"  align="center" class="style2" >  	
     <font face="Arial">	
	<%= strEmployee %></font></td>

    <td height="47"  align="center" class="style2" >  	
     <font face="Arial">	
	<%= strBID %></font></td>
    	

  </tr>
  </table>
</div>



</form>
   
  

<p>&nbsp;</p>
   
  

<p>&nbsp;</p>
   
  

</body>

</html> <%
 
  Function SaveData()
strid = request.querystring("id")

  strsql = "Delete from tblOT_Admin where ID = " & strid

   
  	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
 
         
          Response.redirect("Site_Admins.asp")
  End Function
  
   %><!--#include file="footer.inc"-->