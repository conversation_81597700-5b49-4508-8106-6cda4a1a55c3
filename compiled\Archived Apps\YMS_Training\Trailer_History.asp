 
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">
<TITLE>All-Other Inbound Material Lookup</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

<%  
	Dim strsQL, rstEquip

   	Dim objEquipSearch, rstTotals, strCount, strDed, strNet, strTons, rstTotalReceived, strTR, objNew, rstSpecies
  	Dim  strVendor, strBegDate, strEndDate, strSpecies, strMonth, strDateReceived, strDateAdded, rstMonth
 	Dim  rstVendor, rstNF
  	Dim objGeneral, strPO, gcount, strDays
  	
  	Dim strTrailer, strAT, strStype, objMOC
   	
   	Dim intDirection
  	Dim intPageNumber
   	Dim strPageNav

   intPageNumber = 1

   set objGeneral = new ASP_CLS_General

  if objGeneral.IsSubmit() Then
    Call GetFormData()
    Select Case intDirection
      Case 10: 'goto mocinfo.asp
  
      Case Else:
        Call LoadSearchResults()
    End Select
  else
    intDirection = 0
  end if
Call GetData()
%>

<script language="javascript">
 
  function GotoMOC(MOCID)
  {
    document.forms["form1"].elements["tbl_ID"].value = MOCID;
    document.forms["form1"].elements["Direction"].value = 10;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }

  function Search()
  {
    
    document.forms["form1"].elements["Direction"].value = 1;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }

  function NextPage()
  {
    document.forms["form1"].elements["Direction"].value = 5;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }
  function PrevPage()
  {
    document.forms["form1"].elements["Direction"].value = 6;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }
</script>


<form name="form1" action="Trailer_History.asp" method="post">
 <input type="hidden" name="Direction" value="1" >
 <input type="hidden" name="tbl_ID" value="" >
 <input type="hidden" name=PageNumber value="<%=intPageNumber%>" >

  <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=50%  border=1 align = CENTER>  <tr>
    <TD width="87"><b><font face="Arial" size="2">Trailer:</font></b></TD>
    <TD>
   <font face="Arial">
	  <input type="text" name="Trailer" size="15" value = "<%= strTrailer%>">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</font><input type="button" onClick="javascript:Search()" value="Search" caption="Search"></TD>    <TD>&nbsp;</TD>
        <TD>
   &nbsp;</TD>  <Td>

&nbsp;</td>    <TD>
   &nbsp;</TD>  <Td>
 <TD>
   <font face="Arial">    &nbsp;</TD><Td colspan = 2 align = right></TD>
   
   &nbsp;</td><td>

&nbsp;</td><td>

&nbsp;</td>


 </TR>



  </TABLE></form>
    
    <% if objGeneral.IsSubmit() Then %>

&nbsp;<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=96% class="tablecolor1" border=1 align = center>
    <tr>
		<td colspan="16" bgcolor="white"><font face="Arial" size="2"><b>Search Results</b> - Page <%=intPageNumber%></font></td>
 
    </tr>
    <tr><td colspan="16" bgcolor="white" align="right"><%=strPageNav%>&nbsp;</td></tr>
      <tr class="tableheader">
   <td  align="center"><font face="Arial" size="1">Movement</font></td>
   </font><font face = Arial size="1">
    	<td  align="center"><font face="Arial" size="1">Trailer</font></td>
    	    	<td  align="center"><font face="Arial" size="1">Transfer Trailer</font></td>
   <td  align="center"><font face="Arial" size="1">Species</font></td>
 	
  
		<td  align="center"><font face="Arial" size="1">Location</font></td>
	<td  align="center"><font face="Arial" size="1">Date received</font></td>
	<td  align="center"><font face="Arial" size="1">Received By</font></td>
	<td  align="center"><font face="Arial" size="1">Date Transfered</font></td>
	<td  align="center"><font face="Arial" size="1">Date unloaded</font></td>
	<td  align="center"><font face="Arial" size="1">Transfer Date Unloaded</font></td>
	<td  align="center"><font face="Arial" size="1">Release Number</font></td>
	<td  align="center"><font face="Arial" size="1">Carrier</font></td>

	<td  align="center"><font face="Arial" size="1">Quantity</font></td>

	<td  align="center"><font face="Arial" size="1">Comments</font></td>



      
  	<% 
      Dim ii
       ii = 0
       while not rstEquip.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr class=tablecolor1>
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    <td  align="center"><font face = "arial" size = "1"><a target="_blank" href="Movement_Detail.asp?id=<%= rstEquip.fields("CID")%>">View</a></td>
<td  align="center" ><font face = "arial" size = "1"><%=rstEquip.fields("Trailer")%>&nbsp;</td>

<td  align="center" ><font face = "arial" size = "1"><%=rstEquip.fields("Transfer_Trailer_nbr")%>&nbsp;</td>

<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Species")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Location")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Date_received")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Entry_BID")%>&nbsp;</td>
<td  align="left" ><font face = "arial" size = "1"><%=rstEquip.fields("Transfer_date")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Date_unloaded")%>&nbsp;</td>



<td  align="center" ><font face = "arial" size = "1"><%=rstEquip.fields("Trans_unload_date")%>&nbsp;</td>
<td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("Release_nbr")%>&nbsp;</td>


<td  align="right"><font face = "arial" size = "1"><%=rstEquip.fields("Carrier")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("tons_received")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Other_comments")%>&nbsp;</td>

	
  
   </tr>
    <% 
       ii = ii + 1
       rstEquip.MoveNext
     Wend
    %>
   </table>
<table>    <tr><td colspan="7" bgcolor="white" align="right"><%=strPageNav%>&nbsp;</td></tr></table>

<% end if %><% Function GetData()

   set objNew = new ASP_Cls_Fiber
          




End Function
    Function GetFormData()
      intDirection = cint(Request.Form("Direction"))
       intPageNumber = cint(Request.Form("PageNumber"))
     
    
	strTrailer= request.form("Trailer")

	
End Function
 Function GetData()   
  
        set objMOC = new ASP_CLS_FIBER
    
     strTrailer= request.form("Trailer")

    End Function 
    

    Function LoadSearchResults()
      Dim objEquipSearch, objTotals

      if intDirection =1 Then
         intPageNumber = 1
      end if

      if intDirection = 5 Then
         intPageNumber = intPageNumber + 1
      end if
      if intDirection = 6 Then
         intPageNumber = intPageNumber - 1
      end if



      set objEquipSearch = new ASP_Cls_Fiber
	strTrailer= request.form("Trailer")    
 
 set rstEquip = objEquipSearch.HistSearch(strTrailer, intPageNumber)


     if ( not rstEquip.Eof) Then
      if ( intPageNumber < rstEquip.fields("TotalPage").value ) Then
        strPageNav = "<a href=javascript:NextPage()><B>Next Page</b></a>"
      end if
      if ( intPageNumber > 1 ) Then
         strPageNav = "<a href=javascript:PrevPage()><b>Previous Page</b></a>&nbsp;&nbsp;&nbsp;&nbsp;" & strPageNav
      end if
     end if

     
    End Function
 %><!--#include file="Fiberfooter.inc"-->