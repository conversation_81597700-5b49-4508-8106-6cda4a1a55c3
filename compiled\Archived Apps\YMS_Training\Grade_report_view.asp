
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Grading Report List</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

<%  
	Dim strsQL, rstEquip

   	Dim objEquipSearch,  objNew, strRelease, strDateto, strDatefrom
  	Dim  strVendor, rstMonth
 	Dim  rstVendor
  	Dim objGeneral, strPO

   	Dim intDirection
  	Dim intPageNumber
   	Dim strPageNav

   intPageNumber = 1

   set objGeneral = new ASP_CLS_General

	if objGeneral.IsSubmit() Then 

	strPO = Trim(request.form("PO"))
	strVendor = Trim(Request.form("Vendor"))
	strRelease = Trim(request.form("Release"))
	strDatefrom = request.form("Date_from")
	strDateto = request.form("Date_to")
	strnow = now()
	
	if len(strDateFrom) < 1 then
	  strDatefrom = "01/01/2018"
	end if
	
	if len(strDateTo) < 1 then
	  strDateto = formatdatetime(strnow, 2)
	end if


	strsql = "SELECT tblcars.cid, tblOccGrading.deduct_approval, tblCars.Carrier, tblcars.date_unloaded, tblCars.species, tblCars.Generator, tblCars.trailer, tblcars.vendor,  "_
	& " tblCars.PO, tblcars.release_nbr,  tblCars.Date_received, tblCars.Tons_received, tblcars.net, tblCars.Deduction, tblGradeEmail.Date_sent, tblGradeEmail.Sent_by, "_
	& " tblGradeEmail.Total, tblGradeEmail.deduction, tblGradeEmail.net, tblOCCGrading.New_Broke "_
	& " FROM tblGradeEmail RIGHT JOIN tblOCCGrading ON tblGradeEmail.CID = tblOCCGrading.CID INNER JOIN tblCars ON tblCars.CID = tblOCCGrading.CID where tblcars.Trailer <> 'UNKNOWN' "

	if len(strVendor) > 0 then
      strsql = strsql & " and tblcars.vendor = '" & strVendor & "' "
    end if
    
    if len(strPO) > 0 then
	  strsql = strsql & " and tblCars.PO = '" & strPO & "'"
	end if
	
	if len(strRelease) > 0 then
	  strsql = strsql & " and tblCars.Release_nbr = '" & strRelease & "'"
	end if
	
	if len(strDatefrom) > 0 then
	  strsql = strsql & " and tblcars.date_unloaded >= '" & strDatefrom & "'"
	end if
	
	if len(strDateto) > 0 then
	  strsql = strsql & " and tblcars.date_unloaded <= '" & strDateto & "'"
	end if
	
	strsql = strsql & " ORDER BY Release_nbr"
	
	'response.write(strsql)
	
	Set rstEquip = Server.CreateObject("ADODB.Recordset")
	rstEquip.Open strSQL, Session("ConnectionString")

	end if

%>

<script language="javascript">
 
</script>


<style type="text/css">
.auto-style1 {
	border-color: #C0C0C0;
	border-width: 1px;
	background-color: #EAF1FF;
}
.auto-style2 {
	font-weight: bold;
	border-color: #C0C0C0;
	border-width: 1px;
	background-color: #EAF1FF;
}
.auto-style3 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	background-color: #EAF1FF;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.auto-style4 {
	border: 1px solid #808080;
}
.auto-style5 {
	font-size: x-small;
}
.auto-style6 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	background-color: #EAF1FF;
	font-size: x-small;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
</style>
</head>


<form name="form1" action="Grade_report_view.asp" method="post">
 <input type="hidden" name="Direction" value="1" >
 <input type="hidden" name="tbl_ID" value="" >
 <input type="hidden" name=PageNumber value="<%=intPageNumber%>" >
	<div align="center">
<TABLE borderColor=#E7D1C2 cellSpacing=0 cellPadding=0 width="100%" border=0>  
  <tr><td colspan="5" size = "2" bordercolor="#FFFFFF">
	<font face="Arial"><b>
	Grading Reports</b> </font></td>
	<td align = "RIght" bordercolor="#FFFFFF">
	<font face="Arial"><b><a href="Loginscreen.asp">HOME</a></b></font></td></tr></table>
	<TABLE borderColor=#E7D1C2 cellSpacing=0 cellPadding=0 width="80%" border=1> 
  <TR>
    <TD align="center" class="auto-style2">
    <font face="Arial" size="2">PO</font></TD>
    <TD align="center" class="auto-style1">
    &nbsp;<b><font face="Arial" size="2">Release</font></TD>    
    <TD align="center" class="auto-style2">
    <font face="Arial" size="2">Vendor</font></TD>    
    <TD align="center" class="auto-style2">
    <font face="Arial" size="2">Date From</font></TD>    
    <TD align="center" class="auto-style2">
    <font face="Arial" size="2">Date To</font></TD>    




    <TD align="center" class="auto-style1">
    &nbsp;</TD>    

 </TR>

  <TR>
         <TD bordercolor="#FFFFFF" align="center">
   <font face="Arial">
	<input name="PO" size="10" maxlength="10" value="<%=strPO%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633" tabindex="1"></TD>
    <TD bordercolor="#FFFFFF" align="center">
     &nbsp;<font face="Arial"><input name="Release" size="10" maxlength="10" value="<%=strRelease%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633" tabindex="2"></TD>


    <TD bordercolor="#FFFFFF" align="center">
     <font face="Arial">
	<input name="Vendor" size="20" maxlength="20" value="<%=strVendor%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633" tabindex="3"></TD>


    <TD bordercolor="#FFFFFF" align="center">
     <font face="Arial">
		<input name="Date_from" size="11" maxlength="10" value="<%=strDatefrom%>" style="padding:0; bgcolor:#ff6633; height: 20px;" tabindex="4"></TD>


    <TD bordercolor="#FFFFFF" align="center">
     <font face="Arial">
		<input name="Date_to" size="11" maxlength="10" value="<%=strDateto%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633" tabindex="5"></TD>


    <TD bordercolor="#FFFFFF" align="center">
	<button type="submit">Search</button></td>      
</TR>

  </TABLE></div>
</form>


  <% if objGeneral.IsSubmit() Then 
%>
   <TABLE cellSpacing=0 cellPadding=0 align = center class="auto-style4" style="width: 100%">
 
    <tr><td colspan="15" bgcolor="white" align="right"><%=strPageNav%>&nbsp;</td></tr>
      <tr bgcolor="#F5EBEB">
   <% ' Mike Durham, Gregory Pate, Steven Day, Gary Dunn, Rodney Vickers
   if Session("EmployeeID") = "B96138"   or Session("EmployeeID") = "B88887" or Session("EmployeeID") = "B53909" or Session("EmployeeID") = "C97338" or Session("EmployeeID") = "U04211" or   Session("EmployeeID") = "U10090" then %> 
        <td  align="center" class="auto-style6" colspan="4">&nbsp;</td>
       <% end if %>
     
 	<td  align="center" class="auto-style6"><font face="Arial">PO</font></td>
	<td  align="center" class="auto-style6"><font face="Arial">Release</font></td>
	<td  align="center" class="auto-style6"><font face="Arial">Species</font></td>
	<td  align="center" class="auto-style6"><font face="Arial">Vendor</font></td>
	<td  align="center" class="auto-style6"><font face="Arial">Trailer</font></td>
	<td  align="center" class="auto-style6"><font face="Arial">Carrier</font></td>
	<td  align="center" class="auto-style3">
	<font face="Arial" size = 1 class="auto-style5">Date<br> Received</font></td>
	<td  align="center" class="auto-style3">
	<font face="Arial" size = 1 class="auto-style5">Date<br> Unloaded</font></td>
	<td  align="center" class="auto-style6"><font face="Arial">Generator</font></td>
	<td  align="center" class="auto-style6"><font face="Arial">Email Date</font></td>
	<td  align="center" class="auto-style6"><font face="Arial">Sent by</font></td>
	<td  align="center" class="auto-style6"><font face="Arial">Tons</font></td>
	<td  align="center" class="auto-style6"><font face="Arial">Deduction</font></td>
		<td  align="center" class="auto-style6"><font face="Arial">Approval</font></td>

	<td  align="center" class="auto-style6"><font face="Arial">Net</font></td>

      
  	<% 
      Dim ii
       ii = 0
       while not rstEquip.Eof
       if rstEquip.fields("Trailer") <> "UNKNOWN" then
       strCID = rstEquip("CID")
       strsql2 = "Select QID from tblOCCGrading where CID = " & strCID
       
           Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL2, Session("ConnectionString")
    if not MyRec.eof then
    strQID = MyREc("QID")
    end if
    MyRec.close
	
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="#FFFFFF">
    <% else %>
       <tr bgcolor="#F5EBEB">
    <% end if %>
      
   <% ' Mike Durham, Gregory Pate, Steven Day, Gary Dunn, Rodney Vickers
   if Session("EmployeeID") = "B96138"   or Session("EmployeeID") = "B88887" or Session("EmployeeID") = "B53909" or Session("EmployeeID") = "C97338" or Session("EmployeeID") = "U04211" or   Session("EmployeeID") = "U10090" then %> 
       <% if rstEquip.fields("New_Broke") then %>
 <td  align="center" class="auto-style5"><font face = "arial"><a href="Grading_entry_broke_edit.asp?p=g&id=<%=rstEquip.fields("CID")%>">Edit</a></td>  
       <% elseif strQID > 59553 then %>   
 <td  align="center" class="auto-style5"><font face = "arial"><a href="Grading_Edit_new.asp?p=g&id=<%=rstEquip.fields("CID")%>">Edit</a></td>  
 <% else %>

 <td  align="center" class="auto-style5"><font face = "arial"><a href="Grading_Edit.asp?p=g&id=<%=rstEquip.fields("CID")%>">Edit</a></td>  
       <% end if %>
        <% end if %>
   <% if rstEquip.fields("New_Broke") then %>
 <td  align="center" class="auto-style5"><font face = "arial"><a href="Grading_entry_broke_view.asp?id=<%=rstEquip.fields("CID")%>">View</a></td>  
   <% elseif strQID > 59553 then %>   
 <td  align="center" class="auto-style5"><font face = "arial"><a href="Grading_view_new.asp?id=<%=rstEquip.fields("CID")%>">View</a></td>  
 <% else %>
  <td  align="center" class="auto-style5"><font face = "arial"><a href="Grading_view.asp?id=<%=rstEquip.fields("CID")%>">View</a></td>  

  <% end if %>
  <% if rstEquip.fields("New_Broke") then %>
  <td  align="center" class="auto-style5"><font face = "arial"><a href="Grading_entry_broke_print.asp?id=<%=rstEquip.fields("CID")%>">Print</a></td>  
  <% elseif strQID > 59553 then %>   
  <td  align="center" class="auto-style5"><font face = "arial"><a href="GradePrint_New.asp?id=<%=rstEquip.fields("CID")%>">Print</a></td>  
<% else %>
  <td  align="center" class="auto-style5"><font face = "arial"><a href="GradePrintList.asp?id=<%=rstEquip.fields("CID")%>">Print</a></td>  
  <% end if %>
  
   <% if strQID > 59553 then %> 
   <td  align="center" class="auto-style5"><font face = "arial"><a href="GradeEmail_new.asp?id=<%=rstEquip.fields("CID")%>">Email</a></td>  
  <% else %> 
 <td  align="center" class="auto-style5"><font face = "arial"><a href="GradeEmail.asp?id=<%=rstEquip.fields("CID")%>">Email</a></td>  
  <% end if %>
 
<td  align="center" class="auto-style5"><font face = "arial" size = "2">
<span class="auto-style5"><%=rstEquip.fields("PO")%>&nbsp;</span></td>

<td  align="center"><font face = "arial" size = "2"><span class="auto-style5"><%=rstEquip.fields("Release_nbr")%>&nbsp;</span></td>

<td  align="center"><font face = "arial" size = "2"><span class="auto-style5"><%=rstEquip.fields("Species")%>&nbsp;</span></td>
<td  align="left"><font face = "arial" size = "2"><span class="auto-style5"><%=rstEquip.fields("Vendor")%>&nbsp;</span></td>
<td  align="left"><font face = "arial" size = "2"><span class="auto-style5"><%=rstEquip.fields("Trailer")%>&nbsp;</span></td>
<td  align="left"><font face = "arial" size = "2"><span class="auto-style5"><%=rstEquip.fields("Carrier")%>&nbsp;</span></td>
<td  align="center"><font face = "arial" size = "2"><span class="auto-style5"><%=rstEquip.fields("date_received")%>&nbsp;</span></td>
<td  align="center"><font face = "arial" size = "2"><span class="auto-style5"><%=formatdatetime(rstEquip.fields("date_unloaded"),2) %>&nbsp;</span></td>
<td  align="center"><font face = "arial" size = "2"><%=rstEquip.fields("Generator")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "2"><%=rstEquip.fields("Date_sent")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "2"><%=rstEquip.fields("Sent_by")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "2"><%=rstEquip.fields("Tons_received")%>&nbsp;</td>
	<td  align="right"><font face="Arial" size = 2><%= rstEquip.fields("Deduction")%>&nbsp;</font></td>
		<td  align="right"><font face="Arial" size = 2><%= rstEquip.fields("Deduct_Approval")%>&nbsp;</font></td>

	<td  align="right"><font face="Arial" size = 2><%= rstEquip.fields("Net")%>&nbsp;</font></td>



  
   </tr>
    <% 
       ii = ii + 1
       end if
       rstEquip.MoveNext
     Wend
    %>
   </table>
<table>    <tr><td colspan="7" bgcolor="white" align="right"><%=strPageNav%>&nbsp;</td></tr></table>
<% end if %><!--#include file="Fiberfooter.inc"-->