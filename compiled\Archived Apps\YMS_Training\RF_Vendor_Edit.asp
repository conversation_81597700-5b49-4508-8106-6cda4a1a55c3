																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Edit RF Vendor </TITLE>


<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 



<% Dim MyRec, strsql, MyRec2, strsql2,  MyConn3, strsql3, strid

strid = Request.querystring("id")


  strsql = "Select * from tblTier where ID = " & strid
  
      Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


  set objGeneral = new ASP_CLS_General
  if objGeneral.IsSubmit() Then	
  
  if request.form("Delete") = "Y" then
  
  strsql = "Delete from tblTier where ID = " & strid
  	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

  else
  
  

  strVendor = Replace(Request.form("Vendor"), "'", "''")	
  strGenerator= Replace(Request.form("Generator"), "'", "''")	
  strCity = Replace(Request.form("City"), "'", "''")
  strComments = Replace(Request.form("Comments"), "'", "''")		
  strTest = Replace(Request.form("Test"), "'", "''")
  strContact = Replace(Request.form("Contact"), "'", "''")	
  if len(request.form("Volume")) > 0 then
  strVolume = request.form("Volume")
  else
  strVolume = 0
  End if
  if request.form("Push") = "X" then
  strPush = "X"
  else
  strPush = ""
  end if
    if request.form("LDN") = "Y" then
  strLDN = "Y"
  else
  strLDN = ""
  end if
      if request.form("MOB") = "Y" then
  strMOB = "Y"
  else
  strMOB = ""
  end if

    if request.form("OWB") = "Y" then
  strOWB = "Y"
  else
  strOWB = ""
  end if


      	
  	
	strsql =  "Update tblTier Set Vendor = '" & strVendor & "', Generator = '" & strGenerator & "', City =  '" & strCity & "', "_
	&"  State = '" & request.form("State") & "', Grade = '" & request.form("Grade") & "', Tier = '" & request.form("Tier") & "', "_
	&"  Active = '" & request.form("Active") & "', Comments = '" & strComments & "', Test = '" & strTest & "', "_
	&" Contact = '" & strContact & "', Contact_Phone = '" & Request.form("Contact_Phone") & "', Contact_Email = '" & Request.form("Email") & "', LDN = '" & strLDN & "', "_
	&" MOB = '" & strMOB & "', OWB = '" & strOWB & "', Contract = '" & REquest.form("Contract") & "',Yard_Access = '" & request.form("Yard_Access") & "', "_
	&" Release_Submission = '" & Request.form("Release") & "', Volume = '" & strVolume & "', UOM = '" & request.form("UOM") & "', Freight = '" & request.form("Freight") & "', "_
	&" Push = '" & strPush & "'  where ID = " & strid
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
			end if ' if not deleting
Response.redirect("RF_Vendor.asp")		
end if  ' end if submit

	
%>

<style type="text/css">
.style3 {
	border: 1px solid #000000;
}
.style5 {
	border: 1px solid #C0C0C0;
	background-color: #FFFFD7;
}
.style6 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: arial;
	font-size: small;
	text-align: center;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style7 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	text-align: center;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style8 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.auto-style1 {
	background-color: #E2F3FE;
}
.auto-style2 {
	font-weight: bold;
	background-color: #E2F3FE;
}
.auto-style3 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: arial;
	font-size: small;
	text-align: center;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #E2F3FE;
}
.auto-style6 {
	text-align: center;
}
</style>
</head>

<body>
<br>
	
<form name="form1" action="RF_Vendor_Edit.asp?id=<%= strid %>" method="post" ID="Form1">
<TABLE cellSpacing=0 cellPadding=0 width=100% align = center class="style5">

 <TD align = left class="auto-style1"><font size="2" face = arial>&nbsp;</font></td>
<td align = center class="auto-style2"><font face="Arial">Modify Vendor</font></td>
<td align = right class="auto-style1"><font face="Arial"><a href="javascript:history.go(-1);">Return</a>&nbsp;</td>


</tr>
	    </table>



	<p>&nbsp;</p>
	<table id="table1" style="width: 75%" align="center" class="style3">
<tr bgcolor = #CCCCFF>
		<td class="auto-style3"  >Vendor</td>
			<td class="auto-style3"  >Generator</td>
		<td class="auto-style3" >City</td>
			<td class="auto-style3"  >State</td>
			<td class="auto-style3"  >Grade</td>
		<td class="auto-style3"  >Check to Delete</td>
	</tr>
	<tr>
	
		<td class="style7" style="height: 57px"  >
		<input type="text" name="Vendor" size="20"   tabindex="1" value='<%= MyRec("Vendor") %>' ></td>
		<td class="style7" style="height: 57px;">
		<input type="text" name="Generator" size="20"   tabindex="2" value='<%= MyRec("Generator") %>' ></td>
		<td class="style7" style="height: 57px;  ">
		<input type="text" name="City" size="20" tabindex="3" value='<%= MyRec("City") %>' ></td>
		<td style="height: 57px;   "style8" class="auto-style6">
		<input type="text" name="State" size="20"  tabindex="4" value='<%= Myrec("State") %>' style="width: 51px" ></td>
		<td style="height: 57px;   "style8" class="auto-style6">
	     <input type="text" name="Grade" size="20"   tabindex="5" value='<%= MyRec("Grade") %>' style="width: 103px"></td>
	    <td class="style7" style="height: 57px;  ">
			<input type="checkbox" name="Delete"  value="Y" <% if MyRec("OWB") = "Y" then %> <% end if %> tabindex="16"></td>
	</tr>
	<tr bgcolor = #CCCCFF>
		<td class="auto-style3" style="height: 33px"  >Tier</td>
		<td class="auto-style3" style="height: 33px"  >Test</td>
		<td class="auto-style3" colspan="3" style="height: 33px"  >Comments</td>
	
		<td class="auto-style3" style="height: 33px"  >Active</td>
	</tr>
	<tr>
		<td class="style7" style="height: 50px">
		<input type="text" name="Tier" size="20"   tabindex="6" value='<%= MyRec("Tier") %>' style="width: 32px" ></td>	
		<td class="style7" style="height: 50px"  >
		<input type="text" name="Test" size="10"  tabindex="7" value='<%= MyRec("Test") %>' maxlength="10" style="width: 51px" ></td>
		<td class="style7" colspan="3" style="height: 50px">
		<input type="text" name="Comments"  tabindex="8" value="<%= MyRec("Comments") %>" style="width: 525px"  ></td>
		<td   class="style7" style="height: 50px">
		<select name="Active" tabindex="9">
		<option <% if MyRec("Active") = "Y" then %> selected <% end if %> >Y</option>
		<option <% if MyRec("Active") = "N" then %> selected <% end if %>>N</option>
		</select>
		&nbsp;</td>	
	</tr>
	<tr bgcolor = #CCCCFF>
		<td class="auto-style3" style="height: 46px"  >Contact</td>
		<td class="auto-style3" style="height: 46px"  >Contact Phone</td>
		<td class="auto-style3" colspan="3" style="height: 46px"  >Contact Email</td>
        <td class="auto-style3" style="height: 46px"  >Yard Access</td>

	</tr>
	<tr>
		<td class="style7" style="height: 60px"  >
		<input type="text" name="Contact" size="20" style="width: 200px" tabindex="10" value='<%= MyRec("Contact") %>'></td>		
		<td class="style7" style="height: 60px"  >
		<input type="text" name="Contact_Phone"  tabindex="11" value='<%= MyRec("Contact_Phone") %>' ></td>
		<td class="style7" colspan="3" style="height: 60px" >
		<input type="text" name="Email"   tabindex="12" value='<%= MyRec("Contact_Email") %>' style="width: 514px" ></td>
			<td class="style7" style="height: 33px"  >
		<input type="text" name="Yard_Access"   tabindex="13" value='<%= MyRec("Yard_Access") %>' style="width: 114px" ></td>

    </tr>
    <tr bgcolor = #CCCCFF>
		<td class="auto-style3" style="height: 39px" >LDN</td>
		<td class="auto-style3" style="height: 39px"  >MOB</td>
		<td class="auto-style3" style="height: 39px"  >OWN</td>
		<td class="auto-style3" style="height: 39px" colspan="2"  >Contract</td>
        <td class="auto-style3" style="height: 39px"  >Do Not Push</td>
	</tr>
	<tr>
		<td class="style7" style="height: 33px"  >
		<input type="checkbox" name="LDN"  value="Y" <% if MyRec("LDN") = "Y" then %> checked <% end if %> tabindex="14"></td>
		<td class="style7" style="height: 33px" >
			<input type="checkbox" name="MOB"  value="Y" <% if MyRec("MOB") = "Y" then %> checked <% end if %> tabindex="15"></td>
		<td class="style7" style="height: 33px"  >
			<input type="checkbox" name="OWN"  value="Y" <% if MyRec("OWB") = "Y" then %> checked <% end if %> tabindex="16"></td>
		<td class="style7" style="height: 33px" colspan="2"  >
		<select name="Contract" tabindex="17">
			<option <% if MyRec("Contract") = "N" then %> selected <% end if %>>N</option>
		<option <% if MyRec("Contract") = "Y" then %> selected <% end if %> >Y</option>
	
		</select></td>
		<td class="style7" style="height: 33px"  >
			<input type="checkbox" name="Push"  value="X" <% if MyRec("Push") = "X" then %> checked <% end if %> tabindex="17"></td>
    </tr>
    <tr bgcolor = #CCCCFF>
		<td class="auto-style3" style="height: 38px"  >Release Submission</td>
		<td class="auto-style3" style="height: 38px"  >Monthly Volume</td>
		<td class="auto-style3" style="height: 38px"  >UOM</td>
		<td class="auto-style3" style="height: 38px" colspan="2"  >Freight</td>
		<td class="auto-style3" style="height: 38px"  >G/K</td>
	</tr>	
	<tr>
		<td class="style7" style="height: 52px"  >
		<input type="text" name="Release"   tabindex="18" value='<%= MyRec("Release_Submission") %>' ></td>
		<td class="style7" style="height: 52px"  >
		$
		<input type="text" name="Volume"  tabindex="19" value='<%= MyRec("Volume") %>' style="width: 68px" ></td>
		<td class="style7" style="height: 52px"  >
		<input type="text" name="UOM"   tabindex="20" value='<%= MyRec("UOM") %>' style="width: 44px" ></td>
		<td class="style7" style="height: 52px" colspan="2"  >
		<input type="text" name="Freight"   tabindex="21" value='<%= MyRec("Freight") %>' style="width: 111px" ></td>
				<td class="style7" style="height: 52px"  >
		<select name="GK" tabindex="22">
			<option <% if MyRec("Contract") = "N" then %> selected <% end if %>>N</option>
		<option <% if MyRec("Contract") = "Y" then %> selected <% end if %> >Y</option>
	
		</select></td>

	</tr>

</table>
<br>
		

<p align="center"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></p>
</form>

<!--#include file="Fiberfooter.inc"-->