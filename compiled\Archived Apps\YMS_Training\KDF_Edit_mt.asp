<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 14.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Edit KDF Inbound Load</title>
</head>
<%
    Dim strSQL, MyRec, strID, strCID
      
    Dim strTrailer, strLN, strPO, strML, strsql3, Myconn, strmaterial, strMD


       strId  = Trim(Request.QueryString("id")) 
       strCID = Request.querystring("cid")


 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
 
		Call SaveData() 

	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to delete a Receipt.</font></br>")
	MyRec.close
	end if

  
ELSE


strsql = "SELECT Trailer, Purchdoc, Item, Multi_load from tblSapOpenPO where OID = " & strid

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


    strTrailer = MyRec.fields("Trailer")
    strML = MyRec.fields("Multi_load")
    strPO = MyRec.fields("Purchdoc")
    strLN = MyRec.fields("Item")
 
    

MyRec.close

	end if

%>



<body><br>
<table width = 100%><tr><td width = 33%></td><td align = center width = 34%><b><font face="Arial" size="4">
Edit Inbound KDF Trailer</font> </b></td><td align = right width = 33%><a href="ML_KDF_Edit.asp"><b><font face = arial size = 2>RETURN</a></font></b></td></tr></table>



<form name="form1" action="KDF_Edit_MT.asp?id=<%=strid%>&cid=<%= strcid%>" method="post">
<div align="center">
<table border="1" cellspacing="0" bordercolor="#C0C0C0" width="60%" bgcolor="#FFFFFF" style="border-collapse: collapse" cellpadding="0">
<tr>
    <td bgcolor="#FFFFE6">
   <b>
   <font face="Arial" size="2">Trailer:</font></b></td>

    <td  bgcolor="#FFFFE6"><font face="Arial" size="2"><b>Multi-Item Load:&nbsp;&nbsp; </b>
	(Enter X if Multi-Item Load)</font></td>
  </tr>
  <tr>
    <td  align = right bgcolor="#FFFFFF" >
   <font face="Arial">

      <input name="Trailer" size="15" value = "<%= strTrailer%>" style="float: left"></font><b><font face="Arial" size="2">&nbsp;</font></b></td>
<td  align = left>

      <font face="Arial">

      <input type="text" name="ML" size="4" value = "<%= strML%>"></font></td></tr>

  </tr>
<tr>
    <td bgcolor="#FFFFE6">
   <b><font face="Arial" size="2">PO#</font></b></td>

    <td  bgcolor="#FFFFE6"><b><font face="Arial" size="2">Line Number</font></b></td>
  </tr>

  <tr>
    <td  align = left bgcolor="#FFFFFF" >
<font face="Arial"><input name="PO" size="15" value = "<%= strPO%>" style="float: left"></font></font></td>
<td  align = left>  <font face="Arial"><input name="LN" size="15" value = "<%= strLN%>" style="float: left"></font></td></tr>

 
 

  <tr>
    <td bgcolor="#FFFFE6" colspan="2">
	<p align="center"><Input name="Update" type="submit" Value="Submit" ></td>

  </tr>
</table>

<p>&nbsp;</div>

</form>
</body>
<%
 


 Function SaveData()
 strid = request.querystring("id")
 strcid = request.querystring("cid")
strTrailer = Request.form("Trailer")
strPO = request.form("PO")
strLN = request.form("LN")
If len(request.form("ML")) > 0 then
strML = Request.form("ML")
else
strML = ""
end if 


         strSql = "Update tblSAPOpenPO set Purchdoc = '" & strPO & "', PO_Nbr = " & strPO & ", Line_nbr = " & strLN & ", "_
         &" Item = '" & strLN & "', Trailer = '" & strTrailer & "', Multi_load =  '" & strML & "' where OID =  " & strid & ""

         
	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
				
	strsql = "Select Material, Material_desc from tblSAPAutoImport where Line_nbr = " & strLN & " and Doc_nbr = " & strPO & ""
		 	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
   	 
strmaterial = Myrec.fields("Material") 
strMD = MyRec.fields("Material_Desc")
MyRec.close

if len(strmaterial) > 0 then
' do nothing
else
strmaterial = ""
end if
if len(strMD) > 0 then
' do nothing
else
strMD = ""
end if
	
			
	strsql = "UPDATE tblSAPOpenPO SET tblSAPOpenPO.Material = '" & strMaterial & "',"_
	&"  tblSAPOpenPO.Description = '" & strMD & "'"_
	&" WHERE tblSAPOpenPO.PO_Nbr = " & strPO & " and tblSAPOpenPO.Line_Nbr = " & strLN & ""
	
	         
	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
	

	Response.redirect("EnterNFReceiptMT.asp?id=" & strID & "&p=" & strCID)


End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->