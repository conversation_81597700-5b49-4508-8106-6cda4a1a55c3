

<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>AIG Invoice Search</TITLE>
<!--#include file="classes/asp_cls_General.asp"-->


<!--#include file="classes/asp_cls_headerAIG.asp"-->

<!--#include file="classes/asp_cls_ProcedureMRP.asp"-->
<!--#include file="classes/asp_cls_DataAccessAIG.asp"-->


 <% 
    Dim objGeneral
   dim   rst
   dim intDirection
 



      set objGeneral = new ASP_CLS_General
       

  if objGeneral.IsSubmit() Then
      intDirection = cint(Request.Form("Direction"))  
              Dim objEPSSearch



      set objEPSSearch = new ASP_Cls_ProcedureMRP
     strEMill = Request.form("Mill")
     strEStatus = Request.form("Status")
   

	If strEstatus = "Active" then
set rst = objEPSSearch.ExceptionActive(strEMill)
else
	set rst = objEPSSearch.Except(strEMill, strEStatus)
end if


  
  else
    intDirection = 0
 
  end if
 




    %>



<script language="javascript">

  function Search()
  {
    
    document.forms["form1"].elements["Direction"].value = 1;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }

  function NextPage()
  {
    document.forms["form1"].elements["Direction"].value = 5;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }
  function PrevPage()
  {
    document.forms["form1"].elements["Direction"].value = 6;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }
</script>

<style type="text/css">
.style1 {
	text-align: center;
	border-width: 1px;
	background-color: #FFFFCC;
}
.style2 {
	font-size: small;
	font-weight: bold;
}
.style3 {
	border-width: 1px;
	background-color: #FFFFCC;
}
</style>
</head>

<form name="form1" action="Exception_search.asp" method="post">
 <input type="hidden" name="Direction" value="1" >
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>  
  <tr><td align = Center class="style2"  ><font face = arial>Search for Exceptions to View 
	</font></td>


	</tr></table>
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>  

  <TR><td width="10%" class="style3"><font size="2" face="arial"><a href="Exception_Add.asp">Add New</a></font></td>
   <TD class="style1"><font face="Arial"><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Site:</b></font>&nbsp;&nbsp;
     <font face="Arial">	<select size="1" name="Mill">
	<option value = "%" selected>All</option>
	<option <% If strEmill="Mobile" then %> selected=<% end if %> >Mobile</option>
	<option <% If strEmill="Loudon" then %> selected=<% end if %>>Loudon</option>
	<option <% If strEmill="Owensboro" then %> selected=<% end if %>>Owensboro</option>
	</select>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	<b>Status: </b>  &nbsp;&nbsp;<select size="1" name="Status">
	<option value = "%" selected>All</option>
		<option <% If strEStatus="Active" then %> selected=<% end if %>>Active</option>
	<option <% If strEStatus="New" then %> selected=<% end if %>>New</option>
	<option <% If strEStatus="Pending" then %> selected=<% end if %>>Pending</option>
	<option <% If strEStatus="Completed" then %> selected=<% end if %>>Completed</option>
	</select>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<input type="button" onClick="javascript:Search()" value="Search" caption="Search">
   </td>
 
       
      <TD align = center> &nbsp;</TD>
    
   </TR>
  
  </TABLE>


</form>

 
  <% if objGeneral.IsSubmit()  Then %>
   <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class="tablecolor1" border=1>
    <tr><td colspan="16" bgcolor="white"><font face="Arial" size="2"><b>Search Results</b> </font></td></tr>
   
      <tr class="tableheader">
		
			<th class="style4"><font face = "arial" font size = "1"> Nbr</th>
			<th class="style4"><font face = "arial" font size = "1"> Site<br>Status</th>
		         	<th class="style4"><font face = "arial" font size = "1"> Author</th>
			<th align="left" class="style5"><font face = "arial" font size = "1">Release #</th>
            <th align="left" class="style5"><font face = "arial" font size = "1">Description</th>
	     	<th class="style4"><font face = "arial" font size = "1"> Reported<br>Resolve By</th>
	     

         		<th class="style4"><font face = "arial" font size = "1"> Action Taken</th>
         	   <td>&nbsp;</td>

              	   
              	    <td>&nbsp;</td>
              	     </tr>
  	<% 
      Dim ii
       ii = 0
       while not rst.Eof

 if ( ii mod 2) = 0 Then %>
		
       			<tr bgcolor="#FFFFD7" >
			
 	
    <% else %>
	
       			<tr class="tableheader">
			
	
    <% end if %>
 						
 						<td align = "center" class="style4"><font size =1 font face = arial><%= rst.fields("ID") %>&nbsp;</td>
                    	<td align = "center" class="style4"><font size = 1 font face = arial><%= rst.fields("Site") %><br>
                    	<a href="Exception_complete.asp?id=<%= rst.Fields("ID") %>"><%= rst.fields("Status") %></a></td>
					   	<td align = "center" class="style4"><font size = 1 font face = arial><%= rst.fields("Author") %>&nbsp;</td>

 						<td align = "center" class="style4"><font size = 2 font face = arial><%= rst.fields("Release") %>&nbsp;</td>
                    	<td align = "left" class="style4"><font size = 2 font face = arial><%= rst.fields("Issue") %>&nbsp;</td>
                    	<td align = "center" class="style4"><font size = 1 font face = arial><%= formatdatetime(rst.fields("Date_reported"),2) %><br><%= rst.fields("Resolution_needed") %></td>
                    

                 
                    	<td align = "left" class="style4"><font size = 2 font face = arial><%= rst.fields("Resolution") %>&nbsp;</td> 
						 <td><a href="Exception_update.asp?id=<%= rst.Fields("ID") %>"><font face = "arial" font size = "1">Update</a></td>
						

  </tr>

	
	

</tr>
    <%
       ii = ii + 1
       rst.MoveNext
     Wend
    %>
   </table>


  <% end if
 


 %><!--#include file="AIGfooter.inc"-->