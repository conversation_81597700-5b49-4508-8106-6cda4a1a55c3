 

<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Load Activity TO Warehouse</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

<%  
	Dim strsQL, rstEquip

   	Dim objEquipSearch, rstTotals, strCount, strDed, strNet, strTons, rstTotalReceived, strTR, objNew, rstSpecies
  	Dim  strVendor, strBegDate, strEndDate, strSpecies, strDateReceived, strDateAdded, strAVG
 	Dim  rstVendor, strFIQ, strBAQ, rstGenerator, strGenerator, strVendorname, strGeneratorname
  	Dim objGeneral, gcount, strCountTwo, strSum, strSum1, strNetSum, strDepdate, strLocation 
   	
   	Dim intDirection
  	Dim intPageNumber
   	Dim strPageNav

   intPageNumber = 1

   set objGeneral = new ASP_CLS_General

  if objGeneral.IsSubmit() Then
    Call GetFormData()
    Select Case intDirection
      Case 10: 'goto mocinfo.asp
  
      Case Else:
        Call LoadSearchResults()
    End Select
  else
    intDirection = 0
  end if
Call GetData()
%>

<script language="javascript">
 
  function GotoMOC(MOCID)
  {
    document.forms["form1"].elements["tbl_ID"].value = MOCID;
    document.forms["form1"].elements["Direction"].value = 10;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }

  function Search()
  {
    
    document.forms["form1"].elements["Direction"].value = 1;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }

  function NextPage()
  {
    document.forms["form1"].elements["Direction"].value = 5;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }
  function PrevPage()
  {
    document.forms["form1"].elements["Direction"].value = 6;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }
</script>


<style type="text/css">
.style1 {
	font-size: x-small;
}
.style2 {
	font-family: Arial;
	font-weight: bold;
}
</style>
</head>


<form name="form1" action="To_Baldwin_DC_rpt.asp" method="post">
 <input type="hidden" name="Direction" value="1" >
 <input type="hidden" name="tbl_ID" value="" >
 <input type="hidden" name=PageNumber value="<%=intPageNumber%>" >
 
<TABLE borderColor=white cellSpacing=0 cellPadding=0  width = 100% border=1 >  
  <tr>
  <td  align = left><b><font face="Arial">Load Activity </font></b>
	<span class="style2">TO Warehouse</span></td>
	<td align = right><font face="Arial"><b><a href="Loginscreen.asp">HOME</a></b></font></td></tr>
</table>

 

	<div align="center">

 

<TABLE borderColor=#808080 cellSpacing=0 cellPadding=0 width=100% class="tablecolor1" border=1 id="table2">  

  <TR>
   
	<TD ><b><font face="Arial" size="2">Beg Date:</font></b></TD>
	<td><font face="Arial"><input name="Beg_Date" size="10" maxlength="10" value="<%=strBegDate%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633"></TD>



     <td><font size="2" face="Arial"><b>&nbsp; End Date</b></font></td>
     <td><font face="Arial"><input name="End_Date" size="10" maxlength="10" value="<%=strEndDate%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633"></td>

     <td><font size="2" face="Arial"><b>To Location</b></font></td>

     <td><select size="1" name="From_Location">
	
		<option <% if strLocation = "DC" then %>selected <% end if %>>DC</option>
		<option <% if strlocation = "MERCHANTS" then %>selected <% end if %>>MERCHANTS</option>
		</select></td>

		<TD><b><font face="Arial" size="2">Species:</font></b></TD>
    <TD>  <font face="Arial">   <select size="1" name="Species">
     <option value="">--- All ---</option>
       <%= objGeneral.OptionListAsString(rstSpecies, "Species", "Species", strSpecies) %>
     </select></td>
</TR>

	</table>
	</div>
<table width = 100% bgcolor = white><tr>

  
    <TD  align="right"><input type="button" onClick="javascript:Search()" value="Search" caption="Search"></TD>
 
</TR>


  </TABLE></form>


  <% if objGeneral.IsSubmit() Then 

%>
   <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>
    <tr><td colspan="7" bgcolor="white" ><font face="Arial" size="2"><b>Search Results</b> - Page <%=intPageNumber%></font></td>
              <td><a href="To_Baldwin_DC_Rpt_Excel.asp?b=<%= strBegDate %>&e=<%= strEndDate %>&l=<%= strlocation %>&s=<%= strSpecies %>">Export</a></td>

<% if intPageNumber = 1 then %>


 <td bgcolor = white align="center" ><font face="Arial" size="2"><b>Count<br><%= strCount%>&nbsp;</font></td>
        <td bgcolor = white  align="center" ><font face="Arial" size = 2><b>Total Net<br><%= strNetSum%>&nbsp;</b></font></b></td>
    <% else %>

    <% end if %>
    </tr>
    <tr><td colspan="16" bgcolor="white" align="right" ><span class="style1"><%=strPageNav%></span>&nbsp;</td></tr>
      <tr class="tableheader">	
      <td  align="center" >
		<p align="left" class="style1"><font face="Arial">Species</font></td>

		<td  align="center" class="style1" ><font face="Arial">Receipt #</font></td>
		<td  align="center" class="style1" ><font face="Arial">Vendor</font></td>
<td  align="center" class="style1" ><font face="Arial">Generator</font></td>
	<td  align="center" class="style1" ><font face="Arial">Trailer</font></td>
	<td  align="center" >
	<p align="left" class="style1"><font face="Arial">Date Received</font></td>
	<td  align="center" class="style1" ><font face="Arial">Date Unloaded</font></td>

	<td  align="center" class="style1" ><font face="Arial">Location</font></td>

	<td  align="center" class="style1" ><font face="Arial">Net</font></td>


	<td  align="center" class="style1" ><font  face="Arial">Other</font></td>

      
  	<%  
      Dim ii
       ii = 0
       while not rstEquip.Eof
      
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr class=tablecolor1>
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    


<td  align="left" ><font face = "arial" size = "1"><span class="style1"><%=rstEquip.fields("Species")%></span>&nbsp;</td>
<td  align="left" ><font face = "arial" size = "1"><span class="style1"><%=rstEquip.fields("CID")%></span>&nbsp;</td>
<td  align="center" ><font face = "arial" size = "1"><span class="style1"><%=rstEquip.fields("Vendor")%></span>&nbsp;</td>
<td  align="center" ><font face = "arial" size = "1"><span class="style1"><%=rstEquip.fields("Generator")%></span>&nbsp;</td>

<td  align="center" ><font face = "arial" size = "1"><span class="style1"><%=rstEquip.fields("Trailer")%></span>&nbsp;</td>

<td  align="left" ><font face = "arial" size = "1"><span class="style1"><%=rstEquip.fields("Date_received")%></span>&nbsp;</td>
<td  align="center" ><font face = "arial" size = "1"><span class="style1"><%=rstEquip.fields("Date_unloaded")%></span>&nbsp;</td>

<td  align="center" ><font face = "arial" size = "1"><span class="style1"><%=rstEquip.fields("Location")%></span>&nbsp;</td>
<% if not isnull(rstEquip.fields("Net")) then %>
<td  align="center" ><font face = "arial" size = "1"><span class="style1"><%= formatnumber(rstEquip.fields("net"),3)%></span>&nbsp;</td>
<% else %>
<td  align="center" class="style1" ><font face = "arial">&nbsp;</td>
<% end if %>



<td  align="center" ><font face = "arial" size = "1"><span class="style1"><%= rstEquip.fields("Other_Comments")%></span>&nbsp;</td>



 </tr>
    <% 
       ii = ii + 1
      
       rstEquip.MoveNext
     Wend
    %>
   </table>
<table>    <tr><td colspan="16" bgcolor="white" align="right">
	<span class="style1"><%=strPageNav%></span>&nbsp;</td></tr></table>
<% end if %><% Function GetData()

   set objNew = new ASP_Cls_Fiber
          

		set rstSpecies = objNew.FiberSpecies()



End Function
    Function GetFormData()
      intDirection = cint(Request.Form("Direction")) 
       
	

	strBegDate = Request.form("Beg_date")
	strEndDate = Request.form("End_date")
	strSpecies = Request.form("Species")
	strLocation = Request.form("From_Location")


      intPageNumber = cint(Request.Form("PageNumber"))

 
    End Function

    Function LoadSearchResults()
      Dim objEquipSearch, objTotals

      if intDirection =1 Then
         intPageNumber = 1
      end if

      if intDirection = 5 Then
         intPageNumber = intPageNumber + 1
      end if
      if intDirection = 6 Then
         intPageNumber = intPageNumber - 1
      end if


	
	
	strGenerator = ""
	strSpecies = Request.form("Species")
	strLocation = Request.form("From_Location")
      set objEquipSearch = new ASP_Cls_Fiber
    

 set rstEquip = objEquipSearch.DCSearchTo(50, intPageNumber, strBegDate, strEndDate, strSpecies, strLocation)
set rstTotals = objEquipSearch.DCSearch_totalsTo(intPageNumber, strBegDate, strEndDate,  strSpecies, strLocation)




     if  not rstTotals.Eof Then
    ' strNetSum = round(rstTotals.fields("SumofNet"),3)
     strNetSum = rsttotals.fields("SumofNet")
      strCount = rstTotals.fields("CountofCID").value

      
	else
	strCount = 0
	strNetSum = 0

      
      end if 

     if ( not rstEquip.Eof) Then
      if ( intPageNumber < rstEquip.fields("TotalPage").value ) Then
        strPageNav = "<a href=javascript:NextPage()><B>Next Page</b></a>"
      end if
      if ( intPageNumber > 1 ) Then
         strPageNav = "<a href=javascript:PrevPage()><b>Previous Page</b></a>&nbsp;&nbsp;&nbsp;&nbsp;" & strPageNav
      end if
     end if

       ' if ( not rstTotalReceived.Eof) Then
    ' strTR = rstTotalReceived.fields("countofoid").value
    ' end if
    End Function
 %><!--#include file="Fiberfooter.inc"-->