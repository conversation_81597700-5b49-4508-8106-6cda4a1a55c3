
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Modify Technician</TITLE>
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_Session.asp"-->
<!--#include file="classes/asp_cls_DataAccess.asp"-->

</head>
<style type="text/css">
.style1 {
	font-family: Arial;
}
.style3 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
.auto-style3 {
	font-family: Calibri;
	font-weight: bold;
	font-size: large;
	background-color: #FFFFFF;
}
.auto-style4 {
	font-family: <PERSON>ibri;
	font-size: large;
}
.auto-style5 {
	border: 1px solid #000000;
	border-collapse: collapse;
}
.auto-style6 {
	font-family: <PERSON>ibri;
	font-size: small;
	border: 1px solid #C0C0C0;
	background-color: #F2FBFF;
}
.auto-style7 {
	background-color: #FFFFFF;
}
.auto-style8 {
	border: 1px solid #C0C0C0;
	background-color: #F2FBFF;
	text-align: left;
}
.auto-style9 {
	margin-left: 0px;
}
.auto-style11 {
	font-weight: bold;
	border-style: solid;
	border-color: #C0C0C0;
	background-color: #F2FBFF;
	font-family: Calibri;
	font-size: small;
}
.auto-style12 {
	border-width: 0px;
}
</style>
<script language="javascript">
function openWindow(pURL)
{
	myWindow = window.open(pURL, "myLittleCalendar", 'toolbar=no,location=no,directories=no,status=no,menubar=no,scrollbars=no,resizable=no,width=170,height=270');
}
 function KeyPress() 
 {
 //alert(window.event.keyCode)
 if (window.event.keyCode == 13)
 window.event.keyCode =0;
 } 
</script> 

<%  Dim strTakingCall, strDateCalled, strTimeCalled, strDateIn, strAM

strid = request.querystring("id")

if request.querystring("t") = "d" then
strDateCalled = formatdatetime(now(),2)
strTime = formatdatetime(now(),3)
strTimeCalled = left(strTime,8)
strAM = right(strTime,2)
end if


strsql = "SELECT tblCall.* from tblCall where ID = " & strid

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    strTech = MyRec("Tech")

MyRec.close

 set objGeneral = new ASP_CLS_General


if objGeneral.IsSubmit() Then


	Call SaveData() 

End if %>

<BODY onKeyPress="KeyPress()">
<form name="form1" action="Tech_report_in.asp?id=<%= strid %>" method="post">
 
<table border="0" cellpadding="0" cellspacing="0" width = 60% style="border-collapse: collapse" bordercolor="000000" height="25" align = center >
  <tr>
    <td bordercolor="#CCCCFF" height="25" class="auto-style7" >
    <p align="center" class="auto-style4">REPORT CALL IN</td>
    <td align = center height="25" class="auto-style3"><a href="Call_In.asp">CANCEL</a></td>

    <td align = center height="25" class="auto-style7">
  <Input name="Submit" type="submit" Value="Submit" style="float: right" tabindex="5" ></td>
  </tr>
</table><br><br>
<div align="center">
<table cellpadding="0" cellspacing="0"  height="10" class="auto-style5" style="width: 40%">
    <tr>
<td height="22" align="center" class="auto-style11" >
Date/Time In Log Entered </span> </font></td>
   
<td height="22" align="center" class="auto-style11" ><%= Now() %></td> 
  </tr>
  
    <tr>
    <td height="47"  align="center" class="auto-style6" >   &nbsp;Date Tech 
	Called</td>
    	

    <td height="47" class="auto-style8" >   <font face="Arial">	
	<input type="text" name="Date_Called" size="35" value="<%= strDateCalled %>" style="width: 121px" tabindex="1"></font>&nbsp;&nbsp;
	<INPUT TYPE="image" img border="0" src="calendar.gif" width="47" height="25" VALUE="..." STYLE="font-family: MS Sans Serif,Arial; color: #885B3B; font-size: 12px;" onclick="openWindow('mlcpopup.asp?elt=Date_Called'); return false;">
    &nbsp;&nbsp;&nbsp;	<a href="Tech_report_in.asp?id=<%= strid %>&t=d">
	<img alt="" class="auto-style12" height="26" src="img6.jpg" width="46"></a>		
	</td>


  </tr>
 
    <tr>
    <td  align="center" class="auto-style6" style="height: 42px" >   Time Tech 
	Called</td>
    	

    <td class="auto-style8" style="height: 42px" >   <font face="Arial">	
		<input type="text" name="Time" size="35" value="<%= strTimeCalled %>" style="width: 121px" tabindex="2">&nbsp; 
		<select name="AM">
		<option>AM</option>
		<option <% if strAM = "PM" then %> selected <% end if %>>PM</option>
		</select></font></td>
    	

  </tr>
 
     <tr>
    <td height="47"  align="center" class="auto-style6" >   
	<span style="font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;
mso-fareast-font-family:Calibri;mso-fareast-theme-font:minor-latin;mso-ansi-language:
EN-US;mso-fareast-language:EN-US;mso-bidi-language:AR-SA">Date reporting in for</span></td>
    	

    <td height="47" class="auto-style8" >   <font face="Arial">	
		<input type="text" name="Date_In" size="35" value="<%= strDateIn %>" style="width: 121px" tabindex="3" class="auto-style9"></font>&nbsp;&nbsp;
    <INPUT TYPE="image" img border="0" src="calendar.gif" width="47" height="25" VALUE="..." STYLE="font-family: MS Sans Serif,Arial; color: #885B3B; font-size: 12px;" onclick="openWindow('mlcpopup.asp?elt=Date_In'); return false;">	
</td>
  </tr>

 
    <tr>
    <td height="47"  align="center" class="auto-style6" >   
	<span style="font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;
mso-fareast-font-family:Calibri;mso-fareast-theme-font:minor-latin;mso-ansi-language:
EN-US;mso-fareast-language:EN-US;mso-bidi-language:AR-SA">Tech reporting in</span></td>
    	

    <td height="47" class="auto-style8" >   <font face="Arial">	<%= strTech %>	
	 </font></td>
    	

  </tr>
 
    <tr>
    <td height="47"  align="center" class="auto-style6" >   Tech Taking Call</td>
    	

    <td height="47" class="auto-style8" >   <font face="Arial">	
		<input type="text" name="Taking_Call" size="35" value="<%= strTakingCall %>" style="width: 218px" tabindex="4"></font></td>
    	

  </tr>
 
  </table>
</div>



</form>
   
  

</body>
 <%

  
  Function SaveData()



strNow = now()
strTakingCall = Replace(Request.form("Taking_Call"), "'", "''")  
strDateCalled = request.form("Date_Called")
strTimeCalled = request.form("Time")

strDateIn = request.form("Date_In")
strAM = request.form("AM")

 
 
 If  len(strTakingCall) > 0 and len(strDateCalled) > 0 and len(StrTimeCalled) > 0 and len(strDateIn) > 0 then

 
   
    
    strsql = "Update tblCall set Entry_Time_in_called = '" & strNow & "', date_in_called = '" & strDateCalled & "', time_in_called = '" & strTimeCalled & "', "_
   &"  Time_in_called_AM ='" & request.form("AM") & "', Date_in = '" & strDateIn & "', Taking_call_in = '" & strTakingCall & "' where ID = " & strid
   set MyRec = new ASP_CLS_DataAccess
        MyRec.ExecuteSql strSql 
        Response.redirect("Call_In.asp")
       
         
          Response.write("<br><font face=arial size=3 color=red><b>Submitted</b></font>")

else
Response.write("<br><font face=arial size=3 color=red><b>Please enter all fields </b></font>")
  end if 
  	
         
       
  End Function
  
   %><!--#include file="footer.inc"-->