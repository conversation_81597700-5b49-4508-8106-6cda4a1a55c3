
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">
<TITLE>Daily Inventory Report</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->
<!--#include file="classes/asp_cls_sessionPolaris.asp"-->

	
	
	   <style type="text/css">
.style1 {
	font-family: Arial;
	font-size: x-small;
}
.style2 {
	font-size: x-small;
	background-color: #FFFFDD;
	text-align: center;
}
.style3 {
	background-color: #FFFFDD;
	font-size: x-small;
}
.style4 {
	border: 1px solid #F0F0F0;
}
.style7 {
	color: windowtext;
	font-size: 10.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: bottom;
	white-space: nowrap;
	border-left: .5pt solid windowtext;
	border-right: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom: .5pt solid windowtext;
	padding: 0px;
	background: #00B0F0;
}
.style9 {
	color: windowtext;
	font-size: 10.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: bottom;
	white-space: nowrap;
	border-left: .5pt solid windowtext;
	border-right: 1.0pt solid windowtext;
	border-top-style: none;
	border-top-color: inherit;
	border-top-width: medium;
	border-bottom: .5pt solid windowtext;
	padding: 0px;
	background: #92D050;
}
.style11 {
	color: windowtext;
	font-size: 10.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: bottom;
	white-space: nowrap;
	border-left: .5pt solid windowtext;
	border-right: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom: .5pt solid windowtext;
	padding: 0px;
	background: yellow;
}
.style13 {
	color: windowtext;
	font-size: 10.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: bottom;
	white-space: nowrap;
	border-left: .5pt solid windowtext;
	border-right: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom: 1.0pt solid windowtext;
	padding: 0px;
	background: red;
}
.style14 {
	border: 1px solid #F0F0F0;
	text-align: center;
	background-color: #F3F3F3;
}
.style15 {
	font-family: Arial;
}
.style16 {
	font-size: x-small;
}
.style17 {
	border: 1px solid #F0F0F0;
	text-align: center;
		font-family: Arial;
	background-color: #FFD7FF;
}
.style18 {
	border: 1px solid #000000;
}
.style21 {
	border: 1px solid #F0F0F0;
	font-family: Arial;
	font-size: x-small;
	text-align: center;
	background-color: #FFFFCC;
}
.style26 {
	color: windowtext;
	font-size: x-small;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: bottom;
	white-space: nowrap;
	border-left: .5pt solid windowtext;
	border-right: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom: .5pt solid windowtext;
	padding: 0px;
	background: yellow;
}
.style29 {
	border-left-color: #C0C0C0;
	border-left-width: 1px;
	border-right-color: #000080;
	border-right-width: 1px;
	border-top-color: #C0C0C0;
	border-top-width: 1px;
	border-bottom-color: #000080;
	border-bottom-width: 1px;
	background-color: #F0F0F0;
}
.style30 {
	border-color: #C0C0C0;
	border-width: 1px;
	text-align: center;
	font-family: Arial;
	font-size: x-small;
	background-color: #E8E8FF;
}
.style31 {
	border-width: 1px;
	background-color: #E8E8FF;
}
.style34 {
	border-color: #C0C0C0;
	border-width: 1px;
	text-align: center;
	font-family: Arial;
	font-size: x-small;
	background-color: #FAFBBF;
}
.style38 {
	border-color: #C0C0C0;
	border-width: 1px;
	text-align: center;
	font-family: Arial;
	font-size: x-small;
	background-color: #FFD7FF;
}
.style40 {
	border-left: 1px solid #C0C0C0;
	border-right: 1px solid #000080;
	border-top: 1px solid #C0C0C0;
	border-bottom: 1px solid #000080;
	background-color: #E8E8FF;
}
.style41 {
	border: 1px solid #E8E8FF;
	font-size: x-small;
}
.style44 {
	border: 1px solid #C0C0C0;
	text-align: center;
		font-family: Arial;
		font-size: x-small;
	background-color: #F3F3F3;
}
.style47 {
	border: 1px solid #F0F0F0;
	text-align: center;
	font-family: Arial;
	background-color: #F3F3F3;
}
.style48 {
	border: 1px solid #F0F0F0;
	font-family: Arial;
	font-size: x-small;
	text-align: center;
	background-color: #FFD7FF;
}
.style49 {
	border: 1px solid #F0F0F0;
	font-family: Arial;
	font-size: x-small;
	text-align: center;
	background-color: #E8E8FF;
}
.style50 {
	border: 1px solid #F0F0F0;
	background-color: #E8E8FF;
}
.style51 {
	border-width: 1px;
	background-color: #F2F8F1;
}
.style52 {
	border-left-color: #C0C0C0;
	border-left-width: 1px;
	border-right-color: #000080;
	border-right-width: 1px;
	border-top-color: #C0C0C0;
	border-top-width: 1px;
	border-bottom-color: #000080;
	border-bottom-width: 1px;
	background-color: #FFFFFF;
}
.style53 {
	border-left-color: #C0C0C0;
	border-left-width: 1px;
	border-right-color: #000080;
	border-right-width: 1px;
	border-top-color: #C0C0C0;
	border-top-width: 1px;
	border-bottom-color: #000080;
	border-bottom-width: 1px;
	background-color: #FFECFF;
}
.style54 {
	border-width: 1px;
	background-color: #FFECFF;
}
.style55 {
	border: 1px solid #FFFFFF;
	text-align: center;
	font-family: Arial;
	font-size: x-small;
	background-color: #FCFDDB;
}
.style56 {
	border-width: 1px;
	background-color: #FCFDDB;
}
.style57 {
	border-width: 1px;
	background-color: #F4F4FF;
}
.style62 {
	border-width: 1px;
	background-color: #FFECFF;
	font-size: x-small;
	text-align: center;
}
.style63 {
	border-width: 1px;
	background-color: #FCFDDB;
	text-align: center;
}
.style64 {
	border-width: 1px;
	background-color: #F4F4FF;
	text-align: center;
}
.style65 {
	text-align: center;
	font-size: x-small;
}
.style66 {
	border: 1px solid #E8E8FF;
	text-align: center;
	font-size: x-small;
}
.style67 {
	border: 1px solid #FFFFFF;
	background-color: #E8E8FF;
}
.style68 {
	border: 1px solid #FFFFFF;
	background-color: #FFFFFF;
}
.style69 {
	border-width: 1px;
	background-color: #FFFFFF;
}
.style70 {
	border: 1px solid #FFFFFF;
	font-size: x-small;
	background-color: #FAFBBF;
}
.style71 {
	border-style: solid;
	border-width: 1px;
}
</style>
</head>
<%  
	Dim strsQL, rstDaily, strBegDate, strEndDate, strcount, objPro, strCdate, strKCOP, strOther, strOCC, strMonthName, strMonthDay, strDayofweek
	Dim strdate, objDAC, strOCCRail, strOtherRail, strKCOPRail, strOCCAvg,  strKCOPAvg, strnow, MyConn, strTday7
	Dim strdate1, strYdate
	Dim strOB, MyRec2, strsql2
	Dim KCOP_one, KCOP_Two, KCOP_Three, KCOP_Four, KCOP_Five, KCOP_Six, KCOP_Seven
	DIm OCC_one, OCC_Two, OCC_Three, OCC_Four, OCC_Five, OCC_six, OCC_Seven, MyRec8, strSQL8
	
	strnow = formatdatetime(Now(),2)
	strBegdate = dateadd("d", -7, strnow)
	strEnddate = formatdatetime(now())
	
		
			strYdate = dateadd("d", -3, now())
		
	
	strsql = "DELETE  FROM tblTempInvReportoccRail WHERE Id > 0"
	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

	strsql = "DELETE  FROM tblTempInvReportOtherRail WHERE Id > 0"
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close


		strsql = "DELETE  FROM tblTempInvReportKCOPRail WHERE Id > 0"
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

	
	     
   set objDAC = new ASP_CLS_DataAccess

	        call objDAC.ExecuteSp1("asp_cls_Inv_consumption_two", array(strBegdate, strEnddate))
	        
	             set objPRO = new ASP_CLS_Fiber

        set rstDaily = objPRO.FiveDayTotals()
 
	%>

<p class="style1"><strong>KC MOBILE 
Daily RF REPORT<br><%= Now()%><br><br>
Previous Days Consumption for Reference</strong></p>
<font face="arial" size="1"><table width="50%" class="style4">
<tr><td class="style3">&nbsp;</td><td class="style3">&nbsp;</td><td class="style2"><font face="arial">KCOP</td>
	<td class="style2"><font face="arial">PMX&nbsp;</td><td class="style2"><font face="arial">RFF Total</td>
	<td class="style2"><font face="arial">OCC</td></tr>
<%  strdate = null

strKCOPAvg = 0
strOCCAvg= 0
strCount = 0
while not rstDaily.Eof
strKCOP = 0
strKCOPRail = 0
strOther = 0
strOtherRail = 0
strOCC = 0
strOCCRail = 0
strCdate = rstDaily.fields("Consumption_Date")

 	if rstdaily.fields("Weekday")  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif rstdaily.fields("Weekday")   = 2 then
 	 strDayofweek =  "Monday" 
 	elseif rstdaily.fields("Weekday")   = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif rstdaily.fields("Weekday")   = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif rstdaily.fields("Weekday")  = 5 then
 	 strDayofweek =  "Thursday"
 	elseif rstdaily.fields("Weekday")   = 6 then
 	 strDayofweek =  "Friday"
 	elseif rstdaily.fields("Weekday")  = 7 then
 	 strDayofweek =  "Saturday"
 	end if
%>
<tr><td class="style41"><font face="arial"><%= strCdate %></td>
  <% If datepart("d", strCdate) = datepart("d", strnow) then %>
  <td class="style66"><font face="arial">Consumed since midnight TODAY (Not part of average)</td>
  <% else %>
<td class="style66"><font face="arial"><%= strDayofWeek%></td>
<% end if %>
<%  if isnull(rstDaily.fields("KCOP_Rail")) then
strKCOPRail = 0
else
strKCOPRail = rstDaily.fields("KCOP_Rail") 
end if

if isnull(rstDaily.fields("KCOP")) then
strKCOP = 0
else
strKCOP = rstDaily.fields("KCOP") 
end if

if isnull(rstDaily.fields("OCC")) then
strOCC = 0
else
strOCC = rstDaily.fields("OCC") 
end if

if isnull(rstDaily.fields("OCC_Rail")) then
strOCCRail = 0
else
strOCCRail = rstDaily.fields("OCC_Rail") 
end if

if isnull(rstDaily.fields("Non_KCOP")) then
strOther = 0
else
strOther = rstDaily.fields("Non_KCOP") 
end if

if isnull(rstDaily.fields("OTher_Rail")) then
strOtherRail = 0
else
strOtherRail = rstDaily.fields("OTher_Rail") 
end if

strKCOP = strKCOP + (strKCOPRail * 3)
strOCC = strOCC + (strOCCRail * 3)
strOther = strOther + (StrOtherRail * 3) %>

<td class="style66"><font face="arial"><%= strKCOP %></td>

<td class="style66"><font face="arial"><%= strOther %></td>
<td class="style66"><font face="arial"><%= strKCOP + strOther %></td>
<td class="style66"><font face="arial"><%= strOCC %></td>
</tr>

	
	   <% 
	   If datepart("d", strCdate) <> datepart("d", strnow) then
	   strOCCAvg = strOCC + strOCCAvg
	   strKCOPAvg = strKCOP + strOther + strKCOPAvg
	   end if
	   strCount = strCount + 1
	   rstDaily.MoveNext
	   
	  
     Wend
     strcount = strcount - 1
%>

<tr><td colspan="3" class="style16">&nbsp;</td>
<td class="style16"><font face="arial">Average</td>
<% IF strcount > 0 then %>
<td class="style65"><font face="arial"><%= round(strKCOPAvg / strCount,1) %></td>
<td class="style65"><font face="arial"><%= round(strOCCAvg / strCount,1) %></td>
<% else %>
<td class="style65"><font face="arial">0</td>

<td class="style65"><font face="arial">0</td>
<% end if %>


</tr>
</table><p class="style1"><strong>

 <%   Dim strKCOP1, strPMX1, strOCC1, strKCOP2, strPMX2, strOCC2, strKCOP3, strPMX3, strOCC3, strKCOP4, strPMX4, strOCC4, strsql9
  Dim strKCOP5, strPMX5, strOCC5, strKCOP6, strPMX6, strOCC6, strKCOP7, strPMX7, strOCC7, strTday, strTD, strTM, strYear
  Dim strTD1, strTM1, strTday1
  strTday = formatdatetime(now(),2)
  strYear = datepart("yyyy", strTday)
  strTD = datepart("d", strTday)
  strTM = datepart("m", strTDay)
  strTday1 = dateadd("d", 1, strTday)
    strTD1 = datepart("d", strTday1)
  strTM1 = datepart("m", strTDay1)
  strTday2 = dateadd("d", 2, strTday)
    strTD2 = datepart("d", strTday2)
  strTM2 = datepart("m", strTDay2)
  strTday3 = dateadd("d", 3, strTday)
    strTD3 = datepart("d", strTday3)
  strTM3 = datepart("m", strTDay3)
  
    strTday4 = dateadd("d", 4, strTday)
    strTD4 = datepart("d", strTday4)
  strTM4 = datepart("m", strTDay4)


  strTday5 = dateadd("d", 5, strTday)
    strTD5 = datepart("d", strTday5)
  strTM5 = datepart("m", strTDay5)


  strTday6 = dateadd("d", 6, strTday)
    strTD6 = datepart("d", strTday6)
  strTM6 = datepart("m", strTDay6)
  
      strsql = "Select Total_OCC from tblTempYardTotals where INV_Date = '" & strTday6 & "'"
     Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
if not MyRec.eof then
' do nothing
else

  	
  	strsql8 = "Select Total_OCC, Total_RF from tblTempYardTotals where ID = 16 "
  	    Set MyRec8 = Server.CreateObject("ADODB.Recordset")
   MyRec8.Open strSQL8, Session("ConnectionString") 
   RFDefault = MyRec8.fields("Total_RF")
   OCCDefault = MyRec8.fields("Total_OCC")
   MyRec8.close

  	
	strsql9 =  "INSERT INTO tblTempYardTotals ( INV_Date, Total_RF, total_OCC) "_
	&" SELECT '" & strTday6 & "', " & RFDefault & ", " & OCCDefault & " "
	
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL9
			MyConn.Close

  
end if
MyRec.close


  
  strTday7 = dateadd("d", 7, strTday )
  


  
  strsql = "Select Total_OCC from tblTempYardTotals where INV_Date = '" & strTday7 & "'"
     Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
if not MyRec.eof then
' do nothing
else

  	
  	strsql8 = "Select Total_OCC, Total_RF from tblTempYardTotals where ID = 16 "
  	    Set MyRec8 = Server.CreateObject("ADODB.Recordset")
   MyRec8.Open strSQL8, Session("ConnectionString") 
   RFDefault = MyRec8.fields("Total_RF")
   OCCDefault = MyRec8.fields("Total_OCC")
   MyRec8.close

  	
	strsql9 =  "INSERT INTO tblTempYardTotals ( INV_Date, Total_RF, total_OCC) "_
	&" SELECT '" & strTday7 & "', " & RFDefault & ", " & OCCDefault & " "
	
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL9
			MyConn.Close

  
end if
MyRec.close
   
     strTday7 = dateadd("d", 8, strTday )
  

  
  strsql = "Select Total_OCC from tblTempYardTotals where INV_Date = '" & strTday7 & "'"
     Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
if not MyRec.eof then
' do nothing
else

  	
  	strsql8 = "Select Total_OCC, Total_RF from tblTempYardTotals where ID = 16 "
  	    Set MyRec8 = Server.CreateObject("ADODB.Recordset")
   MyRec8.Open strSQL8, Session("ConnectionString") 
   RFDefault = MyRec8.fields("Total_RF")
   OCCDefault = MyRec8.fields("Total_OCC")
   MyRec8.close

  	
	strsql9 =  "INSERT INTO tblTempYardTotals ( INV_Date, Total_RF, total_OCC) "_
	&" SELECT '" & strTday7 & "', " & RFDefault & ", " & OCCDefault & " "
	
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL9
			MyConn.Close

  
end if
MyRec.close
   
   
     strTday7 = dateadd("d", 9, strTday )
  

  
  strsql = "Select Total_OCC from tblTempYardTotals where INV_Date = '" & strTday7 & "'"
     Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
if not MyRec.eof then
' do nothing
else

  	
  	strsql8 = "Select Total_OCC, Total_RF from tblTempYardTotals where ID = 16 "
  	    Set MyRec8 = Server.CreateObject("ADODB.Recordset")
   MyRec8.Open strSQL8, Session("ConnectionString") 
   RFDefault = MyRec8.fields("Total_RF")
   OCCDefault = MyRec8.fields("Total_OCC")
   MyRec8.close

  	
	strsql9 =  "INSERT INTO tblTempYardTotals ( INV_Date, Total_RF, total_OCC) "_
	&" SELECT '" & strTday7 & "', " & RFDefault & ", " & OCCDefault & " "
	
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL9
			MyConn.Close

  
end if
MyRec.close
   
strOCC3 = 0
strKCOP3 = 0
strOCC4 = 0
strKCOP4 = 0
strOCC5 = 0
strKCOP5 = 0
strOCC6 = 0
strKCOP6 = 0
strOCC7 = 0
strKCOP7 = 0
   
        strsql = "SELECT  count(CID) as Countoford_id, left(release,1) as Species, Date_to as Delivery_date "_
  			&" FROM tblInbound "_
			&" WHERE Destination_City ='MOBILE' and Release <> '0'"_
				
			&"GROUP BY Date_to, left(Release,1) "_
			
			&" HAVING '" & strdate & "' <= Date_to "_
			&" ORDER BY Date_To, left(Release,1)"



   		Set MyRec = Server.CreateObject("ADODB.Recordset")
   		 MyRec.Open strSQL, Session("ConnectionString") 


While not MyRec.eof

If strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2 and strYear = datepart("yyyy", MyRec.fields("Delivery_Date"))and (MyRec.fields("Species") = "P" or MyRec.fields("Species") = "p") then   
strPMX3 = MyRec.fields("countoford_id")
elseif strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and strYear = datepart("yyyy", MyRec.fields("Delivery_Date"))and (MyRec.fields("Species") = "C"  or MyRec.fields("Species") = "c" ) then 
strOCC3 = strOCC3 + MyRec.fields("countoford_id")
elseif strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and strYear = datepart("yyyy", MyRec.fields("Delivery_Date"))and (MyRec.fields("Species") = "M"  or MyRec.fields("Species") = "m" ) then 
strOCC3 = strOCC3 + MyRec.fields("countoford_id")
elseif strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and strYear = datepart("yyyy", MyRec.fields("Delivery_Date"))and (MyRec.fields("Species") = "K"  or MyRec.fields("Species") = "k") then

strKCOP3 =  strKCOP3 + MyRec.fields("countoford_id") + strKCOP3
elseif strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and strYear = datepart("yyyy", MyRec.fields("Delivery_Date"))and (MyRec.fields("Species") = "F"  or MyRec.fields("Species") = "f") then

strKCOP3 =  strKCOP3 + MyRec.fields("countoford_id") + strKCOP3



elseIf strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3 and strYear = datepart("yyyy", MyRec.fields("Delivery_Date"))and  (MyRec.fields("Species") = "P" or MyRec.fields("Species") = "p") then     
strPMX4 = MyRec.fields("countoford_id")
elseif strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3  and strYear = datepart("yyyy", MyRec.fields("Delivery_Date"))and (MyRec.fields("Species") = "C"  or MyRec.fields("Species") = "c" ) then 
strOCC4 = strOCC4 + MyRec.fields("countoford_id")
elseif strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3  and strYear = datepart("yyyy", MyRec.fields("Delivery_Date"))and (MyRec.fields("Species") = "M"  or MyRec.fields("Species") = "m" ) then 
strOCC4 = strOCC4 + MyRec.fields("countoford_id")
elseif strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3  and strYear = datepart("yyyy", MyRec.fields("Delivery_Date"))and  (MyRec.fields("Species") = "K"  or MyRec.fields("Species") = "k") then

strKCOP4 =  strKCOP4 + MyRec.fields("countoford_id") + strKCOP4
elseif strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3  and strYear = datepart("yyyy", MyRec.fields("Delivery_Date"))and  (MyRec.fields("Species") = "F"  or MyRec.fields("Species") = "f") then

strKCOP4 =  strKCOP4 + MyRec.fields("countoford_id") + strKCOP4



elseIf strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4 and strYear = datepart("yyyy", MyRec.fields("Delivery_Date"))and  (MyRec.fields("Species") = "P" or MyRec.fields("Species") = "p") then       
strPMX5 = MyRec.fields("countoford_id")
elseif strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4  and strYear = datepart("yyyy", MyRec.fields("Delivery_Date"))and  (MyRec.fields("Species") = "C"  or MyRec.fields("Species") = "c" ) then 
 strOCC5 = strOCC5 +  MyRec.fields("countoford_id")
elseif strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4  and strYear = datepart("yyyy", MyRec.fields("Delivery_Date"))and  (MyRec.fields("Species") = "M"  or MyRec.fields("Species") = "m" ) then 
 strOCC5 = strOCC5 + MyRec.fields("countoford_id")
elseif strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4  and strYear = datepart("yyyy", MyRec.fields("Delivery_Date"))and (MyRec.fields("Species") = "K"  or MyRec.fields("Species") = "k") then

strKCOP5=  strKCOP5 + MyRec.fields("countoford_id") + strKCOP5
elseif strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4  and strYear = datepart("yyyy", MyRec.fields("Delivery_Date"))and (MyRec.fields("Species") = "F"  or MyRec.fields("Species") = "f") then

strKCOP5=  strKCOP5 + MyRec.fields("countoford_id") + strKCOP5


elseIf strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5 and strYear = datepart("yyyy", MyRec.fields("Delivery_Date"))and  (MyRec.fields("Species") = "P" or MyRec.fields("Species") = "p") then        
strPMX6 = MyRec.fields("countoford_id")
elseif strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5  and strYear = datepart("yyyy", MyRec.fields("Delivery_Date"))and  (MyRec.fields("Species") = "C" or MyRec.fields("Species") = "c" ) then 
strOCC6 = strOCC6 +  MyRec.fields("countoford_id")
elseif strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5  and strYear = datepart("yyyy", MyRec.fields("Delivery_Date"))and  (MyRec.fields("Species") = "M" or MyRec.fields("Species") = "m" ) then 
strOCC6 = strOCC6 + MyRec.fields("countoford_id")
elseif strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5  and strYear = datepart("yyyy", MyRec.fields("Delivery_Date"))and  (MyRec.fields("Species") = "K"  or MyRec.fields("Species") = "k") then
strKCOP6=  strKCOP6 + MyRec.fields("countoford_id") + strKCOP6
elseif strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5  and strYear = datepart("yyyy", MyRec.fields("Delivery_Date"))and  (MyRec.fields("Species") = "F"  or MyRec.fields("Species") = "f") then
strKCOP6=  strKCOP6 + MyRec.fields("countoford_id") + strKCOP6



elseIf strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6 and strYear = datepart("yyyy", MyRec.fields("Delivery_Date"))and  (MyRec.fields("Species") = "P" or MyRec.fields("Species") = "p") then          
strPMX7 = MyRec.fields("countoford_id")
elseif strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6  and strYear = datepart("yyyy", MyRec.fields("Delivery_Date"))and  (MyRec.fields("Species") = "C" or MyRec.fields("Species") = "c" ) then  
strOCC7 = strOCC7 +  MyRec.fields("countoford_id")
elseif strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6  and strYear = datepart("yyyy", MyRec.fields("Delivery_Date"))and  (MyRec.fields("Species") = "M" or MyRec.fields("Species") = "m" ) then  
strOCC7 = strOCC7 + MyRec.fields("countoford_id")
elseif strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6  and strYear = datepart("yyyy", MyRec.fields("Delivery_Date"))and  (MyRec.fields("Species") = "K"  or MyRec.fields("Species") = "k") then

strKCOP7=  trKCOP7 + MyRec.fields("countoford_id") + strKCOP7
elseif strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6  and strYear = datepart("yyyy", MyRec.fields("Delivery_Date"))and  (MyRec.fields("Species") = "F"  or MyRec.fields("Species") = "f") then

strKCOP7=  strKCOP7 + MyRec.fields("countoford_id") + strKCOP7


end if
   MyRec.MoveNext
     Wend
     MyRec.close

Dim strOWBCount, strOWBCount2
strOWBCount = 0
strOWBCount2 = 0

			strsql = "Select [Date_Time_Out], [Outbound_BOL] from [Master_Table_Backup] where Destination = 'Mobile, Al' and "_
			&" [Date_Time_out] > '" & strYdate & "'"
			
      	Set MyRec = Server.CreateObject("ADODB.Recordset")
  		MyRec.Open strSQL, Session("ConnectionOWB") 
  		while not MyRec.eof 
  	
		
		strOP = MyRec.fields("Outbound_BOL")
		strsql2 = "Select [Date_received] from tblCars where [Release_nbr] = '" & strOP & "'"
 		Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    	MyRec2.Open strSQL2, Session("ConnectionString")
   	 
   	 	if not MyRec2.eof then 
   	 	strOWBCount = strOWBCount
  		strOWBCount1 =strOWBCount1
		else
   	 	If datepart("h", MyRec.fields("date_time_out")) > 11 and datepart("d", Myrec.fields("Date_time_out")) = datepart("d", Now()) then 
			strOWBCount1 = strOWBcount1 + 1 %>
 	
   	 	<% elseif datepart("h", MyRec.fields("date_time_out")) < 12 and datepart("d", Myrec.fields("Date_time_out")) = datepart("d", Now()) or datepart("d", Myrec.fields("Date_time_out")) < datepart("d", Now()) then
   	 	strOWBCount = strOWBCount + 1 %>
   	 	
 
   	 	<% end if
  	end if
		MyRec2.close
 
       MyRec.MoveNext
    	 Wend
    	 MyRec.close


 Dim gKCOP, gOCC, gPMX, strRailCars
gKCOP = 0
gOCC = 0
gPMX = 0
strsql = "SELECT tblCars.*,  [date_received] AS Sort_Date FROM tblCars WHERE Location = 'YARD' and Rejected is null"_
&"  ORDER BY tblCars.Species, Sort_Date "   
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    while not MyRec.eof
    If MyRec.fields("Species") = "KCOP" or MyRec.fields("Species") = "OF" then
    gKCOP = gKCOP + 1
    elseif MyRec.fields("Species") = "PMX" or MyRec.fields("Species") = "SBS" or MyRec.fields("Species") = "IGS" then
    gPMX = gPMX + 1
    elseif MyRec.fields("Species") = "OCC"  or MyRec.fields("Species") = "MXP" Then
    gOCC = gOCC + 1
    end if
    MyRec.movenext
    wend
    MyRec.close
    
    StrRailCars = 0
    
 strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE (tblCars.Location='YARD' AND tblCars.Carrier='RAIL' AND (tblCars.Species='KCOP' or tblCars.Species='OF'))"
   Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then
    strRailCars = MyRec.fields("CountofCID")
gKCOP = gKCOP + (MyRec.Fields("CountofCID") * 2)
end if
    Myrec.close
      
    
  strDay2 = dateadd("d", -3, Now())
  strDay0 = dateadd("d", 1, formatdatetime(Now(),2))
  
  Dim strTomorrow
  strTomorrow = dateadd("d", 1, strDay0)
  
   	Dim strOB2, countKOP, countPMX, countOCC, countKCOP2, countPMX2, countOCC2
 		countKCOP = 0
 		countPMX = 0
 		countOCC = 0
 		countKCOP2 = 0
 		countPMX2 = 0
 		countOCC2 = 0

  	     strsql = "SELECT  left(release,1) as Species, Release, Date_to as Delivery_date "_
  			&" FROM tblInbound "_
			&" WHERE Destination_City ='MOBILE' and Release <> '0'"_	
							&" and Date_to > '" & strDay2 & "' "_
						&" and Date_to <= '" & strDay0 & "' "_
			&" ORDER BY Date_to" 	

 	
   		Set MyRec = Server.CreateObject("ADODB.Recordset")
  		 MyRec.Open strSQL, Session("ConnectionString") 
		While not MyRec.eof 

 		
		strOB2 = MyRec.fields("Release")
		
		strsql2 = "Select [Date_received] from tblCars where [Release_nbr] = '" & strOB2 & "'"
 		Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    	MyRec2.Open strSQL2, Session("ConnectionString")
   	 	if not MyRec2.eof then  
  		else
  			if left(MyRec.fields("Release"), 1)  = "p"  or left(MyRec.fields("Release"), 1)  = "P" then
				countPMX = countPMX + 1
				elseif left(MyRec.fields("Release"), 1)  = "c" or left(MyRec.fields("Release"), 1)  = "C" or left(MyRec.fields("Release"), 1)  = "M" or left(MyRec.fields("Release"), 1)  = "m" then
				countOCC = countOCC + 1
					elseif left(MyRec.fields("Release"), 1)  = "k" or left(MyRec.fields("Release"), 1)  = "K" or left(MyRec.fields("Release"), 1)  = "F" or left(MyRec.fields("Release"), 1)  = "f" then
				countKCOP = countKCOP + 1
				end if
				 end if
		MyRec2.close
 
      	 MyRec.MoveNext
    	 Wend
    	 MyRec.close

       strsql = "SELECT  left(release,1) as Species, Release, Date_to as Delivery_date "_
  			&" FROM tblInbound "_
			&" WHERE Destination_City ='MOBILE' and Release <> '0'"_	
							&" and Date_to > '" & strDay0 & "' "_
						&" and Date_to <= '" & strTomorrow & "' "_
			&" ORDER BY Date_to, left(release,1)" 	


   		Set MyRec = Server.CreateObject("ADODB.Recordset")
  	 MyRec.Open strSQL, Session("ConnectionString") 
		While not MyRec.eof 

 		
		strOB2 = MyRec.fields("Release")
		strsql2 = "Select [Date_received] from tblCars where [Release_nbr] = '" & strOB2 & "'"
 		Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    	MyRec2.Open strSQL2, Session("ConnectionString")
   	 	if not MyRec2.eof then  
  		else
  			if left(MyRec.fields("Release"), 1)  = "p"  or left(MyRec.fields("Release"), 1)  = "P" then
				countPMX2 = countPMX2 + 1
				elseif left(MyRec.fields("Release"), 1)  = "c" or left(MyRec.fields("Release"), 1)  = "C" or left(MyRec.fields("Release"), 1)  = "M" or left(MyRec.fields("Release"), 1)  = "m" then
				countOCC2 = countOCC2 + 1
			elseif left(MyRec.fields("Release"), 1)  = "k" or left(MyRec.fields("Release"), 1)  = "K" or left(MyRec.fields("Release"), 1)  = "F" or left(MyRec.fields("Release"), 1)  = "f" then
								
				countKCOP2 = countKCOP2 + 1
				end if
		end if
		MyRec2.close
 
      	 MyRec.MoveNext
    	 Wend
    	 MyRec.close

    
    strKCOP1 = countKCOP + strOWBCount  
    strPMX1 = countPMX
    strOCC1 = CountOCC
    
    strKCOP2 = countKCOP2 + strOWBCount1
    strPMX2 = countPMX2
    strOCC2 = countOCC2

%>

ICR (Inventory, Consumption, Receipts)&nbsp;&nbsp;&nbsp; Note:&nbsp; KCOP Yard 
total includes&nbsp;<%= strRailCars %> Rail Cars which are each counted as 3 trailer loads.</strong></p>


<table style="width: 80%" class="style18">
	<tr>
		<td colspan="2" style="height: 40px" class="style40"></td>
		<td colspan="3" class="style30" style="height: 40px"><strong>Yard INV</strong></td>
		<td colspan="3" class="style34" style="height: 40px"><strong>Inbounds</strong></td>
		<td style="height: 40px" colspan="2" class="style38"><strong>Consumption</strong></td>
		<td colspan="5" class="style44" style="height: 40px"><strong>Inventory 
		EOD</strong></td>
	</tr>
	<tr>
		<td class="style29" style="height: 5px" colspan="5"></td>
		<td colspan="3" class="style55" style="height: 5px">&nbsp;</td>
		<td class="style53"></td>
		<td class="style53"></td>
		<td class="style52" style="height: 5px" colspan="5"></td>
		<tr>
		<td class="style50" colspan="2">&nbsp;</td>
		<td class="style49"><strong>KCOP</strong>&nbsp;</td>
		<td class="style49" style="width: 6px"><strong>PMX</strong></td>
		<td  class="style49"><strong>OCC</strong></td>
		<td class="style21" ><strong>KCOP</strong></td>
		<td class="style21"><strong>PMX</strong></td>
		<td class="style21" ><strong>OCC</strong></td>
		<td  class="style48"><strong><span class="style16">
		RFF Total</span></strong><span class="style16">&nbsp;</span></td>
		<td class="style17" ><strong><span class="style16">
		OCC</span></strong><span class="style16">&nbsp;</span></td>
		<td class="style47"><strong><span class="style1">RFF Total&nbsp;</span></strong><span class="style15"><strong>&nbsp;</strong></span></td>
		<td class="style14"><strong><span class="style1">
		OCC</span></strong><span class="style15"><strong>&nbsp;<font face="arial" size="1"><span class="style1">Total</span></strong></span></td>
		<td class="style14" colspan="3">
<font face="arial" size="1">
	
<font face="arial"><strong><span class="style16">Legend</span></strong></font>&nbsp;</td>
	

	</tr>
	<tr> 
		<td style="height: 22px" class="style67"><font face="arial" size="2"><%= strNow %></td>
		<% strDate1 = datepart("w", strNow)
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

	
		<td  class="style31" class="style42"><font face="arial" size="2"><%= strDayofWeek %></td>
		<td style="height: 22px" class="style64"><font face="arial" size="2"><%= gKCOP %></td>
		<td  class="style64" style="width: 6px"><font face="arial" size="2"><%= gPMX %></td>
		<td  class="style64"><font face="arial" size="2"><%= gOCC %></td>
		
		<td   class="style63"><font face="arial" size="2"><%= strKCOP1 %></td>
	
		<td   class="style63"><font face="arial" size="2"><%= strPMX1 %></td>
		<td   class="style63"><font face="arial" size="2"><%= strOCC1 %></td>
		
		
		<%  strsql = "Select Total_OCC, total_RF from tblTempYardTotals where INV_Date = '" & strnow & "'"
		
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
KCOP_One = MyRec.fields("Total_RF")
OCC_One = MyRec.fields("Total_OCC")
else
KCOP_One = 0
OCC_One = 0
end if
MyRec.close %>
		
		<td class="style62"><%= KCOP_One %></td>
		
		
		<td style="height: 22px" class="style62"><%= OCC_one %></td><td style="height: 22px" 

		<% if (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_one ) > 48 then %>
		
		class="style7">
				<% elseif (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One)  < 49 and (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One ) > 26 then %>
		
		class="style9">
	<% elseif (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One )  <27 and (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One ) > 0 then %>
	class="style11">

	<% else %>
		class="style13">

		<% end if %>
<%= gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One  %></td><td style="height: 22px" 
<% if (gocc + strOcc1 - OCC_one ) > 24 then %>
	class="style7">
<% elseif  (gocc + strOcc1 - OCC_one  ) < 25 and (gocc + strOcc1 - OCC_one) > 15 then %>
class="style9">
<% elseif  (gocc + strOcc1 - OCC_one) < 16 and (gocc + strOcc1 - OCC_one) > 0 then %>
	class="style11">

	<% else %>
		class="style13">

		<% end if %>
<%= gocc + strOcc1 - OCC_one%></td>
		
		
	
				<td height="17" class="style7">	RFF &gt; 49</td>
		
				<td height="17"  class="style7">OCC &gt; 25</td>
	

	</tr>
	<tr>
		<td style="height: 23px" class="style31"><font face="arial" size="2"><%= dateadd("d", 1, strnow)%></td>
				<% strDate1 = datepart("w", dateadd("d", 1, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

		<td  class="style31"><font face="arial" size="2"><%= strDayofWeek %></td>
		<td style="height: 23px" class="style64"></td>
		<td style="height: 23px; width: 6px;" class="style57"></td>
		<td class="style57"></td>
		
	
		<td   class="style63"><font face="arial" size="2"><%= strKCOP2 %></td>
	
		<td   class="style63"><font face="arial" size="2"><%= strPMX2 %></td>
		<td   class="style63"><font face="arial" size="2"><%= strOCC2 %></td>
		
				<%  strSelectdate = dateadd("d", 1, strnow)
				strsql = "Select Total_OCC, total_RF from tblTempYardTotals where INV_Date = '" & strSelectdate & "'"
		
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
KCOP_two = MyRec.fields("Total_RF")
OCC_two = MyRec.fields("Total_OCC")
else
KCOP_two = 0
OCC_two = 0
end if
MyRec.close %>

		<td  class="style62"><%= KCOP_Two %></td>
		<td  class="style62"><%= OCC_Two %></td>
		<td style="height: 22px" 
		<% if (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_one  + strKCOP2 + strPMX2 - KCOP_two )  > 48 then %>
		
		class="style7">
				<% elseif (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One  + strKCOP2 + strPMX2 - KCOP_Two )  < 49 and (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One  + strKCOP2 + strPMX2 - KCOP_two ) > 26 then %>
		
		class="style9">
	<% elseif (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_one  + strKCOP2 + strPMX2 - KCOP_Two )  <27 and (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_one  + strKCOP2 + strPMX2 - KCOP_two ) > 0 then %>
	class="style11">

	<% else %>
		class="style13">

		<% end if %><%= gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One + strKCOP2 + strPMX2 - KCOP_Two  %></td>
<td style="height: 22px" 
<% if (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two  ) > 24 then %>
	class="style7">
<% elseif  (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two  ) < 25 and (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two  ) > 15 then %>
class="style9">
<% elseif  (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two   ) < 16 and (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two ) > 0 then %>
	class="style11">

	<% else %>
		class="style13">

		<% end if %>
<%= gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two %></td>
	
	
				<td height="17" class="style9">	RFF 27-48</td>
	
	
		
					<td height="17"  class="style9">OCC 16-24</td>
	
	</tr>
	<tr>
		<td class="style31" style="height: 17px"><font face="arial" size="2"><%= dateadd("d", 2, strnow)%>&nbsp;</td>
						<% strDate1 = datepart("w", dateadd("d", 2, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

		<td class="style31" style="height: 17px"><font face="arial" size="2"><%= strDayofWeek %>&nbsp;</td>
		<td class="style64" style="height: 17px"></td>
		<td class="style57" style="height: 17px; width: 6px;"></td>
		<td  class="style57" style="height: 17px"></td>
			<td   class="style63"><font face="arial" size="2"><%= strKCOP3 %></td>
	
		<td   class="style63"><font face="arial" size="2"><%= strPMX3 %></td>
		<td   class="style63"><font face="arial" size="2"><%= strOCC3 %></td>
					<%  strSelectdate = dateadd("d", 2, strnow)
				strsql = "Select Total_OCC, total_RF from tblTempYardTotals where INV_Date = '" & strSelectdate & "'"
		
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
KCOP_three = MyRec.fields("Total_RF")
OCC_three = MyRec.fields("Total_OCC")
else
KCOP_three = 0
OCC_three = 0
end if
MyRec.close %>

		<td  class="style62"><%= KCOP_three %></td>
		<td  class="style62"><%= OCC_three %></td>
			<td style="height: 22px" 

		<% if (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_one  + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three ) > 48 then %>
		
		class="style7">
				<% elseif (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_one  + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three )  < 49 and (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_one  + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three ) > 26 then %>
		
		class="style9">
	<% elseif (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_one  + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three )  <27 and (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_one  + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three ) > 0 then %>
	class="style11">

	<% else %>
		class="style13">

		<% end if %>
<%= gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_one  + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three %></td>
<td style="height: 22px" 
<% if (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two + strOCC3 - OCC_three) > 24 then %>
	class="style7">
<% elseif  (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two + strOCC3 - OCC_three) < 25 and (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two + strOCC3 - OCC_three) > 15 then %>
class="style9">
<% elseif  (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two + strOCC3 - OCC_three ) < 16 and (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two + strOCC3 - OCC_three) > 0 then %>
	class="style11">

	<% else %>
		class="style13">

		<% end if %>
<%= gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two + strOCC3 - OCC_three %></td>
		<td   class="style26" style="height: 17px"><font face="arial">RFF 1-27</td>
				<td class="style11" style="height: 17px">OCC 1-15</td>

	</tr>
	<tr>
		<td class="style31" style="height: 22px"><font face="arial" size="2"><%= dateadd("d", 3, strnow)%>&nbsp;</td>
								<% strDate1 = datepart("w", dateadd("d", 3, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

		<td class="style31" style="height: 22px"><font face="arial" size="2"><%= strDayofWeek %>&nbsp;</td>
		<td class="style64" style="height: 22px"></td>
		<td class="style64" style="height: 22px; width: 6px;"></td>
		<td  class="style64" style="height: 22px"></td>
		<td   class="style63" style="height: 22px"><font face="arial" size="2"><%= strKCOP4 %></td>
	
		<td   class="style63" style="height: 22px"><font face="arial" size="2"><%= strPMX4 %></td>
		<td   class="style63" style="height: 22px"><font face="arial" size="2"><%= strOCC4 %></td>
						<%  strSelectdate = dateadd("d", 3, strnow)
				strsql = "Select Total_OCC, total_RF from tblTempYardTotals where INV_Date = '" & strSelectdate & "'"
		
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
KCOP_four= MyRec.fields("Total_RF")
OCC_four = MyRec.fields("Total_OCC")
else
KCOP_four = 0
OCC_four = 0
end if
MyRec.close %>

		<td  class="style62"><%= KCOP_four %></td>
		<td  class="style62"><%= OCC_four %></td>
				<td style="height: 22px" 

		<% if (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three + strKCOP4 + strPMX4 - KCOP_Four ) > 48 then %>
		
		class="style7">
				<% elseif (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three + strKCOP4 + strPMX4 - KCOP_Four )  < 49 and (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three + strKCOP4 + strPMX4 - KCOP_Four ) > 26 then %>
		
		class="style9">
	<% elseif (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three + strKCOP4 + strPMX4 - KCOP_Four )  <27 and (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three + strKCOP4 + strPMX4 - KCOP_Four ) > 0 then %>
	class="style11">

	<% else %>
		class="style13">

		<% end if %>

					<%= gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three + strKCOP4 + strPMX4 - KCOP_Four  %></td>
<td style="height: 22px" 
<% if (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two  + strOCC3 - OCC_Three + strOCC4 - OCC_Four ) > 24 then %>
	class="style7">
<% elseif  (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two  + strOCC3 - OCC_Three + strOCC4 - OCC_Four ) < 25 and (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two  + strOCC3 - OCC_Three + strOCC4 - OCC_Four) > 15 then %>
class="style9">
<% elseif  (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two  + strOCC3 - OCC_Three + strOCC4 - OCC_Four ) < 16 and (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two  + strOCC3 - OCC_Three + strOCC4 - OCC_Four) > 0 then %>
	class="style11">

	<% else %>
		class="style13">

		<% end if %>
<%= gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two  + strOCC3 - OCC_Three + strOCC4 - OCC_Four %></td>
		<td   class="style13" style="height: 22px">				RFF &lt;0</td>

		<td  class="style13" style="height: 22px">				OCC &lt;0</td>

	</tr>
	<tr>
		<td style="height: 19px" class="style31"><font face="arial" size="2"><%= dateadd("d", 4, strnow)%>&nbsp;</td>
								<% strDate1 = datepart("w", dateadd("d", 4, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

		<td class="style31" style="height: 19px"><font face="arial" size="2"><%= strDayofWeek %></td>
		<td style="height: 19px" class="style64"></td>
		<td style="height: 19px; width: 6px;" class="style57"></td>
		<td  class="style57" style="height: 19px"></td>
			<td   class="style63" style="height: 19px"><font face="arial" size="2"><%= strKCOP5 %></td>
	
		<td   class="style63" style="height: 19px"><font face="arial" size="2"><%= strPMX5 %></td>
		<td   class="style63" style="height: 19px"><font face="arial" size="2"><%= strOCC5 %></td>
							<%  strSelectdate = dateadd("d", 4, strnow)
				strsql = "Select Total_OCC, total_RF from tblTempYardTotals where INV_Date = '" & strSelectdate & "'"
		
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
KCOP_five = MyRec.fields("Total_RF")
OCC_five = MyRec.fields("Total_OCC")
else
KCOP_five = 0
OCC_five = 0
end if
MyRec.close %>

		<td  class="style62" style="height: 19px"><%= KCOP_five %></td>
		<td  class="style62" style="height: 19px"><%= OCC_five %></td>
<td style="height: 19px" 

		<% if (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three + strKCOP4 + strPMX4 - KCOP_Four  + strKCOP5 + strPMX5 - KCOP_Five)  > 48 then %>
		
		class="style7">
				<% elseif (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three + strKCOP4 + strPMX4 - KCOP_Four  + strKCOP5 + strPMX5 - KCOP_Five )  < 49 and (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three + strKCOP4 + strPMX4 - KCOP_Four  + strKCOP5 + strPMX5 - KCOP_Five ) > 26 then %>
		
		class="style9">
	<% elseif (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three + strKCOP4 + strPMX4 - KCOP_Four  + strKCOP5 + strPMX5 - KCOP_Five )  <27 and (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three + strKCOP4 + strPMX4 - KCOP_Four  + strKCOP5 + strPMX5 - KCOP_Five ) > 0 then %>
	class="style11">

	<% else %>
		class="style13">

		<% end if %>
					<%= gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three + strKCOP4 + strPMX4 - KCOP_Four  + strKCOP5 + strPMX5 - KCOP_Five  %></td>
<td style="height: 19px" 
<% if (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two  + strOCC3 - OCC_Three + strOCC4 - OCC_Four + strOCC5 - OCC_Five  ) > 24 then %>
	class="style7">
<% elseif  (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two + strOCC3 - OCC_Three + strOCC4 - OCC_Four + strOCC5 - OCC_Five ) < 25 and (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two + strOCC3 - OCC_Three + strOCC4 - OCC_Four + strOCC5 - OCC_Five ) > 15 then %>
class="style9">
<% elseif  (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two + strOCC3 - OCC_Three + strOCC4 - OCC_Four + strOCC5 - OCC_Five  ) < 16 and (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two + strOCC3 - OCC_Three + strOCC4 - OCC_Four + strOCC5 - OCC_Five ) > 0 then %>
	class="style11">

	<% else %>
		class="style13">

		<% end if %>
<%= gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two + strOCC3 - OCC_Three + strOCC4 - OCC_Four + strOCC5 - OCC_Five %></td>
		<td style="height: 19px" class="style68"></td>
		<td style="height: 19px" class="style69"></td>
	</tr>
	<tr>
		<td class="style31" style="height: 22px"><font face="arial" size="2"><%= dateadd("d", 5, strnow)%>&nbsp;</td>
				<% strDate1 = datepart("w", dateadd("d", 5, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

		<td class="style31" style="height: 22px"><font face="arial" size="2"><%= strDayofWeek %>&nbsp;</td>
		<td class="style64" style="height: 22px"></td>
		<td class="style64" style="width: 6px; height: 22px;"></td>
		<td  class="style64" style="height: 22px"></td>
			<td   class="style63" style="height: 22px"><font face="arial" size="2"><%= strKCOP6 %></td>
	
		<td   class="style63" style="height: 22px"><font face="arial" size="2"><%= strPMX6 %></td>
		<td   class="style63" style="height: 22px"><font face="arial" size="2"><%= strOCC6 %></td>
								<%  strSelectdate = dateadd("d", 5, strnow)
				strsql = "Select Total_OCC, total_RF from tblTempYardTotals where INV_Date = '" & strSelectdate & "'"
		
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
KCOP_six = MyRec.fields("Total_RF")
OCC_six = MyRec.fields("Total_OCC")
else
KCOP_six = 0
OCC_six = 0
end if
MyRec.close %>

		<td  class="style62"><%= KCOP_six %></td>
		<td  class="style62"><%= OCC_six%></td>
						<td style="height: 22px" 

		<% if (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three + strKCOP4 + strPMX4 - KCOP_Four  + strKCOP5 + strPMX5 - KCOP_Five + strKCOP6 + strPMX6 - KCOP_Six ) > 48 then %>
		
		class="style7">
				<% elseif (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three + strKCOP4 + strPMX4 - KCOP_Four  + strKCOP5 + strPMX5 - KCOP_Five + strKCOP6 + strPMX6 - KCOP_Six )  < 49 and (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three + strKCOP4 + strPMX4 - KCOP_Four  + strKCOP5 + strPMX5 - KCOP_Five + strKCOP6 + strPMX6 - KCOP_Six ) > 26 then %>
		
		class="style9">
	<% elseif (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three + strKCOP4 + strPMX4 - KCOP_Four  + strKCOP5 + strPMX5 - KCOP_Five + strKCOP6 + strPMX6 - KCOP_Six )  <27 and (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three + strKCOP4 + strPMX4 - KCOP_Four  + strKCOP5 + strPMX5 - KCOP_Five + strKCOP6 + strPMX6 - KCOP_Six) > 0 then %>
	class="style11">

	<% else %>
		class="style13">

		<% end if %>

	<%= gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three + strKCOP4 + strPMX4 - KCOP_Four  + strKCOP5 + strPMX5 - KCOP_Five + strKCOP6 + strPMX6 - KCOP_Six  %></td>
<td style="height: 22px" 
<% if (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two + strOCC3 - OCC_Three + strOCC4 - OCC_Four + strOCC5 - OCC_Five + strOCC6 - OCC_Six) > 24 then %>
	class="style7">
<% elseif  (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two + strOCC3 - OCC_Three + strOCC4 - OCC_Four + strOCC5 - OCC_Five + strOCC6 - OCC_Six) < 25 and (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two + strOCC3 - OCC_Three + strOCC4 - OCC_Four + strOCC5 - OCC_Five + strOCC6 - OCC_Six) > 15 then %>
class="style9">
<% elseif  (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two + strOCC3 - OCC_Three + strOCC4 - OCC_Four + strOCC5 - OCC_Five + strOCC6 - OCC_Six) < 16 and (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two + strOCC3 - OCC_Three + strOCC4 - OCC_Four + strOCC5 - OCC_Five + strOCC6 - OCC_Six) > 0 then %>
	class="style11">

	<% else %>
		class="style13">

		<% end if %>
<%= gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two + strOCC3 - OCC_Three + strOCC4 - OCC_Four + strOCC5 - OCC_Five + strOCC6 - OCC_Six %></td>
		<td class="style69" style="height: 22px"></td>
		<td class="style69" style="height: 22px"></td>
	</tr>
	<tr>
		<td class="style31"><font face="arial" size="2"><%= dateadd("d", 6, strnow)%></td>
				<% strDate1 = datepart("w", dateadd("d", 6, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

		<td  class="style31"><font face="arial" size="2"><%= strDayofWeek %></td>
		<td class="style64"></td>
		<td class="style57" style="width: 6px"></td>
		<td  class="style57"></td>
			<td   class="style63"><font face="arial" size="2"><%= strKCOP7 %></td>
	
		<td   class="style63"><font face="arial" size="2"><%= strPMX7 %></td>
		<td   class="style63"><font face="arial" size="2"><%= strOCC7 %></td>

									<%  strSelectdate = dateadd("d", 6, strnow)
				strsql = "Select Total_OCC, total_RF from tblTempYardTotals where INV_Date = '" & strSelectdate & "'"
		
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
KCOP_seven = MyRec.fields("Total_RF")
OCC_seven = MyRec.fields("Total_OCC")
else
KCOP_seven = 0
OCC_seven = 0
end if
MyRec.close %>

		<td  class="style62"><%= KCOP_seven %></td>
		<td  class="style62"><%= OCC_seven %></td>
										<td style="height: 22px" 

		<% if (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three + strKCOP4 + strPMX4 - KCOP_Four  + strKCOP5 + strPMX5 - KCOP_Five + strKCOP6 + strPMX6 - KCOP_Six + strKCOP7 + strPMX7 - KCOP_Seven ) > 48 then %>
		
		class="style7">
				<% elseif (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three + strKCOP4 + strPMX4 - KCOP_Four  + strKCOP5 + strPMX5 - KCOP_Five + strKCOP6 + strPMX6 - KCOP_Six + strKCOP7 + strPMX7 - KCOP_Seven )  < 49 and (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three + strKCOP4 + strPMX4 - KCOP_Four  + strKCOP5 + strPMX5 - KCOP_Five + strKCOP6 + strPMX6 - KCOP_Six + strKCOP7 + strPMX7 - KCOP_Seven ) > 26 then %>
		
		class="style9">
	<% elseif (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three + strKCOP4 + strPMX4 - KCOP_Four  + strKCOP5 + strPMX5 - KCOP_Five + strKCOP6 + strPMX6 - KCOP_Six + strKCOP7 + strPMX7 - KCOP_Seven )  < 27 	 and (gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three + strKCOP4 + strPMX4 - KCOP_Four  + strKCOP5 + strPMX5 - KCOP_Five + strKCOP6 + strPMX6 - KCOP_Six + strKCOP7 + strPMX7 - KCOP_Seven ) > 0 then %>
	class="style11">

	<% else %>
		class="style13">

		<% end if %>


	<%= gKCOP + gPMX + strKCOP1 + strPMX1 - KCOP_One + strKCOP2 + strPMX2 - KCOP_Two + strKCOP3 + strPMX3 - KCOP_Three + strKCOP4 + strPMX4 - KCOP_Four  + strKCOP5 + strPMX5 - KCOP_Five + strKCOP6 + strPMX6 - KCOP_Six + strKCOP7 + strPMX7 - KCOP_Seven  %></td>

<td style="height: 22px" 
<% if (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two + strOCC3 - OCC_Three + strOCC4 - OCC_Four + strOCC5 - OCC_Five + strOCC6 - OCC_Six + strOCC7 - OCC_Seven) > 24 then %>
	class="style7">
<% elseif  (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two + strOCC3 - OCC_Three + strOCC4 - OCC_Four + strOCC5 - OCC_Five + strOCC6 - OCC_Six + strOCC7 - OCC_Seven) < 25 and (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two + strOCC3 - OCC_Three + strOCC4 - OCC_Four + strOCC5 - OCC_Five + strOCC6 - OCC_Six + strOCC7 - OCC_Seven) > 15 then %>
class="style9">
<% elseif  (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two + strOCC3 - OCC_Three + strOCC4 - OCC_Four + strOCC5 - OCC_Five + strOCC6 - OCC_Six + strOCC7 - OCC_Seven) < 16 and (gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two + strOCC3 - OCC_Three + strOCC4 - OCC_Four + strOCC5 - OCC_Five + strOCC6 - OCC_Six + strOCC7 - OCC_Seven) > 0 then %>
	class="style11">

	<% else %>
		class="style13">

		<% end if %>


<%= gocc + strOcc1 - OCC_One + strOCC2 - OCC_Two + strOCC3 - OCC_Three + strOCC4 - OCC_Four + strOCC5 - OCC_Five + strOCC6 - OCC_Six + strOCC7 - OCC_Seven %></td>
		<td class="style69"></td>
		<td class="style69"></td>
	</tr>
	<tr>
		<td class="style31">&nbsp;</td>
		<td class="style31">&nbsp;</td>
		<td class="style57">&nbsp;</td>
		<td class="style57" style="width: 6px">&nbsp;</td>
		<td  class="style57">&nbsp;</td>
		<td  class="style56">&nbsp;</td>
		<td class="style56">&nbsp;</td>
		<td class="style56">&nbsp;</td>
		<td  class="style54">&nbsp;</td>
		<td class="style54">&nbsp;</td>
		<td class="style69">&nbsp;</td>
		<td class="style69">&nbsp;</td>
		<td class="style69">&nbsp;</td>
		<td class="style69">&nbsp;</td>
		<td class="style51">&nbsp;</td>
	</tr>
</table>
</font>
<p class="style16">
<strong><span class="style15">Planned Loads from OWB

<table style="width: 25%" class="style71">
     <tr>
    

      <td  align="left"  class="style70"><font face="Arial">Scheduled Date</font></td>
         <td  align="left"  class="style70"><font face="Arial">Material #</font></td>
      <td  align="center"  class="style70"><font face="Arial">Count</font></td>
      </tr>
      
      <% strsql = "SELECT Count(tblSapAutoImport.ID) AS CountOfID, tblSapAutoImport.Delivery_Date, Right([Material],8) AS Mat FROM tblSapAutoImport "_
&" WHERE (((tblSapAutoImport.Vendor)='OWENSBORO GLOBAL SALES MILL') AND ((tblSapAutoImport.Received_qty)=0)) "_
&" GROUP BY tblSapAutoImport.Delivery_Date, Right([Material],8) "_
&" HAVING (((tblSapAutoImport.Delivery_Date)>20090709) AND ((Right([Material],8))='70000166' Or (Right([Material],8))='70000071')) "_
&" ORDER BY tblSapAutoImport.Delivery_Date"

       Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
  
   %>
         
  	<% 
    
       ii = 0
       while not MyRec.Eof
    
    %>
    <% if ( ii mod 2) = 0 Then %>
           <tr class=tablecolor1>
    <% else %>
       <tr class=tablecolor2>
    <% end if %>

         <td  align="left"  class="style16">
			<font face="Arial" size = 1 class="style16"><%= mid(MyRec.fields("Delivery_Date"),5,2)%>-<%= right(MyRec.fields("Delivery_Date"),2)%></font></td>
         <td  align="left"  class="style16"><font face="Arial"><%= MyRec.fields("Mat")%></font></td>
      <td  align="center"  class="style16"><font face="Arial"><%= MyRec.fields("countofid")%></font></td>

   </tr>
    <% 
       ii = ii + 1
 
       MyRec.MoveNext
     Wend
     MyRec.close
    %>


   </table>







<p class="style1"><strong>SAP Inventory<br>To get real-time inventory use SAP, Transaction MB52</strong></p>
<% Dim strSAPdate
strsql = "SELECT Max(tblInventoryHistory.Rpt_date) AS MaxOfRpt_date FROM tblInventoryHistory"

     Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionPolaris") 

strSAPdate = MyRec.fields("MaxofRpt_Date")
MyRec.close

strsql = "SELECT tblInventoryHistory.Rpt_date, tblInventoryHistory.SAP_nbr, tblReference.Commodity, tblReference.Description, tblInventoryHistory.Location, tblInventoryHistory.UOM, tblInventoryHistory.Value "_
&" FROM tblInventoryHistory INNER JOIN tblReference ON tblInventoryHistory.SAP_nbr = tblReference.SAP "_
&" WHERE (((tblInventoryHistory.Rpt_date)='" & strSAPdate & "') AND ((tblReference.Commodity)='OCC' "_
&" Or (tblReference.Commodity)='Recycle Fiber') AND ((tblInventoryHistory.Location)='PLP1' Or (tblInventoryHistory.Location)='PLP4'  Or (tblInventoryHistory.Location)='PLP3'))"
       Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionPolaris") 


 %>
<table width="60%" border="1">
     <tr bgcolor="#D9D9FF">
    

      <td  align="left" colspan="2" class="style16">
		<font face="Arial" size = 1 class="style16">Last Downloaded<br> from SAP</font></td>
         <td  align="left" colspan="2" class="style16"><font face="Arial">Material #</font></td>
      <td  align="center" colspan="2" class="style16"><font face="Arial">Location</font></td>
         <td  align="center" colspan="2" class="style16"><font face="Arial">Commodity</font></td>

   <td  align="center" colspan="2" class="style16"><font face="Arial">Description</font></td>
   <td  align="center" colspan="2" class="style16"><font face="Arial">Qty</font></td>
   <td  align="center" colspan="2" class="style16"><font face="Arial">UOM</font></td>

      </tr>
           <%  ii = 0
       while not MyRec.Eof
    
    %>
<tr>
      
      <td  align="left" colspan="2" class="style16"><font face="Arial"><%= strSAPDate %></font></td>
         <td  align="left" colspan="2" class="style16"><font face="Arial"><%= MyRec.fields("SAP_nbr") %></font></td>
      <td  align="center" colspan="2" class="style16"><font face="Arial"><%= MyRec.fields("Location") %></font></td>
         <td  align="center" colspan="2" class="style16"><font face="Arial"><%= MyRec.fields("Commodity") %></font></td>
   <td  align="center" colspan="2" class="style16"><font face="Arial"><%= MyRec.fields("Description") %></font></td>
   <td  align="center" colspan="2" class="style16"><font face="Arial"><%= MyRec.fields("Value") %></font></td>
   <td  align="center" colspan="2" class="style16"><font face="Arial"><%= MyRec.fields("UOM") %></font></td>

</tr>
<%   MyRec.MoveNext
     Wend
     MyRec.close %>
</table>