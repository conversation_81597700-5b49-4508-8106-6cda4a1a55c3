																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Rejected Load List</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->




<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate, strsql3

strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -60, strtdate)

    strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "' and User_type = 'A'"


    Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL3, Session("ConnectionString")


If not Myrec2.eof then


strsql = "SELECT tblCars.* FROM tblCars WHERE Rejected = 'YES' order by CID desc"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Rejected Loads </b></font></td>
<td align = center><font face="Arial"><a href="Sixty_Day list.asp">Identify Reject</a>&nbsp;</td>


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1>  
	 <tr class="tableheader">

	<td>  <font face="Arial" size="1">Receipt</td>
		<td  > <p align="center">       <font face="Arial" size="1">Release<br> Number</font></td>
			<td  ><font face="Arial" size="1"><b>Location</b></font></td>
		<td  ><font face="Arial" size="1"><b>Trailer</b></font></td>
		<td  ><font face="Arial" size="1"><b>Carrier</b></font></td>
		<td  ><font face="Arial" size="1">Species</font></td>
    
		<td  ><font face="Arial" size="1">Vendor</font></td>
		<td  >       <font face="Arial" size="1">PO Number</font></td>
		<td  >        <p align="center">        <font face="Arial" size="1">Tons<br> Received</font></td>
			<td  >        <p align="center">        <font face="Arial" size="1">Deduction</font></td>
		<td  >        <p align="center">        <font face="Arial" size="1">RF Bales</font></td>
		

		<td>        <font face="Arial" size="1">Date<br> Received</font></td>
			<td>        <font face="Arial" size="1">Date<br> Rejected</font></td>
				<td>        <font face="Arial" size="1">Date<br>Unloaded</font></td>
							<td>        <font face="Arial" size="1">Inv <br>Depletion</font></td>

				<td>        <font face="Arial" size="1">Date<br>Left Yard</font></td>

		<td  >        <font face="Arial" size="1">Generator</font></td>
		<td  >        <font face="Arial" size="1">Generator<br> City</font></td>
		<td  >        <font face="Arial" size="1">Gen<br> State</font></td>
		<td  >        <font size="1" face="Arial">Other</font></td>

<td><font size="1" face="arial">Rejected</font></font></td>
	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td> <font size="1" face="Arial"><%= MyRec.fields("CID") %></td>
	<td  ><font size="1" face="Arial"><%= MyRec.fields("Release_Nbr")%>&nbsp;</font></td>
	<td  ><font size="1" face="Arial"><%= MyRec.fields("Location")%>&nbsp;</font></td>

	<td  > <font size="1" face="Arial"><b><%= MyRec.fields("Trailer")%></font></b></td>
			<td  ><font size="1" face="Arial"><b><%= MyRec.fields("Carrier")%>&nbsp;</font></b></td>


		<td  ><font face="Arial" size="1"> <%= MyRec.fields("Species")%></font></td>
		<td><font size="1" face="Arial"><%= MyRec.fields("Vendor")%>&nbsp;</font></td>
		<td  >        <font size="1" face="Arial">        <%= MyRec.fields("PO")%>&nbsp;</font></td>

		
		
			<td  >  <font size="1" face="Arial"> 	 <font size="1" face="Arial">        <%= MyRec.fields("Tons_received")%>&nbsp;</font></td>
				  <td  ><font size="1" face="Arial">&nbsp;<%= MyRec.fields("Deduction")%></font></td>
				 <td  ><font size="1" face="Arial">&nbsp;<%= MyRec.fields("Bales_RF")%>&nbsp;</font></td>


		<td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Date_Received")%></font></td>
				<td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Reject_date")%></font></td>
				<td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Date_Unloaded")%>&nbsp;</font></td>
						<td  >		 <font size="1" face="Arial">        <%= MyRec.fields("INV_Depletion_date")%>&nbsp;</font></td>

					<td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Reject_Shipped")%>&nbsp;</font></td>

       <td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Generator")%>&nbsp;</font></td>
		<td  >	 <font size="1" face="Arial">        <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
		<td  > <font size="1" face="Arial"> <%= MyRec.fields("Gen_State")%>&nbsp;</font></td>
		<td  >	 <font size="1" face="Arial">        <%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>
<% 
if MyRec.fields("Rejected") = "YES" then %>
<td align = center >	 <font size="1" face="Arial">
<a href="Rejected_load.asp?p=r&id=<%= Myrec.fields("CID")%>">YES</a></font></td>
<% else %>
<td align = center >	 <font size="1" face="Arial">

<a href="Rejected_load.asp?p=r&id=<%= Myrec.fields("CID")%>">NO</a></font></td>
<% end if %>
	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>

<% Myrec2.close 
end if %><!--#include file="Fiberfooter.inc"-->