																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Add Inbound Virgin Fiber </TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->


<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate, rstSpecies, objEPS, MyConn3, strsql3, rstVendor, strRelease, strComments

strdate = formatdatetime(Now(),2)

  set objGeneral = new ASP_CLS_General
  if objGeneral.IsSubmit() Then	
  Dim strTrailer,  strETA, strLead, strVendor, strSpecies,  strSAP, strPO, strBales, strTons, strUOM
  
  	strTrailer = request.form("Trailer")
  	

  	
  	If len(request.form("Vendor")) > 0 then
  	strVendor = request.form("Vendor")
  	else
  	strVendor = ""
  	end if 
  	
  	If len(request.form("Units")) > 0 then
  	strUnits = request.form("Units")
  	else
  	strUnits = 0
  	end if
  	
  		
  	strLead = request.form("Lead_time")

  	
  	If len(request.form("Tons")) > 0 then
  	strTons = request.form("Tons")
  	else
  	strTons = 0
  	end if
  	
  		
  	If len(request.form("Release")) > 0 then
  	strrelease= request.form("Release")
  	else
  	strRelease = ""
  	end if
  	
  		If len(request.form("Comments")) > 0 then
  	strComments= Replace(request.Form("Comments"), "'", "''") 
  	else
  	strComments = ""
  	end if
  	
  	
  	strSpecies = request.form("Species")
  	
  If len(request.form("PO")) > 1 then	
 strPO = Request.form("PO")
 else
 strPO = ""
 end if 
 
  	  strsql3 = "Select SAP, UOM, Schedule_agreement from tblVFSpecies where Fiber_species = '" & strSpecies & "'"
  	   	 Set MyConn3 = Server.CreateObject("ADODB.Recordset")
   	 MyConn3.Open strSQL3, Session("ConnectionString")

 
   	 strUOM = MyConn3.fields("UOM")
   	 strSAP = MyConn3.fields("SAP")
   	 MyConn3.close
   	 
   	 strdate = request.form("Date_shipped")
   	 
     strETA = dateadd("d", strLead, strDate)
        	
  	
	strsql =  "INSERT INTO tblVirginFiber ( Status, Trailer, Date_shipped, ETA, Lead_time, Vendor, Other_comment, Release, "_
	&" Species, SAP_Nbr,  PO, Bales_VF, Tons, UOM) "_
	&" SELECT 'Inbound', '" & strTrailer & "', '" & strDate & "', '" & strETA & "', " & strLead & ", '" & strVendor & "', '" & strComments & "', '" & strRelease & "',"_
	&" '" & strSpecies & "', '" & strSAP & "', '" & strPO & "', " & strUnits & "," & strTons & ",  '" & strUOM & "'"
	
	
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
		
end if
	Call getdata()
%>

<body>
<br>
	
<form name="form1" action="VF_Add.asp" method="post" ID="Form1">
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Add Inbound Virgin Fiber Load</b></font></td>
<td align = right><font face="Arial"><a href="VF_Inbound.asp">Return</a>&nbsp;</td>


</tr>
	    </table>



	<p>&nbsp;</p>
	<table border="1" width="100%" id="table1">
<tr bgcolor = #CCCCFF>
		<td bgcolor="#FFFFD7">

<font face="arial" size="1">Trailer/Car</font></td>
		<td bgcolor="#FFFFD7">

<font face="arial" size="1">Date Shipped</font></td>
		<td bgcolor="#FFFFD7">

<p align="center"><font face="Arial" size="1">Release #</font></td>
		<td bgcolor="#FFFFD7"><b><font face="arial" size="1">Lead Time (Days)</font></b></td>
		<td bgcolor="#FFFFD7"><b><font face="arial" size="1">Vendor</font></b></td>
	</tr>
	<tr>
		<td><font face = arial size = 1><input type="text" name="Trailer" size="20"></td>
		<td><font face = arial size = 1>
		<input type="text" name="Date_shipped" size="20"></td>
		<td>
		<p align="center"><font face = arial size = 1>
		<input type="text" name="Release" size="20"></td>
		<td><font face = arial size = 1>
<select name="Lead_time" size="1" >
 <option value=0>--Select --</option>

     <option>1</option>
	<option>2</option>
	<option>3</option>
	<option>4</option>
	<option>5</option>
	<option>6</option>
	<option>7</option>
	<option>8</option>
	<option>9</option>
	<option>10</option>
	<option>11</option>
	<option>12</option>
	<option>13</option>
	<option>14</option>
	<option>15</option>
	<option>16</option>
	<option>17</option>
	<option>18</option>
	<option>19</option>
	<option>20</option>

     </select></td>
		<td><font face = arial size = 1>
<select name="Vendor" size="1" >
 <option value="">--Select --</option>
       <%= objGeneral.OptionListAsString(rstVendor, "Vendor", "Vendor", strVendor) %>
     </select></td>
	</tr>
</table>
<br>
		
		<table border="1" width="100%" cellpadding="0" id="table4" >
			<tr bgcolor = #CCCCFF>

		<td style="font-family: Arial" bgcolor="#FFFFD7"><font size="1"><b>Item 
		Description</b></font></td>

	<td style="font-family: Arial" bgcolor="#FFFFD7">
	<p align="center"><font size="1"><b>Tons</b></font></td>

	<td style="font-family: Arial" bgcolor="#FFFFD7" align="center">
	<font size="1"><b>Units</b></font></td>
	<td style="font-family: Arial" bgcolor="#FFFFD7">
	<p align="center"><font size="1">PO</font></td>
	
	<td style="font-family: Arial" bgcolor="#FFFFD7">
	<p align="center"><font size="1">Comments</font></td>
	
	</tr>
	
	<td><font face = arial size = 1>
<select name="Species" size="1" >
 <option value="">--Select --</option>
       <%= objGeneral.OptionListAsString(rstSpecies, "Fiber_species", "Fiber_species", strSpecies) %>
     </select></td>
<td height="18"><p align="center"><font face = arial size = 1><%= strScheduler%>&nbsp;<!--webbot bot="Validation" s-data-type="Number" s-number-separators="x." --><input type="text" name="Tons" size="9"></td>
<td height="18" align="center"><font face = arial size = 1><input type="text" name="Units" size="7"></td>
<td height="18"><p align="center"><font face = arial size = 1>
<%= strSafety_Specialist%>&nbsp;<input type="text" name="PO" size="12" value="<%= strPO %>"></td>


<td height="18">
<p align="center"><font face = arial size = 1>
<input type="text" name="Comments" size="39"></td>


</tr></table>


<p align="center"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></p>
</form>

<%   Function GetData()
        set objEPS = new ASP_CLS_Fiber
        set rstSpecies = objEPS.VFSpecies()
        set rstVendor = objEPS.VFVendor()

    End Function %><!--#include file="Fiberfooter.inc"-->