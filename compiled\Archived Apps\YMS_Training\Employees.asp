																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE> Employee list</TITLE>
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->


<% Dim MyRec, strsql, MyConn, strPost


strPost = "OK"


strsql = "SELECT tblEmployees.* from tblEmployees order by Last_name, First_name"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>
<style type="text/css">
.style1 {
	border: 1px solid #000000;
}
.style2 {
	border: 1px solid #C0C0C0;
}
.style3 {
	text-align: center;
	border: 1px solid #C0C0C0;
}
.style4 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style5 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style6 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	text-align: center;
}
.style7 {
	font-size: small;
}
.style8 {
	border-width: 1px;
}
</style>
</head>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=60%  border=1 align = center>
<tr>
 <TD align = left style="width: 15%" class="style8"><font size="2" face = arial>
	<span class="style7"><strong>
<% if strPost = "OK" then%></strong>

 <a href="Employee_Add.asp"><strong>Add New</strong></a>
 <strong>
 <% else %>
 &nbsp;</strong></span>
 <% end if %></font></td>
<td align = center><b>
<font face="arial" size="4" > Employee List</font></b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </td>
<td align = center>&nbsp;</td>


</tr>
	    </table><strong><br>
	
	
	</strong>
	
	
	<TABLE align = center style="width: 45%" class="style1">  
	 <tr bgcolor="#FFFFCC">
<td style="height: 37px" class="style5">&nbsp;</td>
	<td style="height: 37px" align="left" class="style4" >  <strong>Last Name</strong></td>
<td style="height: 37px" align="left" class="style4" >  <strong>First Name</strong></td>
	<td style="height: 37px" class="style6" >  <strong>Employee ID</strong></td>
	<td style="height: 37px" class="style6" >  <strong>Inactive</strong></td>

<td style="height: 37px" class="style5">&nbsp;</td>
		


	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
	<td style="height: 30px" class="style3"> <font size="2" face="Arial">
	<% if strPost = "OK" Then %>
	<a href="Employee_Edit.asp?id=<%= MyRec.fields("ID") %>">Edit</a>
	<% else %>
	&nbsp;
	<% end if %></td>	
	<td style="height: 30px" align="left" class="style2" > <font size="2" face="Arial"> <%= MyRec.fields("Last_name")%>&nbsp;</font></td>
     <td style="height: 30px" align="left" class="style2"> <font size="2" face="Arial">   <%= MyRec.fields("First_name")%>&nbsp;</font></td>
          <td style="height: 30px" class="style3"> <font size="2" face="Arial">   <%= MyRec.fields("UID")%>&nbsp;</font></td>
     	<td style="height: 30px" class="style3"> <font size="2" face="Arial">
		 <% if MyRec.fields("Inactive") = -1 then %>
Yes
 <% else %>
&nbsp;
 <% end if %></font>
</td>

	<td style="height: 30px" class="style3"> <font size="2" face="Arial">
	<% if strPost = "OK" Then %>
<a href="Employee_Delete.asp?id=<%= MyRec.fields("ID") %>">Delete</a
	<% else %>
	&nbsp;
	<% end if %></td>	
	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
     MyRec.close
    %>
</table>

