																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Edit Fiber Trucks in Yard</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->




<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate

strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -7, strtdate)
strRdate = "1/31/2017"

    strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "' and User_type = 'A'"


    Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL3, Session("ConnectionString")
	If not MyRec2.eof then
	stradmin = "OK"
	else
	stradmin = ""
	end if
	MyRec2.close

strsql = "SELECT tblCars.* FROM tblCars WHERE (Grade='RF' or Grade = 'BROKE') and Carrier <> 'RAIL' AND Trailer Is Not Null AND (Location='yard' or Location = 'YARD') and Trailer <> 'UNKNOWN'  and date_received > '" & strRdate & "' order by Release_nbr"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Edit Fiber Trailers in Yard</b></font></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1>  
	 <tr class="tableheader">
<td>&nbsp;</td>
			<td  > <p align="center">       <font face="Arial" size="1">Print<br> Receipt</font></td>
		<td  > <p align="center">       <font face="Arial" size="1">Release<br> Number</font></td>
	
		<td  ><font face="Arial" size="1"><b>Trailer</b></font></td>
		<td  ><font face="Arial" size="1"><b>Carrier</b></font></td>
		<td  ><font face="Arial" size="1">Species</font></td>
    
		<td  ><font face="Arial" size="1">Vendor</font></td>
		<td  >       <font face="Arial" size="1">PO Number</font></td>
	<td>       <font face="Arial" size="1">REC Number</font></td>
		<td  >       <p align="center">       <font face="Arial" size="1">Bales</font></td>
		<td  >       <p align="center">       <font face="Arial" size="1">Gross<br> Weight</font></td>
		<td  >
        <p align="center">
        <font face="Arial" size="1">Tare<br> Weight</font></td>
		<td  >
        <p align="center">
        <font face="Arial" size="1">Tons<br> Received</font></td>
		<td  >
        <font face="Arial" size="1">Date<br> Received</font></td>
		<td  >
        <font face="Arial" size="1">Generator</font></td>
		<td  >
        <font face="Arial" size="1">Generator<br> City</font></td>
		<td  >
        <font face="Arial" size="1">Gen<br> State</font></td>
		<td  >
        <font size="1" face="Arial">Other</font></td>
	<td  >
        <font size="1" face="Arial">Delete<br>Receipt</font></td>
    
<td align="center" >
        <font size="1" face="Arial">Damaged<br>Trailer</font></td>
	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
  
    <%  if MyRec("Grade") = "BROKE" and isnull(MyRec("Release_nbr")) then %>
        <td> <font size="1" face="Arial"><a href="Edit_STO_Receipt.asp?id=<%= MyRec.fields("CID") %>">Edit</a></td>
           <% elseif len(MyRec("Release_nbr")) > 2 and Carrier <> "RAIL" then %>
              <td> <font size="1" face="Arial"><a href="Receipt_Release_Edit.asp?id=<%= MyRec.fields("CID") %>">Edit</a></td>

<% else %>
<td> <font size="1" face="Arial"><a href="TruckReceiptYardEdit.asp?id=<%= MyRec.fields("CID") %>">Edit</a></td>
<% end if %>
	<td align = center> <font size="1" face="Arial"><a href="Truck_receipt_print.asp?id=<%= MyRec.fields("CID") %>"><%= MyRec.fields("CID")%></a></td>
	<td  ><font size="1" face="Arial"><%= MyRec.fields("Release_Nbr")%>&nbsp;</font></td>

	<td  > <font size="1" face="Arial"><b><%= MyRec.fields("Trailer")%></font></b></td>
			<td  ><font size="1" face="Arial"><b><%= MyRec.fields("Carrier")%>&nbsp;</font></b></td>


		<td  ><font face="Arial" size="1"> <%= MyRec.fields("Species")%></font></td>
		<td><font size="1" face="Arial"><%= MyRec.fields("Vendor")%>&nbsp;</font></td>
		<td  >        <font size="1" face="Arial">        <%= MyRec.fields("PO")%>&nbsp;</font></td>
	<td  >  <font size="1" face="Arial">        <%= MyRec.fields("REC_Number")%></font></td>
	
		<td  >  <font size="1" face="Arial">  <b>
			<% if len(MyRec("Bales_RF")) > 0 then
			if MyRec("Bales_RF") > 0 then%>
			<%= MyRec.fields("Bales_RF")%>
			<% else %>	
			
			<%= MyRec.fields("Bales_VF")%>
			<% end if 
			else %>		
			
			<%= MyRec.fields("Bales_VF")%>

			<% end if %>&nbsp;</b></font></td>

		<td align = right >		 <font size="1" face="Arial">        <%= MyRec.fields("Gross_Weight")%>&nbsp;</font></td>
		<td align = right >		 <font size="1" face="Arial">        <%= MyRec.fields("Tare_Weight")%>&nbsp;</font></td>
		<td align = right <% if MyRec.fields("Tons_received") = 21  or MyRec.fields("Weigh_required") = "W" then %>bgcolor = pink <% end if %>  >
				 <font size="1" face="Arial">        <%= MyRec.fields("Tons_received")%>&nbsp;<%= MyRec.fields("Weigh_required") %></font></td>
		<td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Date_Received")%></font></td>
       <td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Generator")%>&nbsp;</font></td>
		<td  >	 <font size="1" face="Arial">        <%= MyRec.fields("Gen_City")%></font></td>
		<td  > <font size="1" face="Arial"> <%= MyRec.fields("Gen_State")%></font></td>
		<td  >	 <font size="1" face="Arial">        <%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>
<td align = center >	 <font size="1" face="Arial"><a href="ReceiptDelete.asp?id=<%= Myrec.fields("CID")%>"> Delete</a></font></td>
<% 
if MyRec.fields("Damaged") = "YES" then %>
<td align = center >	 <font size="1" face="Arial">
<% if strAdmin = "OK" then %>
<a href="Damage_Edit.asp?id=<%= Myrec.fields("CID")%>">YES</a></font></td>
<% else %>
YES</font></td>
<% end if %>
<% else %>
<td align = center >	 <font size="1" face="Arial">
<% if strAdmin = "OK" then %>
<a href="Damage_Edit.asp?id=<%= Myrec.fields("CID")%>">NO</a></font></td>
<% else %>
NO</a></font></td>

<% 
end if
end if %>
	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>


<!--#include file="Fiberfooter.inc"-->