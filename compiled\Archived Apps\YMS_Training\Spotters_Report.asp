<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">


<TITLE>Spotter's Report</TITLE>
<!--#include file="classes/asp_cls_headerLOU.asp"-->
<!--#include file="classes/asp_cls_SessionStringLDN.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<style type="text/css">
.style1 {
	border-width: 1px;
	background-color: #EAF1FF;
}
.style2 {
	text-align: center;
}
.auto-style5 {
	border: 1px solid #000000;
}
.auto-style6 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Calibri;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #F4EADF;
}
.auto-style7 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Calibri;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.auto-style8 {
	font-family: Arial, Helvetica, sans-serif;
}

.auto-style9 {
	text-align: center;
	font-family: Arial, Helvetica, sans-serif;
}

</style>
<script language="javascript">
function printReport(strMove)
	{
    //alert(document.form1.hidReleaseNums.value);
	window.open("Spotters_Report_Print.asp?ReleaseNums=" + document.form1.hidReleaseNums.value + '&move=' + strMove);
	}
</script>
</head>

<body>
<form name="form1" action="Spotters_Report.asp" method="post" class="auto-style8" >
<div align="right">
			<a href="Spotters_Report_Print2.asp">PRINT ONLY</a>&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; 
<input type="button" value="Print & Move" onclick="printReport('Move');">&nbsp;&nbsp;&nbsp; </div>
	<div class="auto-style9">
		Spotter's Report&nbsp;&nbsp;&nbsp; <%= Dateadd("h", 1, formatdatetime(Now(),0)) %></div>
<input type="hidden" name="hidReleaseNum" value="">

 <table width="100%">
 <tr>
 <td valign="top">
 <table align="center" class="auto-style5" style="width: 75%" cellspacing="0">
<tr>
     <td class="auto-style6" style="text-align:center">Move</td>
	 <td class="auto-style6" style="text-align:center">Carrier</td>
	 <td class="auto-style6" style="text-align:center">Trailer # In</td>
	 <td class="auto-style6" style="text-align:center">Release #</td>
	 <td class="auto-style6" style="text-align:center">Move From</td>
	 <td class="auto-style6" style="text-align:center">Move To</td>
	 <td class="auto-style6" style="text-align:center">Yard Status</td>
</tr>

</tr>
 <%

 strsql = "SELECT ReleaseNum, MoveStatus, CurrentLocation,CARRIER,MoveTo,[TRAILER# IN],[YARD STATUS] FROM Logistics.dbo.master WHERE MoveStatus IN (-1,1) Order by ReleaseNum"
  'strsql = "SELECT top 10 ReleaseNum, MoveStatus, CurrentLocation,CARRIER,MoveTo,[TRAILER# IN],[YARD STATUS] FROM Logistics.dbo.master WHERE MoveStatus IN (0,-1,1) Order by ReleaseNum"
 Set MyRec = Server.CreateObject("ADODB.Recordset")
 MyRec.Open strSQL, Session("ConnectionStringLDN")
 strReleaseNums = ""
 while not MyRec.eof
    If Len(strReleaseNums) > 0 Then
	   strReleaseNums = strReleaseNums & ","
	End If
    strReleaseNums = strReleaseNums & MyRec("ReleaseNum") %>
 <tr> 
 <td class="auto-style7"  style="text-align:center"><input type="checkbox" name="cboxMove_<%=MyRec("ReleaseNum")%>" value="1" <%If MyRec("MoveStatus") = 1 Or MyRec("MoveStatus") = -1 Then%> checked <%End If%> onclick="unCheck(<%=MyRec("ReleaseNum")%>)"></td> 
   <td class="auto-style7" style="text-align:center"><%= MyRec("Carrier") %>&nbsp;</td>
  <td class="auto-style7" style="text-align:center"><%= MyRec("TRAILER# IN")%>&nbsp;</td>
   <td class="auto-style7" style="text-align:center"><%= MyRec("ReleaseNum")%>&nbsp;</td>
  <td class="auto-style7" style="text-align:center"><%= MyRec("CurrentLocation")%>&nbsp;</td>
<td class="auto-style7" style="text-align:center"><%= MyRec("MoveTo")%>&nbsp;</td>
<td class="auto-style7" style="text-align:center">&nbsp;<%= MyRec("YARD STATUS")%>&nbsp;</td>
</tr>
<% MyRec.movenext
wend
MyRec.close %>

 </table>
 <input type="hidden" name="hidReleaseNums" value="<%=strReleaseNums%>">
 </form>
<!--#include file="LDNfooter.inc"-->