																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE> Call In List</TITLE>

<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_Session.asp"-->
<!--#include file="classes/asp_cls_DataAccess.asp"-->


<% 

Dim MyRec, strsql, MyConn, strPost
 Dim MyS, MySQ, strOK, strsite



%>
<style type="text/css">
.style1 {
	border: 1px solid #000000;
}
.style2 {
	border: 1px solid #C0C0C0;
}
.style3 {
	text-align: center;
	border: 1px solid #C0C0C0;
}
.style5 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style6 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	text-align: center;
}
.style7 {
	font-size: small;
}
.style8 {
	border-width: 1px;
}
.auto-style2 {
 
	background-color: #F2FBFF;
}
.auto-style3 {
 
	background-color: #F2FBFF;
	font-family: Arial, Helvetica, sans-serif;
}
.auto-style4 {
	background-color: #F2FBFF;
	font-family: Arial, Helvetica, sans-serif;
	text-align: center;
}
.auto-style5 {
	border: 1px solid #000000;
	font-family: Arial, Helvetica, sans-serif;
	text-align: center;
	background-color: #F2FBFF;
}
.auto-style6 {
	font-weight: bold;
	text-align: left;
}
.auto-style7 {
	text-decoration: none;
}
.auto-style8 {
	text-align: right;
}
.auto-style9 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	text-align: center;
}
</style>
</head>

<BODY onKeyPress="KeyPress()">
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>
<tr>
 <TD align = left style="width: 25%" class="style8"><font size="2" face = arial>
	<span class="style7"><strong>
 

 <a href="Call_Out.asp"><strong>Call In for Tech not on List</strong></a>
 <strong>
 </td>
<td class="auto-style6">
<font face="arial" size="4" >  Technicians Currently Reported Out<br><br></font></td>
<td align = center width="25%">&nbsp;</td>


</tr>
	    </table>
	<table width="100%"><tr>
	<td>&nbsp;</td>
	<td valign="top">
	<TABLE align = center style="width: 50%" class="style1">  
	 <tr bgcolor="#FFFFCC">
<td  class="auto-style2" style="height: 25px"></td>
	
 
	<td  align="left" class="auto-style3" style="height: 25px" valign="top" >  <strong>&nbsp;Name</strong></td>
	
	<td  align="left" class="auto-style3" style="height: 25px" valign="top" >  <strong>&nbsp;Team Leader</strong></td>
	<td class="auto-style4" style="height: 25px" >  <strong>Date Out</strong></td>



	<td class="auto-style4" style="height: 25px" >  <strong>Date In</strong></td>



	</tr>

 <% strtoday = date()
 strTeam = request.querystring("id") 
 if len(strTeam) > 3 then 
  
strsql = "SELECT tblCall.*, Team_leader  FROM tblCall INNER JOIN tblCallTech ON tblCall.Tech = tblCallTech.Tech  where Team_Leader = '" & strTeam & "' and (Date_in is null or Date_in > '" & strtoday & "') order by Tech"

 else
 
strsql = "SELECT tblCall.*, Team_leader  FROM tblCall INNER JOIN tblCallTech ON tblCall.Tech = tblCallTech.Tech  where Date_in is null or Date_in > '" & strtoday & "' order by Tech"
end if
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    

  
 Dim ii
       ii = 0
       while not MyRec.Eof
       strTeamEMP_ID = ""
       
           strTech = MyREc("Tech")
 
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
	<td style="height: 30px" class="style3"> <font size="2" face="Arial">
	 
	<a href="Tech_Report_In.asp?id=<%= MyRec.fields("ID") %>">Report In</a>
	 </td>	
	<td  align="left" class="style2" > <font size="2" face="Arial"> <%= strTech %>&nbsp;</font></td>
    
	<td  align="left" class="style2" > <font size="2" face="Arial"> <%= MyRec("Team_Leader")%>&nbsp;</font></td>

    
		<td class="style3" > <font size="2" face="Arial"> <%= MyRec.fields("Date_out")%>&nbsp;</font></td>

	<td class="style3" > <font size="2" face="Arial"> <%= MyRec.fields("Date_in")%>&nbsp;</font></td>

    


	</tr>

 <%
       ii = ii + 1
  
       MyRec.MoveNext
     Wend
     MyRec.close
    %>
</table>
<td class="auto-style5" style="width: 15%">
<table width="100%"><tr>
<td><strong>View by Team Leader</strong></td>
<td class="auto-style8"><strong><a href="Call_in.asp">Clear</a></strong></td></tr>

<% strsql = "select distinct Team_Leader from tblCallTech order by Team_Leader"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
While not MyRec.eof %>

<tr><td class="style5"><font face="arial" size="2"><a href="Call_in.asp?id=<%= MyRec("Team_leader") %>" class="auto-style7"><%= MyREc("Team_Leader") %></a></font></td>
<td class="auto-style9">

<%
strTeamL = MyRec("Team_Leader")
 strsql3 = "SELECT Count(tblCall.ID) AS CountOfID FROM tblCall INNER JOIN tblCallTech ON tblCall.Tech = tblCallTech.Tech WHERE Team_leader = '" & strTeamL & "' and (Date_in is null or Date_in > '" & strtoday & "')"
	Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")
    strCount = MyRec3("CountofID")
    MyREc3.close %>
<font face="arial" size="2"><%= strCount %></td></tr>
<% MyRec.movenext
wend
MyRec.close %>
	
 </table>	
	
	
	</td></tr></table>



<!--#include file="footer.inc"-->

