
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Delete Employee</TITLE>

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<% Dim strSQL, MyRec

 set objGeneral = new ASP_CLS_General
 Dim strid
 
 strid= request.querystring("id")
 
 strsql= "Select * from tblEmployees where ID = " & strid
 
     Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    strEmployee = MyRec.fields("First_name") & " " &  MyRec.fields("Last_name")
    strUID = MyRec.fields("UID")
    MyRec.close
 

if objGeneral.IsSubmit() Then

strsql = "SELECT Count(ID) AS Total FROM tblAbsence where EID = " & strid
     Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

   If MyRec.fields("Total") = 0 then
   strDelete = "YES" 
   else
   strDelete = "NO"
   end if
   MyRec.close

If strDelete = "YES" then
	Call SaveData() 
	else
	Response.write("<font face=arial size=3 color=red><br><b>There are Absentee Records entered for this employee.  It can not be deleted.</b></font>")
end if
End if

%>
<style type="text/css">
.style1 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
.style2 {
	border: 1px solid #C0C0C0;
}
.style3 {
	font-family: Arial;
}
</style>
</head>

<body>
<form name="form1" action="Employee_Delete.asp?id=<%= strid%>" method="post">
 
<table border="0" cellpadding="0" cellspacing="0" width = 60% style="border-collapse: collapse" bordercolor="000000" height="25" align = center >
  <tr>
    <td  bgcolor="#FFFFFF" bordercolor="#CCCCFF" height="25" >
    <p align="center"><font face="Arial" size="4">Delete Employee</font></td><td align = center height="25"><font face="Arial"><b><a href="Employees.asp">RETURN</a></b></font></td>

    <td align = center bgcolor="#FFFFFF" height="25">
  <Input name="Submit" type="submit" Value="Click to Delete" style="float: right" ></td>
  </tr>
</table><br><br>
<div align="center">
<table cellpadding="0" cellspacing="0" style="width: 45%;" bordercolor="#808080"  height="10" class="style1">
    <tr>
        <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="22" align="center" ><b>
	<font face="Arial">Employee</font></b></td>
   
    <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="22" align="center" class="style3" >
	<strong>Employee ID</strong></td>
   
  </tr>
  
    <tr>
        <td height="47"  align="center" class="style2" >  	
     <font face="Arial">	
	<% = strEmployee %></font></td>

    <td height="47"  align="center" class="style2" >  	
     <font face="Arial">	
	<% = strUID %></font></td>
    	

  </tr>
  </table>
</div>



</form>
   
  

<p>&nbsp;</p>
   
  

<p>&nbsp;</p>
   
  

</body>

</html> <%
 
  Function SaveData()
strid = request.querystring("id")
  strsql = "Delete from tblEmployees where ID = " & strid

   
  	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
 
         
          Response.redirect("Employees.asp")
  End Function
  
   %>