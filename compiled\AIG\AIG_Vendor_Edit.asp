
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Add AIG User</TITLE>
<!--#include file="classes/asp_cls_DataAccessAIG.asp"-->
<!--#include file="classes/asp_cls_headerAIG.asp"-->
<!--#include file="classes/asp_cls_SessionStringAIG.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->
<%

Dim strSQL, MyRec, strid, strVendor, strVendor_nbr, strPayee_nbr, strVendor_name, strVendor_address, strVendor_street, strEmail_address, strSummary
strid = Request.querystring("id")

strsql = "SELECT tblVendors.* FROM tblVendors where ID = " & strid & "" 
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


 strVendor = MyRec.fields("Vendor")
 strVendor_nbr = MyRec.fields("Vendor_nbr")
 strVendor_name = MyRec.fields("Vendor_name")
 strVendor_address = MyRec.fields("Vendor_address")
 strVendor_street = MyRec.fields("Vendor_street")
 strEmail_address = MyRec.fields("Email_address")
  strPayee_nbr = MyRec.fields("Payee_nbr")
  strSummary = MyRec.fields("Address")
MyRec.close
 set objGeneral = new ASP_CLS_General

  
if objGeneral.IsSubmit() Then

	Call SaveData() 

End if
%>
<style type="text/css">
.style1 {
	font-family: Arial;
	font-size: x-small;
}
.auto-style2 {
	font-weight: bold;
	font-family: Calibri;
	font-size: medium;
	border-color: #C0C0C0;
	border-width: 1px;
	background-color: #E7EBFE;
}
.auto-style3 {
	border-color: #C0C0C0;
	border-width: 1px;
	font-family: Calibri;
	font-size: medium;
	background-color: #E7EBFE;
}
.auto-style4 {
	font-family: Calibri;
}
.auto-style5 {
	font-family: Calibri;
	font-weight: bold;
	font-size: medium;
}
.auto-style6 {
	font-family: Calibri;
	font-size: medium;
}
.auto-style7 {
	font-size: medium;
}
</style>
</head>

<body>
<form name="form1" action="AIG_Vendor_Edit.asp?id=<%= strid%>" method="post">
 <br class="auto-style6">
<table border="0" cellpadding="0" cellspacing="0" width = 60% style="border-collapse: collapse" bordercolor="000000" height="25" align = center >
  <tr>
    <td  bgcolor="#FFFFFF" bordercolor="#CCCCFF" height="25" >
    <p align="center"><font face="Arial" size="4" class="auto-style7"><strong>
	<span class="auto-style4">Edit AIG Vendor&nbsp;</span></strong><span class="auto-style4">
	</span> 
    </font>  	</td><td align = center height="25" class="auto-style5"><a href="AIG_Vendor_Address.asp">RETURN</a></td>

    <td align = center bgcolor="#FFFFFF" height="25">
  <Input name="Submit" type="submit" Value="Submit" style="float: right" class="auto-style6" ></td>
  </tr>
</table><br class="auto-style6">
<div align="center">
<table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#808080"  height="10" width="50%" id="table2">
    <tr>
    <td height="22" align="center" class="auto-style2" >SAP Vendor Name</td>
   
  </tr>
  
    <tr>
    <td  bordercolor="#CCCCFF" height="47" width = "25%" align="center" >  	<font face="Arial">	
	<input type="text" name="Vendor_name" size="52" value="<%=  strVendor_name %>" class="auto-style6"></font></td>
    	

  </tr>
  
 


  </table>



<table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#808080"  height="10" width="75%">
    <tr>
    <td height="22" align="center" class="auto-style2" >YMS Vendor Name</td>
    <td height="22" align="center" class="auto-style2" >Vendor Number</td>
     <td height="22" align="center" class="auto-style2" >Remit To Number</td>
   
  </tr>
  
    <tr>
    <td  bordercolor="#CCCCFF" height="47" align="center" >  	<font face="Arial">	
	<input type="text" name="Vendor" size="37" value="<%= strVendor%>" class="auto-style6"></font></td>
    <td bordercolor="#CCCCFF" height="47" align="center" >	
		
	<font face="Arial">	
	<input type="text" name="Vendor_nbr" size="18" value="<%= strVendor_nbr%>" class="auto-style6"></font></td>
     <td bordercolor="#CCCCFF" height="47" align="center" >	
		
	<font face="Arial">	
	<input type="text" name="Payee_nbr" size="18" value="<%= strPayee_nbr%>" class="auto-style6"></font></td>	

  </tr>

  </table>


  


<table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#808080"  height="10" width="75%" id="table1">
    <tr>
    <td height="22" align="center" class="auto-style2" >Vendor Street&nbsp; or PO Address</td>
   
  </tr>
  
    <tr>
    <td  bordercolor="#CCCCFF" height="47" width = "50%" align="center" >  	<font face="Arial">	
	<input type="text" name="Vendor_address" size="63" value="<%= strVendor_address %>" class="auto-style6"></font></td>
    	

  </tr>
  
 


  </table>

  

<div align="center">
<table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#808080"  height="10" width="75%" id="table3">
    <tr>
    <td height="22" align="center" class="auto-style2" >Vendor City, State, Zip</td>
   
  </tr>
  
    <tr>
    <td  bordercolor="#CCCCFF" height="47" width = "50%" align="center" >  	<font face="Arial">	
	<input type="text" name="Vendor_street" size="63" value="<%= strVendor_street%>" class="auto-style6"></font></td>
    	

  </tr>
  
 


  </table>


<table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#808080"  height="10" width="75%" id="table4">
    <tr>
    <td height="22" align="center" class="auto-style2" >Vendor Email Address</td>
   
  </tr>
  
    <tr>
    <td  bordercolor="#CCCCFF" height="47" width = "50%" align="center" class="auto-style6" >  	
	<font face="Arial">	
	<input type="text" name="Email_address" size="63" style="width: 869px" value="<%= strEmail_address%>"></font></td>
    	

  </tr>
      <tr>
    <td height="22" align="center" class="auto-style3" >
	<strong>Weekly Receipt Summary Email Address</strong></td>
   
  </tr>
  
    <tr>
    <td  bordercolor="#CCCCFF" height="47" width = "50%" align="center" >  	
	<textarea name="Summary" rows="2" style="width: 761px" class="auto-style6"><%= strSummary%></textarea></td>
    	

  </tr>

   </table>

</form>

</div>



</body>

</html>

 <%
  
  Function SaveData()
  

    
  strid = Request.querystring("id")  
  
if isnull(Request.form("Vendor_name")) then
strVendor_name = ""
else
strVendor_name = Replace(Request.form("Vendor_name"), "'", "''") 
end if

if isnull(Request.form("Vendor")) then
strVendor = ""
else
strVendor = Replace(Request.form("Vendor"), "'", "''") 
end if

if isnull(Request.form("Vendor_address")) then
strVendor_address = ""
else
strVendor_address = Replace(Request.form("Vendor_address"), "'", "''") 
end if

if isnull(Request.form("Vendor_street")) then
strVendor_street = ""
else
strVendor_street = Replace(Request.form("Vendor_street"), "'", "''") 
end if

if isnull(Request.form("Email_address")) then
strEmail_address = ""
else
strEmail_address = Replace(Request.form("Email_address"), "'", "''") 
end if

if isnull(Request.form("Summary")) then
strSummary = ""
else
strSummary = Replace(Request.form("Summary"), "'", "''") 
end if


if isnull(Request.form("Vendor_nbr")) or len(Request.form("Vendor_nbr")) < 4 then
strVendor_nbr = ""
else
strVendor_nbr = Request.form("Vendor_nbr")
end if

if isnull(Request.form("Payee_nbr")) or len(Request.form("Payee_nbr")) < 4 then
strPayee_nbr = 0
else
strPayee_nbr = Request.form("Payee_nbr")
end if

        strsql = "Select Vendor_nbr from tblVendors where Vendor_nbr = '" & strVendor_nbr & "' and ID <> " & strid & ""
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
If not MyRec.eof then
Response.write("<font face=arial size=3 color=red><b>This Vendor number is already being used</b></font>")
else


   strsql = "Update tblVendors set [Vendor] =  '" & strVendor & "', Vendor_nbr = '" & strVendor_nbr & "', Address = '" & strSummary & "', "_
   &" Vendor_name = '" & strVendor_name & "', Vendor_address = '" & strVendor_address & "', "_
   &" Vendor_street = '" & strVendor_street & "', Payee_nbr = '" & strPayee_nbr & "', Email_address = '" & strEmail_Address & "' where ID = " & strid & " "
   
  	 set MyRec = new ASP_CLS_DataAccess
     MyRec.ExecuteSql strSql 
         
    Response.redirect("AIG_Vendor_Address.asp")
     
end if     
  End Function
  
   %><!--#include file="AIGfooter.inc"-->