<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 14.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Enter VF Trailer Receipt</title>
<style type="text/css">
.style1 {
	font-size: x-small;
}
.style2 {
	font-family: Arial;
	font-size: x-small;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID, strRelease, MyConn, objMOC, rstFiber, rstSpecies, strKCWeighed, strCarId, MyRec5, strsql5
    Dim strTrailer, strCarrier, strLocation, strSAP
    Dim strReleaseNbr, rstTrailer , strTrailerWeight , strTractor, strTrailerTID, strbales
 
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strOther, strR, strsql3, strSpecies, strSAP_Nbr, strVendor,  strNet, strPO


 	set objGeneral = new ASP_CLS_General

  call getdata()

if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
	strRelease  = ""
	
	strPO = "UNKNOWN"
	
	strSAP_Nbr = ""
	strSpecies = "Unresolved"
	
 	strTrailer = Request.form("Trailer")
	strOther = Replace(Request.form("Other_Comments"), "'", "''") 
	strDateReceived = Request.form("Date_received")
	
	strCarrier = Request.form("Carrier")
	
	strLocation = Request.form("Location")
	
		
	

	Call SaveData() 
		If Request.form("Print_receipt") =  "ON" then
		Response.redirect("Truck_receiptVFG.asp?id=" & strCarID & "&p=" & strbales)

		else
		Response.redirect ("Generic_receipt_VF.asp")
		end if
	
	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to save this information.</font></br>")
	MyRec.close
	end if

  

end if
%>



<body>


<table width = 100%><tr><td width = 33%>
&nbsp;</td><td align = center width = 34%><b><font face="Arial" size="4">
Enter Trailer Receipt</font> </b></td><td align = right width = 33%></td></tr></table>




<form name="form1" action="Generic_receipt_VF.asp" method="post">
<div align="center">
<table border="1" cellspacing="0" bordercolor="#C0C0C0" width="63%" bgcolor="#FFFFEA" style="border-collapse: collapse" cellpadding="0">

<tr>
    <td bgcolor="#FFFFEA" align="right"><b><font face="Arial" size="2">&nbsp;</font></b></td>

    <td  align = center bgcolor="#FFFFEA" colspan="2">&nbsp; 
	</td>
  </tr><tr>
    <td bgcolor="#FFFFEA" align="right"><b><font face="Arial" size="2">Species:&nbsp;</font></b></td>

    <td  bgcolor="#FFFFEA"> &nbsp;<font face="Arial"><strong><span class="style1">Unresolved</span></strong>
   </font> </td>

    <td  bgcolor="#FFFFEA" width="45%"> 
	<p align="center"><font face="Arial"><font size="2" face="Arial"><b>Check 
	to Print Receipt:&nbsp;
</b></font> <input type="checkbox" name="Print_receipt" value="ON" checked></td>

  </tr>
  
    <tr>
    <td bgcolor="#FFFFEA" align="right">&nbsp;</td>

    <td  bgcolor="#FFFFEA" colspan="2"> 
     <font face="Arial"><strong>&nbsp;</strong></font></td>
  </tr>
  
  <tr>
    <td  align = right bgcolor="#FFFFEA" height="29" >
   <span class="style2"><strong>Trailer #</strong></span><b><font face="Arial" size="2">:&nbsp;</font></b></td>
<td  align = left colspan="2" height="29" bgcolor="#FFFFEA">

      <input type="text" name="Trailer" size="15" >&nbsp;
		<font face="Arial" size="2"><b>(Required)</b></font></td></tr>
  <tr>

      <td  bgcolor="#FFFFEA" align = right height="28">
	<font face="Arial" size="2"><b>Carrier: </b></font></td>
<td  align = left colspan="2" height="28" bgcolor="#FFFFEA">

     <font face="Arial" size="2"> &nbsp;<select name="Carrier">
 	<option value="" selected>  Select Carrier (Required)</option>
      <%= objGeneral.OptionListAsString(rstFiber, "Carrier", "CDesc", strCarrier) %>
     </select>&nbsp; <b>(Required)</b></td></tr>
<tr>
    <td  bgcolor="#FFFFEA">  
	<p align="right">  <font face="Arial" size="2"><b>&nbsp;</b></font></td>
    <td bgcolor="#FFFFEA" colspan="2">   &nbsp;</td>
  </tr>

<tr>
	<td  bgcolor="#EEEDDD" height="22" width="17%">
	<p align="right"><b><font face="Arial" size="2">&nbsp;Bales:</font></td>
    <td  bgcolor="#EEEDDD" height="22" width="21%" colspan="2">      
	
      <b><font face="Arial" size="2"> 
		<input name="Bales" size="15" style="height: 22px" > </font></td>
  </tr>
<tr>
	<td  bgcolor="#FFFFEA" height="22" width="17%">&nbsp;</td>
    <td  bgcolor="#FFFFEA" colspan="2" height="22">&nbsp;</td>
  </tr>

       <tr><td  align = right bgcolor="#FFFFEA" ><font face="Arial" size="2"><b>Date Received:&nbsp;</b></font></td>
<td align = left colspan="2" bgcolor="#FFFFEA"> <input type="text" name="Date_Received" size="15" value = <%= formatdatetime(Now(),2)%>></td></tr>
             
         <tr>
          <td  align = right bgcolor="#FFFFEA" >
  <font face="Arial" size="2"><b>Comments:&nbsp;</b></font></td >
   <td align = left colspan="2" bgcolor="#FFFFEA">   <input type="text" name="Other_Comments" size="25">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font face="Arial"><font size="2">&nbsp;Location: </font>   
	<select name="Location" style="font-weight: 700" size="1">

      <option selected>YARD</option>
 
     </select></font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	</td></tr>
<tr>
    <td  bgcolor="#FFFFEA">&nbsp;</td>

    <td bgcolor="#FFFFEA" colspan="2"> 
	<Input name="Update" type="submit" Value="Submit"  ></td>
  </tr>


</table>

</div>

</form>
</body>
<%
Function GetData()
  set objMOC = new ASP_CLS_FIBER
        set rstFiber = objMOC.FiberCarrier()
           
End Function

Function SaveData()
dim strbody, strCID, strECarrier, strETrailer, strELoad, strEDoc, strEDC, strEDCName, strEAdd, strECity, strEState, MyConn2, strsql4
dim strECC, strEBCC

             

   	 
if len(request.form("Bales")) > 0 then
		strBales = Request.form("Bales")
else
strBales = 0
end if
		

	strTrailerTID = 0
	strTrailerweight = 0
	strGrossWeight = 0
	strTareWeight = 0
    strNet = 0
	

	strSAP = ""

	strCarrier = request.form("Carrier")
	
	
	Dim strRightNow
	strRightnow = now()
	
	'Response.redirect("Troublshoot.asp?t=" & strTrailerweight & "&TID=" & strTrailerTID & "&Tr=" & strTractor & "&g=" & strGrossweight & "&tare=" & strTareWeigght)
	strsql = "INSERT INTO tblCars ( Bales_VF, Carrier, Species, Grade, PO, Date_received, Location,  Trailer, Other_Comments,  Tons_Received, Net, Gross_weight, Tare_weight,  Entry_Time, Entry_BID, Entry_Page) "_
	&" SELECT " & strBales & ", '" & strCarrier & "', 'Unresolved', 'VF', 'UNKNOWN', '" & strDateReceived & "', 'YARD',   "_
	&" '" & strTrailer & "', '" & strOther & "', 0, 0, 0, 0, '" & strRightNow & "', '" & Session("EmployeeID") & "', 'Generic_receipt_VF'"
	
	
	
	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
				strsql5 = "SELECT Max(tblCars.CID) AS MaxOfCID FROM tblCars WHERE tblCars.Trailer = '" & strTrailer & "'"
			
   	 Set MyRec5 = Server.CreateObject("ADODB.Recordset")
   	 MyRec5.Open strSQL5, Session("ConnectionString")
   	 strCarID = MyRec5.fields("MaxofCID")
   	 MyRec5.close
		
			
			strEmailTo = "<EMAIL>"
        	strECC = "<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>"
            strEBCC = "<EMAIL>"
                 
			Set objMail = Server.CreateObject("CDO.Message")		
			objMail.From = "<EMAIL>"
			objMail.To = strEmailTo
			objMail.CC = strECC
			objMail.BCC = strEBCC
			
			objMail.Subject = "Receipt for Truck #: " & strTrailer & " Entered as an Exception "
			objMail.HTMLBody = "<font face = arial size = 2>Truck # " & strTrailer & " Receipt #: " & strid & " was entered as an exception. ."
			'objMail.Send
			Set objMail = nothing


End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->