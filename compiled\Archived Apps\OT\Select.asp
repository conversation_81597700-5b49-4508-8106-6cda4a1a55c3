
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE> Select</TITLE>
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_Session.asp"-->
<!--#include file="classes/asp_cls_DataAccess.asp"-->

</head>
<style type="text/css">
.style3 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
.style4 {
	font-family: Arial;
	font-size: medium;
}
.style7 {
	border-style: solid;
	border-color: #C0C0C0;
	font-family: Calibri;
	background-color: #E6ECF9;
}
.style8 {
	border-style: solid;
	border-color: #C0C0C0;
	font-family: <PERSON>ibri;
	background-color: #FFFFE1;
}
</style>
<script language="javascript">
function openWindow(pURL)
{
	myWindow = window.open(pURL, "myLittleCalendar", 'toolbar=no,location=no,directories=no,status=no,menubar=no,scrollbars=no,resizable=no,width=170,height=270');
}


</script>

<% Dim strName, strBID, strSite, strType


 set objGeneral = new ASP_CLS_General


if objGeneral.IsSubmit() Then
if request.form("Date") > 0 then 
Call SaveData()
else

If request.form("Crew") = "" then
Response.write ("You must select a crew")
elseif request.form("Machine") = "" then
Response.write ("You must select a Machine")
elseif   Request.form("New_Date") = "" then
Response.write("You must select or enter a date")
else


	Call SaveData() 
end if
end if ' if you did not select an existing record
End if ' end of submit%>

<body><form name="form1" action="Select.asp" method="post">
 
<table border="0" cellpadding="0" cellspacing="0" width = 60% style="border-collapse: collapse" bordercolor="000000" height="25" align = center >
  <tr>
    <td  bgcolor="#FFFFFF" bordercolor="#CCCCFF" height="25" >
    <p align="center" class="style4">
	Overtime Entry Selection</td>
    <td align = center height="25">&nbsp;</td>

    <td align = center bgcolor="#FFFFFF" height="25">
  <Input name="Submit" type="submit" Value="Submit" style="float: right" ></td>
  </tr>
</table><br><br>
<div align="center">
<table cellpadding="0" cellspacing="0" bordercolor="#808080"  height="10" class="style3" style="width: 45%">
    <tr>
<td align="center" class="style8" style="height: 57px" >Select Existing Week/Machine/Crew</td>
   
  <td align="center" class="style8" style="height: 57px" >
	&nbsp;<font face="Arial"> 
   <select name="Date" size="1">
      <option value=0>--- Select ---</option>
      <% strsql = "Select ID,  OT_Week, Machine, Crew from tblOT_Master order by OT_Week desc, Machine, Crew "
    	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		while not myrec.eof %>
		<option value='<%= MyREc("ID") %>'><%= MyRec.fields("OT_Week") %> - <%= MyRec("Machine") %> - <%= MyRec("Crew") %></option>
		<% MyRec.movenext
		wend
		MyRec.close %>
     </select>

 
      
 </font></td>
   
  </tr>
  
    <tr>
<td align="center" class="style7" style="height: 58px" >OR&nbsp; Enter New Week 
(Sunday)</td>
   
  <td align="center" class="style7" style="height: 58px" >
	<input type="text" name="New_Date" value="" style="width: 116px"><font face="Arial"><INPUT TYPE="image"  src="calendar.gif" width="70" height="31" VALUE="..." STYLE="font-family: MS Sans Serif,Arial; color: #885B3B; " onclick="openWindow('mlcpopup.asp?elt=New_Date'); return false;" class="style4"></font></td>
   
  	</tr>
	<tr>
<td align="center" class="style7" style="height: 61px" >Select Machine</td>
   
  <td align="center" class="style7" style="height: 61px" >
     <font face="Arial">
       <select name="Machine" size="1">
     <option selected value="">Select</option>

<option <% if strMachine = "Perini 1/3" then %> selected <% end if %>>Perini 1/3</option>
<option <% if strMachine = "Perini 2/4" then %> selected <% end if %>>Perini 2/4</option>
<option <% if strMachine = "Perini 5" then %> selected <% end if %>>Perini 5</option>
      
     </select></font></td>
   
 
  	</tr>
	<tr>
<td align="center" class="style7" style="height: 58px" >Select Crew</td>
   
  <td align="center" class="style7" style="height: 58px" >
     <font face="Arial">
       <select name="Crew" size="1">
           <option  value="">Select</option>
 
     		<option <% if strCrew = "A" then %> selected <% end if %> value="A">A/C</option>
			<option <% if strCrew = "B" then %> selected <% end if %> value="B">B/D</option>
	
 
     </select></font></td>

   
 
  	</tr>
  
       
  </table>
</div>



</form>
   
  

</body>
 <%

  
  Function SaveData()
  strMachine = request.form("Machine")
  strcrew = request.form("Crew")
 
  strDate = request.form("New_Date")
strid = request.form("Date")
 
         Response.redirect("OT_Entry.asp?id=" & strid & "&d=" & strdate & "&m=" & strmachine & "&c=" & strcrew)
  End Function
  
   %><!--#include file="footer.inc"-->