
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Send to WH</TITLE>

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<% Dim strSQL, MyRec

 set objGeneral = new ASP_CLS_General
 strpid = request.querystring("p")

 
 
strid = request.querystring("id")
strNow = formatdatetime(Now(),0)

If request.querystring("c") = "Y" then 
  strsql = "Update tblCars set Commodity = Null where CID = " & strid
  else
    strsql = "Update tblCars set Commodity = 'Y', Exit_date = '" & strNow & "' where CID = " & strid
  end if

   
  	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
         if strpid = "o" then
         Response.redirect("YardREportOCCoption.asp")
         else
 
         
          Response.redirect("YardREportBrokeOption.asp")
 end if
  
   %>