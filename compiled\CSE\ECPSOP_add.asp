<html>
<!--#include file="classes/asp_cls_SessionString.asp"-->


<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Add SOP and Task</title>
</head>
<% dim strsql, MyRec, strid, strecp, strTask, objGeneral, strSid

strid = Request.querystring("id")
strSid = request.querystring("sid")


set objGeneral = new ASP_CLS_General
  		 if objGeneral.IsSubmit = True Then
  		strid = request.querystring("id") 
 	
  		strEcp = Replace(Request.form("ECP"), "'", "''") 
  		 strTask = Replace(Request.form("Task"), "'", "''") 

 strsql =  "INSERT INTO tblECP_SOP (Haz_ID, Space_ID, Task) SELECT " & strid & ", '" & strecp & "', '" & strTask & "'"
 Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 


Response.redirect("Section1.asp?id=" & strSid)
end if 

 %><body bgcolor="#E1E8FF">
 <p align="left"><b><font face = arial size = 3>Add </font><font face="arial">
	SWP and Task:</font></b><br><br>
 </p>
 <form name="form1" action="ECPSOP_Add.asp?id=<%= strID%>&sid=<%= strSID%>"  method="post" ID="Form1"  >
 
 <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="60%" bgcolor="#D9E1F9">
   
  <tr>
    <td   bgcolor="#FFFFDD">
	<p align="center"><font face="Arial" size="2">SWP #</font></td>
    <td bgcolor="#FFFFDD" align="center">
	<font face="arial" size="2">Task Name</font></td>

   
  </tr>
  <tr>
    <td align = center bgcolor="#FFFFFF" > <font face = arial size = 2>
	<input type="text" name="ECP" size="22" ></td>
    <td  bgcolor="#FFFFFF"  >
	<p align="center"><font face="Arial" size="2">
	<input type="text" name="Task" size="55" ></font></td>
	
  </table> 
	<p>&nbsp;</p>
	<p>&nbsp;<INPUT TYPE="submit" value="Submit"></p>
	</form>

 
 