<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Edit Transfer from Merchants</title>
<style type="text/css">
.style3 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
.style4 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	text-align: right;
	border-style: solid;
	border-width: 1px;
}
.style5 {
	border-style: solid;
	border-width: 1px;
}
.style6 {
	font-weight: bold;
	border-style: solid;
	border-width: 1px;
}
.style1 {
	margin-bottom: 0px;
}
.auto-style3 {
	font-weight: bold;
	border-style: solid;
	border-width: 1px;
	background-color: #E7EBFE;
}
.auto-style4 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	text-align: right;
	border-style: solid;
	border-width: 1px;
	background-color: #E7EBFE;
}
.auto-style5 {
	border-style: solid;
	border-width: 1px;
	background-color: #E7EBFE;
}
.auto-style6 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.auto-style7 {
	border-style: solid;
	border-width: 1px;
	background-color: #E7EBFE;
	text-align: center;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID, objMOC, rstFiber, strCarrier, strSAP, objEPS, strFactor
      
    Dim strTrailer, strSpecies, strESpecies, rstSpecies
    Dim strReleaseNbr
    Dim strRECNbr
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strGenerator
    Dim strGenCity
    Dim strGenState
    Dim strOther, strR, strsql3, strTransTrailer, strBrokeDescription

       strId  = Trim(Request.QueryString("id")) 
       strpage = request.querystring("p")


 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
 
		Call SaveData() 

	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to Transfer this load.</font></br>")
	MyRec.close
	end if

  
ELSE


Call getdata()
	end if

strsql = "SELECT tblCars.* FROM tblCars WHERE  CID = " & strid & ""

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
strTransTrailer = MyRec.fields("Transfer_Trailer_nbr")
strBrokeDescription = Trim(MyREc("Broke_Description"))
strCarrier = MyRec.fields("Trans_Carrier")
strSpecies = MyRec.fields("Species")
strTons = MyRec.fields("Tons_received")
strDateReceived = MyRec.fields("Transfer_Date")
strBales = MyRec("Bales_RF")
strShredOCC = MyRec("Shred_OCC")
strShredRF = MyREc("Shred_RF")
 
MyRec.close

%>



<body><br>
<table width = 100%><tr><td width = 33%></td><td align = center width = 34%><b><font face="Arial" size="4">
	Edit
Transfer Load from Merchants' Warehouse</font></b></td><td align = right width = 33%>&nbsp;</td></tr></table>



<form name="form1" action="Transfer_From_Merchants_Edit.asp?id=<%= strid%>&p=<%= strpage %>" method="post">
<div align="center">
<table cellspacing="0" bordercolor="#C0C0C0" bgcolor="#FFFFE8" style="width: 75%;" cellpadding="0" class="style3">
<tr>
    <td style="width: 361px" class="auto-style5">&nbsp;</td>

    <td class="auto-style5" colspan="2">&nbsp;</td>
  </tr>


      <td align = right style="width: 361px" class="auto-style3">
  <font face="Arial" size="2">Transfer Trailer Number:&nbsp;</font></td>
<td  align = left class="auto-style5" colspan="2">

      <input type="text" name="Trans_Trailer" size="15" value = "<%= strTransTrailer%>" tabindex="1"></td></tr>
      
       <tr><td style="width: 361px" class="auto-style5">&nbsp;</td>
  <td class="auto-style5" colspan="2">&nbsp;</td></tr>
<tr>
    <td style="width: 361px" class="auto-style3">  
	<p align="right">  <font face="Arial" size="2">Select Carrier:&nbsp;</font></td>
    <td class="auto-style5" colspan="2">   <select name="Carrier" tabindex="2">
 	<option value="BWIF" selected>BWIF</option>
      <%= objGeneral.OptionListAsString(rstFiber, "Carrier", "CDesc", strCarrier) %>
     </select></td>
  </tr>
        <tr>
    <td style="width: 361px" class="auto-style5">&nbsp;</td>

    <td class="auto-style5" colspan="2">&nbsp;</td>
  </tr>
  <tr>
    <td class="auto-style3" style="width: 361px">  
	<p align="right">  <font face="Arial" size="2">Select Species:&nbsp;</font></td>
    <td class="auto-style5" colspan="2">  
	<select name="Species" size="1" tabindex="3" >
 <option value="">--Select --</option>

       <%= objGeneral.OptionListAsString(rstSpecies, "Fiber_species", "Fiber_species", strSpecies) %>
     </select>
&nbsp;<b><font size="2" face = arial>&nbsp;(Species)<br><br>
	<select name="Broke_Description" size="1" class="style1" tabindex="4" >
 <option value="">--Select Broke Type --</option>
<% strsql = "SELECT * from tblBrokeSap where Category = 'BROKE' order by  Type"   
 	 Set MyConn = Server.CreateObject("ADODB.Recordset")
   	 MyConn.Open strSQL, Session("ConnectionString")
while not MyConn.eof
%>
 
 
       <option <% if strBrokeDescription = MyConn("Type") then %> selected <% end if %> value="<%= MyConn("Type")%>"><%= MyConn("SAP") %>-<%= MyConn("Type") %></option>
     <% MyConn.movenext
     wend
     MyConn.close %>
          
     </select>&nbsp; (If you selected BROKE, then you must select type of Broke)<br>
	<br>

     
      <font face="Arial" size="2"><font size="2" face = arial>

     
      <input type="text" name="Enter_species" size="29" value="<%= strSpecies %>"></font>&nbsp; 
	Enter Species ONLY if it was&nbsp;not a choice in the dropdown</font><br>
	</font></b>&nbsp;<b><font size="2" face = arial>&nbsp;&nbsp;</font></b></td>
  </tr>
  <tr>
    <td class="auto-style4" style="width: 361px"><strong>Enter 
	Bales for Species other than Secondary Fiber:&nbsp;&nbsp;&nbsp; </strong></td>

    <td class="auto-style3" colspan="2"><font size="2" face = arial>

     
      <b>
      <input type="text" name="Bales" size="11" style="width: 36px" value="<%= strBales %>" tabindex="5"></b>&nbsp; Tons will be calculated when you Submit.&nbsp;&nbsp; </font></td>
  </tr>

    <tr>
    <td align="right" style="height: 50px; width: 361px" class="auto-style3">
	<font face="Arial" size="2">Enter Tons or Pounds for Secondary Fiber&nbsp;&nbsp;<br>
&nbsp;(Broke, KBLD, OCC, PMX, MXP, SHRED, OF3, USBS, LPSBS, SWL, HWM)&nbsp;&nbsp; <br>
	or for Species not in Dropdown:&nbsp;&nbsp;&nbsp; </font></td>
 
    <td style="height: 50px" class="auto-style5" colspan="2">

     
      <input type="text" name="Tons" size="11" style="width: 60px" value="<%= strTons %>" tabindex="6"></td>

  </tr>
    <tr>
    <td style="width: 361px" class="auto-style5">&nbsp;</td>

    <td class="auto-style5" colspan="2">&nbsp;</td>
  </tr>





       <tr>
          <td  align = right height="27" style="width: 361px" class="auto-style3" >
    <font face="Arial" size="2">Date Transferred:&nbsp;</font></td>
<td align = left height="27" class="auto-style5" colspan="2">

      <input type="text" name="Date_Received" size="15" value = "<%= strDateReceived%>" tabindex="8"></td></tr>
<tr>
    <td style="width: 361px" class="auto-style5">&nbsp;</td>

    <td class="auto-style5" colspan="2">&nbsp;</td>
  </tr>

  <tr>
    <td style="width: 361px" class="auto-style3">
	<p align="right"><font face="Arial" size="2">Print Movement Order:</font></td>

    <td align = left class="auto-style5"> <font size="1" face="Verdana">  
	<input type="checkbox"  value="ON" name="Print_receipt" checked></font></td>

    <td class="auto-style7">  <span class="auto-style6"> <strong>If SHRED, KBLD, PMX, 
	OF3 or HBX going to OCC, check 
	here</strong></span>:<font size="1" face="Verdana"><input type="checkbox"  value="ON" name="OCC" <% if strShredOCC = -1 then %> checked <% end if %>></font>&nbsp;</td>
  </tr>


  <tr>
    <td style="width: 361px" class="auto-style3">
	<p align="right"><font face="Arial" size="2"> </font></td>

    <td align = left class="auto-style5"> <font size="1" face="Verdana">  
 </font></td>

    <td class="auto-style7">  <span class="auto-style6">  <strong>If USBS, LPSBS, HWM, SWL or MXP going to RF, check here:
	</strong>:<font size="1" face="Verdana"><input type="checkbox"  value="ON" name="RF" <% if strShredRF = -1 then %> checked <% end if %>></font></td>
  </tr>

  <tr>
    <td style="width: 361px" class="auto-style5">&nbsp;</td>

    <td align = left class="auto-style5" colspan="2">&nbsp;</td>
  </tr>

  <tr>
    <td style="width: 361px" class="auto-style5">&nbsp;</td>

    <td align = left class="auto-style5" colspan="2"><Input name="Update" type="submit" Value="Submit" ></td>
  </tr>
</table>

</div>

</form>
</body>
<%
 Function GetData()
  set objMOC = new ASP_CLS_FIBER
        set rstFiber = objMOC.FiberCarrier()
        strDateReceived = formatdatetime(Now(),2)  
          set objEPS = new ASP_CLS_Fiber      
          set rstSpecies = objEPS.VFSpecies()
End Function




 Function SaveData()
 strBales = 0
strTrailer = Request.form("Trans_Trailer")
If len(Request.form("Enter_species")) > 0 then
strSpecies = Request.form("Enter_species")
else
strSpecies = Request.form("Species")
end if
strCarrier = Replace(Request.form("Carrier"), "'", "''")

strDateReceived = Request.form("Date_received")

strSAP = ""

Dim strRightnow, strTons
strRightnow = Now()
strTons = Request.form("Tons")
strGrade = "RF"



if len(request.form("Bales")) > 0 then
if request.form("Bales") > 0 then
strsql3 = "Select [Other] from tblVFSpecies where Fiber_Species = '" & strSpecies & "'"
   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")

	If not Myrec.eof then
	strFactor = Trim(MyRec.fields("Other"))
	MyRec.close
	end if

strTons =round(Request.form("Bales") * cdbl(strFactor),3)
strBales = request.form("Bales")
end if
end if



strsql = "Select Grade from tblVFSpecies where Fiber_Species = '" & strSpecies & "'"
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
If not MyRec.eof then
If len(MyRec("Grade")) > 0 then
strGrade = MyRec("Grade")
end if
end if


If strSpecies = "BROKE" then
strGrade = "BROKE"
strBrokeDescription = Request.form("Broke_Description")
strsql = "Select SAP from tblBrokeSAP where Type = '" & strBrokeDescription & "'"
Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
	If not MyRec.eof then
	strSAP = MyRec("SAP")
	end if
	MyRec.close
else

strBrokeDescription = ""
end if 



if strTons > 500 then
strTons = round(strTons/2000,3)
end if

If request.form("OCC") = "ON" then
strShredOCC = -1
else
strShredOCC = 0
end if

If request.form("RF") = "ON" then
strShredRF = -1
else
strShredRF = 0
end if



	strsql =  "Update tblCars Set Date_Received = '" & strDateReceived & "',   Transfer_Trailer_nbr = '" & strTrailer & "', Transfer_Date = '" & strDateReceived & "', "_
	&" Trans_Carrier = '" & strCarrier & "', Grade = '" & strGrade & "', Species = '" & strSpecies & "',  Tons_Received = " & strTons & ",  Shred_OCC = " & strShredOCC & ", "_
	&" Net = " & strTons & ", Broke_Description = '" & strBrokeDescription & "', SAP_Nbr = '" & strSAP & "', Bales_RF = " & strBales & ",  Shred_RF = " & strShredRF & " "_
	&"  where CID = " & strid
	
	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
         
         
 If Request.form("Print_receipt") = "ON" then
 Response.redirect("Transfer_Mreceipt.asp?id=" & strid)
 elseif strPage = "e" then 
 Response.redirect ("Sixty_Day_list_Shuttles.asp")
else


Response.redirect ("Edit_Merchants_Shuttle.asp")
end if


End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->