<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->

 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 14.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Enter Non-Fiber Trailer Receipt</title>
</head>
<%
    Dim strSQL, MyRec, strID, strPO, MyConn, objMOC, rstFiber, strPS, strAT, strUOM, strRelease, strSAP 
    Dim strTrailer, strCarrier
    Dim strPONbr
    Dim strRECNbr

    Dim strTonsReceived
    Dim strDateReceived

    Dim strOther, strR, strsql3, gDescription, gPO, gSAP_Nbr, gVendor, gOID, strNet



 	set objGeneral = new ASP_CLS_General

  

if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close

 	strTrailer = Request.form("Trailer")

	strDateReceived = Request.form("Date_received")
	strRECNbr = Request.form("Rec_Number")
	strCarrier = Request.form("Carrier")
	strUOM = Request.form("UOM")


	strTonsReceived = Request.form("Tons_received")


	
	If isnull(strTrailer) or strTrailer = ""  or isnull(strCarrier) or strCarrier = ""   then
	
	Response.redirect("EnterNF_Generic_Receipt.asp?id=" & strPO & "&n=T")
	else
	Call SaveData() 
	Response.redirect ("EnterNF_Generic_receipt.asp")
	end if
	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to save this information.</font></br>")
	MyRec.close
	end if

  
ELSE
  Call GetData()

	
	strDateReceived = formatdatetime(Now(),2)
	

end if
%>



<body>

<% if Request.querystring("n") = "T" then %>
<p align = center><font face = arial size = 3 color = red><b>You must enter the 
Carrier and Trailer Number.&nbsp;  Please re-enter your information.</p></b></font>
<% else %>
&nbsp;
<% end if %>
<table width = 100%><tr><td width = 33%>
<font face="Arial" size="2">

</td><td align = center width = 34%><b><font face="Arial" size="4">
Enter Non-Fiber Trailer Receipt </font> </b></td>
<td align = center><font face="Arial" size="2"><b></td>
<td align = right width = 10%><a href="SelectRelease.asp"><b><font face = arial size = 2>RETURN</a></font></b></td></tr></table>




<form name="form1" action="EnterNF_Generic_Receipt.asp" method="post">
<div align="center">
<table border="1" cellspacing="0" bordercolor="#808080" width="45%" bgcolor="#FFFFE1" style="border-collapse: collapse" cellpadding="0" bordercolorlight="#C0C0C0">
<tr>
    <td bgcolor="#FFFFDF" width="17%">&nbsp;</td>

    <td  bgcolor="#FFFFDF" colspan="4">&nbsp;</td>
  </tr>
    <tr>
    <td  align = left bgcolor="#FFFFDF" width="17%" >
   <b>
   <font face="Arial" size="2">PO Number:&nbsp;</font></b></td>
<td  align = left bgcolor="#FFFFDF" colspan="3">

     <font face="Arial" size="2">

		<font face="Arial"> 
<input name="PO" size="15" style="font-weight: 700"></font><%= strPONbr%><b>
		</b></font>

  </td>
<td  align = left bgcolor="#FFFFDF" width="36%">

     <font size="2" face="Arial"><b>Check to Print Receipt:&nbsp;&nbsp;</b></font><font face="Arial"><input type="checkbox" name="Print_receipt" value="ON" checked></td></tr>
  <tr>
    <td  align = left bgcolor="#FFFFDF" width="17%" height="27" >
   <b><font face="Arial" size="2">BOL #:</font></b></td>
<td  align = left bgcolor="#FFFFDF" colspan="4" height="27">

      <font face="Arial">

      <input name="Release" size="24" style="font-weight: 700"><font size="2">&nbsp; 
		
		</font></font></td></tr>
  <tr>

      <td  bgcolor="#FFFFDF" align = left width="17%">
  <font face="Arial" size="2"><b>Select Carrier:&nbsp;&nbsp;</b></font></td>
<td  align = left bgcolor="#FFFFDF" colspan="4">

      <font face="Arial">   
		<span style="background-color: #FFFFFF">   
	<select name="Carrier" style="border:1px solid #E3E0FE; font-weight: 700" size="1">
 	<option value="" selected>  Select Carrier (Required)</option>
      <%= objGeneral.OptionListAsString(rstFiber, "Carrier", "CDesc", strCarrier) %>
     </select></span></font></td></tr>
<tr>
    <td  bgcolor="#FFFFDF" align="left" width="17%">  
	<p>  <b><font face="Arial" size="2">Trailer</font></b><font face="Arial" size="2"><b>:&nbsp;&nbsp;</b></font></td>
    <td bgcolor="#FFFFDF" colspan="4">   <font face="Arial">   

      <input name="Trailer" size="24" style="font-weight: 700"> <font size="2">&nbsp;(REQUIRED 
	FIELD)</font></font></td>
  </tr>

       <tr>  <td  bgcolor="#FFFFDF" align = left width="17%"> <font face="Arial" size="2">
			<b>Quantity:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFDF" colspan="4">    <font face="Arial">  
<input name="Tons_Received" size="15" style="font-weight: 700"> </td></tr>

<tr><td  bgcolor="#FFFFDF" align="left" width="17%"><font face="Arial" size="2"><b>Unit of 
	Measure:&nbsp;</b></font></td>
    <td  bgcolor="#FFFFDF" colspan="4">    <font face="Arial">  
	<input name="UOM" size="15" style="font-weight: 700"></td>
  </tr>
       <tr><td  align = left bgcolor="#FFFFDF" width="17%" >
	<font size="2" face="Arial"><b>Location:</b> </font>   
		</td>
<td align = left bgcolor="#FFFFDF" colspan="4">   <font face="Arial">   
	<span style="background-color: #FFFFFF">   
	<select name="Location" style="border:1px solid #E3E0FE; font-weight: 700" size="1">

      <option selected>YARD</option>
 
     </select></span></font></td></tr>
       <tr><td  align = left bgcolor="#FFFFDF" width="17%" ><font face="Arial" size="2"><b>Date Received:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFDF" colspan="4"> <font face="Arial"> 
<input name="Date_received" size="15" value = "<%= strDateReceived%>" style="font-weight: 700">&nbsp; <font size="2">&nbsp;(REQUIRED 
	FIELD)</font></font></td></tr>
              <tr>
          <td  align = left bgcolor="#FFFFDF" width="17%" >
   <font face="Arial" size="2"><b>Vendor:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFDF" colspan="4">
     <font face="Arial" size="2"> <font face="Arial">
		<input name="Vendor" size="44" style="font-weight: 700"></font></TD></tr>
   
              <tr>
          <td  align = left bgcolor="#FFFFDF" width="17%" >
   <font face="Arial" size="2"><b>Commodity:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFDF" colspan="4">   <font face="Arial">   
	<span style="background-color: #FFFFFF">   
	<select name="Species" style="border:1px solid #E3E0FE; font-weight: 700" size="1">

      <option selected>FIBER</option>
 
     	<option>OCC</option>
 
     </select></span></font></TD></tr>
   
              <tr>
          <td  align = left bgcolor="#FFFFDF" width="17%" >
   <font face="Arial" size="2"><b>SAP #:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFDF" width="30%">
   <font face="Arial" size="2">  <font face="Arial"><input name="SAP_Nbr" size="24" style="font-weight: 700"></font></TD>
<td align = left bgcolor="#FFFFDF" width="13%">
   &nbsp;</TD>
<td align = left bgcolor="#FFFFDF" colspan="2">
   <p align="right">
   <font face="Arial" size="2"><b>    <font face="Arial">

      <input name="SAP_DOC_ID" size="15" value = "<%= strSAP %>" style="font-weight: 700; float:right"></font>SAP 
	Doc ID #:</TD></tr>
   
         <tr>
          <td  align = left bgcolor="#FFFFDF" width="17%" >
  <font face="Arial" size="2"><b>Comments/Other:&nbsp;</b></font></td >
   <td align = left bgcolor="#FFFFDF" colspan="4">   <font face="Arial">   
	<input name="Other_comments" size="60" style="font-weight: 700"></font></td></tr>
<tr>
    <td  bgcolor="#FFFFDF" width="17%">&nbsp;</td>

    <td bgcolor="#FFFFDF" colspan="4">&nbsp;</td>
  </tr>

  <tr>
    <td bgcolor="#FFFFDF" width="17%">&nbsp;</td>

    <td align = left bgcolor="#FFFFDF" colspan="4"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></td>
  </tr>
</table>

</div>

</form>
</body>
<% Function GetData()
  set objMOC = new ASP_CLS_FIBER
        set rstFiber = objMOC.FiberCarrier()
End Function

Function SaveData()

	If len(Request.form("SAP_DOC_ID")) > 0 then
	strSAP = Request.form("SAP_DOC_ID")	
	else
	strSAP = ""
	end if
	
	If len(Request.form("Sap_nbr")) > 0 then
	gSap_Nbr = Request.form("Sap_nbr")
	else
	gSAP_Nbr = ""
	end if
	
	
	If len(Request.form("Release")) > 0 then
	strRelease = Request.form("Release")
	else
	strRelease = ""
	end if
	
	If len(Request.form("Vendor")) > 0 then
	gVendor = Replace(Request.form("Vendor"), "'", "''") 

	else
	gVendor = ""
	end if
	
		If len(Request.form("PO")) > 0 then

	strPONbr = Request.form("PO")	
	else
	strPONbr = ""
	end if
	
	if len(Request.form("Date_received")) > 0 then
	strDateReceived = Request.form("Date_received")
	else
	strDateReceived = formatdatetime(Now(),2)
	end if

strCarrier = Request.form("Carrier")
strTrailer = Request.form("Trailer")
	
	Dim strRightNow
	strRightnow = Now()
	if len(strTonsReceived) = 0 then
	strTonsReceived = 0
	end if
	if len(strUOM) = 0 then
	strUOM = ""
	end if
	
	if len(goid) = 0 then
	gOID = 0
	end if
	
		If len(Request.form("Other_comments")) > 0 then
	strOther = Replace(Request.form("Other_comments"), "'", "''") 

	else
	strOther = ""
	end if
	
	
	
	strsql =  "INSERT INTO tblCars ( SAP_DOC_ID, Release_nbr, Carrier,  Grade, Species, SAP_Nbr, Vendor, PO, Date_received,  Location, NFID,  Trailer, Other_Comments, REC_Number, "_
	&" Tons_Received, Entry_Time, Entry_BID, Entry_Page,  UOM) "_
	&" SELECT '" & strSAP & "', '" & strRelease & "', '" & strCarrier & "',  'NF', '" & Request.form("Species") & "', '" & gSAP_Nbr & "', '" & gVendor & "', '" & strPONbr & "', '" & strDateReceived & "',   '" & Request.form("Location") & "', " & gOID & ",   "_
	&" '" & strTrailer & "', '" & strOther & "', '" & strRecNbr & "', " & strTonsReceived & ",  '" & strRightNow & "', '" & Session("EmployeeID") & "', 'EnterNF_Generic_Rec', "_
	&"  '" & strUOM & "'"
	
	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
				If Request.form("Print_receipt") =  "ON" then
	
				Response.redirect("Truck_receipt.asp?id=" & strRelease &"&t=" & strTrailer & "&d=" & strDateReceived)
		else
		Response.redirect("EnterNF_Generic_receipt.asp")
		end if

End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->