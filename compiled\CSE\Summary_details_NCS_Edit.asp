<html>
<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_Procedure.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Summary Details</title>
<style type="text/css">
.style1 {
	text-align: center;
}
.style2 {
	text-align: center;
	font-size: x-small;
	font-family: Arial, Helvetica, sans-serif;
}
.style3 {
	text-align: left;
}
.style4 {
	font-family: Arial;
}
.style5 {
	font-family: Arial;
	font-size: x-small;
}
.auto-style1 {
	border-width: 1px;
	background-color: #E2F3FE;
}
.auto-style2 {
	border-width: 1px;
	font-family: Arial;
	font-size: x-small;
	background-color: #E2F3FE;
}
.auto-style3 {
	border-width: 1px;
	background-color: #E2F3FE;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
</style>
</head>
<% dim strsql, MyRec, strid,  objGeneral, strDescription, strLocation, strComments, strDate, strSpaceID
dim objEPS, rstTeam, rstWA, MyConn, strTeam,  strWA, strFunctional, strAsset, strStatus,  strsql2, MyConn2
Dim strHAStatus, strApprover1, strApprover2, strApprovalDate

strid = Request.querystring("id")
strnow = dateadd("h", -5, now())
strDate = formatdatetime(strnow,2)


strsql2 = "SELECT tblHA.*  FROM tblHA  where SpaceID = '" & strid & "'"

Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strsql2, Session("ConnectionString")
  If not MyRec.eof then
  
strHazID = MyRec.fields("IDHazAssess")
strApprover1 = MyRec.fields("HA_Approver_1")
strApprover2 = MyRec.fields("HA_Approver_2")
strHAStatus = MyRec.fields("HA_Status")
strApprovalDate = MyRec.fields("HA_Approval_Date")
else
strHazID = 0
Myrec.close
end if
 
 strsql = "SELECT tblSOP.* from tblSOP where SOP_NO = '" & strid & "'"

Set MyConn = Server.CreateObject("ADODB.Recordset") 
   MyConn.Open strSQL, Session("ConnectionString")
  If not MyConn.eof then 
strSpaceID = MyConn.fields("SOP_NO")
strDescription =  MyConn.fields("sDescription")
strLocation = MyConn.fields("Location")
strComments = MyConn.fields("Comment")
strDate = MyConn.fields("sDate")
strTeam = Trim(MyConn.fields("TeamName"))

strWA = MyConn.fields("WorkArea")
strFunctional = MyConn.fields("Functional_Location")
strAsset = MyConn.fields("AssignedAsset")
strStatus = MyConn.fields("SpaceStatus")
end if
MyConn.Close

Call getdata()
set objGeneral = new ASP_CLS_General
	 if objGeneral.IsSubmit = True Then
	 strStatus = Request.form("Space_status")
	 strid = Request.querystring("id")
	 
	 
	 
	  If strStatus = "Delete This Record" then
 
 
 
 strsql =  "Delete from  tblSOP  where SID = '" & strSid & "'"

 
 Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 
			Response.redirect("CESearch.asp")
			
else

	 if isnull(Request.form("Description")) then
strDescription = ""
else
strDescription = Replace(Request.form("Description"), "'", "''") 
end IF

if isnull(Request.form("Location")) then
strLocation = ""
else
strLocation= Replace(Request.form("Location"), "'", "''") 
end if

if isnull(Request.form("Comments")) then
strComments = ""
else
strComments = Replace(Request.form("Comments"), "'", "''") 
end if

if isnull(Request.form("Functional_location")) then
strFunctional = ""
else
strFunctional= Replace(Request.form("Functional_location"), "'", "''") 
end if

strWorkArea = Request.form("Workarea")

If strWorkArea = "DRC Converting" or strWorkArea = "LDC Conv. 2nd" or strWorkArea = "LOG Material Handling" or strWorkArea = "UCTAD Conv. 3rd" or strWorkArea = "UCTAD Conv. 4th" then
strArea = "F"
end if
If strWorkArea = "DRC Manufacturing" or strWorkArea = "LDC Manufacturing" or strWorkArea = "PM Pulp Prep" or strWorkArea = "UCTAD Manufacturing" then
strArea = "M"
end if
If strWorkArea = "PM Chips/Barge" or strWorkArea = "PM Drying/Repulping" or strWorkArea = "PM Pulp Mill" then
strArea = "P"
end if
If strWorkArea = "LOG Distribution" then
strArea = "D"
end if
If strWorkArea = "SF Buildings & Grounds" or strWOrkArea = "SF Facilities Maintenance" then
strArea = "B"
end if 
If strWorkarea = "SF Engineering/Stores" then
strArea = "B"
end if 
If strWorkArea = "U/E Utilities" or strWorkarea = "U/E WWT" then
strArea = "U"
end if


 strsql =  "Update tblSOP  set  TeamName = '" & Request.form("Team") & "', WorkArea = '" & strWorkarea & "', "_
 &"  Sdescription =  '" & strDescription & "', Location = '" & strLocation & "', Spacestatus = '" & strStatus & "',"_
 &"  Comment =  '" & strComments & "', Functional_location = '" & strFunctional & "' where SOP_NO = '" & strid & "'"

 
 Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 
			
If Request.form("Non_CS") = "Yes" then 

strsql =  "Update tblSOP  set Location_type = Null where SOP_NO = '" & strid & "'"

 
 Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 


end if
end if

	 Response.redirect("Summary_details_edit.asp?id=" & strid)
	 end if 
 %><body bgcolor="#FFFFFF"><br>
 	<form name="form1" action="Summary_details_NCS_edit.asp?id=<%= strid%>"  method="post" ID="Form1"  >
 <table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#FFFFFF">
   
  <tr>
 <td align="left" height="25"><b><font face="arial">SUMMARY DETAILS </font></b>
	
</td>
 <td align="left" height="40"><INPUT TYPE="submit" value="Submit"></td><td align = right><font face="arial"><b><a href="javascript:history.go(-1)">RETURN</a></b></td></tr></table>

 
 <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#D9E1F9">
   
  <tr>

   
    <td align="center" class="auto-style1">
	<font face="Arial" size="2">Team</font></td>

   
    <td align="center" class="auto-style1">
	<font face="Arial" size="2">Work Area</font></td>

   
    <td class="auto-style1">
	<p align="center"><font face="Arial" size="2">Space #</font></td>
    <td align="center" class="auto-style1">
	<font face="Arial" size="2">Description/Name</font></td>

   
    <td align="center" class="auto-style1">
	<font face="Arial" size="2">Physical Location</font></td>

   
  </tr>
  <tr>
	
    <td  bgcolor="#FFFFFF"  >

	<p align="center">  <font face="Arial"> 
	    <% if strTeam = "LDC" then 
       strTeam = "LDC "
       end if %>
	   <select name="Team" size="1">
       <option value="">--- All ---</option>
    

       <%= objGeneral.OptionListAsString(rstTeam, "Team", "Team", strTeam) %>
     </select></font></td>

	
        <td  bgcolor="#FFFFFF"  >
	<p align="center">
		<font face="Arial">  <select name="Workarea" size="1">
       <option value="">--- All ---</option>
       <%= objGeneral.OptionListAsString(rstWA, "WorkArea", "WorkArea", strWA) %>
     </select></font></td>
	
    <td align = center bgcolor="#FFFFFF" > 
	<font face="Arial" size="2">
	<%= strSpaceID %></font></td>
    <td  bgcolor="#FFFFFF"  align = center ><font face="Arial" size="2">
	
	<span class="style4">
	
	<textarea name="Description" rows="1" cols="50" style="font-family:Arial" > <%= strDescription %></textarea></span></font></td>
	
    <td  bgcolor="#FFFFFF" align = center  >
 <font face="Arial" size="2">
 <input name="Location" size="40"  value="<%= strLocation %>" ></font></td>

	
    </table> <br>
 
 <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#D9E1F9" id="table1" align = center>
   
  <tr>
    <td align="center" width="205" height="22" class="auto-style1">
	<font face="Arial" size="2">Assigned Asset</font></td>

   
    <td align="center" width="604" height="22" class="auto-style1">
	<font face="Arial" size="2">Functional Location</font></td>

   
    <td align="center" height="22" class="auto-style1">
	<font face="Arial" size="2">Space Status</font></td>

   
    <td align="center" height="22" class="auto-style2">
	C<font size="2">hange to CS</font></td>

   
  </tr>
  <tr>
    <td  bgcolor="#FFFFFF" align = center >
	
		<font face="Arial" size="2"> <%= strAsset%>&nbsp;</font></td>
	
    <td  bgcolor="#FFFFFF" align = center  >
<font face="Arial" size="2">
<input name="Functional_location" size="74" value="<%= strFunctional %>" ></font></td>
	
	
    <td  bgcolor="#FFFFFF"  align = center >
		<font face="Arial">  <select name="Space_status" size="1">
		<option value = "">--Select---</option>
       <option <% if strStatus = "In Use" then%> selected <% end if %>>In Use</option>
       <option <% if strStatus = "Abandoned" then%> selected <% end if %>>Abandoned</option>
         <option <% if strStatus = "Not Used" then%> selected <% end if %>>Not Used</option>
           <option <% if strStatus = "Removed" then%> selected <% end if %>>Removed</option>
              <option>Delete This Record</option>

     </select></font></td>
	
	
    <td  bgcolor="#FFFFFF"  align = center >
		<font face="Arial">  <select name="Non_CS" size="1">
		<option selected="">No</option>
       <option <% if strStatus = "In Use" then%> <% end if %>>Yes</option>
     </select></font></td>
	
  </table><br>
 
<br>

 
 <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#D9E1F9" id="table5">
    <tr><td class="auto-style3">Comments</td></tr>

  
  <tr>
	
    <td  bgcolor="#FFFFFF" class="style3"  >
	<p align="center" class="style3"> <font face="Arial" size="2">
	<input name="Comments" size="118"  value="<%= strComments %>" style="width: 885px" ></font></td>
	</tr>
    </table> 

	</form>

    <%  Function GetData()
        set objEPS = new ASP_CLS_ProcedureESL
        set rstTeam = objEPS.ReadTeamList()
 	set rstWA = objEPS.ReadWorkArea()


    End Function %><!--#include file="footer.inc"-->