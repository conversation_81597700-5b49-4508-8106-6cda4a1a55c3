<html>
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->

<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 6.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>This assessment is intended to document a summary of&nbsp; hazards and 
control measures required to permit safe entry</title>
<style>
<!--
div.Section1
	{page:Section1;}
-->
</style>
</head>
<% dim strsql, MyConn, strid, strsql1, MyRec, strTimesper<PERSON><PERSON><PERSON>, strT<PERSON><PERSON><PERSON>ear, strOther<PERSON>imes, strNext_Audit_date
Dim strOtherGas1, sttOtherGas2, strOtherGas3, strcontAirMonDetTube, strHazID, strECP_Required
Dim strDescofSpace, strContents, strOtherIngulf, strIf_entry_task_1, strIf_entry_task_2, strIf_entry_task_3
Dim strSpace_labeled, strH_Unknown, strkcemployees, strcontractors, strpre_entrppurgevent, stroxygen, strso2, strChlorine_dioxide, strlel 
Dim strCO, strCH2O, strH2S, strco2, strvinylacetate, strnh3, strcl2, strcontairmon , strabove110, strbelow45
Dim  stringulfment, strchips, strstock, strwater, strpapertrim, strhemechanical, strheelectrical, strhesteam 
Dim  strHEStored_Energy, strhehydraulic, strhepneumatic, strhethermal, strHERadiation
Dim strhechemical, strhegravity, strheposnegpressure, strblankblind, stroffset, strdoubleblock, strifcleaning, strifinspection
Dim strIFRepairMaint, strifhotwork, strCleaningWOEntry, strInspectionCk, strRepairMainWOEntry, strHotworkWOEntry
Dim strTask_1_wo_entry, strTask_2_wo_entry, strTask_3_wo_entry, strECP_energy, strIllumIssues, strLowVolt, strGFIRequired, strglassesWSS, strimpactGOG
Dim strChemicalGOG, strDustGOG, strOtherGogglesChk, strOtherGoggles, strfaceshield, strWeldingHelmet, strotherEyeProt
Dim strregularshoes, strChemicalBoots, strOtherShoes, strPPESpReq, strAirlineResp, strdustResp, strescapeResp
Dim strescapeRespWAttendant, strhalfResp, strfullResp,strPAPResp
Dim strScottAirPack, strOtherResp, strhearing, strHardHat, strcottonleather, strhotGlove
Dim strchemicalGlove, strchemicalRubber, strchemicalNitrile, strCutResistant, strOtherGlove
Dim strpapercoveralls, strchemicalCloth, strnonsparking, strcoolvest, strkneepads, strOtherClothing, strAtt_call
Dim strAttendantSOS, strEntrantSOS, strHAStatus
dim strexhazNearbyProcessIssue,  strPSMArea, strProcessControl, strExt, strNextel, strSpecialAlarm, strListAlarm



	

strid = Request.querystring("id")

strsql = "SELECT tblHA.*, tblSOP.LOCATION, tblSOP.SDescription FROM tblHA INNER JOIN tblSOP ON tblHA.SpaceID = tblSOP.SOP_NO "_
  &" where SpaceID = '" & strid & "'"
Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
 
strHAStatus = MyConn.fields("HA_Status")		
strDescofspace = MyCOnn.fields("DescofSpace")
strHazID = MyConn.fields("IDHazAssess")




MyConn.close


 %>

 <body bgcolor="#FFFFFF">

	<p align="right"><b><font face="Arial" size="2"><a href="CESearch.asp">RETURN</a></font></b></p>
	<p align = center><b><font face="Arial">Reports for Space:&nbsp;<%= strid %></font></b><br><br>
 &nbsp;</p>
  <table border="1" cellpadding="0" align = center cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="50%" id="table1">
  <tr>
    <td  height="19" bgcolor="#FFFFDD" width="227">
	<p align="center"><font face="Arial" size="2">Section</font></td>
    <td  height="19" bgcolor="#FFFFDD" align="center">
	<font face="arial" size="2">View Report</font></td>
   
  </tr>
  <tr>
    <td align = left bgcolor="#FFFFFF" width="227" > <font face="Arial" size="2">Section 1 
	(Hazard Assessment)</font></td>
    <td bgcolor="#FFFFFF" height="30"  >
	<p align="center"><font face="Arial" size="2">&nbsp;<a href="rptSection1.asp?id=<%= strid%>"><%= strDescofSpace%></a></font><br></td>
  
  </tr>
    <tr>
    <td align = left bgcolor="#FFFFFF" width="227" > <font face="Arial" size="2">Section 2
	(Compartments)</font></td>
    <td bgcolor="#FFFFFF" align = center ><br>
    
        <% Dim strsqlC, MyConnC
    strsqlC = "SELECT tblCompartment.* from tblCompartment where Haz_ID = " & strHazID 
    Set MyConnC = Server.CreateObject("ADODB.Recordset") 
   MyConnC.Open strSQLC, Session("ConnectionString")
  If not MyConnC.eof then 
      Dim ii
       ii = 0
       while not MyConnC.Eof %>
       <font face = arial size = 2><a href="rptSection2.asp?cid=<%= MyConnc.fields("ID")%>&id=<%= strid%>">
<%= MyConnC.fields("CompartName")%></a>

 <br>
    <%
       ii = ii + 1
       MyConnC.MoveNext
     Wend
     End if
     MyConnc.close
    %>   
    
    &nbsp;</td>
  
  </tr>
    <tr>
    <td align = left bgcolor="#FFFFFF" width="227" > <font face="Arial" size="2">Section 3 
	(Portals)</font></td>
    <td bgcolor="#FFFFFF" align = center ><font face="Arial" size="2" >
           <% 
    strsqlC = "SELECT tblPortals.* from tblPortals where Haz_ID = " & strHazID 
    Set MyConnC = Server.CreateObject("ADODB.Recordset") 
   MyConnC.Open strSQLC, Session("ConnectionString")
  If not MyConnC.eof then 
      
       ii = 0
       while not MyConnC.Eof %>
       <font face = arial size = 2><a href="rptSection3.asp?pid=<%= MyConnc.fields("ID")%>">
<%= MyConnC.fields("NameLocPortal")%></a>

 <br>
    <%
       ii = ii + 1
       MyConnC.MoveNext
     Wend
     End if
     MyConnc.close
    %>   
    
    &nbsp;</td>
	</td>
  
  </tr>
</table>
</form>

<!--#include file="footer.inc"-->