 

<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Rail Cars Received</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->
 

<%  	strBeg  = request.form("Beg_Date")
	strEnd = request.form("End_Date")

 

   set objGeneral = new ASP_CLS_General

 
%>

<script language="javascript">
function openWindow(pURL)
{
	myWindow = window.open(pURL, "myLittleCalendar", 'toolbar=no,location=no,directories=no,status=no,menubar=no,scrollbars=no,resizable=no,width=170,height=270');
}


</script>


<style type="text/css">
.style3 {
	font-weight: bold;
	border-width: 1px;
}
.style4 {
	font-family: Arial;
}
.style5 {
	background-color: #F0F8FF;
}
.style6 {
	font-weight: bold;
	border-width: 1px;
	background-color: #F0F8FF;
}
.style7 {
	border: 1px solid #C0C0C0;
}
.auto-style1 {
	background-color: #E7EBFE;
}
.auto-style2 {
	font-weight: bold;
	border-width: 1px;
	background-color: #E7EBFE;
}
.auto-style3 {
	border: 1px solid #000000;
}
.auto-style4 {
	font-family: Arial;
	text-align: center;
}
</style>
</head>


<form name="form1" action="Rail_Cars_Received.asp" method="post">
 
<TABLE borderColor=white cellSpacing=0 cellPadding=0  width = 100% border=1 >  
  <tr>
  <td class="auto-style4"><strong>Recoved Paper Rail Cars Received</strong></td>
 </tr>
</table>

 

<TABLE cellSpacing=0 cellPadding=0 align = CENTER id="table2" class="auto-style3" style="width: 75%">  

  <TR>
   
	<TD class="auto-style2" ><font face="Arial" size="2">Beg Date:</font></TD>
<TD class="auto-style2" ><font face="Arial"><input name="Beg_Date" size="10" maxlength="10" value="<%=strBeg%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633">
<font face="Arial" size="2">	&nbsp;<strong> <font face="Arial">
<INPUT TYPE="image"  src="calendar.gif" width="70" height="31" VALUE="..." STYLE="font-family: MS Sans Serif,Arial; color: #885B3B; " onclick="openWindow('mlcpopup.asp?elt=Beg_Date'); return false;" class="style4"></font></strong></TD>



     <td class="auto-style2"><font size="2" face="Arial">&nbsp; End Date</font></td>
   <TD class="auto-style2" ><font face="Arial"><input name="End_Date" size="10" maxlength="10" value="<%=strEnd%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633">&nbsp;&nbsp;&nbsp;
		<INPUT TYPE="image"  src="calendar.gif" width="70" height="31" VALUE="..." STYLE="font-family: MS Sans Serif,Arial; color: #885B3B; " onclick="openWindow('mlcpopup.asp?elt=End_Date'); return false;" class="style4"></td>

 

  
    <TD  align="right" class="auto-style1"><input type="submit" value="Search"></TD>
 
</TR>


  </TABLE></form>


  <% if objGeneral.IsSubmit() Then 
  	strBeg  = request.form("Beg_Date")
	strEnd = request.form("End_Date")

%>
   <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>


 <td bgcolor = white align="center" >&nbsp;</font></td>

     <td bgcolor = white  align="right" ><font face="Arial" size = 2> <b>
 <a href="Rail_Cars_Received_Excel.asp?b=<%= strBeg %>&e=<%= strEnd %>">Excel</a>  &nbsp;&nbsp;&nbsp;
        </b></font></td>   </tr></table>
    	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1 align = center>  
	 <tr class="tableheader">
	<td  align = left style="height: 12px">     <font face="Arial" size="1">	CID</font></td>
				<td  align = left style="height: 12px">     <font face="Arial" size="1">	Rail Car</font></td>
		<td  align = left style="height: 12px">     <font face="Arial" size="1">Release Number</font></b></td>
	
		<td  align = left style="height: 12px">     <font face="Arial" size="1">Species</font></td>
		<td  align = left style="height: 12px">     <font face="Arial" size="1"> Date Received</td>
	 
		<td  align = left style="height: 12px">     <font face="Arial" size="1">Vendor</font></td>
		<td style="height: 12px"  ><b><font face="Arial" size="1">Bales</font></b></td>
			<td  align = left style="height: 12px">     <font face="Arial" size="1">Tons</font></b></td>
 
		
	</tr>

    <%     Dim ii
       ii = 0
    
    strsql = "SELECT  OID,  CID,  Trailer,  Species,  Date_received,  Release_Nbr, Vendor,  Bales_VF,  Tons_received FROM tblCars "_
&" WHERE (Species = 'KCOP' or Species = 'OF' or Species = 'PMX' or Species = 'MXP' or Species = 'OCC' or Species = 'SHRED' or Species = 'HBX' or Species = 'ROCC')"_
&"  AND Date_received >= '" & strBeg  & "' and Date_received <= '" & strEnd & "' and Carrier = 'RAIL' ORDER BY  Species,  Date_received"	
	   Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    IF not MyRec.eof  then
  

    while not MyRec.eof 
  
  if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>

	<td  ><font size="1" face="Arial"><%= MyRec.fields("CID")%> </font></td> 

	<td  ><font size="1" face="Arial"><%= MyRec.fields("Trailer")%>&nbsp;</font></td>
		<td  ><font size="1" face="Arial"><%= MyRec.fields("Release_Nbr") %>&nbsp;</font></td>
	<td  ><font size="1" face="Arial"><%= MyRec.fields("Species")%>&nbsp;</font></td>
	<td  ><font size="1" face="Arial"><%= MyRec.fields("Date_received")%>&nbsp;</font></td>
	<td  ><font size="1" face="Arial"><%= MyRec.fields("Vendor")%>&nbsp;</font></td>
	<td  ><font size="1" face="Arial"><%= MyRec.fields("Bales_VF")%>&nbsp;</font></td>
	<td  ><font size="1" face="Arial"><%= MyRec.fields("Tons_received")%> </font></td>
 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
     end if
     end if ' is submit
    %>
</table>

<!--#include file="Fiberfooter.inc"-->