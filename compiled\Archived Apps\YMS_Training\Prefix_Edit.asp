<script>window.history.go(1);</script>
<html>
<!--#include file="classes/asp_cls_headerOYM.asp"-->
<!--#include file="classes/asp_cls_SessionStringOYM.asp"-->
 

<!--#include file="classes/asp_cls_DataAccessOYM.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 14.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Edit Last Number for BOL # Prefix</title>

</head>
<%
    Dim strSQL, MyConn, strID 
    Dim strNumber, objGeneral


    
    
    	
strid = Request.querystring("id")
    

 	set objGeneral = new ASP_CLS_General
 	
 	strsql = "Select Last_number, Load_prefix from tblPrefix where PID = " & strid & ""
 	
   	 Set MyConn = Server.CreateObject("ADODB.Recordset")
   	MyConn.Open strSQL, Session("ConnectionString")
 

strPrefix = MyConn.fields("Load_prefix")

strNumber = MyConn.fields("Last_number")
  
	MyConn.close
if objGeneral.IsSubmit() Then


	Call SaveData() 



end if

	
%>



<body>

<br>
<table width = 100%><tr><td align = center width = 67%>
	<b><font face="Arial">Enter Last Number for BOL Prefix</font></b></td><td align = right width = 33%><b>
	<font face = Arial size = 2><a href="Prefix.asp">RETURN</a></font></b></td></tr></table>




<form name="form1" action="Prefix_edit.asp?id=<%= strid%>" method="post">


<table border="1" cellpadding="0" cellspacing="0" width = 30% style="border-collapse: collapse" bordercolor="000000" align = center>
  
    <tr>    
    
	<td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" >
 
     <font face="Arial" size="2">PREFIX</font></td>
    
	<td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" >
 
     
 
    <font face="Arial" size="2">Last Number</font></td>
    
 </tr>
    <td  bgcolor="white"  bordercolor="#CCCCFF" height="22">
	
		<font face="Arial" size="2"><%= strPrefix %></font></td>
    <td  bgcolor="white"  bordercolor="#CCCCFF" height="22">
	
		<font face="Arial" size="2">

      <input name="Last_number" size="10" value = "<%= strNumber %>" ></font></td>



 
  </tr>
  
  
      <tr>    <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" colspan="2" >
     &nbsp;</td>
 

 </tr>
    <td  bgcolor="#F0F0FF" bordercolor="#CCCCFF" height="22" colspan="2">
	<p align="center"><font face="Arial" size="2">  

	     &nbsp;</font><font face="Arial"><Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></td>
	


  </tr>

</table>
<div align="center">
<table border="0" bordercolor="#FFFFFF" width="50%" bgcolor="#FFFFFF" style="border-collapse: collapse" cellpadding="0">

  <tr>
    <td bgcolor="#FFFFFF" width="215">&nbsp;</td>

    <td align = left bgcolor="#FFFFFF">&nbsp;</td>
  </tr>
</table>

</div>

</form>

<p>&nbsp;</p>

</body>
<% 

Function SaveData()

strid = request.querystring("id")

   
   strNumber = Request.form("Last_number")


strsql = "Update tblPrefix set Last_number = " & strNumber & " where PID = " & strid & ""
          
    

	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
	Response.redirect ("Prefix.asp")
	
End Function %>

</html>
<!--#include file="OYMfooter.inc"-->