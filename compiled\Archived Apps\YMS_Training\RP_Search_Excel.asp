
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">
<TITLE>Recovered Paper Orders</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

<!--#include file="classes/asp_cls_General.asp"-->

<%  
	Dim strsQL, rstEquip

   	Dim objEquipSearch, rstTotals, strCount, strDed, strNet, strTons, rstTotalReceived, strTR, objNew, rstSpecies
  	Dim  strVendor, strBegDate, strEndDate, strSpecies, strMonth, strDateReceived, strDateAdded, rstMonth, strShipWeek
 	Dim  rstVendor
  	Dim objGeneral, strPO, gcount
  	call LoadSearchResults()
  	
Response.ContentType = "application/vnd.ms-excel"

%>
   <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% border=1 align = center>

    <td  align="center"><font face="Arial" size = 2>Ordered:<br>&nbsp;<%=strCount%></font></td>
    <td  align="center"><font face="Arial" size = 2>Received:<br>&nbsp;<%=strTR%></font></td>
    
        <td  align="center"><font face="Arial" size = 2>Fill Rate:<br>&nbsp;<% if strCount = 0 then %> 0%&nbsp;<% else %><%=round((strTR/strCount)*100,1)%>%<% end if %></font></td>
    <td  align="center"><font face="Arial" size = 2>Tons Received:<br>&nbsp;<% if isnull(strTons) then %>0 <% else %> <%=round(strTons,3)%><% end if %></font></td>
        <td  align="center"><font face="Arial" size = 2>Deduction:<br>&nbsp;<% if isnull(strDed) then %> 0 <% else %> <%= round(strDed,3) %><% end if %></font></td>
        <td  align="center"><font face="Arial" size = 2>Net:<br><% if isnull(strNet) then %>0<% else %>&nbsp;<%=round(strNet,3)%><% end if %></font></td>
    
    </tr>
    <tr><td colspan="13" bgcolor="white" align="right"><%=strPageNav%>&nbsp;</td></tr>
      <tr class="tableheader">
 	<td  align="center"><font face="Arial" size = 1>Species</font></td>

	<td  align="center"><font face="Arial" size = 1>PO</font></td>
	<td  align="center"><font face="Arial" size = 1>Trailer</font></td>
		<td  align="center"><font face="Arial" size = 1>Carrier</font></td>
	<td  align="center"><font face="Arial" size = 1>Date<br> Received</font></td>
	<td  align="center"><font face="Arial" size = 1>Date<br> Unloaded</font></td>
	<td  align="center"><font face="Arial" size = 1>Release</font></td>
	<td  align="center"><font face="Arial" size = 1>Vendor</font></td>
	<td  align="center"><font face="Arial" size = 1>Generator</font></td>
	
	<td  align="center"><font face="Arial" size = 1>Month</font></td>
	<td  align="center"><font face="Arial" size = 1>Tons</font></td>
	<td  align="center"><font face="Arial" size = 1>Deduction</font></td>
	<td  align="center"><font face="Arial" size = 1>Net</font></td>

      
  	<% 
      Dim ii
       ii = 0
       while not rstEquip.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr class=tablecolor1>
    <% else %>
       <tr class=tablecolor2>
    <% end if %>

<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Species")%>&nbsp;</td>


<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("PO")%>&nbsp;</td>

<td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("Trailer")%>&nbsp;</td>
<td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("Carrier")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("date_received")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("date_unloaded")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Release")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Vendor")%>&nbsp;</td>
<% if len(rstEquip.fields("Generator")) > 0 then %>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Generator")%>&nbsp;</td>
<% else %>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Generator")%>&nbsp;</td>
<% end if %>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Import_month")%>&nbsp;</td>
	<td  align="right"><font face="Arial" size = 1><%= rstEquip.fields("Tons_received")%>&nbsp;</font></td>
	<% if len(rstEquip.fields("Deduction")) > 0 then %>
	<td  align="right"><font face="Arial" size = 1><%= round(rstEquip.fields("Deduction"),3) %>&nbsp;</font></td>
	
	<% else%>
	<td  align="right"><font face="Arial" size = 1><%= rstEquip.fields("Deduction") %>&nbsp;</font></td>
	<% end if %>
<% if rstEquip.fields("Net") > 0 then %>
	<td  align="right"><font face="Arial" size = 1><%= round(rstEquip.fields("Net"),3)%>&nbsp;</font></td>
<% else %>
	<td  align="right"><font face="Arial" size = 1>0&nbsp;</font></td>
<% end if %>

  
   </tr>
    <% 
       ii = ii + 1
       rstEquip.MoveNext
     Wend
    %>
   </table>
 

   <% Function LoadSearchResults()
      Dim objEquipSearch, objTotals

	strPO = request.querystring("p")
	strVendor = request.querystring("v")
	strSpecies = request.querystring("s")
	strMonth = request.querystring("m")


      set objEquipSearch = new ASP_Cls_Fiber
    
  
  strsql = "SELECT tblOrder.oid, tblCars.Carrier, tblcars.date_unloaded, tblOrder.species, tblCars.Generator, tblCars.trailer, "_
  &"  tblOrder.vendor, tblorder.sap_nbr, tblOrder.PO, req_ship_week, release, import_month, tblCars.Date_received, "_
  &"  tblCars.Tons_received, tblcars.net, tblCars.Deduction, tblOrder.Generator "_
  &" FROM tblOrder LEFT JOIN tblCars ON tblOrder.Release = tblCars.Release_nbr  where tblOrder.OID > 0 "
  
  if len(strVendor) > 0 then 
  strsql = strsql & " and tblOrder.vendor = '" & strVendor & "'"
  end if
  
  if len(strMonth) > 0 then
    strsql = strsql & " and tblOrder.Import_month = '" & strMonth & "'"
    end if
  
  if len(strPO) > 0 then
   strsql = strsql & " and tblOrder.PO = '" & strPO & "'"
    end if
 if len(strspecies) > 0 then
   strsql = strsql & " and tblOrder.Species = '" & strSpecies & "'"
    end if




strsql = strsql & " ORDER BY tblOrder.Import_month DESC, tblOrder.Req_ship_week"

    Set rstEquip = Server.CreateObject("ADODB.Recordset")
    rstEquip.Open strSQL, Session("ConnectionString")

' set rstEquip = objEquipSearch.RPSearch(strmonth, intPageNumber, strSpecies, strPO, strVendor)
set rstTotals = objEquipSearch.RPTotals(strmonth, strSpecies, strPO, strVendor)
set rstTotalReceived = objEquipSearch.RPTotalReceived(strmonth, strSpecies, strPO, strVendor)

     if  not rstTotals.Eof Then
      strCount = rstTotals.fields("CountofOID").value
      strDed = rstTotals.fields("SumofDeduction").value
      strNet = rstTotals.fields("SumofNet").value
      strTons = rstTotals.fields("SumofTons_received").value
      
      end if 


        if ( not rstTotalReceived.Eof) Then
     strTR = rstTotalReceived.fields("countofoid").value
     end if
    End Function
 %>