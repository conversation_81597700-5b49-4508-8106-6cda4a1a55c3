<html>
<script>window.history.go(1);</script>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionPolaris.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 14.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Enter Non-Fiber Trailer Receipt</title>
</head>
<%
    Dim strSQL, MyRec, strID, strPO, MyConn, objMOC, rstFiber, strPS, strAT, strUOM, strNewID   
    Dim strTrailer, strCarrier
    Dim strPONbr
    Dim strRECNbr, gVendorName, strPID

    Dim strTonsReceived
    Dim strDateReceived

    Dim strOther, strR, strsql3, gDescription, gPO, gSAP_Nbr, gVendor, gOID, strNet, strMultiLoad

    strPO  = Trim(Request.QueryString("id")) 
	strR = left(strPO,1)
	
	strPID = request.querystring("p")

 	set objGeneral = new ASP_CLS_General

  

if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close

 	strTrailer = Request.form("Trailer")
	strOther = Replace(Request.form("Other_Comments"), "'", "''") 
	strDateReceived = Request.form("Date_received")
	strRECNbr = Request.form("Rec_Number")
	strCarrier = Request.form("Carrier")
	strUOM = Request.form("UOM")


	strTonsReceived = Request.form("Tons_received")


	
	If isnull(strTrailer) or strTrailer = ""  or isnull(strCarrier) or strCarrier = ""  or isnull(strTonsReceived) or strTonsReceived = "" or isnull(strDateReceived) or strDateReceived = "" then
	
	Response.write("<font face = arial size = 4><b>Something was wrong with the Trailer, Carrier, Quantity or Date</b></font>")
	else
	Call SaveData() 
	Response.redirect ("SelectWeyInbound.asp")
	end if
	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to save this information.</font></br>")
	MyRec.close
	end if

  
ELSE
  Call GetData()
	strsql = "SELECT tblSAPOpenPO.* FROM tblSAPOpenPO WHERE OID = '" & strPO & "'"

   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
	
	gDescription = MyRec.fields("Description")
	gSap_Nbr = MyRec.fields("Material")
	gVendor = MyRec.fields("Vendor")
	
	IF gvendor = "WEYERHAEUSER COMPANY" then
	strCarrier = "BWIF"
	end if


	If not isnull(MyRec.fields("Loaded_qty")) then
	strTonsReceived = MyRec.fields("Loaded_qty")
	strUOM = MyRec.fields("Loaded_uom")
	else
	strTonsReceived = MyRec.fields("Quantity")
	strUOM = MyRec.fields("OUn")
	end if
	

	strPONbr = MyRec.fields("Purchdoc") & "-" & Myrec.fields("Item")
	gOID = Myrec.fields("OID")
	If MyRec.fields("Trailer") <> "Unknown" then
	strTrailer  = MyRec.fields("Trailer")
	else
	strTrailer = ""
	End if
	
	strMultiload = MyRec.fields("Multi_load")


	MyRec.close
	
	if len(gSAP_Nbr) > 0 then
		strsql = "SELECT tblReference.* FROM tblReference WHERE SAP = " & gSAP_Nbr & ""
	
		Set MyRec = Server.CreateObject("ADODB.Recordset")
   		 MyRec.Open strSQL, Session("ConnectionPolaris")
   	 	If not MyRec.eof then
		strPS = MyRec.fields("Product System")
		strAT = MyRec.fields("Asset Team")
		end if
		MyRec.close
	end if
	strDateReceived = formatdatetime(Now(),2)
	

end if
%>



<body>

<% if Request.querystring("n") = "T" then %>
<p align = center><font face = arial size = 3 color = red><b>You must enter the Carrier, Trailer Number, Quantity and Unit of Measure.  Please re-enter your information.</p></b></font>
<% else %>
&nbsp;
<% end if %>
<table width = 100%><tr><td width = 33%>
<font face="Arial" size="2">

</td><td align = center width = 34%><b><font face="Arial" size="4">
Enter Trailer Receipt </font> </b></td>
<td align = center><font face="Arial" size="2"><b></td>
<td align = right width = 10%><a href="SelectNFRelease.asp"><b><font face = arial size = 2>RETURN</a></font></b></td></tr></table>




<form name="form1" action="EnterNFReceiptMT.asp?id=<%=strPO%>&p=<%= strPID%>" method="post">
<div align="center">
<table border="1" cellspacing="0" bordercolor="#808080" width="45%" bgcolor="#FFFFE1" style="border-collapse: collapse" cellpadding="0" bordercolorlight="#C0C0C0">
<tr>
    <td bgcolor="#FFFFDF" width="19%">&nbsp;</td>

    <td  bgcolor="#FFFFDF" colspan="2">&nbsp;</td>
  </tr>
    <tr>
    <td  align = left bgcolor="#FFFFDF" width="19%" >
   <b>
   <font face="Arial" size="2">PO Number:&nbsp;</font></b></td>
<td  align = left bgcolor="#FFFFDF" width="48%">

     <font face="Arial" size="2">

      <%= strPONbr%><b>
		</b></font>

  </td>
<td  align = left bgcolor="#FFFFDF" width="32%">

		<font face="Arial"><font size="2"><b>Check to Print Receipt:</b></font><input type="checkbox" name="Print_receipt" value="ON" checked></td></tr>
  <tr>
    <td  align = left bgcolor="#FFFFDF" width="19%" >
   <b><font face="Arial" size="2">Vendor Receipt #:</font></b></td>
<td  align = left bgcolor="#FFFFDF" colspan="2">

      <font face="Arial">

      <input name="REC_Number" size="24" value = "<%= strRECNbr%>" style="font-weight: 700"></font></td></tr>
  <tr>

      <td  bgcolor="#FFFFDF" align = left width="19%">
  <font face="Arial" size="2"><b>Select Carrier:&nbsp;&nbsp;</b></font></td>
<td  align = left bgcolor="#FFFFDF" colspan="2">

      <font face="Arial">   
	<select name="Carrier" style="font-weight: 700">
 	<option value="" selected>  Select Carrier (Required)</option>
      <%= objGeneral.OptionListAsString(rstFiber, "Carrier", "CDesc", strCarrier) %>
     </select></font></td></tr>
<tr>
    <td  bgcolor="#FFFFDF" align="left" width="19%">  
	<p>  <b><font face="Arial" size="2">Trailer</font></b><font face="Arial" size="2"><b>:&nbsp;&nbsp;</b></font></td>
    <td bgcolor="#FFFFDF" colspan="2">   <font face="Arial">   

      <input name="Trailer" size="24" value = "<%= strTrailer%>" style="font-weight: 700"></font>&nbsp;&nbsp;
      
      <% if strMultiload = "x" or strMultiload = "X" then %> <font size = 2 face = arial><b>Multi-Item Load</b></font>
      <% end if %>
      </td>
  </tr>

       <tr>  <td  bgcolor="#FFFFDF" align = left width="19%"> <font face="Arial" size="2">
			<b>Quantity:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFDF" colspan="2">    <font face="Arial">  
<input name="Tons_Received" size="15" value = "<%= strTonsReceived%>" style="font-weight: 700"> </td></tr>

<tr><td  bgcolor="#FFFFDF" align="left" width="19%"><font face="Arial" size="2"><b>Unit of 
	Measure:&nbsp;</b></font></td>
    <td  bgcolor="#FFFFDF" colspan="2">    <font face="Arial">  
	<input name="UOM" size="15" value = "<%= strUOM%>" style="font-weight: 700"></td>
  </tr>
       <tr><td  align = left bgcolor="#FFFFDF" width="19%" >&nbsp;</td>
<td align = left bgcolor="#FFFFDF" colspan="2"> &nbsp;</td></tr>
       <tr><td  align = left bgcolor="#FFFFDF" width="19%" ><font face="Arial" size="2"><b>Date Received:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFDF" colspan="2"> <font face="Arial"> 
<input name="Date_Received" size="15" value = "<%= strDateReceived%>" style="font-weight: 700"></font></td></tr>
              <tr>
          <td  align = left bgcolor="#FFFFDF" width="19%" >
   <font face="Arial" size="2"><b>Vendor:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFDF" colspan="2">
     <font face="Arial" size="2"> <%= gVendor%>&nbsp;</TD></tr>
   
              <tr>
          <td  align = left bgcolor="#FFFFDF" width="19%" >
   <font face="Arial" size="2"><b>Commodity:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFDF" colspan="2"><font face="Arial" size="2"><%= gDescription%>
      &nbsp;</TD></tr>
   
              <tr>
          <td  align = left bgcolor="#FFFFDF" width="19%" >
   <font face="Arial" size="2"><b>SAP #:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFDF"><% if len(gSAP_Nbr) > 2 then %>
   <font face="Arial" size="2">   <%= gSAP_Nbr%>&nbsp;
   <% else %>
    <font face="Arial" size="3" color = red> <b>THERE IS SOMETHING WRONG WITH THE PO#, PLEASE EDIT</b>&nbsp;</font>
   </font> <b><font color="#0000FF" face="Arial">
<a href="KDF_Edit_MT.asp?id=<%=gOID%>&cid=<%= strpid%>">HERE</a></font></b><font color="#0000FF">
<% end if %></font></TD></tr>
   
              <tr>
          <td  align = left bgcolor="#FFFFDF" width="19%" >
   <b><font face="Arial" size="2">Product System</font></b><font face="Arial" size="2"><b>:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFDF" colspan="2">
   <font face="Arial" size="2">  <%= strPS%> &nbsp;</TD></tr>
   
              <tr>
          <td  align = left bgcolor="#FFFFDF" width="19%" >
   <font face="Arial" size="2"><b>Asset Team:</b></font></td>
<td align = left bgcolor="#FFFFDF" colspan="2">
     <font face="Arial" size="2">  <%= strAT%> &nbsp;</TD></tr>
   
         <tr>
          <td  align = left bgcolor="#FFFFDF" width="19%" >
  <font face="Arial" size="2"><b>Comments/Other:&nbsp;</b></font></td >
   <td align = left bgcolor="#FFFFDF" colspan="2">   <font face="Arial">   
	<input name="Other_Comments" size="60" value = "<%= strOther%>" style="font-weight: 700"></font></td></tr>
<tr>
    <td  bgcolor="#FFFFDF" width="19%">&nbsp;</td>

    <td bgcolor="#FFFFDF" colspan="2">&nbsp;</td>
  </tr>

  <tr>
    <td bgcolor="#FFFFDF" width="19%">&nbsp;</td>

    <td align = left bgcolor="#FFFFDF" colspan="2"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" >&nbsp; 
	</font></td>
  </tr>
</table>

</div>

</form>
</body>
<% Function GetData()
  set objMOC = new ASP_CLS_FIBER
        set rstFiber = objMOC.FiberCarrier()
End Function

Function SaveData()


    strPO  = Trim(Request.QueryString("id")) 

		strsql = "SELECT tblSAPOpenPO.* FROM tblSAPOpenPO WHERE OID = '" & strPO & "'"

   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
	
	if len(MyRec.fields("Stype")) > 0 then
	gDescription = MyRec.fields("Stype")
	else 
	gDescription = ""
	end if
	
	If len(MyRec.fields("Material")) > 0 then
	gSap_Nbr = MyRec.fields("Material")
	else
	gSAP_Nbr = ""
	end if
	
	If len(Myrec.fields("Vendor")) > 0 then
	gVendor = MyRec.fields("Vendor")
	else
	gVendor = ""
	end if
	
	If len(Myrec.fields("Vendor_name")) > 0 then
	gVendorName = MyRec.fields("Vendor_Name")
	else
	gVendorName = ""
	end if

	If len(MyRec.fields("Purchdoc")) > 0 then
	strPONbr = MyRec.fields("Purchdoc") 
	else
	strPONbr = ""
	end if
	
	gOID = Myrec.fields("OID")


	MyRec.close

		strsql = "SELECT tblReference.* FROM tblReference WHERE SAP = " & gSAP_Nbr & ""
	
		Set MyRec = Server.CreateObject("ADODB.Recordset")
   		 MyRec.Open strSQL, Session("ConnectionPolaris")
   	 If not MyRec.eof then
			strPS = MyRec.fields("Product System")
			strAT = MyRec.fields("Asset Team")
			else
			strPS = ""
			strAT = ""
			
	end if
	MyRec.close
	
	strDateReceived = Request.form("Date_Received")
	
	strpid = request.querystring("p")
	
	Dim strRightNow
	strRightnow = Now()
	
	strsql =  "INSERT INTO tblCarsMT ( PID, Carrier,  Grade, SAP_Nbr, Vendor, PO, Date_received,  Location, NFID,  Trailer, Other_Comments, REC_Number, "_
	&" Tons_Received, Entry_Time, Entry_BID, Entry_Page, Asset_team, PS, Species, UOM) "_
	&" SELECT " & strpid & ", '" & strCarrier & "',  'NF', '" & gSAP_Nbr & "', '" & gVendor & "', '" & strPONbr & "', '" & strDateReceived & "',   'YARD', " & gOID & ",   "_
	&" '" & strTrailer & "', '" & strOther & "', '" & strRecNbr & "', " & strTonsReceived & ",  '" & strRightNow & "', '" & Session("EmployeeID") & "', 'EnterNFReceiptMT', "_
	&" '" & strAT & "', '" & strPS & "', '" & gDescription & "', '" & strUOM & "'"
	
	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
	strsql = "Update tblSAPOpenPO set Status = 'R', Report_location = 'B', Rec_date = '" & strdatereceived & "' where OID = " & gOID & ""
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
		
	
			
		strsql = "SELECT tblSAPOpenPO.OID FROM tblSAPOpenPO "_
&" WHERE tblSapOpenPO.Trailer = '" & strTrailer & "' and (tblSapOpenPO.Multi_load = 'x' or tblSAPOpenPO.Multi_load = 'X' ) and tblSAPOpenPO.Status is null"	
		 Set MyConn = Server.CreateObject("ADODB.Recordset")
   		MyConn.Open strSQL, Session("ConnectionString")
   		If not MyConn.eof then
   		strNewID = MyConn.fields("OID")
   		Myconn.close
   		Response.redirect("EnterNFReceiptMT.asp?id=" & strnewid & "&p=" & strpid)
   	
   		else	
			
	MyConn.close		
Response.redirect("KDF_Receipt.asp?id=" & strpid)
 end if 
End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->