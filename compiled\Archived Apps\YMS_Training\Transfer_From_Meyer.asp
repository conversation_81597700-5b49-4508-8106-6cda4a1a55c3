<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Truck Load Transfer from Meyer Building</title>
<style type="text/css">
.style3 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
.style4 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	text-align: right;
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
}
.style5 {
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
}
.style6 {
	font-weight: bold;
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
}
.style7 {
	color: #000000;
}
.auto-style1 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.auto-style2 {
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
	text-align: center;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID, objMOC, rstFiber, strCarrier, strSAP, objEPS, strFactor, strBrokeDescription
      
    Dim strTrailer, strSpecies, strESpecies, rstSpecies
    Dim strReleaseNbr
    Dim strRECNbr
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strGenerator
    Dim strGenCity
    Dim strGenState
    Dim strOther, strR, strsql3, strTransTrailer, strGrade

       strId  = Trim(Request.QueryString("id")) 


 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then

	If Request.form("Species") = "" then
		Response.write("<font face=arial size=4 color=red>You must select the Species</font>")
			Call getdata()
	elseIf  Request.form("Species") = "BROKE" and  Request.form("Broke_Description") = "" then

		Response.write("<font face=arial size=4 color=red>You must select the Type of Broke for BROKE loads</font>")
			
		Call getdata()

'	elseif request.form("Species") = "BROKE" AND request.form("Tons") < .4 then
		
		'Response.write("<font face=arial size=4 color=red>You must enter the Tons or Pounds for BROKE loads</font>")
	
		
		' Call getdata()
	else


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
 
		Call SaveData() 

	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to Transfer this load.</font></br>")
	MyRec.close
	end if
end if
  
ELSE


Call getdata()
	end if

%>



<body><br>
<table width = 100%><tr><td width = 33%></td><td align = center width = 34%><b><font face="Arial" size="4">
Transfer Load from Meyer Building</font></b></td><td align = right width = 33%>&nbsp;</td></tr></table>



<form name="form1" action="Transfer_From_Meyer.asp" method="post">
<div align="center">
<table cellspacing="0" bordercolor="#C0C0C0" bgcolor="#FFFFE8" style="width: 75%;" cellpadding="0" class="style3">
<tr>
    <td class="style5" colspan="3">&nbsp;</td>

  </tr>


      <td align = right style="width: 361px" class="style6">
  <font face="Arial" size="2">Transfer Trailer Number:&nbsp;</font></td>
<td  align = left class="style5" colspan="2">

      <input type="text" name="Trans_Trailer" size="15" value = "<%= strTransTrailer%>" tabindex="1"></td></tr>
      
       <tr><td class="style5" colspan="3">&nbsp;</td>
  </tr>
<tr>
    <td style="width: 361px" class="style6">  
	<p align="right">  <font face="Arial" size="2">Select Carrier:&nbsp;</font></td>
    <td class="style5" colspan="2">   <select name="Carrier" tabindex="2">
 	<option value="BWIF" selected>BWIF</option>
      <%= objGeneral.OptionListAsString(rstFiber, "Carrier", "CDesc", strCarrier) %>
     </select></td>
  </tr>
        <tr>
    <td class="style5" colspan="3">&nbsp;</td>

  </tr>
  <tr>
    <td class="style6" style="width: 361px">  
	<p align="right">  <font face="Arial" size="2">Select Species:&nbsp;</font></td>
    <td class="style5" colspan="2">  
	<select name="Species" size="1" tabindex="3" >
 <option value="">--Select --</option>

       <%= objGeneral.OptionListAsString(rstSpecies, "Fiber_species", "Fiber_species", strSpecies) %>
     </select>
&nbsp;<b><font size="2" face = arial>&nbsp;(Species)<br><br>
	<select name="Broke_Description" size="1" tabindex="4" >
 <option>--Select Broke Type --</option>
 
 <% 
strsql = "SELECT * from tblBrokeSap where Category = 'BROKE' order by  Type"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    while not MyRec.eof %>

       <option value="<%= MyRec("TYpe") %>"><%= MyRec("SAP") %>-<%= MyRec("Type") %></option>
 <% MyRec.movenext
 wend
 MyRec.close %> 
       
       
        
     </select>&nbsp; (If you selected BROKE, then you must select type of Broke)<br><br><font face = arial size = 4 color="teal">
    <select name="Format"   size="1" tabindex="5">
	<option selected value="">  Select Packaging Format</option>
 	<option   >Bales</option>
<option   >Rolls</option>
<option  >Gaylords</option>

 	</select> <span class="style7">&nbsp;</span><font size="2" face = arial><span class="style7"> 
	(If you selected BROKE, then you must select packaging format)</span></font></font> </td>
  </tr>
  <tr>
    <td class="style4" style="width: 361px"><strong>Enter 
	Bales for Species other than Secondary Fiber:&nbsp;&nbsp;&nbsp; </strong></td>

    <td class="style6" colspan="2"><font size="2" face = arial>
      <input type="text" name="Bales" size="11" style="width: 36px" tabindex="8">&nbsp; Tons will be calculated when you Submit.&nbsp;&nbsp; </font></td>
  </tr>

    <tr>
    <td align="right" style="height: 50px; width: 361px" class="style6">
	<font face="Arial" size="2">Enter Tons or Pounds for Secondary Fiber&nbsp;&nbsp;<br>
&nbsp;(Broke, KBLD, OCC, PMX, MXP OF3, 
	HWM, LPSBS, USBS, SWL)&nbsp;&nbsp; <br>
	or for Species not in Dropdown:&nbsp;&nbsp;&nbsp; </font></td>

    <td style="height: 50px" class="style5" colspan="2">

     
      <input type="text" name="Tons" size="11" style="width: 60px; height: 22px;" tabindex="9"></td>

  </tr>
    <tr>
    <td class="style5" colspan="3">&nbsp;</td>

  </tr>





       <tr>
          <td  align = right height="27" style="width: 361px" class="style6" >
    <font face="Arial" size="2">Date Transferred:&nbsp;</font></td>
<td align = left height="27" class="style5" colspan="2">

      <input type="text" name="Date_Received" size="15" value = "<%= strDateReceived%>" tabindex="10"></td></tr>
<tr>
    <td class="style5" colspan="3">&nbsp;</td>

  </tr>

  <tr>
    <td style="width: 361px" class="style6">
	<p align="right"><font face="Arial" size="2">Print Movement Order:</font></td>

    <td align = left class="style5"> <font size="1" face="Verdana">  
	<input type="checkbox"  value="ON" name="Print_receipt" checked></font></td>

    <td class="auto-style2"> <span class="auto-style1"> <strong>If SHRED, KBLD, PMX, 
	OF3 or HBX going to OCC, check here:</strong></span> <font size="1" face="Verdana">  
	<input type="checkbox"  value="ON" name="OCC"></font></td>
  </tr>
<tr>
    <td style="width: 361px" class="style6">
	<p align="right"><font face="Arial" size="2"> </font></td>

    <td align = left class="style5"> <font size="1" face="Verdana">  
	 </font></td>

    <td class="auto-style2"> <span class="auto-style1"> <strong>If USBS, LPSBS, HWM, SWL or MXP going to RF, check here:
</strong></span> <font size="1" face="Verdana">  
	<input type="checkbox"  value="ON" name="RF"></font></td>
  </tr>

  <tr>
    <td class="style5" colspan="3">&nbsp;</td>

  </tr>

  <tr>
    <td style="width: 361px" class="style5">&nbsp;</td>

    <td align = left class="style5" colspan="2"><Input name="Update" type="submit" Value="Submit" ></td>
  </tr>
</table>

</div>

</form>
</body>
<%
 Function GetData()
  set objMOC = new ASP_CLS_FIBER
        set rstFiber = objMOC.FiberCarrier()
        strDateReceived = formatdatetime(Now(),2)  
          set objEPS = new ASP_CLS_Fiber      
          set rstSpecies = objEPS.VFSpecies()
End Function




 Function SaveData()
 strBales = 0
strTrailer = Request.form("Trans_Trailer")
If len(Request.form("Enter_species")) > 0 then
strSpecies = Request.form("Enter_species")
else
strSpecies = Request.form("Species")
end if
strCarrier = Replace(Request.form("Carrier"), "'", "''")

strDateReceived = Request.form("Date_received")

strSAP = ""

Dim strRightnow, strTons
strRightnow = Now()
strTons = Request.form("Tons")
strGrade = "RF"



if len(request.form("Bales")) > 0 then
if request.form("Bales") > 0 then
strsql3 = "Select [Other] from tblVFSpecies where Fiber_Species = '" & strSpecies & "'"
   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")

	If not Myrec.eof then
	strFactor = Trim(MyRec.fields("Other"))
	MyRec.close
	end if

strTons =round(Request.form("Bales") * cdbl(strFactor),3)
strBales = request.form("Bales")
end if
end if



strsql = "Select Grade from tblVFSpecies where Fiber_Species = '" & strSpecies & "'"
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
If not MyRec.eof then
If len(MyRec("Grade")) > 0 then
strGrade = MyRec("Grade")
end if
end if


If strSpecies = "BROKE" then
strGrade = "BROKE"
strBrokeDescription = Request.form("Broke_Description")
strsql = "Select SAP from tblBrokeSAP where Type = '" & strBrokeDescription & "'"
Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
	If not MyRec.eof then
	strSAP = MyRec("SAP")
	end if
	MyRec.close
else

strBrokeDescription = ""
end if 



if strTons > 500 then
strTons = round(strTons/2000,3)
end if

IF  request.form("OCC") = "ON" then
strOCC = -1
else
strOCC = 0
end if
IF   request.form("RF") = "ON" then
strRF = -1
else
strRF = 0
end if



	strsql =  "INSERT INTO tblCars (Broke_Description, PMO_Nbr, Date_Received, Trailer,  OID,  Transfer_Trailer_nbr, Transfer_Date, "_
	&" Trans_Carrier, Species, Grade, Location, SAP_NBR, Tons_Received, Deduction, Net, Entry_Time, Entry_BID, Entry_Page, Bales_RF,  Format_PKG, Shred_OCC, Shred_RF ) "_
	&" SELECT  '" & strBrokeDescription & "', 'Meyer Bldg', '" & strDateReceived & "', 'UNKNOWN',  0,'" & strTrailer & "', '" & strDateReceived & "', "_
	&" '" & strCarrier & "', '" & strSpecies & "', '" & strGrade & "',  'YARD',  "_
	&"   '" & strSAP & "'," & strTons & ", 0, " & strTons & ", '" & strRightnow & "', '" & Session("EmployeeID") & "', 'Transfer_From_Meyer.asp', "_
	&"  " & strBales & ",  '" & request.form("Format") & "', " & strOCC & ", " & strRF & ""
	
	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

Dim strlast
strsql = "Select Max(CID) as MAXofCID from tblCars"

   	 Set MyConn = Server.CreateObject("ADODB.Recordset")
   	 MyConn.Open strSQL, Session("ConnectionString")
   	 strlast = MyConn.fields("MaxofCID")
   	 MyConn.close

         
 dim strbody, strbody2, strEmailTo, strECC, strEBCC      
         


	      strEmailTo = "<EMAIL>"
                   strECC = "<EMAIL>, <EMAIL>"
      
           
            
         

            strEBCC = "<EMAIL> "
         
                 
			Set objMail = Server.CreateObject("CDO.Message")		
			objMail.From = "<EMAIL>"
			objMail.To = strEmailTo
			objMail.CC = strECC
			'objMail.BCC = strEBCC
		

			objMail.Subject = "Truck of Recycle Fiber Arrived FROM Meyer Building going to Yard"
			strBody2 = "<table border=0  cellpadding=0 cellspacing=0 width=800><tr><td align = center><font face = arial size = 2>&nbsp; Carrier&nbsp; </td><td align = left><font face = arial size = 2>Trailer&nbsp; </td><td align = left><font face = arial size = 2>Species&nbsp; </td></tr>"
			
          strbody2 = strbody2 & " <tr><td align = center><font face = arial size = 1> " & strCarrier &  "</td><td align = left><font face = arial size = 1>" & strTrailer & "</td><td align = left><font face = arial size = 1>" & strSpecies & "</td></tr> "
   
				
		objMail.HTMLBody = "<font face = arial size = 2>Truck of Recycle Fiber Arrived FROM Meyer Building going to Yard:<br><br>" & strbody2



			'objMail.Send
			Set objMail = nothing	       
         
         
      
         
     
         
         
 If Request.form("Print_receipt") = "ON" then
 Response.redirect("Transfer_Meyerreceipt.asp?id=" & strlast)
 else        
  

Response.redirect ("Fiberindex.asp")
end if


End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->