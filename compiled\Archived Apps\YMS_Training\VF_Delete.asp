﻿<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->

<!--#include file="classes/asp_cls_General.asp"-->


<%

dim strID
strID = Request.querystring("id")

    set objGeneral = new ASP_CLS_General
  %>


   <form action="VF_Delete.asp?id=<%= strid%>&t=<%= strPO%>" method=post >

                <table width = 100%> 
<tr><td></td><td colspan=2 align = right><font face="Arial"><a href="VF_Inbound.asp"><b>Return</b></a></font></td></tr>
<% Dim Myrec, strsql1, strPO


strsql1 = "SELECT tblVirginFiber.* FROM tblVirginFiber WHERE VID = " & strid & ""

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL1, Session("ConnectionString")

strPO = MyRec.fields("PO") 
MyRec.close
%>
     <tr>
                        <td><Font size = 2 face = Arial> Are you sure you want to delete the record for PO# <b><%= strPO %></b>? <br><br> If so, click the button below.
                           
            </td>
            
        </tr>
        
    </table>

<p>

<Input name="Update" type="submit" Value="Delete Record" >
</form>


  <% if objGeneral.IsSubmit() Then 


Dim strsQL3

    strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
   
    strid = request.querystring("id")
    
    strPO = Request.querystring("t")

   
    
	Dim strsql, Myconn
	strSQL = "DELETE FROM tblVirginFiber where tblVirginFiber.VID = " &  strID & ""

			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
MyRec.close


Response.redirect ("VF_Inbound.asp") 
else
Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to delete a record.</font></br>")
MyRec.close
end if
  end if
%><!--#include file="Fiberfooter.inc"-->