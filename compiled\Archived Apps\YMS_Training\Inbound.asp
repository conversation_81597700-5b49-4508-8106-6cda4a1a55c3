 
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Recovered Paper Orders</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->

 
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

<%  
	Dim strsQL, rstEquip, objGeneral, objNew, rstSpecies, strSpecies, MyRec, strCommodity, strDigit, strDigitLC, MyConn


 	Dim  strdate, strDelDate, strcount, strPO, strRelease,  strStatus, strReleaseNo, strsql2, MyRec2


strdate = formatdatetime(now(),2)
strdate = dateadd("d", -2, strdate)

   set objGeneral = new ASP_CLS_General

call Getdata()
  if objGeneral.IsSubmit() Then
  strCommodity = Request.form("Commodity")
  strSpecies = Request.form("Species")
  	If len(Request.form("PO")) > 4 then  
 	 strPO = Request.form("PO")
  	else 
  	strPO = Null
 	 End if
 	 
 	 end if
%>
<style type="text/css">
.auto-style1 {
	background-color: #E7EBFE;
}
.auto-style2 {
	font-weight: bold;
	background-color: #E7EBFE;
}
.auto-style3 {
	border-right: 2px solid #C0C0C0;
	border-left: 2px solid #C0C0C0;
	border-top: 2px solid #C0C0C0;
	border-top-width: 2px;
	border-bottom: 2px solid #C0C0C0;
}
.auto-style4 {
	font-family: Arial, Helvetica, sans-serif;
}
.auto-style5 {
	font-size: x-small;
}
.auto-style6 {
	border-width: 1px;
	background-color: #E7EBFE;
}
.auto-style7 {
	border-width: 1px;
	font-size: x-small;
	background-color: #E7EBFE;
}
</style>
</head>

<form name="form1" action="Inbound.asp" method="post">
<TABLE cellSpacing=0 cellPadding=1 width=100%  class="auto-style3">  
  <tr><td  colspan = "2" align = center class="auto-style2">
	<p align="left"><font face = "Arial">Inbound&nbsp;List</td>

	<td colspan = 3 align = "RIght" class="auto-style2">	<font face="Arial"><a href="Loginscreen.asp">HOME</a></font></td></tr>


  <TR>
         <TD align = center class="auto-style1" >
			<b><font face="Arial">Commodity&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </font></b>
      <font face="Arial">   
	<select size="1" name="Commodity">
     <option value = "">All</option>
 
		<option <% if strCommodity = "SECONDARY FIBER" THEN %> selected <% end if %>>SECONDARY FIBER</option>

     </select></font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <font face="Arial"><b>OR</b></font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
			<b><font face="Arial">Species&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </font></b>
   
			<font face="Arial">   
	<select size="1" name="Species" style="font-weight: 700">

     <option value="">--- All ---</option>
     <% strsql = "Select distinct Species from tblCars where date_received > '7/31/2019' and (Grade = 'RF' or Grade = 'Broke')"
         Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
    while not Myconn.eof %>
    <option <% if strSpecies = MyConn("Species") then %> selected <% end if %>><%= MyConn("Species") %></option>"

<% MyConn.movenext
wend
MyConn.close %>
     </select></font><b> </b></font>
    </TD>
<td class="auto-style2"><font face="Arial">PO #:&nbsp;&nbsp;<input type="text" name="PO" size="15" value="<%= strPO%>"></font></td>
    <TD align="right" class="auto-style1" >
    <Input name="Update" type="submit" Value="Search" style="float: left" >&nbsp;&nbsp;&nbsp;

</TD>

</TR>
  </TABLE>
</form>
  <% 

  if objGeneral.IsSubmit() Then
 	 strCommodity = Request.form("Commodity")
 	 strSpecies = Request.form("Species")
 	 If len(Request.form("PO")) > 4 then  
 	 strPO = Request.form("PO")
 	 else 
  	strPO = 0
  End if

If len(strPO) > 2 then

    strsql = "SELECT tblInbound.* FROM tblInbound where Destination_city = 'MOBILE' and '" & strdate & "' <= date_to "_
&" and PO = '" & strpo & "' "_
&" order by left(release,1), date_to" 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
	 MyRec.Open strSQL, Session("ConnectionString") 

  		 elseif request.form("Commodity")  = "SECONDARY FIBER" then
	 

    strsql = "SELECT tblInbound.* FROM tblInbound where Destination_city = 'MOBILE' and '" & strdate & "' <= date_to "_
    &" AND (left(release,1) = 'C' or  left(release,1) = 'c' or left(release,1) = 'K' or  left(release,1) = 'k' or left(release,1) = 'S' or  left(release,1) = 's' or "_
    &" left(release,1) = 'P' or  left(release,1) = 'p' or left(release,1) = 'F' or  left(release,1) = 'f' or left(release,1) = 'M' or  left(release,1) = 'm' or "_
	&" left(release,1) = 'R' or  left(release,1) = 'r' or left(release,1) = 'H' or  left(release,1) = 'h' or left(release,1) = 'D' or  left(release,1) = 'd' or "_
	&" left(release,1) = 'L' or  left(release,1) = 'l'  or left(release,1) = 'W' or  left(release,1) = 'w'"_

	&" left(release,1) = 'B' or  left(release,1) = 'b') order by left(release,1), date_to" 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
	 MyRec.Open strSQL, Session("ConnectionString") 


elseif   request.form("Species") <> "" then
  		if request.form("Species") = "OCC" then
 	 strDigit = "C"
 	 strDigitLC = "c"
    	  	elseif request.form("Species") = "KBLD" then
  	strDigit = "K"
  	strDigitLC = "k"

  	elseif request.form("Species") = "SWL" then
 	 strDigit = "S"
    elseif request.form("Species") = "PMX" then
  	strDigit = "P"
  	strDigitLC = "p"
   elseif request.form("Species") = "DLK"  or request.form("Species") = "IGS" then
  	strDigit = "6"
  	elseif request.form("Species") = "WLS" then
  	strDigit = "8"
  	 elseif request.form("Species") = "SBS" then
  	strDigit = "7"
   elseif request.form("Species") = "OF3" then
 	 strDigit = "F"
  strDigitLC = "f"
    elseif request.form("Species") = "MXP" then
 	 strDigit = "M"
  strDigitLC = "m"
    elseif request.form("Species") = "ROCC" then
 	 strDigit = "R"
  strDigitLC = "r"
    elseif request.form("Species") = "LPSBS" then
 	 strDigit = "L"
  strDigitLC = "l"
   elseif request.form("Species") = "HWM" then
 	 strDigit = "W"
  strDigitLC = "w"


 	
  	 elseif request.form("Species") = "BROKE"  or request.form("Species") = "Broke" then
  	strDigit = "B"
  		 elseif request.form("Species") = "WBROK" then
  	strDigit = "B"
		 elseif request.form("Species") = "HBX" then
  	strDigit = "H"
	 elseif request.form("Species") = "SHRED" then
  	strDigit = "D"
  	


  end if

	If strDigit = "B" then
 	 strsql = "SELECT tblInbound.* FROM tblInbound where Destination_city = 'MOBILE' and '" & strdate & "' <= date_to"_
	&" and (left(release,1) = '" & strdigit  & "'  or len(Status) > 1) "_
	&" order by date_to"  

	else      
  	strsql = "SELECT tblInbound.* FROM tblInbound where Destination_city = 'MOBILE' and '" & strdate & "' <= date_to"_
	&" and (left(release,1) = '" & strdigit  & "'  or len(Status) > 1) "_
	&" order by date_to"  
    end if

   Set MyRec = Server.CreateObject("ADODB.Recordset")
	 MyRec.Open strSQL, Session("ConnectionString") 	

else
  strsql = "SELECT tblInbound.* FROM tblInbound where Destination_city = 'MOBILE' and '" & strdate & "' <= date_to"_
&" order by left(release,1), date_to" 


   Set MyRec = Server.CreateObject("ADODB.Recordset")
	 MyRec.Open strSQL, Session("ConnectionString") 	

end if

strcount = 1
  
  %>
   <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=96%  border=1 align = center>
<tr><td colspan="12" align="right" class="auto-style4"><a href="Inbound_Excel.asp?s=<%= strSpecies %>&p=<%= strPO %>&c=<%= strCommodity %>">EXCEL</a></td></tr>
 
      <tr bgcolor="#EFEFEF">
      <td  align="center" class="auto-style5"><font face="Arial">Load</font></td>
    <td  align="center" class="auto-style5"><font face="Arial">Trailer</font></td>
       <td  align="center"><font face="Arial" size = 1 class="auto-style5">Verify<br>Weight</font></td>

       <td  align="center" class="auto-style5"><font face="Arial">Species</font></td>
  <td  align="center" class="auto-style5"><font face="Arial">Ship_status</font></td>
   
      <td  align="center" class="auto-style5"><font face="Arial">Delivery Date</font></td>
 	<td  align="left" class="auto-style5"><font face="Arial">Vendor Name</font></td>
<td  align="left" class="auto-style5"><font face="Arial">Vendor City</font></td>
<td  align="center" class="auto-style5"><font face="Arial">PO Number</font></td>
<td  align="center" class="auto-style5"><font face="Arial">Release</font></td>
<td  align="center" class="auto-style5"><font face="Arial">Carrier</font></td>
<td  align="center" class="auto-style5"><font face="Arial">Count</font></td>

      
  	<% 
      Dim ii
       ii = 0
       while not MyRec.Eof
       strReleaseNo = MyREc("release")
       
       strsql2 = "Select CID from tblCars where Release_nbr = '" & strReleaseNo & "'"
          Set MyRec2 = Server.CreateObject("ADODB.Recordset")
	 MyRec2.Open strSQL2, Session("ConnectionString") 	
	 if not MyRec2.eof then
	 'skip display
	 else

       
       if MyRec.fields("date_to")  <> strDeldate then
       strcount = 1
       end if
       
 strOSpecies = ""
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="#EFEFEF">
    <% else %>
       <tr bgcolor="#E2F3FE">
    <% end if %>
              <td  align="left" class="auto-style7"><font face="Arial" size = 1>
			  <span class="auto-style5"><%= MyRec.fields("Load_nbr")%>&nbsp;</span></font></td>
                  <td  align="left" class="auto-style6"><font face="Arial" size = 1>
				  <span class="auto-style5"><%= MyRec.fields("Trailer")%>&nbsp;</span></font></td>
                  
                  <% strsql3 = "Select V_Weight, Species from tblOrder where Release = '" & strReleaseNo & "'"
                  
                         Set MyRec3 = Server.CreateObject("ADODB.Recordset")
	 MyRec3.Open strSQL3, Session("ConnectionString") 	
	 if not MyRec3.eof then
	 strOSpecies = MyRec3("Species") %>
	 

                  
                     <td  align="left" class="auto-style7"><font face="Arial" size = 1>
					 <span class="auto-style5"><%= MyRec3.fields("V_weight")%>&nbsp;</span></font></td>
                     <% else %>
                              <td  align="left" class="auto-style7">
							  <font face="Arial"> &nbsp;</font></td>
                     <% end if %>
           <td  align="left" class="auto-style7"><font face="Arial" size = 1>
           <span class="auto-style5">
          <% 
          
          if left(MyRec.fields("Release"),1) = "C"  or left(MyRec.fields("Release"),1) = "c" then %>
              OCC
              <% elseif left(MyRec.fields("Release"),1) = "K" or  left(MyRec.fields("Release"),1) = "k"then %>  
   KBLD

          <% elseif left(MyRec.fields("Release"),1) = "H" or  left(MyRec.fields("Release"),1) = "h"then %>  
              HBX
<% elseif left(MyRec.fields("Release"),1) = "D" or  left(MyRec.fields("Release"),1) = "d"then %>  
              SHRED

              <% elseif left(MyRec.fields("Release"),1) = "P"  or left(MyRec.fields("Release"),1) = "p" then %>  
              PMX
                 <% elseif left(MyRec.fields("Release"),1) = "S"  or left(MyRec.fields("Release"),1) = "s" then %>  
              SWL

                <% elseif left(MyRec.fields("Release"),1) = "F"  or left(MyRec.fields("Release"),1) = "f" then %>  
             OF3
                   <% elseif left(MyRec.fields("Release"),1) = "W"  or left(MyRec.fields("Release"),1) = "w" then %>  
             HWM
              <% elseif left(MyRec.fields("Release"),1) = "L"  or left(MyRec.fields("Release"),1) = "l" then %>  
             LPSBS


  <% elseif left(MyRec.fields("Release"),1) = "M"  or left(MyRec.fields("Release"),1) = "m" then %>  
              MXP
 <% elseif left(MyRec.fields("Release"),1) = "B" or left(MyRec.fields("Release"),1) = "b"  then 
 strRelease = myrec.fields("release")
      strsql = "SELECT SAP_Nbr from tblOrder where Release = '" & strRelease & "' "


   Set MyRec2 = Server.CreateObject("ADODB.Recordset")
	 MyRec2.Open strSQL, Session("ConnectionString") 
   if not MyRec2.eof then 
 %>  
           BROKE - <%= MyRec2("SAP_NBR") %>
           <% else %>
           BROKE
           <% end if
           MyRec2.close %>

      <% elseif left(MyRec.fields("Release"),1) = "R"  or left(MyRec.fields("Release"),1) = "r" then %>  
           ROCC
  
        <% elseif len(MyRec.fields("Status")) > 1 then
        strStatus = MyRec.fields("Status")
              strsql = "SELECT Type from tblBrokeSAP where SAP = '" & strStatus & "' "


   Set MyRec2 = Server.CreateObject("ADODB.Recordset")
 	 MyRec2.Open strSQL, Session("ConnectionString") 
   if not MyRec2.eof then %>
           BROKE - <%= MyRec2("Type") %>
        
      <% else  %>
      BROKE
      <% end if %>
           <% else %>  
             &nbsp;
             <% end if %></span></font></td>
       <td  align="center" class="auto-style6"><font face="Arial" size = 1>
	   <span class="auto-style5"><%= MyRec.fields("Ship_status") %>&nbsp;</span></font></td>

     <td  align="center" class="auto-style6"><font face="Arial" size = 1><span class="auto-style5"><%= formatdatetime(MyRec.fields("date_to"),2)%>&nbsp;</span></font></td>
     <td  align="left" class="auto-style6"><font face="Arial" size = 1><span class="auto-style5"><%= MyRec.fields("ship_from_name")%>&nbsp;</span></font></td>
     <td  align="left" class="auto-style6"><font face="Arial" size = 1><span class="auto-style5"><%= MyRec.fields("ship_from_city")%>&nbsp;</span></font></td>
     <td  align="center" class="auto-style6"><font face="Arial" size = 1><span class="auto-style5"><%= MyRec.fields("po")%>&nbsp;</span></font></td>
   
     <td  align="center" class="auto-style6"><font face="Arial" size = 1><span class="auto-style5"><%= MyRec.fields("release")%>&nbsp;</span></font></td>
     <td  align="center" class="auto-style6"><font face="Arial" size = 1><span class="auto-style5"><%= MyRec.fields("carrier")%>&nbsp;
       </span>
       <td  align="center" class="auto-style5"><font face="Arial"><%= strcount%>  </font></td> 
   </tr>
    <% 
       ii = ii + 1
       strDelDate = MyRec.fields("date_to")
       strcount = strcount + 1
       
       end if
       MyRec.MoveNext
     Wend
    %>
   </table>
<table>    <tr><td colspan="7" bgcolor="white" align="right">&nbsp;</td></tr></table>

<%
MyRec.close
end if
 %>
 
  <% Function GetData()

   set objNew = new ASP_Cls_Fiber
        
	set rstSpecies = objNew.FiberSpecies()


End Function




 %><!--#include file="Fiberfooter.inc"-->