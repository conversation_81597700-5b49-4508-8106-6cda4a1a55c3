																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Modify Broke Consumption Projection </TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->


<% Dim MyRec, strsql, strid, strOF
  Dim strdate, strTB, strTG, strWTB, strBTB  

  
strid = request.querystring("id")

strsql = "Select * from tblBrokeConsumption where ID = " & strid
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 

strTB = MyRec.fields("Tissue_bales")
strTG = MyRec.fields("Tissue_Gaylords")
strWTB = MyRec.fields("White_towel_bales")
strBTB = MyRec.fields("Brown_towel_bales")
strdate = MyRec.fields("Inv_Date")
MyRec.close


  set objGeneral = new ASP_CLS_General
  if objGeneral.IsSubmit() Then	

  
	If len(request.form("TB")) > 0 then
  	strTB = request.form("TB")
  	else
  	strTB = 0
  	end if
  	
  	If len(request.form("TG")) > 0 then
  	strTG = request.form("TG")
  	else
  	strTG = 0
  	end if
	
  	If len(request.form("WTB")) > 0 then
  	strWTB = request.form("WTB")
  	else
  	strWTB = 0
  	end if
  	
   	  	If len(request.form("BTB")) > 0 then
  	strBTB= request.form("BTB")
  	else
  	strBTB = 0
  	end if

  	
   	 strdate = request.form("C_Date")
   	 
  
    
strsql =  "Update tblBrokeConsumption set Tissue_Bales = " & strTB & ", Tissue_gaylords = " & strTG & ", White_towel_bales = " & strWTB & ", Brown_towel_bales = " & strBTB & " where ID = " & strid
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
Response.redirect("BC.asp")		
end if
	
%>

<style type="text/css">
.style2 {
	font-family: arial;
	font-size: x-small;
}
.style4 {
	border-style: solid;
	border-width: 1px;
}
.style6 {
	text-align: center;
	font-weight: bold;
	border-left-style: solid;
	border-left-width: 1px;
	border-right: 1px solid #808080;
	border-top-style: solid;
	border-top-width: 1px;
	border-bottom: 1px solid #808080;
	background-color: #FFFFCC;
}
.style7 {
	text-align: center;
	border-left-style: solid;
	border-left-width: 1px;
	border-right: 1px solid #808080;
	border-top-style: solid;
	border-top-width: 1px;
	border-bottom: 1px solid #808080;
}

</style>
</head>

<body>
<br>
	
<form name="form1" action="BC_Edit.asp?id=<%= strid%>" method="post" ID="Form1">
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Modify Broke Consumption Projection</b></font></td>
<td align = right><font face="Arial"><a href="BC.asp">Return</a>&nbsp;</td>


</tr>
	    </table>



	<p>&nbsp;</p>
	<table id="table1" style="width: 30%" class="style4" align="center">
<tr >
	
		<td class="style6"  ><font face="Arial" size="2">Tissue Bales</font></td>
			<td class="style6"  ><font face="Arial" size="2">Tissue Gaylords</font></td>
		<td class="style6">   <font face="Arial" size="2">White Towel Bales</font></td>
				<td class="style6">   <font face="Arial" size="2">Brown Towel Bales</font></td>
	</tr>
	<tr>
		<td class="style7"><font face = arial size = 1>
		<input type="text" name="TB" size="20" style="width: 69px" value="<%= strTB %>"></td>
		<td class="style7"><font face = arial size = 1>
		<input type="text" name="TG" size="20" style="width: 69px" value="<%= strTG %>"></td>

		<td class="style7">
		<p align="center" class="style5"><font face = arial size = 1>
		<input type="text" name="WTB" size="20" style="width: 53px; height: 22px;" value="<%= strWTB %>"></td>
		<td class="style7">
		<p align="center" class="style5"><font face = arial size = 1>
		<input type="text" name="BTB" size="20" style="width: 53px; height: 22px;" value="<%= strBTB %>"></td>



	</tr>
</table>


<p align="center"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></p>
</form>
<!--#include file="Fiberfooter.inc"-->