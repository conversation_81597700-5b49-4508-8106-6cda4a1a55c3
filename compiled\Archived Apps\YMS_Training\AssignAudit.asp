																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Assign Audit Weight for Truck Receipt</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->



<% Dim MyRec, strsql, strdate, strtdate

strdate = formatdatetime(Now(),2)
strtdate = dateadd("d", -30, strdate)
strsql = "SELECT tblCars.* FROM tblCars WHERE date_received > '" & strtdate & "' and (((tblCars.Audit_Tons) Is Null) AND ((tblCars.Weigh_required)='W')) "_
&" or Audit_status = 'OPEN' Order by Carrier, trailer "


    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><b>
<font face="arial" size="4" >Assign Audit Weight</font></b></span></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1>  
	 <tr class="tableheader">
<td  align = center> <font face="Arial" size="1"><b>Enter<br> Audit Weight</b></font></td>

	<td  > <font face="Arial" size="2"><b>Trailer</b></font></td>
	
	<td  > <font face="Arial" size="1">Status</font></td>
		
		<td  ><font face="Arial" size="1"><b>Carrier</b></font></td>
		<td  ><font face="Arial" size="1">Species</font></td>
    
		<td  ><font face="Arial" size="1">Vendor</font></td>
		<td  >
        <font face="Arial" size="1">PO Number</font></td>
		<td  >
        <p align="center">
        <font face="Arial" size="1">Release<br> Number</font></td>
	
		<td  >
        <font face="Arial" size="1">REC <br>Number</font></td>
		<td  >
        <p align="center">
        <font face="Arial" size="1">Gross<br> Weight</font></td>
		<td  >
        <p align="center">
        <font face="Arial" size="1">Tare<br> Weight</font></td>
	<td  ><p align="center">
     <font face="Arial" size="1">Audit<br>Tons</font></td>
		<td  >
        <font face="Arial" size="1">Date<br> Received</font></td>
        <td  >
        <font face="Arial" size="1">Date<br> Unloaded</font></td>
        		<td  >
        <font face="Arial" size="1">Generator</font></td>
		<td  >
        <font face="Arial" size="1">Generator<br> City</font></td>
		<td  >
        <font face="Arial" size="1">Gen<br> State</font></td>
		<td  >
        <font size="1" face="Arial">Other</font></td>


	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
	<td align = center ><font size="1" face="Arial"><a href="Truck_audit_weights.asp?id=<%= MyRec.fields("CID") %>">Enter</a></td>

	<td  >   <font size="2" face="Arial"><b> <%= MyRec.fields("Trailer")%></font></b></td>
   
	<td  >   <font size="2" face="Arial"><b> <%= MyRec.fields("Audit_status")%>&nbsp;</font></b></td>

			<td  > <font size="1" face="Arial"><b> <%= MyRec.fields("Carrier")%>&nbsp;</font></b></td>


		<td> <font face="Arial" size="1"> <%= MyRec.fields("Species")%></font></td>
		<td>        <font size="1" face="Arial">        <%= MyRec.fields("Vendor")%></font></td>
		<td  >        <font size="1" face="Arial">        <%= MyRec.fields("PO")%></font></td>
		<td  >        <font size="1" face="Arial">        <%= MyRec.fields("Release_Nbr")%></font></td>
	<td>		 <font size="1" face="Arial">        <%= MyRec.fields("REC_Number")%></font></td>
		<td align = right ><font size="1" face="Arial"><%= MyRec.fields("Audit_gross")%>&nbsp;</font></td>
		<td align = right ><font size="1" face="Arial">        <%= MyRec.fields("Audit_tare")%>&nbsp;</font></td>

		<td align = right  ><font size="1" face="Arial">        <%= MyRec.fields("Audit_tons")%>&nbsp;</font></td>
		
		<td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Date_Received")%></font></td>
				<td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Date_Unloaded")%></font></td>
       <td  >
		 <font size="1" face="Arial">
        <%= MyRec.fields("Generator")%></font></td>
		<td  >
		 <font size="1" face="Arial">
        <%= MyRec.fields("Gen_City")%></font></td>
		<td  >
		 <font size="1" face="Arial">
        <%= MyRec.fields("Gen_State")%></font></td>
		<td  >
		 <font size="1" face="Arial">
        <%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>

	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>


<!--#include file="Fiberfooter.inc"-->