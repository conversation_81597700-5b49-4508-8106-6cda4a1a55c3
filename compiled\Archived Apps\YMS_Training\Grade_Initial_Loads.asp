																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Grade Inbound Load</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->



<% Dim MyRec, strsql, strdate, strYdate, strToday

strdate = formatdatetime(Now(),2)
strToday = Now()
strYdate = dateadd("d", -3, strToday)

strsql = "SELECT tblCars.* FROM tblCars LEFT JOIN tblOCCGrading ON tblCars.CID = tblOCCGrading.CID "_
&" WHERE tblCars.Grade = 'RF' and tblOCCGrading.OCC_Tech Is Null and tblCars.Trailer <> 'UNKNOWN'  and tblCars.Species <> 'WLS' order by tblCars.Species, tblCars.trailer"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><b>
<font face="arial" size="4" >Inbound Load Grading Form</font></b></span></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1>  
	 <tr class="tableheader">
<td>&nbsp;</td>
	<td  >
        <font face="Arial" size="2"><b>Trailer</b></font></td>
        <td  >
        
        <font face="Arial" size="1"><b>Location</b></font></td>
        <td  >
        <font face="Arial" size="1">Carrier</font></td>
		<td  >
        <font face="Arial" size="1">Species</font></td>
    
		<td  >
        <font face="Arial" size="1">Vendor</font></td>
		<td  >
        <font face="Arial" size="1">PO Number</font></td>
		<td  >
        <p align="center">
        <font face="Arial" size="1">Release<br> Number</font></td>
	
		<td  >
        <font face="Arial" size="1">REC Number</font></td>
		<td  >
        <p align="center">
        <font face="Arial" size="1">Gross<br> Weight</font></td>
		<td  >
        <p align="center">
        <font face="Arial" size="1">Tare<br> Weight</font></td>
		<td  >
        <p align="center">
        <font face="Arial" size="1">Tons<br> Received</font></td>
		<td  >
        <font face="Arial" size="1">Date<br> Outed</font></td>
		<td  >
        <font face="Arial" size="1">Generator</font></td>
		<td  >
        <font face="Arial" size="1">Generator<br> City</font></td>
		<td  >
        <font face="Arial" size="1">Gen<br> State</font></td>
		<td  >
        <font size="1" face="Arial">Other</font></td>


	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td> <a href="Car_unload_entry.asp?id=<%= MyRec.fields("CID") %>">
<font face="Arial" size="1">Grade</font></a></td>
	<td  >
        <font size="2" face="Arial"><b>
        <%= MyRec.fields("Trailer")%></font></b></td>
        	<td  > <font face="Arial" size="1">        <%= MyRec.fields("Location")%>&nbsp;</font></td>
        	<td  > <font face="Arial" size="1">  <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
			<td  >  <font face="Arial" size="1">       <%= MyRec.fields("Species")%>&nbsp;</font></td>
		<td>  <font size="1" face="Arial">        <%= MyRec.fields("Vendor")%>&nbsp;</font></td>
				<td  >        <font size="1" face="Arial">        <%= MyRec.fields("PO")%>&nbsp;</font></td>
						<td  >        <font size="1" face="Arial">        <%= MyRec.fields("Release_Nbr")%>&nbsp;</font></td>
					<td  >        <font size="1" face="Arial">        <%= MyRec.fields("Rec_number")%>&nbsp;</font></td>
									<td align = right >		 <font size="1" face="Arial">
        <%= MyRec.fields("Gross_Weight")%>&nbsp;</font></td>
		<td align = right >
		 <font size="1" face="Arial">
        <%= MyRec.fields("Tare_Weight")%>&nbsp;</font></td>
		<td align = right  >
		 <font size="1" face="Arial">
        <%= MyRec.fields("Tons_received")%>&nbsp;</font></td>
        		<td  >
		 <font size="1" face="Arial">
        <%= MyRec.fields("Date_unloaded")%>&nbsp;</font></td>
               <td  >
		 <font size="1" face="Arial">
        <%= MyRec.fields("Generator")%>&nbsp;</font></td>
        		<td  >
		 <font size="1" face="Arial">
        <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
        		<td  >
		 <font size="1" face="Arial">
        <%= MyRec.fields("Gen_State")%>&nbsp;</font></td>
        		<td  >
		 <font size="1" face="Arial">
        <%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>

	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>

<!--#include file="Fiberfooter.inc"-->