
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">


<TITLE>Modify Broke Type</TITLE>

<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->

 <%    Dim objMOC,  strID

       Dim objGeneral
	
       set objGeneral = new ASP_CLS_General

if objGeneral.IsSubmit() Then

If len(Request.form("PO")) > 1 then
strid = Request.form("PO")

Response.redirect("Order_Edit_List.asp?po=" & strid)

elseif len(request.form("Release")) > 1  then
strid = request.form("Release")
Response.redirect("Order_Edit_List.asp?r=" & strid)

else
Response.write("<font size = 3 color = red face = arial><b>Please Enter a PO# or a Release Number</b></font>")
end if

End if
%>
<SCRIPT LANGUAGE="JavaScript">
function formHandler(Form1){
document.forms["Form1"].submit();
}
</SCRIPT>

<style type="text/css">
.style4 {
	font-size: x-small;
}
.style5 {
	font-family: Arial;
	font-size: medium;
	font-weight: bold;
}
.style7 {
	font-family: Arial;
	font-size: medium;
}
</style>
</head>

<body>
<form name="form1" action="Request_release.asp" method="post" >

<p>&nbsp;</p>
<div align="center">
<table border="1" cellpadding="0" class = "tablecolor1" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="75%" id="AutoNumber2">
<tr>    <TD align = center bgcolor="#FFFFB3">  <p>&nbsp;</p> <b>
		<font face="Arial"><br><br></font></b>&nbsp;
     <font face="Arial">
		
     	<p>&nbsp;</p>
	
<p align="center"><br>&nbsp;</TD>
       <TD align = center bgcolor="#F2F2FF">  <p>&nbsp;</p> 
		<font face="Arial" size="4"> 
		<b>Enter PO#&nbsp; </b></font><br>
		<font face="Arial" size="3"><b><br></b></font>&nbsp;&nbsp;&nbsp;
     <font face = arial size = 1>
		<span class="style4">
		<input type="text" name="PO" size="20" style="width: 104px"></span></font><p class="style7">
		<strong>OR</strong></p>&nbsp;<p> 
		<span class="style5">Enter Release #</span><br><br>
     	<font face = arial size = 1>
		<span class="style4">
		<input type="text" name="Release" size="10" style="width: 116px"></span></font><font size="3"></p>
		<p align="center"><input type="submit" value="Continue" id=submit1 name=submit1><br>&nbsp;</TD>
       <TD align = center bgcolor="#FFFFB3">  <p>&nbsp;</p> <b>
		<font face="Arial"><br><br></font></b>&nbsp;
     <font face="Arial">
		
     	<p>&nbsp;</p>
	
<p align="center"><br>&nbsp;</TD></tr>

</table>
</div>
</form>

<!--#include file="Fiberfooter.inc"-->