<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Yard Email</title>
</head>
<%
dim strbody, strCID, strECarrier, strETrailer, strELoad, strEDoc, strEDC, strEDCName, strEAdd, strECity, strEState
dim strECC, strEBCC, strESpecies, MyRec, strsql, MyConn2, strbody3, strbody1




dim  strCarrier, str<PERSON>railer,   strsql3, MyRec3, gTcount, str<PERSON><PERSON>, str<PERSON>oney, MyConn
dim strSpecies, strDateReceived, strVendor, strDetention, strDays, strdate, gcount, gSpecies, strNow, strEmailto, strEmailCC
dim gAvgdays, gExcessDays, gTotaldays, strExcess, strMTDDetention, gMTDAvgDays, gMTDTotalCount, gMTDTotalDays, gMTDExcess
dim strMTDDetentionR, gMTDAvgDaysR, gMTDTotalCountR, gMTDTotalDaysR, gMTDExcessR
Dim gCountR, strMoneyR, gAvgDaysR, gExcessR, gTcountR, strTMoneyR
strdate = formatdatetime(now(),2)
gcount = 0
gcountR = 0
gtcountR = 0
gtcount = 0
gSpecies = ""
strMoney = 0
strMoneyR = 0
strTMoney = 0
strTMoneyR = 0
gAvgdays = 0
gAvgdaysR = 0
gExcessDays = 0
gExcessDaysR = 0
gTotaldays = 0
strMTDDetention = 0
gMTDAvgDays = 0
gMTDTotalCount = 0
gMTDTotalDays = 0
gMTDExcess = 0
strMTDDetentionR = 0
gMTDAvgDaysR = 0
gMTDTotalCountR = 0
gMTDTotalDaysR = 0
gMTDExcessR = 0

strsql = "SELECT tblCars.*,  [date_received] AS Sort_Date FROM tblCars WHERE (((tblCars.Date_received) Is Not Null) AND "_
&"  ((tblCars.Location)='Yard') AND ((tblCars.Trailer) Is Not Null)) and Species <> 'KDF'  and Species <> 'Display Components' ORDER BY tblCars.Species, Sort_Date "   
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

 Dim ii
       ii = 0
		
           strEmailTo = "<EMAIL>"
          strEmailCC = "<EMAIL>"  
       ' strEmailTo = "<EMAIL>"        
              
			Set objMail = Server.CreateObject("CDO.Message")		
			objMail.From = "<EMAIL>"
			objMail.To = strEmailTo	
		objMail.BCC = strEmailCC
	
			objMail.Subject = "Fiber Yard Report "
			strBody2 = "<table border=0  cellpadding=0 cellspacing=0 width=800><tr><td align = left><font face = arial size = 2>Species&nbsp; </td><td align = left><font face = arial size = 2>Date Received&nbsp; </td><td align = left><font face = arial size = 2> Trailer </td><td align = left><font face = arial size = 2>Carrier&nbsp;</td><td align = left><font face = arial size = 2>Vendor&nbsp;</td><td align = left><font face = arial size = 2>To-Date<br> Detention</td><td align = left><font face = arial size = 2>Days in<br>Yard</td></tr>"
			
				While not MyRec.EOF
			'On error Resume Next

    



     if isnull(MyRec.fields("Transfer_date")) then 
     strCarrier = MyRec.fields("Carrier") 
     strTrailer = MyRec.fields("Trailer") 
     strSpecies = MyRec.fields("Species") 
     strDateReceived = MyRec.fields("Date_received")
     strVendor = MyRec.fields("Vendor") 
       strRejected = MyRec.fields("Rejected")  
        
       else
         strSpecies = MyRec.fields("Species") & " SHUTTLE"        
		strDateReceived = MyRec.fields("Transfer_Date")
		strTrailer =  MyRec.fields("Transfer_trailer_nbr")
		strCarrier = MyRec.fields("Trans_Carrier")
		 strVendor = MyRec.fields("Vendor") 
		 strRejected = MyRec.fields("Rejected")
	 end if 

        if strRejected = "YES" then
        strRejected = "Rejected"
        end if    
       if isnull(MyRec.fields("Transfer_date")) then
        
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

             else
        
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Trans_Carrier") & "' "
             end if  
   
                  strDays = fix(Now()- cdate(MyRec.fields("Date_received")))
                  
           
                  
    If MyRec.fields("Carrier") = "RAIL"  or MyRec.fields("Carrier") = "RAIL" Then
         strdays = int(strdays)
                    if strdays = 0 then
          strDetention = 0
          elseif strdays = 1 then
          strDetention = 27.00
          elseif strdays = 2 then
          strDetention = 53.00
          
          elseIf strdays = 3 then 
          strDetention = 80.00
          elseif strdays = 4 then
          strDetention = 106.00
          elseif strdays = 5 then
          strDetention = 145.00
          elseif strdays = 6 then
          strDetention = 185.00
          elseif strdays = 7 then
          strDetention = 262.00
          elseif strdays = 8 then 
          strDetention = 339.00
          elseif strdays > 8 then
          strDetention = round(339.48 + (77.45*(strdays-8)),0)
          end if
          
    	if strdays  > 2 then 
    	strExcess = 1
  			else            
       		
			strExcess = 0
	 end if 
	 
If left(MyRec("Trailer"), 4) = "GACX" then
strDetention = 0
strExcess = 0
end if          
  else        
 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    if strDays  > strFree then 
   strDetention =   (int(strDays) * strFee) - (strFee * strFree)
   strExcess = 1
	 else 
	strDetention = 0
	strExcess = 0
	 end if 
	
end if
  
	
if UCase(MyRec.fields("Species")) = Ucase(gSpecies) or gcount = 0 then	
	if strdays < 6 or left(MyRec("Species"),6) = "FIBRIA" Then	
 	strbody2 = strbody2 & " <tr bgcolor=white><td align = left><font face = arial size = 1> " & strSpecies &  "</td><td align = left><font face = arial size = 1>" & strDateReceived & "</td><td align = left><font face = arial size = 1>"  & strTrailer & "</td><td align = left><font face = arial size = 1>"  & strCarrier & "</td><td align = left><font face = arial size = 1>" & strVendor & "</td><td align = left><font face = arial size = 1>" & strDetention & " " & strRejected & " </td><td align = left><font face = arial size = 1>" & strDays & " </td></tr> "
	elseif strdays > 5 and strdays < 9 then
	 strbody2 = strbody2 & " <tr bgcolor=#FFFF99><td align = left><font face = arial size = 1> " & strSpecies &  "</td><td align = left><font face = arial size = 1>" & strDateReceived & "</td><td align = left><font face = arial size = 1>"  & strTrailer & "</td><td align = left><font face = arial size = 1>"  & strCarrier & "</td><td align = left><font face = arial size = 1>" & strVendor & "</td><td align = left><font face = arial size = 1>" & strDetention & " " & strRejected & "</td><td align = left><font face = arial size = 1>" & strDays & " </td></tr> "
	else
	 strbody2 = strbody2 & " <tr bgcolor=#FCDCE6><td align = left><font face = arial size = 1> " & strSpecies &  "</td><td align = left><font face = arial size = 1>" & strDateReceived & "</td><td align = left><font face = arial size = 1>"  & strTrailer & "</td><td align = left><font face = arial size = 1>"  & strCarrier & "</td><td align = left><font face = arial size = 1>" & strVendor & "</td><td align = left><font face = arial size = 1>" & strDetention & " " & strRejected & "</td><td align = left><font face = arial size = 1>" & strDays & " </td></tr> "

	end if 

else

	strBody2 = strbody2 & "<font face = arial size = 1> Total " & gSpecies & ": " & gcount
 	if strdays < 6 or left(MyRec("Species"),6) = "FIBRIA" Then	
 	strbody2 = strbody2 & " <tr bgcolor=white><td align = left><font face = arial size = 1> " & strSpecies &  "</td><td align = left><font face = arial size = 1>" & strDateReceived & "</td><td align = left><font face = arial size = 1>"  & strTrailer & "</td><td align = left><font face = arial size = 1>"  & strCarrier & "</td><td align = left><font face = arial size = 1>" & strVendor & "</td><td align = left><font face = arial size = 1>" & strDetention & " " & strRejected & " </td><td align = left><font face = arial size = 1>" & strDays & " </td></tr> "
	elseif strdays > 5 and strdays < 9 then
 	strbody2 = strbody2 & " <tr bgcolor=#FFFF99><td align = left><font face = arial size = 1> " & strSpecies &  "</td><td align = left><font face = arial size = 1>" & strDateReceived & "</td><td align = left><font face = arial size = 1>"  & strTrailer & "</td><td align = left><font face = arial size = 1>"  & strCarrier & "</td><td align = left><font face = arial size = 1>" & strVendor & "</td><td align = left><font face = arial size = 1>" & strDetention & " " & strRejected & "</td><td align = left><font face = arial size = 1>" & strDays & " </td></tr> "
	else
 	strbody2 = strbody2 & " <tr bgcolor=#FCDCE6><td align = left><font face = arial size = 1> " & strSpecies &  "</td><td align = left><font face = arial size = 1>" & strDateReceived & "</td><td align = left><font face = arial size = 1>"  & strTrailer & "</td><td align = left><font face = arial size = 1>"  & strCarrier & "</td><td align = left><font face = arial size = 1>" & strVendor & "</td><td align = left><font face = arial size = 1>" & strDetention & " " & strRejected & "</td><td align = left><font face = arial size = 1>" & strDays & " </td></tr> "

	end if 
gcount = 0

end if

If Myrec.fields("Carrier") = "RAIL" or MyRec.fields("Carrier") = "Rail" then
	gTotaldaysR = gTotaldaysR + strdays    
  gcountR = gcountR + 1
       gTcountR = gTcountR + 1
 		gSpecies = MyRec.fields("Species")
 		strMoneyR = strMoneyR + cint(strDetention)
 		strTMoneyR = strTmoneyR + strMoney
 		gExcessR = gExcessR + strExcess
else


 	gTotaldays = gTotaldays + strdays    
  gcount = gcount + 1
       gTcount = gTcount + 1
 		gSpecies = MyRec.fields("Species")
 	 
 		strMoney = strMoney + cint(strDetention)
 		strTMoney = strTmoney + strMoney
 		gExcess = gExcess + strExcess
 		
 		end if
       ii = ii + 1
       MyRec.MoveNext
            
  
		 		WEND
           		MyRec.Close
           		Dim strMdate
           		strMdate = formatdatetime(Now(),2)
           		
    strsql = "SELECT tblCars.* FROM tblCars WHERE (month(Date_unloaded) = month('" & strMdate & "') and year(Date_unloaded) = year('" & strMdate & "') and species <> 'KDF' and Species <> 'Display Components') "_
    &" or (((tblCars.Date_received) Is Not Null) AND ((tblCars.Location)='Yard') AND Species <> 'KDF'  and Species <> 'Display Components' AND ((tblCars.Trailer) Is Not Null)) "  
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")    
   				While not MyRec.EOF 
   				

  if isnull(MyRec.fields("Transfer_date")) then
        
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

             else
        
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Trans_Carrier") & "' "
             end if  
             
             
             
   If isnull(Myrec.fields("Date_unloaded")) then 
                  strDays = Fix(Now()- cdate(MyRec.fields("Date_received")))
                else 
              strdays =  Fix(datediff("d", Myrec.fields("date_received"), Myrec.fields("Date_unloaded")))
				
                  end if 
                  
                  
                   
    If MyRec.fields("Carrier") = "RAIL"  or MyRec.fields("Carrier") = "Rail" Then
    
         strdays = int(strdays)
          if strdays = 0 then
          strDetention = 0
          elseif strdays = 1 then
          strDetention = 27.00
          elseif strdays = 2 then
          strDetention = 53.00
          
          elseIf strdays = 3 then 
          strDetention = 80.00
          elseif strdays = 4 then
          strDetention = 106.00
          elseif strdays = 5 then
          strDetention = 145.00
          elseif strdays = 6 then
          strDetention = 185.00
          elseif strdays = 7 then
          strDetention = 262.00
          elseif strdays = 8 then 
          strDetention = 339.00
          elseif strdays > 8 then
          strDetention = round(339.48 + (77.45*(strdays-8)),0)
          end if
    	if strdays  > 2 then 
    	strExcess = 1
  			else            
       		
			strExcess = 0
	 end if 
	 
If left(MyRec("Trailer"), 4) = "GACX" then
strDetention = 0
strExcess = 0
end if           
  else                   
          
 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    
    end if  
    MyRec3.close
    if strDays  > strFree then 
   strDetention =   (int(strDays) * strFee) - (strFee * strFree) 
   strExcess = 1 
   else
   strDetention = 0
   strExcess = 0
   end if
 end if  
 If MyRec.fields("Carrier") = "Rail" or MyRec.fields("Carrier") = "RAIL" then
	strMTDDetentionR  = strMTDDetentionR + strDetention 	
  	gMTDExcessR = gMTDExcessR + strExcess
 	gMTDTotaldaysR = gMTDTotaldaysR + strdays    
 	gMTDTotalCountR = gMTDTotalCountR + 1 
 	else
 		strMTDDetention  = strMTDDetention + strDetention 	
  	gMTDExcess = gMTDExcess + strExcess
 	gMTDTotaldays = gMTDTotaldays + strdays    
 	gMTDTotalCount = gMTDTotalCount + 1 
 	end if
 		
     
       MyRec.MoveNext



            
  
		 		WEND
           		MyRec.Close      
        		
           		strNow = formatdatetime(now(),0)
           		gAvgDays = round((gTotaldays/gTcount),1)
           		if gTcountR > 0 then 
           		gAvgDaysR = round((gTotaldaysR/gTcountR),1)
           		else
           		gAvgDaysR = 0
           		end if
           		if gMTDTotalCountR > 0 then
           		gMTDAvgDaysR = round((gMTDTotaldaysR/gMTDTotalCountR),1)
           		else
           		gMTDAvgDaysR = 0
           		end if
           			gMTDAvgDays = round((gMTDTotaldays/gMTDTotalCount),1)
	
	strBody2 = strbody2 & "Total " & gSpecies & ": " & gcount 
		
		strBody3 = "<p>On Yard Trailer Totals</p><table border=0  cellpadding=0 cellspacing=0 width=800><tr><font face = arial size = 1><td>Total Count</td><td>Detention Total</td><td>On Avg Days</td><td># Loads in Excess of Free Days</td></tr>"
						
	strBody3 = strbody3 & "<tr><font face = arial size = 1><td>" & gtcount & "</td><td>$" & formatNumber(strMoney,0) & "</td><td>" & gAvgDays & "</td><td>" & gExcess & "</td></tr>"
	
				strBody3 = strBody3 & "<tr><font face = arial size = 1><td>&nbsp;</td><td>MTD Detention</td><td>MTD Average Days</td><td>MTD # Loads in Excess of Free Days</td></tr>"
				
					strBody3 = strBody3 & "<tr><font face = arial size = 1><td>&nbsp;</td><td> $" & formatNumber(strMTDDetention,0) & "</td><td>" & gMTDAvgDays & "</td><td>" & gMTDExcess & "</td></tr></table>"
		
				strBody3 = strbody3 & "<p>On Yard Rail Totals</p><table border=0  cellpadding=0 cellspacing=0 width=800><tr><font face = arial size = 1><td>Total Count</td><td>Demurrage Total</td><td>On Avg Days</td><td># Loads in Excess of Free Days</td></tr>"
						
	strBody3 = strbody3 & "<tr><font face = arial size = 1><td>" & gtcountR & "</td><td>$" & formatNumber(strMoneyR,0) & "</td><td>" & gAvgDaysR & "</td><td>" & gExcessR & "</td></tr>"
	
				strBody3 = strBody3 & "<tr><font face = arial size = 1><td>&nbsp;</td><td>MTD Demurrage</td><td>MTD Average Days</td><td>MTD # Loads in Excess of Free Days</td></tr>"
				
					strBody3 = strBody3 & "<tr><font face = arial size = 1><td>&nbsp;</td><td> $" & FormatNumber(strMTDDetentionR,0) & "</td><td>" & gMTDAvgDaysR & "</td><td>" & gMTDExcessR & "</td></tr></table>"

strbody1 = "<p><font face = arial size = 2 color=navy><b> NOTE: Because trailers are constantly arriving and being emptied, the below Information is only valid for a <br>short time following the time listed above.  Please re-run the yard reports from the Fiber Logistics System<br> which can be accessed from the Fiber team web page when needed for decision making.  Contact Steven <br>Day x2181 for help with these reports.</b></p>"
		objMail.HTMLBody = "<font face = arial size = 2> Fiber Yard report for " &  strNow  & " <br><br>" & strbody1 & strbody3 & strbody2




			' objMail.Send
			Set objMail = nothing
		
			strsql = "Update tblYardEmailDate set  Email_Date = '" & strNow & "', Ename = '" & Session("EmployeeID") & "' where E_Key = 1"

	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
			    
strNow = formatdatetime(Now(),2)
strnow = dateadd("d", -7, strNow)
strnow = datepart("y", strnow)
'strnow = 0
stryear = datepart("yyyy", now())

strmonth = datepart("m", now())

strsql = "SELECT Sum(tblCars.Net) AS SumOfNet, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE RC_Transload Is Null AND Datepart(m, inv_depletion_date) = '" & strMonth & "' and  datepart(yyyy, inv_depletion_date) = '" & stryear & "' "_
&" AND (Species='KCOP' Or Species='PMX' Or Species='OF' or Species = 'OF3' or Species = 'KBLD' or Species = 'SHRED' or Species = 'HBX')"

   	 Set MyRec2 = Server.CreateObject("ADODB.Recordset")
   	 MyRec2.Open strSQL, Session("ConnectionString")

If not MyRec2.eof then
if len(MyRec2("SumofNet")) > 0 then
strKCOPNet = round(MyRec2("SumofNet"),0)
else
strKCOPNet = 0
end if
strKCOPLoads = MyRec2("CountofCID") 
else
strKCOPNet = 0
strKCOPLoads = 0
end if
MyRec2.close

strsql = "SELECT Sum(tblCars.Net) AS SumOfNet, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE RC_Transload Is Null AND Datepart(m, inv_depletion_date) = '" & strMonth & "' and  datepart(yyyy, inv_depletion_date) = '" & stryear & "' "_
&" AND  (Species='OF' or Species = 'OF3')"

   	 Set MyRec2 = Server.CreateObject("ADODB.Recordset")
   	 MyRec2.Open strSQL, Session("ConnectionString")

If not MyRec2.eof then
if len(MyRec2("SumofNet")) > 0 then
strOFNet = round(MyRec2("SumofNet"),0)
else
strOFNet = 0
end if
strOFLoads = MyRec2("CountofCID") 
else
strOFNet = 0
strOFLoads = 0
end if
MyRec2.close

if strKCOPnet > 0 then
strOFPct =round((strOFnet/strKCOPNet)*100,0) & "%"
else
strOFPct = 0 & "%"
end if



               strEmailTo = "<EMAIL>"   
                    strEBCC = "<EMAIL>"
            '  strEmailTo = "<EMAIL>"   
                 
			Set objMail = Server.CreateObject("CDO.Message")		
			objMail.From = "<EMAIL>"
			objMail.To = strEmailTo
			'objMail.to = strEBCC	
			objMail.BCC = strEBCC
	
			objMail.Subject = "OF Recap"
			
strBody2 = "<table border=0  cellpadding=0 cellspacing=0 width=800><tr><td align = center><font face = arial size = 2>&nbsp; Date Consumed&nbsp; </td><td align = left><font face = arial size = 2># Loads&nbsp; </td><td align = left><font face = arial size = 2> % OF Total </td><td align = left><font face = arial size = 2> OF Tons </td><td align = left><font face = arial size = 2> Total RFF Tons</tr>"
			

strsql2 = "SELECT datepart(y, [Inv_depletion_date]) AS Expr1, Datepart(d, inv_depletion_date) as Inv_Date, Datepart(m, inv_depletion_date) as Inv_month, Sum(tblCars.Net) AS SumOfNet, Count(tblCars.CID) AS CarCount FROM tblCars "_
&" WHERE (Species='OF'  or Species = 'KCOP' or Species = 'PMX' or Species = 'KBLD' or Species = 'SHRED' or Species = 'OF3' or Species = 'HBX')  and RC_transload is null"_
&" GROUP BY datepart(y, [Inv_depletion_date]), Datepart(d, inv_depletion_date), Datepart(m, inv_depletion_date), datepart(yyyy, inv_depletion_date) "_
&" HAVING datepart(y, [Inv_depletion_date]) > '" & strNow & "'  and datepart(yyyy, inv_depletion_date) = '" & stryear & "' "_
&" ORDER BY datepart(y, [Inv_depletion_date])"

   	 Set MyRec2 = Server.CreateObject("ADODB.Recordset")
   	 MyRec2.Open strSQL2, Session("ConnectionString")

If not MyRec2.eof then
				While not MyRec2.EOF
strConsumptionDate = MyRec2.fields("Inv_month")  & "/" & MyRec2.fields("Inv_date")
strKcopTons = round(myRec2.fields("SumofNet"),0)
strday = MyRec2.fields("Inv_date")
strMonth = MyRec2.fields("Inv_month")		
			
strsql = "SELECT datepart(y, [Inv_depletion_date]) AS Expr1, Datepart(d, inv_depletion_date) as Inv_Date, Datepart(m, inv_depletion_date) as Inv_month, Sum(tblCars.Net) AS SumOfNet, Count(tblCars.CID) AS CarCount FROM tblCars "_
&" WHERE (Species='OF' or Species = 'OF3') and RC_transload is null"_
&" GROUP BY datepart(y, [Inv_depletion_date]), Datepart(d, inv_depletion_date), Datepart(m, inv_depletion_date), datepart(yyyy, inv_depletion_date) "_
&" HAVING Datepart(d, inv_depletion_date) = '" & strDay & "' and Datepart(m, inv_depletion_date) = '" & strMonth & "' and datepart(yyyy, inv_depletion_date) = '" & stryear & "'"

   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")	 
  	 
			
If not MyRec.eof then
     
    strOFTons = round(MyRec.fields("SumofNet"),0) 
     strLoads = MyRec.fields("CarCount")  
     strpct = round((strOFtons/strKcopTons)*100,0) & "%"
	else
	  

     strOFTons = "0" 
    strLoads = "0" 
    strpct = "0%"
	
	end if
	MyRec.close		

  strbody2 = strbody2 & " <tr><td align = center><font face = arial size = 2> " & strConsumptiondate &  "</td><td align = left><font face = arial size = 2>" & strLoads & "</td><td align = left><font face = arial size = 2>"  & strpct & "</td><td align = left><font face = arial size = 2>"  & strOFtons & "</td><td align = left><font face = arial size = 2>"  & strKCOPtons & "</td></tr> "
		    

	MyRec2.MoveNext
     			WEND
           		MyRec2.Close
           	
	 strbody2 = strbody2 & " <tr><td colspan=5>&nbsp;</td></td></tr> "
	
       	
  strbody2 = strbody2 & " <tr><td align = center><font face = arial size = 2><b>MTD:</td><td align = left><font face = arial size = 2><b>" & strOFLoads & "</td><td align = left><font face = arial size = 2><b>"  & strOFpct & "</td><td align = left><font face = arial size = 2><b>"  & strOFNet & "</td><td align = left><font face = arial size = 2><b>"  & strKCOPNet & "</td></b></tr> "
	

	objMail.HTMLBody = "<font face = arial size = 2>The following is a recap of the OF loads consumed in the past week:<br><br><p align=left><b>Note:  These numbers are based on the trailers/cars outed on shift by Fiber technicians.  The numbers can change based on validation work or subsequent changes to entries. </p></b><br><br>" & strbody2



			' objMail.Send
			Set objMail = nothing
	end if




strmonth = datepart("m", now())


strsql = "SELECT Sum(tblCars.Net) AS SumOfNet, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE RC_Transload Is Null AND Datepart(m, inv_depletion_date) = '" & strMonth & "' and  datepart(yyyy, inv_depletion_date) = '" & stryear & "' "_
&" AND (Species='OCC' Or Species='MXP')"

   	 Set MyRec2 = Server.CreateObject("ADODB.Recordset")
   	 MyRec2.Open strSQL, Session("ConnectionString")

If not MyRec2.eof then
	if len(MyRec2("SumofNet")) > 0 then 

	strOCCNet = round(MyRec2("SumofNet"),0)
	else
	strOCCNet = 0
	end if
strOCCLoads = MyRec2("CountofCID") 
else
strOCCNet = 0
strOCCLoads = 0
end if
MyRec2.close

strsql = "SELECT Sum(tblCars.Net) AS SumOfNet, Count(tblCars.CID) AS CountOfCID FROM tblCars "_
&" WHERE RC_Transload Is Null AND Datepart(m, inv_depletion_date) = '" & strMonth & "' and  datepart(yyyy, inv_depletion_date) = '" & stryear & "' "_
&" AND  Species='MXP'"

   	 Set MyRec2 = Server.CreateObject("ADODB.Recordset")
   	 MyRec2.Open strSQL, Session("ConnectionString")

If not MyRec2.eof then
if len(MyRec2("SumofNet")) > 0 then 
strMXPNet = round(MyRec2("SumofNet"),0)
end if
strMXPLoads = MyRec2("CountofCID") 
else
strMXPNet = 0
strMXPLoads = 0
end if
MyRec2.close

if strOCCnet > 0 then
strMXPPct =round((strMXPnet/strOCCNet)*100,0) & "%"
else
strMXPPct = 0 & "%"
end if




               strEmailTo = "<EMAIL>"   
                    strEBCC = "<EMAIL>"
                 
              '   strEmailTo = "<EMAIL>"
			Set objMail = Server.CreateObject("CDO.Message")		
			objMail.From = "<EMAIL>"
			objMail.To =   strEmailTo
			'objMail.CC = strECC
			objMail.BCC = strEBCC
			objMail.Subject = "MXP Recap"
			
strBody2 = "<table border=0  cellpadding=0 cellspacing=0 width=800><tr><td align = center><font face = arial size = 2>&nbsp;<b> Date Consumed&nbsp; </td><td align = left><font face = arial size = 2><b># Loads&nbsp; </td><td align = left><font face = arial size = 2> <b>% OCC Total </td><td align = left><font face = arial size = 2><b> MXP Tons </td><td align = left><font face = arial size = 2> <b>Total OCC Tons</b></tr>"
			

strsql2 = "SELECT datepart(y, [Inv_depletion_date]) AS Expr1, Datepart(d, inv_depletion_date) as Inv_Date, Datepart(m, inv_depletion_date) as Inv_month, Sum(tblCars.Net) AS SumOfNet, Count(tblCars.CID) AS CarCount FROM tblCars "_
&" WHERE (Species='OCC'   or Species = 'MXP') and RC_transload is null"_
&" GROUP BY datepart(y, [Inv_depletion_date]), Datepart(d, inv_depletion_date), Datepart(m, inv_depletion_date), datepart(yyyy, inv_depletion_date) "_
&" HAVING datepart(y, [Inv_depletion_date]) > '" & strNow & "' and datepart(yyyy, inv_depletion_date) = '" & stryear & "' "_
&" ORDER BY datepart(y, [Inv_depletion_date])"

   	 Set MyRec2 = Server.CreateObject("ADODB.Recordset")
   	 MyRec2.Open strSQL2, Session("ConnectionString")

If not MyRec2.eof then
				While not MyRec2.EOF
strConsumptionDate = MyRec2.fields("Inv_month")  & "/" & MyRec2.fields("Inv_date")
strKcopTons = round(myRec2.fields("SumofNet"),0)
strday = MyRec2.fields("Inv_date")
strMonth = MyRec2.fields("Inv_month")		
			
strsql = "SELECT datepart(y, [Inv_depletion_date]) AS Expr1, Datepart(d, inv_depletion_date) as Inv_Date, Datepart(m, inv_depletion_date) as Inv_month, Sum(tblCars.Net) AS SumOfNet, Count(tblCars.CID) AS CarCount FROM tblCars "_
&" WHERE Species='MXP' and RC_transload is null "_
&" GROUP BY datepart(y, [Inv_depletion_date]), Datepart(d, inv_depletion_date), Datepart(m, inv_depletion_date), datepart(yyyy, inv_depletion_date) "_
&" HAVING Datepart(d, inv_depletion_date) = '" & strDay & "' and Datepart(m, inv_depletion_date) = '" & strMonth & "' and datepart(yyyy, inv_depletion_date) = '" & stryear & "'"

   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")	 
  	 
			
If not MyRec.eof then
     
    strOFTons = round(MyRec.fields("SumofNet"),0) 
     strLoads = MyRec.fields("CarCount")  
     strpct = round((strOFtons/strKcopTons)*100,0) & "%"
	else
	  

     strOFTons = "0" 
    strLoads = "0" 
    strpct = "0%"
	
	end if
	MyRec.close		

  strbody2 = strbody2 & " <tr><td align = center><font face = arial size = 2> " & strConsumptiondate &  "</td><td align = left><font face = arial size = 2>" & strLoads & "</td><td align = left><font face = arial size = 2>"  & strpct & "</td><td align = left><font face = arial size = 2>"  & strOFtons & "</td><td align = left><font face = arial size = 2>"  & strKCOPtons & "</td></tr> "
		    

	MyRec2.MoveNext
     			WEND
           		MyRec2.Close
    
 strbody2 = strbody2 & " <tr><td colspan=5>&nbsp;</td></td></tr> "
	
       	
  strbody2 = strbody2 & " <tr><td align = center><font face = arial size = 2><b>MTD:</td><td align = left><font face = arial size = 2><b>" & strMXPLoads & "</td><td align = left><font face = arial size = 2><b>"  & strMXPpct & "</td><td align = left><font face = arial size = 2><b>"  & strMXPNet & "</td><td align = left><font face = arial size = 2><b>"  & strOCCNet & "</td></b></tr> "
	
	objMail.HTMLBody = "<font face = arial size = 2>The following is a recap of the MXP loads consumed in the past week:<br><br><p align=left><b>Note:  These numbers are based on the trailers/cars outed on shift by Fiber technicians.  The numbers can change based on validation work or subsequent changes to entries. </p></b><br><br>" & strbody2

end if

		' 	objMail.Send
			Set objMail = nothing

if request.querystring("s") = "i" then
 Response.redirect("Send_yard_email.asp")
else
    Response.redirect("KDF_email.asp")
end if
 %>
