 

<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">
<TITLE>Audited Vendor Weights</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

<%  
	Dim strsQL, MyRec, objGeneral, objNew, strMonth, strDigit, rstMonth, strCount, strKC<PERSON>, MyRec1, strAudited, strRS, strRSW, strPO


 	Dim  strdate, strDelDate

strKCW = 1

strdate = formatdatetime(now(),2)

   set objGeneral = new ASP_CLS_General

call Getdata()
strMonth = request.form("Month")


%>
<style type="text/css">
.style1 {
	font-family: Arial;
	font-size: x-small;
}
.style2 {
	font-family: Arial;
}
</style>
</head>

<form name="form1" action="Vendor_weights.asp" method="post">
<TABLE cellSpacing=0 cellPadding=1 width=100%  border=0 bgcolor="#FFFFE8">  
  <tr><td  colspan = "2" align = center>
	<p align="left"><b><font face="Arial">Audited Vendor Weights</font></b></td>

	<td colspan = 3 align = "RIght">	<font face="Arial"><b><a href="Loginscreen.asp">HOME</a></b></font></td></tr>


  <TR>
         <TD align = center width="16%">
			<p align="right"><b><font face="Arial">Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </font></b></TD>
    <TD align = left >     
    <font face="Arial">
   <select size="1" name="Month">

     <option value="">--- All ---</option>
       <%= objGeneral.OptionListAsString(rstMonth, "Import_month", "Import_month", strMonth) %>
     </select></font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	</TD>
         <TD align = center >
			<p class="style2"><strong>PO# </strong>&nbsp;<font face="Arial" size="2"><input name="PO" size="25" value="<%= strPO%>" ></font></TD>
    <TD align = left width="17%">     &nbsp;</TD>

    <TD align="right" >
    <Input name="Update" type="submit" Value="Search" style="float: left" >&nbsp;&nbsp;&nbsp;

</TD>

</TR>
  </TABLE>
</form>
  <% 

  if objGeneral.IsSubmit() Then
  strMonth = Request.form("Month")
  strPO = request.form("PO")
  
    If request.form("Month") = "" and len(Request.form("PO")) > 2  then
 strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars INNER JOIN tblOrder ON tblCars.OID = tblOrder.OID where tblCars.PO = '" & strPO & "' "
 elseif request.form("Month") = "" then
  strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars INNER JOIN tblOrder ON tblCars.OID = tblOrder.OID "
 elseif len(request.form("Month")) > 1 and  len(Request.form("PO")) > 2  then
 strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars INNER JOIN tblOrder ON tblCars.OID = tblOrder.OID WHERE tblOrder.Import_month ='" & strMonth & "' and tblCars.PO = '" & strPO & "'"
else
 strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars INNER JOIN tblOrder ON tblCars.OID = tblOrder.OID WHERE tblOrder.Import_month ='" & strMonth & "' "


end if


   Set MyRec1 = Server.CreateObject("ADODB.Recordset")
   MyRec1.Open strSQL, Session("ConnectionString") 
   strcount = MyRec1.fields("CountofCID")
   MyRec1.close
   
         If request.form("Month") = "" and len(Request.form("PO")) > 2  then
 strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars INNER JOIN tblOrder ON tblCars.OID = tblOrder.OID where (Audit_tons > 0 and tblCars.PO = '" & strPO & "' ) or (KC_Weighed = 'YES' and Tons_Received <> 21 and tblCars.PO = '" & strPO & "')"
  elseif request.form("Month") = "" then
 strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars INNER JOIN tblOrder ON tblCars.OID = tblOrder.OID where Audit_tons > 0 or (KC_Weighed = 'YES' and Tons_Received <> 21)"
   elseif len(request.form("Month")) > 1 and  len(Request.form("PO")) > 2  then
  strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars INNER JOIN tblOrder ON tblCars.OID = tblOrder.OID WHERE (tblOrder.Import_month ='" & strMonth & "' and Audit_tons > 0 and tblCars.PO = '" & strPO & "') "_
  &"  or (tblOrder.Import_month ='" & strMonth & "' and KC_Weighed = 'YES' and Tons_Received <> 21 and tblCars.PO = '" & strPO & "')"

 else
 strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars INNER JOIN tblOrder ON tblCars.OID = tblOrder.OID WHERE (tblOrder.Import_month ='" & strMonth & "' and Audit_tons > 0) or (tblOrder.Import_month ='" & strMonth & "' and KC_Weighed = 'YES' and Tons_Received <> 21)"


end if


   Set MyRec1 = Server.CreateObject("ADODB.Recordset")
   MyRec1.Open strSQL, Session("ConnectionString") 
   strRSW = MyRec1.fields("CountofCID")
   MyRec1.close
   
   
            If request.form("Month") = "" and len(Request.form("PO")) > 2  then
             strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars INNER JOIN tblOrder ON tblCars.OID = tblOrder.OID where tblCars.Weigh_required='W' and tblCars.PO = '" & strPO & "'"
 
  elseif request.form("Month") = "" then

 strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars INNER JOIN tblOrder ON tblCars.OID = tblOrder.OID where tblCars.Weigh_required='W'"
    elseif len(request.form("Month")) > 1 and  len(Request.form("PO")) > 2  then
 strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars INNER JOIN tblOrder ON tblCars.OID = tblOrder.OID WHERE tblOrder.Import_month ='" & strMonth & "' and tblCars.Weigh_required='W' and tblCars.PO = '" & strPO & "'"

 
 else
 strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars INNER JOIN tblOrder ON tblCars.OID = tblOrder.OID WHERE tblOrder.Import_month ='" & strMonth & "' and tblCars.Weigh_required='W'"


end if


   Set MyRec1 = Server.CreateObject("ADODB.Recordset")
   MyRec1.Open strSQL, Session("ConnectionString") 
   strAudited = MyRec1.fields("CountofCID")
   MyRec1.close

 If request.form("Month") = "" and len(Request.form("PO")) > 2  then
  strsql = "SELECT tblCars.*, tblOrder.release, tblorder.req_ship_week, tblorder.import_month FROM tblCars INNER JOIN tblOrder ON tblCars.OID = tblOrder.OID WHERE tblCars.Weigh_required='W' and tblCars.PO = '" & strPO & "'"
 
  elseif request.form("Month") = "" then
strsql = "SELECT tblCars.*, tblOrder.release, tblorder.req_ship_week, tblorder.import_month FROM tblCars INNER JOIN tblOrder ON tblCars.OID = tblOrder.OID WHERE tblCars.Weigh_required='W' "
     elseif len(request.form("Month")) > 1 and  len(Request.form("PO")) > 2  then
  strsql = "SELECT tblCars.*, tblOrder.release, tblorder.req_ship_week, tblOrder.import_month FROM tblCars INNER JOIN tblOrder ON tblCars.OID = tblOrder.OID WHERE tblOrder.Import_month='" & strMonth & "' AND tblCars.Weigh_required='W' and tblCars.PO = '" & strPO & "'"
  


 else
  strsql = "SELECT tblCars.*, tblOrder.release, tblorder.req_ship_week, tblOrder.import_month FROM tblCars INNER JOIN tblOrder ON tblCars.OID = tblOrder.OID WHERE tblOrder.Import_month='" & strMonth & "' AND tblCars.Weigh_required='W'"
  end if


   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 



%>
   <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=96% bgcolor="#FFFFCE" border=1 align = center>
    <tr><td colspan="7" bgcolor="white"><font face="Arial" size="2"><b>Search Results</b> </td>
    <td  align="center"><font face="Arial" size = 2>Total<br> Received:<br>&nbsp;<%=strCount%></font></td>
    <td  align="center"><font face="Arial" size = 2># Selected<br>for Audit<br>&nbsp;<%=strAudited%></font></td>
  <td  align="center"><span class="style1"> % Selected <br>for Audit</span><font face="Arial" size = 2>:<br>&nbsp;
  <% if strCount > 0 then %>
  <%= round((strAudited/strcount)*100, 1)%>
  <% else %>
  0
  <% end if %></font></td>

        <td  align="center"><font face="Arial" size = 2>RS <br>Weighed:<br>&nbsp;<%=strRSW %><font></td>
<% if strAudited = 0 then
strRS = 0
else
strRS = round((strRSW/strAudited)*100,1)
end if %>
    <td  align="center"><font face="Arial" size = 2>RS Performance<br> (Target 100%):<br>&nbsp;<%=strRS%></font></td>

    </tr>

      <tr class="tableheader">
 	<td  align="center"><font face="Arial" size = 1>Species</font></td>
	<td  align="center"><font face="Arial" size = 1>PO</font></td>
	<td  align="center"><font face="Arial" size = 1>Trailer</font></td>
		<td  align="center"><font face="Arial" size = 1>Carrier</font></td>
	<td  align="center"><font face="Arial" size = 1>Date<br> Received</font></td>
	<td  align="center"><font face="Arial" size = 1>Date<br> Unloaded</font></td>
	<td  align="center"><font face="Arial" size = 1>Release</font></td>
	<td  align="center"><font face="Arial" size = 1>Vendor</font></td>
	<td  align="center"><font face="Arial" size = 1>Generator</font></td>
	<td  align="center"><font face="Arial" size = 1>Month</font></td>
	<td  align="center"><font face="Arial" size = 1>Tons</font></td>
	<td  align="center"><font face="Arial" size = 1>Audit <br>Tons</font></td>


      
  	<% 
      Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr class=tablecolor1>
    <% else %>
       <tr class=tablecolor2>
    <% end if %>

<td  align="center"><font face = "arial" size = "1"><%=MyRec.fields("Species")%>&nbsp;</td>


<td  align="center"><font face = "arial" size = "1"><%=MyRec.fields("PO")%>&nbsp;</td>

<td  align="left"><font face = "arial" size = "1"><%=MyRec.fields("Trailer")%>&nbsp;</td>
<td  align="left"><font face = "arial" size = "1"><%=MyRec.fields("Carrier")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=MyRec.fields("date_received")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%= MyRec.fields("date_unloaded")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=MyRec.fields("Release")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=MyRec.fields("Vendor")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=MyRec.fields("Generator")%>&nbsp;</td>

<td  align="center"><font face = "arial" size = "1"><%=MyRec.fields("Import_month")%>&nbsp;</td>
<td  align="right"><font face="Arial" size = 1><% if isnull(MyRec.fields("Tons_received")) then %> .00 <% else %> <%= formatnumber(MyRec.fields("Tons_received"),2)%><% end if %>&nbsp;</font></td>
<td  align="right"><font face="Arial" size = 1><% if isnull(MyRec.fields("Audit_tons")) then %> .00 <% else %> <%= formatnumber(MyRec.fields("Audit_tons"),2)%><% end if %>&nbsp;</font></td>




  
   </tr>
    <% 
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
   </table>

<table>    <tr><td colspan="7" bgcolor="white" align="right">&nbsp;</td></tr></table>

<%
MyRec.close
end if
 %>
 
  <% Function GetData()

   set objNew = new ASP_Cls_Fiber     
	
	set rstMonth= objNew.FiberMonth()
	strPO = Request.form("PO")
End Function




 %><!--#include file="Fiberfooter.inc"-->