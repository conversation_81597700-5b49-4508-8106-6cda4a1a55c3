
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Add Team</TITLE>

<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->

<style type="text/css">
.style1 {
	border: 1px solid #C0C0C0;
	font-family: arial;
		font-size: x-small;
	text-align: center;
}
.style3 {
	border: 1px solid #000000;
	background-color: #FFFFDD;
}
.style4 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style5 {
	text-align: center;
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style8 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
.style9 {
	border: 1px solid #C0C0C0;
}
.style10 {
	border: 1px solid #C0C0C0;
	font-family: arial;
	font-size: x-small;
}
.auto-style1 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #EDF7FE;
}
.auto-style2 {
	text-align: center;
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #EDF7FE;
}
.auto-style3 {
	border: 1px solid #C0C0C0;
	font-family: arial;
	font-size: x-small;
	background-color: #EDF7FE;
}
.auto-style4 {
	border: 1px solid #C0C0C0;
	background-color: #FFFFDD;
}
</style>
</head>
<% dim strsql, MyRec, strid, strecp, strMill, objGeneral, strarea

strMill = request.querystring("m")
set objGeneral = new ASP_CLS_General
  		 if objGeneral.IsSubmit = True Then
   
  		     strTeam =  Replace(Request.form("Team"), "'", "''")
  		     strAbb = Request.form("Abb")
  		     
  strsql = "Select max(VA_Team_ID) as MaxID from VA_Team_Names"
      Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
			strMax = MyRec("MaxID")
			MyRec.close
			
			strMax = strMax + 1

  
  

			 
 strsql =  "INSERT INTO VA_Team_names (VA_Team_ID, VA_Team_Name, Team_Abb) SELECT  " & strMax & ", '" & strTeam & "', '" & strAbb & "'"
 Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 


Response.redirect("Teams.asp")
end if 

 %><body bgcolor="#FFFFFF"><br>
 <form name="form1" action="Team_Add.asp"  method="post" ID="Form1"  >
  
  <table width = 100% class="auto-style4" cellspacing="1"><tr>
	<td align = left class="auto-style1">
<font face = arial size = 3><strong>Add New Team</strong></font></td>
	<td class="auto-style2">
<a href="javascript:history.go(-1)"><font face = arial size = 2 color = black>
<strong>Return</strong></font></a></td>
<td align = right class="auto-style1"><INPUT TYPE="submit" value="Submit"></td>
</tr></table><br><br>

 
 <table cellpadding="0" bordercolor="#111111" bgcolor="#D9E1F9" align="center" class="style8" style="width: 40%">
   
  <tr>

   
    <td align="center" style="height: 54" class="auto-style3">
	<strong>Team Name</strong></td>

   
    <td align="center" style="height: 54" class="auto-style3">
	<strong>Abb</strong></td>

   
  </tr>
  <tr>
 
    <td  bgcolor="#FFFFFF" class="style1" style="height: 94px"  >
	<font face="Arial" size="2">
	<input type="text" name="Team" size="55" style="width: 193px" tabindex="1" ></font></td>
	
    <td  bgcolor="#FFFFFF" class="style1" style="height: 94px"  >
	<font face="Arial" size="2">
	<input type="text" name="Abb" size="55" style="width: 67px" tabindex="1" ></font></td>
	
  </table> 
	<p>&nbsp;</p>
	<p>&nbsp;</p>
	</form>
 
 
<!--#include file="footer.inc"-->