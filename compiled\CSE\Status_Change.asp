<html>
<!--#include file="classes/asp_cls_SessionString.asp"-->


<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Update Entry Status</title>
</head>
<% dim strsql, MyRec, strid, strstatus


	

strid = Request.querystring("p")
strstatus = Request.querystring("id")
If request.querystring("id") = "Dormant" then
strstatus = "Active"
elseif request.querystring("id") = "Active" or request.querystring("id") = "active" then
strstatus = "Complete"
elseif request.querystring("id") = "Complete" then
strstatus = "Dormant"
end if



If strstatus = "Active" then 

 strsql =  "Update tblPermit set Entry_Status = '" & strstatus & "' where PID = " & strid & ""
 
 else
 strsql =  "Update tblPermit set Entry_Status = '" & strstatus & "', Entry_approval = 'No' where PID = " & strid & ""
 
 end if
 
 Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 
			
	Dim strdate, strBName
	If len(Session("Ename")) > 2 then
	strBName = Session("Ename")
	else
	strBName = ""
	end if
	strnow = dateadd("h", -5, now())
strDate = formatdatetime(strnow,2)
	
strsql = "Insert into tblPermitHistory	(PID, BID, Activity_Date, Status, BID_Name) "_
&" Select " & strid & ", '" & Session("EmployeeID") & "', '" & strdate & "', '" & strstatus & "', '" & strBName & "'"	
		 Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 
	
Response.redirect("Guards.asp")

 %>
