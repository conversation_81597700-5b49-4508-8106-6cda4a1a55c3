
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">


<TITLE>Invalid Admin Delete</TITLE>

</head>

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<%      
    	Function ReturnEmail(strCheck)
		Dim objADUserQuery,objADU
		Dim strFirstName, strLastName
		Set objADUserQuery = New ActiveDirectoryUserQuery
		objADUserQuery.targetDomain = "kcc.com"
			objADUserQuery.addFilter "cn",strCheck
		objADUserQuery.executeQuery
		If(objADUserQuery.resultsCount = 0) Then
			ReturnEmail=""
		ElseIf(objADUserQuery.resultsCount = 1) Then
			set objADU=objADUserQuery.results(0)
			ReturnEmail=objADU.mail
		Else
			ReturnEmail=""
		end if
		Set objADUserQuery = Nothing
		Set objADU=Nothing
	End Function

  
 strSQL = "SELECT EID, Firstname, Lastname from tblAdmin where len(EID) < 8 order by Lastname "
		   
			Set MyRec = Server.CreateObject("ADODB.Recordset")
			
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
	
If not MyRec.eof then
			While not MyRec.EOF
			
		
			
                 	 strEmailTo = "Unknown Owner"
                 	 strTo = MyRec.fields("EID")
 		 			
                 	 strOwner = MyRec.Fields("Firstname")
                 	 strMill = MyRec.Fields("Lastname")

                  	strEmailTo = ReturnEmail(strTo)
                  	If strEmailTo = "" then 
       
strsql2 = "Delete from tblAdmin where EID = '" & strTo & "'"

	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL2
			MyConn.Close

               
		
			End if
			

	
	            MyRec.MoveNext
     			WEND
     			end if
           		MyRec.Close 
Response.redirect("Invalid_Admin.asp")
%>
 
 
