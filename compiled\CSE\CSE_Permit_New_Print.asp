<html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns="http://www.w3.org/TR/REC-html40" xmlns:m="http://schemas.microsoft.com/office/2004/12/omml">
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 14.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">

<title>This assessment is intended to document a summary of&nbsp; hazards and 
control measures required to permit safe entry</title>
<style type="text/css">
.style2 {
	font-family: 'Arial Narrow';
	font-size: large;
}
.style3 {
	font-family: Arial;
	font-size: x-small;
}
.style4 {
	border-width: 1px;
}
.style5 {
	border-style: solid;
	border-width: 1px;
	font-family: 'Arial Narrow';
	font-size: large;
}
.style6 {
	border-style: solid;
	border-width: 1px;
	border-color: black;
}
.style7 {
	border-style: solid;
	border-width: 1px;
	font-family: 'Arial Narrow';
	font-size: x-small;
}
.style8 {
	font-family: 'Arial Narrow';
	font-size: x-small;
}
.style15 {
	font-size: x-small;
	font-weight: bold;
	
}
.style16 {
	font-size: x-small;
	font-family: Arial;
	text-align: center;
}
        .page-break {
            page-break-before: always; /* Forces a page break before the element */
        }
    </style>
</head>

<% 
strid = Request.querystring("id")
strpid = Request.querystring("pid")
strsql = "Select tblPermit.* from tblPermit where PID = " & strPID 

Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
   		
strPurposeofEntry = MyConn.fields("Purpose")
strDateIssued = MyConn.fields("Date_issued")
strTimeIssued = MyConn.fields("Time_issued")

strTimeExpired = MyConn.fields("Time_expired")
Myconn.close

strsql = "SELECT tblHA.*, tblSOP.LOCATION, tblSOP.SDescription FROM tblHA INNER JOIN tblSOP ON tblHA.SpaceID = tblSOP.SOP_NO "_
  &" where SpaceID = '" & strid & "'"
Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
		
strDescofspace = MyCOnn.fields("DescofSpace")
strContents = MyConn.fields("Contents")
strTimesperMonth = MyConn.fields("TimesperMonth")
strTimesperYear = MyConn.fields("TimesperYear")
strOtherTimes = MyConn.fields("Othertimes")
strNext_Audit_date = MyConn.fields("Next_audit_date")


	set objGeneral = new ASP_CLS_General
  		
 Dim counter
 counter = 0


%>
<body onload="if (window.print) {window.print()}">
<table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" align = CENTER>

  <tr>
    <td rowspan="2"><img src="images/KC_Logo_Img.png"></td>
	<td class="style2" width="50%"><b>KIMBERLY-CLARK   NORTH AMERICA</b></font></td>
	<td class="style5" bgcolor="D9E1F2" width="8%" align="center" bordercolor="black">Blue</td>
	<td class="style2">&nbsp;&nbsp;Must be filled out by the <b>Authorizer	</b></font>
</td>
</tr>
	<td class="style2"><b>CONFINED SPACE ENTRY PERMIT-Appendix A</b></td>
	<td class="style5" bgcolor="#E2EFDA" align="center" bordercolor="black">Green</td>
	<td class="style2">&nbsp;&nbsp;Must be filled out by the <b>Attendant	</b></td>

  </tr></table>
  <table bgcolor="D9E1F2" width="100%" class="style6"><tr>
	<td class="style3" width="60%" >Area&nbsp;<u><%= MyConn.fields("Location")%></u></td>
	<td class="style3" width="20%">Date&nbsp;<u><%= strDateIssued %></u></td>
	<td class="style3" width="20%">Time&nbsp;<u><%= strTimeIssued %></u></td>
		
	<tr>
<td class="style3">Confined Space #&nbsp;<u><%= strid %></u></td>
<td class="style3" colspan=2>Confined Space Name&nbsp;<u><%= MyConn.fields("SDescription")%></u></td>	
	</tr>
		<tr>
<td class="style3" >Description of Work to be Done&nbsp;<u><%= strPurposeofEntry %></u></td>

<td class="style3" colspan=2>Permit Expiration Time<u>&nbsp;<%= strTimeExpired %></u></td>

	
	
	</tr>
  </table>
   <table bgcolor="D9E1F2" width="100%" class="style6" ><tr>
<td colspan="4" class="style3" align=left><b>POTENTIAL HAZARDS (Check all that apply)</b></td>

	<td class="style3">______  Sharps</td>
	<td>&nbsp;</td>

	<td class="style3">______  Slippery</td>
	<td>&nbsp;</td></tr>
<tr>
	<td class="style3">______  Biological</td>
	<td>&nbsp;</td>

	<td class="style3">______  Physical</td>
	<td>&nbsp;</td>

	<td class="style3">______  Atmosphere</td>
	<td>&nbsp;</td>

	<td class="style3">______  Communication</td>
	<td>&nbsp;</td>


   </tr>
   <tr>
	<td class="style3">______  Chemical</td>
	<td>&nbsp;</td>

	<td class="style3">______  Thermal</td>
	<td>&nbsp;</td>

	<td class="style3">______  Engulfment</td>
	<td>&nbsp;</td>

	<td class="style3">______  Falls</td>
	<td>&nbsp;</td>
   </tr>
    <tr>
	<td class="style3">______  Mechanical</td>
	<td>&nbsp;</td>

	<td class="style3">______  Electrical</td>
	<td>&nbsp;</td>

	<td class="style3" colspan="4">______  Other ___________________________</td>


   </tr>
   </table>
   <table bgcolor="D9E1F2" width="100%" class="style6" ><tr>
<td colspan="4" class="style3" align=left><b>SAFETY EQUIPMENT REQUIRED (Check all that apply)</b></td>

	<td class="style3" colspan="4">&nbsp;</tr>
<tr>
	<td class="style3">______  Safety Harness</td>
	<td>&nbsp;</td>

	<td class="style3">______  Respiratory Protection</td>
	<td>&nbsp;</td>

	<td class="style3">______  Safety Glasses</td>
	<td>&nbsp;</td>

	<td class="style3">______  Bump Cap</td>
	<td>&nbsp;</td>


   </tr>
   <tr>
	<td class="style3">______  Lifeline</td>
	<td>&nbsp;</td>

	<td class="style3">______  Ventilation</td>
	<td>&nbsp;</td>

	<td class="style3">______  Gloves</td>
	<td>&nbsp;</td>

	<td class="style3">______  Confined Space Cart</td>
	<td>&nbsp;</td>
   </tr>
      <tr>
	<td class="style3">______  Extraction Device</td>
	<td>&nbsp;</td>

	<td class="style3">______  Lighting</td>
	<td>&nbsp;</td>

	<td class="style3">______  Hard Hat</td>
	<td>&nbsp;</td>

	<td class="style3">______  Additional Air Monitor</td>
	<td>&nbsp;</td>
   </tr>
    <tr>
	<td class="style3">______  Dock Safety Struts</td>
	<td>&nbsp;</td>

	<td class="style3">______  Barricade Tape</td>
	<td>&nbsp;</td>

	<td class="style3" colspan="4">______  Other _______________________</td>


   </tr>
   </table>
      <table bgcolor="D9E1F2" width="100%" class="style6"><tr>
	<td colspan="3" class="style3" align=left><b>CSE CHECKLIST: All questions answers must be YES. Provide information where indicated.</b></td>	
	</tr>
	<tr><td width="4%">______</td><td class="style3" width="4%" align="center">1</td>
	<td class="style3">Has the entry procedure for this confined space been reviewed by all involved?&nbsp;&nbsp;&nbsp;&nbsp;Dcoument # __________</td>
	</tr>
	<tr><td width="4%">______</td><td class="style3" width="4%" align="center">2</td>
	<td class="style3">Has the equipment been properly locked out?&nbsp;&nbsp;&nbsp;&nbsp;Document # 
	<%	scounter = 0
			strsql = "SELECT tblPermitECPSOP.PID, tblECP.HAZ_ID, tblECP.ECP_ID, tblECP.Task, tblECP.EID "_
			&" FROM tblPermitECPSOP INNER JOIN tblECP ON tblPermitECPSOP.EID = tblECP.EID "_
			&" where tblPermitECPSOP.PID = " & strPID & ""	
    Set MyConnC = Server.CreateObject("ADODB.Recordset") 
   		MyConnC.Open strSQL, Session("ConnectionString")
  If not MyConnC.eof then   	      
           while not MyConnC.Eof
		    %>
       <font face="Verdana" size="1">
		<% if scounter = 0 then %>
<%= MyConnC.fields("ECP_ID")%>
<% else %>
&nbsp;<%= MyConnC.fields("ECP_ID")%>
<% end if %>
    <% scounter = scounter + 1

       MyConnC.MoveNext
     Wend
     End if
     MyConnc.close
    %>




	</td>
	</tr>
<tr><td width="4%">______</td><td class="style3" width="4%" align="center">3</td>
	<td class="style3">Have SDS's been reviewed for potential hazards of the chemicals existing or to be brought in the space?</td>
	</tr>
	</tr>
<tr><td width="4%">______</td><td class="style3" width="4%" align="center">4</td>
	<td class="style3">Has the Entrant, Attendant, and Authorizer received their annual entry training, and been medically cleared?</td>
	</tr>
	<tr><td width="4%">______</td><td class="style3" width="4%" align="center">5</td>
	<td class="style3">Has all Safety Equipment been inspected?</td>
	</tr>
	<tr><td width="4%">______</td><td class="style3" width="4%" align="center">6</td>
	<td class="style3">Has a pre-entry Safety Briefing been conducted and the rescue plan been reviewed?</td>
	</tr>
	<tr><td width="4%">______</td><td class="style3" width="4%" align="center">7</td>
	<td class="style3">Have "Introduced" or "Adjacent" work hazards been considered? (e.g. pressure washer/generator/lift truck)</td>
	</tr>
	<tr><td width="4%">______</td><td class="style3" width="4%" align="center">8</td>
	<td class="style3">Has the Response Team provided clearance for Entry?&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;  Team member name:____________________</td>
	</tr>
		<tr><td width="4%">______</td><td class="style3" width="4%" align="center">9</td>
	<td class="style3">Communication Method/Procedure assigned? &nbsp;&nbsp;  Entrant:_______&nbsp;&nbsp;  ERT:_______&nbsp;&nbsp; Authorizer:_______</td>
	</tr>
	<tr><td width="4%">______</td><td class="style3" width="4%" align="center">10</td>
	<td class="style3">Has monitor been calibrated & bump tested?</td>
	</tr>
	</table>
	      <table bgcolor="#E2EFDA" width="100%" class="style6" height="55px"><tr>
<td class="style3" width = 100%><b>RESCUE</b>&nbsp;&nbsp;<u>Step 1 <img src="images/Arrow.png" height="8px" width="15px"> </u><i>Contact Response Team</i>
&nbsp;&nbsp;<u>Step 2 <img src="images/Arrow.png" height="8px" width="15px"> </u><i> Encourage Self Rescue</i>
&nbsp;&nbsp;<u>Step 3 <img src="images/Arrow.png" height="8px" width="15px"> </u><i>Pull lifeline/extraction device</i></td></tr>
<tr><td class="style3" width = 100%><img src="images/Arrow.png" height="8px" width="15px"> Entrants must exit the space if monitor alarms or other Emergency exists.   Contact Response Team and Authorizer Immediately.</td>
		  </tr>
		  </table>
		    <table bgcolor="#E2EFDA" width="100%" class="style6" height="45px"><tr>
<td class="style3" width = 100%><b>ENTRY</b</td></tr>
<tr><td class="style3" width = 100%><img src="images/Arrow.png" height="8px" width="15px"> Target values must be achieved prior to the initial entry
&nbsp;&nbsp;
<i>Space Volume / Fan CFM x Four = Minutes Ventilation</i></td>
		  </tr>
		  </table>
   <table bgcolor="#E2EFDA" width="100%" style="border-collapse: collapse" bordercolor="#111111" >
	<tr><td class="style7" width="6%">TIME</td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="10%" align="center"><b>Target</b></td>
<td class="style7" width="10%" align="center"><b>Alarm</b></td>  </tr>

	<tr><td class="style7" width="6%">O<sub>2</sub>%</td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="10%" align="center"><b>20.4-21.4</b></td>
<td class="style7" width="10%" align="center"><b>19.5-23.5</b></td>  </tr>
	<tr><td class="style7" width="6%">CO ppm</td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="10%" align="center"><b>0</b></td>
<td class="style7" width="10%" align="center"><b>25</b></td>  </tr>
	<tr><td class="style7" width="6%">H<sub>2</sub>S ppm</td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="10%" align="center"><b>0</b></td>
<td class="style7" width="10%" align="center"><b>1</b></td>  </tr>
	<tr><td class="style7" width="6%">LEL/LFL%</td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="10%" align="center"><b>0</b></td>
<td class="style7" width="10%" align="center"><b>10</b></td>  </tr>
	<tr><td class="style7" width="6%">&nbsp;</td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="10%"></td>
<td class="style7" width="10%"></td>  </tr>
	<tr><td class="style7" width="6%">&nbsp;</td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="5%"></td>
<td class="style7" width="10%"></td>
<td class="style7" width="10%"></td>  </tr>
</table>
<table bgcolor="#E2EFDA" width="100%" class="style6" height="45px">

<tr><td class="style3" width = 100%>Readings must be <b><u>recorded</u></b> initially, at least every 1 hour, and if area is vacated for more than 30 minutes and re-entered.
</td></tr>
<tr>
<td class="style3" width = 100%>
Signature of person performing initial air test:&nbsp;________________________________________________</td>
		  </tr>
		  </table>
	
 <table width="100%" style="border-collapse: collapse" bordercolor="#111111" >
<tr>
<td colspan="2" bgcolor="D9E1F2" class="style3" ><i>Signature only required upon initial entry.</i></td>
<td colspan="14" bgcolor="#E2EFDA" class="style3" >Time&nbsp;&nbsp; &nbsp; 
	     <i>Permit must be reissued with each change in work force across shifts"</i></td>
</tr>
<tr>
<td  bgcolor="D9E1F2" class="style7" width="15%">Entrant Name </td>
<td  bgcolor="D9E1F2" class="style7" width="15%">Signature </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" align="center">In </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" align="center">Out </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" align="center">In </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" align="center">Out </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" align="center">In </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" align="center">Out </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" align="center">In </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" align="center">Out </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" align="center">In </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" align="center">Out </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" align="center">In </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" align="center">Out </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" align="center">In </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" align="center">Out </td>
</tr>

<tr>
<td  bgcolor="D9E1F2" class="style7" width="15%">&nbsp;</td>
<td   class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
</tr>
<tr>
<td  bgcolor="D9E1F2" class="style7" width="15%">&nbsp;</td>
<td   class="style7" width="5%" > </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
</tr>
<tr>
<td  bgcolor="D9E1F2" class="style7" width="15%">&nbsp;</td>
<td   class="style7" width="5%" > </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
</tr>
<tr>
<td  bgcolor="D9E1F2" class="style7" width="15%">&nbsp;</td>
<td   class="style7" width="5%" > </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
</tr>
<tr>
<td  bgcolor="D9E1F2" class="style7" width="15%">&nbsp;</td>
<td  class="style7" width="5%" > </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
</tr>
<tr>
<td  bgcolor="D9E1F2" class="style7" width="15%">&nbsp;</td>
<td   class="style7" width="5%" > </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" ></td>
</tr>

<tr>
<td colspan="2" bgcolor="D9E1F2" class="style3" ><b>AUTHORIZING SIGNATURE OF:</b></td>
<td colspan="6" bgcolor="D9E1F2" class="style3" >Authorizer ______________________</td>
<td colspan="8" bgcolor="#E2EFDA" class="style3" >Attendant _______________________</td>
</tr>
<tr>
<td colspan="2" bgcolor="D9E1F2" class="style3" >&nbsp;</td>
<td colspan="6" bgcolor="D9E1F2" class="style3" >Print Name ______________________</td>
<td colspan="8" bgcolor="#E2EFDA" class="style3" >Print Name _______________________</td>
</tr>

	<tr>
<td class="style7" align="center" colspan="16"><b> TERMINATED/COMPLETED (Ensure Space is Closed or Barricaded)</b></td>	 </tr>																		
<tr></tr><td class="style7" align="center" colspan="16">By: ____________________________ &nbsp; &nbsp;    Time: ________  &nbsp; &nbsp;  Date:_____________________</td> </tr>
<tr></tr><td class="style7" align="center" colspan="16">Reason for termination: ________________________________________________________________ </td> </tr>
 </tr>
 </table>
<br>
<p align="center" class="style2"><b>Page 2</b></p>
<table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" align = CENTER>

  <tr>
    <td rowspan="2"><img src="images/KC_Logo_Img.png"></td>
	<td class="style2" width="50%"><b>KIMBERLY-CLARK   NORTH AMERICA</b></font></td>
	<td class="style5" bgcolor="D9E1F2" width="8%" align="center" bordercolor="black">Blue</td>
	<td class="style2">&nbsp;&nbsp;Must be filled out by the <b>Authorizer	</b></font>
</td>
</tr>
	<td class="style2"><b>CONFINED SPACE ENTRY PERMIT-Appendix A</b></td>
	<td class="style5" bgcolor="#E2EFDA" align="center" bordercolor="black">Green</td>
	<td class="style2">&nbsp;&nbsp;Must be filled out by the <b>Attendant	</b></td>

  </tr></table>
   <table width="100%" style="border-collapse: collapse" bordercolor="#111111" >
<tr>
<td colspan="2" bgcolor="D9E1F2" class="style3" ><i>Signature only required upon initial entry.</i></td>
<td colspan="14" bgcolor="#E2EFDA" class="style3" >Time&nbsp;&nbsp; &nbsp; 
	     <i>Permit must be reissued with each change in work force across shifts"</i></td>
</tr>
<tr>
<td  bgcolor="D9E1F2" class="style7" width="15%">Entrant Name </td>
<td  bgcolor="D9E1F2" class="style7" width="15%">Signature </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" align="center">In </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" align="center">Out </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" align="center">In </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" align="center">Out </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" align="center">In </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" align="center">Out </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" align="center">In </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" align="center">Out </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" align="center">In </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" align="center">Out </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" align="center">In </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" align="center">Out </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" align="center">In </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" align="center">Out </td>
</tr>

<tr>
<td  bgcolor="D9E1F2" class="style7" width="15%">&nbsp;</td>
<td   class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
</tr>
<tr>
<td  bgcolor="D9E1F2" class="style7" width="15%">&nbsp;</td>
<td   class="style7" width="5%" > </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
</tr>
<tr>
<td  bgcolor="D9E1F2" class="style7" width="15%">&nbsp;</td>
<td   class="style7" width="5%" > </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
</tr>
<tr>
<td  bgcolor="D9E1F2" class="style7" width="15%">&nbsp;</td>
<td   class="style7" width="5%" > </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
</tr>
<tr>
<td  bgcolor="D9E1F2" class="style7" width="15%">&nbsp;</td>
<td  class="style7" width="5%" > </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
</tr>
<tr>
<td  bgcolor="D9E1F2" class="style7" width="15%">&nbsp;</td>
<td   class="style7" width="5%" > </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp; </td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" >&nbsp;</td>
<td  bgcolor="#E2EFDA" class="style7" width="5%" ></td>
</tr>
</table>
<table width="100%" >
<tr><td class="style8" align="center" colspan="16"><b>Leader Review</b></tr>
	<tr><td  class="style8" width="15%" align="right">Reviewed by</td>
	<td  class="style8" width="15%">______________________________________</td>
	<td  class="style8" width="15%"></td>
	<td   class="style8" colspan="2">Notes</td>
	<td   class="style8" colspan="12">____________________________________________________________________________</td>
	</tr>
		<tr><td  class="style8" width="15%" align="right">Print Name</td>
	<td  class="style8" width="15%">______________________________________</td>
	<td  class="style8" width="15%"></td>
	<td   class="style8" colspan="2">&nbsp;</td>
	<td   class="style8" colspan="12">____________________________________________________________________________</td>
	</tr>
		<tr><td  class="style8" width="15%" align="right">Date</td>
	<td  class="style8" width="15%">______________________________________</td>
	<td  class="style8" width="15%"></td>
	<td   class="style8" colspan="2">&nbsp;</td>
	<td   class="style8" colspan="12">____________________________________________________________________________</td>
	</tr>
</table>
<p class="page-break"></p>
<% strsql = "SELECT tblHA.*, tblSOP.LOCATION, tblSOP.SDescription FROM tblHA INNER JOIN tblSOP ON tblHA.SpaceID = tblSOP.SOP_NO "_
  &" where SpaceID = '" & strid & "'"
Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
  If not MyConn.eof then
 strSpaceid = strid
 strLocation = MyConn.fields("Location")
 strSdescription = MyConn.fields("SDescription")
 strHid = MyConn.fields("IDHazAssess")
 end if 
 MyConn.close %>
<table  border=1 cellspacing=0 cellpadding=0 width="100%">
 <tr >
  <td colspan=6 >
	<table class="MsoNormalTable" border="1" cellspacing="0" cellpadding="0" width="100%" align="center">
		<tr style="mso-yfti-irow:0;mso-yfti-firstrow:yes;height:34.25pt">
			<td colspan="2" valign="top" border: solid windowtext 1.0pt; mso-border-alt: solid windowtext .75pt; background: white; padding: 0in 5.4pt 0in 5.4pt; height: 34.25pt">
			<p class="style3"><b style="mso-bidi-font-weight:
  normal"><span style="font-size:10.0pt;mso-bidi-font-size:11.0pt">
			KIMBERLY-CLARK CORPORATION</span></b><br><span class="style1"><st1:place w:st="on"><b style="mso-bidi-font-weight:normal"><span style="font-size:10.0pt;mso-bidi-font-size:11.0pt">North 
			Atlantic</span></b></st1:place><b style="mso-bidi-font-weight:normal"><span style="font-size:10.0pt;mso-bidi-font-size:
  11.0pt"> Consumer Products</span></b></span><span style="font-size:10.0pt;
  mso-bidi-font-size:11.0pt"></span></p>
			</td>
			<td colspan="3" valign="top"  border: solid windowtext 1.0pt; border-left: none; mso-border-left-alt: solid windowtext .75pt; mso-border-alt: solid windowtext .75pt; background: white; padding: 0in 5.4pt 0in 5.4pt; height: 34.25pt">
			<p class="style3"><b style="mso-bidi-font-weight:normal">
			<span style="font-size:10.0pt;mso-bidi-font-size:11.0pt">LOSS 
			PREVENTION MANUAL</span></b></p>
			</td>
			<td  valign="top"  border: solid windowtext 1.0pt; border-left: none; mso-border-left-alt: solid windowtext .75pt; mso-border-alt: solid windowtext .75pt; background: white; padding: 0in 5.4pt 0in 5.4pt; height: 34.25pt">
	
			<p class="style4">
			<span style="font-size:10.0pt;mso-bidi-font-size:11.0pt" class="style1">
			<span style="mso-spacerun:yes">&nbsp;</span><b style="mso-bidi-font-weight:normal">Page</b></span><span class="style2"><b style="mso-bidi-font-weight:normal"><span style="font-size: 10.0pt; mso-bidi-font-size: 11.0pt; mso-bidi-font-family: &quot;Times New Roman&quot;" class="style1">
			</span></b></span><!--[if supportFields]>
			<span style="mso-bidi-font-family: &quot;Times New Roman&quot;">
			<span style="mso-spacerun:yes" class="style1">&nbsp;</span></span><span style="mso-bidi-font-family: &quot;Times New Roman&quot;" class="style1">PAGE
			</span><![endif]--><span class="style2">
			<span style="mso-bidi-font-family: &quot;Times New Roman&quot;" class="style1">
			<span style="mso-no-proof:yes">1</span></span></span><!--[if supportFields]><span class="style2"></span><![endif]--><span class="style2"><b style="mso-bidi-font-weight:normal"><span style="font-size: 10.0pt; mso-bidi-font-size: 11.0pt; mso-bidi-font-family: &quot;Times New Roman&quot;" class="style1"> 
			of </span></b>
			<span style="font-size: 10.0pt; mso-bidi-font-size: 11.0pt; mso-bidi-font-family: &quot;Times New Roman&quot;" class="style1">
			1</span></span><b style="mso-bidi-font-weight:normal"><span style="font-size:10.0pt;mso-bidi-font-size:
  11.0pt"><o:p></o:p></span></b></p>
			</td>
		</tr>
		<tr style="mso-yfti-irow:1;page-break-inside:avoid;height:34.3pt">
			<td  colspan="2" valign="top" border:solid windowtext 1.0pt;
  border-top:none;mso-border-top-alt:solid windowtext .75pt;mso-border-alt:
  solid windowtext .75pt;padding:0in 5.4pt 0in 5.4pt;height:34.3pt">
			<p class="style3">
			<span style="font-size:10.0pt;
  mso-bidi-font-size:11.0pt">Location<b style="mso-bidi-font-weight:normal">: 
			Mobile<st1:City
  w:st="on"><st1:place w:st="on"><br>
			</b></span></p>
			</td>
			<td  valign="top" border-top:none;border-left:
  none;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .75pt;mso-border-left-alt:solid windowtext .75pt;
  mso-border-alt:solid windowtext .75pt;padding:0in 5.4pt 0in 5.4pt;height:
  34.3pt">
			<p class="style3">
			<span style="font-size:10.0pt;
  mso-bidi-font-size:11.0pt">Procedure No:<br>4.7<o:p></o:p></span></b></p>
			</td>
			<td  colspan="2" valign="top" border-top:none;
  border-left:none;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .75pt;mso-border-left-alt:solid windowtext .75pt;
  mso-border-alt:solid windowtext .75pt;padding:0in 5.4pt 0in 5.4pt;height:
  34.3pt">
			<p class="style3">
			<span style="font-size:10.0pt;
  mso-bidi-font-size:11.0pt">Issue Date:<br>11.17.08<o:p></o:p></b></p>
			</td>
			<td  valign="top" border-top:none;border-left:
  none;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .75pt;mso-border-left-alt:solid windowtext .75pt;
  mso-border-alt:solid windowtext .75pt;padding:0in 5.4pt 0in 5.4pt;height:
  34.3pt">
			<p class="style3">
			<span style="font-size:10.0pt;
  mso-bidi-font-size:11.0pt">Revision Date:<br>11/25/2013</span></b><span style="font-size:10.0pt;mso-bidi-font-size:11.0pt"><o:p></o:p></span></p>
			</td>
		</tr>
		<tr style="mso-yfti-irow:2;mso-yfti-lastrow:yes;height:34.3pt">
			<td valign="top" border:solid windowtext 1.0pt;
  border-top:none;mso-border-top-alt:solid windowtext .75pt;mso-border-alt:
  solid windowtext .75pt;padding:0in 5.4pt 0in 5.4pt;height:34.3pt">
			<p class="style3">
			<span style="font-size:10.0pt;
  mso-bidi-font-size:11.0pt">Instruction:</span><b style="mso-bidi-font-weight:normal"><br><span style="font-size:10.0pt;mso-bidi-font-size:11.0pt">Operational 
			Controls<o:p></o:p></span></b></p>
			</td>
			<td  colspan="3" valign="top" border-top:none;
  border-left:none;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .75pt;mso-border-left-alt:solid windowtext .75pt;
  mso-border-alt:solid windowtext .75pt;padding:0in 5.4pt 0in 5.4pt;height:
  34.3pt">
			<p class="style16">Element:<br>
			<b style="mso-bidi-font-weight:normal">In-Process 
			Entry Audit<o:p></o:p></b></p>
			</td>
			<td colspan="2" valign="top" border-top:none;
  border-left:none;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .75pt;mso-border-left-alt:solid windowtext .75pt;
  mso-border-alt:solid windowtext .75pt;padding:0in 5.4pt 0in 5.4pt;height:
  34.3pt">
			<p class="style3">
			<span style="font-size:10.0pt;
  mso-bidi-font-size:11.0pt">Standard
			<br><b style="mso-bidi-font-weight:normal">Confined 
			Space Entry Program<o:p></o:p></b></p>
			</td>
		</tr>
	</table>
	<p class=MsoNormal align=center style='margin-bottom:3.0pt;text-align:center'>
	<o:p></o:p></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:1'>
  <td width="15%" style='width:15.06%;border-top:none;border-left:
  solid windowtext 2.25pt;border-bottom:solid windowtext 1.0pt;border-right:
  solid windowtext 1.0pt;mso-border-top-alt:solid windowtext .5pt;mso-border-alt:
  solid windowtext .5pt;mso-border-left-alt:solid windowtext 2.25pt;padding:
  0in 5.4pt 0in 5.4pt'>
  <p>	<span style="font-size:10.0pt;  mso-bidi-font-size:11.0pt">
	Space ID :</span><o:p></o:p></p>
  </td>
  <td width="19%" style='width:19.08%;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt'>
<%= strid %>
  </td>
  <td width="14%" style='width:14.56%;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt'>
  <p class=MsoNormal align=right style='margin-top:1.0pt;text-align:right'>
	<span style="font-size:10.0pt;  mso-bidi-font-size:11.0pt">CSE Permit #</span<o:p></o:p></span></p>
  </td>
  <td width="12%" style='width:12.0%;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt'><%= strpid %>
  </td>
  <td style='width:7%;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt'>
  <p class=style10 align=right style='margin-top:1.0pt;text-align:right; width: 81px;'>
	<span style="font-size:10.0pt;  mso-bidi-font-size:11.0pt">
  Space Name:</span></p>
  </td>
  <td width="32%" style='width:32.78%;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 2.25pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;mso-border-right-alt:solid windowtext 2.25pt;
  padding:0in 5.4pt 0in 5.4pt'>
<%= strSDescription%>  </td>
 </tr>
 <tr style='mso-yfti-irow:2'>
  <td width="15%" style='width:15.06%;border-top:none;border-left:
  solid windowtext 2.25pt;border-bottom:solid windowtext 1.0pt;border-right:
  solid windowtext 1.0pt;mso-border-top-alt:solid windowtext .5pt;mso-border-top-alt:
  .5pt;mso-border-left-alt:2.25pt;mso-border-bottom-alt:.25pt;mso-border-right-alt:
  .5pt;mso-border-color-alt:windowtext;mso-border-style-alt:solid;padding:0in 5.4pt 0in 5.4pt'>
  <p class=style10 align=right style='margin-top:1.0pt;text-align:left'>
	<span style="font-size:10.0pt;  mso-bidi-font-size:11.0pt">Beginning Date::</span></p>
  </td>
  <td width="19%" style='width:19.08%;border-top:none;border-left:
  none;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;mso-border-bottom-alt:solid windowtext .25pt;
  padding:0in 5.4pt 0in 5.4pt'><%= strDateIssued%></td>
  <td width="14%" style='width:14.56%;border-top:none;border-left:
  none;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;mso-border-bottom-alt:solid windowtext .25pt;
  padding:0in 5.4pt 0in 5.4pt'>
  <p class=MsoNormal align=right style='margin-top:1.0pt;text-align:right'>
 	<span style="font-size:10.0pt;  mso-bidi-font-size:11.0pt">Location of Point of Entry:</span></p>
  </td>
  <td width="51%" colspan=3 style='width:51.3%;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 2.25pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-top-alt:.5pt;mso-border-left-alt:.5pt;mso-border-bottom-alt:.25pt;
  mso-border-right-alt:2.25pt;mso-border-color-alt:windowtext;mso-border-style-alt:
  solid;padding:0in 5.4pt 0in 5.4pt'>
 <%= strLocation%>  </td>
 </tr>
 <tr style='mso-yfti-irow:3'>
  <td colspan=6 valign=top style='border-top:none;border-left:solid windowtext 2.25pt;
  border-bottom:none;border-right:solid windowtext 2.25pt;padding:0in 5.4pt 0in 5.4pt'>
  &nbsp;</td>
 </tr>
</table>

</div>

<table border="1" cellspacing="0" cellpadding="0" width="100%">
	<tr>
		<td width="100%" colspan="6" valign="top" style="width:7.5in;border:solid windowtext 1.0pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt" >
	<b><font face="arial" size="3">Lockout</b>
		</td>
	</tr>
	<tr >
		<td width="564" colspan="4" valign="top" style="width:423.0pt;border:solid windowtext 1.0pt;
  border-top:none;mso-border-top-alt:solid windowtext .5pt;mso-border-alt:solid windowtext .5pt;
  padding:0in 5.4pt 0in 5.4pt"><font face="arial" size="3">
  
  1.&nbsp;&nbsp;&nbsp;&nbsp;Is the attendant and all entrants locked out on the Isolation Board?
		</td>
				<td width="78" valign="top" style="width:58.5pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p  align="center"><font face="arial" size="3">Yes</p>
		</td>
		<td width="78" valign="top" style="width:58.5pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p  align="center"><font face="arial" size="3">No</p>
		</td>
	</tr>
	<tr >
		<td width="564" colspan="4" valign="top" style="width:423.0pt;border:solid windowtext 1.0pt;
  border-top:none;mso-border-top-alt:solid windowtext .5pt;mso-border-alt:solid windowtext .5pt;
  padding:0in 5.4pt 0in 5.4pt" class="style1"><font  size="3" face="arial">
  
  2.&nbsp;&nbsp;&nbsp;&nbsp;Is the Vessel Isolation completed and posted on the Lockout Board?
		</td>
			<td width="78" valign="top" style="width:58.5pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p  align="center"><font face="arial" size="3">Yes</p>
		</td>
		<td width="78" valign="top" style="width:58.5pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p  align="center"><font face="arial" size="3">No</p>
		</td>
	</tr>
	<tr >
		<td width="100%" colspan="6" valign="top" style="width:7.5in;border:solid windowtext 1.0pt;
  border-top:none;mso-border-top-alt:solid windowtext .5pt;mso-border-alt:solid windowtext .5pt;
  padding:0in 5.4pt 0in 5.4pt" class="style1"><b>
	<font face="arial" size="3">Permit</span></b>
		</td>
	</tr>
	<tr style="mso-yfti-irow:5">
		<td width="564" colspan="4" valign="top" style="width:423.0pt;border:solid windowtext 1.0pt;
  border-top:none;mso-border-top-alt:solid windowtext .5pt;mso-border-alt:solid windowtext .5pt;
  padding:0in 5.4pt 0in 5.4pt" class="style1">
		3.&nbsp;&nbsp;&nbsp;
	
		<font face="arial">
		<span class="style24">Is the date range on the permit current?
		</span>
		</td>
			<td width="78" valign="top" style="width:58.5pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p  align="center"><font face="arial" size="3">Yes</p>
		</td>
		<td width="78" valign="top" style="width:58.5pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p  align="center"><font face="arial" size="3">No</p>
		</td>
	</tr>
	<tr style="mso-yfti-irow:6">
		<td width="564" colspan="4" valign="top" style="width:423.0pt;border:solid windowtext 1.0pt;
  border-top:none;mso-border-top-alt:solid windowtext .5pt;mso-border-alt:solid windowtext .5pt;
  padding:0in 5.4pt 0in 5.4pt" class="style25">
	
			<font face="arial">	4.&nbsp;&nbsp;&nbsp;&nbsp;Is the purpose of entry 		noted on the permit?
		</td>
			<td width="78" valign="top" style="width:58.5pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p  align="center"><font face="arial" size="3">Yes</p>
		</td>
		<td width="78" valign="top" style="width:58.5pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p  align="center"><font face="arial" size="3">No</p>
		</td>
	</tr>
	<tr style="mso-yfti-irow:7">
		<td width="564" colspan="4" valign="top" style="width:423.0pt;border:solid windowtext 1.0pt;
  border-top:none;mso-border-top-alt:solid windowtext .5pt;mso-border-alt:solid windowtext .5pt;
  padding:0in 5.4pt 0in 5.4pt" class="style25">
	<font face="arial">5.&nbsp;&nbsp;&nbsp;
		Are the air tests current (within the last 4 hours)?
		</td>
			<td width="78" valign="top" style="width:58.5pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p  align="center"><font face="arial" size="3">Yes</p>
		</td>
		<td width="78" valign="top" style="width:58.5pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p  align="center"><font face="arial" size="3">No</p>
		</td>
	</tr>
	<tr >
			<td  colspan="4" valign="top" style="border:solid windowtext 1.0pt;
  border-top:none;mso-border-top-alt:solid windowtext .5pt;mso-border-alt:solid windowtext .5pt;
  padding:0in 5.4pt 0in 5.4pt" class="style25">

	<font face="arial">6.&nbsp;&nbsp;&nbsp;
		
Have all tasks checked in 
		the "requirements completed section" been completed?
		</td>
			<td width="78" valign="top" style="width:58.5pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p  align="center"><font face="arial" size="3">Yes</p>
		</td>
		<td width="78" valign="top" style="width:58.5pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p  align="center"><font face="arial" size="3">No</p>
		</td>
	</tr>
	<tr style="mso-yfti-irow:9">
		<td width="564" colspan="4" valign="top" style="width:423.0pt;border:solid windowtext 1.0pt;
  border-top:none;mso-border-top-alt:solid windowtext .5pt;mso-border-alt:solid windowtext .5pt;
  padding:0in 5.4pt 0in 5.4pt" class="style25">
		<font face="arial">7.&nbsp;&nbsp;&nbsp;
		
	Has the permit been signed by a vessel entry leader?
		</td>
			<td width="78" valign="top" style="width:58.5pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p  align="center"><font face="arial" size="3">Yes</p>
		</td>
		<td width="78" valign="top" style="width:58.5pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p  align="center"><font face="arial" size="3">No</p>
		</td>
		</td>
	</tr>
	<tr style="mso-yfti-irow:10">
		<td width="100%" colspan="6" valign="top" style="width:7.5in;border:solid windowtext 1.0pt;
  border-top:none;mso-border-top-alt:solid windowtext .5pt;mso-border-alt:solid windowtext .5pt;
  padding:0in 5.4pt 0in 5.4pt" class="style25">
	<font face="arial" size="3"><b>Sign-in/Sign-out Sheet</b>
		</td>
	</tr>
	<tr >
		<td width="564" colspan="4" valign="top" style="border-left: 1.0pt solid windowtext; border-right: 1.0pt solid windowtext; border-bottom: 1.0pt solid windowtext; width:423.0pt; border-top:none; mso-border-top-alt:solid windowtext .5pt; mso-border-alt:solid windowtext .5pt;
  padding:0in 5.4pt 0in 5.4pt; height: 22px;" class="style25">
	<font face="arial" size="3">8.&nbsp;&nbsp;&nbsp;
		Have all entrants signed it?
		</td>
			<td width="78" valign="top" style="width:58.5pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p  align="center"><font face="arial" size="3">Yes</p>
		</td>
		<td width="78" valign="top" style="width:58.5pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p  align="center"><font face="arial" size="3">No</p>
		</td>
	</tr>
	<tr >
		<td width="564" colspan="4" valign="top" style="border-left: 1.0pt solid windowtext; border-right: 1.0pt solid windowtext; border-bottom: 1.0pt solid windowtext; width:423.0pt; border-top:none; mso-border-top-alt:solid windowtext .5pt; mso-border-alt:solid windowtext .5pt;
  padding:0in 5.4pt 0in 5.4pt; height: 22px;" class="style25">
	<font face="arial" size="3">9.&nbsp;&nbsp;&nbsp;
Is the current attendant 
		signed in?
		</td>
		<td width="78" valign="top" style="width:58.5pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p  align="center"><font face="arial" size="3">Yes</p>
		</td>
		<td width="78" valign="top" style="width:58.5pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p  align="center"><font face="arial" size="3">No</p>
		</td>
	</tr>
	<tr>
		<td colspan="4" valign="top" style="border:solid windowtext 1.0pt;
  border-top:none;mso-border-top-alt:solid windowtext .5pt;mso-border-alt:solid windowtext .5pt;
  padding:0in 5.4pt 0in 5.4pt" class="style25">
	<font face="arial">10.&nbsp;		
	Does that attendant have a 	means for reporting an emergency per the permit?
		</td>
			<td width="78" valign="top" style="width:58.5pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p  align="center"><font face="arial" size="3">Yes</p>
		</td>
		<td width="78" valign="top" style="width:58.5pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p  align="center"><font face="arial" size="3">No</p>
		</td>
	</tr>
	<tr style="mso-yfti-irow:14">
		<td width="100%" colspan="6" valign="top" style="width:7.5in;border:solid windowtext 1.0pt;
  border-top:none;mso-border-top-alt:solid windowtext .5pt;mso-border-alt:solid windowtext .5pt;
  padding:0in 5.4pt 0in 5.4pt" class="style25">
	<font face="arial" size="3"><b>Work Practices</b>
		</td>
	</tr>
	<tr >
		<td width="564" colspan="4" valign="top" style="border-left: 1.0pt solid windowtext; border-right: 1.0pt solid windowtext; border-bottom: 1.0pt solid windowtext; width:423.0pt; border-top:none; mso-border-top-alt:solid windowtext .5pt; mso-border-alt:solid windowtext .5pt;
  padding:0in 5.4pt 0in 5.4pt; height: 22px;" class="style25">
	<font face="arial" size="3">11.&nbsp;&nbsp;Has the pre-entry meeting 
		been held?
		</td>
		<td width="78" valign="top" style="width:58.5pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p  align="center"><font face="arial" size="3">Yes</p>
		</td>
		<td width="78" valign="top" style="width:58.5pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p  align="center"><font face="arial" size="3">No</p>
		</td>
	</tr>
	<tr >
		<td width="564" colspan="4" valign="top" style="border-left: 1.0pt solid windowtext; border-right: 1.0pt solid windowtext; border-bottom: 1.0pt solid windowtext; width:423.0pt; border-top:none; mso-border-top-alt:solid windowtext .5pt; mso-border-alt:solid windowtext .5pt;
  padding:0in 5.4pt 0in 5.4pt; height: 22px;" class="style25">
	<font face="arial" size="3">12.&nbsp;&nbsp;Are entrants using 
		required PPE?
		</td>
		<td width="78" valign="top" style="width:58.5pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p  align="center"><font face="arial" size="3">Yes</p>
		</td>
		<td width="78" valign="top" style="width:58.5pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p  align="center"><font face="arial" size="3">No</p>
		</td>
	</tr>
	<tr >
		<td width="564" colspan="4" valign="top" style="border-left: 1.0pt solid windowtext; border-right: 1.0pt solid windowtext; border-bottom: 1.0pt solid windowtext; width:423.0pt; border-top:none; mso-border-top-alt:solid windowtext .5pt; mso-border-alt:solid windowtext .5pt;
  padding:0in 5.4pt 0in 5.4pt; height: 22px;" class="style25">
	<font face="arial" size="3">13.&nbsp;&nbsp;Is vessel entry leader 
		present on-site?
		</td>
		<td width="78" valign="top" style="width:58.5pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p  align="center"><font face="arial" size="3">Yes</p>
		</td>
		<td width="78" valign="top" style="width:58.5pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p  align="center"><font face="arial" size="3">No</p>
		</td>
	</tr>
	<tr >
		<td width="564" colspan="4" valign="top" style="border-left: 1.0pt solid windowtext; border-right: 1.0pt solid windowtext; border-bottom: 1.0pt solid windowtext; width:423.0pt; border-top:none; mso-border-top-alt:solid windowtext .5pt; mso-border-alt:solid windowtext .5pt;
  padding:0in 5.4pt 0in 5.4pt; height: 22px;" class="style25">
	<font face="arial" size="3">14.&nbsp;&nbsp;Has Security been called 
		to activate the space?</td>
		<td width="78" valign="top" style="width:58.5pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p  align="center"><font face="arial" size="3">Yes</p>
		</td>
		<td width="78" valign="top" style="width:58.5pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p  align="center"><font face="arial" size="3">No
		</td>
	</tr>
	<tr >
		<td width="126" colspan="2" valign="top" style="width:94.5pt;border:solid windowtext 1.0pt;
  border-top:none;mso-border-top-alt:solid windowtext .5pt;mso-border-alt:solid windowtext .5pt;
  padding:0in 5.4pt 0in 5.4pt" class="style25">
		<font face="arial" size="3">Other Comments
		</td>
		<td width="594" colspan="4" valign="top" style="width: 445.5pt; mso-border-left-alt: solid windowtext .5pt; mso-border-left-alt: solid windowtext .5pt; mso-border-right-alt: solid windowtext .5pt; background: #EAEAEA; padding: 0in 5.4pt 0in 5.4pt; border-left-color: inherit; border-right-style: solid; border-right-color: windowtext; border-top-color: inherit; border-bottom-color: inherit;" class="style27">
		<font face="arial" size="3">Use to explain "no" answers from above or other observations
		</td>
	</tr>
	<tr>
		<td width="100%" colspan="6" style="height: 6px" class="style26" >&nbsp;		</td>
	</tr>
	<tr>
		<td width="100%" colspan="6" class="style26" >&nbsp;		</td>
	</tr>
<tr>
		<td width="100%" colspan="6" class="style26" >&nbsp;		</td>
	</tr>
<tr>
		<td width="100%" colspan="6" class="style26" >&nbsp;		</td>
	</tr>
<tr>
		<td width="100%" colspan="6" class="style26" >&nbsp;		</td>
	</tr>
	<tr style="mso-yfti-irow:25">
		<td width="113" valign="top" style="width:84.6pt;border:solid windowtext 1.0pt;
  border-top:none;mso-border-top-alt:solid windowtext 1.5pt;mso-border-alt:
  solid windowtext .5pt;mso-border-top-alt:solid windowtext 1.5pt;padding:0in 5.4pt 0in 5.4pt" class="style1">
		<p class="style1"><b style="mso-bidi-font-weight:normal">
		<span style="font-size:10.0pt"><font face="arial" size="3">Date/Time<o:p></o:p></span></b></p>
		</td>
		<td width="607" colspan="5" valign="top" style="width:455.4pt;border-top:none;
  border-left:none;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext 1.5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;mso-border-top-alt:solid windowtext 1.5pt;
  padding:0in 5.4pt 0in 5.4pt">
		<p class="style1"><o:p>&nbsp;</o:p></p>
		</td>
	</tr>
	<tr >
		<td valign="top" style="border:solid windowtext 1.0pt;
  border-top:none;mso-border-top-alt:solid windowtext .5pt;mso-border-alt:solid windowtext .5pt;
  padding:0in 5.4pt 0in 5.4pt" class="style1">
		<p class="style1"><b style="mso-bidi-font-weight:normal">
	<font face="arial" size="3">Auditors (print)<o:p></o:p></span></b></p>
		</td>
		<td width="289" colspan="2" valign="top" style="width:216.9pt;border-top:none;
  border-left:none;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p class="MsoNormal"><![if !supportLists]>
		<span style="mso-fareast-font-family:
  Arial"><span style="mso-list:Ignore"><span class="style1">1.</span><span style="font-style: normal; font-variant: normal; font-weight: normal; font-size: 7.0pt; line-height: normal;" class="style1">&nbsp;&nbsp;&nbsp;
		</span></span></span><![endif]><span class="style1"><o:p>&nbsp;</o:p></span></p>
		</td>
		<td width="318" colspan="3" valign="top" style="width:238.5pt;border-top:none;
  border-left:none;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p class="MsoNormal"><![if !supportLists]>
		<span style="mso-fareast-font-family:
  Arial"><span style="mso-list:Ignore"><span class="style1">2.</span><span style="font-style: normal; font-variant: normal; font-weight: normal; font-size: 7.0pt; line-height: normal;" class="style1">&nbsp;&nbsp;&nbsp;
		</span></span></span><![endif]><span class="style1"><o:p>&nbsp;</o:p></span></p>
		</td>
	</tr>
	<tr style="mso-yfti-irow:27;mso-yfti-lastrow:yes">
		<td width="113" valign="top" style="width:84.6pt;border:none;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-top-alt:solid windowtext .5pt;
  mso-border-right-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt" class="style1">
		<p class="style1"><o:p>&nbsp;</o:p></p>
		</td>
		<td width="289" colspan="2" valign="top" style="width:216.9pt;border-top:none;
  border-left:none;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p class="MsoNormal"><![if !supportLists]>
		<span style="mso-fareast-font-family:
  Arial"><span style="mso-list:Ignore"><span class="style1">3.</span><span style="font-style: normal; font-variant: normal; font-weight: normal; font-size: 7.0pt; line-height: normal;" class="style1">&nbsp;&nbsp;&nbsp;
		</span></span></span><![endif]><span class="style1"><o:p>&nbsp;</o:p></span></p>
		</td>
		<td width="318" colspan="3" valign="top" style="width:238.5pt;border-top:none;
  border-left:none;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-left-alt:solid windowtext .5pt;
  mso-border-alt:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt">
		<p class="MsoNormal"><![if !supportLists]>
		<span style="mso-fareast-font-family:
  Arial"><span style="mso-list:Ignore"><span class="style1">4.</span><span style="font-style: normal; font-variant: normal; font-weight: normal; font-size: 7.0pt; line-height: normal;" class="style1">&nbsp;&nbsp;&nbsp;
		</span></span></span><![endif]><span class="style1"><o:p>&nbsp;</o:p></span></p>
		</td>
	</tr>
	<![if !supportMisalignedColumns]>
	<tr height="0">
		<td width="113" style="border:none"></td>
		<td width="13" style="border:none"></td>
		<td width="276" style="border:none"></td>
		<td width="162" style="border:none"></td>
		<td width="78" style="border:none"></td>
		<td width="78" style="border:none"></td>
	</tr>
	<![endif]>
</table>
<p class="style12" align="right"><span style="font-size:10.0pt">Keep completed 
audit with entry permit paperwork<o:p></o:p></span></p>