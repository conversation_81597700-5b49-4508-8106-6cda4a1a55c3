 

<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>INBOUND BY WEEK</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->





<style type="text/css">
.style1 {
	font-size: x-small;
}
.style3 {
	font-weight: bold;
	border-width: 1px;
}
.style4 {
	font-family: Arial, Helvetica, sans-serif;
}
.style5 {
	font-family: Arial, Helvetica, sans-serif;
	border-width: 1px;
	background-color: #FFFFCC;
}
.style6 {
	font-family: Arial, Helvetica, sans-serif;
	border-width: 1px;
	background-color: #FFECEC;
}
.style7 {
	font-family: Arial, Helvetica, sans-serif;
	border-width: 1px;
	background-color: #EAFFFF;
}
.style8 {
	font-family: Arial, Helvetica, sans-serif;
	text-align: center;
}
.auto-style3 {
	font-family: Arial, Helvetica, sans-serif;
	text-align: center;
	background-color: #E2F3FE;
}
.auto-style4 {
	font-family: Arial, Helvetica, sans-serif;
	background-color: #E2F3FE;
}
.auto-style5 {
	border: 1px solid #000000;
}
.auto-style6 {
	border: 1px solid #EEEEEE;
	font-family: Arial, Helvetica, sans-serif;
	text-align: center;
}
.auto-style7 {
	border: 1px solid #EEEEEE;
	font-family: Arial, Helvetica, sans-serif;
	text-align: left;
}
</style>
</head>


<form name="form1" action="Inventory_by_week.asp" method="post">
 
<TABLE borderColor=white cellSpacing=0 cellPadding=0  width = 100% border=1 >  
  <tr>
  <td  align = left><font face = "Arial"><b>Loads Received by Week and Species&nbsp;&nbsp;&nbsp;&nbsp; 
  
  <% if request.querystring("p") = "e" then 
  
    Response.ContentType = "application/vnd.ms-excel"	
     else %>
  <a href="Inventory_By_Week.asp?p=e">Export</a>
  <% end if %></b></font></td>
</tr>
</table>

<%
    
    strsql = "SELECT tblCars.Species,  DatePart(ww,[date_received]) AS Week, Count(tblCars.CID) AS Total FROM tblCars "_
&" WHERE (((tblCars.Date_received)>'12/31/2017') AND ((tblCars.Grade)='RF') AND ((tblCars.Trailer)<>'UNKNOWN')) "_
&" GROUP BY  DatePart(ww,[date_received]), Species "_
&" ORDER BY  Species, DatePart(ww,[date_received]) DESC"
	
	   Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
%>    
  
 
   <table align="left" class="auto-style5" style="width: 35%" cellspacing="1">
   <tr><td class="auto-style4">Species</td> 
	   <td class="auto-style3">Week</td>
	   <td class="auto-style3">Total Inbound</td>
	   <td class="auto-style3">Total Shuttles </td>

  <td class="auto-style3">&nbsp;S </td>
	   <td class="auto-style3">&nbsp;M </td>
	   <td class="auto-style3">&nbsp;T </td>
	   <td class="auto-style3">&nbsp;W </td>
	   <td class="auto-style3">&nbsp;T </td>
	   <td class="auto-style3">&nbsp;F </td>
	   <td class="auto-style3">&nbsp;S </td></tr>
   <%    
       IF not MyConn.eof  then
    while not MyConn.eof 
    strWeek = MyConn("Week")
    strSpecies = MyConn("Species") %>
 <td class="auto-style7"><%= MyConn("Species") %></td> 
	   <td class="auto-style6"><%= MyConn("Week") %></td>
	   <td class="auto-style6"><%= MyCOnn("Total") %></td>	
	   
	   <% strsql2 = "Select count(CID) as SHU from tblCars where Species = '" & strSpecies & "' and Date_received > '12/31/2017' "_
&" and datepart(ww,Date_received) = " & strweek & " and Grade = 'RF' and Trailer = 'UNKNOWN' and (location = 'RF' or Location = 'OCC')"
   Set MyConn2 = Server.CreateObject("ADODB.Recordset")
    MyConn2.Open strSQL2, Session("ConnectionString")
If not MyConn2.eof then
strTransfers = MyConn2("SHU")
else
strTransfers = ""
end if 
MyConn2.close %>
	      <td class="auto-style6"><%= strTransfers %></td>
<% 
strSunday = ""
strMonday = ""
strTuesday = ""
strWednesday = ""
strThursday = ""
strFriday =  ""
strSaturday = ""

strsql2 = "Select count(CID) as WDT,  datepart(w, Date_received) as WD from tblCars where Species = '" & strSpecies & "' and Date_received > '12/31/2017' "_
&" and  datepart(ww,Date_received) = " & strweek & " and Grade = 'RF' and Trailer <> 'UNKNOWN' Group by datepart(w, Date_received)"
	   Set MyConn2 = Server.CreateObject("ADODB.Recordset")
    MyConn2.Open strSQL2, Session("ConnectionString")
If not MyConn2.eof then 
while not MyConn2.eof
if MyConn2("WD") = 1 then
strSunday = MyConn2("WDT")
elseif  MyConn2("WD") = 2 then
strMonday = MyConn2("WDT")
elseif  MyConn2("WD") = 3 then
strTuesday = MyConn2("WDT")
elseif  MyConn2("WD") = 4 then
strWednesday = MyConn2("WDT")
elseif  MyConn2("WD") = 5 then
strThursday = MyConn2("WDT")
elseif  MyConn2("WD") = 6 then
strFriday = MyConn2("WDT")
elseif  MyConn2("WD") = 7 then
strSaturday = MyConn2("WDT")
end if 
MyConn2.movenext
wend
end if 
MyConn2.close


%>
   
	   <td class="auto-style6"><%= StrSunday %>&nbsp;</td>
	   <td class="auto-style6"><%= StrMonday %>&nbsp;</td>
	      <td class="auto-style6"><%= StrTuesday %>&nbsp;</td>
	         <td class="auto-style6"><%= StrWednesday %>&nbsp;</td>
	            <td class="auto-style6"><%= StrThursday %>&nbsp;</td>
	               <td class="auto-style6"><%= StrFriday %>&nbsp;</td>
	                  <td class="auto-style6"><%= StrSaturday %>&nbsp;</td>
   
</tr>

      
 <%
  MyConn.movenext
    wend
    end if
    MyConn.close 
    %>

</TABLE>
