																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Truck Receipt</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->




<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate, strid, strTrailer, strDateReceived, MyConn, strcid, strslq3, strpounds

strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -7, strtdate)
strid = request.querystring("id")
strpounds = request.querystring("p")
strTrailer = request.querystring("t")
strDateReceived = Request.querystring("d")

strsql = "SELECT tblCars.* FROM tblCars WHERE cid  = " & strid
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    
    strcid = MyRec.fields("CID")
    
  strsql3 = "Update tblcars set Rec_number = " & strcid & " where CID = " & strcid  
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL3
			MyConn.Close
			
%>
<body onload="if (window.print) {window.print()}">

<p align="center"><img height="40" src="kcc40white2.gif" width="450"><br><br><br>
	
</p>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Receipt for Rail Car Load at Kimberly-Clark 
<a href="Select_Rail.asp"><b>Mobile</b></a></font></td>
<td align = center>&nbsp;</td>


</tr>
	    </table>
<br><br><br>
	
<table width = 100%>
<Tr>	<td align="center"><b><font face="Arial">Receipt Nbr<br><%= MyRec.fields("CID")%></font></b></td>
<td align = center>
<img alt="" src="Boxcar.gif"></td>

	<td align="center"><b><font face="Arial">Date Received<br>&nbsp;<%= MyRec.fields("Date_Received")%></font></b></td></Tr>


</table>	<br><br><br><br>

<table border="1" width="100%" id="table1" cellspacing="1" bordercolorlight="#808080" bordercolor="#000000">


		<tr>
		<td bordercolor="#808080" align="center"><b><font face="Arial">Carrier</font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">Trailer #</font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">Species</font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">Tons <br>
		Received</font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">Pounds<br>
		Received</font></b></td>
		
	</tr>
			<tr>
	
		<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("Carrier")%></font></b></td>
			<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("Trailer")%></font></b></td>
		
		<td align="center"><b><font face="Arial"><%= MyRec.fields("Species")%>&nbsp;</font></b></td>
			<td align="center"><font face="Arial"><b><%= MyRec.fields("Tons_received")%></b></font></td>
			<td align="center"><font face="Arial"><b><%= round(MyRec.fields("Tons_received")*2000,3) %></b></font></td>

	</tr>
	</table>
	<table>
	<tr>
		<td colspan = 6>&nbsp;</td>
	</tr>
		<tr>
		<td colspan = 6>&nbsp;</td>
	</tr></table>
	
<br><br>
<br><br>

		<% if len(MyRec.fields("Other_comments")) > 0 then %>
		<table width = 100% align = center><tr><td align = center>
	<b><font face="Arial"><%= MyRec.fields("Other_Comments")%></td></tr></table>
		<% end if %>
	&nbsp;<p align = center>&nbsp;</p>
		<div align="center">
		<table width = 80% border="1">
	<tr><td align="center">
		<p><b><font face="Arial">SAP DOC ID</font></b></td>
		<td align="center">
		<b><font face="Arial">MAGIC #</font></b></td>
		<td align="center"><b><font face="Arial">Receiver Name</font></b></td>
		
		<td align="center"><b><font face="Arial">Initials</font></b></td>
		
	</tr>
	
	<tr><% if MyRec.fields("Species") = "UNRESOLVED" or MyRec.fields("Species") = "Unresolved" then %>
	<td align="center" bgcolor="#E4E4E4"<b><font face="Arial" color="red">
	<strong>DO NOT ENTER INTO SAP</strong></font></b></td>
	<% else %>
		<td align="center"><b><font face="Arial"><%= MyRec.fields("SAP_DOC_ID") %>&nbsp;</font></b></td>
		<% end if %>
		<td align="center">&nbsp;</td>
		<td align="center"><font face = Arial><b><%= session("Ename") %></td>
	
		<td align="center">&nbsp;</td>
	</tr>
	
</table></div>
<style type="text/css" >
.break(pagebreak-after: always)
.style1 {
	font-size: x-large;
	font-weight: bold;
}
.style2 {
	font-size: x-large;
}
.style3 {
	font-family: Arial;
}
</style>
<% if MyRec.fields("Weigh_required") = "W" then %>
<br class="break">
<br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br>
<table width="100%" >

<p align="center"><img height="40" src="kcc40white2.gif" width="450"><br><br><br><br>
	
</p>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><b><font face="Arial"><span class="style2">Weigh Truck at Kimberly-Clark 
</span> 
<a href="SelectRelease.asp"><span class="style1">Mobile</span></a></font></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br><br><br>
	
<table width = 100%>
<Tr>	<td align="left"><b><font face="Arial">Receipt Nbr:&nbsp;<%= MyRec.fields("CID")%></font></b></td>
	<td align="right"><b><font face="Arial">Date Received:&nbsp;<%= MyRec.fields("Date_Received")%></font></b></td></Tr>


</table>	<br><br><br><br>

<table border="1" width="100%" id="table1" cellspacing="1" bordercolorlight="#808080" bordercolor="#000000">

	
		<tr>
		<td bordercolor="#808080" align="center"><b><font face="Arial">Carrier</font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">Trailer #</font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">Species</font></b></td>
	
		<td bordercolor="#808080" align="center"><b><font face="Arial">Release<br> 
		or BOL #</font></b></td>
		
	
		
	</tr>
			<tr>
	
		<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("Carrier")%></font></b></td>
			<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("Trailer")%></font></b></td>
		
		<td align="center"><b><font face="Arial"><%= MyRec.fields("Species")%>&nbsp;</font></b></td>
		
			<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("Release_Nbr")%></font></b></td>
			
	</tr>
	</table>
	
	<% end if %>