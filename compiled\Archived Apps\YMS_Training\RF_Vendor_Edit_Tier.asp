																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Edit RF Vendor </TITLE>


<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 



<% Dim MyRec, strsql, MyRec2, strsql2,  MyConn3, strsql3, strid

strid = Request.querystring("id")


  strsql = "Select * from tblTier where ID = " & strid
  
      Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


  set objGeneral = new ASP_CLS_General
  if objGeneral.IsSubmit() Then	
  Dim strVendor
  
	  strTest = Replace(Request.form("Test"), "'", "''")
        	
  	
	strsql =  "Update tblTier Set  Tier = '" & request.form("Tier") & "', Test = '" & strTest & "' where ID = " & strid
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
Response.redirect("RF_Vendor.asp")		
end if

	
%>

<style type="text/css">
.style3 {
	border: 1px solid #000000;
}
.style5 {
	border: 1px solid #C0C0C0;
	background-color: #FFFFD7;
}
.style6 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: arial;
	font-size: small;
	text-align: center;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style10 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial, Helvetica, sans-serif;
	font-size: small;
	text-align: center;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style11 {
	background-color: #FFFFD7;
}
.auto-style1 {
	font-weight: bold;
	background-color: #E2F3FE;
}
.auto-style2 {
	background-color: #E2F3FE;
}
.auto-style3 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: arial;
	font-size: small;
	text-align: center;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #E2F3FE;
}
.auto-style4 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial, Helvetica, sans-serif;
	font-size: small;
	text-align: center;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #E2F3FE;
}
</style>
</head>

<body>
<br>
	
<form name="form1" action="RF_Vendor_Edit_Tier.asp?id=<%= strid %>" method="post" ID="Form1">
<TABLE cellSpacing=0 cellPadding=0 width=100% align = center class="style5">

 <TD align = left class="auto-style2"><font size="2" face = arial>&nbsp;</font></td>
<td align = center class="auto-style1"><font face="Arial">Modify Vendor Tier Rating</font></td>
<td align = right class="auto-style2"><font face="Arial"><a href="javascript:history.go(-1);">Return</a>&nbsp;</td>


</tr>
	    </table>



	<p>&nbsp;</p>
	<table id="table1" style="width: 75%" align="center" class="style3">
<tr bgcolor = #CCCCFF>
		<td class="auto-style3" colspan="2"><strong>Vendor</strong></td>
			<td class="auto-style3"><strong>Generator</strong></td>
		<td class="auto-style3"><strong>City</strong></td>
			<td class="auto-style3"><strong>State</strong></td>
		<td class="auto-style3"><strong>Grade</strong></td>
	</tr>
	<tr>
	
		<td class="style10" style="height: 75px" colspan="2">
		<%= MyRec("Vendor") %></td>
		<td class="style10" style="height: 75px">
	<%= MyRec("Generator") %></td>
		<td class="style10" style="height: 75px">
<%= MyRec("City") %></td>
		<td style="height: 75px" class="style10">
		<%= Myrec("State") %></td>
	<td class="style10" style="height: 75px">
<%= MyRec("Grade") %></td>
	</tr>
	<tr bgcolor = #CCCCFF>
		<td class="auto-style4">Tier</td>
		<td class="auto-style4">Test</td>
			<td class="auto-style4" colspan="4">Comments</td>
	</tr>
	<tr>
	
		<td class="style10" style="height: 75px">
		<input type="text" name="Tier" size="20" style="width: 54px" tabindex="6" value="<%= MyRec("Tier") %>" ></td>
	
		<td class="style10" style="height: 75px">
		<input type="text" name="Test" size="10" style="width: 98px; height: 26px;" tabindex="7" value="<%= MyRec("Test") %>" maxlength="10" ></td>
		<td class="style10" style="height: 75px" colspan="4">
<%= MyRec("Comments") %> &nbsp;</td>
	</tr>

</table>
<br>
		

<p align="center"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></p>
</form>

<!--#include file="Fiberfooter.inc"-->