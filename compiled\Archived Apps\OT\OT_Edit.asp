
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Overtime Detail</TITLE>
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_Session.asp"-->
<!--#include file="classes/asp_cls_DataAccess.asp"-->

</head>
<style type="text/css">
.style3 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
.style4 {
	font-family: Arial;
	font-size: medium;
}
.style5 {
	border-style: solid;
	border-color: #C0C0C0;
	background-color: #E6ECF9;
}
.style6 {
	font-family: Calibri;
}
.style7 {
	border-style: solid;
	border-color: #C0C0C0;
	font-family: Calibri;
	background-color: #E6ECF9;
}
.style8 {
	border-style: solid;
	border-color: #C0C0C0;
	font-family: Calibri;
	background-color: #FFFFFF;
}
</style>

<% Dim strName, strBID, strSite, strType

strid = request.querystring("id")
strdetail = request.querystring("d")

STRSQL = "Select EMP_ID from tblOT_Admin where EMP_ID = '" & Session("EmployeeiD") & "'  "

Set MyS = Server.CreateObject("ADODB.Recordset") 
   		Set MyS = Server.CreateObject("ADODB.RecordSet")
		MyS.Open strSQL, Session("ConnectionString")
If not MyS.eof then
strPost = "OK"
else 
strPost = ""
end if
MyS.close

 
strsql = "Select tblOT_Master.* from tblOT_Master where ID = " & strid
    	Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		if not MyRec.eof then 
strdate = MyREc("OT_week")
strMachine = MyRec("Machine")
strCrew = MyREc("Crew")
str1D = MyRec("Sun_Day")
str2D = MyRec("Mon_day")
str3D = MyREc("Tue_day")
str4D = MyRec("Wed_day")
str5D = MyREc("Thu_Day")
str6D = MyREc("Fri_day")
str7D = MyREc("Sat_Day")
str1N = MyRec("Sun_Night")
str2N = MyRec("Mon_Night")
str3N = MyREc("Tue_Night")
str4N = MyRec("Wed_Night")
str5N = MyREc("Thu_Night")
str6N = MyREc("Fri_Night")
str7N = MyREc("Sat_Night")

end if
MyREc.close


  strsql = "Select Count(ID) as Total from tblOT where (Sun_Option = 'Y' or Sun_Option = 'F') and Sun_DN = 'D'"
 Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		if not MyRec.eof then 
		str1DT = MyRec("Total")
		else
		str1DT = 0
		end if

  strsql = "Select Count(ID) as Total from tblOT where (Sun_Option = 'Y' or Sun_Option = 'F') and Sun_DN = 'N'"
 Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		if not MyRec.eof then 
		str1NT = MyRec("Total")
		else
		str1NT = 0
		end if


  strsql = "Select Count(ID) as Total from tblOT where (Mon_Option = 'Y' or Mon_Option = 'F') and Mon_DN = 'D'"
 Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		if not MyRec.eof then 
		str2DT = MyRec("Total")
		else
		str2DT = 0
		end if

  strsql = "Select Count(ID) as Total from tblOT where (Mon_Option = 'Y' or Mon_Option = 'F') and Mon_DN = 'N'"
 Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		if not MyRec.eof then 
		str2NT = MyRec("Total")
		else
		str2NT = 0
		end if

  strsql = "Select Count(ID) as Total from tblOT where (Tue_Option = 'Y' or Tue_Option = 'F') and Tue_DN = 'D'"
 Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		if not MyRec.eof then 
		str3DT = MyRec("Total")
		else
		str3DT = 0
		end if

  strsql = "Select Count(ID) as Total from tblOT where (Tue_Option = 'Y' or Tue_Option = 'F') and Tue_DN = 'N'"
 Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		if not MyRec.eof then 
		str3NT = MyRec("Total")
		else
		str3NT = 0
		end if
		
		  strsql = "Select Count(ID) as Total from tblOT where (Wed_Option = 'Y' or Wed_Option = 'F') and Wed_DN = 'D'"
 Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		if not MyRec.eof then 
		str4DT = MyRec("Total")
		else
		str4DT = 0
		end if

  strsql = "Select Count(ID) as Total from tblOT where (Wed_Option = 'Y' or Wed_Option = 'F') and Wed_DN = 'N'"
 Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		if not MyRec.eof then 
		str4NT = MyRec("Total")
		else
		str4NT = 0
		end if
		
		  strsql = "Select Count(ID) as Total from tblOT where (Thu_Option = 'Y' or Thu_Option = 'F') and Thu_DN = 'D'"
 Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		if not MyRec.eof then 
		str5DT = MyRec("Total")
		else
		str5DT = 0
		end if

  strsql = "Select Count(ID) as Total from tblOT where (Thu_Option = 'Y' or Thu_Option = 'F') and Thu_DN = 'N'"
 Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		if not MyRec.eof then 
		str5NT = MyRec("Total")
		else
		str5NT = 0
		end if
		
		  strsql = "Select Count(ID) as Total from tblOT where (Fri_Option = 'Y' or Fri_Option = 'F') and Fri_DN = 'D'"
 Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		if not MyRec.eof then 
		str6DT = MyRec("Total")
		else
		str6DT = 0
		end if

  strsql = "Select Count(ID) as Total from tblOT where (Fri_Option = 'Y' or Fri_Option = 'F') and Fri_DN = 'N'"
 Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		if not MyRec.eof then 
		str6NT = MyRec("Total")
		else
		str6NT = 0
		end if
		
		  strsql = "Select Count(ID) as Total from tblOT where (Sat_Option = 'Y' or Sat_Option = 'F') and Sat_DN = 'D'"
 Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		if not MyRec.eof then 
		str7DT = MyRec("Total")
		else
		str7DT = 0
		end if

  strsql = "Select Count(ID) as Total from tblOT where (Sat_Option = 'Y' or Sat_Option = 'F') and Sat_DN = 'N'"
 Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		if not MyRec.eof then 
		str7NT = MyRec("Total")
		else
		str7NT = 0
		end if

		
		







strD1 = datepart("d", strdate)
strdate2 = dateadd("d", 1, strdate)
strD2 = datepart("d", strdate2)
strdate3 = dateadd("d", 1, strdate2)
strD3 = datepart("d", strdate3)
strdate4 = dateadd("d", 1, strdate3)
strD4 = datepart("d", strdate4)
strdate5 = dateadd("d", 1, strdate4)
strD5 = datepart("d", strdate5)
strdate6 = dateadd("d", 1, strdate5)
strD6 = datepart("d", strdate6)
strdate7 = dateadd("d", 1, strdate6)
strD7 = datepart("d", strdate7)

 
 set objGeneral = new ASP_CLS_General
 
 
	


if objGeneral.IsSubmit() Then
if request.form("Delete") = "ON" then

strsql = "Delete from tblOT where ID = " & strdetail
  	 set MyRec = new ASP_CLS_DataAccess
       MyRec.ExecuteSql strSql 
  
    Response.redirect("OT_Entry.asp?id=" & strid)

else


	Call SaveData() 
	
	end if

End if %>

<body><form name="form1" action="OT_Edit.asp?d=<%= strDetail%>&id=<%= strid %>" method="post">
 
<table border="0" cellpadding="0" cellspacing="0" width = 60% style="border-collapse: collapse" bordercolor="000000" height="25" align = center >
  <tr>
    <td  bgcolor="#FFFFFF" bordercolor="#CCCCFF" height="25" >
    <p align="center">
	<span class="style4">Overtime Week&nbsp; for <%= strMachine %>&nbsp;&nbsp; Crew <%= strCrew %> </span></td>
    <td align = center height="25"><font face="Arial"><b><a href="OT_Entry.asp?id=<%= strid %>">RETURN</a></b></font></td>

    <td align = center bgcolor="#FFFFFF" height="25">
    <% if strPost = "OK" then %>
  <Input name="Submit" type="submit" Value="Submit" style="float: right" >
  <% end if %></td>
  </tr>
</table><br><br>
<div align="center">
<table cellpadding="0" cellspacing="0" bordercolor="#808080"  height="10" class="style3" style="width: 90%">
    <tr>
<td height="22" align="center" class="style5" ><span class="style6">Week Of: <%= strdate %></span> </td>
   
  <td height="22" align="center" class="style7" >
	Date <%= strD1 %></td>
   
  <td height="22" align="center" class="style7" >
	Date <%= strD2 %></td>
   
  <td height="22" align="center" class="style7"  >
	Date <%= strD3 %></td>
 
  <td height="22" align="center" class="style7"  >
	Date <%= strD4 %></td>
 
  <td height="22" align="center" class="style7"  >
	Date <%= strD5 %></td>
 
  <td height="22" align="center" class="style7"   >
	Date <%= strD6 %></td>
 
  <td height="22" align="center" class="style7" s  >
	Date <%= strD7 %></td>
 
  </tr>
  
    <tr>
<td height="22" align="center" class="style7" >Day of Week</td>
   
  <td height="22" align="center" class="style7" >
	SUN</td>
   
  <td height="22" align="center" class="style7" >
	MON</td>
   
  <td height="22" align="center" class="style7"  >
	TUE</td>
 
  <td height="22" align="center" class="style7"  >
	WED</td>
 
  <td height="22" align="center" class="style7"  >
	THU</td>
 
  <td height="22" align="center" class="style7"   >
	FRI</td>
 
  <td height="22" align="center" class="style7" s  >
	SAT</td>
 
  	</tr>
	<tr>
<td height="22" align="center" class="style7" >Day Needs</td>
   
  <td height="22" align="center" class="style7" ><%= str1D %> Total   <%= str1D - str1DT %> Remaining </td>
   
  <td height="22" align="center" class="style7" >	<%= str2D %> Total   <%= str2D - str2DT %> Remaining </td>

   
  <td height="22" align="center" class="style7"  ><%= str3D %> Total   <%= str3D - str3DT %> Remaining </td>
  <td height="22" align="center" class="style7"  ><%= str4D %> Total   <%= str4D - str4DT %> Remaining </td>
  <td height="22" align="center" class="style7"  ><%= str5D %> Total   <%= str5D - str5DT %> Remaining </td> 
  <td height="22" align="center" class="style7"   ><%= str6D %> Total   <%= str6D - str6DT %> Remaining </td> 
  <td height="22" align="center" class="style7">	<%= str7D %> Total   <%= str7D - str7DT %> Remaining </td>

 
	</tr>
	<tr>
<td height="22" align="center" class="style7" >Night Needs</td>
   
  <td height="22" align="center" class="style7" ><%= str1N %> Total <%= str1N - str1NT %> Remaining</td>

   
   <td height="22" align="center" class="style7" ><%= str2N %> Total <%= str2N - str2NT %> Remaining</td>

   
  <td height="22" align="center" class="style7"  ><%= str3N %> Total <%= str3N - str3NT %> Remaining</td>

 
  <td height="22" align="center" class="style7"  ><%= str4N %> Total <%= str4N - str4NT %> Remaining</td>

  <td height="22" align="center" class="style7"  ><%= str5N %> Total <%= str5N - str5NT %> Remaining</td>

 
  <td height="22" align="center" class="style7"   >	<%= str6N %> Total <%= str6N - str6NT %> Remaining</td>

 
  <td height="22" align="center" class="style7"  ><%= str7N %> Total <%= str7N - str7NT %> Remaining</td> 
 
	</tr>
	<tr>
<td height="22" align="center" class="style8" colspan="8" >&nbsp;</td>
   
 
  	</tr>
	<tr>
<td height="22" align="center" class="style7" >Technician</td>
   
  <td height="22" align="center" class="style7" >
	&nbsp;</td>
   
  <td height="22" align="center" class="style7" >
		&nbsp;</td>

   
  <td height="22" align="center" class="style7"  >
		&nbsp;</td>

 
  <td height="22" align="center" class="style7"  >
		&nbsp;</td>
  <td height="22" align="center" class="style7"  >
		&nbsp;</td>

 
  <td height="22" align="center" class="style7"   >
		&nbsp;</td>

 
  <td height="22" align="center" class="style7" s  >
		&nbsp;</td>

 
  	</tr>
  	
  	<% strsql = "Select tblOT.* from tblOT where ID = " & strdetail
  	    Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL, Session("ConnectionString")
    if not MyREc2.eof  then%>
  	
  	
	<tr>
<td height="22" align="center" class="style7" >&nbsp;<select name="Name">



 <%  if strCrew = "A" or strCrew = "C" then 
 
 strsql = "SELECT tblOT_Employee.* from tblOT_Employee where Machine = '" & strMachine & "' and Crew = 'A' or Crew = 'C'  order by   EMP_Name"
 
 else
  strsql = "SELECT tblOT_Employee.* from tblOT_Employee where Machine = '" & strMachine & "' and Crew = 'B' or Crew = 'D'  order by   EMP_Name"
 end if

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    while not MyREc.eof %>
    <option <% if MyRec2("OT_Emp_ID") = MyRec("Emp_ID") then %> selected <% end if %> value="<%= MyRec("Emp_ID") %>"><%= MyREc("Emp_name") %></option>
<% MyRec.movenext
wend
MyRec.close %>
	</select></td>
   
  <td height="22" align="center" class="style7" >
	<select name="D_SUN">
	<option value="">Select</option>
<option <% if MyRec2("Sun_Option") = "Y" then %> selected <% end if %> value="Y">Yes</option>
<option <% if MyRec2("Sun_Option") = "N" then %> selected <% end if %> value="N">No</option>
<option <% if MyRec2("Sun_Option") = "F" then %> selected <% end if %> value="F">Forced</option>
<option <% if MyRec2("Sun_Option") = "B" then %> selected <% end if %> value="B">Bumped</option>

	</select>
	<br>
	<input name="Sun" type="radio" value="D" <% if MyRec2("Sun_DN") = "D" Then %> checked <% end if %>>Day  
   <input name="Sun" type="radio" value="N" <% if MyRec2("Sun_DN") = "N" Then %> checked <% end if %>>Night</td>
  <td height="22" align="center" class="style7" >
		<select name="D_MON">
			<option value="">Select</option>
<option <% if MyRec2("Mon_Option") = "Y" then %> selected <% end if %> value="Y">Yes</option>
<option <% if MyRec2("Mon_Option") = "N" then %> selected <% end if %> value="N">No</option>
<option <% if MyRec2("Mon_Option") = "F" then %> selected <% end if %> value="F">Forced</option>
<option <% if MyRec2("Mon_Option") = "B" then %> selected <% end if %> value="B">Bumped</option>

	</select>
	<br>
<input name="Mon" type="radio" value="D" <% if MyRec2("Mon_DN") = "D" Then %> checked <% end if %>>Day  
  <input name="Mon" type="radio" value="N" <% if MyRec2("Mon_DN") = "N" Then %> checked <% end if %>>Night</td>

   
  <td height="22" align="center" class="style7"  >
		<select name="D_TUE">
			<option value="">Select</option>
<option <% if MyRec2("Tue_Option") = "Y" then %> selected <% end if %> value="Y">Yes</option>
<option <% if MyRec2("Tue_Option") = "N" then %> selected <% end if %> value="N">No</option>
<option <% if MyRec2("Tue_Option") = "F" then %> selected <% end if %> value="F">Forced</option>
<option <% if MyRec2("Tue_Option") = "B" then %> selected <% end if %> value="B">Bumped</option>

			</select>
	<br>
<input name="Tue" type="radio" value="D" <% if MyRec2("Tue_DN") = "D" Then %> checked <% end if %>>Day  
  <input name="Tue" type="radio" value="N" <% if MyRec2("Tue_DN") = "N" Then %> checked <% end if %>>Night</td>

 
  <td height="22" align="center" class="style7"  >
		<select name="D_WED">
			<option value="">Select</option>
<option <% if MyRec2("Wed_Option") = "Y" then %> selected <% end if %> value="Y">Yes</option>
<option <% if MyRec2("Wed_Option") = "N" then %> selected <% end if %> value="N">No</option>
<option <% if MyRec2("Wed_Option") = "F" then %> selected <% end if %> value="F">Forced</option>
<option <% if MyRec2("Wed_Option") = "B" then %> selected <% end if %> value="B">Bumped</option>

		</select>
	<br>
<input name="Wed" type="radio" value="D" <% if MyRec2("Wed_DN") = "D" Then %> checked <% end if %>>Day  
  <input name="Wed" type="radio" value="N" <% if MyRec2("Wed_DN") = "N" Then %> checked <% end if %>>Night</td>
  <td height="22" align="center" class="style7"  >
		<select name="D_THU">
			<option value="">Select</option>
<option <% if MyRec2("Thu_Option") = "Y" then %> selected <% end if %> value="Y">Yes</option>
<option <% if MyRec2("Thu_Option") = "N" then %> selected <% end if %> value="N">No</option>
<option <% if MyRec2("Thu_Option") = "F" then %> selected <% end if %> value="F">Forced</option>
<option <% if MyRec2("Thu_Option") = "B" then %> selected <% end if %> value="B">Bumped</option>

			</select>
	<br>
<input name="Thu" type="radio" value="D" <% if MyRec2("Thu_DN") = "D" Then %> checked <% end if %>>Day  
  <input name="Thu" type="radio" value="N" <% if MyRec2("Thu_DN") = "N" Then %> checked <% end if %>>Night</td>
 
  <td height="22" align="center" class="style7"   >
		<select name="D_FRI">
			<option value="">Select</option>
<option <% if MyRec2("Fri_Option") = "Y" then %> selected <% end if %> value="Y">Yes</option>
<option <% if MyRec2("Fri_Option") = "N" then %> selected <% end if %> value="N">No</option>
<option <% if MyRec2("Fri_Option") = "F" then %> selected <% end if %> value="F">Forced</option>
<option <% if MyRec2("Fri_Option") = "B" then %> selected <% end if %> value="B">Bumped</option>

			</select>
	<br>
<input name="Fri" type="radio" value="D" <% if MyRec2("Fri_DN") = "D" Then %> checked <% end if %>>Day  
  <input name="Fri" type="radio" value="N" <% if MyRec2("Fri_DN") = "N" Then %> checked <% end if %>>Night</td>
 
  <td height="22" align="center" class="style7" s  >
		<select name="D_SAT">
			<option value="">Select</option>
<option <% if MyRec2("Sat_Option") = "Y" then %> selected <% end if %> value="Y">Yes</option>
<option <% if MyRec2("Sat_Option") = "N" then %> selected <% end if %> value="N">No</option>
<option <% if MyRec2("Sat_Option") = "F" then %> selected <% end if %> value="F">Forced</option>
<option <% if MyRec2("Sat_Option") = "B" then %> selected <% end if %> value="B">Bumped</option>

		</select>
	<br>
<input name="Sat" type="radio" value="D" <% if MyRec2("Sat_DN") = "D" Then %> checked <% end if %>>Day  
  <input name="Sat" type="radio" value="N" <% if MyRec2("Sat_DN") = "N" Then %> checked <% end if %>>Night</td>

 
  	</tr>
  
      <tr>
<td align="center" class="style7" colspan="8" style="height: 51px" ><input type="checkbox" name="Delete" value="ON">Delete this 
Record</td>
   
  	</tr>
 
 <% end if %>
  </table>
</div>



</form>
   
  

</body>
 <%

  
  Function SaveData()

 strid = request.querystring("id")
 strdetail = request.querystring("d")
 
 strBID = request.form("Name")
 strsql = "SELECT Emp_Name from tblOT_Employee where EMP_ID = '" & strBID & "'"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    strName = MyRec("Emp_name")
    MyRec.close
    
    If request.form("Sun") = "D" then
    strS1 = "D"
    elseif request.form("Sun") = "N" then
    strS1 = "N"
    else
    strS1 = ""
    end if
    
        If request.form("Mon") = "D" then
    strS2 = "D"
    elseif request.form("Mon") = "N" then
    strS2 = "N"
    else
    strS2 = ""
    end if
    
    If request.form("Tue") = "D" then
    strS3 = "D"
    elseif request.form("Tue") = "N" then
    strS3 = "N"
    else
    strS3 = ""
    end if
    
    If request.form("Wed") = "D" then
    strS4 = "D"
    elseif request.form("Wed") = "N" then
    strS4 = "N"
    else
    strS4 = ""
    end if
    
    If request.form("Thu") = "D" then
    strS5 = "D"
    elseif request.form("Thu") = "N" then
    strS5 = "N"
    else
    strS5 = ""
    end if
    
    
    If request.form("Fri") = "D" then
    strS6 = "D"
    elseif request.form("Fri") = "N" then
    strS6 = "N"
    else
    strS6 = ""
    end if
    
    If request.form("Sat") = "D" then
    strS7 = "D"
    elseif request.form("Sat") = "N" then
    strS7 = "N"
    else
    strS7 = ""
    end if







 

 
  strsql = "Update tblOT Set Sun_Option = '" & request.form("D_Sun") & "', Mon_Option = '" & request.form("D_Mon") & "', Tue_Option = '" & request.form("D_Tue") & "', "_
  &"  Wed_Option = '" & request.form("D_Wed") & "', Thu_option = '" & request.form("D_Thu") & "', Fri_Option =  '" & request.form("D_Fri") & "', Sat_option = '" & request.form("D_Sat") & "', "_
  &"  Sun_DN = '" & strS1 & "', Mon_DN =  '" & strS2 & "', Tue_DN = '" & strS3 & "', Wed_DN = '" & strS4 & "', Thu_DN = '" & strS5 & "', Fri_DN =  '" & strS6 & "', Sat_DN = '" & strS7 & "', "_
  &" OT_Emp_ID = '" & strBID & "',  Emp_Name = '" & strName & "', PL_EMP_ID = '" & Session("EmployeeID") & "' where ID = " & strdetail 
   
  	 set MyRec = new ASP_CLS_DataAccess
      MyRec.ExecuteSql strSql 
    '  Response.write ("strsql: " & strsql) 
    Response.redirect("OT_Entry.asp?id=" & strid)
  End Function
  
   %><!--#include file="footer.inc"-->