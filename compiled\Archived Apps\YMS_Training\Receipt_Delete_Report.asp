																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Deletion Report</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->



<% Dim MyRec, strsql, strid, MyConn, strsqlC, strsql3, MyRec2
strid = Request.querystring("id")

    strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "' and User_type = 'A'"


    Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL3, Session("ConnectionString")


If not Myrec2.eof then


strsql = "SELECT  BID, DDate, Comment, CID from tblMovement where From_location = 'Deleted' order by CID desc"
   
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    
    If not MyRec.eof then
    


%>

<style type="text/css">
.style1 {
	font-family: Arial;
	font-size: x-small;
}
</style>
</head>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=0>
<tr>

<td align = center><b>
<font face="arial" size="4" >Receipt Delete Report </td>


</tr>
	    </table><br>

	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=60% class = "tablecolor1" border=0 align="center">  
	 <tr class="tableheader">

	<td class="style1"  ><strong>Receipt #</strong></td>
    	<td align = left ><font face="Arial" size="2"><strong>Date</strong></font></td>

<td  ><b><font face="Arial" size="2">BID</font></b></td>
<td  ><b><font face="Arial" size="2">Notes</font></b></td>
	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    


		
 
   <td  >  <font size="2" face="Arial">        <%= MyRec.fields("CID")%>&nbsp;</font></td>
 <td  >  <font size="2" face="Arial">        <%= MyRec.fields("DDate")%>&nbsp;</font></td>
	
  <td  >  <font size="2" face="Arial">        <%= MyRec.fields("BID")%>&nbsp;</font></td>	
	<td><font size="2" face="Arial"><%= MyRec.fields("Comment")%>&nbsp;</font></td>

	</tr>

 <%    ii = ii + 1
       MyRec.MoveNext
    
   Wend
 end if  
 
 else
 Response.write("<b><font face=arial size=3>You do not have authorization to view this page</font></b>")

end if
 Myrec2.close  %>