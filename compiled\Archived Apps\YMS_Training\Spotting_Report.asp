																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Recovered Paper Spotting Report</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->



<% Dim MyRec, strsql, strDays, strDate, gVendor, gCount, gTCount, strsql1, MyConn, MyRec1, strsql2, strsql3, MyRec3, strFee, strFree
Dim strtdate, strYdate, strAdate, icount, icountP, icountF, strdate1, strGenerator, strGenCity, strVendor


strtdate = formatdatetime(now(),2)
strYdate = dateadd("h", -60, Now())
strAdate = dateadd("d", -5, strtdate)
strYdate = formatdatetime(strYdate)
strAdate = formatdatetime(strAdate)
strdate1 = Now()
strdate1 = formatdatetime(strdate1)




strsql2 = "DELETE FROM tblTempSpotReport WHERE (((tblTempSpotReport.TID)>0))"
set MyRec1 = new ASP_CLS_DataAccess
         MyRec1.ExecuteSql strSql2 

	strdate = formatdatetime(now(),2)
gcount = 0
gVendor = ""

strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE date_Received is not null AND ((Species)='KCOP' or (Species) = 'KBLD') AND Location='Yard' and Rejected is null "_
 &" AND Trailer Is Not Null  ORDER BY Vendor, Date_received"

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
       while not MyRec.Eof 
       
              if len(MyRec.fields("Generator")) > 0 then 
strGenerator = Replace(MyRec.fields("Generator"), "'", "''")
else
strGenerator = ""
end if 

if len(MyRec.fields("Gen_city")) > 0 then 
strGenCity = Replace(MyRec.fields("Gen_city"), "'", "''")
else
strGenCity = ""
end if 

If len(MyRec.fields("Vendor")) > 0 then
strVendor = Replace(MyRec.fields("Vendor"), "'", "''")
else
strVendor = ""
end if 

   			
     if MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      strSpecies = MyRec("Species")
      
      If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "', '" & MyRec.fields("PS") & "', "_

     &" '" & MyRec.fields("PO") & "', '" & strVendor  & "', '" & strGenerator & "', '" & strGenCity & "', "_
     &" '" & strAudit & "', " & strDays & ",  '" & strSpecies & "'"
     
     else
     
    
     strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "', '" & strVendor  & "', '" & strGenerator & "', '" & strGenCity & "', "_
     &" '" & strAudit & "', " & strDays & ",  '" & strSpecies & "'"
     
     end if

    set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql1        
        
       MyRec.MoveNext
 
       
       wend
       MyRec.close
       


             

       
       
       

strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where (Type = 'KCOP' or Type = 'KBLD')  and days > 4.9 order by days desc "

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
     
%>


<style type="text/css">
.style1 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.style2 {
	font-size: x-small;
	font-weight: bold;
}
.style3 {
	font-size: x-small;
}
</style>
</head>


<body>

<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=0>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><b>
<font face="arial" size="4" >Spotting Report <%= strDate%><br>&nbsp;</font></b></td>
<td align = center>&nbsp;</td>


</tr>
	    </table>

<p align = center><b>
<font face="arial" size="4" >Critical Loads (On the Yard More than 5 Days - Consume 
First)<br>KBLD</p>	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center  ><font face="Arial" size="2"><b>Request<br> to Spot</b></font></td>

		<td align = center  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center>
		<p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left><font face="Arial" size="2"><b>Release</b></font></td>
<td align=left><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left>
<p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>

<td align="center" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>


	</tr>

 <% Dim ii
       ii = 0
       icount = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    
 

<td bordercolor="#000000">


	<p align="center"><input type="checkbox" name="C1" value="ON"></p></td>

	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Rec_nbr")%>&nbsp;</font></td>
    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
         
       <%   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %>   
    <% if MyRec.fields("Days") > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>	
	


	</tr>

 <%   If MyRec.fields("Carrier") <> "Rail" or MyRec.fields("Carrier") <> "RAIL" then
       icount = icount + 1
       end if
 		 ii = ii + 1
       MyRec.MoveNext
    
    
	%>

<%  Wend %>
<% MyRec.close%>

</table>

<%   ' HBX

strsql2 = "DELETE FROM tblTempSpotReport WHERE (((tblTempSpotReport.TID)>0))"
set MyRec1 = new ASP_CLS_DataAccess
         MyRec1.ExecuteSql strSql2 

	strdate = formatdatetime(now(),2)
gcount = 0
gVendor = ""

strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE date_Received is not null AND Species='HBX'   AND Location='Yard' and Rejected is null  "_
 &" AND Trailer Is Not Null   ORDER BY Vendor, Date_received"




 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
       while not MyRec.Eof

    
    
     if MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      
   If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "', '" & MyRec.fields("PS") & "', "_

        &" '" & MyRec.fields("PO") & "', '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "', "_
     &" '" & strAudit & "', " & strDays & ",  'HBX'"
     
     else
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days, [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "', '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "', "_
     &" '" & strAudit & "', " & strDays & ", 'HBX'"
end if
    set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql1
         
         
     
      If MyRec.fields("Carrier") <> "Rail" or MyRec.fields("Carrier") <> "RAIL" then
       icount = icount + 1
       end if

 		 ii = ii + 1
       MyRec.MoveNext   
    
 Wend
  MyRec.close
       
       
 


strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where Type = 'HBX'  and days > 4.9 order by days desc "

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    If not MyRec.eof then
     
%>

<p align = center><b>
<font face="arial" size="4" >Critical Loads (On the Yard More than 5 Days - Consume 
First)<br>HBX</p>	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center  ><font face="Arial" size="2"><b>Request<br> to Spot</b></font></td>

		<td align = center  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center>
		<p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left><font face="Arial" size="2"><b>Release</b></font></td>

<td align=left><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left>
<p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>

<td align="center" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>


	</tr>

 <% 
       ii = 0

       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
 

<td bordercolor="#000000">


	<p align="center"><input type="checkbox" name="C1" value="ON"></p></td>

	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Rec_nbr")%>&nbsp;</font></td>


    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
         
       <%   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %>   
    <% if MyRec.fields("Days") > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>	
	


	</tr>

 <%   
       If MyRec.fields("Carrier") <> "Rail" or MyRec.fields("Carrier") <> "RAIL" then
       icount = icount + 1
       end if

 		 ii = ii + 1
       MyRec.MoveNext
    
    
	%>

<%  Wend
end if %>
<% MyRec.close%>

</table> <br>



<%   ' SHRED

strsql2 = "DELETE FROM tblTempSpotReport WHERE (((tblTempSpotReport.TID)>0))"
set MyRec1 = new ASP_CLS_DataAccess
         MyRec1.ExecuteSql strSql2 

	strdate = formatdatetime(now(),2)
gcount = 0
gVendor = ""

strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE date_Received is not null AND Species='SHRED' and (SHRED_OCC = Null or SHRED_OCC = 0)  AND Location='Yard' and Rejected is null  "_
 &" AND Trailer Is Not Null   ORDER BY Vendor, Date_received"

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
       while not MyRec.Eof

    
    
     if MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      
   If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, PO, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "',  "_
     &" '" & MyRec.fields("PO") & "', '" & strVendor  & "', '" & strGenerator & "', '" & strGenCity & "', "_
     &" '" & strAudit & "', " & strDays & ",  'SHRED'"
     
     else
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days, [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "', '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "', "_
     &" '" & strAudit & "', " & strDays & ", 'SHRED'"
end if
    set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql1
         
         
     
      If MyRec.fields("Carrier") <> "Rail" or MyRec.fields("Carrier") <> "RAIL" then
       icount = icount + 1
       end if

 		 ii = ii + 1
       MyRec.MoveNext   
    
 Wend
  MyRec.close
       
       
 


strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where Type = 'SHRED'  and days > 4.9 order by days desc "

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    If not MyRec.eof then
     
%>

<p align = center><b>
<font face="arial" size="4" >Critical Loads (On the Yard More than 5 Days - Consume 
First)<br>SHRED</p>	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center  ><font face="Arial" size="2"><b>Request<br> to Spot</b></font></td>

		<td align = center  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center>
		<p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left><font face="Arial" size="2"><b>Release</b></font></td>

<td align=left><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left>
<p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>

<td align="center" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>


	</tr>

 <% 
       ii = 0

       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
 

<td bordercolor="#000000">


	<p align="center"><input type="checkbox" name="C1" value="ON"></p></td>

	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Rec_nbr")%>&nbsp;</font></td>
 

    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
         
       <%   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %>   
    <% if MyRec.fields("Days") > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>	
	


	</tr>

 <%   
       If MyRec.fields("Carrier") <> "Rail" or MyRec.fields("Carrier") <> "RAIL" then
       icount = icount + 1
       end if

 		 ii = ii + 1
       MyRec.MoveNext
    
    
	%>

<%  Wend
end if %>
<% MyRec.close%>

</table> <br>

<%   ' OF

strsql2 = "DELETE FROM tblTempSpotReport WHERE (((tblTempSpotReport.TID)>0))"
set MyRec1 = new ASP_CLS_DataAccess
         MyRec1.ExecuteSql strSql2 

	strdate = formatdatetime(now(),2)
gcount = 0
gVendor = ""

strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE date_Received is not null AND (Species='OF' or Species = 'of' or Species = 'OF3' or Species = 'of3') AND Location='Yard' and Rejected is null  "_
 &" AND Trailer Is Not Null   ORDER BY Vendor, Date_received"




 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
       while not MyRec.Eof

    
    
     if MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      
   If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "',  '" & MyRec.fields("PS") & "', "_

     &" '" & MyRec.fields("PO") & "', '" & strVendor  & "', '" & strGenerator & "', '" & strGenCity & "', "_
     &" '" & strAudit & "', " & strDays & ",  'OF'"
     
     else
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days, [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "', '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "', "_
     &" '" & strAudit & "', " & strDays & ", 'OF'"
	end if
    set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql1
         
         
     
      If MyRec.fields("Carrier") <> "Rail" or MyRec.fields("Carrier") <> "RAIL" then
       icount = icount + 1
       end if

 		 ii = ii + 1
       MyRec.MoveNext   
    
 Wend
  MyRec.close
       
       
 


strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where Type = 'OF'  and days > 4.9 order by days desc "

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    If not MyRec.eof then
     
%>

<p align = center><b>
<font face="arial" size="4" >Critical Loads (On the Yard More than 5 Days - Consume 
First)<br>OF3</p>	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center  ><font face="Arial" size="2"><b>Request<br> to Spot</b></font></td>

		<td align = center  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center>
		<p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left><font face="Arial" size="2"><b>Release</b></font></td>

<td align=left><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left>
<p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>

<td align="center" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>


	</tr>

 <% 
       ii = 0

       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
 

<td bordercolor="#000000">


	<p align="center"><input type="checkbox" name="C1" value="ON"></p></td>

	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Rec_nbr")%>&nbsp;</font></td>
 

    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
         
       <%   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %>   
    <% if MyRec.fields("Days") > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>	
	


	</tr>

 <%   
       If MyRec.fields("Carrier") <> "Rail" or MyRec.fields("Carrier") <> "RAIL" then
       icount = icount + 1
       end if

 		 ii = ii + 1
       MyRec.MoveNext
    
    
	%>

<%  Wend
end if %>
<% MyRec.close%>

</table> <br>

<%   ' PMX

strsql2 = "DELETE FROM tblTempSpotReport WHERE (((tblTempSpotReport.TID)>0))"
set MyRec1 = new ASP_CLS_DataAccess
         MyRec1.ExecuteSql strSql2 

	strdate = formatdatetime(now(),2)
gcount = 0
gVendor = ""

strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE date_Received is not null AND (Species='PMX' or Species = 'pmx') AND Location='Yard' and Rejected is null  "_
 &" AND Trailer Is Not Null   ORDER BY Vendor, Date_received"




 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
       while not MyRec.Eof

    
    
     if MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      
   If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "', '" & MyRec.fields("PS") & "', "_
       &" '" & MyRec.fields("PO") & "', '" & strVendor  & "', '" & strGenerator & "', '" & strGenCity & "', "_
     &" '" & strAudit & "', " & strDays & ",  'PMX'"
     
     else
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days, [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "', '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "', "_
     &" '" & strAudit & "', " & strDays & ", 'PMX'"
end if
    set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql1
         
         
     
     

 		 ii = ii + 1
       MyRec.MoveNext   
    
 Wend
  MyRec.close
       
       
 


strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where Type = 'PMX'  and days > 4.9 order by days desc "

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    If not MyRec.eof then
     
%>

<p align = center><b>

<font face="arial" size="4" >Critical Loads (On the Yard More than 5 Days - Consume 
First)<br>PMX</p>	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center  ><font face="Arial" size="2"><b>Request<br> to Spot</b></font></td>

		<td align = center  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center>
		<p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left><font face="Arial" size="2"><b>Release</b></font></td>

<td align=left><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left>
<p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>

<td align="center" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>


	</tr>

 <% 
       ii = 0

       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
 

<td bordercolor="#000000">


	<p align="center"><input type="checkbox" name="C1" value="ON"></p></td>

	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Rec_nbr")%>&nbsp;</font></td>

    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
         
       <%   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %>   
    <% if MyRec.fields("Days") > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>	
	


	</tr>

 <%   
        If MyRec.fields("Carrier") <> "Rail" or MyRec.fields("Carrier") <> "RAIL" then
       icount = icount + 1
       end if

 		 ii = ii + 1
       MyRec.MoveNext
    
    
	%>

<%  Wend
end if %>
<% MyRec.close%>

</table> <br>





<%  'KCOP
strsql2 = "DELETE FROM tblTempSpotReport WHERE (((tblTempSpotReport.TID)>0))"
set MyRec1 = new ASP_CLS_DataAccess
         MyRec1.ExecuteSql strSql2 

	strdate = formatdatetime(now(),2)
gcount = 0
gVendor = ""


strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE date_Received > '" & strAdate & "'  and date_received <= '" & strYdate & "' AND ((Species)='KCOP' or (Species) = 'KBLD') AND Location='Yard' and Rejected is null  "_
 &" AND Trailer Is Not Null   ORDER BY Generator, Date_received"
 
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
       while not MyRec.Eof

          if len(MyRec.fields("Generator")) > 0 then 
strGenerator = Replace(MyRec.fields("Generator"), "'", "''")
else
strGenerator = ""
end if 

if len(MyRec.fields("Gen_city")) > 0 then 
strGenCity = Replace(MyRec.fields("Gen_city"), "'", "''")
else
strGenCity = ""
end if 
 
    
     if MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      
      If len(MyRec.fields("Vendor")) > 0 then
      strVendor = Replace(MyRec.fields("Vendor"), "'", "''")
      else
      strVendor = ""
      end if
      strSpecies = MyRec("Species")
      
            If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
      &" '" & MyRec.fields("PO") & "', '" & strVendor  & "', '" & strGenerator & "', '" & strGenCity & "', "_
     &" '" & strAudit & "', " & strDays & ",  '" & strSpecies & "'"
     
     else
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "','" & strVendor  & "', '" & strGenerator & "', '" & strGenCity & "', "_
     &" '" & strAudit & "', " & strDays & ",  '" & strSpecies & "'"
end if
    set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql1
         
     	 If MyRec.fields("Carrier") <> "Rail" or MyRec.fields("Carrier") <> "RAIL" then
       icount = icount + 1
       end if

       MyRec.MoveNext
   
       
       wend
       MyRec.close
       
       
       
       

strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where (Type = 'KCOP' or Type = 'KBLD')   order by days desc "

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
     
%>


<br>
	


<p align = center><b>
<font face="arial" size="4" >Additional Loads (On the Yard 2 1/2 - 4 Days - Consume Next)<br>KBLD</p>	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center style="height: 23px"  ><font face="Arial" size="2"><b>Request<br> to Spot</b></font></td>

		<td align = center style="height: 23px"  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center style="height: 23px">
		<p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left style="height: 23px"><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left style="height: 23px"><font face="Arial" size="2"><b>Release</b></font></td>
<td align=left style="height: 23px"><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left style="height: 23px"><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left style="height: 23px">
<p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>

<td align="center" style="height: 23px" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" style="height: 23px" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>


	</tr>

 <% 
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    
 

<td bordercolor="#000000">


	<p align="center"><input type="checkbox" name="C1" value="ON"></p></td>

	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Rec_nbr")%>&nbsp;</font></td>

 

    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
         
       <%   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %>   
    <% if MyRec.fields("Days") > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>	
	


	</tr>

 <%   
       If MyRec.fields("Carrier") <> "Rail" or MyRec.fields("Carrier") <> "RAIL" then
       icount = icount + 1
       end if

 		 ii = ii + 1
       MyRec.MoveNext
    
    
	%>

<%  Wend %>
<% MyRec.close%>

</table>
	


<br>



<%  'HBX
strsql2 = "DELETE FROM tblTempSpotReport WHERE (((tblTempSpotReport.TID)>0))"
set MyRec1 = new ASP_CLS_DataAccess
         MyRec1.ExecuteSql strSql2 

	strdate = formatdatetime(now(),2)
gcount = 0
gVendor = ""


strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE date_Received > '" & strAdate & "'  and date_received <= '" & strYdate & "' AND Species='HBX' AND Location='Yard' and Rejected is null  "_
 &" AND Trailer Is Not Null   ORDER BY Generator, Date_received"
 
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
       while not MyRec.Eof

          if len(MyRec.fields("Generator")) > 0 then 
strGenerator = Replace(MyRec.fields("Generator"), "'", "''")
else
strGenerator = ""
end if 

if len(MyRec.fields("Gen_city")) > 0 then 
strGenCity = Replace(MyRec.fields("Gen_city"), "'", "''")
else
strGenCity = ""
end if 
 
    
     if MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      
      If len(MyRec.fields("Vendor")) > 0 then
      strVendor = Replace(MyRec.fields("Vendor"), "'", "''")
      else
      strVendor = ""
      end if
      
            If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "', '" & MyRec.fields("PS") & "', "_
        &" '" & MyRec.fields("PO") & "', '" & strVendor  & "', '" & strGenerator & "', '" & strGenCity & "', "_
     &" '" & strAudit & "', " & strDays & ",  'HBX'"
     
     else
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "','" & strVendor  & "', '" & strGenerator & "', '" & strGenCity & "', "_
     &" '" & strAudit & "', " & strDays & ",  'HBX'"
end if
    set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql1
         
     	 If MyRec.fields("Carrier") <> "Rail" or MyRec.fields("Carrier") <> "RAIL" then
       icount = icount + 1
       end if

       MyRec.MoveNext
   
       
       wend
       MyRec.close
       
       
       
       

strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where Type = 'HBX'   order by days desc "

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    If not MyRec.eof then 
     
%>


<br>
	


<p align = center><b>
<font face="arial" size="4" >Additional Loads (On the Yard 2 1/2 - 4 Days - Consume Next)<br>HBX</p>	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center style="height: 23px"  ><font face="Arial" size="2"><b>Request<br> to Spot</b></font></td>

		<td align = center style="height: 23px"  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center style="height: 23px">
		<p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left style="height: 23px"><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left style="height: 23px"><font face="Arial" size="2"><b>Release</b></font></td>
<td align=left style="height: 23px"><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left style="height: 23px"><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left style="height: 23px">
<p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>

<td align="center" style="height: 23px" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" style="height: 23px" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>


	</tr>

 <% 
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    
 

<td bordercolor="#000000">


	<p align="center"><input type="checkbox" name="C1" value="ON"></p></td>

	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Rec_nbr")%>&nbsp;</font></td>

  

    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
         
       <%   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %>   
    <% if MyRec.fields("Days") > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>	
	


	</tr>

 <%   
       If MyRec.fields("Carrier") <> "Rail" or MyRec.fields("Carrier") <> "RAIL" then
       icount = icount + 1
       end if

 		 ii = ii + 1
       MyRec.MoveNext
    
    
	%>

<%  Wend %>
<% MyRec.close
end if%>

</table>
	


<br>

<%  'SHRED
strsql2 = "DELETE FROM tblTempSpotReport WHERE (((tblTempSpotReport.TID)>0))"
set MyRec1 = new ASP_CLS_DataAccess
         MyRec1.ExecuteSql strSql2 

	strdate = formatdatetime(now(),2)
gcount = 0
gVendor = ""


strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE date_Received > '" & strAdate & "'  and date_received <= '" & strYdate & "' AND Species='SHRED' and (Shred_OCC = 0 or Shred_OCC = Null) AND Location='Yard' and Rejected is null  "_
 &" AND Trailer Is Not Null   ORDER BY Generator, Date_received"
 
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
       while not MyRec.Eof

          if len(MyRec.fields("Generator")) > 0 then 
strGenerator = Replace(MyRec.fields("Generator"), "'", "''")
else
strGenerator = ""
end if 

if len(MyRec.fields("Gen_city")) > 0 then 
strGenCity = Replace(MyRec.fields("Gen_city"), "'", "''")
else
strGenCity = ""
end if 
 
    
     if MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      
      If len(MyRec.fields("Vendor")) > 0 then
      strVendor = Replace(MyRec.fields("Vendor"), "'", "''")
      else
      strVendor = ""
      end if
      
            If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "', '" & MyRec.fields("PS") & "', "_
     &" '" & MyRec.fields("PO") & "', '" & strVendor  & "', '" & strGenerator & "', '" & strGenCity & "', "_
     &" '" & strAudit & "', " & strDays & ",  'SHRED'"
     
     else
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "','" & strVendor  & "', '" & strGenerator & "', '" & strGenCity & "', "_
     &" '" & strAudit & "', " & strDays & ",  'SHRED'"
end if
    set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql1
         
     	 If MyRec.fields("Carrier") <> "Rail" or MyRec.fields("Carrier") <> "RAIL" then
       icount = icount + 1
       end if

       MyRec.MoveNext
   
       
       wend
       MyRec.close
       
       
       
       

strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where Type = 'SHRED'   order by days desc "

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyREc.eof then
     
%>


<br>
	


<p align = center><b>
<font face="arial" size="4" >Additional Loads (On the Yard 2 1/2 - 4 Days - Consume Next)<br>SHRED</p>	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center style="height: 23px"  ><font face="Arial" size="2"><b>Request<br> to Spot</b></font></td>

		<td align = center style="height: 23px"  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center style="height: 23px">
		<p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left style="height: 23px"><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left style="height: 23px"><font face="Arial" size="2"><b>Release</b></font></td>
<td align=left style="height: 23px"><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left style="height: 23px"><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left style="height: 23px">
<p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>

<td align="center" style="height: 23px" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" style="height: 23px" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>


	</tr>

 <% 
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    
 

<td bordercolor="#000000">


	<p align="center"><input type="checkbox" name="C1" value="ON"></p></td>

	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Rec_nbr")%>&nbsp;</font></td>

   

    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
         
       <%   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %>   
    <% if MyRec.fields("Days") > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>	
	


	</tr>

 <%   
       If MyRec.fields("Carrier") <> "Rail" or MyRec.fields("Carrier") <> "RAIL" then
       icount = icount + 1
       end if

 		 ii = ii + 1
       MyRec.MoveNext
    
    
	%>

<%  Wend %>
<% MyRec.close
end if %>

</table>
	


<br>

<%  'OF
strsql2 = "DELETE FROM tblTempSpotReport WHERE (((tblTempSpotReport.TID)>0))"
set MyRec1 = new ASP_CLS_DataAccess
         MyRec1.ExecuteSql strSql2 

	strdate = formatdatetime(now(),2)


strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE date_Received > '" & strAdate & "'  and date_received <= '" & strYdate & "' AND (Species='OF' or species = 'of' or Species = 'OF3' or Species = 'of3') AND Location='Yard' and Rejected is null "_
 &" AND Trailer Is Not Null   ORDER BY Generator, Date_received"
 
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
       while not MyRec.Eof

          if len(MyRec.fields("Generator")) > 0 then 
strGenerator = Replace(MyRec.fields("Generator"), "'", "''")
else
strGenerator = ""
end if 

if len(MyRec.fields("Gen_city")) > 0 then 
strGenCity = Replace(MyRec.fields("Gen_city"), "'", "''")
else
strGenCity = ""
end if 
 
    
     if MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      
      If len(MyRec.fields("Vendor")) > 0 then
      strVendor = Replace(MyRec.fields("Vendor"), "'", "''")
      else
      strVendor = ""
      end if
      
            If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_

    &" '" & MyRec.fields("PO") & "', '" & strVendor  & "', '" & strGenerator & "', '" & strGenCity & "', "_
     &" '" & strAudit & "', " & strDays & ",  'OF'"
     
     else
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "','" & strVendor  & "', '" & strGenerator & "', '" & strGenCity & "', "_
     &" '" & strAudit & "', " & strDays & ",  'OF'"
end if
    set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql1
         
      If MyRec.fields("Carrier") <> "Rail" or MyRec.fields("Carrier") <> "RAIL" then
       icount = icount + 1
       end if

       MyRec.MoveNext
   
       
       wend
       MyRec.close
       
       
       
       

strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where Type = 'OF'   order by days desc "

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
     if not MyRec.eof then %>


<br>
	


<p align = center><b>
<font face="arial" size="4" >Additional Loads (On the Yard 2 1/2 - 4 Days - Consume Next)<br>OF3</p>	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center  ><font face="Arial" size="2"><b>Request<br> to Spot</b></font></td>

		<td align = center  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center>
		<p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left><font face="Arial" size="2"><b>Release</b></font></td>
<td align=left><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left>
<p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>

<td align="center" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>


	</tr>

 <% 
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    
 

<td bordercolor="#000000">


	<p align="center"><input type="checkbox" name="C1" value="ON"></p></td>

	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Rec_nbr")%>&nbsp;</font></td>

  

    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
         
       <%   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %>   
    <% if MyRec.fields("Days") > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>	
	


	</tr>

 <%   
        If MyRec.fields("Carrier") <> "Rail" or MyRec.fields("Carrier") <> "RAIL" then
       icount = icount + 1
       end if

 		 ii = ii + 1
       MyRec.MoveNext
    
    
	%>

<%  Wend %>
<% MyRec.close%>

</table>
<% end if %>	


<br>

<%  'PMX
strsql2 = "DELETE FROM tblTempSpotReport WHERE (((tblTempSpotReport.TID)>0))"
set MyRec1 = new ASP_CLS_DataAccess
         MyRec1.ExecuteSql strSql2 

	strdate = formatdatetime(now(),2)
gcount = 0
gVendor = ""


strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE date_Received > '" & strAdate & "'  and date_received <= '" & strYdate & "' AND (Species='PMX' or Species = 'pmx') AND Location='Yard' and Rejected is null  "_
 &" AND Trailer Is Not Null   ORDER BY Generator, Date_received"
 
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
       while not MyRec.Eof

          if len(MyRec.fields("Generator")) > 0 then 
strGenerator = Replace(MyRec.fields("Generator"), "'", "''")
else
strGenerator = ""
end if 

if len(MyRec.fields("Gen_city")) > 0 then 
strGenCity = Replace(MyRec.fields("Gen_city"), "'", "''")
else
strGenCity = ""
end if 
 
    
     if MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      
      If len(MyRec.fields("Vendor")) > 0 then
      strVendor = Replace(MyRec.fields("Vendor"), "'", "''")
      else
      strVendor = ""
      end if
      
            If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_

       &" '" & MyRec.fields("PO") & "', '" & strVendor  & "', '" & strGenerator & "', '" & strGenCity & "', "_
     &" '" & strAudit & "', " & strDays & ",  'PMX'"
     
     else
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "','" & strVendor  & "', '" & strGenerator & "', '" & strGenCity & "', "_
     &" '" & strAudit & "', " & strDays & ",  'PMX'"
end if
    set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql1
         
   

       MyRec.MoveNext
   
       
       wend
       MyRec.close
       
       
       
       

strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where Type = 'PMX'   order by days desc "

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
     if not MyRec.eof then
%>


<br>
	


<p align = center><b>
<font face="arial" size="4" >Additional Loads (On the Yard 2 1/2 - 4 Days - Consume Next)<br>PMX</p>	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center  ><font face="Arial" size="2"><b>Request<br> to Spot</b></font></td>

		<td align = center  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center>
		<p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left><font face="Arial" size="2"><b>Release</b></font></td>
<td align=left><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left>
<p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>

<td align="center" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>


	</tr>

 <% 
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    
 

<td bordercolor="#000000">


	<p align="center"><input type="checkbox" name="C1" value="ON"></p></td>

	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Rec_nbr")%>&nbsp;</font></td>

    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
         
       <%   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %>   
    <% if MyRec.fields("Days") > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>	
	


	</tr>

 <%   
        If MyRec.fields("Carrier") <> "Rail" or MyRec.fields("Carrier") <> "RAIL" then
       icount = icount + 1
       end if

 		 ii = ii + 1
       MyRec.MoveNext
    
    
	%>

<%  Wend %>
<% MyRec.close%>

</table>
	
<% end if %>

<br>
  

<%  ' last section **************************************************************


 If icount < 20 then  


'KCOP
strsql2 = "DELETE FROM tblTempSpotReport WHERE (((tblTempSpotReport.TID)>0))"
set MyRec1 = new ASP_CLS_DataAccess
         MyRec1.ExecuteSql strSql2 

	strdate = formatdatetime(now(),2)
gcount = 0
gVendor = ""


strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE date_Received > '" & strYdate & "'  and date_received <= '" & strdate1 & "' AND ((Species)='KCOP' or (Species)='KBLD' ) AND Location='Yard' and Rejected is null  "_
 &" AND Trailer Is Not Null   ORDER BY Generator, Date_received"
 
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
       while not MyRec.Eof
       if len(MyRec.fields("Generator")) > 0 then 
strGenerator = Replace(MyRec.fields("Generator"), "'", "''")
else
strGenerator = ""
end if 

if len(MyRec.fields("Gen_city")) > 0 then 
strGenCity = Replace(MyRec.fields("Gen_city"), "'", "''")
else
strGenCity = ""
end if 

    
    
 if  lcase(MyRec.fields("Generator")) = lcase(gVendor) and gcount >1 then
    
       MyRec.MoveNext
    else 
   			 if lcase(MyRec.fields("Generator")) <> lcase(gVendor) then
   			 gcount = 0
   			 end if
     if MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      strSpecies = MyRec("Species")
      
            If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "','" & MyRec.fields("PS") & "', "_
       &" '" & MyRec.fields("PO") & "', '" & strVendor  & "', '" & strGenerator & "', '" & strGenCity & "', "_
     &" '" & strAudit & "', " & strDays & ",  '" & strSpecies & "'"
     
     else
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "','" & Replace(MyRec.fields("Vendor"), "'", "''")  & "', '" & strGenerator & "', '" & strGenCity & "', "_
     &" '" & strAudit & "', " & strDays & ",  '" & strSpecies & "'"
end if
    set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql1
         
         
    
    

       MyRec.MoveNext
       end if
       
       wend
       MyRec.close     
       
       
       

strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where (Type = 'KCOP' or Type = 'KBLD')  order by days desc "

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
 
%>


<p align = center><b>
<font face="arial" size="4" >Additional Loads (On the Yard Current - 2 1/2 Days - Consume Last)<br>
KBLD</p>	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center  ><font face="Arial" size="2"><b>Request<br> to Spot</b></font></td>

		<td align = center  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center>
		<p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left><font face="Arial" size="2"><b>Release</b></font></td>
<td align=left><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left>
<p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>

<td align="center" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>


	</tr>

 <% 
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    
 

<td bordercolor="#000000">


	<p align="center"><input type="checkbox" name="C1" value="ON"></p></td>

	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Rec_nbr")%>&nbsp;</font></td>
  

    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
         
       <%   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %>   
    <% if MyRec.fields("Days") > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>	
	


	</tr>

 <%   
       
 		 ii = ii + 1
       MyRec.MoveNext
    
    
	%>

<%  Wend %>
<% MyRec.close%>

</table>

<%  ' PMX

strsql2 = "DELETE FROM tblTempSpotReport WHERE (((tblTempSpotReport.TID)>0))"
set MyRec1 = new ASP_CLS_DataAccess
         MyRec1.ExecuteSql strSql2 

	strdate = formatdatetime(now(),2)



strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE (((tblCars.Date_received) Is Not Null) AND ((tblCars.Species)='PMX') AND ((tblCars.Location)='Yard') and Rejected is null  "_
 &" AND ((tblCars.Trailer) Is Not Null))   ORDER BY tblCars.Vendor, tblCars.Date_received"

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
       while not MyRec.Eof

       if len(MyRec.fields("Generator")) > 0 then 
strGenerator = Replace(MyRec.fields("Generator"), "'", "''")
else
strGenerator = ""
end if 

if len(MyRec.fields("Gen_city")) > 0 then 
strGenCity = Replace(MyRec.fields("Gen_city"), "'", "''")
else
strGenCity = ""
end if 

 
     if MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      
            If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr,  PO, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "',  '" & MyRec.fields("PS") & "', "_
    &" '" & MyRec.fields("PO") & "', '" & strVendor  & "', '" & strGenerator & "', '" & strGenCity & "', "_
     &" '" & strAudit & "', " & strDays & ",  'PMX'"
     
     else
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "', '" & Replace(MyRec.fields("Vendor"), "'", "''")  & "', '" & strGenerator & "', '" & strGencity & "', "_
     &" '" & strAudit & "', " & strDays & ",  'PMX'"
end if
       set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql1
      
     
       MyRec.MoveNext
   
       
       wend
       MyRec.close
       
             
  

strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE tblCars.Date_received Is Not Null AND Species='SHRED'  and (Shred_OCC = Null or Shred_OCC = 0) AND  Location='Yard' and Rejected is null  "_
 &" AND  Trailer Is Not Null  ORDER BY tblCars.Vendor, tblCars.Date_received"

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
       while not MyRec.Eof

    
           if len(MyRec.fields("Generator")) > 0 then 
strGenerator = Replace(MyRec.fields("Generator"), "'", "''")
else
strGenerator = ""
end if 

if len(MyRec.fields("Gen_city")) > 0 then 
strGenCity = Replace(MyRec.fields("Gen_city"), "'", "''")
else
strGenCity = ""
end if 


   		
    
    if MyRec.fields("Weigh_required") = "W" and MyRec.fields("Audit_tons") > 0  Then 
     straudit = "WC"
     elseif MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      
            If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "', '" & MyRec.fields("PS") & "', "_
       &" '" & MyRec.fields("PO") & "', '" & strVendor  & "', '" & strGenerator & "', '" & strGenCity & "', "_

     &" '" & strAudit & "', " & strDays & ",  'SHRED'"
     
     else
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days, [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "', '" & Replace(MyRec.fields("Vendor"), "'", "''")  & "', '" & strGenerator & "', '" & strGenCity & "', "_
     &" '" & strAudit & "', " & strDays & ", 'SHRED'"
     
     end if

       set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql1
         
         
    
       MyRec.MoveNext
   
       
       wend
       MyRec.close     
       

strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE tblCars.Date_received Is Not Null AND Species='HBX'   AND  Location='Yard' and Rejected is null  "_
 &" AND  Trailer Is Not Null  ORDER BY tblCars.Vendor, tblCars.Date_received"

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
       while not MyRec.Eof

    
           if len(MyRec.fields("Generator")) > 0 then 
strGenerator = Replace(MyRec.fields("Generator"), "'", "''")
else
strGenerator = ""
end if 

if len(MyRec.fields("Gen_city")) > 0 then 
strGenCity = Replace(MyRec.fields("Gen_city"), "'", "''")
else
strGenCity = ""
end if 


   		
    
    if MyRec.fields("Weigh_required") = "W" and MyRec.fields("Audit_tons") > 0  Then 
     straudit = "WC"
     elseif MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      
            If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "', '" & MyRec.fields("PS") & "', "_

        &" '" & MyRec.fields("PO") & "', '" & strVendor  & "', '" & strGenerator & "', '" & strGenCity & "', "_
     &" '" & strAudit & "', " & strDays & ",  'HBX'"
     
     else
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days, [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "', '" & Replace(MyRec.fields("Vendor"), "'", "''")  & "', '" & strGenerator & "', '" & strGenCity & "', "_
     &" '" & strAudit & "', " & strDays & ", 'HBX'"
     
     end if

       set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql1
         
         
    
       MyRec.MoveNext
   
       
       wend
       MyRec.close






  

strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE (((tblCars.Date_received) Is Not Null) AND ((tblCars.Species)='OF' or Species = 'of' or Species = 'OF3' or Species = 'OF') AND ((tblCars.Location)='Yard') and Rejected is null  "_
 &" AND ((tblCars.Trailer) Is Not Null))   ORDER BY tblCars.Vendor, tblCars.Date_received"

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
       while not MyRec.Eof

           if len(MyRec.fields("Generator")) > 0 then 
strGenerator = Replace(MyRec.fields("Generator"), "'", "''")
else
strGenerator = ""
end if 

if len(MyRec.fields("Gen_city")) > 0 then 
strGenCity = Replace(MyRec.fields("Gen_city"), "'", "''")
else
strGenCity = ""
end if 

    

   		
    
    if MyRec.fields("Weigh_required") = "W" and MyRec.fields("Audit_tons") > 0  Then 
     straudit = "WC"
     elseif MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      
            If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
       &" '" & MyRec.fields("PO") & "', '" & strVendor  & "', '" & strGenerator & "', '" & strGenCity & "', "_
     &" '" & strAudit & "', " & strDays & ",  'OF'"
     
     else
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days, [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "', '" & Replace(MyRec.fields("Vendor"), "'", "''")  & "', '" & strGenerator & "', '" & strGenCity & "', "_
     &" '" & strAudit & "', " & strDays & ", 'OF'"
     
     end if

       set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql1
         
         
    
       MyRec.MoveNext
   
       
       wend
       MyRec.close
       


strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where Type = 'HBX'   order by days desc "


 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    If not MyRec.eof then
 
%>

<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=0><tr>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><b>
<font face="arial" size="4" >HBX</font></b></td>
<td align = center>&nbsp;</td>


</tr>
	    </table>

	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center  ><font face="Arial" size="2"><b>Request <br>to Spot</b></font></td>

		<td align = center  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center>
		<p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left class="style2"><font face="Arial">Release</font></td>
<td align=left><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left>
<p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>

<td align="center" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>


	</tr>

 <%
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    

<td>
<p align="center"><input type="checkbox" name="C2" value="ON"></td>


	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>

    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
         
	
       <% 
   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %> 
     <% if MyRec.fields("Days") > strFree then %>
   <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
		<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
          
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>

	</tr>

 <%    
       
 		gVendor = lcase(MyRec.fields("Vendor"))
       ii = ii + 1
       MyRec.MoveNext
    
    
	 %>

<%  Wend %>
<% MyRec.close
end if%>

</table>
<br>
<% strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where Type = 'SHRED'   order by days desc "


 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    If not MyRec.eof then %>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=0><tr>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><b>
<font face="arial" size="4" >SHRED</font></b></td>
<td align = center>&nbsp;</td>


</tr>
	    </table>

	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center  ><font face="Arial" size="2"><b>Request <br>to Spot</b></font></td>

		<td align = center  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center>
		<p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left class="style2"><font face="Arial">Release</font></td>
<td align=left><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left>
<p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>

<td align="center" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>


	</tr>

 <%
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    

<td>
<p align="center"><input type="checkbox" name="C3" value="ON"></td>


	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Rec_nbr")%>&nbsp;</font></td>
   

    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
         
	
       <% 
   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %> 
     <% if MyRec.fields("Days") > strFree then %>
   <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
		<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
          
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>

	</tr>

 <%    
       
 		gVendor = lcase(MyRec.fields("Vendor"))
       ii = ii + 1
       MyRec.MoveNext
    
    
	 %>

<%  Wend %>
<% MyRec.close
end if%>

</table>

<% strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where Type = 'OF'   order by days desc "


 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    If not MyRec.eof then %>


<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=0><tr>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><b>
<font face="arial" size="4" >OF3</font></b></td>
<td align = center>&nbsp;</td>


</tr>
	    </table>

	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center  ><font face="Arial" size="2"><b>Request <br>to Spot</b></font></td>

		<td align = center  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center>
		<p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left class="style2"><font face="Arial">Release</font></td>
<td align=left><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left>
<p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>

<td align="center" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>


	</tr>

 <%
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    

<td>
<p align="center"><input type="checkbox" name="C4" value="ON"></td>


	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Rec_nbr")%>&nbsp;</font></td>
   

    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
         
	
       <% 
   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %> 
     <% if MyRec.fields("Days") > strFree then %>
   <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
		<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
          
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>

	</tr>

 <%    
       
 		gVendor = lcase(MyRec.fields("Vendor"))
       ii = ii + 1
       MyRec.MoveNext
    
    
	 %>

<%  Wend %>
<% MyRec.close
end if%>

</table>
<% ' PMX

strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where Type = 'PMX'   order by days desc "


 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
     If not MyRec.eof then
%>



<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=0>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><b>
<font face="arial" size="4" >PMX</font></b></td>
<td align = center>&nbsp;</td>


</tr>
	    </table>

	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center  ><font face="Arial" size="2"><b>Request <br>to Spot</b></b></font></td>

		<td align = center  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center>
		<p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left><font face="Arial" size="2"><b>Release</b></font></td>
<td align=left><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left>
<p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>

<td align="center" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>


	</tr>

 <%
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    


<td>
<p align="center"><input type="checkbox" name="C5" value="ON"></td>

	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
		<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Rec_nbr")%>&nbsp;</font></td>
    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
     
          
       <% 
   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %> 
    <% if MyRec.fields("Days") > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>	

	</tr>

 <%    
       
 		gVendor = lcase(MyRec.fields("Vendor"))
       ii = ii + 1
       MyRec.MoveNext
    
    
	 %>

<%  Wend %>
<% MyRec.close
end if
%>

</table>
<% end if %>
<br>



<table align = center><tr><font face = arial><td colspan = 4>
	<font face="Arial" size="2"><b>Requested Trailers Not on this Sheet</b></font></font></td></tr>

<tr><td><font face="Arial" size="2">Trailer #:</font></td><td>
	<font face="Arial" size="2">______________</font></td>
<font face="Arial" size="2" ><td>
	<font face="Arial" size="2">Reason for Requesting:</font></td><td>
	<font face="Arial" size="2">__________________________________________________________</font></td></tr>


<tr><td><font face="Arial" size="2">Trailer #:</font></td><td>
	<font face="Arial" size="2">______________</font></td><td>
	<font face="Arial" size="2" >Reason for Requesting:</td><td>
	<font face="Arial" size="2">__________________________________________________________</font></td></tr>


<tr><td><font face="Arial" size="2">Trailer #:</font></td><td>
	<font face="Arial" size="2">______________</font></td><td>
	<font face="Arial" size="2" >Reason for Requesting:</td><td>
	<font face="Arial" size="2">__________________________________________________________</font></td></tr>
<tr><td colspan = 4><font face="Arial" size="2"><b>Spotting Issues with Railserve</b></font></font></td></tr>

<tr><td><font face="Arial" size="2">Trailer #:</font></td><td>
	<font face="Arial" size="2">______________</font></td>
<font face="Arial" size="2" ><td>
	S<font face="Arial" size="2">potting Issue</font></font><font face="arial" size="4" ><font face="Arial" size="2">:</font></td><td>
	<font face="Arial" size="2">__________________________________________________________</font></td></tr>

<tr><td><font face="Arial" size="2">Trailer #:</font></td><td>
	<font face="Arial" size="2">______________</font></td><td>
	<font face="Arial" size="2">Spotting Issue:</font></td><td>
	<font face="Arial" size="2">__________________________________________________________</font></td></tr>


</table>


