																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Modify Consumption Projection </TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->


<% Dim MyRec, strsql, strid, strOF
  Dim strdate, strRF, strOCC, strMXP
  
strid = request.querystring("id")

strsql = "Select * from tblTempYardTotals where ID = " & strid
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 

strOCC = MyRec.fields("Total_OCC")
strMXP = MyRec.fields("Total_MXP")
strKCOP_T1 = MyRec("KCOP_Tier1")
strKCOP_T2 = MyRec("KCOP_Tier2")
strKCOP_T3 = MYRec("KCOP_Tier3")
strKCOP_T4 = MyRec("KCOP_Tier4")
strOF_T1 = MyRec("OF_Tier1")
strOF_T2 = MyRec("OF_Tier2")
strOF_T3 = MyRec("OF_Tier3")
strPMX = MyRec("PMX")
strHBX = MyREc("HBX")
strSHRED = MyREc("Shred_T1")
strSHREDT5 = MyREc("Shred_T5")
strKBLD = MyRec("KBLD")
strOF3 = MyRec("OF3")


strdate = MyRec.fields("Inv_Date")

strRF = MyRec.fields("Total_RF")
strOF = MyRec.fields("Yard_KCOP")

MyRec.close


  set objGeneral = new ASP_CLS_General
  if objGeneral.IsSubmit() Then	

  
  	If len(request.form("KBLD")) > 0 then
  	strKBLD = request.form("KBLD")
  	else
  	strKBLD = 0
  	end if
  	
 
  	
  	If len(request.form("OF3")) > 0 then
  	strOF3 = request.form("OF3")
  	else
  	strOF3 = 0
  	end if

  	
	
  	If len(request.form("KCOP_T1")) > 0 then
  	strKCOP_T1 = request.form("KCOP_T1")
  	else
  	strKCOP_T1 = 0
  	end if
  	
  	 If len(request.form("KCOP_T2")) > 0 then
  	strKCOP_T2 = request.form("KCOP_T2")
  	else
  	strKCOP_T2 = 0
  	end if
  	
  	If len(request.form("KCOP_T3")) > 0 then
  	strKCOP_T3 = request.form("KCOP_T3")
  	else
  	strKCOP_T3 = 0
  	end if

If len(request.form("KCOP_T4")) > 0 then
  	strKCOP_T4 = request.form("KCOP_T4")
  	else
  	strKCOP_T4 = 0
  	end if
  	
  	If len(request.form("OF_T1")) > 0 then
  	strOF_T1 = request.form("OF_T1")
  	else
  	strOF_T1 = 0
  	end if
  	
    If len(request.form("OF_T2")) > 0 then
  	strOF_T2 = request.form("OF_T2")
  	else
  	strOF_T2 = 0
  	end if
  	
  	If len(request.form("OF_T3")) > 0 then
  	strOF_T3 = request.form("OF_T3")
  	else
  	strOF_T3 = 0
  	end if

	If len(request.form("PMX")) > 0 then
  	strPMX = request.form("PMX")
  	else
  	strPMX = 0
  	end if

	
  	If len(request.form("OCC")) > 0 then
  	strOCC = request.form("OCC")
  	else
  	strOCC = 0
  	end if
  	
   	  	If len(request.form("MXP")) > 0 then
  	strMXP = request.form("MXP")
  	else
  	strMXP = 0
  	end if
  	
  	  	
  	If len(request.form("RFF")) > 0 then
  	strRF = request.form("RFF")
  	else
  	strRF = 0
  	end if
  	
 
 		If len(request.form("OF")) > 0 then
  	strOF = request.form("OF")
  	else
  	strOF = 0
  	end if
  	
  	  	
  	If len(request.form("SHRED")) > 0 then
  	strSHRED = request.form("SHRED")
  	else
  	strSHRED = 0
  	end if

	If len(request.form("SHRED_T5")) > 0 then
  	strSHREDT5 = request.form("SHRED_T5")
  	else
  	strSHREDT5 = 0
  	end if
  	
  		If len(request.form("HBX")) > 0 then
  	strHBX = request.form("HBX")
  	else
  	strHBX = 0
  	end if


  	
  	   	 strdate = request.form("C_Date")
 
  	

strsql =  "Update tblTempYardTotals set INV_Date = '" & strDate & "', KCOP_Tier1 = " & strKCOP_T1 & ", KCOP_Tier2 = " & strKCOP_T2 & ", KCOP_Tier3 = " & strKCOP_T3 & ", KCOP_Tier4 = " & strKCOP_T4 & ", "_
&" OF_Tier1 = " & strOF_T1 & ", OF_Tier2 = " & strOF_T2 & ", OF_Tier3 = " & strOF_T3 & ", PMX = " & strPMX & ", total_OCC = " & strOCC & ",  total_MXP = " & strMXP & ", "_
&" Yard_KCOP = " & strOF & ", Total_RF = " & strRF & ", SHRED_T1 = " & strShred & ", SHRED_T5 = " & strShredT5 & ", HBX = " & strHBX & ", KBLD = " & strKBLD & ", OF3 = " & strOF3 & " where ID = " & strid

	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
Response.redirect("Facility.asp")		
end if
	
%>

<style type="text/css">
.style2 {
	font-family: arial;
	font-size: x-small;
}
.style3 {
	font-weight: bold;
	font-family: Arial;
	font-size: x-small;
	text-align: center;
	border: 1px solid #C0C0C0;
}
.style6 {
	border: 1px solid #C0C0C0;
	background-color: #FFFFFF;
}
.style7 {
	border: 1px solid #C0C0C0;
	text-align: center;
	background-color: #FFFFFF;
}
.style8 {
	border-style: solid;
	border-width: 1px;
	text-align: center;
}
.style9 {
	border: 1px solid #000000;
}
.style10 {
	border-style: solid;
	border-width: 1px;
	text-align: center;
	font-family: Arial;
	font-size: x-small;
}
.style11 {
	border-style: solid;
	border-width: 1px;
	text-align: center;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
</style>
</head>

<body>
<br>
	
<form name="form1" action="Facility_Edit.asp?id=<%= strid%>" method="post" ID="Form1">
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Modify Consumption Projection</b></font></td>
<td align = right><font face="Arial"><a href="Facility.asp">Return</a>&nbsp;</td>


</tr>
	    </table>



	<p>&nbsp;</p>
<p>&nbsp;</p>
		<TABLE cellSpacing=0 cellPadding=0 class = "style9" align = center style="width: 50%">  
	 <tr class="tableheader">
 

				<td  align = left class="style2" rowspan="2">     <font face="Arial" size="2">	Inventory Date</font></td>
		 
				<td class="style3" rowspan="2"  >KBLD</td>

				<td class="style3" rowspan="2"  >OF3</td>

				<td class="style3" colspan="4"  >&nbsp;&nbsp;&nbsp; KCOP</td>

				<td class="style3" colspan="3"  >&nbsp;&nbsp; OF</td>

			<td class="style8"  ><font face="Arial" size="2">PMX</font></td>

			<td class="style11" colspan="2"  >SHRED</td>

			<td class="style11"  >HBX</td>
		<td class="style3" colspan="2">  &nbsp; OCC</td>
		<td class="style3" colspan="2">  RFF & OF</td>
	</tr>

	 <tr >
		 
				<td class="style8" style="height: 31px"  ><font face="Arial" size="2">1-A</font></td>

		<td class="style8" style="height: 31px"  ><font face="Arial" size="2">1-B</font></td>

		<td class="style8" style="height: 31px; width: 22px"  ><font face="Arial" size="2">2</font></td>
		<td class="style8" style="height: 31px"  ><font face="Arial" size="2">T-5</font></td>
 
				<td class="style8" style="height: 31px"  ><font face="Arial" size="2">1</font><span class="style2">
				</span></td>

		<td class="style10" style="height: 31px"  >2</td>

		<td class="style8" style="height: 31px; width: 26px"  ><font face="Arial" size="2">3</font></td>

			<td class="style8" style="height: 31px"  ><font face="Arial" size="2">PMX</font></td>

			<td class="style11" style="height: 31px"  >1</td>

			<td class="style11" style="height: 31px"  >T5</td>

			<td class="style11" style="height: 31px"  >HBX</td>
		<td class="style8" style="height: 31px">   <font face="Arial" size="2">OCC </font></td>
				<td class="style8" style="height: 31px">   <font face="Arial" size="2">MXP </font></td>
						<td class="style8" style="height: 31px">   <font face="Arial" size="2">RFF </font></td>
				<td class="style8" style="height: 31px">   <font face="Arial" size="2">OF </font></td>

	</tr>

 

	<td class="style6" style="height: 35px"  ><font size="2" face="Arial"><input type="text" name="C_Date" value="<%= strDate%>"></font></td>
	
	<td class="style9" style="height: 35px"><font face = arial size = 1>
		<input type="text" name="KBLD" size="5" style="width: 50px" value="<%= strKBLD%>" tabindex="1"></td>
	
	<td class="style9" style="height: 35px"><font face = arial size = 1>
		<input type="text" name="OF3" size="5" style="width: 50px" value="<%= strOF3 %>" tabindex="1"></td>
	
	<td class="style9" style="height: 35px"><font face = arial size = 1>
		<input type="text" name="KCOP_T1" size="5" style="width: 50px" value="<%= strKCOP_T1%>" tabindex="1"></td>
		<td class="style9" style="height: 35px"><font face = arial size = 1>
		<input type="text" name="KCOP_T2" size="5" style="width: 49px" value="<%= strKCOP_T2 %>" tabindex="2"></td>

		<td class="style9" style="height: 35px">
		<p align="center" class="style7"><font face = arial size = 1>
		<input type="text" name="KCOP_T3" size="5" style="width: 45px; height: 22px;" value="<%= strKCOP_T3 %>" tabindex="3"></td>
		<td class="style9" style="height: 35px">
		<p align="center" class="style7"><font face = arial size = 1>
		<input type="text" name="KCOP_T4" size="5" style="width: 40px; height: 22px;" value="<%= strKCOP_T4%>" tabindex="4"></td>
	<td class="style9" style="height: 35px"><font face = arial size = 1>
		<input type="text" name="OF_T1" size="5" style="width: 47px" value="<%= strOF_T1 %>" tabindex="5"></td>
		<td class="style9" style="height: 35px"><font face = arial size = 1>
		<input type="text" name="OF_T2" size="5" style="width: 42px" value="<%= strOF_T2%>" tabindex="6"></td>

		<td class="style9" style="height: 35px">
		<p align="center" class="style7"><font face = arial size = 1>
		<input type="text" name="OF_T3" size="5" style="width: 43px; height: 22px;" value="<%= strOF_T3%>" tabindex="7"></td>
		<td class="style9" style="height: 35px"><font face = arial size = 1>
		<input type="text" name="PMX" size="5" style="width: 48px" value="<%= strPMX%>" tabindex="8"></td>
		<td class="style9" style="height: 35px"><font face = arial size = 1>
		<input type="text" name="SHRED" size="5" style="width: 48px" value="<%= strSHRED%>" tabindex="8"></td>
		<td class="style9" style="height: 35px"><font face = arial size = 1>
		<input type="text" name="SHRED_T5" size="5" style="width: 48px" value="<%= strSHREDT5%>" tabindex="8"></td>
		<td class="style9" style="height: 35px"><font face = arial size = 1>
		<input type="text" name="HBX" size="5" style="width: 48px" value="<%= strHBX%>" tabindex="8"></td>
		<td class="style9" style="height: 35px"><font face = arial size = 1>
		<input type="text" name="OCC" size="25" style="width: 47px" value="<%= strOCC %>" tabindex="9"></td>

		<td class="style9" style="height: 35px">
		<p align="center" class="style7"><font face = arial size = 1>
		<input type="text" name="MXP" size="5" style="width: 43px; height: 22px;" value="<%= strMXP %>" tabindex="10"></td>
  	<td class="style9" style="height: 35px">
		<p align="center" class="style7"><font face = arial size = 1>
		<input type="text" name="RFF" size="5" style="width: 43px; height: 22px;" value="<%= strRF%>" tabindex="10"></td>

	<td class="style9" style="height: 35px">
		<p align="center" class="style7"><font face = arial size = 1>
		<input type="text" name="OF" size="5" style="width: 43px; height: 22px;" value="<%= strOF %>" tabindex="10"></td>

 

</tr>
</table>


<p align="center"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></p>
</form>
<!--#include file="Fiberfooter.inc"-->