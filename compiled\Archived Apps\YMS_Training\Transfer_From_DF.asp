<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Truck Load Transfer from Dry Fiber</title>
<style type="text/css">
.style3 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
.style6 {
	font-weight: bold;
	border-style: solid;
	border-width: 1px;
}
.style5 {
	border-style: solid;
	border-width: 1px;
}
.style4 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	text-align: right;
	border-style: solid;
	border-width: 1px;
}
.style7 {
	font-family: Arial;
	font-size: medium;
	font-weight: bold;
}
.auto-style1 {
	text-align: right;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID, objMOC, rstFiber, strCarrier, strSAP, objEPS, strFactor
      
    Dim strTrailer, strSpecies, strESpecies, rstSpecies, strGrade
    Dim strReleaseNbr
    Dim strRECNbr
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strGenerator
    Dim strGenCity
    Dim strGenState
    Dim strOther, strR, strsql3, strTransTrailer, strBrokeDescription

       strId  = Trim(Request.QueryString("id")) 


 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then

	If Request.form("Species") = "" then
		Response.write("<font face=arial size=4 color=red>You must select the Species</font>")
			Call getdata()
	elseIf  Request.form("Species") = "BROKE" and  Request.form("Broke_Description") = "" then

		Response.write("<font face=arial size=4 color=red>You must select the Type of Broke for BROKE loads</font>")
			
		Call getdata()

	else


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
 
		Call SaveData() 

	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to Transfer this load.</font></br>")
	MyRec.close
	end if
end if
  
ELSE


Call getdata()
	end if

%>



<body><br>
<table width = 100%><tr><td width = 33%></td><td align = center width = 34%><b><font face="Arial" size="4">
Transfer Load from </font></b><span class="style7">Dry FIber</span></td><td align = right width = 33%>&nbsp;</td></tr></table>



<form name="form1" action="Transfer_From_DF.asp" method="post">
<div align="center">
<table cellspacing="0" bordercolor="#C0C0C0" bgcolor="#FFFFE8" style="width: 75%;" cellpadding="0" class="style3">
<tr>
    <td bgcolor="#FFFFE8" style="width: 361px">&nbsp;</td>

    <td  bgcolor="#FFFFE8">&nbsp;</td>
  </tr>


      <td  bgcolor="#FFFFE8" align = right style="width: 361px">
  <font face="Arial" size="2"><b>Transfer Trailer Number:&nbsp;</b></font></td>
<td  align = left bgcolor="#FFFFE8">

      <input type="text" name="Trans_Trailer" size="15" value = "<%= strTransTrailer%>" tabindex="1"></td></tr>
      
       <tr><td bgcolor="#FFFFE8" style="width: 361px">&nbsp;</td>
  <td bgcolor="#FFFFE8">&nbsp;</td></tr>
<tr>
    <td  bgcolor="#FFFFE8" style="width: 361px">  
	<p align="right">  <font face="Arial" size="2"><b>Select Carrier:&nbsp;</b></font></td>
    <td bgcolor="#FFFFE8">   <select name="Carrier" tabindex="2">
 	<option value="BWIF" selected>BWIF</option>
      <%= objGeneral.OptionListAsString(rstFiber, "Carrier", "CDesc", strCarrier) %>
     </select></td>
  </tr>
        <tr>
    <td  bgcolor="#FFFFE8" style="width: 361px">&nbsp;</td>

    <td bgcolor="#FFFFE8">&nbsp;</td>
  </tr>
  <tr>
    <td  bgcolor="#FFFFE8" class="style6" style="width: 361px">  
	<p align="right">  <font face="Arial" size="2">Select Species:&nbsp;</font></td>
    <td bgcolor="#FFFFE8" class="style5">  
	<select name="Species" size="1" tabindex="3" >
 <option value="">--Select --</option>

       <%= objGeneral.OptionListAsString(rstSpecies, "Fiber_species", "Fiber_species", strSpecies) %>
     </select>
&nbsp;<b><font size="2" face = arial>&nbsp;(Species)<br><br>
	<select name="Broke_Description" size="1" tabindex="4" >
 <option>--Select Broke Type --</option>
 
 <% 
strsql = "SELECT * from tblBrokeSap where Category = 'BROKE' order by  Type"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    while not MyRec.eof %>

       <option value="<%= MyRec("TYpe") %>"><%= MyRec("SAP") %>-<%= MyRec("Type") %></option>
 <% MyRec.movenext
 wend
 MyRec.close %> 
       
       
        
     </select>&nbsp; (If you selected BROKE, then you must select type of Broke)<br>
	<br>

     
      <input type="text" name="Enter_species" size="29" style="width: 183px" tabindex="5"><font face="Arial" size="2">&nbsp; 
	Enter Species ONLY if it was&nbsp;not a choice in the dropdown:</font><br>
	<br>
	</font></b>&nbsp; </td>
  </tr>
  <tr>
    <td  bgcolor="#FFFFE8" class="style4" style="width: 361px"><strong>Enter 
	Bales for Species other than Secondary Fiber:&nbsp;&nbsp;&nbsp; </strong></td>

    <td bgcolor="#FFFFE8" class="style6"><font size="2" face = arial>
      <input type="text" name="Bales" size="11" style="width: 36px" tabindex="6">&nbsp; Tons will be calculated when you Submit.&nbsp;&nbsp; </font></td>
  </tr>

    <tr>
    <td  bgcolor="#FFFFE8" align="right" style="height: 50px; width: 361px">
	<b><font face="Arial" size="2">Enter Tons or Pounds for Secondary Fiber&nbsp;&nbsp;<br>
&nbsp;(Broke, KCOP, OCC, PMX, MXP OF)&nbsp;&nbsp; <br>
	or for Species not in Dropdown:&nbsp;&nbsp;&nbsp; </font></b></td>

    <td bgcolor="#FFFFE8" style="height: 50px">

     
      <input type="text" name="Tons" size="11" style="width: 60px" tabindex="7"></td>

  </tr>
    <tr>
    <td  bgcolor="#FFFFE8" style="width: 361px">&nbsp;</td>

    <td bgcolor="#FFFFE8">&nbsp;</td>
  </tr>





       <tr>
          <td  align = right bgcolor="#FFFFE8" height="27" style="width: 361px" >
    <font face="Arial" size="2"><b>Date Transferred:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFE8" height="27">

      <input type="text" name="Date_Received" size="15" value = "<%= strDateReceived%>" tabindex="8"></td></tr>
	
	<tr>
    <td  bgcolor="#FFFFE8" style="width: 361px">&nbsp;</td>

    <td bgcolor="#FFFFE8">&nbsp;</td>
  </tr>
<tr>
    <td bgcolor="#FFFFE8" style="width: 361px" class="auto-style1"> 
 <font face="Arial" size="2"><b> Transfer to::</b></font></td>

    <td align = left bgcolor="#FFFFE8">     <font face="Arial" size="2">&nbsp;&nbsp;&nbsp;
	<input type="checkbox"  value="ON" name="Yard">Yard&nbsp;&nbsp;&nbsp;
	<input type="checkbox"  value="ON" name="Merchants">Merchants
	</font></td>
    </tr>
<tr>
    <td  bgcolor="#FFFFE8" style="width: 361px">&nbsp;</td>

    <td bgcolor="#FFFFE8">&nbsp;</td>
  </tr>

  <tr>
    <td bgcolor="#FFFFE8" style="width: 361px">
	<p align="right"><font face="Arial" size="2"><b>Print Movement Order:</b></font></td>

    <td align = left bgcolor="#FFFFE8"> <font size="1" face="Verdana">  
	<input type="checkbox"  value="ON" name="Print_receipt" checked></font></td>
  </tr>

  <tr>
    <td bgcolor="#FFFFE8" style="width: 361px">&nbsp;</td>

    <td align = left bgcolor="#FFFFE8">&nbsp;</td>
  </tr>

  <tr>
    <td bgcolor="#FFFFE8" style="width: 361px">&nbsp;</td>

    <td align = left bgcolor="#FFFFE8"><Input name="Update" type="submit" Value="Submit" ></td>
  </tr>
</table>

</div>

</form>
</body>
<%
 Function GetData()
  set objMOC = new ASP_CLS_FIBER
        set rstFiber = objMOC.FiberCarrier()
        strDateReceived = formatdatetime(Now(),2)  
          set objEPS = new ASP_CLS_Fiber      
          set rstSpecies = objEPS.VFSpecies()
End Function




 Function SaveData()
 strBales = 0
strTrailer = Request.form("Trans_Trailer")
If len(Request.form("Enter_species")) > 0 then
strSpecies = Request.form("Enter_species")
else
strSpecies = Request.form("Species")
end if
strCarrier = Replace(Request.form("Carrier"), "'", "''")

strDateReceived = Request.form("Date_received")

strSAP = ""

Dim strRightnow, strTons
strRightnow = Now()
strTons = Request.form("Tons")
strGrade = "RF"



if len(request.form("Bales")) > 0 then
if request.form("Bales") > 0 then
strsql3 = "Select [Other] from tblVFSpecies where Fiber_Species = '" & strSpecies & "'"
   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")

	If not Myrec.eof then
	strFactor = Trim(MyRec.fields("Other"))
	MyRec.close
	end if

strTons =round(Request.form("Bales") * cdbl(strFactor),3)
strBales = request.form("Bales")
end if
end if



strsql = "Select Grade from tblVFSpecies where Fiber_Species = '" & strSpecies & "'"
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
If not MyRec.eof then
If len(MyRec("Grade")) > 0 then
strGrade = MyRec("Grade")
end if
end if


If strSpecies = "BROKE" then
strGrade = "BROKE"
strBrokeDescription = Request.form("Broke_Description")
strsql = "Select SAP from tblBrokeSAP where Type = '" & strBrokeDescription & "'"
Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
	If not MyRec.eof then
	strSAP = MyRec("SAP")
	end if
	MyRec.close
else

strBrokeDescription = ""
end if 



if strTons > 500 then
strTons = round(strTons/2000,3)
end if

If request.form("Merchants") = "ON" then
strLocation = "MERCHANTS"
else
strLocation = "YARD"
end if


	strsql =  "INSERT INTO tblCars (Broke_Description, PMO_Nbr, Date_Received, Trailer,  OID,  Transfer_Trailer_nbr, Transfer_Date, Trans_Carrier, Species, Grade, Location, SAP_NBR, Tons_Received, Deduction, Net, Entry_Time, Entry_BID, Entry_Page, Bales_RF ) "_
	&" SELECT  '" & strBrokeDescription & "', 'DF', '" & strDateReceived & "', 'UNKNOWN',  0,'" & strTrailer & "', '" & strDateReceived & "', '" & strCarrier & "', '" & strSpecies & "', '" & strGrade & "',  '" & strLocation & "',   "_
	&"   '" & strSAP & "'," & strTons & ", 0, " & strTons & ", '" & strRightnow & "', '" & Session("EmployeeID") & "',  'Transfer_From_DF.asp', " & strBales & ""
	
	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

Dim strlast
strsql = "Select Max(CID) as MAXofCID from tblCars"

   	 Set MyConn = Server.CreateObject("ADODB.Recordset")
   	 MyConn.Open strSQL, Session("ConnectionString")
   	 strlast = MyConn.fields("MaxofCID")
   	 MyConn.close

         
    
         
         
      
         
     
         
         
 If Request.form("Print_receipt") = "ON" then
 Response.redirect("Transfer_From_DFReceipt.asp?id=" & strlast)
 else        
  

Response.redirect ("Fiberindex.asp")
end if


End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->