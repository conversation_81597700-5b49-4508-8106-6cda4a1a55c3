																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Recovered Paper Yard Inventory Report</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->



<% Dim MyRec, strsql, strDays, strDate, gSpecies, gCount, gTCount, strsql3, MyRec3, gTotalWeight, strAdmin, MyRec2

	strdate = formatdatetime(now(),2)
gcount = 0
gTotalWeight = 0
gSpecies = ""

    strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "' and User_type = 'A'"


    Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL3, Session("ConnectionString")



If not Myrec2.eof then
strAdmin = "YES"
else
strAdmin = ""
end if
MyRec2.close 

If Session("EmployeeID") = "B60405" or Session("EmployeeID") = "B53911" or Session("EmployeeID") = "B60423" or Session("EmployeeID") = "B48916" then 
strAdmin = "YES"
end if


IF Session("EmployeeID") = "D10480" then
strAdmin = "YES"
end if


strAdmin = "YES"

	 if strAdmin = "" then %>
	<!--#include file="classes/asp_cls_headerFIBER.asp"-->

	<p align="center"><font face="arial" color="red" size="3"><b>The RF Yard Inventory Report is Not Available.  <br><br>
	Please use the <a href="Spotting_report.asp">Shift Spotting Report</a><br>
	</b></font></p>
	<% else 

strsql = "SELECT tblCars.*, tblOrder.Tier, tblCars.[date_received] AS Sort_Date FROM tblCars LEFT JOIN tblOrder ON tblCars.OID = tblOrder.OID "_
&" WHERE  Date_received Is Not Null  AND  Location ='Yard'  "_
&"  AND  Trailer  Is Not Null  and (((tblCars.Species = 'KBLD'  or left(tblCars.Species,3) = 'PMX' or tblCars.Species = 'SHRED'     "_
&" or tblCars.Species = 'HBX' or tblCars.Species = 'OF3' ) and (Shred_OCC = 0 or Shred_OCC = Null)) or "_
&"  ((tblCars.Species = 'MXP' OR tblCars.Species = 'USBS' or tblCars.Species = 'LPSBS' or tblCars.Species = 'SWL' or tblCars.Species = 'HWM') and (SHRED_RF = 1 or Shred_RF= -1 ))) "_
&"  ORDER BY tblCars.Species, Sort_Date ASC"   
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<style type="text/css">
.style1 {
	text-align: left;
}
.style2 {
	text-align: center;
}
</style>
</head>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=0>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><b>
<font face="arial" size="4" >Yard Inventory Report for <%= strDate%></font></b></span></td>
<td align = center>&nbsp;</td>


</tr>
	    </table>
<p align="right"><font face="Arial">Yard Physically Checked by 
_______________________<br>Signature&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </font>&nbsp; </p>

	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">

	<td  ><font face="Arial" size="2"><b>Species</b></font></td>
	 
		<td class="style2"  ><font face="Arial" size="2"><b>Test</b></font></td>

		<td align = center  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td class="style1"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=center><font face="Arial" size="2"><b>Carrier</b></font></td>
	<td align=center><font face="Arial" size="2"><b>REC Nbr</b></font></td>
	<td align=center><font face="Arial" size="2"><b>PO</b></font></td>
		<td align=center><font face="Arial" size="2"><b>Release</b></font></td>
<td align=left><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left><font face="Arial" size="2"><b>Generator<br>City</b></font></td>
<td align=left><font face="Arial" size="1"><b>Weight</b></font></td>

<td align=left><font face="Arial" size="1"><b>Audit</b></font></td>
<td align=center><font face="Arial" size="1"><b>To Date<br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="1"><b>Days <br>in Yard</b></font></td>

<td align="center" ><font face="Arial" size="1"><b>Other</b></font></td>
<td align = center ><font face="Arial" size="2"><b>Checkoff</b></font></td>
	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    
    <% if  Trim(MyRec.fields("Species")) = gSpecies or gcount = 0 then %>


        
        <% if isnull(MyRec.fields("Transfer_date")) then %>
        <td>       <font size="2" face="Arial">       <%= MyRec.fields("Species")%>
        <% if MyRec.fields("Rejected") = "YES" then %>
        - Rejected
        <% end if %>
        </font></td>
        
        <% If mYrec("Trailer")  = "UNKNOWN" and MyRec("PS") > 3 then
        strRelease = Myrec("PS")
        else        
        strRelease = MyRec("Release_nbr")
        end if
        strTest = ""
        strTier = ""
        strsql2 = "SELECT tblTier.Tier, tblTier.Test "_
 &" FROM tblTier INNER JOIN tblOrder ON (tblTier.State = tblOrder.State) AND (tblTier.City = tblOrder.City) "_
 &"  AND (tblTier.Generator = tblOrder.Generator) AND (tblTier.Vendor = tblOrder.Vendor) AND (tblTier.Grade = tblOrder.Species) "_
&" where Release = '" & strRelease & "'"
         Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL2, Session("ConnectionString")
    If not MyRec2.eof then
    strTest = MyRec2("Test")
    strTier = MyRec2("Tier")
    end if
    MyRec2.close 
    
     %>
    
 
        <td align = center ><font size="2" face="Arial"><%= strTest%></font></td>
	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%>&nbsp;</font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
	 <td  >  <font size="2" face="Arial">        <%= MyRec.fields("REC_number")%>&nbsp;</font></td>
        <% else %>
        	<td>       <font size="2" face="Arial">       <%= MyRec.fields("Species")%>&nbsp;&nbsp;:SHUTTLE</font></td>
               	
        		
        	<td align = center   ><font size="2" face="Arial">&nbsp;</font></td>
		<td align = center width="69" ><font size="2" face="Arial"><%= MyRec.fields("Transfer_Date")%>&nbsp;</font></td>
	
			<td align=left  > <font size="2" face="Arial">&nbsp;<%= MyRec.fields("Transfer_trailer_nbr")%></font></td>
		<td  >   <font size="2" face="Arial">  &nbsp;      <%= MyRec.fields("Trans_Carrier")%>&nbsp;</font></td>
		   <td  >  <font size="2" face="Arial">        <%= MyRec.fields("PMO_nbr")%>&nbsp;</font></td>
		<% end if %>
		

		
   <td  >  <font size="2" face="Arial">   <%= MyRec.fields("PO")%>&nbsp;</font></td>
      <td  >  <font size="2" face="Arial"> <%= MyRec.fields("PS")%>  <%= MyRec.fields("Release_nbr")%>&nbsp;</font></td>

    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
               <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Net")%>&nbsp;</font></td>


      <td  >  <font size="2" face="Arial"> 
        
    <% if MyRec.fields("Weigh_required") = "W" and MyRec.fields("Audit_tons") > 0  Then %>
      WC
       <% elseif (MyRec.fields("Weigh_required") = "W" and isnull(MyRec.fields("Audit_tons"))) or ( MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then %>W
        <% else %> &nbsp; <% end if %></td>
          
          
         <% if isnull(MyRec.fields("Transfer_date")) then
             strDays = round(Now()- cdate(MyRec.fields("Date_received")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

             else
             strDays = round(Now()- cdate(MyRec.fields("Transfer_date")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Trans_Carrier") & "' "
             end if  

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    if strDays  > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(strDays) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
 
	<td align = center><font size="2" face="Arial"><%= strDays%></font></td>	
	
	<td><font size="2" face="Arial"><%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>
	<td ALIGN = CENTER><font size="2" face="Arial"><input type="checkbox" name="C1" value="ON">&nbsp;</font></td>
	</tr>

 <%    gcount = gcount + 1
       gTcount = gTcount + 1
       If len(MyRec.fields("Net"))> 0 then 
       gTotalWeight = gtotalWeight + MyRec.fields("Net")
       end if
 		gSpecies = Trim(MyRec.fields("Species"))
       ii = ii + 1
       MyRec.MoveNext
    
    %>
<% else %>
<td><font face = arial size = 2><b></b>Total:&nbsp;<%= gcount%></b></font></td>
 
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
   <td>&nbsp;</td>
   <td>&nbsp;</td>
       <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
        <td>&nbsp;</td>
      <td><font face = arial size = 2>
      <% if isnull(gTotalweight)  then %>
      <%= gTotalweight %>
      <% else %>
      <%= round(gTotalweight,2) %>
      <% end if %>
      </td>
      <td>&nbsp;</td>
        <td>&nbsp;</td>
<td>&nbsp;</td>	
<td>&nbsp;</td>

<td>&nbsp;</td>
	</tr>
	<TR><td>&nbsp;</td>

<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
   <td>&nbsp;</td>
   <td>&nbsp;</td>
       <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
<td>&nbsp;</td>	
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
	</tr>
	
	  <tr class=tablecolor2>
        
        <% if isnull(MyRec.fields("Transfer_date")) then %>
        <td>        <font size="2" face="Arial">         <%= MyRec.fields("Species")%>
         <% if MyRec.fields("Rejected") = "YES" then %>
        - Rejected
        <% end if %>
                
</font></td>  

            <% if MyRec("Trailer") = "UNKNOWN"  and MyRec("PS") > 3 then 
            strRelease = MyRec("PS")
            else
            strRelease = MyRec("Release_nbr")
            end if
        strTest = ""
        strTier = ""
        strsql2 = "SELECT tblTier.Tier, tblTier.Test "_
 &" FROM tblTier INNER JOIN tblOrder ON (tblTier.State = tblOrder.State) AND (tblTier.City = tblOrder.City) "_
 &"  AND (tblTier.Generator = tblOrder.Generator) AND (tblTier.Vendor = tblOrder.Vendor) AND (tblTier.Grade = tblOrder.Species) "_
&" where Release = '" & strRelease & "'"
         Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL2, Session("ConnectionString")
    If not MyRec2.eof then
    strTest = MyRec2("Test")
 
    end if
    MyRec2.close
    %>
   
 
 <td align = center ><font size="2" face="Arial"><%= strTest %></font></td>

        		<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%>&nbsp;</font></td>
        		<td  > <font size="2" face="Arial"><%= MyRec.fields("Trailer")%></font></td>
        		<td  >    <font size="2" face="Arial"><%= MyRec.fields("Carrier")%></font></td>
        		  <td> <font size="2" face="Arial">        <%= MyRec.fields("REC_number")%></font></td>
        <% else %>
        	<td>       <font size="2" face="Arial">       <%= MyRec.fields("Species")%>&nbsp;&nbsp;SHUTTLE</font></td>
   
 
        	<td align = center   ><font size="2" face="Arial">&nbsp;</font></td>
		<td align = center width="69" ><font size="2" face="Arial"><%= MyRec.fields("Transfer_Date")%>&nbsp;</font></td>
	
			<td align=left  > <font size="2" face="Arial">&nbsp;<%= MyRec.fields("Transfer_trailer_nbr")%></font></td>
		<td  >   <font size="2" face="Arial">  &nbsp;      <%= MyRec.fields("Trans_Carrier")%>&nbsp;</font></td>

				<td> <font size="2" face="Arial"><%= MyRec.fields("PMO_nbr")%>&nbsp;</font></td>
		


		<% end if %>
		
<td>   
        <font size="2" face="Arial">        <%= MyRec.fields("PO")%></font></td>
        <td>   
        <font size="2" face="Arial"> <%= MyRec.fields("PS")%>       <%= MyRec.fields("Release_nbr")%></font></td>

        <td  >        <font size="2" face="Arial">        <%= MyRec.fields("Vendor")%></font></td>
          <td  >        <font size="2" face="Arial">        <%= MyRec.fields("Generator")%>&nbsp;</font></td>
          <td  >        <font size="2" face="Arial">        <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
       
           <td  >        <font size="2" face="Arial">        <%= MyRec.fields("Net")%>&nbsp;</font></td>
        <td  >  <font size="2" face="Arial"> 

        
    <% if MyRec.fields("Weigh_required") = "W" and MyRec.fields("Audit_tons") > 0  Then %>
      WC
       <% elseif (MyRec.fields("Weigh_required") = "W" and isnull(MyRec.fields("Audit_tons"))) or ( MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then %>W
        <% else %> &nbsp; <% end if %></td>

             <% if isnull(MyRec.fields("Transfer_date")) then
             strDays = round(Now()- cdate(MyRec.fields("Date_received")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

             else
             strDays = round(Now()- cdate(MyRec.fields("Transfer_date")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Trans_Carrier") & "' "
             end if  
             


 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %> 
    <% if strDays  > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(strDays) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>          

 
	<td align = center><font size="2" face="Arial"><%= strDays%></font></td>

	

	<td><font size="2" face="Arial"><%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>
	<td ALIGN = CENTER><font size="2" face="Arial"><input type="checkbox" name="C1" value="ON">&nbsp;</font></td>
	</tr>
		
	<% gcount = 1
	 gTcount = gTcount + 1
	 
	 gtotalweight = MyRec.fields("Net")
	 gSpecies = MyRec.fields("Species")
       ii = ii + 1
       MyRec.MoveNext
    
	 end if %>

<%  Wend %>
	  <tr class=tablecolor2>
<td><font face = arial size = 2><b></b>Total:&nbsp;<%= gcount%></b></font></td>
 
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
   <td>&nbsp;</td>
   <td>&nbsp;</td>
       <td >&nbsp;</td>
      <td>&nbsp;</td>
<td>&nbsp;</td>	
<td>&nbsp;</td>	
<td>&nbsp;</td>	
  <td><font face = arial size = 2><%= round(gTotalweight,2) %></td>
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>

	</tr>
	<TR><td>&nbsp;</td>
 
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
   <td>&nbsp;</td>
   <td>&nbsp;</td>
       <td >&nbsp;</td>
      <td>&nbsp;</td>
<td>&nbsp;</td>	

<td>&nbsp;</td>
<td>&nbsp;</td>	
	</tr>
<TR><td><font face = arial size = 2><b></b>Grand Total:&nbsp;<%= gTcount%></b></font></td>
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
   <td>&nbsp;</td>
   <td>&nbsp;</td>
       <td>&nbsp;</td>
      <td >&nbsp;</td>
<td>&nbsp;</td>	
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>

	</tr>

</table>
<% end if %>