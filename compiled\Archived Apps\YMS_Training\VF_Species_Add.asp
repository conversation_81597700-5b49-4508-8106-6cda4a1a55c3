																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Add Inbound Virgin Fiber </TITLE>


<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 



<% Dim MyRec, strsql, MyRec2, strsql2,  MyConn3, strsql3,  strSchedule_Agreement

strdate = formatdatetime(Now(),2)

  set objGeneral = new ASP_CLS_General
  if objGeneral.IsSubmit() Then	
  Dim strSpecies,  strBT_Factor, strUOM
  
  	strSpecies = request.form("Species")
  	

  	
  	If len(request.form("SAP")) > 0 then
  	strSAP = request.form("SAP")
  	else
  	strSAP = ""
  	end if 
  	
  	If len(request.form("UOM")) > 0 then
  	strUOM = request.form("UOM")
  	else
  	strUOM = 0
  	end if
  	
  
  		
  	If len(request.form("Schedule_Agreement")) > 0 then
  	strSchedule_Agreement= request.form("Schedule_Agreement")
  	else
  	strSchedule_Agreement = ""
  	end if
 
  	
  If len(request.form("BT_Factor")) > 1 then	
 strBT_Factor = Request.form("BT_Factor")
 else
 strBT_Factor = ""
 end if 
 
        	
  	
	strsql =  "INSERT INTO tblVFSpecies (Grade, Fiber_Species,  SAP, UOM, Schedule_Agreement, Other) "_
	&" SELECT '" & request.form("Grade") & "',  '" & strSpecies & "',  '" & strSAP & "', '" & strUOM & "', '" & strSchedule_Agreement & "','" & strBT_Factor & "'"
	
	
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
Response.redirect("VF_Species.asp")		
end if

	
%>

<style type="text/css">
.style1 {
	font-family: arial;
	font-size: xx-small;
}
.style3 {
	border-style: solid;
	border-width: 1px;
}
.style5 {
	font-size: x-small;
}
.style6 {
	font-family: arial;
	font-size: x-small;
}
</style>
</head>

<body>
<br>
	
<form name="form1" action="VF_Species_Add.asp" method="post" ID="Form1">
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Add Species </b></font></td>
<td align = right><font face="Arial"><a href="VF_Species.asp">Return</a>&nbsp;</td>


</tr>
	    </table>



	<p>&nbsp;</p>
	<table id="table1" style="width: 75%" align="center" class="style3">
<tr bgcolor = #CCCCFF>
		<td bgcolor="#FFFFD7" class="style6">

Species</td>
		<td bgcolor="#FFFFD7" class="style6">

SAP #</td>
		<td bgcolor="#FFFFD7">

<p align="center" class="style1"><span class="style5">U</span><span class="style6">OM</span></td>
	</tr>
	<tr>
		<td><font face = arial size = 1>
		<input type="text" name="Species" size="20" style="width: 178px"></td>
		<td><font face = arial size = 1>
		<input type="text" name="SAP" size="20"></td>
		<td>
		<p align="center"><font face = arial size = 1>
		<input type="text" name="UOM" size="20" style="width: 59px"></td>
	</tr>
</table>
<br>
		
		<table cellpadding="0" id="table4" style="width: 75%" align="center" class="style3" >
			<tr bgcolor = #CCCCFF>

		<td style="font-family: Arial" bgcolor="#FFFFD7"><span class="style5">
		Grade<br>
		(Used to determine which dropdown list to include&nbsp; this species in 
		for transfers) </span></td>

		<td style="font-family: Arial" bgcolor="#FFFFD7" class="style5">Schedule 
		Agreement</td>

	<td style="font-family: Arial" bgcolor="#FFFFD7">
	<p align="center" class="style5">Bale to Ton Factor</td>
	
	</tr>
	
	<td><font face = arial size = 1><select name="Grade">
		<option value="">--Select--</option>
		<option <% if strgrade = "RF" then %>selected <% end if %>>RF</option>
		<option <% if strgrade = "VF" then %>selected <% end if %>>VF</option>
		</select></td>


	<td><font face = arial size = 1>
<input type="text" name="Schedule_Agreement" size="39"></td>


<td height="18">
<p align="center"><font face = arial size = 1>
<input type="text" name="BT_Factor" size="39" style="width: 155px"></td>


</tr></table>


<p align="center"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></p>
</form>

<!--#include file="Fiberfooter.inc"-->