<% Option Explicit %>
<HTML>
<HEAD>
    <TITLE>ActiveDirectoryUserQuery Class Test</TITLE>
    <!--#INCLUDE VIRTUAL="/scripts/ADUserQuery.inc"-->
</HEAD>
<BODY STYLE="background-color: #FFFFFF; font-size: 10pt; color: #000000; font-family: sans-serif;">
<%
'-- Create the objADUserQuery Object and assign values to the required properties. 
'-- These must be set for the class to correctly execute.
    Dim strUserId
        strUserId = UCase(Request.ServerVariables("LOGON_USER"))
        strUserId = Left(Replace(strUserId,"KCUS\",""),6)
        Response.Write "UserID: " & strUserId & "<P>"
    
    Dim objADUserQuery
    Set objADUserQuery = New ActiveDirectoryUserQuery
        'objADUserQuery.authorizedUserName = "kcus\b16599s"
        'objADUserQuery.authorizedUserPassword = ""
        objADUserQuery.targetDomain = "kcc.com"
        objADUserQuery.addFilter "cn", strUserId & "*"

    '-- Modify the Filters to narrow or broaden the search.
        'objADUserQuery.removeFilter "cn" 
        'objADUserQuery.removeAllFilters
        'objADUserQuery.setFilter "cn","IN ('" & strUserId & "','B19587')"

    '-- check some filter incormation                
        Response.write("getFilter('cn'): " & objADUserQuery.getFilter("cn") & "<BR>")
        Response.write("filterExists('cn'): " & objADUserQuery.filterExists("cn") & "<BR>")

    '-- Execute the Query and Process the Results
        objADUserQuery.executeQuery

    '-- Write out Header Information
	    Response.Write("ADUserQuery Version    : " & objADUserQuery.version & "<BR>") 
        Response.Write("authorizedUserName     : " & objADUserQuery.authorizedUserName & "<BR>")     
        Response.Write("authorizedUserPassword : " & objADUserQuery.authorizedUserPassword & "<BR>") 
        Response.Write("targetDomain           : " & objADUserQuery.targetDomain & "<BR>")           
        Response.Write("ldapSearchString       : " & objADUserQuery.ldapSearchString & "<BR>")        
        Response.Write("resultsCount           : " & objADUserQuery.resultsCount & "<BR>")           
        Response.Write("errorCount             : " & objADUserQuery.errorCount & "<P>")             

    '-- Check for Errors. Display errors if they exist.
        If(objADUserQuery.errorCount > 0) Then
            'Response.Write(Replace(objADUserQuery.errorsText,vbCrLf,"<BR>"))
	    Response.Write Replace(Err.Number & " - " & Err.Description,vbCrLf,"<BR>") 

    '-- If no errors exist, process the results set.
        Else
            If(objADUserQuery.resultsCount = 0) Then
                Response.write("<P>No users Found.<P>")
        '-- Access the results in 2 ways.    
            ElseIf(objADUserQuery.resultsCount = 1) Then
            '-- Use standard array form '(x)' where x is an ordinal position in the array.    
                Response.Write("<PRE>" & String(100,"-") & vbCrLf & _
                                         objADUserQuery.results(0).AllProperties & "</PRE>")
            Else
            '-- or process the entier collection with a standard for ... each loop.    
                Dim objADUser
                For Each objADUser in objADUserQuery.Results
                    Response.Write("<PRE>" & String(100,"-") & vbCrLf & _
                                             objADUser.AllProperties & "</PRE><P>")
                Next
                Set objADUser = Nothing
            End If
        End If
    
    Set objADUserQuery = Nothing

%>
</BODY>
</HTML>