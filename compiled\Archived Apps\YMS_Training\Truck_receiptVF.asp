																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Receipt for Virgin Fiber</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->




<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate, strid, strTrailer, strDateReceived

strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -7, strtdate)
strid = request.querystring("id")
strTrailer = request.querystring("t")
strDateReceived = Request.querystring("d")

strsql = "SELECT tblCars.* FROM tblCars WHERE VID = " & strid 

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>
<body onload="if (window.print) {window.print()}">

<p align="center"><img height="40" src="kcc40white2.gif" width="450"></p>
<p align="center"><b><font face="Arial">Receipt for Load at Kimberly-Clark 

<a href="Select_Rail.asp">Mobile</a></font></b><br>
</p>
	
<br><br><br>
	
<table width = 100%>
<Tr>	<td align="center"><b><font face="Arial">Receipt Nbr<br><%= MyRec.fields("CID")%></font></b></td>
<td align = center>
<img alt="" src="Boxcar.gif"></td>

	<td align="center"><b><font face="Arial">Date Received<br>&nbsp;<%= MyRec.fields("Date_Received")%></font></b></td></Tr>


</table>	<br><br><br><br>

<table border="1" width="100%" id="table1" cellspacing="1" bordercolorlight="#808080" bordercolor="#000000">

	
		<tr>
		<td bordercolor="#808080" align="center"><b><font face="Arial">Carrier</font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">Trailer #</font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">Species</font></b></td>
		<td bordercolor="#808080" align="center"><b><font face="Arial">PO #</font></b></td>
		<td bordercolor="#808080" align="center"><b><font face="Arial">Release<br> 
		or BOL #</font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">Weight <br>
		Received</font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">Units</font></b></td>
		
	</tr>
			<tr>
	
		<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("Carrier")%></font></b></td>
			<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("Trailer")%></font></b></td>
		
		<td align="center"><b><font face="Arial"><%= MyRec.fields("Species")%>&nbsp;</font></b></td>
		<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("PO")%></font></b></td>
			<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("Release_Nbr")%></font></b></td>
			<td align="center"><font face="Arial"><b><%= MyRec.fields("Tons_received")%>&nbsp;<%= MyRec.fields("UOM")%></b></font></td>
			<td align="center"><font face="Arial"><b><%= MyRec.fields("Bales_VF")%>&nbsp;</b></td>
	</tr>
	</table>
	<table>
	<tr>
		<td colspan = 6>&nbsp;</td>
	</tr>
		<tr>
		<td colspan = 6>&nbsp;</td>
	</tr></table>
	
	<table width = 50% align = center>
	<tr>
		<td valign="top"><b><font face="Arial">Vendor:&nbsp;&nbsp;
		<%= MyRec.fields("Vendor")%></font></b></td>		
		
		</td>
		<% if len(MyRec.fields("Generator")) > 0 then %>
		<td valign="top" ><b><font face="Arial">Generator:</td><td><b><font face="Arial"><%= MyRec.fields("Generator")%><br>
		<%= MyRec.fields("Gen_City")%>&nbsp;<%= MyRec.fields("Gen_State")%></font></b></td>
		<% end if %>
	</tr></table>
<br><br>
<br><br>

		<% if len(MyRec.fields("Other_comments")) > 0 then %>
		<table width = 100% align = center><tr><td align = center>
	<b><font face="Arial"><%= MyRec.fields("Other_Comments")%></td></tr></table>
		<% end if %>
	&nbsp;<p align = center>&nbsp;</p>
		<div align="center">
		<table width = 80% border="1">
	<tr><td align="center">
		<p><b><font face="Arial">SAP DOC ID</font></b></td>
		<td align="center">
		<b><font face="Arial">MAGIC #</font></b></td>
		<td align="center"><b><font face="Arial">Receiver Name</font></b></td>
		
		<td align="center"><b><font face="Arial">Initials</font></b></td>
		
	</tr>
	
	<tr>
		<td align="center"><b><font face="Arial"><%= MyRec.fields("SAP_DOC_ID") %>&nbsp;</font></b></td>
		<td align="center">&nbsp;</td>
		<td align="center"><font face = Arial><b><%= session("Ename") %></td>
	
		<td align="center">&nbsp;</td>
	</tr>
	
</table></div>
	</div>