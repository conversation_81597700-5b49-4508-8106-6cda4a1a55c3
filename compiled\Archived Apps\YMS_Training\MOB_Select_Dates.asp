
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Select Criteria</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->


 <%  

   Dim objEPS
   Dim  strBegDate, strEndDate, strSortField, strSortType, rstTeam, strTeam, rstESL, rstWA, strWA, strCrew, strID, strStatus, rstEmployee
   Dim objGeneral, strP<PERSON><PERSON>ber, rstAsset, strAsset, rstSource, strSource,  strEstBegDate, strEstEndDate
   dim   rstEPS
  
  set objGeneral = new ASP_CLS_General


if objGeneral.IsSubmit() Then

 
 	strMonth = request.form("Month")
  
  Response.redirect("MOB_Grading_Summary.asp?id=" & strMonth)


end if

 %>

<style type="text/css">
.style1 {
	border: 4px solid #4396B1;
	border-collapse: collapse;
	}
.style2 {
	text-align: center;
	background-color: #D3E9EF;
}
.style4 {
	border-style: solid;
	border-width: 2px;
	background-color: #4396B1;
}
.style5 {
	background-color: #D3E9EF;
}
.style6 {
	font-weight: bold;
	background-color: #D3E9EF;
}
.style7 {
	color: #FFFFFF;
}
.style9 {
	font-family: Arial;
}
.auto-style1 {
	font-weight: bold;
	background-color: #D3E9EF;
	font-family: Arial;
	font-size: x-small;
}
</style>
</head>
<script language="javascript">
function openWindow(pURL)
{
	myWindow = window.open(pURL, "myLittleCalendar", 'toolbar=no,location=no,directories=no,status=no,menubar=no,scrollbars=no,resizable=no,width=170,height=270');
}


</script>

<form name="form1" action="MOB_Select_Dates.asp" method="post">
<br>
<TABLE cellSpacing=0 cellPadding=3 style="width: 75%" align="center" class="style4">
<tr>

 <TD class="style7">
     <p align="left"><font face="Arial" size = 3><b>Select Criteria for </b></font>
		<span class="style9">Fill Rates by Grade Report</span></td>
<td align = right>&nbsp;</td></tr>
	    </table><br>
<br>
<br>
	
<table cellpadding="2" id="AutoNumber1" bordercolorlight="#C0C0C0" bordercolordark="#000000" align="center" class="style1" style="width: 25%">
    
  <tr><td bordercolor="#C0C0C0" colspan="4" class="style5">&nbsp;</td>
	</td></tr>


     <tr> <TD bordercolor="#C0C0C0" style="height: 54px" class="auto-style1">S<font size="2">elect 
		 Month</font></TD>
    <TD style="height: 54px" class="style5">
     <font face="Arial">
     &nbsp;&nbsp;
   <select size="1" name="Month">

     <option value="">--- Select Import Month ---</option>
 <% strsql = "Select Month_year from tblmonth where MID > 146"
    Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString")
   While not MyRec.eof %>
<option><%= MyRec("Month_year") %></option>

 <% MyRec.movenext
 wend
 MyRec.close
 %>
     </select></TD>
    <TD bordercolor="#C0C0C0" style="height: 54px" class="style6">&nbsp;</TD>
    <TD style="height: 54px" class="style5">
     &nbsp;</TD></tr>


<tr><td bordercolor="#C0C0C0" colspan="4" style="height: 52px" class="style2"><font face="Arial">
<input type="submit" value="Submit Criteria" caption="Search"></font></td>
	</tr>

     </table><font face="Arial" size="2"><br>
	</font>
<table >
  <TR bgcolor="white">
   
    <TD >&nbsp;</TD></TR>
  </TABLE></form>
<br>

<!--#include file="Fiberfooter.inc"-->