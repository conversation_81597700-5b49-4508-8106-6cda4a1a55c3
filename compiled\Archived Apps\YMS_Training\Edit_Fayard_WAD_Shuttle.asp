																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Edit Fayard Shuttles</TITLE>
<style type="text/css">
.auto-style1 {
	font-family: Calibri;
}
.auto-style2 {
	font-family: Calibri;
	font-weight: bold;
	font-size: small;
}
.auto-style3 {
	font-family: Calibri;
	font-size: small;
}
.auto-style4 {
	font-size: small;
}
.auto-style5 {
	text-align: center;
}
.auto-style6 {
	font-family: <PERSON><PERSON>ri;
	font-size: small;
	text-align: right;
}
.auto-style7 {
	font-family: Calibri;
	font-weight: normal;
	font-size: small;
}
.auto-style8 {
	border: 1px solid #000000;
}
</style>
</head>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->




<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate

strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -4, strtdate)

strsql = "SELECT tblCars.* FROM tblCars WHERE  Entry_page = 'WAD_TSF_Fayard.asp'  and Entry_time > '" & strdate & "' order by CID desc"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=80%  border=1>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center class="auto-style2">Edit Wadding Transfers from Fayard Warehouse</td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br>
	
	
	<TABLE cellSpacing=0 cellPadding=0 class = "auto-style8" align="center" style="width: 85%">  
	 <tr class="tableheader">
<td class="auto-style3">&nbsp;</td>
			<td class="auto-style5"  > <align ="center">       
			<font size="1" class="auto-style3">Print<br> Receipt</font></td>
		<td class="auto-style7"  > <align ="left">       Receipt Number</td>
	
		<td class="auto-style7"  > WH Ticket #</td>

	
		<td class="auto-style3"  >Trailer</td>
			<td class="auto-style3"  >Location</td>

		<td class="auto-style3"  >Carrier</td>
		<td class="auto-style3"  >Species</td>
  
		<td  > <p align="center" class="auto-style3"> Tons</td>
		<td  > <p class="auto-style6"> Date Transferred</td>
	<td  > <p class="auto-style6">Comments</td>


		<td  > &nbsp;</td>
	

	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td class="auto-style7"> <a href="Transfer_From_Fayard_WAD_Edit.asp?id=<%= MyRec.fields("CID") %>">Edit</a></td>
	<td align = center class="auto-style7"> <a href="Transfer_Freceipt_WAD.asp?id=<%= MyRec.fields("CID") %>">Print</a></td>
	<td class="auto-style3"  ><%= MyRec.fields("CID") %></td>

		<td class="auto-style3"  ><%= MyRec.fields("WH_Ticket") %>&nbsp;</td>

	<td class="auto-style3"  > <%= MyRec.fields("Transfer_Trailer_nbr")%></td>
		<td class="auto-style3"  > <%= MyRec.fields("Location")%></td>
			<td  ><font size="1" face="Arial"><span class="auto-style1">
			<span class="auto-style4"><%= MyRec.fields("Trans_Carrier")%>&nbsp;</span></span></font></td>


		<td class="auto-style3"  > <%= MyRec.fields("Species")%></td>
	
		<td class="auto-style3"  >        <%= MyRec.fields("Tons_received")%></td>
	<td class="auto-style6"  >  <%= MyRec.fields("Transfer_date")%></td>
	<td class="auto-style6"  >  <%= MyRec.fields("Other_Comments")%>&nbsp;</td>
	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>


<!--#include file="Fiberfooter.inc"-->