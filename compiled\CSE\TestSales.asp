﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<script language="JavaScript" type="text/javascript"> 
function loginpage_onload() { 
document.forms[0].submit(); 
} 
</script>
</head> 
<body onload="loginpage_onload();"> 
<form action="https://login.salesforce.com/?display=page&locale=us" method="post"> 
<input type="hidden" name="username" value="<EMAIL>" /> 
<input type="hidden" name="pw" value="password123" /> 
<input type='hidden' name="un" value="">
 <input type='hidden' name="width" value="">
 <input type='hidden' name="height" value="">
 <input type='hidden' name="hasRememberUn" value="true">
 <input type='hidden' name="startURL" value="/home/<USER>">
 <input type='hidden' name="loginURL" value="">
 <input type='hidden' name="loginType" value="">
 <input type='hidden' name="useSecure" value="true">
 <input type='hidden' name="local" value="">
 <input type='hidden' name="lt" value="standard">
 <input type='hidden' name="qs" value="">
 <input type='hidden' name="locale" value="">
 <input type='hidden' name="oauth_token" value="">
 <input type='hidden' name="oauth_callback" value="">
 <input type='hidden' name="login" value="">
 <input type='hidden' name="serverid" value="">
 <input type='hidden' name="display" value="page">

</form> 

