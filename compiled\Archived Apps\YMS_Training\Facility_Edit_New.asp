																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Modify Consumption Projection </TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->


<% Dim MyRec, strsql, strid, strOF
  Dim strdate, strRF, strOCC, strMXP
  
strid = request.querystring("id")

strsql = "Select * from tblTempYardTotals where ID = " & strid
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 

strPMX = MyRec("PMX")
strHBX = MyREc("HBX")
strSHRED = MyREc("Shred_T1")
strKBLD = MyRec("KBLD")
strOF3 = MyRec("OF3")
strOCC = MyRec("Total_OCC")
strMXP = MyRec("Total_MXP")
strSWL = MyRec("SWL")
strUSBS = MyRec("USBS")
strLPSBS = MyRec("LPSBS")
strHWM = MyRec("HWM")
strShredOCC = MyRec("Shred_OCC")

strdate = MyRec.fields("Inv_Date")


MyRec.close


  set objGeneral = new ASP_CLS_General
  if objGeneral.IsSubmit() Then	

  
  	If len(request.form("KBLD")) > 0 then
  	strKBLD = request.form("KBLD")
  	else
  	strKBLD = 0
  	end if
  	
 
  	
  	If len(request.form("OF3")) > 0 then
  	strOF3 = request.form("OF3")
  	else
  	strOF3 = 0
  	end if

  	
	

	If len(request.form("PMX")) > 0 then
  	strPMX = request.form("PMX")
  	else
  	strPMX = 0
  	end if

	
  	If len(request.form("OCC")) > 0 then
  	strOCC = request.form("OCC")
  	else
  	strOCC = 0
  	end if
  	
   	  	If len(request.form("MXP")) > 0 then
  	strMXP = request.form("MXP")
  	else
  	strMXP = 0
  	end if
  	
   
  	  	
  	If len(request.form("SHRED")) > 0 then
  	strSHRED = request.form("SHRED")
  	else
  	strSHRED = 0
  	end if

 
  	
  		If len(request.form("HBX")) > 0 then
  	strHBX = request.form("HBX")
  	else
  	strHBX = 0
  	end if

		If len(request.form("SWL")) > 0 then
  	strSWL = request.form("SWL")
  	else
  	strSWL = 0
  	end if
 
 	If len(request.form("USBS")) > 0 then
  	strUSBS = request.form("USBS")
  	else
  	strUSBS = 0
  	end if
  	
  	  		If len(request.form("LPSBS")) > 0 then
  	strLPSBS = request.form("LPSBS")
  	else
  	strLPSBS = 0
  	end if
 
   		If len(request.form("HWM")) > 0 then
  	strHWM = request.form("HWM")
  	else
  	strHWM = 0
  	end if
 
  
   		If len(request.form("SHRED_OCC")) > 0 then
  	strShredOCC= request.form("SHRED_OCC")
  	else
  	strSHREDOCC = 0
  	end if
 
	
  	   	 strdate = request.form("C_Date")

strsql =  "Update tblTempYardTotals set INV_Date = '" & strDate & "', PMX = " & strPMX & ", total_OCC = " & strOCC & ",  total_MXP = " & strMXP & ", "_
&" SWL = " & strSWL & ", USBS = " & strUSBS & ", LPSBS = " & strLPSBS & ", HWM = " & strHWM & ", Shred_OCC = " & strShredOCC & ", "_
&" SHRED_T1 = " & strShred & ",   HBX = " & strHBX & ", KBLD = " & strKBLD & ", OF3 = " & strOF3 & " where ID = " & strid
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
Response.redirect("Facility_new.asp")		
end if
	
%>

<style type="text/css">
.style2 {
	font-family: arial;
	font-size: x-small;
}
.style3 {
	font-weight: bold;
	font-family: Arial;
	font-size: x-small;
	text-align: center;
	border: 1px solid #C0C0C0;
}
.style6 {
	border: 1px solid #C0C0C0;
	background-color: #FFFFFF;
}
.style7 {
	border: 1px solid #C0C0C0;
	text-align: center;
	background-color: #FFFFFF;
}
.style8 {
	border-style: solid;
	border-width: 1px;
	text-align: center;
}
.style9 {
	border: 1px solid #000000;
}
.style10 {
	border-style: solid;
	border-width: 1px;
	text-align: center;
	font-family: Arial;
	font-size: x-small;
}
.style11 {
	border-style: solid;
	border-width: 1px;
	text-align: center;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.auto-style1 {
	color: black;
	font-size: 11.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Calibri, sans-serif;
	vertical-align: bottom;
	white-space: nowrap;
	border-left: 1.0pt solid windowtext;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: 1.0pt solid windowtext;
	border-bottom: 1.0pt solid windowtext;
	padding: 0px;
	background: yellow;
}
.auto-style2 {
	color: black;
	font-size: 11.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Calibri, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: nowrap;
	border-left: 1.0pt solid windowtext;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: 1.0pt solid windowtext;
	border-bottom: 1.0pt solid windowtext;
	padding: 0px;
	background: yellow;
}
.auto-style6 {
	color: black;
	font-size: 11.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Calibri, sans-serif;
	text-align: center;
	vertical-align: bottom;
	white-space: nowrap;
	border-left: 1.0pt solid windowtext;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: 1.0pt solid windowtext;
	border-bottom: 1.0pt solid windowtext;
	padding: 0px;
	background: #92D050;
}
.auto-style7 {
	border-right: 1px solid #000000;
	color: black;
	font-size: 11.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Calibri, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: nowrap;
	border-left: 1px solid windowtext;
	border-top: 1px solid windowtext;
	border-bottom: 1px solid windowtext;
	padding: 0px;
	background: #92D050;
}
.auto-style8 {
	border-left: 1px none #000000;
	border-right: 1px solid #000000;
	color: black;
	font-size: 11.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Calibri, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: nowrap;
	border-top: 1px solid windowtext;
	border-bottom: 1px solid windowtext;
	padding: 0px;
	background: #92D050;
}
.auto-style9 {
	color: black;
	font-size: 11.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Calibri, sans-serif;
	text-align: center;
	vertical-align: bottom;
	white-space: nowrap;
	border-left: 1.0pt solid windowtext;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: 1.0pt solid windowtext;
	border-bottom: 1.0pt solid windowtext;
	padding: 0px;
	background: #00B0F0;
}
.auto-style10 {
	color: black;
	font-size: 11.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Calibri, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: nowrap;
	border-left: 1.0pt solid windowtext;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: 1.0pt solid windowtext;
	border-bottom: 1.0pt solid windowtext;
	padding: 0px;
	background: #00B0F0;
}
.auto-style11 {
	color: black;
	font-size: 11.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Calibri, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: nowrap;
	border-left-style: none;
	border-left-color: inherit;
	border-left-width: medium;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: 1.0pt solid windowtext;
	border-bottom: 1.0pt solid windowtext;
	padding: 0px;
	background: #00B0F0;
}
.auto-style12 {
	border: 1px solid #000000;
	text-align: center;
	border-collapse: collapse;
}
.auto-style13 {
	color: black;
	font-size: 11.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Calibri, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: nowrap;
	border-left-style: none;
	border-left-color: inherit;
	border-left-width: medium;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: 1.0pt solid windowtext;
	border-bottom: 1.0pt solid windowtext;
	padding: 0px;
	background: #FFFFFF;
}
.auto-style14 {
	color: black;
	font-size: 11.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Calibri, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: nowrap;
	border-left: 1.0pt solid windowtext;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: 1.0pt solid windowtext;
	border-bottom: 1.0pt solid windowtext;
	padding: 0px;
	background: #FFFFFF;
}
.auto-style15 {
	border-left: 1px none #000000;
	border-right: 1px solid #000000;
	color: black;
	font-size: 11.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Calibri, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: nowrap;
	border-top: 1px solid windowtext;
	border-bottom: 1px solid windowtext;
	padding: 0px;
	background: #FFFFFF;
}
.auto-style16 {
	border-right: 1px solid #000000;
	color: black;
	font-size: 11.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Calibri, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: nowrap;
	border-left: 1px solid windowtext;
	border-top: 1px solid windowtext;
	border-bottom: 1px solid windowtext;
	padding: 0px;
	background: #FFFFFF;
}
.auto-style17 {
	color: black;
	font-size: 11.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Calibri, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: nowrap;
	border-left: 1px solid windowtext;
	border-right-style: solid;
	border-right-color: inherit;
	border-right-width: 1px;
	border-top: 1px solid windowtext;
	border-bottom: 1px solid windowtext;
	padding: 0px;
	background: #FFFFFF;
}
.auto-style18 {
	color: black;
	font-size: 11.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Calibri, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: nowrap;
	border-left: 1px solid windowtext;
	border-right-style: solid;
	border-right-color: inherit;
	border-right-width: 1px;
	border-top: 1px solid windowtext;
	border-bottom: 1px solid windowtext;
	padding: 0px;
	background: #00B0F0;
}
</style>
</head>

<body>
<br>
	
<form name="form1" action="Facility_Edit_New.asp?id=<%= strid%>" method="post" ID="Form1">
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Modify Consumption Projection&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
Inventory Date: </b></font><font size="2" face="Arial">
<input type="text" name="C_Date" value="<%= strDate%>" style="width: 121px"></font></td>
<td align = right><font face="Arial"><a href="Facility_new.asp">Return</a>&nbsp;</td>


</tr>
	    </table>



	<p>
	<table cellpadding="0" cellspacing="0" class="auto-style12" style="width:480pt" width="640" align="center">
		<tr height="20">
			<td class="auto-style1" colspan="6" >			RF GRADES</td>
		</tr>
		<tr height="31">
			<td class="auto-style2" height="31" >KBLD</td>
			<td class="auto-style2">OF3</td>
			<td class="auto-style2" colspan="2">PMX</td>
			<td class="auto-style2">SHRED</td>
			<td class="auto-style2">HBX</td>
		</tr>
		<tr height="31">
			<td class="auto-style14" height="31" ><font face = arial size = 1>
		<input type="text" name="KBLD" size="5" style="width: 35px" value="<%= strKBLD%>" tabindex="1"></td>
			<td class="auto-style14"><font face = arial size = 1>
		<input type="text" name="OF3" size="5" style="width: 35px" value="<%= strOF3 %>" tabindex="2"></td>
			<td class="auto-style17" colspan="2"><font face = arial size = 1>
		<input type="text" name="PMX" size="5" style="width: 35px" value="<%= strPMX%>" tabindex="3"></td>
			<td class="auto-style14"><font face = arial size = 1>
		<input type="text" name="SHRED" size="5" style="width: 35px" value="<%= strSHRED%>" tabindex="4"></td>
			<td class="auto-style13"><font face = arial size = 1>
		<input type="text" name="HBX" size="5" style="width: 35px" value="<%= strHBX%>" tabindex="5"></td>
		</tr>
		<tr><td colspan="6">&nbsp;</td></tr>
		<tr height="20">
			<td class="auto-style6" colspan="6" height="20" style="height: 15.0pt">
			OCC BROWN GRADES</td>
		</tr>
		<tr height="31">
			<td class="auto-style7" colspan="3" height="31"  width="50%">OCC</td>
			<td class="auto-style8" colspan="3">MXP</td>
		</tr>
		<tr height="31">
			<td class="auto-style16" colspan="3" height="31" style="height: 23.15pt">
			<font face = arial size = 1>
		<input type="text" name="OCC" size="25" style="width: 47px" value="<%= strOCC %>" tabindex="9"></td>
			<td class="auto-style15" colspan="3"><font face = arial size = 1>
		<input type="text" name="MXP" size="5" style="width: 43px; height: 22px;" value="<%= strMXP %>" tabindex="10"></td>
		</tr>
				<tr><td colspan="6">&nbsp;</td></tr>
		<tr height="20">
			<td class="auto-style9" colspan="6" height="20" style="height: 15.0pt">
			OCC WHITE GRADES</td>
		</tr>
		<tr height="31">
			<td class="auto-style10" height="31"  >
			SWL</td>
			<td class="auto-style10">USBS</td>
			<td class="auto-style10" colspan="2">LPSBS</td>
			<td class="auto-style18">HWM</td>
			<td class="auto-style11">OCC SHRED</td>
		</tr>
		<tr height="31">
			<td class="auto-style14" height="31"  >
			<font face = arial size = 1>
		<input type="text" name="SWL" size="5" style="width: 35px" value="<%= strSWL%>" tabindex="11"></td>
			<td class="auto-style14"><font face = arial size = 1>
		<input type="text" name="USBS" size="5" style="width: 35px" value="<%= strUSBS%>" tabindex="12"></td>
			<td class="auto-style14" colspan="2"><font face = arial size = 1>
		<input type="text" name="LPSBS" size="5" style="width: 35px; height: 22px;" value="<%= strLPSBS%>" tabindex="13"></td>
			<td class="auto-style17"><font face = arial size = 1>
		<input type="text" name="HWM" size="5" style="width: 35px; height: 22px;" value="<%= strHWM%>" tabindex="14"></td>
			<td class="auto-style13"><font face = arial size = 1>
		<input type="text" name="SHRED_OCC" size="5" style="width: 35px" value="<%= strSHREDOCC%>" tabindex="15"></td>
		</tr>
		</table>
	</p>
<p>&nbsp;</p>


<p align="center"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></p>
</form>
<!--#include file="Fiberfooter.inc"-->