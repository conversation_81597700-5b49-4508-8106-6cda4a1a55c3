﻿<%
Class ASP_CLS_ProcedureMRP

 Public Function Invoice(Mill, PO)
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
 

    set Invoice = objDAC.ExecuteSp("asp_sp_InvoiceSearch", array(Mill, PO))
    set objDAC = nothing

 End Function

Public Function Except(Mill, Status)
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
 

    set Except = objDAC.ExecuteSp("asp_sp_ExceptionSearch", array(Mill, Status))
    set objDAC = nothing

 End Function
 
  Public Function ExceptionActive(Mill)
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
 

    set ExceptionActive = objDAC.ExecuteSp("asp_sp_ExceptionActive", array(Mill))
    set objDAC = nothing

 End Function


 Public Function InvoiceNoPO(Mill)
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
 

    set InvoiceNoPO = objDAC.ExecuteSp("asp_sp_InvoiceSearchNoPO", array(Mill))
    set objDAC = nothing

 End Function

 Public Function ESLSearchPC(PageNumber)
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
 

    set eslSearchPC = objDAC.ExecuteSp("asp_cls_PC", array(50, PageNumber))
    set objDAC = nothing

 End Function

 Public Function ESLSearchPCAPP(PageNumber)
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
 

    set eslSearchPCAPP = objDAC.ExecuteSp("asp_cls_PC_APP", array(50, PageNumber))
    set objDAC = nothing

 End Function

 Public Function ESLSearchPCDR(PageNumber)
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
 

    set eslSearchPCDR = objDAC.ExecuteSp("asp_cls_PC_DR", array(50, PageNumber))
    set objDAC = nothing

 End Function











End Class
%> 