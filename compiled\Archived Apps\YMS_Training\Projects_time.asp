<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_SessionStringFiberDEV.asp"-->

<html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Project Time </TITLE>

<% 
	x = Request.Servervariables("LOGON_USER")
	strEID = ""
	If InStr(x,"\") Then
  		y = SPLIT(x,"\")
  		strEID = y(1)
	Else
  		strEID = x
	End If
	     	

         Session("EmployeeID") = strEID


 Dim MyRec, strsql, strstartdate
strStartdate = "10/6/20 7:00 am"
 
 
 if Session("EmployeeID") = "F00390" then 
strsql = "SELECT tblTime.* from tblTime where TIME_START > '" & strStartDate & "'   and Person = 'F00390'     order by Time_start desc"
else
strsql = "SELECT tblTime.* from tblTime where TIME_START > '" & strStartDate & "'      order by Time_start desc"

end if

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<style type="text/css">
.style1 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial;
	font-size: x-small;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style2 {
	font-family: Arial, Helvetica, sans-serif;
}
.style3 {
	font-family: Arial, Helvetica, sans-serif;
	font-weight: bold;
}
.style4 {
	border: 1px solid #808080;
}
.style5 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style6 {
	text-align: center;
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style7 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial;
	font-size: x-small;
	text-align: center;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style8 {
	font-size: x-small;
}
.style9 {
	text-align: center;
	font-family: Arial, Helvetica, sans-serif;
}
.style10 {
	font-size: small;
	font-weight: bold;
}
.style11 {
	text-align: right;
	font-family: Arial, Helvetica, sans-serif;
}
.auto-style1 {
	font-size: x-small;
	font-family: Arial, Helvetica, sans-serif;
}
</style>
</head>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>
<tr> 

<td align = left class="style3">Time List</td>
<td align = left class="style3">&nbsp;</td>
<td align="right" class="style2"><a href="Time_Add.asp">Add New</a></td>



</tr>
	    </table>
	
	
	<TABLE cellSpacing=0 cellPadding=0 width=100% class = "style4" align = center>  
	 <tr class="tableheader">
		<td class="style5" ><font face="Arial" size="2">	Project</font></td>
			<td class="style5" ><font face="Arial" size="2">Task</font></td>
		<td class="style1"  > <font face="Arial" size="2">Time Start</font></td>
		<td class="style1"  > <font face="Arial" size="2">Time End</font></td>
		<td class="style7"  > <font face="Arial" size="2">Duration</font></td>

		<td class="style1"  > <font face="Arial" size="2">Files Modified</font></td>
		
		<td class="style1"  > <font face="Arial" size="2">Notes</font></td>
		<td class="style7"  > <font face="Arial" size="2">Person Completing</font></td>
				<td class="style7"  > <font face="Arial" size="2">Edit</font></td>
	</tr>

 <% Dim ii
       ii = 0
       strBritt = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>

	<td class="style5"  ><font size="2" face="Arial"><%= MyRec.fields("Project")%></font></td>
		<td class="style5"  ><font size="2" face="Arial"><%= MyRec.fields("Task")%></font></td>
	<td class="style5"  ><font size="2" face="Arial"><%= MyRec.fields("Time_start")%>&nbsp;</font></td>
<td class="style5"  ><font size="2" face="Arial"><%= MyRec.fields("Time_Stop")%>&nbsp;</font></td>
<td class="style6"  ><font size="2" face="Arial">
<% if len(MyRec("time_stop")) > 2 then
strStart = MyRec("Time_start")
strEnd = MyRec("Time_Stop") %>

<%= datediff("n", strSTart, strEnd)%>
<% strPerson = MyRec("Person")

if strPerson = "F00390" then 
strTim = strTim + datediff("n", strSTart, strEnd)
elseif strPerson = "F18550" then 
strC = strC  + datediff("n", strSTart, strEnd)
elseif strPerson = "C97338" then
strDJ = strDJ + datediff("n", strSTart, strEnd)
end if
 end if %>&nbsp;</font></td>	
<td class="style5"  ><font size="2" face="Arial"><%= MyRec.fields("Files_modified")%>&nbsp;</font></td>
<td class="style5"  ><font size="2" face="Arial"><%= MyRec.fields("Status")%>&nbsp;</font></td>
<td class="style6"  ><font size="2" face="Arial"><%= MyRec.fields("person")%>&nbsp;</font></td>
<td class="style6"  ><font size="2" face="Arial"><a href="Time_Edit.asp?id=<%= MyRec("ID") %>"><%= MyRec("ID") %></a>&nbsp;</font></td>
</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>


		<span class="style2"><strong><br class="style8">
<span class="style8">
<% strTotal = round(strTim/60,2)
strTotalDJ = round(strDJ/60,2)
strTotalC = round(strC/60,2)
If session("EmployeeID") = "C97338" then
%>Total Time for DJ:  <%= strTotalDJ %> &nbsp;&nbsp; </span> <br class="style8">
<br class="style8">
<span class="style8">
<% end if %>
<% if Session("EmployeeID") = "F00390" or Session("EmployeeID") = "C97338" then %>
Total Time for Tim:  <%= strTotal %>   Mailed check 10/6 for 40.5 hrs</span><br class="style8"> 
 <span class="style8"> &nbsp;<br>
 <% end if %>
 
</span>
</strong>

 
</span>
<% if Session("EmployeeID") = "F18550" or Session("EmployeeID") = "C97338" then %>
<br class="auto-style1"> 
 <span class="style8"> &nbsp;<br>
 <% end if %>
 
</span>
 

<br>

