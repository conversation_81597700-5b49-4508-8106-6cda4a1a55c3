																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>In Transit Rail Cars </TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->


<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate

strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -7, strtdate)

strsql = "SELECT * from tblVirginFiber where status = 'Received'  and Release = 'B12M3195' "

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<style type="text/css">
.style1 {
	font-family: Arial;
	font-weight: bold;
	text-align: right;
}
.style2 {
	text-align: right;
}
</style>
</head>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>
<tr> <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center>
<p align="left"><b><font face="Arial">Select Recovered Fiber Rail Car Load to Receive </font></b></td>
<td class="style1"><a href="Generic_Receipt_Rail.asp">Enter Rail Exception if Car is not on List</a></td>

</tr>
	    </table>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1 align = center>  
	 <tr class="tableheader">
<td>&nbsp;</td>

				<td  align = left>     <font face="Arial" size="1">	Trailer/Car</font></td>
		<td  ><b><font face="Arial" size="1">Date Shipped</font></b></td>
	
		<td  align = left>     <font face="Arial" size="1">ETA</font></td>
		<td  align = left>     <font face="Arial" size="1">Release</font></td>
		<td  align = left>     <font face="Arial" size="1">Vendor</font></td>
		<td  ><b><font face="Arial" size="1">Item Description</font></b></td>
			<td  ><b><font face="Arial" size="1">SAP</font></b></td>
				<td  ><b><font face="Arial" size="1">PO</font></b></td>
		<td  ><b><font face="Arial" size="1">Units</font></b></td>
		<td  align = left>     <font face="Arial" size="1">	Tons</font></td>
		<td  ><b><font face="Arial" size="1">UOM</font></b></td>
			<td  ><b><font face="Arial" size="1">Comments</font></b></td>
		
	</tr>

 <%  Dim ii
       ii = 0
       while not MyRec.Eof

 
 
 strRelease = MyRec("Release")

 
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td> <font size="1" face="Arial">



<a href="EnterRail.asp?id=<%= MyRec.fields("Release") %>">Receive</a></td>

	<td  ><font size="1" face="Arial"><%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	
<td  ><font size="1" face="Arial"><%= MyRec.fields("Date_shipped")%>&nbsp;</font></td>
<% if datediff("d", MyRec.fields("ETA"), strTdate) > 0 then %>	
<td bgcolor="#FEDAD6"  >
<% else %>
<td>
<% end if %><font size="1" face="Arial"><%= MyRec.fields("ETA")%>&nbsp;</font></td>
<td  ><font size="1" face="Arial"><%= MyRec.fields("Release")%>&nbsp;</font></td>
	<td  ><font size="1" face="Arial"><%= MyRec.fields("Vendor")%>&nbsp;</font></td>
	
<td  ><font size="1" face="Arial"><%= MyRec.fields("Species")%>&nbsp;</font></td>
<td  ><font size="1" face="Arial"><%= MyRec.fields("SAP_NBR")%>&nbsp;</font></td>
<td  ><font size="1" face="Arial"><%= MyRec.fields("PO")%>&nbsp;</font></td>
	<td  ><font size="1" face="Arial"><%= MyRec.fields("Bales_VF")%>&nbsp;</font></td>
	
<td  ><font size="1" face="Arial"><%= MyRec.fields("Tons")%>&nbsp;</font></td>
	<td  ><font size="1" face="Arial"><%= MyRec.fields("UOM")%>&nbsp;</font></td>
		<td  ><font size="1" face="Arial"><%= MyRec.fields("Other_comment")%>&nbsp;</font></td>
</tr>

 <%  
       ii = ii + 1
       MyRec.MoveNext
     Wend

    %>
</table>
<%

strsql = "SELECT * from tblVirginFiber where status = 'Inbound'  and Trailer is not null and vendor <> 'Corporate' order by Trailer"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>
<tr> <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center>
<p align="left"><b><font face="Arial">Select Virgin Fiber Rail Car Load to Receive</font></b></td>

<td class="style2">&nbsp;</td>

</tr>
	    </table>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1 align = center>  
	 <tr class="tableheader">
<td>&nbsp;</td>

				<td  align = left>     <font face="Arial" size="1">	Trailer/Car</font></td>
		<td  ><b><font face="Arial" size="1">Date Shipped</font></b></td>
	
		<td  align = left>     <font face="Arial" size="1">ETA</font></td>
		
		<td  align = left>     <font face="Arial" size="1">Vendor</font></td>
		<td  ><b><font face="Arial" size="1">Item Description</font></b></td>
			<td  ><b><font face="Arial" size="1">SAP</font></b></td>
				<td  ><b><font face="Arial" size="1">PO</font></b></td>
		<td  ><b><font face="Arial" size="1">Units</font></b></td>
		<td  align = left>     <font face="Arial" size="1">	Tons</font></td>
		<td  ><b><font face="Arial" size="1">UOM</font></b></td>
			<td  ><b><font face="Arial" size="1">Comments</font></b></td>
		
	</tr>

 <% 
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td> <font size="1" face="Arial">

<a href="VF_Login.asp?p=ER&id=<%= MyRec.fields("VID") %>">Receive</a></td>

	<td  ><font size="1" face="Arial"><%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	
<td  ><font size="1" face="Arial"><%= MyRec.fields("Date_shipped")%>&nbsp;</font></td>
<% if datediff("d", MyRec.fields("ETA"), strTdate) > 0 then %>	
<td bgcolor="#FEDAD6"  >
<% else %>
<td>
<% end if %><font size="1" face="Arial"><%= MyRec.fields("ETA")%>&nbsp;</font></td>
	<td  ><font size="1" face="Arial"><%= MyRec.fields("Vendor")%>&nbsp;</font></td>
	
<td  ><font size="1" face="Arial"><%= MyRec.fields("Species")%>&nbsp;</font></td>
<td  ><font size="1" face="Arial"><%= MyRec.fields("SAP_NBR")%>&nbsp;</font></td>
<td  ><font size="1" face="Arial"><%= MyRec.fields("PO")%>&nbsp;</font></td>
	<td  ><font size="1" face="Arial"><%= MyRec.fields("Bales_VF")%>&nbsp;</font></td>
	
<td  ><font size="1" face="Arial"><%= MyRec.fields("Tons")%>&nbsp;</font></td>
	<td  ><font size="1" face="Arial"><%= MyRec.fields("UOM")%>&nbsp;</font></td>
		<td  ><font size="1" face="Arial"><%= MyRec.fields("Other_comment")%>&nbsp;</font></td>
</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>

<!--#include file="Fiberfooter.inc"-->