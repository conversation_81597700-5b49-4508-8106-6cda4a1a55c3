
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Load Activity FROM Warehouse</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"--> 
<!--#include file="classes/asp_cls_General.asp"-->


<%  
strBegDate = request.querystring("b")
strEndDate = request.querystring("e")
strSpecies = request.querystring("s")
strLocation = request.querystring("l")

    

if strLocation = "TRANSLOAD" then
   
if strSpecies = "" then
 
strsql2  = "SELECT tblCars.* from tblCars  where RC_CID > 0 "_
&" and Date_received  >= '" & strBegDate & "' and tblCars.Date_received <= '" & strEndDate & "' ORDER BY Date_received desc "

else
strsql2  = "SELECT tblCars.* FROM tblCars  where RC_CID > 0 and species = '" & strSpecies & "' "_
&" and Date_received  >= '" & strBegDate & "' and tblCars.Date_received <= '" & strEndDate & "' ORDER BY Date_received desc "

end if
 Set rstEquip = Server.CreateObject("ADODB.Recordset")
    rstEquip.Open strSQL2, Session("ConnectionString"), adOpenDynamic
else
strsql2 = " SELECT  cid,  PO,  location, Transfer_date, trans_unload_date,  PMO_Nbr,  Pnbr,  species, Inv_depletion_date, transfer_trailer_nbr, "_
&"   release_nbr,  Vendor,  Trailer,  Generator,  Date_Received,  Date_unloaded,  Bales_RF,   other_comments,  Tons_received,  Deduction,  Net  FROM tblCars "_
&"  where  Date_received >= '" & strBegDate & "' and Date_received <= '" & strEndDate & "'"

if len(strLocation) > 0 then
strsql2 = strsql2 & " and PMO_Nbr = '" & strlocation & "'"
end if

if len(strSpecies) > 1 then
strsql2 = strsql2 & " and Species = '" & strspecies & "'"
end if
 Set rstEquip = Server.CreateObject("ADODB.Recordset")

   rstEquip.Open strSQL2, Session("ConnectionString"), adOpenDynamic

 end if
Response.ContentType = "application/vnd.ms-excel"	
 %>




<style type="text/css">
.auto-style1 {
	border-color: #C0C0C0;
	border-width: 1px;
	background-color: #E7EBFE;
}
.auto-style2 {
	font-weight: bold;
	border-color: #C0C0C0;
	border-width: 1px;
	background-color: #E7EBFE;
}
.auto-style3 {
	border-style: solid;
	border-width: 1px;
}
.auto-style4 {
	font-size: x-small;
}
.auto-style5 {
	font-size: x-small;
	text-align: left;
}
</style>
</head>

  <table>
      <tr class="tableheader">	
      <td  align="center" class="auto-style4" >
		<p align="left" class="auto-style4"><font face="Arial"><strong>Species</strong></font></td>

		<td  align="center" class="auto-style4" ><font face="Arial"><strong>From Location</strong></font></td>
		<td  align="center" class="auto-style4" ><font face="Arial"><strong>PMO</strong></font></td>
		<td  align="center" class="auto-style4" ><font face="Arial"><strong>Rental</strong></font></td>

	<td  align="center" class="auto-style4" ><font face="Arial"><strong>Transfer Trailer</strong></font></td>
	<td class="auto-style5" ><font face="Arial"><strong>Transfer Date</strong></font></td>
	<td  align="center" class="auto-style4" ><font face="Arial"><strong>Trans Unload</strong></font></td>
	<td  align="center" ><font face="Arial" size="1" class="auto-style4">
	<strong>Inv Depletion<br>Date</strong></font></td>
	<td  align="center" class="auto-style4" ><font face="Arial"><strong>Location</strong></font></td>

	<td  align="center" class="auto-style4" ><font face="Arial"><strong>Net</strong></font></td>


	<td  align="center" class="auto-style4" ><font  face="Arial"><strong>Other</strong></font></td>
	<td  align="center" class="auto-style4" ><font  face="Arial"><strong>Count</strong></font></td>
      
  	<%   strcount = 1
      Dim ii
       ii = 0
       while not rstEquip.Eof
      
    %>
    <% if ( ii mod 2) = 0 Then %>
      <tr bgcolor="#E7EBFE">
      
    <% else %>
      <tr bgcolor="white">
    <% end if %>
    
    <%    if len(rstEquip.fields("Inv_depletion_date")) > 2 then
   if formatdatetime(rstEquip.fields("Inv_depletion_date"),2) <> strDepdate then
       strcount = 1
       end if 
       end if %> 

<td  align="left" class="auto-style4" ><font face = "arial" size = "1">
<span class="auto-style4"><%=rstEquip.fields("Species")%>&nbsp;</span></td>

<td  align="center" ><font face = "arial" size = "1"><span class="auto-style4"><%=rstEquip.fields("PMO_Nbr")%>&nbsp;</span></td>
<td  align="center" ><font face = "arial" size = "1"><span class="auto-style4"><%=rstEquip.fields("Pnbr")%>&nbsp;</span></td>
<%     if rstEquip("Trailer") = "UNKNOWN" Then 
    strCheck = right(rstEquip("Transfer_trailer_nbr"),5)
    else
     strCheck = right(rstEquip("Trailer"),5)
	end if
    strsql2 = "select Trailer_Nbr from tblRentedTrailer where right(Trailer_Nbr,5) = '" & strCheck & "'"
    
    		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL2, Session("ConnectionString")
		If not MyRec.eof then
%>
<td  align="center" class="auto-style4" ><font face = "arial">YES&nbsp;</td>
<% else %>
<td  align="center" class="auto-style4" ><font face = "arial">&nbsp;</td>
<% end if 
MyRec.close %>

<td  align="center" class="auto-style4" ><font face = "arial" size = "1">
<span class="auto-style4"><%=rstEquip.fields("Transfer_Trailer_nbr")%>&nbsp;</span></td>

<td  align="left" ><font face = "arial" size = "1"><span class="auto-style4"><%=rstEquip.fields("Transfer_date")%>&nbsp;</span></td>
<td  align="center" ><font face = "arial" size = "1"><span class="auto-style4"><%=rstEquip.fields("Trans_unload_date")%>&nbsp;</span></td>
<td  align="center" ><font face = "arial" size = "1"><span class="auto-style4"><%=rstEquip.fields("Inv_depletion_date")%>&nbsp;</span></td>
<td  align="center" ><font face = "arial" size = "1"><span class="auto-style4"><%=rstEquip.fields("Location")%>&nbsp;</span></td>
<% if not isnull(rstEquip.fields("Net")) then %>
<td  align="center" class="auto-style4" ><font face = "arial" size = "1">
<span class="auto-style4"><%= formatnumber(rstEquip.fields("net"),3)%>&nbsp;</span></td>
<% else %>
<td  align="center" class="auto-style4" ><font face = "arial">&nbsp;</td>
<% end if %>



<td  align="center" class="auto-style4" ><font face = "arial" size = "1">
<span class="auto-style4"><%= rstEquip.fields("Other_Comments")%>&nbsp;</span></td>
<td  align="center" ><font face = "arial" size = "1"><span class="auto-style4"><%= strCount%>&nbsp;</span></td>


 </tr>
    <% 
       ii = ii + 1
       if len(rstEquip.fields("Inv_depletion_date")) > 2 then
          strDepDate = formatdatetime(rstEquip.fields("Inv_depletion_date"),2)
          end if
           strcount = strcount + 1
       rstEquip.MoveNext
     Wend
    %>
   </table>
 
