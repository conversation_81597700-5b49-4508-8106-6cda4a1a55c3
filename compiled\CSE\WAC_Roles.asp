
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 6.0">
<TITLE>Administrator Roles</TITLE>

<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_Procedure.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<%    Dim   rstTeam, strTeam, rstESL, rstWA, strWA,  strID, objEPS, strName, strName2, strType

    set objGeneral = new ASP_CLS_General %>
	<body>

<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% border=1>
  <tr><td colspan="4" bgcolor="white" class="subheader">&nbsp;</td></tr>

 <TD>
     <p align="left"><font size="3" face="Arial"><b>Administrator Roles</b>&nbsp;&nbsp;</font></td>
<td align = right><a href="Admin_roles.asp"><font face="Arial"><b>Return</b></font></td></tr>
	    </table><font face="Arial"><br>
	    <%  
	        Dim SCRIPT_NAME
		Dim strSQL
		Dim BACK_TO_LIST_TEXT

		SCRIPT_NAME = Request.ServerVariables("SCRIPT_NAME")

		BACK_TO_LIST_TEXT = "<p>Click <a href=""" & SCRIPT_NAME & """>" _
    		    & "here</a> to go back to record list.</p>"
    		    
    If Session("EmployeeID") = "C97338" or session("EmployeeID") = "B41792" or Session("EmployeeID") = "B87061" or Session("EmployeeID") = "B68370"  or Session("EmployeeID") = "B42111" then
 
	    %>

	    <%
		Select Case LCase(Trim(Request.QueryString("action")))
		    Case "add"
		    %>
			</font>
			<form action="<%= SCRIPT_NAME %>?action=addsave" method="post">
			<%  Call GetFormData() %>
		            <font face="Arial">
		            <br><b>Work Area:</b> 
			    <br><font face="Arial">  <select name="Work_area">
       <option value="">--- All ---</option>
       <%= objGeneral.OptionListAsString(rstWA, "WorkArea", "WorkArea", strWA) %>
     </select></font>
			    <br>
			    <br><b>BID:    </b> 
			    <br><Input name="BID" size="13">
			    <br>
			      <br><b>Administrator/Manager:    </b> <br>
			 <select name="TM" size="1">
       <option value="A" selected>Team Administrator</option>
       <option value="M">Manager</option>
     </select><br>

			    	</font>

			    <br><Input name="Update" type="submit" Value="Add Name" >
 			</form>
                    <%
	         

 		    Case "addsave"
 		   
         strName2 = ReturnEname(Request.Form("BID"))
         strType = Request.form("TM")
	
			strSQL = "Insert into tblAuthorizers (Work_area, P_BID, P_Name, P_Type) Values('" & Request.Form("Work_area") & "', " _
			   & "'" & Request.Form("BID") & "', '" & strName2 & "', '" & strType & "')"

			
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

			Response.Write("Record has been added successfully.")

			
	

		    Case "edit"
			iRecordId = Request.QueryString("id")
			strSQL = "Select * From tblAuthorizers Where NID = " & iRecordId

			Set MyRec = Server.CreateObject("ADODB.RecordSet")
			MyRec.Open strSQL, Session("ConnectionString")

			If Not MyRec.EOF Then
			    %>
				<form action="<%= SCRIPT_NAME %>?action=editsave" method="post">
				<%  Call GetFormData() %>
				    <input type="hidden" name="id" value="<%= MyRec.Fields("NID").Value %>" />
				    <% strWA = MyRec.fields("Work_Area")%>
		            	    <font face="Arial">
		            	    <br><b>Work Area:</b> 
			    	    <br>
                    <font face="Arial">  <select name="Work_area">
       <option value="">--- All ---</option>
       <%= objGeneral.OptionListAsString(rstWA, "WorkArea", "WorkArea", strWA) %>
     </select></font></TD>
			    	    <br>
			    	    <br><b>BID:    </b> 
			    	    <br>
                    <input Name="BID" value="<%= MyRec.Fields("P_BID").Value %>" size="10" />
			    	   	    <br>
			    	    &nbsp;<br><b>Administrator/Manager:    </b> <br>
			 <select name="TM" size="1">
       <option value="A" selected>Team Administrator</option>
       <option value="M">Manager</option>
     </select></font><br>
		
			    	   	    <br><Input name="Update" type="submit" Value="Update Record" > 
				</form>
			    <%
			End If

		
			

		    Case "editsave"
		       strName2 = ReturnEname(Request.Form("BID"))
			iRecordId = Clng(Request.form("id"))
			 strType = Request.form("TM")
		
			strSQL = "Update tblAuthorizers Set Work_area = '" & Request.Form("Work_area") & "', P_BID = '" & Request.Form("BID") & "', "_
			&" P_Name = '" & strName2 & "', P_Type = '" & strType & "' Where NID = " & iRecordId

			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

			Response.Write("Record has been updated successfully.")

		

		    Case "delete"
	        ' Get the id to delete
			iRecordId = Clng(Request.QueryString("id"))
			

			strSQL = "DELETE FROM tblAuthorizers WHERE NID = " & iRecordId 

			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

			Response.Write("Record has been deleted successfully.")


		    Case else   'Default view
			strSQL = "SELECT * from tblAuthorizers order by work_area"
		        Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
			%>
			<table Border=1 bgcolor = #F0F0F0 BORDERCOLOR = #CCCCFF cellpadding = 3 width = 75% align = center>
			<thead>
			    <tr>
		
                	
                		<th bgcolor="#FDFEE9"> <font face="arial" size="2">Work Area</font></th>
	     		
                		<th bgcolor="#FDFEE9"> <font face="arial" size="2">Name</font></th>
                	
                			<th bgcolor="#FDFEE9"> <font face="arial" size="2">Team Administrator<br> or Manager</font></th>
			
	     	
                	
                		<th colspan="2" bgcolor="#FDFEE9">&nbsp;</th>
                       
              	    </tr>
			    </thead>
			    <tbody>
	
        		        <% While not MyRec.EOF %>
              			    <tr>
               <td bgcolor="#FFFFFF"><font face="Arial"><%= MyRec.Fields("Work_area").Value %></font></td>
              		   <td bgcolor="#FFFFFF"><font face="Arial"><%= MyRec.Fields("P_Name").Value %></font></td>
              		    		   <td bgcolor="#FFFFFF" align = "center"><font face="Arial">
              		 <% if MyRec.fields("P_Type") = "M" then %>
              		 Manager
              		 <% else%>
              		 Administrator
              		 <% end if %>   		   
              		    		 </font></td>
					<td align="center" bgcolor="#FFFFFF"><font face="Arial"><a href="<%= SCRIPT_NAME %>?action=edit&id=<%= MyRec.Fields("NID").Value %>">Edit</a></font></td>
              			 <td align="center" bgcolor="#FFFFFF"><font face="Arial"><a href="<%= SCRIPT_NAME %>?action=delete&id=<%= MyRec.Fields("NID").Value %>">Delete</a></font></td>
  						 </tr>
                            
              			    <% 
				        MyRec.MoveNext
                 			WEND
                 			MyRec.Close
              			    %>

			    </tbody>
			    <tfoot>
				<tr>
				    <td colspan="4" align="right"><font face="Arial"><a href="<%= SCRIPT_NAME %>?action=add">Add a new record</a></font></td>
				</tr>
			    </tfoot>

            		</table>
		<font face="Arial">
		<%					
		End Select
		%>                    
      	</font>                    
      	</body>
      	
      	  <%  Function GetFormData()
	set objEPS = new ASP_CLS_ProcedureESL
  	set rstWA = objEPS.ReadWorkArea()

    End Function
   else
    Response.write ("<font face = arial size = 3>You do not have authorization to view this page")
   
    end if  %><!--#include file="footer.inc"-->