<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Enter Trailer Receipt</title>
<style type="text/css">
.style1 {
	font-size: x-small;
}
.style25 {
	font-weight: bold;
	border-width: 1px;
	background-color: #FDE3E8;
}
.style26 {
	border-width: 1px;
	background-color: #FDE3E8;
}
.style28 {
	border: 1px solid #000000;
	border-collapse: collapse;
	}
.style29 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.auto-style1 {
	font-weight: bold;
	background-color: #EAF1FF;
}
.auto-style2 {
	background-color: #EAF1FF;
}
.auto-style3 {
	border-width: 1px;
	background-color: #EAF1FF;
}
.auto-style4 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	background-color: #EAF1FF;
}
.auto-style5 {
	color: #000000;
	font-size: x-small;
}
.auto-style6 {
	text-align: right;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID,  MyConn, objMOC, rstFiber, rstSpecies, strKCWeighed, strPounds  
    Dim strTrailer, strCarrier, strLocation, MyRec5, strsql5, stralert
    Dim rstTrailer , strTrailerWeight , strTractor, strTrailerTID
 
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strOther, strsql3, strSpecies, strNet, strPO, strR, objGeneral
 
  set objGeneral = new ASP_CLS_General
if objGeneral.IsSubmit() Then
	If request.form("Cancel") = "ON" then
	Response.redirect ("SelectSTO.asp")
	else


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
	
	

	strSpecies ="Unresolved"
	
 	strTrailer = Request.form("Trailer")
	strOther = Replace(Request.form("Other_Comments"), "'", "''") 
	strDateReceived = Request.form("Date_received")
	strGrossweight = Request.form("Gross_Weight")
	strPounds = Request.form("Tons_Received")
	strCarrier = Request.form("Carrier")
	strLocation = Request.form("Location")
 
	strSAPWeight = request.form("SAP_Weight")
	strLoad = Request.form("Load")
	
If   isnull(strCarrier) or strCarrier = ""  then
 
	Session("GrossWeight") = strGrossWeight
	Session("Pounds") = strPounds
	Session("Trailer") = strTrailer
	Session("Carrier") = strCarrier
	Session("Other") = strOther
	Session("SAPWeight") = strSAPWeight
	Session("Load") = strLoad


	Response.redirect("Generic_STO_Receipt.asp?n=T")
	else
	
	Call SaveData() 
		end if ' if there is an alert
	
	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to save this information.</font></br>")
	MyRec.close
	end if ' if you don't have authorization
end if ' if they did not cancel out
end if ' if they did not submit
%>

<% if Request.querystring("n") = "T" then 
strTrailer = Session("Trailer")
strCarrier = Session("Carrier")
 
strGrossWeight = Session("GrossWeight")
strSapWeight = SEssion("SAPWeight")
strLoad = SEssion("Load")

strPounds = Session("Pounds")
strOther = Session("Other")
 %>
<p align = center><font face = arial size = 3 color = red><b>


 
<% if strCarrier = "" then %>
You must enter a Carrier</span><br>
<% end if %>

</p>
</b></font>
<% else %>
&nbsp;
<% end if %>


<body>
<table width = 100%><tr><td width = 33%>
&nbsp;</td><td align = center width = 34%><b><font face="Arial" size="4">
Enter Trailer Receipt </b></font> </b></td><td align = right width = 33%><a href="SelectSTO.asp"><b><font face = arial size = 2>RETURN</a></font></b></td></tr></table>




<form name="form1" action="Generic_STO_Receipt.asp" method="post">
<div align="center">
<table cellspacing="0" bgcolor="#FFFFEA" style="width: 70%;" cellpadding="0" class="style28">

<tr>
    <td align="right" class="auto-style1"><font face="Arial" size="2">&nbsp;</font></td>

    <td  align = center colspan="2" class="auto-style2">&nbsp; 
	</td>
  </tr><tr>
    <td align="right" class="auto-style1"><font face="Arial" size="2">Species:</font></td>

    <td class="auto-style2">  
     <font face="Arial">
  	<span class="style1"><strong>&nbsp;Unresolved</strong></span></td>

    <td style="width: 44%" class="auto-style2"> 
	<p align="center"><font face="Arial"><font size="2" face="Arial"><b>Check 
	to Print Receipt:&nbsp;
</b></font> <input type="checkbox" name="Print_receipt" value="ON" checked></td>

  </tr>
  
    <tr>
    <td align="right" style="height: 23px" class="auto-style1">
	<font face="Arial" size="2">&nbsp;</font></td>

    <td colspan="2" style="height: 23px" class="auto-style2"> 
     </td>
  </tr>
  
  <tr>
    <td  align = right height="29" class="auto-style1" >
   <font face="Arial" size="2">Trailer:&nbsp;</font></td>
<td  align = left colspan="2" height="29" class="auto-style2">

      <input type="text" name="Trailer" size="15" value="<%= strTrailer %>" >&nbsp;
		<font face="Arial" size="2"><b>(Required)</b></font></td></tr>
  <tr>

      <td align = right height="28" class="auto-style1">
	<font face="Arial" size="2">Select Carrier: </font></td>
<td  align = left colspan="2" height="28" class="auto-style2">

      	<select name="Carrier" style="font-weight: 700">
 	<option value="" selected>  Select Carrier (Required)</option>
 	<% strsql = "Select Carrier,C_Description, Weight from tblCarrier order by Carrier"
 	   	 Set MyRec4 = Server.CreateObject("ADODB.Recordset")
   	 MyRec4.Open strSQL, Session("ConnectionString")
   	 while not MyRec4.eof %>
   	 <option <% if strCarrier = MyRec4("Carrier") then %> selected <% end if %> value="<%= MyRec4("Carrier") %>"><%= MyRec4("Carrier") %>&nbsp;<%= MyRec4("C_Description") %>&nbsp;<%= MyRec4("Weight") %></option>"

 <%MyRec4.Movenext
 wend
 MyRec4.close %>
     </select></td></tr>
<tr>
    <td class="auto-style1">  
	<p align="right">  <font face="Arial" size="2">&nbsp;</font></td>
    <td colspan="2" class="auto-style2">   &nbsp;</td>
  </tr>

<tr>
	<td height="22" width="17%" class="auto-style2" align="right" rowspan="2"><font face="Arial" size="2">
	<strong>Scale Gross</strong>:</font></td>
    <td height="22" class="auto-style2" rowspan="2"   >    <font face="Arial">  
	<input name="Gross" size="15" value="<%= strGrossWeight%>" ><span class="style1"> <strong>lbs</strong></span></td>
    <td height="22" class="auto-style2" style="height: 11">     <font face="Arial" size="2"   >   
 Scale out (Cab Only):&nbsp;
	<input name="C_Pounds" size="15" value = "<%= strCPounds%>"   >   lbs&nbsp;&nbsp;&nbsp;<span class="style43"> 
	(Trailer weight will be picked up from selected carrier)</span> OR </td>
  </tr>


<tr>
    <td height="22" class="auto-style2" style="height: 11">    
   <font face="Arial" size="2"   >   Scale out (Cab and Trailer): &nbsp; 
 
	<input name="CT_Pounds" size="15" value = "<%= strCTPounds%>"   >   lbs</td>
  </tr>
<tr>
	<td height="22" width="17%" class="auto-style2">
	&nbsp;</td>
    <td height="22" class="auto-style2" colspan="2">    <font face = arial size = 4 color="teal">
	<input type="checkbox" name="Axle" value="Y"  > <span class="auto-style5"> <span class="style18"> 
	<span class="style13"><span class="style8">Check here if total Truck &amp; Trailer did not fit on scale at same time  </span>
	</span></span></span></span>&nbsp;</td>
  </tr>

<tr>
	<td height="22" width="17%" class="auto-style2">&nbsp;</td>
    <td colspan="2" height="22" class="auto-style2">&nbsp;</td>
  </tr>

       <tr><td  align = right class="auto-style4" style="height: 47px" ><strong>
		Shipping Ticket Weight:</strong></td>
<td align = left colspan="2" class="auto-style2" style="height: 47px"> 
<input type="text" name="SAP_Weight" size="15" value = "<%= strSAPWeight %>" style="width: 136px"></td></tr>
  <tr>
	<td  align = right class="auto-style2" style="height: 32px" ><strong>
		<span class="style29">STO Delivery Number</span>:</strong></td>
<td align = left colspan="2" class="auto-style2" style="height: 32px"> 
<input type="text" name="Load" size="15" value = "<%= strLoad %>" style="width: 136px"></td>
</tr>
  <tr>
	<td height="22" width="17%" class="auto-style2">&nbsp;</td>
    <td colspan="2" height="22" class="auto-style2">&nbsp;</td>
  </tr>
           
       <tr><td  align = right class="auto-style1" style="height: 33px" ><font face="Arial" size="2">Date Received:&nbsp;</font></td>
<td align = left colspan="2" class="auto-style2" style="height: 33px"> 
<input type="text" name="Date_Received" size="15" value = <%= formatdatetime(Now(),2)%> style="width: 136px"></td></tr>
             
         <tr>
          <td  align = right class="auto-style1" >
  <font face="Arial" size="2">Comments:&nbsp;</font></td >
   <td align = left colspan="2" class="auto-style2">   
	<input type="text" name="Other_Comments" size="25" style="width: 387px" value="<%= strOther %>">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font face="Arial"><font size="2">&nbsp;Location: </font>   
	<select name="Location" style="font-weight: 700" size="1">

      <option selected>YARD</option>
 
     </select></font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	</td></tr>
	
	<% if stralert = "YES" then %>
<% end if %>

<tr>
    <td class="auto-style2">&nbsp;</td>

    <td colspan="2" class="auto-style2"> 
	<Input name="Update" type="submit" Value="Submit"  ></td>
  </tr>


</table>

</div>

</form>
</body>
<%  
Function SaveData()
dim strbody, strCID, strECarrier, strETrailer, strELoad, strEDoc, strEDC, strEDCName, strEAdd, strECity, strEState, MyConn2, strsql4
dim strECC, strEBCC
	
	if len(request.form("Tons_Received")) > 0 then
		
		strPounds = Request.form("Tons_received")
	strTonsReceived = round(strPounds/2000,3)



	strTrailerTID = 0
	strTrailerweight = 0
	strGrossWeight = 0
	strTareWeight = 0
    strNet = strTonsReceived
	end if 


	
	
  	strTrailerWeight = 14460

   	 strSQL3 = "Select weight from tblCarrier   where Carrier = '" & strCarrier & "'"

   	 Set MyConn3 = Server.CreateObject("ADODB.Recordset")
   	 MyConn3.Open strSQL3, Session("ConnectionString")
   	 if not MyConn3.eof then
   	 
   	 strTrailerWeight = MyConn3.fields("Weight")
   	 end if
   	 MyConn3.close
   	 
   	 if len(strTrailerweight) > 3 then
   	 ' do nothing
   	 else
   	 strTrailerWeight = 14460
   	 end if
   	 
   	 strTareWeight = 0
 strGrossWeight = Request.form("Gross")	 
 

If len(request.form("CT_Pounds")) > 2 then
strCTPounds = request.form("CT_Pounds")
	strGrossWeight = Request.form("Gross")

	strTonsReceived = round((strGrossWeight - strCTPounds)/2000,3)

	strPounds = round((strGrossWeight - strCTPounds),3)
		strTareWeight =  strCTPounds
 
	strNet = strTonsReceived

	elseif 	len(request.form("C_Pounds")) > 2 then
	strCPounds = Request.form("C_Pounds")
	
   	 
	strTonsReceived = round((Request.form("Gross") - strCPounds - strTrailerweight)/2000,3)
	strPounds = round((Request.form("Gross") - strCpounds - strTrailerweight),3)
		strTareWeight =  strTrailerweight + strCpounds
	strGrossWeight = Request.form("Gross")
	strNet = strTonsReceived
	 end if
	 
 
 


	
	
	Dim strRightNow
	strRightnow = now()
	
	strSapWeight = request.form("SAP_Weight")
	strLoad = request.form("Load")
	if len(strLoad) > 0 then
	'do nothing
	else
	strLoad = 0
	end if
	
	
strsql =  "INSERT INTO tblCars ( Trailer_weight, Trailer_TID,    Carrier, Species, Grade,    Date_received,  Location,  Trailer, Other_Comments, "_
&"  Tons_Received, Net, Gross_weight, Tare_weight,  Entry_Time, Entry_BID, Entry_Page, Status, STO_Number, Tons ) "_
	&" SELECT  " & strTrailerWeight & ", 0,  '" & strCarrier & "', 'UNRESOLVED', 'UNRESOLVED', "_
	&"   '" & strDateReceived & "',  'YARD',  "_
	&" '" & strTrailer & "', '" & strOther & "',  " & strSapWeight & ", " & strSapWeight & ", " & strGrossWeight & ", "_
	&"  " & strTareWeight & ", '" & strRightNow & "', '" & Session("EmployeeID") & "', 'Generic_STO_receipt', " & strSapWeight & ", " & strLoad & ", " & strNet & ""
	

	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
				
				strsql5 = "SELECT Max(tblCars.CID) AS MaxOfCID FROM tblCars WHERE tblCars.Trailer = '" & strTrailer & "'"
			
   	 Set MyRec5 = Server.CreateObject("ADODB.Recordset")
   	 MyRec5.Open strSQL5, Session("ConnectionString")
   	 strcarID = MyRec5.fields("MaxofCID")
   	 MyRec5.close
   	 
   	 
   	 


			
			strEmailTo = "<EMAIL>,  <EMAIL>"
        	 
            strEBCC = "<EMAIL>"
                 
			Set objMail = Server.CreateObject("CDO.Message")		
			objMail.From = "<EMAIL>"
			objMail.To = strEBCC
		
			
			objMail.Subject = "STO Truck Receipt for Trailer#: " & strTrailer & " Entered as an Exception "		
			
		
			objMail.HTMLBody = "<font face = arial size = 2>Truck # " & strTrailer & " was entered as an exception. "
			' objMail.Send
			Set objMail = nothing


		If Request.form("Print_receipt") =  "ON" then
		
	
	
		Response.redirect("STO_Truck_receipt.asp?id=" & strCarID & "&p=" & strpounds)
			

	
		else
		Response.redirect ("SelectSTO.asp")
		end if

End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->