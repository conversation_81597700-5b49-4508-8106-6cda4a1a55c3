

<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Inventory Reduction Report</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

<%  
	Dim strsQL, rstEquip

   	Dim objEquipSearch, rstTotals, strCount, strDed, strNet, strTons, rstTotalReceived, strTR, objNew, rstSpecies
  	Dim  strVendor, strBegDate, strEndDate, strSpecies, strDateReceived, strDateAdded, strAVG
 	Dim  rstVendor, strFIQ, strBAQ, rstGenerator, strGenerator, strVendorname, strGeneratorname
  	Dim objGeneral, gcount, strCountTwo, strSum, strSum1, strNetSum, strDepdate, strEdate, strBdate, strBegTime, strEndTime
   	
   	Dim intDirection
  	Dim intPageNumber
   	Dim strPageNav

   intPageNumber = 1

   set objGeneral = new ASP_CLS_General

  if objGeneral.IsSubmit() Then
    Call GetFormData()
    Select Case intDirection
      Case 10: 'goto mocinfo.asp
  
      Case Else:
        Call LoadSearchResults()
    End Select
  else
    intDirection = 0
  end if
Call GetData()
%>

<script language="javascript">
 
  function GotoMOC(MOCID)
  {
    document.forms["form1"].elements["tbl_ID"].value = MOCID;
    document.forms["form1"].elements["Direction"].value = 10;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }

  function Search()
  {
    
    document.forms["form1"].elements["Direction"].value = 1;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }

  function NextPage()
  {
    document.forms["form1"].elements["Direction"].value = 5;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }
  function PrevPage()
  {
    document.forms["form1"].elements["Direction"].value = 6;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }
</script>


<style type="text/css">
.style1 {
	font-size: x-small;
}
.style2 {
	border-width: 1px;
}
.style3 {
	font-weight: bold;
	border-width: 1px;
}
.auto-style1 {
	text-align: left;
}
</style>
</head>


<form name="form1" action="Inventory_reduction_rpt.asp" method="post">
 <input type="hidden" name="Direction" value="1" >
 <input type="hidden" name="tbl_ID" value="" >
 <input type="hidden" name=PageNumber value="<%=intPageNumber%>" >
 
<TABLE borderColor=white cellSpacing=0 cellPadding=0  width = 100% border=1 >  
  <tr>
  <td  align = left><b><font face = "Arial">Inventory Reduction Report</font></b></td>
	<td align = right><font face="Arial"><b><a href="Loginscreen.asp">HOME</a></b></font></td></tr>
</table>

 

<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class="tablecolor1" border=1 align = CENTER id="table2">  

  <TR>
   
	<TD class="style3" ><font face="Arial" size="2">Beg Date:</font></TD>
<TD class="style3" ><font face="Arial"><input name="Beg_Date" size="10" maxlength="10" value="<%=strBDate%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633">
<font face="Arial" size="2">	&nbsp;<strong> Time: </strong></span>&nbsp;
<select size="1" name="Beg_time">
<option <% if strBegTime = "1:00:00 AM" then %> selected <% end if %> >1:00:00 AM</option>
<option <% if strBegTime = "2:00 AM" then %> selected <% end if %> >2:00 AM</option>
<option <% if strBegTime = "3:00 AM" then %> selected <% end if %> >3:00 AM</option>
<option <% if strBegTime = "4:00 AM" then %> selected <% end if %> >4:00 AM</option>
<option <% if strBegTime = "5:00 AM" then %> selected <% end if %> >5:00 AM</option>
<option <% if strBegTime = "6:00 AM" then %> selected <% end if %> >6:00 AM</option>
<option <% if strBegTime = "7:00 AM" then %> selected <% end if %> >7:00 AM</option>
<option <% if strBegTime = "8:00 AM" then %> selected <% end if %> >8:00 AM</option>
<option <% if strBegTime = "9:00 AM" then %> selected <% end if %> >9:00 AM</option>
<option <% if strBegTime = "10:00 AM" then %> selected <% end if %> >10:00 AM</option>
<option <% if strBegTime = "11:00 AM" then %> selected <% end if %> >11:00 AM</option>
<option <% if strBegTime = "12:00 PM" then %> selected <% end if %> >12:00 PM</option>
<option <% if strBegTime = "1:00 PM" then %> selected <% end if %> >1:00 PM</option>
<option <% if strBegTime = "2:00 PM" then %> selected <% end if %> >2:00 PM</option>
<option <% if strBegTime = "3:00 PM" then  %> selected <% end if %> >3:00 PM</option>
<option <% if strBegTime = "4:00 PM" then %> selected <% end if %> >4:00 PM</option>
<option <% if strBegTime = "5:00 PM" then  %> selected <% end if %> >5:00 PM</option>
<option <% if strBegTime = "6:00 PM" then  %> selected <% end if %> >6:00 PM</option>
<option <% if strBegTime = "7:00 PM" then  %> selected <% end if %> >7:00 PM</option>
<option <% if strBegTime = "8:00 PM" then%> selected <% end if %> >8:00 PM</option>
<option <% if strBegTime = "9:00 PM" then%> selected <% end if %> >9:00 PM</option>
<option <% if strBegTime = "10:00 PM" then %> selected <% end if %> >10:00 PM</option>
<option <% if strBegTime = "11:00 PM" then %> selected <% end if %> >11:00 PM</option>
<option <% if strBegTime = "12:00 AM" then %> selected <% end if %> >12:00 AM</option>

     </select></TD>



     <td class="style3"><font size="2" face="Arial">&nbsp; End Date</font></td>
   <TD class="style3" ><font face="Arial"><input name="End_Date" size="10" maxlength="10" value="<%=strEDate%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633">&nbsp;&nbsp;&nbsp;
		<strong><span class="style1">Time</span></strong>:&nbsp;   
		<select size="1" name="End_time">
<option <% if strEndTime = "1:00:00 AM" then %> selected <% end if %> >1:00:00 AM</option>
<option <% if strEndTime = "2:00 AM" then %> selected <% end if %> >2:00 AM</option>
<option <% if strEndTime = "3:00 AM" then %> selected <% end if %> >3:00 AM</option>
<option <% if strEndTime = "4:00 AM" then %> selected <% end if %> >4:00 AM</option>
<option <% if strEndTime = "5:00 AM" then %> selected <% end if %> >5:00 AM</option>
<option <% if strEndTime = "6:00 AM" then %> selected <% end if %> >6:00 AM</option>
<option <% if strEndTime = "7:00 AM" then %> selected <% end if %> >7:00 AM</option>
<option <% if strEndTime = "8:00 AM" then %> selected <% end if %> >8:00 AM</option>
<option <% if strEndTime = "9:00 AM" then %> selected <% end if %> >9:00 AM</option>
<option <% if strEndTime = "10:00 AM" then %> selected <% end if %> >10:00 AM</option>
<option <% if strEndTime = "11:00 AM" then %> selected <% end if %> >11:00 AM</option>
<option <% if strEndTime = "12:00 PM" then %> selected <% end if %> >12:00 PM</option>
<option <% if strEndTime = "1:00 PM" then %> selected <% end if %> >1:00 PM</option>
<option <% if strEndTime = "2:00 PM" then %> selected <% end if %> >2:00 PM</option>
<option <% if strEndTime = "3:00 PM" then  %> selected <% end if %> >3:00 PM</option>
<option <% if strEndTime = "4:00 PM" then %> selected <% end if %> >4:00 PM</option>
<option <% if strEndTime = "5:00 PM" then  %> selected <% end if %> >5:00 PM</option>
<option <% if strEndTime = "6:00 PM" then  %> selected <% end if %> >6:00 PM</option>
<option <% if strEndTime = "7:00 PM" then  %> selected <% end if %> >7:00 PM</option>
<option <% if strEndTime = "8:00 PM" then%> selected <% end if %> >8:00 PM</option>
<option <% if strEndTime = "9:00 PM" then%> selected <% end if %> >9:00 PM</option>
<option <% if strEndTime = "10:00 PM" then %> selected <% end if %> >10:00 PM</option>
<option <% if strEndTime = "11:00 PM" then %> selected <% end if %> >11:00 PM</option>
<option <% if strEndTime = "12:00 AM" then %> selected <% end if %> >12:00 AM</option>
     </select></td>

		<TD><b><font face="Arial" size="2">Species:</font></b></TD>
    <TD>  <font face="Arial">   <select size="1" name="Species">
     <option value="">--- All ---</option>
       <%= objGeneral.OptionListAsString(rstSpecies, "Species", "Species", strSpecies) %>
     </select></td>
</TR>

	</table>
<table width = 100% bgcolor = white><tr>

  
    <TD  align="right"><input type="button" onClick="javascript:Search()" value="Search" caption="Search"></TD>
 
</TR>


  </TABLE></form>


  <% if objGeneral.IsSubmit() Then 

%>
   <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>
    <tr><td colspan="15" bgcolor="white" ><font face="Arial" size="2"><b>Search Results</b> - Page <%=intPageNumber%></font></td>
       
<% if intPageNumber = 1 then
 %>

 <td bgcolor = white align="center" ><font face="Arial" size="2"><b>
 <a href="Inventory_reduction_rptExcel.asp?b=<%= strBegDate %>&e=<%= strEndDate %>&s=<%= strSpecies %>">Excel</a>&nbsp;</font></td>

 <td bgcolor = white align="center" ><font face="Arial" size="2"><b>Count<br><%= strCount%>&nbsp;</font></td>
        <td bgcolor = white  align="center" ><font face="Arial" size = 2>
        <% if len(strSpecies) > 2 then %>
        <b>Total Net for <%= strSpecies %>
        <% else %>
        
        <b>Total Net
        <% end if %>
        <br><%= strNetSum%>&nbsp;</b></font></b></td>


    <% end if %>
    </tr>
    <tr>
  
    <%   DIm MyConn
    If len(strSpecies) > 2 then
    ' do nothing
    else
    
    strsql = "SELECT Count(tblCars.CID) AS CountOfCID, Sum(tblCars.Net) AS SumOfNet, tblCars.Species FROM tblCars "_
	&" WHERE Inv_depletion_date > '" & strBegDate & "' and Inv_depletion_date  <= '" & strEndDate & "' "_
	&" GROUP BY tblCars.Species "_
	&" ORDER BY tblCars.Species"
	
	   Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
    IF not MyConn.eof  then
    while not MyConn.eof %>
 <td align="left"> <font face="Arial" size="1"> <%= MyConn("Species") %>: <%= MyConn("SumofNet") %></td>
  <td colspan="17" align="left" > <% if strNetSum > 0 then %>
 <font face="Arial" size="1"> <%= round((MyConn("SumofNet")/strNetSum) * 100,0) %>
  <% else %>
 <font face="Arial" size="1"> 0
  <% end if %>%</td></tr>
    <% MyConn.movenext
    wend
    end if
    MyConn.close 
    end if
    %>
    

</td></tr>
    <tr><td colspan="17" bgcolor="white" align="right" ><%=strPageNav%>&nbsp;</td></tr>
      <tr class="tableheader">	
      <td class="auto-style1" ><font face="Arial" size="1">Species</font></td>
 
	<td  align="lefr" ><font face="Arial" size="1">Vendor</font></td>
	<td  align="left" ><font face="Arial" size="1">Generator</font></td>
		<td  align="left" style="width: 45px" ><font face="Arial" size="1">City</font></td>
	<td  align="center" style="width: 31px" ><font face="Arial" size="1">PO</font></td>
		<td  align="center" style="width: 31px" ><font face="Arial" size="1">Release</font></td>

		<td  align="center" ><font face="Arial" size="1">Rec Nbr</font></td>
		<td  align="center" ><font face="Arial" size = 1>PMO</font></td>
	<td  align="center" ><font face="Arial" size = 1>Trailer</font></td>
	<td  align="center" ><font face="Arial" size = 1>Transfer Trailer</font></td>
	<td  align="center" ><font face="Arial" size="1">Transfer Date</font></td>
	<td  align="center" ><font face="Arial" size="1">Trans Unload</font></td>
	<td  align="center" ><font face="Arial" size="1">Inv Depletion<br>Date</font></td>
	<td  align="center" ><font face="Arial" size="1">Location</font></td>

	<td  align="center" ><font face="Arial" size="1">Net</font></td>
	<td  align="center" ><font  face="Arial" size="1">Org Bales</font></td>

	<td  align="center" ><font  face="Arial" size="1">Other</font></td>
	<td  align="center" ><font  face="Arial" size="1">Count</font></td>
      
  	<%   strcount = 1
      Dim ii
       ii = 0
       while not rstEquip.Eof
      
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr class=tablecolor1>
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    <%    
   if formatdatetime(rstEquip.fields("Inv_depletion_date"),2) <> strDepdate then
       strcount = 1
       end if 
       strVendor = rstEquip("Vendor")
       strGenerator = rstEquip("Generator")
       strCity = rstEquip("Gen_City")
       strState = rstEquip("Gen_State")
       strGrade = Rstequip("Species") %> 

<td  align="left" ><font face = "arial" size = "1"><%=rstEquip.fields("Species")%>&nbsp;</td>

     <%   strsql2 = "SELECT Tier from tblTier where State = '" & strState & "' "_
     &" and City = '" & strCity & "' and Generator = '" & strGenerator & "' "_
     &" and Vendor = '" & strVendor & "' and Grade = '" & strGrade & "' "
 
         Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL2, Session("ConnectionString")
    If not MyRec2.eof then

    strTier = MyRec2("Tier")
    else
    strTier = ""
    
    
    end if
    MyRec2.close %>


 
<td  align="left" ><font face = "arial" size = "1"><%=rstEquip.fields("Vendor")%>&nbsp;</td>
<td  align="left" ><font face = "arial" size = "1"><%=rstEquip.fields("Generator")%>&nbsp;</td>
<td  align="left" style="width: 45px" ><font face = "arial" size = "1"><%=rstEquip.fields("Gen_city")%>&nbsp;</td>

<td  align="left" style="width: 31px" ><font face = "arial" size = "1"><%=rstEquip.fields("PO")%>&nbsp;</td>

<td  align="left" style="width: 31px" ><font face = "arial" size = "1"><%=rstEquip.fields("Release_nbr")%>&nbsp;</td>
<td  align="center" ><font face = "arial" size = "1"><%=rstEquip.fields("PMO_Nbr")%>&nbsp;</td>
<td  align="center" ><font face = "arial" size = "1"><%=rstEquip.fields("Pnbr")%>&nbsp;</td>
<td  align="center" ><font face = "arial" size = "1"><%=rstEquip.fields("Trailer")%>&nbsp;</td>

<td  align="center" ><font face = "arial" size = "1"><%=rstEquip.fields("Transfer_Trailer_nbr")%>&nbsp;</td>

<td  align="left" ><font face = "arial" size = "1"><%=rstEquip.fields("Transfer_date")%>&nbsp;</td>
<td  align="center" ><font face = "arial" size = "1"><%=rstEquip.fields("Trans_unload_date")%>&nbsp;</td>
<td  align="center" ><font face = "arial" size = "1"><%=rstEquip.fields("Inv_depletion_date")%>&nbsp;</td>
<td  align="center" ><font face = "arial" size = "1"><%=rstEquip.fields("Location")%>&nbsp;</td>
<% if not isnull(rstEquip.fields("Net")) then %>
<td  align="center" ><font face = "arial" size = "1"><%= formatnumber(rstEquip.fields("net"),3)%>&nbsp;</td>
<% else %>
<td  align="center" ><font face = "arial" size = "1">&nbsp;</td>
<% end if %>

<td  align="center" ><font face = "arial" size = "1"><%= rstEquip.fields("Bales_rf")%>&nbsp;</td>



<td  align="center" ><font face = "arial" size = "1"><%= rstEquip.fields("Other_Comments")%>&nbsp;</td>
<td  align="center" ><font face = "arial" size = "1"><%= strCount%>&nbsp;</td>


 </tr>
    <% 
       ii = ii + 1
          strDepDate = formatdatetime(rstEquip.fields("Inv_depletion_date"),2)
           strcount = strcount + 1
       rstEquip.MoveNext
     Wend
    %>
   </table>
<table>    <tr><td colspan="16" bgcolor="white" align="right"><%=strPageNav%>&nbsp;</td></tr></table>
<% end if %><% Function GetData()

   set objNew = new ASP_Cls_Fiber
          
	set rstVendor = objNew.FiberVendor()
		set rstSpecies = objNew.FiberSpecies()

set rstGenerator= objNew.FiberGenerator()


End Function
    Function GetFormData()
      intDirection = cint(Request.Form("Direction")) 
       
	
	strVendor = ""
	strGenerator = ""
	strBegDate = Request.form("Beg_date") & " " & request.form("Beg_time")
	strBDate = Request.form("Beg_date")
	strEndDate = Request.form("End_date")& " " & request.form("End_time")
	strEdate = Request.form("End_date")
	strSpecies = Request.form("Species")
	Session("Species") = strSpecies
	Session("Beg_Date") = strBegDate
	Session("End_date") = strEndDate
	strBegTime = request.form("Beg_time")
	strEndTime = request.form("End_time")


      intPageNumber = cint(Request.Form("PageNumber"))

 
    End Function

    Function LoadSearchResults()
      Dim objEquipSearch, objTotals

      if intDirection =1 Then
         intPageNumber = 1
      end if

      if intDirection = 5 Then
         intPageNumber = intPageNumber + 1
      end if
      if intDirection = 6 Then
         intPageNumber = intPageNumber - 1
      end if


	
	strSpecies = Request.form("Species")
      set objEquipSearch = new ASP_Cls_Fiber
    

 

If strSpecies = "OCC" then
 set rstEquip = objEquipSearch.IRSearchOCCR1(50, intPageNumber, strBegDate, strEndDate)
set rstTotals = objEquipSearch.IRSearch_totalsOCCR1(intPageNumber, strBegDate, strEndDate)


else
set rstEquip = objEquipSearch.IRSearchRev(50, intPageNumber, strBegDate, strEndDate, strSpecies)
set rstTotals = objEquipSearch.IRSearch_totalsR1(intPageNumber, strBegDate, strEndDate,  strSpecies)
end if

     if  not rstTotals.Eof Then
if not isnull(rstTotals.fields("SumofNet")) then   
  strNetSum = round(rstTotals.fields("SumofNet"),3)
     
      strCount = rstTotals.fields("CountofCID").value
end if
      
	else
	strCount = 0
	strNetSum = 0

      
      end if 

     if ( not rstEquip.Eof) Then
      if ( intPageNumber < rstEquip.fields("TotalPage").value ) Then
        strPageNav = "<a href=javascript:NextPage()><B>Next Page</b></a>"
      end if
      if ( intPageNumber > 1 ) Then
         strPageNav = "<a href=javascript:PrevPage()><b>Previous Page</b></a>&nbsp;&nbsp;&nbsp;&nbsp;" & strPageNav
      end if
     end if

       ' if ( not rstTotalReceived.Eof) Then
    ' strTR = rstTotalReceived.fields("countofoid").value
    ' end if
    End Function
 %><!--#include file="Fiberfooter.inc"-->