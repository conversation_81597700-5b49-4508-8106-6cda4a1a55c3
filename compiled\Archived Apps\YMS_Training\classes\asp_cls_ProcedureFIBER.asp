﻿<%
Class ASP_CLS_FIBER

 Public Function IRSearchRev(NumPerPage, PageNumber, BegDate, EndDate, Species)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2012#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if
    set IRSearchRev = objDAC.ExecuteSp("asp_cls_Inv_reduction_Rev1", array(NumPerPage, PageNumber, BegDate, EndDate, Species))
    set objDAC = nothing
 
 End Function

     Public Function IRSearchExcelR1(BegDate, EndDate, Species)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2012#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set IRSearchExcelR1 = objDAC.ExecuteSp("asp_cls_Inv_reductionExcel_Rev1", array( BegDate, EndDate,  Species))
    set objDAC = nothing
 
 End Function

      Public Function IRSearchOCCR1( NumPerPage, PageNumber, BegDate, EndDate)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2012#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set IRSearchOCCR1 = objDAC.ExecuteSp("asp_cls_Inv_reduction_OCC_rev1", array(NumPerPage, PageNumber, BegDate, EndDate))
    set objDAC = nothing
 
 End Function

Public Function IRSearchExcelOCCR1(BegDate, EndDate)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2012#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set IRSearchExcelOCCR1 = objDAC.ExecuteSp("asp_cls_Inv_reductionExcel_OCC_Rev1", array( BegDate, EndDate))
    set objDAC = nothing
 
 End Function

Public Function IRSearch_totalsR1( PageNumber, BegDate, EndDate,  Species)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2012#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set IRSearch_totalsR1 = objDAC.ExecuteSp("asp_cls_Inv_Reduction_totals_rev1", array( 50, PageNumber, BegDate, EndDate, Species))
    set objDAC = nothing
 
 End Function

 Public Function IRSearch_totalsOCCR1(PageNumber, BegDate, EndDate)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2012#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set IRSearch_totalsOCCR1 = objDAC.ExecuteSp("asp_cls_Inv_Reduction_totalsOCC_rev1", array( 50, PageNumber, BegDate, EndDate))
    set objDAC = nothing
 
 End Function

   Public Function TrailerOptionsAuditLight()
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set TrailerOptionsAuditLight = objDAC.ExecuteSp("asp_sp_TrailerOptions_audit_light", array())
    set objDAC = nothing

 End Function

    Public Function IRSearchExcel(BegDate, EndDate, Species)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2011#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set IRSearchExcel = objDAC.ExecuteSp("asp_cls_Inv_reductionExcel", array( BegDate, EndDate,  Species))
    set objDAC = nothing
 
 End Function
 
     Public Function IRSearchExcelOCC(BegDate, EndDate)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2011#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set IRSearchExcelOCC = objDAC.ExecuteSp("asp_cls_Inv_reductionExcel_OCC", array( BegDate, EndDate))
    set objDAC = nothing
 
 End Function



   Public Function FiveDayTotals()
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess

    set FiveDayTotals = objDAC.ExecuteSp("asp_cls_Inv_consumption", array())
    set objDAC = nothing
 
 End Function


  Public Function FiberOasis(Adate)
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set FiberOasis= objDAC.ExecuteSp("asp_sp_Oasis", array(Adate))
    set objDAC = nothing

 End Function
 
   Public Function FiberOnYard()
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set FiberOnYard= objDAC.ExecuteSp("asp_sp_OnYard", array())
    set objDAC = nothing

 End Function

  Public Function FiberSapImport()
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set FiberSAPImport= objDAC.ExecuteSp("asp_sp_SAP_Import", array())
    set objDAC = nothing

 End Function

 Public Function FiberTruckUnload()
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set FiberTruckUnload = objDAC.ExecuteSp("asp_sp_Unload", array())
    set objDAC = nothing

 End Function
 
 Public Function VFVendor()
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set VFVendor = objDAC.ExecuteSp("asp_sp_VFVendor", array())
    set objDAC = nothing

 End Function

 Public Function FiberTruckUnloadSpecies(Species)
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set FiberTruckUnloadSpecies = objDAC.ExecuteSp("asp_sp_UnloadSpecies", array(Species))
    set objDAC = nothing

 End Function
 
  Public Function FiberTruckUnloadTransOCC
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set FiberTruckUnloadTransOCC = objDAC.ExecuteSp("asp_sp_Unload_TransOCC", array())
    set objDAC = nothing

 End Function

 
  Public Function FiberTruckUnloadKCOP()
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set FiberTruckUnloadKCOP = objDAC.ExecuteSp("asp_sp_UnloadKCOP", array())
    set objDAC = nothing

 End Function
 
  Public Function FiberTruckUnloadTransKCOP()
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set FiberTruckUnloadTransKCOP = objDAC.ExecuteSp("asp_sp_Unload_TransKCOP", array())
    set objDAC = nothing

 End Function

 
   Public Function FiberTruckUnloadOther()
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set FiberTruckUnloadOther = objDAC.ExecuteSp("asp_sp_UnloadOther", array())
    set objDAC = nothing

 End Function
 
  
   Public Function FiberTruckUnloadTransOther()
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set FiberTruckUnloadTransOther = objDAC.ExecuteSp("asp_sp_Unload_TransOther", array())
    set objDAC = nothing

 End Function

 
  Public Function NFTrailers()
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set NFTrailers = objDAC.ExecuteSp("asp_sp_NFUnload", array())
    set objDAC = nothing

 End Function
 
   Public Function NFCommodity()
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set NFCommodity = objDAC.ExecuteSp("asp_sp_NFCommodity", array())
    set objDAC = nothing

 End Function
 
  Public Function FiberTruckUnloadTrans()
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set FiberTruckUnloadTrans = objDAC.ExecuteSp("asp_sp_Unload_trans", array())
    set objDAC = nothing

 End Function
 
 Public Function FiberRelease()
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set FiberRelease = objDAC.ExecuteSp("asp_sp_Release", array())
    set objDAC = nothing

 End Function
 
  Public Function FiberNFRelease()
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set FiberNFRelease = objDAC.ExecuteSp("asp_sp_NFRelease", array())
    set objDAC = nothing

 End Function
 
  
  Public Function FiberVFRelease()
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set FiberVFRelease = objDAC.ExecuteSp("asp_sp_VFRelease", array())
    set objDAC = nothing

 End Function

 Public Function FiberWeyRelease()
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set FiberWeyRelease = objDAC.ExecuteSp("asp_sp_WeyRelease", array())
    set objDAC = nothing

 End Function

Public Function FiberPO()
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set FiberPO = objDAC.ExecuteSp("asp_sp_PO", array())
    set objDAC = nothing

 End Function
 
 Public Function FiberCommodity()
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set FiberCommodity = objDAC.ExecuteSp("asp_sp_Commodity", array())
    set objDAC = nothing

 End Function

 Public Function FiberSpecies()
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set FiberSpecies = objDAC.ExecuteSp("asp_sp_Species", array())
    set objDAC = nothing

 End Function
 
  Public Function FiberVendor()
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set FiberVendor = objDAC.ExecuteSp("asp_sp_Vendor", array())
    set objDAC = nothing

 End Function
 
   Public Function YtoB()
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set YtoB = objDAC.ExecuteSp("asp_sp_YtoB", array())
    set objDAC = nothing

 End Function
 
   Public Function FiberGenerator()
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set FiberGenerator = objDAC.ExecuteSp("asp_sp_Generator", array())
    set objDAC = nothing

 End Function
 
  Public Function FiberCarrier()
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set FiberCarrier = objDAC.ExecuteSp("asp_sp_Carrier", array())
    set objDAC = nothing

 End Function
 
  Public Function TrailerOptions()
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set TrailerOptions = objDAC.ExecuteSp("asp_sp_TrailerOptions", array())
    set objDAC = nothing

 End Function

  Public Function TrailerOptionsAudit()
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set TrailerOptionsAudit = objDAC.ExecuteSp("asp_sp_TrailerOptions_audit", array())
    set objDAC = nothing

 End Function
 
  Public Function FiberMonth()
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set FiberMonth = objDAC.ExecuteSp("asp_sp_Month", array())
    set objDAC = nothing

 End Function
 
   Public Function VFSpecies()
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess
    

    set VFSpecies= objDAC.ExecuteSp("asp_sp_VFSpecies", array())
    set objDAC = nothing

 End Function


 Public Function GradeSearch(PageNumber, PO, Vendor, Release, Date_from, Date_to)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If Date_from = "" then
  Date_from = #01/01/2009#
  end if
  If Date_to = "" then
  Date_to = dateadd("d", 1, Date())
  End if


    set GradeSearch = objDAC.ExecuteSp("asp_cls_GradeSearch", array( PageNumber, PO, Vendor, Release, Date_from, Date_to))
    set objDAC = nothing
 
 End Function



 Public Function RPSearch(Import_month, PageNumber,  Species, PO, Vendor)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess


    set RPSearch = objDAC.ExecuteSp("asp_cls_RP", array( 50, PageNumber, Import_month, Species, PO, Vendor))
    set objDAC = nothing
 
 End Function

 Public Function RPSearchSW(Import_month, PageNumber,  Species, PO, Vendor, Req_Ship_week)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess


    set RPSearchSW = objDAC.ExecuteSp("asp_cls_RPSW", array( 50, PageNumber, Import_month, Species, PO, Vendor, Req_ship_week))
    set objDAC = nothing
 
 End Function
 
  Public Function NFSearch(PS, PageNumber,  AT, SType)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess


    set NFSearch = objDAC.ExecuteSp("asp_cls_NFSearch", array( 50, PageNumber, PS, AT, Stype))
    set objDAC = nothing
 
 End Function

  Public Function AllSearch(PS, PageNumber,  AT, SType)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess


    set AllSearch = objDAC.ExecuteSp("asp_cls_AllSearch", array( 50, PageNumber, PS, AT, Stype))
    set objDAC = nothing
 
 End Function
 
   Public Function NFHistSearch(Trailer, PageNumber)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess


    set NFHistSearch = objDAC.ExecuteSp("asp_cls_NFHistSearch", array( 50, PageNumber, Trailer))
    set objDAC = nothing
 
 End Function
 
    Public Function HistSearch(Trailer, PageNumber)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess


    set HistSearch = objDAC.ExecuteSp("asp_cls_Trailer_history", array( 50, PageNumber, Trailer))
    set objDAC = nothing
 
 End Function
 
  Public Function VRSearch( NumPerPage, PageNumber, BegDate, EndDate, Generator, Species, Vendor)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2007#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set VRSearch = objDAC.ExecuteSp("asp_cls_VR", array(NumPerPage, PageNumber, BegDate, EndDate, Generator, Species, Vendor))
    set objDAC = nothing
 
 End Function
 
   Public Function VRSearch_totals( PageNumber, BegDate, EndDate, Generator, Species, Vendor)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2007#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set VRSearch_totals = objDAC.ExecuteSp("asp_cls_VR_totals", array( 50, PageNumber, BegDate, EndDate, Generator, Species, Vendor))
    set objDAC = nothing
 
 End Function
 
   Public Function IRSearch( NumPerPage, PageNumber, BegDate, EndDate, Generator, Species, Vendor)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2007#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set IRSearch = objDAC.ExecuteSp("asp_cls_Inv_reduction", array(NumPerPage, PageNumber, BegDate, EndDate, Generator, Species, Vendor))
    set objDAC = nothing
 
 End Function
 
    Public Function IRSearchOCC( NumPerPage, PageNumber, BegDate, EndDate, Generator,  Vendor)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2015#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set IRSearchOCC = objDAC.ExecuteSp("asp_cls_Inv_reduction_OCC", array(NumPerPage, PageNumber, BegDate, EndDate, Generator,  Vendor))
    set objDAC = nothing
 
 End Function


Public Function DCSearch( NumPerPage, PageNumber, BegDate, EndDate, Species, Location)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2015#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set DCSearch = objDAC.ExecuteSp("asp_cls_DC", array(NumPerPage, PageNumber, BegDate, EndDate,  Species, Location))
    set objDAC = nothing
 
 End Function

Public Function DCSearchTo( NumPerPage, PageNumber, BegDate, EndDate, Species, Location)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2009#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set DCSearchTo = objDAC.ExecuteSp("asp_cls_DC_to", array(NumPerPage, PageNumber, BegDate, EndDate,  Species, Location))
    set objDAC = nothing
 
 End Function

   Public Function IRSearch_totals( PageNumber, BegDate, EndDate,  Generator, Species, Vendor)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2008#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set IRSearch_totals = objDAC.ExecuteSp("asp_cls_Inv_Reduction_totals", array( 50, PageNumber, BegDate, EndDate, Generator, Species, Vendor))
    set objDAC = nothing
 
 End Function
 
    Public Function IRSearch_totalsOCC( PageNumber, BegDate, EndDate,  Generator, Vendor)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2008#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set IRSearch_totalsOCC = objDAC.ExecuteSp("asp_cls_Inv_Reduction_totals_OCC", array( 50, PageNumber, BegDate, EndDate, Generator,  Vendor))
    set objDAC = nothing
 
 End Function

 
   Public Function DCSearch_totals( PageNumber, BegDate, EndDate,  Species, Location)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2009#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set DCSearch_totals = objDAC.ExecuteSp("asp_cls_DC_totals", array( 50, PageNumber, BegDate, EndDate, Species, Location))
    set objDAC = nothing
 
 End Function

   Public Function DCSearch_totalsTo( PageNumber, BegDate, EndDate,  Species, Location)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2009#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set DCSearch_totalsTo = objDAC.ExecuteSp("asp_cls_DC_totals_to", array( 50, PageNumber, BegDate, EndDate, Species, Location))
    set objDAC = nothing
 
 End Function
 
   Public Function VREVSearch( NumPerPage, PageNumber, BegDate, EndDate, Generator, Species, Vendor)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2007#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set VREVSearch = objDAC.ExecuteSp("asp_cls_VR_EV", array( NumPerpage, PageNumber, BegDate, EndDate, Generator, Species, Vendor))
    set objDAC = nothing
 
 End Function
 
    Public Function VREVSearch_totals( PageNumber, BegDate, EndDate, Generator, Species, Vendor)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2007#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set VREVSearch_totals = objDAC.ExecuteSp("asp_cls_VR_EV_totals", array( 50, PageNumber, BegDate, EndDate, Generator, Species, Vendor))
    set objDAC = nothing
 
 End Function
 
   Public Function VREGSearch( NumPerpage, PageNumber, BegDate, EndDate, Generator, Species, Vendor)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2007#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set VREGSearch = objDAC.ExecuteSp("asp_cls_VR_EG", array( NumPerPage, PageNumber, BegDate, EndDate, Generator, Species, Vendor))
    set objDAC = nothing
 
 End Function
 
    Public Function VREGSearch_totals( PageNumber, BegDate, EndDate, Generator, Species, Vendor)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2007#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set VREGSearch_totals = objDAC.ExecuteSp("asp_cls_VR_EG_totals", array( 50, PageNumber, BegDate, EndDate, Generator, Species, Vendor))
    set objDAC = nothing
 
 End Function
 
    Public Function VREVGSearch( NumperPage, PageNumber, BegDate, EndDate, Generator, Species, Vendor)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2007#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set VREVGSearch = objDAC.ExecuteSp("asp_cls_VR_EVG", array( NumPerPage, PageNumber, BegDate, EndDate, Generator, Species, Vendor))
    set objDAC = nothing
 
 End Function
 
     Public Function VREVGSearch_totals( PageNumber, BegDate, EndDate, Generator, Species, Vendor)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2007#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set VREVGSearch_totals = objDAC.ExecuteSp("asp_cls_VR_EVG_totals", array( 50, PageNumber, BegDate, EndDate, Generator, Species, Vendor))
    set objDAC = nothing
 
 End Function
 
  Public Function RPSearchTA(BegDate, PageNumber,  EndDate, strSpecies)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2007#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set RPSearchTA = objDAC.ExecuteSp("asp_cls_RPTA", array( 50, PageNumber, BegDate, EndDate, strSpecies))
    set objDAC = nothing
 
 End Function

  Public Function RPSearchTANull(BegDate, PageNumber,  EndDate, strSpecies)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2007#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set RPSearchTANull = objDAC.ExecuteSp("asp_cls_RPTANull", array( 50, PageNumber, BegDate, EndDate, strSpecies))
    set objDAC = nothing
 
 End Function
 
   Public Function Detainage(BegDate, PageNumber,  EndDate, BegDateUnload, EndDateUnload, strCarrier, strSpecies)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2007#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if
    If BegDateUnload = "" then
  BegDateUnload = #01/01/2007#
  end if
  If EndDateUnload = "" then
  EndDateUnload = Date()
  End if

    set Detainage= objDAC.ExecuteSp("asp_cls_Detainage", array( 50, PageNumber, BegDate, EndDate, BegDateUnload, EndDateUnload, strCarrier, strSpecies))
    set objDAC = nothing
 
 End Function
 
   Public Function RPSearchTA_Shuttles(BegDate, PageNumber,  EndDate, strSpecies)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2007#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set RPSearchTA_Shuttles = objDAC.ExecuteSp("asp_cls_RPTA_Shuttles", array( 50, PageNumber, BegDate, EndDate, strSpecies))
    set objDAC = nothing
 
 End Function

   Public Function RPSearchTA_ShuttlesNull(BegDate, PageNumber,  EndDate, strSpecies)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2007#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set RPSearchTA_ShuttlesNull = objDAC.ExecuteSp("asp_cls_RPTA_ShuttlesNull", array( 50, PageNumber, BegDate, EndDate, strSpecies))
    set objDAC = nothing
 
 End Function
 
    Public Function RPSearchTA_direct(BegDate, PageNumber,  EndDate, strSpecies)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2007#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set RPSearchTA_Direct = objDAC.ExecuteSp("asp_cls_RPTA_Direct", array( 50, PageNumber, BegDate, EndDate, strSpecies))
    set objDAC = nothing
 
 End Function

    Public Function RPSearchTA_directNull(BegDate, PageNumber,  EndDate, strSpecies)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2007#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set RPSearchTA_DirectNull = objDAC.ExecuteSp("asp_cls_RPTA_DirectNull", array( 50, PageNumber, BegDate, EndDate, strSpecies))
    set objDAC = nothing
 
 End Function
 
     Public Function TR_direct(BegDate, PageNumber,  EndDate, strSpecies)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2008#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set TR_Direct = objDAC.ExecuteSp("asp_cls_TR_Direct", array( 50, PageNumber, BegDate, EndDate, strSpecies))
    set objDAC = nothing
 
 End Function

  Public Function TR_all(BegDate, PageNumber,  EndDate, strSpecies)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2008#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set TR_all = objDAC.ExecuteSp("asp_cls_TR_all", array( 50, PageNumber, BegDate, EndDate, strSpecies))
    set objDAC = nothing
 
 End Function
 
   Public Function Unload_all(BegDate, PageNumber,  EndDate,  strCarrier, strSpecies, strLocation)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2013#
  end if
  If EndDate = "" then
  EndDate = dateadd("d", 1, Date())
  End if

    set Unload_all = objDAC.ExecuteSp("asp_cls_UnloadR1", array( 500, PageNumber, BegDate, EndDate,  strCarrier, strSpecies,  strLocation))
    set objDAC = nothing
 
 End Function
 
    Public Function UnloadRail(BegDate, PageNumber,  EndDate, strSpecies,  strLocation)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2013#
  end if
  If EndDate = "" then
  EndDate = dateadd("d", 1, Date())
  End if

    set UnloadRail = objDAC.ExecuteSp("asp_cls_UnloadRail", array( 500, PageNumber, BegDate, EndDate, strSpecies,  strLocation))
    set objDAC = nothing
 
 End Function


 
      Public Function TR_Shuttles(BegDate, PageNumber,  EndDate, strSpecies)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
  If BegDate = "" then
  BegDate = #01/01/2008#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set TR_Shuttles = objDAC.ExecuteSp("asp_cls_TR_Shuttles", array( 50, PageNumber, BegDate, EndDate, strSpecies))
    set objDAC = nothing
 
 End Function
 
 
 Public Function RPTotals(Import_month,  Species, PO, Vendor)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
 
    set RPTotals = objDAC.ExecuteSp("asp_cls_RPTotals", array(  Import_month, Species, PO, Vendor))
    set objDAC = nothing
 
 End Function

 Public Function RPTotalsSW(Import_month,  Species, PO, Vendor, Req_ship_week)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
 
    set RPTotalsSW = objDAC.ExecuteSp("asp_cls_RPTotalsSW", array(  Import_month, Species, PO, Vendor, Req_ship_week))
    set objDAC = nothing
 
 End Function
 
  Public Function RPATotals(BegDate, EndDate, strSpecies)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
 
  If BegDate = "" then
  BegDate = #01/01/2007#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set RPATotals = objDAC.ExecuteSp("asp_cls_RPATotals", array(  BegDate, EndDate, strSpecies))
    set objDAC = nothing
 
 End Function
 
   Public Function UnloadTotals(BegDate, EndDate, strCarrier, strSpecies, strLocation)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
 
  If BegDate = "" then
  BegDate = #01/01/2013#
  end if
  If EndDate = "" then
  EndDate = dateadd("d", 1, Date())
  End if

    set UnloadTotals = objDAC.ExecuteSp("asp_cls_UnloadTotalsR1", array(  BegDate, EndDate, strCarrier, strSpecies, strLocation))
    set objDAC = nothing
 
 End Function
 
  Public Function UnloadRailTotals(BegDate, EndDate, strSpecies, strLocation)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
 
  If BegDate = "" then
  BegDate = #01/01/2013#
  end if
  If EndDate = "" then
  EndDate = dateadd("d", 1, Date())
  End if

    set UnloadRailTotals = objDAC.ExecuteSp("asp_cls_UnloadTotalsR1Rail", array(  BegDate, EndDate,  strSpecies, strLocation))
    set objDAC = nothing
 
 End Function


 
   Public Function RPATotals_Shuttles(BegDate, EndDate, strSpecies)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
 
  If BegDate = "" then
  BegDate = #01/01/2007#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set RPATotals_Shuttles = objDAC.ExecuteSp("asp_cls_RPATotals_Shuttles", array(  BegDate, EndDate, strSpecies))
    set objDAC = nothing
 
 End Function
 
    Public Function RPATotals_Direct(BegDate, EndDate, strSpecies)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
 
  If BegDate = "" then
  BegDate = #01/01/2007#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set RPATotals_Direct = objDAC.ExecuteSp("asp_cls_RPATotals_Direct", array(  BegDate, EndDate, strSpecies))
    set objDAC = nothing
 
 End Function
 
   Public Function VR(BegDate, EndDate, strVendor)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
 
  If BegDate = "" then
  BegDate = #01/01/2007#
  end if
  If EndDate = "" then
  EndDate = Date()
  End if

    set VR = objDAC.ExecuteSp("asp_cls_VRTotals", array(  BegDate, EndDate, strVendor))
    set objDAC = nothing
 
 End Function
 

 
  Public Function RPTotalReceived(Import_month, Species, PO, Vendor)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
 


    set RPTotalReceived = objDAC.ExecuteSp("asp_cls_RPTotalReceived", array(  Import_month, Species, PO, Vendor))
    set objDAC = nothing
 
 End Function

  Public Function RPTotalReceivedSW(Import_month, Species, PO, Vendor, Req_ship_week)
    Dim objDAC
    set objDAC = new ASP_CLS_DataAccess
 


    set RPTotalReceivedSW = objDAC.ExecuteSp("asp_cls_RPTotalReceivedSW", array(  Import_month, Species, PO, Vendor, Req_ship_week))
    set objDAC = nothing
 
 End Function



 
End Class
%> 