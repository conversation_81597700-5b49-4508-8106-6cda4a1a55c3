<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 14.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Enter Broke Trailer Receipt</title>
<style type="text/css">
.style1 {
	font-size: x-small;
}
.style25 {
	font-weight: bold;
	border-width: 1px;
	background-color: #FDE3E8;
}
.style26 {
	border-width: 1px;
	background-color: #FDE3E8;
}
.style28 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID,  MyConn, objMOC, rstFiber, rstSpecies, strKCWeighed, strPounds  
    Dim strTrailer, strCarrier, strLocation, MyRec5, strsql5, stralert
    Dim rstTrailer , strTrailerWeight , strTractor, strTrailerTID, strSAP, strerror, strType
 
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived, strLoadID
    Dim strOther, strsql3, strSpecies, strNet, strPO, strR, objGeneral

		strother = ""
		
		If len(request.querystring("id")) > 4 then
	strLoadID = request.querystring("id")
	
strsql = "Select tblInbound.* from tblInbound where load_nbr = " & strLoadID & ""
   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")


	If not Myrec.eof then
	strSAP = MyRec.fields("Status")
	strTrailer = MyRec.fields("Trailer")
	strCarrier = MyRec.fields("Carrier")
	end if
	MyRec.close
else
strSAP = request.querystring("s")
strTrailer = ""
strCarrier = ""
end if	
		
	
  call getdata()
  set objGeneral = new ASP_CLS_General
if objGeneral.IsSubmit() Then
strerror = 0

   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
	
	

	strSpecies ="BROKE"
	
 	strTrailer = Request.form("Trailer")
	strOther = Replace(Request.form("Other_Comments"), "'", "''") 
	strDateReceived = Request.form("Date_received")
	strGrossweight = Request.form("Gross_Weight")
	strPounds = Request.form("Tons_Received")
	strCarrier = Request.form("Carrier")
	strLocation = "YARD"
	strTrailerTID = request.form("Trailer_option")
	strSAP = request.form("SAP")
	
	 if  len(strTrailer)< 1 then
	 strerror = 1 %>
	 <font face="arial" size="3" color="red"><b>You must enter a Trailer number.</b></font><br>
<% end if %>
<% if strCarrier = "" then
strerror = 1 %>
 <font face="arial" size="3" color="red"><b>You must enter a Carrier</b></font><br>
<% end if %>
<% if strSAP= "" then
strerror = 1  %>
 <font face="arial" size="3" color="red"><b>You must select a SAP #</b></font><br>
<% end if

	if strerror = 0 then
	Call SaveData() 
	end if
	
	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to save this information.</font></br>")
	MyRec.close
	end if ' if you don't have authorization
    
   
end if ' if they did not submit
%>

<% if Request.querystring("n") = "T" then 
strTrailer = Session("Trailer")
strCarrier = Session("Carrier")
strTrailerTID = Session("TrailerTID")
strGrossWeight = Session("GrossWeight")

strPounds = Session("Pounds")
strOther = Session("Other")
strSAP = Session("SAP")
 %>
<p align = center><font face = arial size = 3 color = red><b>



<% if isnull(Session("Trailer")) or len(Session("Trailer"))< 1 then %>
You must enter a Trailer number.<br>
<% end if %>
<% if strCarrier = "" then %>
You must enter a Carrier</span><br>
<% end if %>
<% if strSAP= "" then %>
You must select a SAP #</span><br>
<% end if %>

</p></b></font>
<% else %>
&nbsp;
<% end if %>


<body>
<table width = 100%><tr><td width = 33%>
&nbsp;</td><td align = center width = 34%><b><font face="Arial" size="4">
Enter STO Broke Trailer Receipt </b></font> </b></td></tr></table>




<form name="form1" action="STO_Receipt.asp?id=<%=strLoadid%>" method="post">
<div align="center">
<table cellspacing="0" bordercolor="#C0C0C0" bgcolor="#FFFFEA" style="width: 80%;" cellpadding="0" class="style28">

<tr>
    <td bgcolor="#FFFFEA" align="right"><b><font face="Arial" size="2">&nbsp;</font></b></td>

    <td  align = center bgcolor="#FFFFEA" colspan="2">&nbsp; 
	</td>
  </tr><tr>
    <td bgcolor="#FFFFEA" align="right"><b><font face="Arial" size="2">Species:</font></b></td>

    <td  bgcolor="#FFFFEA">  
     <font face="Arial">
  	<span class="style1"><strong>&nbsp;BROKE</strong></span></td>

    <td  bgcolor="#FFFFEA" style="width: 44%"> 
	<font face="Arial"><font size="2" face="Arial"><b>Check 
	to Print Receipt:&nbsp;
</b></font> <input type="checkbox" name="Print_receipt" value="ON" checked></td>

  </tr>
  
    <tr>
    <td bgcolor="#FFFFEA" align="right" style="height: 23px"><font face="Arial" size="2"><b>
	Material #:</b></td>

    <td  bgcolor="#FFFFEA" colspan="2" style="height: 23px"> 
     <font face="Arial" size="2"><b>    <font face="Arial"> 
	<select name="SAP" style="font-weight: 700; width: 132px;" size="1" tabindex="1">
 	<option selected value="">  Select</option>
      <% strsql = "Select tblBrokeSAP.* from tblBrokeSAP"
      
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    while not MyRec.eof

       %>
        <option <% if strSAP = MyRec("SAP") then %> selected <% end if %> ><%= MyRec("SAP") %></option>
        <% MyRec.movenext
        wend
        MyRec.close %>
  
     </select>&nbsp;
		<font face="Arial" size="2">&nbsp;&nbsp;&nbsp; (Required)</font></font></b></td>
  </tr>
  
  <tr>
    <td  align = right bgcolor="#FFFFEA" height="29" >
   <b>
   <font face="Arial" size="2">Trailer:&nbsp;</font></b></td>
<td  align = left colspan="2" height="29" bgcolor="#FFFFEA">

      <input type="text" name="Trailer" size="15" value="<%= strTrailer %>" tabindex="2" >&nbsp;
		<font face="Arial" size="2"><b>(Required)</b></font></td></tr>
  <tr>

      <td  bgcolor="#FFFFEA" align = right height="28">
	<font face="Arial" size="2"><b>Select Carrier: </b></font></td>
<td  align = left height="28" bgcolor="#FFFFEA">

      <select name="Carrier" tabindex="3">
 	<option selected>  Select Carrier - Required</option>
      <%= objGeneral.OptionListAsString(rstFiber, "Carrier", "CDesc", strCarrier) %>
     </select><font face="Arial" size="2"><b>&nbsp; </b></font></td>
<td  align = left height="28" bgcolor="#FFFFEA">

      &nbsp; </td></tr>
<tr>
    <td  bgcolor="#FFFFEA">  
	<p align="right">  <font face="Arial" size="2"><b>&nbsp;</b></font></td>
    <td bgcolor="#FFFFEA" colspan="2">   &nbsp;</td>
  </tr>

    <tr>
		<td align = right width="17%" class="style26"> 
		<p align="center"> <b>
			<font face="Arial" size="2">Combined Trailer/Tractor</font></b><font face="Arial" size="2"><b> 
		Weight:&nbsp;</b></font></td>
<td align = left colspan="2" class="style26">    <font face="Arial"> 
<select name="Trailer_option" style="font-weight: 700" size="1" tabindex="4">
 	<option selected>  Select Trailer</option>
      <%= objGeneral.OptionListAsString(rstTrailer, "TID", "Toption", strTrailerTID) %>
        
     </select></td>
</tr>
<tr>
	<td height="22" align="right" width="17%" class="style26">
	&nbsp;</td>
    <td colspan="2" height="22" class="style26">         
	&nbsp;</td>
  </tr>
<tr>
	<td height="22" width="17%" class="style25">
	<p align="right"><font face="Arial" size="2">Gross Weight:</font></td>
    <td height="22" width="21%" class="style26">      
	
      <input type="text" name="Gross_Weight" size="15" style="height: 22px" value="<%= strGrossweight %>" tabindex="5" ></td>
    <td height="22" style="width: 44%" class="style25">    
	 <font face="Arial" size="2">OR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
		Pounds Received:&nbsp;&nbsp; 
		<input name="Tons_Received" size="15" value = "<%= strPounds%>" tabindex="6" ></font></td>
  </tr>
<tr>
	<td  bgcolor="#FFFFEA" height="22" width="17%">&nbsp;</td>
    <td  bgcolor="#FFFFEA" colspan="2" height="22">&nbsp;</td>
  </tr>

       <tr><td  align = right bgcolor="#FFFFEA" ><font face="Arial" size="2"><b>Date Received:&nbsp;</b></font></td>
<td align = left colspan="2" bgcolor="#FFFFEA"> 
<input type="text" name="Date_Received" size="15" value = <%= formatdatetime(Now(),2)%> style="width: 136px" tabindex="7"></td></tr>
             
         <tr>
          <td  align = right bgcolor="#FFFFEA" >
  <font face="Arial" size="2"><b>Comments:&nbsp;</b></font></td >
   <td align = left colspan="2" bgcolor="#FFFFEA">   
	<input type="text" name="Other_Comments" size="25" style="width: 278px" value="<%= strOther %>" tabindex="8">
	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font face="Arial"><font size="2">&nbsp;Location: </font>   
YARD</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	</td></tr>
	
	

<tr>
    <td  bgcolor="#FFFFEA">&nbsp;</td>

    <td bgcolor="#FFFFEA" colspan="2"> 
	<Input name="Update" type="submit" Value="Submit"  ></td>
  </tr>


</table>

</div>

</form>
</body>
<% Function GetData()
  set objMOC = new ASP_CLS_FIBER
        set rstFiber = objMOC.FiberCarrier()
    
        set rstTrailer = objMOC.TrailerOptions()
           
End Function

Function SaveData()
dim strbody, strCID, strECarrier, strETrailer, strELoad, strEDoc, strEDC, strEDCName, strEAdd, strECity, strEState, MyConn2, strsql4
dim strECC, strEBCC
	
strloadid = request.querystring("id")
	if len(request.form("Tons_Received")) > 0 then
		
		strPounds = Request.form("Tons_received")
	strTonsReceived = round(strPounds/2000,3)



	strTrailerTID = 0
	strTrailerweight = 0
	strGrossWeight = 0
	strTareWeight = 0
    strNet = strTonsReceived
	end if 


	
	If len(request.form("Gross_Weight")) > 0 then
	strTrailerTID = request.form("Trailer_option")
   	 strSQL3 = "Select weight from tblTrailerOptions where TID = " & strTrailerTID

   	 Set MyConn3 = Server.CreateObject("ADODB.Recordset")
   	 MyConn3.Open strSQL3, Session("ConnectionString")
   	 
   	 strTrailerWeight = MyConn3.fields("Weight")
   	 
   	 MyConn3.close
   	 
   	   	 
	strTonsReceived = round((Request.form("Gross_Weight") - strTrailerweight)/2000,3)
	strPounds = round((Request.form("Gross_Weight") - strTrailerweight),3)
	strTareWeight =  strTrailerweight
   	 

	strGrossWeight = Request.form("Gross_Weight")
		strNet = strTonsReceived
	end if
	

	
If strTrailerweight > 100 then
	strTrailerweight = (strTrailerweight - 18780) ' subtract off the weight of the conventional tractor
	end if 
	

	
	Dim strRightNow
	strRightnow = now()
	
	strSAP = request.form("SAP")
	strsql = "Select Type from tblBrokeSAP where SAP = '" & strSAP & "'"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
   	StrType = MyRec("Type")
	MyRec.close

	
	
	
strsql =  "INSERT INTO tblCars (Delivery_number, SAP_Nbr, Broke_Description, Trailer_weight, Trailer_TID, Tractor_weight,   Carrier, Species, Grade,  "_
&"   Date_received, Location,  Trailer, Other_Comments,  Tons_Received, Net, Gross_weight, Tare_weight,  Entry_Time, Entry_BID, Entry_Page, Status ) "_
	&" SELECT '" & strLoadid & "', '" & strSAP & "', '" & strType & "',  " & strTrailerWeight & ", " & strTrailerTID & ", 18780,   '" & strCarrier & "', 'BROKE', 'BROKE', "_
	&"   '" & strDateReceived & "',   'YARD',  "_
	&" '" & strTrailer & "', '" & strOther & "',  " & strTonsReceived & ", " & strTonsReceived & ", " & strGrossWeight & ", " & strTareWeight & ", "_
	&" '" & strRightNow & "', '" & Session("EmployeeID") & "', 'STO_Receipt', " & strTonsReceived & ""

	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
				
				strsql5 = "SELECT Max(tblCars.CID) AS MaxOfCID FROM tblCars WHERE tblCars.Trailer = '" & strTrailer & "'"
			
   	 Set MyRec5 = Server.CreateObject("ADODB.Recordset")
   	 MyRec5.Open strSQL5, Session("ConnectionString")
   	 strcarID = MyRec5.fields("MaxofCID")
   	 MyRec5.close
   	 
   	 
   	 



		If Request.form("Print_receipt") =  "ON" then
		
	
	
	
				Response.redirect("Truck_receipt.asp?id=" & strCarID & "&p=" & strpounds)
			

	
		else
		Response.redirect ("Broke_receipt.asp")
		end if

End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->