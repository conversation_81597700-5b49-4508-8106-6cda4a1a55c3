																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>AIG User Access</TITLE>
<!--#include file="classes/asp_cls_DataAccessAIG.asp"-->
<!--#include file="classes/asp_cls_headerAIG.asp"-->
<!--#include file="classes/asp_cls_SessionStringAIG.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<% Dim MyRec, strsql, MyConn


strsql = "SELECT tblAIGUserType.* FROM tblAIGUserType where BID = '" & Session("EmployeeID") & "' and User_type = 'O'" 
    Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
    If MyConn.eof  then
    Response.write ("<br><br><font face = arial size = 3><b>You do not have authorization to view this page</b></font>")
   else
    BuildScreen()
    
    end if 
    If session("EmployeeID") = "C97338" then
    Buildscreen()
    end if
   
    %>
    <%Sub Buildscreen

strsql = "SELECT tblAIGUserType.* FROM tblAIGUserType order by User_name" 
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>

 <TD width = 10% align = left><font size="2" face = arial><a href="AIG_Add.asp">Add New</a></font></td>
<td align = center><b>
<font face="arial" size="4" >AIG User List</font></b></span></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=50% class = "tablecolor1" border=1 align = center>  
	 <tr class="tableheader">

	<td  >  <font face="Arial" size="2">BID #</font></td>
		<td  >  <font face="Arial" size="2">User Name</font></td>
        <td  > <font face="Arial" size="2">User Type</font></td>
	<td>&nbsp;</td>
		


	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>

	<td  >
        <font size="2" face="Arial">
        <%= MyRec.fields("BID")%>&nbsp;</font></td>
        <td  >
        <font size="2" face="Arial">
        <%= MyRec.fields("user_name")%>&nbsp;</font></td>
        	<td  >      <font face="Arial" size="2">
        	<% if MyRec.fields("User_type") = "O" then %>
        	System Owner
        	<% elseif MyRec.fields("User_type") = "V" then %>
        	Vendor Information Maintainer
        	<% elseif MyRec.fields("User_type") = "B" then %>
        	Buyer
        	<% elseif MyRec.fields("User_type") = "D" then %>
        	Display Only
        	<% else %>
        	&nbsp;
        	<% end if %>
       &nbsp;</font></td>
		
	<td> <font size="2" face="Arial"><a href="AIG_Delete.asp?id=<%= MyRec.fields("UID") %>">Delete</a></td>	
	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
     MyRec.close
    %>
</table>
<% end sub %><!--#include file="AIGfooter.inc"-->