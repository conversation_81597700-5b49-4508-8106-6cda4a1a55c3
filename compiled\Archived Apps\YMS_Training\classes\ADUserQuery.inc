<%
'---------------------------------------------------------------------------------------------------
'--## # FILE DESCRIPTION ---------------------------------------------------------------------------
'---------------------------------------------------------------------------------------------------
'-- 
'-- This file contains two classes used to query a Microsoft Active Directory and return a 
'-- collecion of user objects.
'--
'--     CLASS: ActiveDirectoryUserQuery      The Search  (wrapper/collection)object. 
'--     CLASS: ActiveDirectoryUser           A valid active directory user object, populated with
'--                                          standard LDAP Properties and some custom AD properties.
'--
'-- These classes are a replacement for the current scripts that access the exchange GAL for 
'-- employee information. They use LDAP to access employee information from the Windows 2000 Active 
'-- Directory services instead of from the Exchange 5.5 directory structure. This class is written 
'-- in VBScript, and can be used in ASP and Windows script host files.
'--
'-- IF THE FILES ARE USED UNDER ASP ...
'-- (1) Credentials must be provided to the script since  
'-- 
'--
'---------------------------------------------------------------------------------------------------
'--## # VERSION INFORMATION ------------------------------------------------------------------------
'---------------------------------------------------------------------------------------------------
'--
'-- Version 2.05
'-- Modified by B16599 (Sean Powell) on 5/21/2004
'-- (1) Changed Secretary AD propertiey to msexchassistantname to fit the AD2003 schema expansion.
'--
'-- Version 2.04
'-- Modified by B16599 (Sean Powell) on 2/19/2004
'-- (1) Added DC Randomization and Failover with the udfGetDC function.
'--
'-- Version 2.03
'-- Modified by B16599 (Sean Powell) on 1/22/2004
'-- (1) Added "ObjectCategory = 'Person'" to the default WHERE clause to prevent machine accounts 
'--     from being returned.
'-- (2) Added the LDAP property "memberOf", and the friendly names "groupNames" and "" to the   
'--     ActiveDirectoryUser object so that a user's groups can be returned. THIS WILL NOT RETURN 
'--     NESTED GROUPS - only directly assigned groups.
'-- (3) Added the "udfmakeList" function in the ActiveDirectoryUser object.
'-- (4) Updated some of the "friendly" properties to include the single instance version of the 
'--     property. For example, 
'--     'telephoneNumber' is a single-instance field that contains 1 phone number.
'--     'phone' is the friendly name for the telephoneNumber field.
'--     'otherTelephone' is a multi-instance field that can contain up to 10 phone numbers. It does 
'--                      not contain the phone number contained in the telephoneNumber field.
'--     'phones' was the friendly name for the otherTelephone, however, now 'phones' is a combination 
'--              of the telephoneNumber and otherTelephone fields, so that all of the numbers appear 
'--              in a single property.
'--     The fields 'WebPages','phones','telephones','homephones','pagers','mobiles','faxes' and 
'--              'ipphones' were updated wherever the udfmakeList function is used.
'-- (5) Added the 'exchangeServer', 'exchangePath' and 'exchangeServer' properties, which list a 
'--     users K-C Exchange server. 
'--
'-- Version 2.02
'-- Modified by B16599 (Sean Powell)
'-- Added a default user ID to active direcotry for this class. Therefore, passing in credentials is 
'-- no longer required. If credentials are passed they are used, if not the default credentials are 
'-- used, which do allow for full access to the AD User container for read-only purposed. Added a 
'-- version number to the class for user identification.
'-- 
'-- Version 2.01
'-- Modified by B16599 (Sean Powell)
'-- Added properties for passing in user auithentication, so the object will work with Active 
'-- Directory securty changes made recenetly. This also allows the ActiveDirectoryUserQuery object 
'-- to access the extended AD attributes.
'-- 
'-- Version 2.00
'-- Created by B16599 (Sean Powell)
'-- Re-Wrote the entire class. It is now broken into two classes:
'--    ActiveDirectoryUser, which is a user object populated with LDAP and AD properties.
'--    ActiveDirectoryUserQuery, which is a container collection used to query active directory 
'--    and return a collection of user Objects.
'-- 
'-- Version 1.00 (FindUser.cls)
'-- Created by B16599 (Sean Powell)
'--
'---------------------------------------------------------------------------------------------------
'--## @ ADUserQuery Description --------------------------------------------------------------------
'---------------------------------------------------------------------------------------------------
'-- 
'-- DESCRIPTION: A class used to search Active Directory. This will create a collection of 
'--              ActiveDirectoryUser objects.
'--
'---------------------------------------------------------------------------------------------------
'--## @ ADUserQuery Object Model -------------------------------------------------------------------
'---------------------------------------------------------------------------------------------------
'--
'-- PROPERTIES: 
'--     authorizedUserName          Str: The user name/id to authenticate to AD as.
'--     authorizedUserPassword      Str: The password to authenticate to AD with.
'--     targetDomain                Str: To domain to search. (Ex: kcc.com).
'--     ldapSearchString            Str: The last search string executed by executeQuery.
'--     domainControllerList        Str: A list of Domain controllers to use, seperated by commas.
'--     resultsCount                Int: The results count from the last query executed by executeQuery.
'--     orderBy                     Str: The ADSI property(s) to sor the results by in a standard SQL order by clause.
'--     errorsText                  Str: A text message listing all the erros in the errors collection.
'--     errorCount                  Int: Count for number of errors in the erros collection.
'--     version                     Str: Indicates the version number of the object (only works in version 2.02+).
'--
'-- METHODS:    
'--     executeQuery                Executes the query and populates the results collection with a collection of 
'--                                 ActiveDirectoryUser objects that meet the conditions in the filters collection. 
'--     addFilter Name,Value        Method: Add a filter set. 
'--                                 Return: None.
'--                                         Name = The LDAP property name to filter on. (ex. cn, samAccountName, etc.)
'--                                         Value = The value the filter should check against.
'--                                                     use 'B12345' to find an exact match.
'--                                                     use 'B12345*' to find all entries that match base 'B12345???'.
'--                                                     use '*B12345*' to find all entries that contain 'B12345'.
'--                                         ** both name and value must be typed as strings or an error will occur.
'--     setFilter(Name)             Method: Sets the value for the specified Ldap property name.
'--                                 Return: None.
'--     getFilter(Name)             Property (Str): Returns the value for the specified Ldap property name.
'--     filterExists(Name)          Method: Determines if a filter for the given Ldap property name already exists.
'--                                 Return: boolean
'--     removeFilter(Name)          Method: Removes an individual filter criteria set determined by 'Name'.
'--                                 Return: None.
'--     removeAllFilters            Method: Removes all filter criteria sets.
'--                                 Return: None.
'--
'-- COLLECTIONS:
'--     filters (A collection of search Criteria - This is a dictionary object)          
'--       .count                    Property (Int): The current number of filter criteria sets.
'--       .keys                     Collection (Ary): An array of existing filter keys (ldap property names)
'--       .items                    Collection (Ary): An array of existing filter values (criteria)      
'--
'--     errors (A collection of Errors encountered during method calls.0
'--       .number                   Property (Int): Error Number raised by Application/Environment (or custom).
'--       .description              Property (Str): Textual Error Description.
'--       .source                   Property (Str): Textual source definition of the event that raised the error.
'--
'--     Results (a collection of ActiveDirectoryUser objects - see the Class for property descriptions)
'--
'-----------------------------------------------------------------------------------------------------------------------
'--## @ ADUserQuery Usage Example --------------------------------------------------------------------------------------
'-----------------------------------------------------------------------------------------------------------------------
'--
'--   This example should work line for line, but YOU MUST do the following:
'--      1) Copy the usage example to a new file.
'--      2) replace authorizedUserName with an authorized user account in the KCUS domain.
'--      3) replace authorizedUserPassword with the correct password for the KCUS domain account.
'--      4) remove the space in the 4 sets of asp brackets
'--
'--    < % Option Explicit % >
'--    <HTML>
'--    <HEAD>
'--        <TITLE>ActiveDirectoryUserQuery Class Test</TITLE>
'--        <!-- # INCLUDE VIRTUAL="/scripts/ADUserQuery.inc"-->
'--    </HEAD>
'--    <BODY STYLE="background-color: #FFFFFF; font-size: 10pt; color: #000000; font-family: sans-serif;">
'--    < %
'--    '-- Create the objADUserQuery Object and assign values to the required properties. 
'--    '-- These must be set for the class to correctly execute.
'--        Dim objADUserQuery
'--        Set objADUserQuery = New ActiveDirectoryUserQuery
'--            'objADUserQuery.authorizedUserName = "kcus\b12345"
'--            'objADUserQuery.authorizedUserPassword = "password"
'--            objADUserQuery.targetDomain = "kcc.com"
'--            objADUserQuery.addFilter "cn","B16599*"
'--    
'--        '-- Modify the Filters to narrow or broaden the search.
'--            objADUserQuery.removeFilter "cn" 
'--            objADUserQuery.removeAllFilters
'--            objADUserQuery.setFilter "cn","IN('B16599','B19587')"
'--            
'--        '-- to ensure you get only user accounts asigned to real people, add
'--        '-- this mail filter (applications of workstations don't usually get
'--        '-- email accounts.    
'--            objADUserQuery.addFilter "mail","*@kcc.com*"
'--    
'--        '-- Add an order by clause to sort the results.
'--            objADUserQuery.orderBy = "sn"
'--    
'--        '-- check some filter incormation                
'--            Response.write("getFilter('cn'): " & objADUserQuery.getFilter("cn") & "<BR>")
'--            Response.write("filterExists('cn'): " & objADUserQuery.filterExists("cn") & "<BR>")
'--    
'--        '-- Execute the Query and Process the Results
'--            objADUserQuery.executeQuery
'--    
'--        '-- Write out Header Information
'--            Response.Write("authorizedUserName     : " & objADUserQuery.authorizedUserName & "<BR>")     
'--            Response.Write("authorizedUserPassword : " & objADUserQuery.authorizedUserPassword & "<BR>") 
'--            Response.Write("targetDomain           : " & objADUserQuery.targetDomain & "<BR>")           
'--            Response.Write("ldapSearchString       : " & objADUserQuery.ldapSearchString & "<BR>")        
'--            Response.Write("resultsCount           : " & objADUserQuery.resultsCount & "<BR>")           
'--            Response.Write("errorCount             : " & objADUserQuery.errorCount & "<P>")             
'--    
'--        '-- Check for Errors. Display errors if they exist.
'--            If(objADUserQuery.errorCount > 0) Then
'--                Response.Write(Replace(objADUserQuery.errorsText,vbCrLf,"<BR>"))
'--    
'--        '-- If no errors exist, process the results set.
'--            Else
'--                If(objADUserQuery.resultsCount = 0) Then
'--                    Response.write("<P>No users Found.<P>")
'--            '-- Access the results in 2 ways.    
'--                ElseIf(objADUserQuery.resultsCount = 1) Then
'--                '-- Use standard array form '(x)' where x is an ordinal position in the array.    
'--                    Response.Write(Replace(objADUserQuery.results(0).AllProperties,vbCrLf,"<BR>"))
'--                Else
'--                '-- or process the entier collection with a standard for ... each loop.    
'--                    Dim objADUser
'--                    For Each objADUser in objADUserQuery.Results
'--                        Response.Write(Replace(objADUser.UserSummary,vbCrLf,"<BR>")&"<P>")
'--                    Next
'--                    Set objADUser = Nothing
'--                End If
'--            End If
'--        
'--        Set objADUserQuery = Nothing
'--    
'--    % >
'--    </BODY>
'--    </HTML>

'-----------------------------------------------------------------------------------------------------------------------

    Class ActiveDirectoryUserQuery

    '-------------------------------------------------------------------------------------------------------------------
    '--## @ ADUserQuery Properties -------------------------------------------------------------------------------------
    '-------------------------------------------------------------------------------------------------------------------
        
        Public authorizedUserName            '-- The user to authenticate to AD as.
        Public authorizedUserPassword        '-- The password to authenticate to AD with.
        Public targetDomain                  '-- The domain to perform the search in.
        Public filters                       '-- A collection of filters to apply to the search.
        Public orderBy                       '-- A standard SQL order by clause
        Public results()                     '-- The collection of results returned by executeQuery.
        Public resultsCount                  '-- The results count from the last query executed by executeQuery. 
        Public errors                        '-- A collection of Errors.
        Public ldapSearchString              '-- The last search string executed by executeQuery.
        Public domainControllerList          '-- A List of domain controllers to use.
        Public version

        Public Property Get errorCount      '-- Returns the number of errors in the errors collection.
            errorCount = errors.items.count
        End Property

        Public Property Get errorsText       '-- Returns the errors collection in a text-format.
            Dim objErr,strErrors
            For Each objErr in Errors.Items
                strErrors = strErrors & "ERROR NUMBER: " & objErr.Number & vbCrLf & _
                                        "SOURCE: " & objErr.Source & vbCrLf & _
                                        "DESCRIPTION: " & objErr.Description & vbCrLf & vbCrLf
            Next
            errorsText = strErrors
            Set objErr = Nothing
        End Property

        Private defaultUserName
        Private defaultUserPassword
        Private defaultDC
        Private defaultDCList

    '--------------------------------------------------------------------------------------------------------------------
    '--## @ ADUserQuery Public Methods ----------------------------------------------------------------------------------
    '--------------------------------------------------------------------------------------------------------------------

    '--------------------------------------------------------------------------------------------------------------------
    '--## ADUserQuery.S.executeQuery ------------------------------------------------------------------------------------
    '--------------------------------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Wrapper function for execution of LDAP search.
    '--
    '-- OUTPUTS: Sets Errors Collection
    '--
    '--------------------------------------------------------------------------------------------------------------------
        Public Sub executeQuery

            On Error Resume Next
                If (validateSearchCriteria) Then
                    If NOT (IsNull(defaultDC)) Then
                        createLdapSearchString
                        executeSearch
                    End If
                End If
            On Error Goto 0
            
        End Sub

    '--------------------------------------------------------------------------------------------------------------------
    '--## ADUserQuery.S.addFilter ---------------------------------------------------------------------------------------
    '--------------------------------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Adds a new filter criteria pair to the filters collection.
    '--
    '-- INPUTS: strFilterKey       A valid filter key (LDAP Property)
    '--         strFilterValue     a filter value 
    '--
    '--------------------------------------------------------------------------------------------------------------------
        Public Sub addFilter(ByVal strFilterName, ByVal strCriteria)

            On Error Resume Next
                Err.Clear
                If (validateProperty(strFilterName)) Then
                    filters.Add CStr(strFilterName), CStr(strCriteria)
                    If (Err) Then
                        '-- ## || ADUserQuery::addFilter::1
                        If(Err.Number = 457) Then
                            Errors.AddItem 1, "CLASS.ActiveDirectoryUserQuery::addFilter", _
                                                    Err.Source & " " & Err.Number & ": " & Err.Description & ": " &  _
                                                    "Filter key '" & strFilterName & "' already exists with value '" & _
                                                    strCriteria & "'. To update the value use the setFilter method."
                        Else
                         '-- ## || ADUserQuery::addFilter::2
                            Errors.AddItem 2, "CLASS.ActiveDirectoryUserQuery::addFilter", _
                                                    Err.Source & " " & Err.Number & ": " & Err.Description 
                        End If
                    End If
                Else
                    Errors.AddItem 3, "CLASS.ActiveDirectoryUserQuery::addFilter", _
                                           "'" & strFilterName & "' is not a valid LDAP property."
                End If
            On Error Goto 0
            
        End Sub

    '--------------------------------------------------------------------------------------------------------------------
    '--## ADUserQuery.F.getFilter ---------------------------------------------------------------------------------------
    '--------------------------------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: gets the value of a filter criteria set.
    '--
    '-- INPUTS: strFilterKey       A valid filter key (LDAP Property)
    '--
    '--------------------------------------------------------------------------------------------------------------------
        Public Function getFilter(ByVal strFilterName)

            On Error Resume Next
            Err.Clear
                If(Filters.Exists(CStr(strFilterName))) Then
                    getFilter = filters.Item(CStr(strFilterName))
                Else
                 '-- ## || ADUserQuery::getFilter::1
                    Errors.AddItem 1, "CLASS.ActiveDirectoryUserQuery::getFilter", _
                                      Err.Source & " " & Err.Number & ": " & Err.Description & ": " &  _
                                      "Filter key '" & strFilterName & "' does not exist. Use " & _
                                      "filterExists to determine if a filter exists before acting on it."
                End If
             '-- ## || ADUserQuery::getFilter::2
                If(Err) Then errors.AddItem 2, "CLASS.ActiveDirectoryUserQuery::removeFilter", _
                                                Err.Source & " " & Err.Number & ": " & Err.Description 
            On Error Goto 0
            
        End Function

    '--------------------------------------------------------------------------------------------------------------------
    '--## ADUserQuery.F.setFilter ---------------------------------------------------------------------------------------
    '--------------------------------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: sets the value of a filter criteria set. if the key doesn't exist the set is created rather than
    '--              updated.
    '--
    '-- INPUTS: strFilterKey       A valid filter key (LDAP Property)
    '--         strCriteria        A filter string
    '--
    '--------------------------------------------------------------------------------------------------------------------
        Public Function setFilter(ByVal strFilterName, ByVal strCriteria)

            On Error Resume Next
            Err.Clear
                If(Filters.Exists(CStr(strFilterName))) Then
                    filters.Item(CStr(strFilterName)) = CStr(strCriteria)
                Else
                    filters.Add CStr(strFilterName),CStr(strCriteria)
                End if

             '-- ## || ADUserQuery::getFilter::1
                If(Err) Then errors.AddItem 1, "CLASS.ActiveDirectoryUserQuery::setFilter", _
                                                Err.Source & " " & Err.Number & ": " & Err.Description 
            On Error Goto 0
            
        End Function

    '--------------------------------------------------------------------------------------------------------------------
    '--## ADUserQuery.S.filterExists ------------------------------------------------------------------------------------
    '--------------------------------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: determinies if a filter exists in the collection.
    '--
    '-- INPUTS: strFilterKey       A valid filter key (LDAP Property)
    '--
    '--------------------------------------------------------------------------------------------------------------------
        Public Function filterExists(ByVal strFilterName)

            On Error Resume Next
            Err.Clear
                filterExists = Filters.Exists(CStr(strFilterName))
             '-- ## || ADUserQuery::removeFilter::2
                If(Err) Then errors.AddItem 1, "CLASS.ActiveDirectoryUserQuery::removeFilter", _
                                                Err.Source & " " & Err.Number & ": " & Err.Description 
            On Error Goto 0
            
        End Function

    '--------------------------------------------------------------------------------------------------------------------
    '--## ADUserQuery.S.removeFilter ------------------------------------------------------------------------------------
    '--------------------------------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: removes a new filter criteria pair from the filters collection.
    '--
    '-- INPUTS: strFilterKey       A valid filter key (LDAP Property)
    '--
    '--------------------------------------------------------------------------------------------------------------------
        Public Sub removeFilter(ByVal strFilterName)

            On Error Resume Next
            Err.Clear
                If(Filters.Exists(CStr(strFilterName))) Then
                    filters.Remove CStr(strFilterName)
                Else
                 '-- ## || ADUserQuery::removeFilter::1
                    Errors.AddItem 1, "CLASS.ActiveDirectoryUserQuery::removeFilter", _
                                            "Filter key '" & strFilterName & "' does not exist. Use " & _
                                            "filterExists to determine if a filter exists before acting on it."
                End If
                
             '-- ## || ADUserQuery::removeFilter::2
                If(Err) Then errors.AddItem 2, "CLASS.ActiveDirectoryUserQuery::removeFilter", _
                                                Err.Source & " " & Err.Number & ": " & Err.Description 
            On Error Goto 0
            
        End Sub

    '--------------------------------------------------------------------------------------------------------------------
    '--## ADUserQuery.S.removeAllFilters --------------------------------------------------------------------------------
    '--------------------------------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: removes a new filter criteria pair from the filters collection.
    '--
    '--------------------------------------------------------------------------------------------------------------------
        Public Sub removeAllFilters

            On Error Resume Next
            Err.Clear
                filters.RemoveAll
             '-- ## || ADUserQuery::removeFilter::1
                If(Err) Then errors.AddItem 1, "CLASS.ActiveDirectoryUserQuery::removeAllFilters", _
                                                Err.Source & " " & Err.Number & ": " & Err.Description 
            On Error Goto 0
            
        End Sub


    '--------------------------------------------------------------------------------------------------------------------
    '--## @ ADUserQuery Private Procedures ------------------------------------------------------------------------------
    '--------------------------------------------------------------------------------------------------------------------

    '--------------------------------------------------------------------------------------------------------------------
    '--## ADUserQuery.s.Class_Initialize --------------------------------------------------------------------------------
    '--------------------------------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Class Initialization Routine
    '--
    '--------------------------------------------------------------------------------------------------------------------
        Private Sub Class_Initialize
            version = "2.05"
            Set filters = CreateObject("Scripting.Dictionary")
            Set errors = New ErrorItems
            defaultUserName = ""
            defaultUserPassword = ""
            defaultDCList = "ustcndc0,ustcndc1,ustcndc2,ustcndc3"
            If (Len(Trim(domainControllerList))>4) Then
                defaultDC = udfGetDC(domainControllerList)
            Else
                defaultDC = udfGetDC(defaultDCList)
            End If
        End Sub

    '--------------------------------------------------------------------------------------------------------------------
    '--## ADUserQuery.f.GetDC -------------------------------------------------------------------------------------------
    '--------------------------------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Returns a randomized, validated domain controller.
    '--
    '--------------------------------------------------------------------------------------------------------------------
        Private Function udfGetDC(strDCList)

            Const con_ADS_SECURE_AUTHENTICATION       = &H1 
            Const con_ADS_USE_ENCRYPTION              = &H2
            Const con_ADS_USE_SSL                     = &H2
            Const con_ADS_READONLY_SERVER             = &H4 
            Const con_ADS_PROMPT_CREDENTIALS          = &H8 
            Const con_ADS_NO_AUTHENTICATION           = &H10 
            Const con_ADS_FAST_BIND                   = &H20 
            Const con_ADS_USE_SIGNING                 = &H40 
            Const con_ADS_USE_SEALING                 = &H80 
            Const con_ADS_USE_DELEGATION              = &H100 
            Const con_ADS_SERVER_BIND                 = &H200

            Dim intIndex, arrDCs, strDC, strLog
            
            Dim objTemp, colTemp : colTemp = Split(strDCList,",")
            
            Dim colDCs, objDC
            Set colDCs = CreateObject("Scripting.Dictionary")
            For Each objTemp in colTemp
                colDCs.Add LCase(objTemp),""
            Next 
            
            Randomize
            On Error Resume Next
            Do 
                intIndex = Int((colDCs.Count) * Rnd)
                arrDCs = colDCs.Keys
                strDC = arrDCs(intIndex)
                strLog = strLog & strDC & ", "
                Err.Clear
                Set objDC = GetObject("WinNT:").OpenDSObject("WinNT://" & strDC, _
                                                                   defaultUserName, defaultUserPassword,_
                                                                   con_ADS_SECURE_AUTHENTICATION)
                If (Err.Number <> 0) Then
                    colDCs.Remove(strDC)
                Else
                    Set objDC = Nothing
                    Exit Do
                End If
            Loop Until (colDCs.Count = 0)
            
            If (colDCs.Count = 0) Then 
            '-- ## || ADUserQuery::GetDC::1
                If(Err) Then errors.AddItem 1, "CLASS.ActiveDirectoryUserQuery::getDC", _
                                                "A user session could not be established with any of the specified " & _
                                                "domain controllers (" & Left(strLog,Len(strLog)-2) & ")." 
                udfGetDC = Null
            Else
                udfGetDC = strDC
            End If

        End Function

    '--------------------------------------------------------------------------------------------------------------------
    '--## ADUserQuery.f.validateSearchCriteria --------------------------------------------------------------------------
    '--------------------------------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Validates the domain and search criteria before performing a search.
    '--
    '--------------------------------------------------------------------------------------------------------------------
        Public Function validateSearchCriteria
            
            On Error Resume Next
             
        '-- Validate the target domain for errors.    
            Select Case LCase(Trim(targetDomain))
                   Case "kcc.com"                     '-- OK, this is a valid domain.
                   Case "test.kcc.com"                '-- OK, this is a valid domain.
                   Case "kctest.com"                  '-- OK, this is a valid domain.
                   Case "internet.kimberly-clark.com" '-- OK, this is a valid domain.
                   Case "blueape.net"                 '-- OK, this is a valid domain.
                   Case ""                            '-- Generate an error, target domain is a required field.
                   '-- ## || ADUserQuery::validateSearchCriteria::1     
                        Errors.AddItem 1,"CLASS.ActiveDirectoryUserQuery::validateSearchCriteria", _
                                         "'targetDomain' is a required property. " & _
                                         "Please specify a target domain to search."
                   Case Else
                   '-- ## || ADUserQuery::validateSearchCriteria::2
                        Errors.AddItem 2,"CLASS.ActiveDirectoryUserQuery::validateSearchCriteria", _
                                         "'" & targetDomain & "' is an invalid value for the 'targetDomain' " & _
                                         "property. Please specify one of the following target domains: " & _
                                         "kcc.com, internet.kimberly-clark.com, test.kcc.com or kctest.com. "
            End Select                     
            
        '-- Check that a valid authorizedUserName has been set.   
        '-- ## || ADUserQuery::validateSearchCriteria::3       
            'If(Len(authorizedUserName) = 0) Then _
            '   Errors.AddItem 3,"CLASS.ActiveDirectoryUserQuery::validateSearchCriteria", _
            '                    "'authorizedUserName' is required. " & _
            '                    "Please a valid user name on the target domain in the 'KCUS\B12345' format."

        '-- Check that a valid authorizedUserPassword has been set.
        '-- ## || ADUserQuery::validateSearchCriteria::4
            'If(Len(authorizedUserPassword) = 0) Then _
            '   Errors.AddItem 4,"CLASS.ActiveDirectoryUserQuery::validateSearchCriteria", _
            '                    "'authorizedUserPassword' is required."

        '-- Check that the Filters collection has at least one filter criteria set.
        '-- ## || ADUserQuery::validateSearchCriteria::5
            If(filters.Count = 0) Then _
               Errors.AddItem 5,"CLASS.ActiveDirectoryUserQuery::validateSearchCriteria", _
                                "Collection 'filters' contains no criteria. " & _
                                "You must define at least one set of filter criteria to search."
            
        '-- If no errors exist in the input data attempt to load the user object.    
            If(errors.items.Count = 0) Then validateSearchCriteria = true Else validateSearchCriteria = false

        '-- ## || ADUserQuery::validateSearchCriteria::6
            If(Err) Then Errors.AddItem 6, "CLASS.ActiveDirectoryUserQuery::validateSearchCriteria", _
                                           Err.Number & ":" & Err.Description & ":" & Err.Source
            
            On Error Goto 0

        End Function

    '--------------------------------------------------------------------------------------------------------------------
    '--## ADUserQuery.s.createLdapSearchString --------------------------------------------------------------------------
    '--------------------------------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Creates the LDAP Search String to execute.
    '--
    '--------------------------------------------------------------------------------------------------------------------
        Private Sub createLdapSearchString

            Dim strSqlWhere
            Dim strSqlSelect
            Dim strSQLOrderBy
            Dim strTemp
            Dim arrTemp
            Dim objFilterKey

            On Error Resume Next
            Err.Clear

            If(Len(Trim(orderBy))>0) Then strSQLOrderBy = " ORDER BY " & orderBy Else strSQLOrderBy = ""

            strSqlSelect = "SELECT cn, ADSPath " & _
                           "  FROM '" & "LDAP://" & defaultDC & "/DC=" & Join(Split(LCase(targetDomain),"."),",DC=") & "' " & _
                           " WHERE objectClass = 'user' AND ObjectCategory = 'Person' "

            For Each objFilterKey in filters
                If (validateProperty(objFilterKey)) Then
                    If (Left(UCase(Trim(filters.item(objFilterKey))),3) = "IN(" OR _
                        Left(UCase(Trim(filters.item(objFilterKey))),4) = "IN (") Then
                        strTemp = Trim(filters.item(objFilterKey))
                        strTemp = Replace(strTemp,"IN (","IN(")
                        strTemp = Left(strTemp,Len(strTemp)-1)
                        strTemp = Mid(strTemp,4)
                        strTemp = Replace(strTemp,"','","'',''")
                        arrTemp = Split(strTemp,"','")
                        strSqlWhere = strSqlWhere & " AND (" 
                        For Each strTemp in arrTemp
                             strSqlWhere = strSqlWhere & objFilterKey & " = " & strTemp & " OR "
                        Next
                        strSqlWhere = Left(strSqlWhere,Len(strSqlWhere)-4) & ")"                       
                        '-- strSqlWhere = strSqlWhere & " " & objFilterKey & " " & filters.item(objFilterKey)
                    Else
                        strSqlWhere = strSqlWhere & " AND " & objFilterKey & " = '" & filters.item(objFilterKey) & "'" 
                    End If
                Else
                    Errors.AddItem 2, "CLASS.ActiveDirectoryUserQuery::createLdapSearchString", _
                                           "'" & objFilterKey & "' is not a valid LDAP property."
                End If
            Next
            Set objFilterKey = Nothing

            ldapSearchString = strSqlSelect & strSqlWhere & strSQLOrderBy

         '-- ## || ADUserQuery::createLdapSearchString::1
            If(Err) Then Errors.AddItem 1, "CLASS.ActiveDirectoryUserQuery::createLdapSearchString", _
                                           Err.Number & ":" & Err.Description & ":" & Err.Source

            On Error Goto 0

        End Sub

    '--------------------------------------------------------------------------------------------------------------------
    '--## ADUserQuery.s.executeSearch -----------------------------------------------------------------------------------
    '--------------------------------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Executes a search against LDAP with a predefined search string
    '--
    '--------------------------------------------------------------------------------------------------------------------
        Public Function executeSearch
    
            Const con_ADS_PASS_ENCRYPT = True
            Const con_ADS_QUERY_TIMEOUT = 90
            Const con_ADS_QUERY_CACHERESULTS = False
            Const con_ADS_SCOPE_BASE = 0
            Const con_ADS_SCOPE_ONELEVEL = 1 
            Const con_ADS_SCOPE_SUBTREE = 2
            Const con_ADS_CHASE_REFERRALS_NEVER = &H0 
            Const con_ADS_CHASE_REFERRALS_SUBORDINATE = &H20 
            Const con_ADS_CHASE_REFERRALS_EXTERNAL = &H40 
    
            Dim   objDBConnection
            Dim   rsAdsiUsers
            Dim   objAdoCmd

            On Error Resume Next
            Err.Clear

            Set objDBConnection = CreateObject("ADODB.Connection")
                objDBConnection.Provider = "ADsDSOObject"
                If (Len(Trim(authorizedUserName)) = 0) Then
                    objDBConnection.Properties("User ID") = defaultUserName
                    objDBConnection.Properties("Password") = defaultUserPassword
                Else 
                    objDBConnection.Properties("User ID") = authorizedUserName
                    objDBConnection.Properties("Password") = authorizedUserPassword
                End If
                objDBConnection.Properties("Encrypt Password") = con_ADS_PASS_ENCRYPT
                objDBConnection.Open "DS Query"
                Set objAdoCmd = CreateObject("ADODB.Command")
                Set objAdoCmd.ActiveConnection = objDBConnection
                    objAdoCmd.Properties("Timeout") = con_ADS_QUERY_TIMEOUT
                    objAdoCmd.Properties("searchscope") = con_ADS_SCOPE_SUBTREE
                    objAdoCmd.Properties("Chase referrals") = con_ADS_CHASE_REFERRALS_EXTERNAL
                    objAdoCmd.Properties("Cache Results") = con_ADS_QUERY_CACHERESULTS
                    objAdoCmd.CommandText = ldapSearchString
                    Set rsAdsiUsers = CreateObject("ADODB.Recordset")
                    Set rsAdsiUsers = objAdoCmd.Execute
                        If(Err) Then 
                            If(Err.Number = -2147217911) Then
                             '-- ## || ADUserQuery::executeSearch::1 
                                Errors.AddItem 1, "CLASS.ActiveDirectoryUserQuery::executeSearch", _
                                               Err.Number & ":" & Err.Description & ":" & Err.Source & _
                                               ". Check 'authorizedUserName', 'authorizedUserPassword' and " & _
                                               "'targetDomain' to ensure they are a valid user/domain " & _
                                               "combination."
                                Err.Clear
                            Else
                             '-- ## || ADUserQuery::executeSearch::2   
                                Errors.AddItem 2, "CLASS.ActiveDirectoryUserQuery::executeSearch", _
                                               Err.Number & ":" & Err.Description & ":" & Err.Source
                                Err.Clear
                            End If
                        Else
                            resultsCount = rsAdsiUsers.RecordCount
                            If NOT (rsAdsiUsers.BOF AND rsAdsiUsers.EOF) Then
                                Redim results(rsAdsiUsers.RecordCount-1)
                                Do While NOT rsAdsiUsers.EOF
                                    Set results(rsAdsiUsers.AbsolutePosition-1) = getUser(rsAdsiUsers("ADSPath"))
                                    rsAdsiUsers.Movenext
                                Loop
                            End If
                            rsAdsiUsers.Close
                        End If
                    Set rsAdsiUsers = Nothing
                Set objAdoCmd = Nothing
            Set objDBConnection = Nothing
            
         '-- ## || ADUserQuery::executeSearch::3   
            If(Err) Then Errors.AddItem 3, "CLASS.ActiveDirectoryUserQuery::executeSearch", _
                                           Err.Number & ":" & Err.Description & ":" & Err.Source
            
            On Error Goto 0
    
        End Function

    '--------------------------------------------------------------------------------------------------------------------
    '--## ADUserQuery.s.GetUser -----------------------------------------------------------------------------------------
    '--------------------------------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Creates and Loads a user object with LDAP properties.
    '--
    '--------------------------------------------------------------------------------------------------------------------
        Private Function getUser(ByVal strAdsUserPath)

            Const con_ADS_SECURE_AUTHENTICATION       = &H1 
            Const con_ADS_USE_ENCRYPTION              = &H2
            Const con_ADS_USE_SSL                     = &H2
            Const con_ADS_READONLY_SERVER             = &H4 
            Const con_ADS_PROMPT_CREDENTIALS          = &H8 
            Const con_ADS_NO_AUTHENTICATION           = &H10 
            Const con_ADS_FAST_BIND                   = &H20 
            Const con_ADS_USE_SIGNING                 = &H40 
            Const con_ADS_USE_SEALING                 = &H80 
            Const con_ADS_USE_DELEGATION              = &H100 
            Const con_ADS_SERVER_BIND                 = &H200
                    
            On Error Resume Next
            Err.Clear

            Dim objUser
            Dim strUserName
            If (Len(Trim(authorizedUserName)) = 0) Then
                strUserName = defaultUserName
                Set objUser = GetObject("LDAP:").OpenDSObject(strAdsUserPath, defaultUserName, defaultUserPassword, _
                                                              con_ADS_SERVER_BIND + con_ADS_USE_DELEGATION + con_ADS_SECURE_AUTHENTICATION)
           Else 
                strUserName = authorizedUserName
                Set objUser = GetObject("LDAP:").OpenDSObject(strAdsUserPath,authorizedUserName,authorizedUserPassword, _
                                                              con_ADS_SERVER_BIND + con_ADS_USE_DELEGATION + con_ADS_SECURE_AUTHENTICATION)
           End If

           If (Err) Then 
               Errors.AddItem 1, "CLASS.ActiveDirectoryUserQuery::getUser", "Error '" & _
                                 Err.Number & " -- " & Err.Description & " -- " & Err.Source & "' " & _
                                 "when accesing '" & strAdsUserPath & "' with user Id '" & strUserName & "'."
            Else
                Dim objADUser
                Set objADUser = New ActiveDirectoryUser
                With objADUser
            '-- Set Windows NT Properties
               '-- ADSI/LDAP Directory Keys and Qualifiers ----------------------------------------------------------------
                    .adsPath                       = objUser.adsPath
                    .distinguishedName             = objUser.distinguishedName
                    .cn                            = objUser.cn
                    .ou                            = GetCollectionValues(objUser.ou)
                    .o                             = GetCollectionValues(objUser.o)
                    .objectClass                   = GetCollectionValues(objUser.objectClass)
                    .objectCategory                = GetCollectionValues(objUser.objectCategory)
                '-- User Account Properties --------------------------------------------------------------------------------
                    .sAMAccountName                = objUser.sAMAccountName   
                    .primaryGroupID                = objUser.primaryGroupID
                    .seeAlso                       = objUser.seeAlso
                    .info                          = objUser.info
                '-- User Name Properties -----------------------------------------------------------------------------------    
                    .givenName                     = objUser.givenName  
                    .initials                      = objUser.initials   
                    .sn                            = objUser.sn         
                    .description                   = objUser.description
                    .personalTitle                 = objUser.personalTitle
                '-- Electronic Adresses ------------------------------------------------------------------------------------
                    .mail                          = GetCollectionValues(objUser.mail)                      
                    .url                           = GetCollectionValues(objUser.url)                       
                '-- Physical Addresses -------------------------------------------------------------------------------------
                    .physicalDeliveryOfficeName    = objUser.physicalDeliveryOfficeName
                    .street                        = objUser.street
                    .postOfficeBox                 = objUser.postOfficeBox
                    .l                             = objUser.l            
                    .st                            = objUser.st           
                    .postalCode                    = objUser.postalCode   
                    .countryCode                   = objUser.countryCode  
                    .c                             = objUser.c            
                    .co                            = objUser.co           
                    .postalAddress                 = objUser.postalAddress          
                    .street                        = objUser.street         
                '-- Telephone Numbers --------------------------------------------------------------------------------------
                    .telephoneNumber               = objUser.telephoneNumber           
                    .homePhone                     = objUser.homePhone                    
                    .pager                         = objUser.pager                        
                    .mobile                        = objUser.mobile                       
                    .facsimileTelephoneNumber      = objUser.facsimileTelephoneNumber   
    
            '-- Set Additional Windows 2000 Properties
                    If(GetServerVersion > 4) Then
                '-- ADSI/LDAP Directory Keys and Qualifiers ----------------------------------------------------------------
                    .canonicalName                 = objUser.canonicalName
                '-- User Account Properties --------------------------------------------------------------------------------
                    .scriptPath                    = objUser.scriptPath       
                    .homeDrive                     = objUser.homeDrive                
                    .homeDirectory                 = objUser.homeDirectory
                    .comment                       = objUser.comment
                    .userPrincipalName             = objUser.userPrincipalName
                    .profilePath                   = objUser.profilePath
                '-- User Name Properties -----------------------------------------------------------------------------------    
                    .displayName                   = objUser.displayName
                    .middleName                    = objUser.middleName
                '-- Electronic Adresses ------------------------------------------------------------------------------------
                    .wWWHomePage                   = GetCollectionValues(objUser.wWWHomePage)   
                    .proxyAddresses                = GetCollectionValues(objUser.proxyAddresses)           
                '-- Physical Addresses -------------------------------------------------------------------------------------
                    .registeredAddress             = objUser.registeredAddress      
                    .postOfficeBox                 = objUser.postOfficeBox          
                    .preferredDeliveryMethod       = objUser.preferredDeliveryMethod
                    .homePostalAddress             = objUser.homePostalAddress      
                    .otherMailbox                  = objUser.otherMailbox           
                    .streetAddress                 = objUser.streetAddress 
                    .msExchHomeServerName          = objUser.msExchHomeServerName
                '-- Telephone Numbers --------------------------------------------------------------------------------------
                    .otherTelephone                = GetCollectionValues(objUser.otherTelephone)            
                    .otherHomePhone                = GetCollectionValues(objUser.otherHomePhone)               
                    .otherPager                    = GetCollectionValues(objUser.otherPager)                   
                    .otherMobile                   = GetCollectionValues(objUser.otherMobile)                  
                    .telephoneAssistant            = objUser.telephoneAssistant  
                    .otherFacsimileTelephoneNumber = GetCollectionValues(objUser.otherFacsimileTelephoneNumber)
                    .ipPhone                       = objUser.ipPhone                      
                    .otherIpPhone                  = GetCollectionValues(objUser.otherIpPhone)
                '-- Organizational Information ------------------------------------------------------------------------------
                    .info                          = objUser.info                         
                    .title                         = objUser.title        
                    .department                    = objUser.department   
                    .company                       = objUser.company      
                    .manager                       = objUser.manager 
                    .secretary                     = objUser.msexchassistantname     
                    .directReports                 = GetCollectionValues(objUser.directReports)
                    .memberOf                      = GetCollectionValues(objUser.memberOf)
                    End If
                End With
                Set getUser = objADUser
                Set objADUser = Nothing
            
            '-- ## || ADUserQuery::getUser::2   
                If(Err) Then Errors.AddItem 2, "CLASS.ActiveDirectoryUserQuery::getUser", _
                                               Err.Number & " -- " & Err.Description & " -- " & Err.Source
            
            End If
            
            Set objUser = Nothing
            
            On Error Goto 0

        End Function


    '--------------------------------------------------------------------------------------------------------------------
    '--## ADUserQuery.s.GetCollectionValues -----------------------------------------------------------------------------
    '--------------------------------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Creates a semi-colon seperated list of collection values.
    '--
    '--------------------------------------------------------------------------------------------------------------------
        Private Function GetCollectionValues(colItems)
        
            Dim objItem
            Dim strItems

            On Error Resume Next
            Err.Clear
            
            Select Case VarType(colItems)
                   Case (8204)        
                        For Each objItem in colItems
                            strItems = strItems & objItem & ";"
                        Next
                        If(Len(strItems)>0) Then
                            GetCollectionValues = Left(strItems,Len(strItems)-1)
                        Else
                            GetCollectionValues = ""
                        End If
                   Case (8192) : GetCollectionValues = Join(colItems,";")
                   Case (10)   : GetCollectionValues = "ERROR: " & colItems.Description
                   Case Else   : GetCollectionValues = CStr(colItems)
            End Select
            
         '-- ## || ADUserQuery::getCollectionValues::1   
            If(Err) Then Errors.AddItem 1, "CLASS.ActiveDirectoryUserQuery::getCollectionValues", _
                                           Err.Number & " -- " & Err.Description & " -- " & Err.Source
            
            On Error Goto 0
        
        End Function

    '--------------------------------------------------------------------------------------------------------------------
    '--## ADUserQuery.f.GetServerVersion --------------------------------------------------------------------------------
    '--------------------------------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Gets the current IIS Server version.
    '--
    '--------------------------------------------------------------------------------------------------------------------
        Private Function getServerVersion
            On Error Resume Next
            Err.Clear
                getServerVersion = Int(Split(Request.ServerVariables("SERVER_SOFTWARE"),"/")(1))
             '-- ## || ADUserQuery::getServerVersion::1   
                If(Err) Then Errors.AddItem 1, "CLASS.ActiveDirectoryUserQuery::getServerVersion", _
                                            Err.Number & " -- " & Err.Description & " -- " & Err.Source
            On Error Goto 0
        End Function

    '--------------------------------------------------------------------------------------------------------------------
    '--## ADUserQuery.f.ValidateProperty --------------------------------------------------------------------------------
    '--------------------------------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Validate an LDAP Property.
    '--
    '--------------------------------------------------------------------------------------------------------------------
        Private Function validateProperty(ByVal strPropname)

            Dim strValidProperties
                strValidProperties = " cn canonicalName distinguishedName ou o AdsPath objectClass objectCategory " & _
                                     " userPrincipalName sAMAccountName profilePath scriptPath primaryGroupID " & _
                                     " homeDrive homeDirectory seeAlso givenName initials sn displayName " & _
                                     " description personalTitle middleName Name FullName mail otherMailbox " & _
                                     " proxyAddresses wWWHomePage url physicalDeliveryOfficeName street " & _
                                     " streetAddress postOfficeBox registeredAddress preferredDeliveryMethod " & _
                                     " homePostalAddress l st postalCode countryCode c co Address telephoneNumber " & _
                                     " otherTelephone homePhone otherHomePhone pager otherPager mobile otherMobile " & _
                                     " facsimileTelephoneNumber otherFacsimileTelephoneNumber ipPhone otherIpPhone " & _
                                     " info telephoneAssistant title department company manager directReports " & _
                                     " secretary comment memberOf msExchHomeServerName "

            If(InStr(LCase(strValidProperties)," " & LCase(Trim(strPropname)) & " ") > 0) Then
                validateProperty = true
            Else
                validateProperty = false
            End If

        End Function


    End CLASS
    
'-----------------------------------------------------------------------------------------------------------------------
'--## @ ADUser Description ---------------------------------------------------------------------------------------------
'-----------------------------------------------------------------------------------------------------------------------
'-- 
'-- This class is a single Active Directory user object. It contains all of the properties of a standard Microsoft
'-- Active Directory / LDAP user container. It has no methods or collections.
'--
'-----------------------------------------------------------------------------------------------------------------------

'-----------------------------------------------------------------------------------------------------------------------
'--## @ ADUser Object Model --------------------------------------------------------------------------------------------
'-----------------------------------------------------------------------------------------------------------------------
'--
'-- ** LDAP/AD PATH FORMAT: CN=B12345,OU=NeenahAdmin,OU=US,OU=NorthAmerica,OU=Accounts,DC=kcc,DC=com
'--
'-- ** All properties are typed as string.
'--
'-- ** an asterisk '*' in the '4' column indicates that the property is NOT available on windows NT4 servers. 
'--
'-- PROPERTIES ---------------------------------------------------------------------------------------------------------
'--
'-- 4 NAME                             FRIENDLY NAME        DESCRIPTION
'-- - ------------------------------   ------------------   ------------------------------------------------------------ 
'--   cn                               UserID               Canonical Name. (B12345)
'-- * canonicalName                    UserID               Canonical Name (B12345)
'--   distinguishedName                ADPath               Distinguished Name - AD Path (CN=,OU=,OU=,OU=,OU=,DC=,DC=)
'--   ou                                                    LDAP Organizational Unit Name
'--   o                                                     LDAP Organizational Name
'--   AdsPath                          LDAPPath             LDAP location in Active Directory (LDAP://CN=,OU=,...)
'--   objectClass                      LDAPClass            LDAP Object Class
'--   objectCategory                   LDAPCategory         LDAP Object Category
'-- * userPrincipalName                                     User Logon Name (<EMAIL>)
'--   sAMAccountName                   LogonName            Logon Name for Pre-2k (logonname)
'-- * profilePath                                           Path for the users Windows Profile
'-- * scriptPath                                            Path for the users startup scripts
'--   primaryGroupID                                        Numeric Group ID for users Primary KCUS Group. 
'-- * homeDrive                                             Drive letter mapped to user's home Directory.
'-- * homeDirectory                                         UNC path to the user's private folder.
'--   seeAlso                                               A Comment Field.
'--   givenName                        FirstName            First Name
'--   initials                                              Initials (FML), not middle initial.
'--   sn                               Surname,LastName     Surname, Last Name
'-- * displayName                      GALName              Display Name (LastName, FirstName MI)
'--   description                                           Description (FirstName MI LastName)
'--   personalTitle                                         Title (Mr. Ms, etc..)
'-- * middleName                                            Middle Name
'--   Name                                                  First and Last Name
'--   FullName                                              FirstName MiddleName LastName
'--   mail                             Email                E-Mail
'-- * otherMailbox
'-- * proxyAddresses                   emails               All Email Addresses
'-- * wWWHomePage                      WebPage              Web Page
'--   url                              WebPages             Web Page Other (a list)
'--   physicalDeliveryOfficeName       Office               Office
'--   street                                                Street Address
'-- * streetAddress                                         Street Address
'-- * postOfficeBox                    POBox                P.O. Box
'-- * registeredAddress
'-- * preferredDeliveryMethod
'-- * homePostalAddress
'--   l                                City                 City
'--   st                               State,Province       State/Province
'--   postalCode                       Zip,ZipCode          Zip/Postal Code
'--   countryCode                                           Country/region (pulldown)
'--   c                                Country              2-Digit Country Code (US)
'--   co                               CountryName          Country (UNITED STATES or USA)
'--   Address                                               Street, POBox, City, State, Zip
'--   telephoneNumber                  Phone,Telephone      Telephone Number
'-- * otherTelephone                   Phones,Telephones    Telephone Number Other (a list)
'--   homePhone                                             Home
'-- * otherHomePhone                   HomePhones           Home Other (a list)
'--   pager                                                 Pager
'-- * otherPager                       Pagers               Pager Other (a list)
'--   mobile                                                Mobile
'-- * otherMobile                      Mobiles              Mobile Other (a list)
'--   facsimileTelephoneNumber         Fax                  Fax
'-- * otherFacsimileTelephoneNumber    Faxes                Fax Other (a list)
'-- * ipPhone                                               IP Phone
'-- * otherIpPhone                     IPPhones             IP Phone Other (a list)
'-- * info                             Notes                Misceleanous Notes
'-- * telephoneAssistant               AssistantPhone       Secretary Phone                     
'-- * title                                                 Title
'-- * department                                            Department
'-- * company                                               Company
'-- * manager                                               Manager
'-- * directReports                                         Direct Reports (a list)
'-- * secretary                                             User's Secretary
'-- * comment
'-- * msExchHomeServerName                                  K-C Mailserver ADS Path (LDAP Standard Property)
'-- * exchangePath                                          K-C Mailserver ADS Path
'-- * exchangeServer                                        K-C Mailserver Name
'-- * memberOf                                              A semi-colon delimited list of AD Group paths (LDAP Standard)
'-- * groupADsPaths                                         A semi-colon delimited list of AD Group Paths for a user.
'-- * groupNames                                            A semi-colon delimited list of AD Group names for a user.
'--   AllProperties                                         A summary of all standard LDAP properties for the object
'--   UserSummary                                           A user summary of Name, address, office locations and phones
'--  
'-----------------------------------------------------------------------------------------------------------------------

    CLASS ActiveDirectoryUser

    '-------------------------------------------------------------------------------------------------------------------
    '--## @ ADUser Properties ------------------------------------------------------------------------------------------
    '-------------------------------------------------------------------------------------------------------------------

    '-------------------------------------------------------------------------------------------------------------------
    '--## ADUser.P.Base - EXPOSED - ADSI PROPERTIES MAPPED TO COMMON NAMES ---------------------------------------------
    '-------------------------------------------------------------------------------------------------------------------
    '-- Error Information
        Public Errors                        '-- Errors collection for object.
    '-- ADSI Authentication Information
        Public AuthUser                      '-- The user to authenticate to AD as.
        Public AuthPassword                  '-- The password to authenticate to AD with.
    '-- ADSI Key Information -------------------------------------------------------------------------------------------
        Public cn                            '-- Canonical Name. (B12345)
        Public canonicalName                 '-- Canonical Name (B12345)
        Public distinguishedName             '-- Distinguished Name (CN=B12345,OU=Users,DC=kcc,DC=com)
        Public ou                            '-- LDAP Organizational Unit Name
        Public o                             '-- LDAP Organizational Name
        Public AdsPath                       '-- LDAP location in Active Directory.
        Public objectClass                   '-- LDAP Object Class
        Public objectCategory                '-- LDAP Object Category
    '-- ADSI User Accound Information ----------------------------------------------------------------------------------
        Public userPrincipalName             '-- User Logon Name (<EMAIL>)
        Public sAMAccountName                '-- Logon Name for Pre-2k (logonname)
        Public profilePath                   '-- Path for the users Windows Profile
        Public scriptPath                    '-- Path for the users startup scripts
        Public primaryGroupID                '-- Numeric Group ID for users Primary KCUS Group. 
        Public homeDrive                     '-- Drive letter mapped to user's home Directory.
        Public homeDirectory                 '-- UNC path to the user's private folder.
        Public msExchHomeServerName
        Public memberOf                      '-- DIRECT groups that a user is a member of - doesn't show nested groups
        Public seeAlso                       '-- A Comment Field.
    '-- User Name Information ------------------------------------------------------------------------------------------                                            
        Public givenName                     '-- First Name
        Public initials                      '-- Initials (FML), not middle initial.
        Public sn                            '-- Last Name
        Public displayName                   '-- Display Name
        Public description                   '-- Description
        Public personalTitle                 '-- Title (Mr. Ms, etc..)
        Public middleName                    '-- Middle Name
    '-- Electronic Addresses -------------------------------------------------------------------------------------------
        Public mail                          '-- E-Mail
        Public proxyAddresses                '-- Other Email Addresses
        Public wWWHomePage                   '-- Web Page
        Public url                           '-- Web Page Other (a list)
    '-- Pysical Addresses Information ----------------------------------------------------------------------------------
        Public physicalDeliveryOfficeName    '-- Office
        Public street                        '-- Street
        Public streetAddress
        Public postOfficeBox                 '-- P.O. Box
        Public l                             '-- City
        Public st                            '-- State/Province
        Public postalCode                    '-- Zip/Postal Code
        Public countryCode                   '-- Country/region (pulldown)
        Public c                             '-- Country (US)
        Public co                            '-- Country (UNITED STATES)
        Public registeredAddress
        Public postalAddress
        Public preferredDeliveryMethod
        Public homePostalAddress
        Public otherMailbox
    '-- ADSI Telephones Information -------------------------------------------------------------------------------------                                                            
        Public telephoneNumber               '-- Telephone Number
        Public otherTelephone                '-- Telephone Number Other (a list)
        Public homePhone                     '-- Home
        Public otherHomePhone                '-- Home Other (a list)
        Public pager                         '-- Pager
        Public otherPager                    '-- Pager Other (a list)
        Public mobile                        '-- Mobile
        Public otherMobile                   '-- Mobile Other (a list)
        Public facsimileTelephoneNumber      '-- Fax
        Public otherFacsimileTelephoneNumber '-- Fax Other (a list)
        Public ipPhone                       '-- IP Phone
        Public otherIpPhone                  '-- IP Phone Other (a list)
        Public info                          '-- Notes
        Public comment
        Public telephoneAssistant         
    '-- Adsi Organization Tab -------------------------------------------------------------------------------------------                                            
        Public title                         '-- Title
        Public department                    '-- Department
        Public company                       '-- Company
        Public manager                       '-- Manager
        Public directReports                 '-- Direct Reports (a list)
        Public secretary                     '-- User's Secretary

    '--------------------------------------------------------------------------------------------------------------------
    '--## ADUser.P.Friendly - EXPOSED - ADSI PROPERTIES MAPPED TO COMMON NAMES ------------------------------------------
    '--------------------------------------------------------------------------------------------------------------------
        Public Property Get UserId           : UserId = cn                                                 : End Property
        Public Property Get ADPath           : ADPath = distinguishedName                                  : End Property                                                           
        Public Property Get LDAPPath         : LDAPPath = AdsPath                                          : End Property                                                    
        Public Property Get LDAPClass        : LDAPClass = objectClass                                     : End Property
        Public Property Get LDAPCategory     : LDAPCategory = objectCategory                               : End Property
        Public Property Get LogonName        : LogonName = sAMAccountName                                  : End Property        
        Public Property Get FirstName        : FirstName = givenName                                       : End Property
        Public Property Get LastName         : LastName = sn                                               : End Property
        Public Property Get Surname          : Surname = sn                                                : End Property
        Public Property Get GALName          : GALName = displayName                                       : End Property
        Public Property Get Email            : Email = mail                                                : End Property
        Public Property Get Emails           : proxyAddresses                                              : End Property
        Public Property Get WebPage          : WebPage = wWWHomePage                                       : End Property
        Public Property Get WebPages         : WebPages = udfmakeList(wWWHomePage,url)                     : End Property
        Public Property Get Office           : Office = physicalDeliveryOfficeName                         : End Property
        Public Property Get POBox            : POBox = postOfficeBox                                       : End Property
        Public Property Get City             : City = l                                                    : End Property
        Public Property Get State            : State = st                                                  : End Property
        Public Property Get Province         : Province = st                                               : End Property
        Public Property Get Zip              : Zip = postalCode                                            : End Property
        Public Property Get ZipCode          : ZipCode = postalCode                                        : End Property
        Public Property Get Country          : Country = c                                                 : End Property
        Public Property Get CountryName      : CountryName = co                                            : End Property
        Public Property Get phone            : phone = telephoneNumber                                     : End Property
        Public Property Get phones           : phones = udfmakeList(telephoneNumber,otherTelephone)        : End Property
        Public Property Get telephone        : telephone = telephoneNumber                                 : End Property
        Public Property Get telephones       : telephones = udfmakeList(telephoneNumber,otherTelephone)    : End Property
        Public Property Get homephones       : homephones = udfmakeList(homePhone,otherHomePhone)          : End Property
        Public Property Get pagers           : pagers = udfmakeList(pager,otherPager)                      : End Property
        Public Property Get mobiles          : mobiles = udfmakeList(mobile,otherMobile)                   : End Property
        Public Property Get fax              : fax = facsimileTelephoneNumber                              : End Property
        Public Property Get faxes            : faxes = udfmakeList(fax, otherFacsimileTelephoneNumber)     : End Property
        Public Property Get ipphones         : ipphones = udfmakeList(ipPhone, otherIpPhone)               : End Property
        Public Property Get AssistantPhone   : AssistantPhone = telephoneAssistant                         : End Property
        Public Property Get SecretaryPhone   : SecretaryPhone = telephoneAssistant                         : End Property
        Public Property Get Assistant        : Assistant = secretary                                       : End Property
        Public Property Get groupADsPaths    : groupADsPaths = memberOf                                    : End Property
        Public Property Get exchangePath     : exchangePath = msExchHomeServerName                         : End Property
         

    '--------------------------------------------------------------------------------------------------------------------
    '--## ADUser.P.Custom - EXPOSED - ADD ADDITIONAL PROPERTIES TO STANDARD SET -----------------------------------------
    '--------------------------------------------------------------------------------------------------------------------
        Public Property Get FullName         : FullName = givenName & " " & middleName & " " & sn          : End Property
        Public Property Get Name             : Name = givenName & " " & sn                                 : End Property

        Public Property Get exchangeServer 
            Dim arrTemp
            If (Len(msExchHomeServerName)>0) Then
            '-- /o=KCC/ou=MSXSITE00/cn=Configuration/cn=Servers/cn=USTCAX35
                arrTemp = Split(msExchHomeServerName,"=")
                exchangeServer = arrTemp(UBound(arrTemp))
            Else
                exchangeServer = ""
            End If
        End Property       

        Public Property Get Address
            Address = streetAddress & vbCrLf
            If(Len(postOfficeBox)>0) Then Address = Address & postOfficeBox & vbCrLf
            Address = Address & City & ", " & State & "  " & ZipCode
        End Property

        Public Property Get groupNames
            Dim strGroupName, strGroupNames
            For Each strGroupName in Split(memberOf,";")
                strGroupNames = strGroupNames & Mid(strGroupName,4,InStr(strGroupName,",OU=")-4) & ";"
            Next
            groupNames = Left(strGroupNames, Len(strGroupNames)-1)                          
        End Property
        
        Public Property Get UserSummary
            UserSummary = "USER SUMMARY:" & vbcrlf & _
                          "    User ID: "                       & sAMAccountName & vbCrLf & _
                          "    Name: "                          & Name & vbCrLf & _                           
                          "    Initials: "                      & initials & vbCrLf & _                      
                          "    Description: "                   & description & vbCrLf & _                   
                          "    Notes: "                         & info & vbCrLf & _                          
                          "    E-Mail Address: "                & email & vbCrLf & _                          
                          "    WWW Home Page: "                 & wWWHomePage & vbCrLf & _ 
                          "    Work Phone Number: "             & telephoneNumber & vbCrLf & _   
                          "    Home Phone Number: "             & homePhone & vbCrLf & _
                          "    Mobile Phone Number: "           & mobile & vbCrLf & _                     
                          "    Pager Number: "                  & pager & vbCrLf & _                         
                          "    Fax: "                           & fax & vbCrLf & _      
                          "    Title: "                         & title & vbCrLf & _                         
                          "    Department: "                    & department & vbCrLf & _                    
                          "    Company: "                       & company & vbCrLf & _                       
                          "    Manager: "                       & manager & vbCrLf & _                     
                          "    Direct Reports: "                & directReports & vbCrLf & _
                          "    Office: "                        & office & vbCrLf & _    
                          "    Street Address: "                & streetAddress & vbCrLf & _                 
                          "    P. O. Box: "                     & postOfficeBox & vbCrLf & _                 
                          "    City: "                          & l & vbCrLf & _                             
                          "    State: "                         & st & vbCrLf & _                            
                          "    Zip Code: "                      & postalCode & vbCrLf & _                    
                          "    Country: "                       & co & vbCrLf                           
        End Property
    '--------------------------------------------------------------------------------------------------------------------
    '--## ADUser.M.Functions --------------------------------------------------------------------------------------------
    '--------------------------------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Utility functions for the class.
    '--
    '--------------------------------------------------------------------------------------------------------------------
        Private Function udfmakeList(strSingle, strList)
            
            If (strSingle = "" AND strList = "") Then 
                udfmakeList = ""
            ElseIf (strSingle > "" AND strList = "") Then
                udfmakeList = strSingle
            ElseIf (strSingle = "" AND strList > "") Then
                udfmakeList = strList
            ElseIf (strSingle > "" AND strList > "") Then
                udfmakeList = strSingle & ";" & strList 
            Else
                udfmakeList = "unhandled condition"  
            End If

        End Function

    '--------------------------------------------------------------------------------------------------------------------
    '--## ADUser.P.Debugging --------------------------------------------------------------------------------------------
    '--------------------------------------------------------------------------------------------------------------------
    '--
    '-- DESCRIPTION: Returns a text string of all the object's real LDAP Properties (mainly used for debugging).
    '--
    '--------------------------------------------------------------------------------------------------------------------
        Public Property Get AllProperties
        
            AllProperties = "LDAP DIRECTORY INFORMATION"           & vbcrlf & _
                             "    distinguishedName: "             & distinguishedName & vbCrLf & _ 
                             "    AdsPath: "                       & AdsPath & vbCrLf & _                          
                             "    cn: "                            & cn & vbCrLf & _             
                             "    canonicalName: "                 & canonicalName & vbCrLf & _                     
                             "    ou: "                            & ou & vbCrLf & _                      
                             "    o: "                             & o & vbCrLf & _                            
                             "    objectClass: "                   & objectClass & vbCrLf & _                   
                             "    objectCategory: "                & objectCategory & vbCrLf  & _
                             "USER ACCOUNT INFORMATION"            & vbcrlf & _
                             "    sAMAccountName: "                & sAMAccountName & vbCrLf & _ 
                             "    userPrincipalName: "             & userPrincipalName & vbCrLf & _
                             "    profilePath: "                   & profilePath & vbCrLf & _                          
                             "    scriptPath: "                    & scriptPath & vbCrLf & _             
                             "    primaryGroupID: "                & primaryGroupID & vbCrLf & _                      
                             "    homeDrive: "                     & homeDrive & vbCrLf & _             
                             "    homeDirectory: "                 & homeDirectory & vbCrLf & _                     
                             "    exchangeServer: "                & exchangeServer & vbCrLf & _  
                             "    exchangePath: "                  & exchangePath & vbCrLf & _
                             "    seeAlso: "                       & seeAlso & vbCrLf & _
                             "    comment: "                       & comment & vbCrLf & _
                             "    info: "                          & info & vbCrLf & _
                             "USER NAME INFORMATION"               & vbcrlf & _                    
                             "    givenName: "                     & givenName & vbCrLf & _ 
                             "    sn: "                            & sn & vbCrLf & _                          
                             "    initials: "                      & initials & vbCrLf & _             
                             "    displayName: "                   & displayName & vbCrLf & _                     
                             "    description: "                   & description & vbCrLf & _                      
                             "    personalTitle: "                 & personalTitle & vbCrLf & _                            
                             "    middleName: "                    & middleName & vbCrLf & _                 
                             "ELECTRONIC ADDRESSES"                & vbcrlf & _                    
                             "    mail: "                          & mail & vbCrLf & _ 
                             "    proxyAddresses: "                & proxyAddresses & vbCrLf & _
                             "    wWWHomePage: "                   & wWWHomePage & vbCrLf & _
                             "    url: "                           & url & vbCrLf & _   
                             "PHYSICAL ADDRESSES"                  & vbcrlf & _ 
                             "    physicalDeliveryOfficeName: "    & physicalDeliveryOfficeName & vbCrLf & _                          
                             "    street: "                        & street & vbCrLf & _                          
                             "    streetAddress: "                 & streetAddress & vbCrLf & _                          
                             "    postOfficeBox: "                 & postOfficeBox & vbCrLf & _                          
                             "    l: "                             & l & vbCrLf & _                          
                             "    st: "                            & st & vbCrLf & _
                             "    postalCode: "                    & postalCode & vbCrLf & _
                             "    countryCode: "                   & countryCode & vbCrLf & _
                             "    c: "                             & c & vbCrLf & _
                             "    co: "                            & co & vbCrLf & _
                             "    registeredAddress: "             & registeredAddress & vbCrLf & _
                             "    postalAddress: "                 & postalAddress & vbCrLf & _
                             "    postOfficeBox: "                 & postOfficeBox & vbCrLf & _
                             "    preferredDeliveryMethod: "       & preferredDeliveryMethod & vbCrLf & _
                             "    homePostalAddress: "             & homePostalAddress & vbCrLf & _
                             "    otherMailbox: "                  & otherMailbox & vbCrLf & _
                             "TELEPHONE NUMBERS"                   & vbcrlf & _
                             "    telephoneNumber: "               & telephoneNumber & vbCrLf & _
                             "    otherTelephone: "                & otherTelephone & vbCrLf & _
                             "    telephones: "                    & telephones & vbCrLf & _
                             "    homePhone: "                     & homePhone & vbCrLf & _
                             "    otherHomePhone: "                & otherHomePhone & vbCrLf & _
                             "    homePhones: "                    & homePhones & vbCrLf & _
                             "    pager: "                         & pager & vbCrLf & _
                             "    otherPager: "                    & otherPager & vbCrLf & _
                             "    pagers: "                        & pagers & vbCrLf & _
                             "    mobile: "                        & mobile & vbCrLf & _
                             "    otherMobile: "                   & otherMobile & vbCrLf & _
                             "    mobiles: "                       & mobiles & vbCrLf & _
                             "    facsimileTelephoneNumber: "      & facsimileTelephoneNumber & vbCrLf & _
                             "    otherFacsimileTelephoneNumber: " & otherFacsimileTelephoneNumber & vbCrLf & _
                             "    faxes: "                         & faxes & vbCrLf & _
                             "    ipPhone: "                       & ipPhone & vbCrLf & _
                             "    otherIpPhone: "                  & otherIpPhone & vbCrLf & _
                             "    ipPhones: "                      & ipPhones & vbCrLf & _
                             "ORGANIZATIONAL INFORMATION"          & vbcrlf & _
                             "    title: "                         & title & vbCrLf & _
                             "    department: "                    & department & vbCrLf & _
                             "    company: "                       & company & vbCrLf & _
                             "    manager: "                       & manager & vbCrLf & _
                             "    directReports: "                 & directReports & vbCrLf & _
                             "    secretary: "                     & secretary & vbcrlf & _
                             "GROUP INFORMATION"                   & vbcrlf & _
                             "    groupNames: "                    & Replace(groupNames,";",";" & vbCrLf & String(16," ")) & vbCrLf & _
                             "    groupADsPaths: "                 & Replace(groupADsPaths,";",";" & vbCrLf & String(19," ")) & vbCrLf
        End Property
    
    End Class
        
'-----------------------------------------------------------------------------------------------------------------------
'--## # SUPPORTING CLASSES ---------------------------------------------------------------------------------------------
'-----------------------------------------------------------------------------------------------------------------------

'-----------------------------------------------------------------------------------------------------------------------
'--## @ ErrorItems Description -----------------------------------------------------------------------------------------
'-----------------------------------------------------------------------------------------------------------------------
'-- 
'-- DESCRIPTION: Supporting classes used to provide error information for the ActiveDirectoryUserQuery object.
'--
'-----------------------------------------------------------------------------------------------------------------------

    CLASS ErrorItems

        Public Items

    	Private Sub Class_Initialize
           Set Items = CreateObject("Scripting.Dictionary")
    	End Sub

        Public Sub AddItem(ByVal intItemNumber, ByVal strSource, ByVal strItemDescription)
          Dim objItem
          Set objItem = New ErrorItem
              objItem.Number = CStr(intItemNumber)
              objItem.Description = CStr(strItemDescription)
              objItem.Source = CStr(strSource)
              Set Items(objItem) = objItem
          Set objItem = Nothing
        End Sub
        
        Public Sub Clear
            Items.RemoveAll
        End Sub

    End CLASS

    CLASS ErrorItem
    
        Public Number
        Public Description
        Public Source
        
    End CLASS
    
%>
