																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Edit Seal Number </TITLE>

<!--#include file="classes/asp_cls_headerOYM.asp"-->
<!--#include file="classes/asp_cls_SessionStringOYM.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->

<!--#include file="classes/asp_cls_DataAccessOYM.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureOYM.asp"-->


<% Dim MyRec, strsql, MyRec2, strsql2
strid = request.querystring("id")
strpage = request.querystring("p")

 

  set objGeneral = new ASP_CLS_General
  
  If strpage = "M" then

   strsql = "SELECT Master_Table.* FROM Master_Table  WHERE MID = " & strid
   elseif strpage = "B" then
     strsql = "SELECT Master_Table_Backup.* FROM Master_Table_Backup  WHERE MID = " & strid

	else
   strsql = "SELECT Archive_Table.* FROM ARchive_table  WHERE MID = " & strid
end if
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

If not MyRec.eof then
  

  

  if objGeneral.IsSubmit() Then	
  Dim strCarrier
  
 
  	
  If len(request.form("Seal")) > 1 then	
 strSeal = Request.form("Seal")
 else
 strSeal = ""
 end if 
 
        If strpage = "M" then

	strsql =  "Update Master_table  set Seal = '" & strSeal & "' where MID = " & strid	
	
   elseif strpage = "B" then
  	strsql =  "Update Master_table_Backup  set Seal = '" & strSeal & "' where MID = " & strid	

	else
  	strsql =  "Update Archive_table  set Seal = '" & strSeal & "' where MID = " & strid	

end if
  	
  	

	
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
Response.redirect("OYM_Select_Seal.asp")		
end if

	
%>

<style type="text/css">
.style3 {
	border-style: solid;
	border-width: 1px;
}
.style6 {
	font-family: arial;
	font-size: x-small;
	background-color: #FFFFD7;
}
</style>
</head>

<body>
<br>
	
<form name="form1" action="Seal_Edit.asp?p=<%= strpage%>&id=<%= strid%>" method="post" ID="Form1">
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Edit Seal Number </b></font></td>
<td align = right><font face="Arial"><a href="javascript:history.go(-1);">Return</a>&nbsp;</td>


</tr>
	    </table>



	<p>&nbsp;</p>
	<table id="table1" style="width: 45%" align="center" class="style3">
<tr bgcolor = #CCCCFF>
		<td bgcolor="#FFFFD7" class="style6" style="height: 6px">

Trailer</td>
		<td bgcolor="#FFFFD7" class="style6" style="height: 6px">

Seal Number</td>
	</tr>
	<tr>
		<td><font face = arial size = 2><%= MyRec.fields("Trailer") %>&nbsp;</td>
		<td><font face = arial size = 1>
		<input type="text" name="Seal" size="20" value="<%= MyRec.fields("Seal") %>" style="width: 235px"></td>
	</tr>
</table>
<br>
		

<p align="center"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></p>
</form>
<%
End if
 MyRec.close %><!--#include file="OYMfooter.inc"-->