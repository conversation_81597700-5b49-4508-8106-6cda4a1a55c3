<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">


<TITLE>Select Virgin Fiber Inbound</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

 <%    Dim objDAC, strSQL, MyRec, objMOC, rstFiber, strTrailer, strR, strID
	Dim gSpecies, gSap_Nbr, gVendor, gPO, gRelease, gOID, gGenerator, gCity, gState
       Dim objGeneral, strDate, MyConn
strDate = formatdatetime(Now(),2)
	
	
  set objGeneral = new ASP_CLS_General
  Call getData()   
if objGeneral.IsSubmit() Then

strR = request.form("Release")



	Response.redirect("VF_Login.asp?id=" & strR)
	


End if
%>
<SCRIPT LANGUAGE="JavaScript">
function formHandler(Form1){
document.forms["Form1"].submit();
}
</SCRIPT>

<body>
<form name="form1" action="SelectVFInbound.asp" method="post" >

<p>&nbsp;</p>
<table border="1" cellpadding="0"  cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="75%" id="AutoNumber2" align = center>

       <TD align = center bgcolor="#FFFFE8">  <p>&nbsp;</p> <font face="Arial" size="3"><b>
		Select Inbound VF Trailer/Car </b></font>&nbsp;&nbsp;<br><br>
     <font face="Arial">
     	<font size="2"> &nbsp;</font><select name="Release">
 	<option value="" selected>-----Select-----</option>
      <%= objGeneral.OptionListAsString(rstFiber, "VID", "TS", strTrailer) %>
     </select><br>
		<br><br><p align="center">
     <b><a href="Generic_Receipt_rail_VF.asp">If Load's Car Number is not an option, 
Click Here</a></b></p>



		<p>&nbsp;</p> 
<p align="center"><input type="submit" value="Continue" id=submit1 name=submit1><br>&nbsp;</TD>

       </tr>

</table>
</form>


<p align="center">
     &nbsp;</p>



<%

 Function GetData()
        set objMOC = new ASP_CLS_FIBER
        set rstFiber = objMOC.FiberVFRelease()
         End Function  
%><!--#include file="Fiberfooter.inc"-->