 

<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Fibers Waste Paper Consumption Report based on YMS Outings</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

<%  
	Dim strsQL, rstEquip

   	Dim objEquipSearch, rstTotals, strCount, strDed, strNet, strTons, rstTotalReceived, strTR, objNew, rstSpecies
  	Dim  strVendor, strBegDate, strEndDate,   strDateReceived, strDateAdded, strAVG, strKCOPTotal, strOCCTotal
 	Dim  rstVendor, strFIQ, strBAQ, rstGenerator, strGenerator, strVendorname, strGeneratorname
  	Dim objGeneral, gcount, strCountTwo, strSum, strSum1, strNetSum, strDepdate, strEdate, strBdate, strBegTime, strEndTime
   	
   	Dim intDirection
  	Dim intPageNumber
   	Dim strPageNav

   intPageNumber = 1

   set objGeneral = new ASP_CLS_General

  if objGeneral.IsSubmit() Then
    Call GetFormData()
         
  end if
Call GetData()
%>



<style type="text/css">
.style1 {
	font-size: x-small;
}
.style3 {
	font-weight: bold;
	border-width: 1px;
}
.style4 {
	font-family: Arial, Helvetica, sans-serif;
}
.style5 {
	font-family: Arial, Helvetica, sans-serif;
	border-width: 1px;
	background-color: #FFFFCC;
}
.style6 {
	font-family: Arial, Helvetica, sans-serif;
	border-width: 1px;
	background-color: #FFECEC;
}
.style7 {
	font-family: Arial, Helvetica, sans-serif;
	border-width: 1px;
	background-color: #EAFFFF;
}
.style8 {
	font-family: Arial, Helvetica, sans-serif;
	text-align: center;
}
.auto-style1 {
	font-family: Arial, Helvetica, sans-serif;
	border-width: 1px;
	background-color: #FFFFEA;
}
.auto-style2 {
	text-align: center;
}
.auto-style3 {
	border-style: solid;
	border-width: 1px;
}
</style>
</head>


<form name="form1" action="Inventory_reduction_Summary.asp" method="post">
 
<TABLE borderColor=white cellSpacing=0 cellPadding=0  width = 100% border=1 >  
  <tr>
  <td class="auto-style2"><b><font face = "Arial">Fibers Waste Paper Consumption Report based on YMS Outings</font></b></td>
	<td align = right><font face="Arial"><b><a href="Loginscreen.asp">HOME</a></b></font></td></tr>
</table><br>

 

<TABLE borderColor=white cellSpacing=0 cellPadding=0 align = CENTER id="table2" class="auto-style3" style="width: 60%">  

  <TR>
   
	<TD class="style3" ><font face="Arial" size="2">Beg Date:</font></TD>
<TD class="style3" ><font face="Arial"><input name="Beg_Date" size="10" maxlength="10" value="<%=strBDate%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633">
<font face="Arial" size="2">	&nbsp;<strong> Time: </strong>&nbsp;
<select size="1" name="Beg_time">
<option <% if strBegTime = "1:00:00 AM" then %> selected <% end if %> >1:00:00 AM</option>
<option <% if strBegTime = "2:00 AM" then %> selected <% end if %> >2:00 AM</option>
<option <% if strBegTime = "3:00 AM" then %> selected <% end if %> >3:00 AM</option>
<option <% if strBegTime = "4:00 AM" then %> selected <% end if %> >4:00 AM</option>
<option <% if strBegTime = "5:00 AM" then %> selected <% end if %> >5:00 AM</option>
<option <% if strBegTime = "6:00 AM" then %> selected <% end if %> >6:00 AM</option>
<option <% if strBegTime = "7:00 AM" then %> selected <% end if %> >7:00 AM</option>
<option <% if strBegTime = "8:00 AM" then %> selected <% end if %> >8:00 AM</option>
<option <% if strBegTime = "9:00 AM" then %> selected <% end if %> >9:00 AM</option>
<option <% if strBegTime = "10:00 AM" then %> selected <% end if %> >10:00 AM</option>
<option <% if strBegTime = "11:00 AM" then %> selected <% end if %> >11:00 AM</option>
<option <% if strBegTime = "12:00 PM" then %> selected <% end if %> >12:00 PM</option>
<option <% if strBegTime = "1:00 PM" then %> selected <% end if %> >1:00 PM</option>
<option <% if strBegTime = "2:00 PM" then %> selected <% end if %> >2:00 PM</option>
<option <% if strBegTime = "3:00 PM" then  %> selected <% end if %> >3:00 PM</option>
<option <% if strBegTime = "4:00 PM" then %> selected <% end if %> >4:00 PM</option>
<option <% if strBegTime = "5:00 PM" then  %> selected <% end if %> >5:00 PM</option>
<option <% if strBegTime = "6:00 PM" then  %> selected <% end if %> >6:00 PM</option>
<option <% if strBegTime = "7:00 PM" then  %> selected <% end if %> >7:00 PM</option>
<option <% if strBegTime = "8:00 PM" then%> selected <% end if %> >8:00 PM</option>
<option <% if strBegTime = "9:00 PM" then%> selected <% end if %> >9:00 PM</option>
<option <% if strBegTime = "10:00 PM" then %> selected <% end if %> >10:00 PM</option>
<option <% if strBegTime = "11:00 PM" then %> selected <% end if %> >11:00 PM</option>
<option <% if strBegTime = "12:00 AM" then %> selected <% end if %> >12:00 AM</option>

     </select></TD>



     <td class="style3"><font size="2" face="Arial">&nbsp; End Date</font></td>
   <TD class="style3" ><font face="Arial"><input name="End_Date" size="10" maxlength="10" value="<%=strEDate%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633">&nbsp;&nbsp;&nbsp;
		<strong><span class="style1">Time</span></strong>:&nbsp;   
		<select size="1" name="End_time">
<option <% if strEndTime = "1:00:00 AM" then %> selected <% end if %> >1:00:00 AM</option>
<option <% if strEndTime = "2:00 AM" then %> selected <% end if %> >2:00 AM</option>
<option <% if strEndTime = "3:00 AM" then %> selected <% end if %> >3:00 AM</option>
<option <% if strEndTime = "4:00 AM" then %> selected <% end if %> >4:00 AM</option>
<option <% if strEndTime = "5:00 AM" then %> selected <% end if %> >5:00 AM</option>
<option <% if strEndTime = "6:00 AM" then %> selected <% end if %> >6:00 AM</option>
<option <% if strEndTime = "7:00 AM" then %> selected <% end if %> >7:00 AM</option>
<option <% if strEndTime = "8:00 AM" then %> selected <% end if %> >8:00 AM</option>
<option <% if strEndTime = "9:00 AM" then %> selected <% end if %> >9:00 AM</option>
<option <% if strEndTime = "10:00 AM" then %> selected <% end if %> >10:00 AM</option>
<option <% if strEndTime = "11:00 AM" then %> selected <% end if %> >11:00 AM</option>
<option <% if strEndTime = "12:00 PM" then %> selected <% end if %> >12:00 PM</option>
<option <% if strEndTime = "1:00 PM" then %> selected <% end if %> >1:00 PM</option>
<option <% if strEndTime = "2:00 PM" then %> selected <% end if %> >2:00 PM</option>
<option <% if strEndTime = "3:00 PM" then  %> selected <% end if %> >3:00 PM</option>
<option <% if strEndTime = "4:00 PM" then %> selected <% end if %> >4:00 PM</option>
<option <% if strEndTime = "5:00 PM" then  %> selected <% end if %> >5:00 PM</option>
<option <% if strEndTime = "6:00 PM" then  %> selected <% end if %> >6:00 PM</option>
<option <% if strEndTime = "7:00 PM" then  %> selected <% end if %> >7:00 PM</option>
<option <% if strEndTime = "8:00 PM" then%> selected <% end if %> >8:00 PM</option>
<option <% if strEndTime = "9:00 PM" then%> selected <% end if %> >9:00 PM</option>
<option <% if strEndTime = "10:00 PM" then %> selected <% end if %> >10:00 PM</option>
<option <% if strEndTime = "11:00 PM" then %> selected <% end if %> >11:00 PM</option>
<option <% if strEndTime = "12:00 AM" then %> selected <% end if %> >12:00 AM</option>
     </select></td>
    <TD  align="right"><input type="submit" value="Search"></TD>
</TR>

	</table>
</form>


  <% if objGeneral.IsSubmit() Then 

%>
   <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>


 <td bgcolor = white align="center" >&nbsp;</font></td>

     <td bgcolor = white  align="right" ><font face="Arial" size = 2> <b>
 <a href="Inventory_reduction_Summary_Excel.asp?b=<%= strBegDate %>&e=<%= strEndDate %>">Excel</a>  &nbsp;&nbsp;&nbsp;
        </b></font></td>   </tr></table>
    
    <%   
   DIm MyConn,  strPMX,  strBroke, strOCC, strROCC, strMXP,  strSWL, strPMorf, strSHREDOCC
 
    strPMX = 0
 	strHWM  = 0
 	 	strLPSBS = 0
    strBroke = 0
    strOCC = 0
    strROCC = 0
    strMXP = 0
 
    strSWL = 0
    strPMorf = 0
    strHBX = 0
    strSHRED = 0 
    strSWL = 0
    strUSBS = 0
    strOF3 = 0
    strKBLD = 0
    strSHREDOCC = 0
     strKBLDOCC = 0
      strHBXOCC = 0
       strPMXOCC = 0
        strOF3OCC = 0
        strSWLOCC = 0
        strMXPOCC = 0
        strUSBSOCC = 0
        strLPSBSOCC = 0
        strHWMOCC = 0
        
      
  ''''RF totals
  
    strsql = "SELECT Count(tblCars.CID) AS CountOfCID, Sum(tblCars.Net) AS SumOfNet, tblCars.Species FROM tblCars "_
	&" WHERE Date_Unloaded > '" & strBegDate & "' and Inv_depletion_date  <= '" & strEndDate & "'  and location ='RF' "_
	&" GROUP BY tblCars.Species "_
	&" ORDER BY tblCars.Species"
	
	   Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
    IF not MyConn.eof  then


    while not MyConn.eof 
  
    If MyConn("Species") = "SWL" then
    strSWL = MYConn("SumofNet")
    elseif   Trim(MyConn("Species")) = "HBX"  then
   strHBX= MYConn("SumofNet") 
elseif   Trim(MyConn("Species")) = "SHRED"  then
   strSHRED= MYConn("SumofNet") 

 
  elseif      Trim(MyCOnn("Species")) = "MXP"  then
    strMXP = MYConn("SumofNet")
    
    elseif     MyConn("Species") = "OF3"  then
    strOF3 = MYConn("SumofNet")
    
  elseif    MyConn("Species") = "KBLD" then
    strKBLD = MYConn("SumofNet")

elseif     left(MyCOnn("Species"),3) = "PMX"  then
    strPMX = MYConn("SumofNet")
    
elseif     MyCOnn("Species") = "USBS"  then
    strUSBS= MYConn("SumofNet")
elseif     MyCOnn("Species") = "HWM"  then
    strHWM= MYConn("SumofNet")
elseif     MyCOnn("Species") = "LPSBS"  then
    strLPSBS= MYConn("SumofNet")
    end if
 MyConn.movenext
    wend
    end if
    MyConn.close 
    
      
      
    
    strsql = "SELECT Count(tblCars.CID) AS CountOfCID, Sum(tblCars.Net) AS SumOfNet, tblCars.Species FROM tblCars "_
	&" WHERE Date_Unloaded > '" & strBegDate & "' and Inv_depletion_date  <= '" & strEndDate & "'  and location ='BROKE CENTER'  or location='Broke Center'"_
	&" GROUP BY tblCars.Species "_
	&" ORDER BY tblCars.Species"
	
	   Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
    IF not MyConn.eof  then
  
   strBroke = MYConn("SumofNet")  
 
    end if
    MyConn.close 
    
   strsql = "SELECT   Sum(tblCars.Net) AS SumOfNet  FROM tblCars "_
	&" WHERE Date_Unloaded > '" & strBegDate & "' and Inv_depletion_date  <= '" & strEndDate & "'  and Species = 'P-MORFF'" 
	
	   Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
    IF not MyConn.eof  then
  
   strPMORF = MYConn("SumofNet")  
 
    end if
    MyConn.close 

    
   strsql = "SELECT  Sum(tblCars.Net) AS SumOfNet, Species  FROM tblCars "_
	&" WHERE Date_Unloaded > '" & strBegDate & "' and Inv_depletion_date  <= '" & strEndDate & "'  "_
	&" and Location = 'OCC' Group by Species "
 
	
	   Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
    While not MyConn.eof
 
if MyConn("Species") = "SHRED" then 
   strSHREDOCC = MyConn("SumofNet")
  
  
  elseif MyConn("Species") = "SWL" then 
   strSWLOCC = MyConn("SumofNet")
  
 
  
  elseif MyConn("Species") = "KBLD" then 
   strKBLDOCC = MyConn("SumofNet")
 

    elseif MyConn("Species") = "OF3" then 
   strOF3OCC = MyConn("SumofNet")
   

  elseif MyConn("Species") = "HBX" then 
   strHBXOCC = MyConn("SumofNet")
 
   
    elseif MyConn("Species") = "PMX" then 
   strPMXOCC = MyConn("SumofNet")
 
 
   
   elseif MyConn("Species") = "OCC" then 
   strOCC = MyConn("SumofNet")
  
   
  elseif MyConn("Species") = "ROCC" then 
   strROCC = MyConn("SumofNet")
 
 

 
elseif MyConn("Species") = "MXP" then 
   strMXPOCC = MyConn("SumofNet")
    
elseif MyConn("Species") = "USBS" then 
   strUSBSOCC = MyConn("SumofNet")
    
 
  
 elseif MyConn("Species") = "LPSBS" then 
   strLPSBSOCC = MyConn("SumofNet")
  
   
elseif MyConn("Species") = "HWM" then 
   strHWMOCC = MyConn("SumofNet")
end if 
  MyConn.movenext
  wend
  MyConn.close    
strKCOPTotal =   strPMX  + strHBX + strSHRED + strKBLD + strOF3 + strHWM + strUSBS + strLPSBS + strSWL + strMXP
strOCCtotal = strOCC + strROCC + strMXPOCC + strUSBSOCC + strSWLOCC + strSHREDOCC + strHWMOCC + strLPSBocc + strPMXOCC  + strHBXOCC + strKBLDOCC + strOF3OCC
    %>
  
   <table width="100%"><tr><td width="33%">
   <table border="1">
   <tr><td class="auto-style1">RF Waste Paper</td><td class="auto-style1">Total Weight</td>
	<td class="auto-style1">Sht Tons</td>
	<td class="auto-style1">% of Total</td></tr>
   <tr><td class="style4">KBLD</td><td><span class="style4"><% if  strKBLD > 0 then %><%= Round(strKBLD * 2000,0) %><% else %> 0 <% end if %></span>&nbsp;</td><td>
	<span class="style4"><%= strKBLD %></span>&nbsp;</td><td class="style8">
	<% if strKCOPTotal > 0 then %>
	
	<%= round(strKBLD/strKCOPTotal * 100,0)%><% end if %>	&nbsp;</td></tr>
   <tr><td class="style4">OF3</td><td><span class="style4"><%= strOF3 * 2000 %></span>&nbsp;</td><td>
	<span class="style4"><%= strOF3 %></span>&nbsp;</td><td class="style8"><% if strKCOPTotal > 0 then %>
	<%= round(strOF3/strKCOPTotal * 100,0)%><% end if %>
	&nbsp;</td></tr>
   <tr><td class="style4">PMX</td><td><span class="style4"><%= strPMX * 2000 %></span>&nbsp;</td><td>
	<span class="style4"><%= strPMX %></span>&nbsp;</td><td class="style8">
	<% if strKCOPTotal > 0 then %>
	<%= round(strPMX/strKCOPTotal * 100,0)%><% end if %>
	&nbsp;</td></tr>
	
	  <tr><td class="style4">SHRED</td><td><span class="style4"><%= strSHRED * 2000 %></span>&nbsp;</td><td>
	<span class="style4"><%= strSHRED %></span>&nbsp;</td><td class="style8">
	<% if strKCOPTotal > 0 then %>
	<%= round(strSHRED/strKCOPTotal * 100,0)%><% end if %>
	&nbsp;</td></tr>


  <tr><td class="style4">HBX</td><td><span class="style4"><%= strHBX * 2000 %></span>&nbsp;</td><td>
	<span class="style4"><%= strHBX %></span>&nbsp;</td><td class="style8">
	<% if strKCOPTotal > 0 then %>
	<%= round(strHBX/strKCOPTotal * 100,0)%><% end if %>
	&nbsp;</td></tr>

 
 
  <tr><td class="style4">LPSBS</td><td><span class="style4"><%= strLPSBS * 2000 %></span>&nbsp;</td><td>
	<span class="style4"><%= strLPSBS %></span>&nbsp;</td><td class="style8">
	<% if strKCOPTotal > 0 then %>
	<%= round(strLPSBS/strKCOPTotal * 100,0)%><% end if %>
	&nbsp;</td></tr>
 <tr><td class="style4">SWL</td><td><span class="style4"><%= strSWL * 2000 %></span>&nbsp;</td><td>
	<span class="style4"><%= strSWL %></span>&nbsp;</td><td class="style8">
	<% if strKCOPTotal > 0 then %>
	<%= round(strSWL/strKCOPTotal * 100,0)%><% end if %>
	&nbsp;</td></tr>

 
 <tr><td class="style4">USBS</td><td><span class="style4"><%= strUSBS * 2000 %></span>&nbsp;</td><td>
	<span class="style4"><%= strUSBS %></span>&nbsp;</td><td class="style8">
	<% if strKCOPTotal > 0 then %>
	<%= round(strUSBS/strKCOPTotal * 100,0)%><% end if %>
	&nbsp;</td></tr>
 <tr><td class="style4">HWM</td><td><span class="style4"><%= strHWM * 2000 %></span>&nbsp;</td><td>
	<span class="style4"><%= strHWM %></span>&nbsp;</td><td class="style8">
	<% if strKCOPTotal > 0 then %>
	<%= round(strHWM/strKCOPTotal * 100,0)%><% end if %>
	&nbsp;</td></tr>

 
 <tr><td class="style4">MXP</td><td><span class="style4"><%= strMXP * 2000 %></span>&nbsp;</td><td>
	<span class="style4"><%= strMXP %></span>&nbsp;</td><td class="style8">
	<% if strKCOPTotal > 0 then %>
	<%= round(strMXP/strKCOPTotal * 100,0)%><% end if %>
	&nbsp;</td></tr>

 
   <tr><td class="style4">Total</td><td class="style4"><%= strKCOPTotal * 2000 %> </td><td class="style4">
	<%= strKCOPTotal %></td><td class="style8">100%&nbsp;</td></tr></table></td>
   
  <td width="33%">      <table border="1">
   <tr><td class="style6">OCC Waste Paper</td><td class="style6">Total Weight</td>
	<td class="style6">Sht Tons</td>
	<td class="style6">% of Total</td></tr>
   <tr><td class="style4">OCC</td><td class="style4"><%= strOCC * 2000 %>&nbsp;</td><td class="style4"><%= strOCC %>&nbsp;</td>
	<td class="style8">
   <% if strOCCTotal > 0 then %>
   <%= round(strOCC / strOCCTotal * 100,0) %><% end if %>
	&nbsp;</td></tr>
   <tr><td class="style4">MXP</td><td class="style4"><%= strMXPOCC * 2000 %>&nbsp;</td><td class="style4"><%= strMXPOCC %>&nbsp;</td>
	<td class="style8">
	   <% if strOCCTotal > 0 then %>
   <%= round(strMXPOCC / strOCCTotal * 100,0) %><% end if %>
&nbsp;</td></tr>
   <tr><td class="style4">ROCC</td><td class="style4"><%= strROCC * 2000 %>&nbsp;</td><td class="style4"><%= strROCC %>&nbsp;</td>
	<td class="style8">
	   <% if strOCCTotal > 0 then %>
   <%= round(strROCC / strOCCTotal * 100,0) %><% end if %>
&nbsp;</td></tr>
   <tr><td class="style4">USBS</td><td class="style4"><%= strUSBSOCC * 2000 %>&nbsp;</td><td class="style4"><%= strUSBSOCC %>&nbsp;</td>
	<td class="style8">
	   <% if strOCCTotal > 0 then %>
   <%= round(strUSBSOCC / strOCCTotal * 100,0) %><% end if %>
&nbsp;</td></tr>
      <tr><td class="style4">SWL</td><td class="style4"><%= strSWLOCC * 2000 %>&nbsp;</td><td class="style4"><%= strSWLOCC %>&nbsp;</td>
	<td class="style8">
	   <% if strOCCTotal > 0 then %>
   <%= round(strSWLOCC / strOCCTotal * 100,0) %><% end if %>
&nbsp;</td></tr>

      <tr><td class="style4">SHRED</td><td class="style4"><%= strSHREDOCC * 2000 %>&nbsp;</td><td class="style4"><%= strSHREDOCC %>&nbsp;</td>	 
	<td class="style8">
	   <% if strOCCTotal > 0 then %>
   <%= round(strSHREDOCC / strOCCTotal * 100,0) %><% end if %>
&nbsp;</td></tr>

      <tr><td class="style4">HWM</td><td class="style4"><%= strHWMOCC * 2000 %>&nbsp;</td><td class="style4"><%= strHWMOCC %>&nbsp;</td>
	<td class="style8">
	   <% if strOCCTotal > 0 then %>
   <%= round(strHWMOCC / strOCCTotal * 100,0) %><% end if %>
&nbsp;</td></tr>

          <tr><td class="style4">LPSBS</td><td class="style4"><%= strLPSBSOCC * 2000 %>&nbsp;</td><td class="style4"><%= strLPSBSOCC %>&nbsp;</td>
	<td class="style8">
	   <% if strOCCTotal > 0 then %>
   <%= round(strLPSBSOCC / strOCCTotal * 100,0) %><% end if %>
&nbsp;</td></tr>

                  <tr><td class="style4">KBLD</td><td class="style4"><%= strKBLDOCC * 2000 %>&nbsp;</td><td class="style4"><%= strKBLDOCC %>&nbsp;</td>
	<td class="style8">
	   <% if strOCCTotal > 0 then %>
   <%= round(strKBLDOCC / strOCCTotal * 100,0) %><% end if %>
&nbsp;</td></tr>

                <tr><td class="style4">OF3</td><td class="style4"><%= strOF3OCC * 2000 %>&nbsp;</td><td class="style4"><%= strOF3OCC %>&nbsp;</td>
	<td class="style8">
	   <% if strOCCTotal > 0 then %>
   <%= round(strOF3OCC / strOCCTotal * 100,0) %><% end if %>
&nbsp;</td></tr>

                  <tr><td class="style4">HBX</td><td class="style4"><%= strHBXOCC * 2000 %>&nbsp;</td><td class="style4"><%= strHBXOCC %>&nbsp;</td>
	<td class="style8">
	   <% if strOCCTotal > 0 then %>
   <%= round(strHBXOCC / strOCCTotal * 100,0) %><% end if %>
&nbsp;</td></tr>

                   <tr><td class="style4">PMX</td><td class="style4"><%= strPMXOCC * 2000 %>&nbsp;</td><td class="style4"><%= strPMXOCC %>&nbsp;</td>
	<td class="style8">
	   <% if strOCCTotal > 0 then %>
   <%= round(strPMXOCC / strOCCTotal * 100,0) %><% end if %>
&nbsp;</td></tr>

   <tr><td class="style4">Total</td><td class="style4"><%= strOCCTotal * 2000 %></td><td class="style4"><%= strOCCTotal %></td>
	<td class="style8">
	   100%&nbsp;</td></tr></table></td>

  <td width="33%">    <table border="1">
   <tr><td class="style7">Other</td><td class="style7">Total Weight</td>
	<td class="style7">Sht Tons</td></tr>
   <tr><td class="style4">Broke</td><td class="style4"><%= strBroke * 2000 %>&nbsp;</td><td class="style4"><%= strBroke %>	&nbsp;</td></tr>
   <tr><td class="style4">PMORF</td><td class="style4"><%= strPmorf * 2000 %>&nbsp;</td><td class="style4"><%= strPmorf %>	&nbsp;</td></tr>
</table></td>
 </tr>
</TABLE>
    
<% end if %>
<% Function GetData()

   set objNew = new ASP_Cls_Fiber
          

		set rstSpecies = objNew.FiberSpecies()


End Function
    Function GetFormData()
 
	strBegDate = Request.form("Beg_date") & " " & request.form("Beg_time")
	strBDate = Request.form("Beg_date")
	strEndDate = Request.form("End_date")& " " & request.form("End_time")
	strEdate = Request.form("End_date")
 
	Session("Beg_Date") = strBegDate
	Session("End_date") = strEndDate
	strBegTime = request.form("Beg_time")
	strEndTime = request.form("End_time")




 
    End Function %>

 <!--#include file="Fiberfooter.inc"-->