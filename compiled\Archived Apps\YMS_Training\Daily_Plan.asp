																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Daily Plan</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->




<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate

strtdate = formatdatetime(Now(),2)

set objGeneral = new ASP_CLS_General
  		 if objGeneral.IsSubmit = True Then
strsql2 = "SELECT tblDailyPlan.* FROM tblDailyPlan WHERE  Data_date = '" & strtdate & "'"

    Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL2, Session("ConnectionString")
    
    If not MyRec2.eof then
    while not MyRec2.eof
    
    strComments =  Replace(request.form("C" & MyRec2("ID")), "'", "''")
    
    If len(request.form("A" & MyRec2("ID"))) > 0 then
    strActual = request.form("A" & MyRec2("ID"))
 
    
    strid = MyRec2("ID")
    strsql3 = "Update tblDailyPlan set Actual = " & strActual & ", Comments = '" & strComments & "' where data_date = '" & strtdate & "' and ID = " & strid
      Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL3
			MyRec.Close 

       else 
    strActual = 0
    end if
    
    MyRec2.movenext
    wend
    MyRec2.close
    end if
    
   
%>
<span class="style29"><strong>RECORDS ARE UPDATED</strong></span>

<%
    end if


strsql = "SELECT tblDailyPlan.* FROM tblDailyPlan WHERE  Data_date = '" & strtdate & "'"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    
    If not MyRec.eof then
    
   

%>
<style type="text/css">
.style2 {
	font-family: Arial, Helvetica, sans-serif;
	font-weight: bold;
	font-size: x-small;
}
.style3 {
	border: 1px solid #EEF2F6;
	text-align: left;
}
.style4 {
	border: 1px solid #808080;
}
.style6 {
	font-weight: bold;
	border: 1px solid #EEF2F6;
}
.style7 {
	border: 1px solid #EEF2F6;
}
.style8 {
	border: 1px solid #EEF2F6;
	font-family: Arial, Helvetica, sans-serif;
	font-weight: bold;
	font-size: x-small;
}
.style9 {
	font-weight: bold;
	border: 1px solid #EEF2F6;
	text-align: center;
}
.style10 {
	border: 1px solid #EEF2F6;
	text-align: center;
}
.style27 {
	font-size: small;
}
.style28 {
	text-align: right;
}
.style29 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
</style>
</head>

<body>
<br>
	  <form name="form1" action="Daily_Plan.asp"  method="post" ID="Form1"  >
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=95%  border=1>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><strong>Team Mobile Daily Plan - Lazer Moves</strong></font></td>
<td align = center><font size="2" face = arial><b>
	<font face="calibri" size="3">
	<span class="style27">
</span></font>Date:  <%= strTdate %></b>&nbsp;</td>
<td class="style28"><INPUT TYPE="submit" value="Submit"></td>


</tr>
	    </table><br>
	
	
	<TABLE cellSpacing=0 cellPadding=0 class = "style4" align="center" style="width: 70%">  
	 <tr class="tableheader">
			<td class="style8"  > <p align="center" class="style2">       Driver</td>
		<td class="style7"  > <p align="left" class="style2">       Location</td>
	
		<td class="style8"  >Shift</td>
			<td class="style8"  >Projected</td>

		<td class="style8"  >Actual</td>
		<td class="style8"  >Notes</td>
  
		<td class="style7"  > <p align="center" class="style2"> Variance</td>
	

	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
       
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td class="style3"> <font size="2" face="Arial">
<% if strDriver = MyRec("Driver") then %>
&nbsp;
<% else %><b>
<%= MyRec("Driver") %></b>
<% end if %></td>

	<td class="style3"> <font size="2" face="Arial"><% if strLocation = MyRec("Location") then %>
&nbsp;
<% else %>
<%= MyRec("Location") %>
<% end if %></td>
	<td class="style7"  ><font size="2" face="Arial"><%= MyRec("Shift") %></font></td>

	<td class="style9"  > <font size="2" face="Arial"><%= MyRec("Projected") %></font></td>
		<td class="style6"  > <font size="2" face="Arial">
		<input type="text" name="<%= "A" & MyRec("ID") %>" value="<%= MyRec("Actual") %>" style="width: 35px">
		
</font></td>
			<td class="style6"  ><font size="2" face="Arial">
			<input type="text" name="<%="C" & MyRec("ID") %>" value="<%= MyRec("Comments") %>" style="width: 370px">&nbsp;</font></td>


		<td class="style10"  ><font size="2" face="Arial"><%= MyRec("Projected") - MyRec("Actual") %>&nbsp;</td>
	
	</tr>

 <%
       ii = ii + 1
       strDriver = MyRec("Driver")
       strLocation = MyRec("Location")
       MyRec.MoveNext
     Wend
    %>
</table>
</form>
<% 
else
strsql = "INSERT INTO tblDailyPlan ( Driver, Location, Shift, Data_date ) "_
&" SELECT tblDailyPlanDefault.Driver, tblDailyPlanDefault.Location, tblDailyPlanDefault.Shift, '" & strTdate & "' "_
&" FROM tblDailyPlanDefault"
 Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 
			
			Response.redirect("Daily_Plan.asp")

end if

 %><!--#include file="Fiberfooter.inc"-->