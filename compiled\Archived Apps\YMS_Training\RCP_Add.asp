																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Add Rail Car Target </TITLE>


<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->


<% Dim MyRec, strsql


  set objGeneral = new ASP_CLS_General
  if objGeneral.IsSubmit() Then	
  Dim strdate, strTarget
  

  	
  	If len(request.form("Target")) > 0 then
  	strTarget= request.form("Target")
  	else
  	strTarget = 0
  	end if
  	
  	strdate = request.form("Date")
  	
  		
  
  
        	
  	
	strsql =  "INSERT INTO tblRailTarget (INV_Date, Target) "_
	&" SELECT '" & strDate & "', " & strTarget & " "
	
	
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
Response.redirect("RF_Projections.asp")		
end if
	
%>

<style type="text/css">
.style4 {
	border-style: solid;
	border-width: 1px;
}
.style7 {
	text-align: center;
}
.style8 {
	font-family: arial;
	font-size: x-small;
	text-align: center;
}
.style9 {
	background-color: #FFFFD7;
}
</style>
</head>

<body>
<br>
	
<form name="form1" action="RCP_Add.asp" method="post" ID="Form1">
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Add Rail Car Target</b></font></td>
<td align = right><font face="Arial"><a href="RC_Projections.asp">Return</a>&nbsp;</td>


</tr>
	    </table>



	<p>&nbsp;</p>
	<table id="table1" style="width: 35%" class="style4" align="center">
<tr bgcolor = #CCCCFF>
		<td bgcolor="#FFFFD7" class="style8">

&nbsp;Date</td>
		<td bgcolor="#FFFFD7" class="style8">

&nbsp;<span class="style9">Target</span></td>

	</tr>
	<tr>
		<td class="style7"><font face = arial size = 1>
		<input type="text" name="Date" size="20" style="width: 112px"></td>
		<td class="style7"><font face = arial size = 1>
		<input type="text" name="Target" size="20" style="width: 69px"></td>

	</tr>
</table>


<p align="center"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></p>
</form>
<!--#include file="Fiberfooter.inc"-->