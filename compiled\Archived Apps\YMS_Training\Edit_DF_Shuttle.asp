																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Edit Dry Fiber Shuttles</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->




<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate

strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -4, strtdate)

strsql = "SELECT tblCars.* FROM tblCars WHERE  Entry_page = 'Transfer_From_DF.asp'   and Entry_time > '" & strdate & "' order by CID desc"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=80%  border=1>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Edit Transfers from Dry Fiber</b></font></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=50% class = "tablecolor1" border=1 align="center">  
	 <tr class="tableheader">
<td>&nbsp;</td>
			<td  > <p align="center">       <font face="Arial" size="1">Print<br> Receipt</font></td>
		<td  > <p align="left">       <font face="Arial" size="1">Receipt Number</font></td>
	
		<td  ><font face="Arial" size="1"><b>Trailer</b></font></td>
			<td  ><font face="Arial" size="1"><b>Location</b></font></td>

		<td  ><font face="Arial" size="1"><b>Carrier</b></font></td>
		<td  ><font face="Arial" size="1">Species</font></td>
  
		<td  > <p align="center"> <font face="Arial" size="1">Tons</font></td>
		<td  > <p align="left"> <font face="Arial" size="1">Date Transferred</font></td>
	

	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td> <font size="1" face="Arial"><a href="Transfer_From_Merchants_Edit.asp?id=<%= MyRec.fields("CID") %>">Edit</a></td>
	<td align = center> <font size="1" face="Arial"><a href="Transfer_Mreceipt.asp?id=<%= MyRec.fields("CID") %>">Print</a></td>
	<td  ><font size="1" face="Arial"><%= MyRec.fields("CID") %></font></td>

	<td  > <font size="1" face="Arial"><b><%= MyRec.fields("Transfer_Trailer_nbr")%></font></b></td>
		<td  > <font size="1" face="Arial"><b><%= MyRec.fields("Location")%></font></b></td>
			<td  ><font size="1" face="Arial"><b><%= MyRec.fields("Trans_Carrier")%>&nbsp;</font></b></td>


		<td  ><font face="Arial" size="1"> <%= MyRec.fields("Species")%></font></td>
	
		<td  >        <font size="1" face="Arial"><%= MyRec.fields("Tons_received")%></font></td>
	<td  >  <font size="1" face="Arial"><%= MyRec.fields("Transfer_date")%></font></td>
	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>


<!--#include file="Fiberfooter.inc"-->