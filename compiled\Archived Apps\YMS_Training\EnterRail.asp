<html xmlns:m="http://schemas.microsoft.com/office/2004/12/omml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 14.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Enter Trailer Receipt</title>
<style type="text/css">
.style4 {
	border-width: 1px;
	background-color: #FFFFEA;
}
.style6 {
	text-align: right;
	font-family: Arial;
	font-size: x-small;
	border-width: 1px;
	background-color: #FFFFEA;
}
.style28 {
	color: #9F112E;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID, strRelease, MyConn, objMOC, rstFiber, strLocation, strSAP, rstTrailer , strTrailerWeight , strTractor   
    Dim strTrailer, strCarrier, strSQL3, MyConn3, strVID, strUOM, strPounds, strRail, strDate_shipped
    Dim strReleaseNbr, strTrailerTID, strBales, stralert
    Dim strRECNbr
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strGenerator
    Dim strGenCity
    Dim strGenState, gAuditStatus
    Dim strOther, strR,  gSpecies, gPO, gSAP_Nbr, gVendor, gOID, strNet, gWeight_required, gTrailerStatus, strKCWeighed
    Dim MyRec5, strsql5, strCarID, strPost, strPostCID

    strRelease  = Trim(Request.QueryString("id")) 
	strR = left(strRelease,1)

 	set objGeneral = new ASP_CLS_General

  	strVID = 0

if objGeneral.IsSubmit() Then

If request.form("Cancel") = "ON" then
Response.redirect ("SelectRelease.asp")
else
   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
	strRelease  = Trim(Request.QueryString("r")) 
	strR = left(strRelease,1)
 	strTrailer = Request.form("Trailer")
	strOther = Replace(Request.form("Other_Comments"), "'", "''") 
	strDateReceived = Request.form("Date_received")
	
	strCarrier = "RAIL"
	strLocation = Request.form("Location")

	strGenerator = Replace(Request.form("Generator"), "'", "''")
	strGenCity = Replace(Request.form("Gen_City"), "'", "''")
	strGenState = Request.form("Gen_State")
	
If len(Request.form("Rail_weight")) > 1 then

		strTonsReceived = Request.form("Rail_weight")
		strPounds = round((strTonsReceived * 2000),3)
		strRail= strTonsReceived
elseif  len(Request.form("Rail_weight")) < 2 then
		strTonsReceived = 0
		strPounds = 0
		strRail= 0

end if
strNet = strTonsreceived
	
	strsql = "SELECT CID FROM tblCars WHERE Release_nbr = '" & strRelease & "'"

   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
	If not MyRec.eof then
	strPOST = "NO"
	strPostCID = MyRec.fields("CID")
	else 
	strPOST = "OK"
	end if
MyRec.close
	If strPOST = "OK" then


		Call SaveData() 
			If Request.form("Print_receipt") =  "ON" then
		
	
				Response.redirect("Truck_receipt.asp?id=" & strCarID & "&p=" & strpounds & "&r=ER")
			
				else
				Response.redirect ("Select_rail.asp")
				end if
	else
	
		Response.write("<br><font face=arial size=3 color=red><br>Receipt Number " & strPostCID & " already has this release number.  It could be you clicked the Submit button twice.</font><br>")
	end if

	
else
Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to save this information.</font></br>")
end if
end if
  
ELSE

	strsql = "SELECT tblOrder.* FROM tblOrder WHERE Release = '" & strRelease & "'"

   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
   	 If not MyRec.eof then
	
	gSpecies = MyRec.fields("Species")
	gSap_Nbr = MyRec.fields("SAP_Nbr")
	gVendor = MyRec.fields("Vendor")
	gPO = MyRec.fields("PO")
	strReleaseNbr = MyRec.fields("Release")
	gOID = Myrec.fields("OID")
	strGenerator = Myrec.fields("Generator")
	strGenCity  = MyRec.fields("City")
	strGenState = Myrec.fields("State")
	strR = left(strReleaseNbr,1)
	
	else
	Response.write ("<font face=arial size=3>The release number is invalid.</font><br><br> ")
	end if
	MyRec.close
	
	
	strDateReceived = formatdatetime(Now(),2)
	
		strsql = "SELECT tblVirginFiber.* FROM tblVirginFiber WHERE Release = '" & strRelease & "' and Status = 'Inbound'"
	
	
   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
   	 If not MyRec.eof then
   	 strTrailer = MyRec.fields("Trailer")
   	 strTonsReceived = MyRec.fields("Tons")
   	 If len(strTonsReceived) > 0 then
   	 strRail = round(MyRec.fields("Tons"),3)
   	 else
   	 strRail = 0
   	 end if
   	 strCarrier = "RAIL"
   	 strBales = MyRec.fields("Bales_VF")
   	 strVID = MyRec.fields("VID")
   	 strUOM = MyRec.fields("UOM")
   	 strDate_shipped = MyRec.fields("Date_shipped")

   	 end if 
   	 MyRec.close
   	 
   	 

end if
%>



<body>


<% if Request.querystring("n") = "T" then 
strTrailer = Session("Trailer")
strCarrier = Session("Carrier")
strTrailerTID = Session("TrailerTID")
strGrossWeight = Session("GrossWeight")

strGenerator = Session("Generator")
strGenCity = Session("GenCity")
strGenState = Session("GenState")
strTonsReceived = Session("TonsReceived")
strPounds = formatnumber(Session("Pounds"),0)
strRail = Session("Rail")
 %>
<p align = center><font face = arial size = 4 color="teal"><b>

<font face = arial size = 3 color = red>
<span class="style28">
<% if (Session("Rail") < 50 and strCarrier = "RAIL") or (Session("Rail") > 75 and strCarrier = "RAIL") then %>
The Tons Received must be between 55 and 75.  You entered: <%= Session("Rail") %><br>
<% end if %>

<% if isnull(Session("Trailer")) or len(Session("Trailer"))< 1 then %>
You must enter a Rail Car number.<br>
<% end if %>
<% if len(strGenerator) < 2 then %>
You must enter a Generator<br>
<% end if %>
<% if len(StrGenCity) < 2 then %>
You must enter a Generator City<br>
<% end if %>
<% if len(strGenState) < 2 then %>
You must enter a Generator State
<% end if %></span>

</p></font></b> 
<% else %>
&nbsp;
<% end if %>

<table width = 100%><tr><td width = 33% style="height: 24px">
<font face="Arial" size="2"><b>Species: <%=gSpecies%>&nbsp;&nbsp; SAP Nbr: <%= gSAP_Nbr%></b>

</td><td align = center width = 34% style="height: 24px"><b><font face="Arial" size="4">
Enter Recovered Fiber Rail Receipt &nbsp;<b><%= strReleaseNbr%></b></font> </b></td>
	<td align = right width = 33% style="height: 24px"><a href="Select_Rail.asp"><b><font face = arial size = 2>RETURN</a></font></b></td></tr></table>




<form name="form1" action="EnterRail.asp?r=<%=strRelease%>&s=<%= gSpecies%>" method="post">
<input type = hidden name = "Bales" value = <%= strBales %>>
<input type = hidden name = "VID" value = <%= strVID %>>
<input type = hidden name = "UOM" value = <%= strUOM %>>
<input type = hidden name = "Date_Shipped" value = <%= strDate_shipped %>>

<div align="center">
<table border="1" cellspacing="0" width="70%" bgcolor="#FFFFEA" style="border-collapse: collapse" cellpadding="0" bordercolorlight="#C0C0C0">
<tr>
    <td bgcolor="#FFFFEA" width="17%">&nbsp;</td>

    <td  bgcolor="#FFFFEA" colspan="3">&nbsp;</td>
  </tr>
  <tr>
    <td  align = right bgcolor="#FFFFEA" width="17%" >
   <b>
   <font face="Arial" size="2">Rail Car #:&nbsp;</font></b></td>
<td  align = left colspan="2" bgcolor="#FFFFEA">

      <font face="Arial">

      <input name="Trailer" size="15" value = "<%= strTrailer%>" style="font-weight: 700"><b>
		</b>&nbsp;&nbsp;</font></td>
<td  align = left bgcolor="#FFFFEA" width="29%"> <font face="Arial"><font size="2"><b>Check to Print Receipt:</b></font><input type="checkbox" name="Print_receipt" value="ON" checked></td></tr>
  <tr>

      <td  bgcolor="#FFFFEA" align = right width="17%">
	<font face="Arial" size="2"><b>Carrier:</b></font></td>
<td  align = left bgcolor="#FFFFEA" colspan="3">

      <font face="Arial" size="2"><b>&nbsp;&nbsp; RAIL</td>
</tr>

<tr><td height="22" width="17%" class="style6"><strong>Tons Received:</strong></td>
    <td colspan="3" height="22" class="style4">    <font face="Arial">  <strong>
	<input name="Rail_weight" size="15" value = "<%= strRail %>" style="font-weight: 700" style="float: left"></strong></td>
  </tr>
       <tr><td  align = right bgcolor="#FFFFEA" width="17%" ><font face="Arial" size="2"><b>Date Received:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFEA"> <font face="Arial"> 
<input name="Date_Received" size="15" value = "<%= strDateReceived%>" style="font-weight: 700"></font></td>
<td align = left bgcolor="#FFFFEA" width="18%"> 
<p align="right"><font face="Arial" size="2">
<b>SAP DOC ID:</b>

      </td>
<td align = left bgcolor="#FFFFEA" width="29%"> <font face="Arial" size="2"><b>    <font face="Arial">

      <input name="SAP_DOC_ID" size="16" value = "<%= strSAP %>" style="font-weight: 700"></font></td></tr>
              <tr>
          <td  align = right bgcolor="#FFFFEA" width="17%" >
   <font face="Arial" size="2"><b>Generator:&nbsp;</b></font></td>
<td align = left colspan="3" bgcolor="#FFFFEA">
      <font face="Arial">
      <input name="Generator" size="25" value = "<%= strGenerator %>" style="font-weight: 700"></font></TD></tr>
        <tr>
          <td  align = right bgcolor="#FFFFEA" height="28" width="17%" >
    <font face="Arial" size="2"><b>Generator City:&nbsp;</b></font></td>
<td align = left colspan="3" height="28" bgcolor="#FFFFEA">
      <font face="Arial">
      <input name="Gen_City" size="15" value ="<%= strGenCity%>" style="font-weight: 700"></font></td></tr>

           <tr>
          <td align = right bgcolor="#FFFFEA" width="17%" >
   <font face="Arial" size="2"><b>Generator State:&nbsp;</b></font></td>
<td align = left colspan="3" bgcolor="#FFFFEA">
      <font face="Arial">
      <input name="Gen_State" size="15" value = "<%= strGenState%>" style="font-weight: 700"></font></td></tr>
         <tr>
          <td  align = right bgcolor="#FFFFEA" width="17%" >
  <font face="Arial" size="2"><b>Other:&nbsp;</b></font></td >
   <td align = left colspan="3" bgcolor="#FFFFEA">   <font face="Arial">   
	<input name="Other_Comments" size="25" value = "<%= strOther%>" style="font-weight: 700">&nbsp;&nbsp;&nbsp;
	<font size="2">&nbsp;Location: </font>   
	<select name="Location" style="font-weight: 700" size="1">

      <option selected>YARD</option>
	 <option  >MERCHANTS</option>
 
     </select></font></td></tr>
<tr>
    <td  bgcolor="#FFFFEA" width="17%"></td>

    <td bgcolor="#FFFFEA" colspan="3"></td>
  </tr>

    
  <tr>
    <td  bgcolor="#FFFFEA" width="17%"></td>

    <td bgcolor="#FFFFEA" colspan="3"></td>
  </tr>

    <td bgcolor="#FFFFEA" width="17%">&nbsp;</td>

    <td align = left bgcolor="#FFFFEA" colspan="3"><font face="Arial">
	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" >&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </font></td>
  </tr>
</table>

</div>

</form>
</body>
<%

Function SaveData()
dim strbody, strCID, strECarrier, strETrailer, strELoad, strEDoc, strEDC, strEDCName, strEAdd, strECity, strEState, MyConn2, strsql4
dim strECC, strEBCC, strESpecies


strESpecies = Request.querystring("s")

	strsql = "SELECT tblOrder.* FROM tblOrder WHERE Release = '" & strRelease & "'"

   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
	
	gSpecies = MyRec.fields("Species")
	gSap_Nbr = MyRec.fields("SAP_Nbr")

	gVendor = Replace(MyRec.fields("Vendor"), "'", "''")
	gPO = MyRec.fields("PO")
	gRelease = MyRec.fields("Release")
	gOID = Myrec.fields("OID")
	gWeight_required = MyRec.fields("Weigh_trailer")
	gAuditstatus = ""
	

	MyRec.close
	

	
	
	
	Dim strRightNow, strAud
	strRightnow = Now()
	
	

   	 strPounds = round(Request.form("Rail_weight") * 2000,3)
   	 strTonsReceived = request.form("Rail_weight")
   	 	strTrailerTID = 0
	strTrailerweight = 0
	strGrossWeight = 0
	strTareWeight = 0

   	 


	
	

	
	if len(request.form("SAP_DOC_ID")) > 2 then
	strSAP = Request.form("SAP_DOC_ID")
	else
	strSAP = ""
	end if
	
	
	If len(request.form("Bales")) > 0 then
	strBales = request.form("Bales")
	else
	strBales = 0
	end if
	
	If len(request.form("UOM")) > 0 then
	strUOM = request.form("UOM")
	else
	strUOM = ""
	end if
	
	strVID = request.form("VID")
	
	'Response.write("Trailer weight" & strTrailerweight & "TID" & strTrailerTID & "Tractor weight" & strTractor & "Tons " & strTonsReceived)
	'Response.write("SAP" & strSAP & "Carrier" & strCarrier & "Species" & gSpecies & "SAP_Nbr " & gSAP_Nbr)
		'Response.write("Vendor" & gVendor & "PO" & gPO & "Date" & strDateReceived & "Release " & strRelease & "Tare" & strTareWeight)
	
	strsql =  "INSERT INTO tblCars ( UOM, VID, Bales_VF, Bales_RF,    SAP_DOC_ID, Carrier, Species, Grade, SAP_Nbr, Vendor, PO, Date_received, Release_nbr, Location_unloaded, Location, OID, Generator, Gen_City, Gen_State, Trailer, Other_Comments, Tons_Received, Net,  Entry_Time, Entry_BID, Entry_Page, Status) "_
	&" SELECT '" & strUOM & "', " & strVID & ", " & strBales & ", " & strBales & ",  '" & strSAP & "', '" & strCarrier & "', '" & gSpecies & "', 'RF', '" & gSAP_Nbr & "', '" & gVendor & "', '" & gPO & "', '" & strDateReceived & "', '" & strRelease & "', 'RF', '" & Request.form("Location") & "', " & gOID & ", '" & strGenerator & "', '" & strGencity & "', '" & strGenstate & "',  "_
	&" '" & strTrailer & "', '" & strOther & "',  " & strTonsReceived & ", " & strTonsReceived & ",   '" & strRightNow & "', '" & Session("EmployeeID") & "', 'EnterRail', '" & strRail & "'"
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
			strsql5 = "SELECT Max(tblCars.CID) AS MaxOfCID FROM tblCars WHERE tblCars.Trailer = '" & strTrailer & "'"
			
   	 Set MyRec5 = Server.CreateObject("ADODB.Recordset")
   	 MyRec5.Open strSQL5, Session("ConnectionString")
   	 strCarID = MyRec5.fields("MaxofCID")
   	 MyRec5.close
   	 
strsql = "Select Type from tblBrokeSap where Category = 'BROKE' and SAP = '" & gSAP_Nbr & "'"

   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
	If not MyRec.eof then
	strBrokeD = MyRec("Type")
	strsql = "Update tblCars set Broke_Description = '" & strBrokeD & "' where CID = " & strCarID & ""
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
	end if	
	
	If request.form("Location") = "MERCHANTS" then
	
	strsql2 = "Update tblCars set Date_Unloaded = '" & strDateReceived & "' where CID = " & strCarID & ""
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL2
			MyConn.Close

	
	end if

			

		strsql = "Update tblVirginFiber set Status = 'Received' where VID = " & strVID & ""
		
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
			strsql = "Update tblCars set Date_shipped = '" & Request.form("Date_Shipped") & "' where CID = " & strCarID & ""
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->