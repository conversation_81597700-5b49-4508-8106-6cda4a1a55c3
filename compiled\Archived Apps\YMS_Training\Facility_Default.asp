																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Add Consumption Projection </TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->


<% Dim MyRec, strsql, strid
  Dim strdate, strRF, strOCC, strMXP
  


strsql = "Select tblTempYardTotals.* from tblTempYardTotals where ID = 16"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 

strOCC = MyRec.fields("Total_OCC")
strMXP = MyRec.fields("Total_MXP")
strKCOP_T1 = MyRec("KCOP_Tier1")
strKCOP_T2 = MyRec("KCOP_Tier2")
strKCOP_T3 = MYRec("KCOP_Tier3")
strKCOP_T4 = MyRec("KCOP_Tier4")
strOF_T1 = MyRec("OF_Tier1")
strOF_T2 = MyRec("OF_Tier2")
strOF_T3 = MyRec("OF_Tier3")
strPMX = MyRec("PMX")
strHBX = MyREc("HBX")
strSHRED = MyREc("Shred_T1")
strSHREDT5 = MyREc("Shred_T5")
strKBLD = MyRec("KBLD")
strOF3 = MyRec("OF3")


strRF = MyRec.fields("Total_RF")
 
strOF = MyRec.fields("Yard_KCOP")
 


MyRec.close


  set objGeneral = new ASP_CLS_General
  if objGeneral.IsSubmit() Then	

    	If len(request.form("KBLD")) > 0 then
  	strKBLD = request.form("KBLD")
  	else
  	strKBLD = 0
  	end if
  	
 
  	
  	If len(request.form("OF3")) > 0 then
  	strOF3 = request.form("OF3")
  	else
  	strOF3 = 0
  	end if


  	
  	If len(request.form("KCOP_T1")) > 0 then
  	strKCOP_T1 = request.form("KCOP_T1")
  	else
  	strKCOP_T1 = 0
  	end if
  	
  	 If len(request.form("KCOP_T2")) > 0 then
  	strKCOP_T2 = request.form("KCOP_T2")
  	else
  	strKCOP_T2 = 0
  	end if
  	
  	If len(request.form("KCOP_T3")) > 0 then
  	strKCOP_T3 = request.form("KCOP_T3")
  	else
  	strKCOP_T3 = 0
  	end if

If len(request.form("KCOP_T4")) > 0 then
  	strKCOP_T4 = request.form("KCOP_T4")
  	else
  	strKCOP_T4 = 0
  	end if
  	
  	If len(request.form("OF_T1")) > 0 then
  	strOF_T1 = request.form("OF_T1")
  	else
  	strOF_T1 = 0
  	end if
  	
    If len(request.form("OF_T2")) > 0 then
  	strOF_T2 = request.form("OF_T2")
  	else
  	strOF_T2 = 0
  	end if
  	
  	If len(request.form("OF_T3")) > 0 then
  	strOF_T3 = request.form("OF_T3")
  	else
  	strOF_T3 = 0
  	end if

	If len(request.form("PMX")) > 0 then
  	strPMX = request.form("PMX")
  	else
  	strPMX = 0
  	end if

	
  	If len(request.form("OCC")) > 0 then
  	strOCC = request.form("OCC")
  	else
  	strOCC = 0
  	end if
  	
   	  	If len(request.form("MXP")) > 0 then
  	strMXP = request.form("MXP")
  	else
  	strMXP = 0
  	end if
  	
  	  	
  	If len(request.form("RFF")) > 0 then
  	strRF = request.form("RFF")
  	else
  	strRF = 0
  	end if
  	
  	  	If len(request.form("OF")) > 0 then
  	strOF = request.form("OF")
  	else
  	strOF = 0
  	end if
  	
  	If len(request.form("SHRED")) > 0 then
  	strSHRED = request.form("SHRED")
  	else
  	strSHRED = 0
  	end if

	If len(request.form("SHRED_T5")) > 0 then
  	strSHREDT5 = request.form("SHRED_T5")
  	else
  	strSHREDT5 = 0
  	end if
  	
  		If len(request.form("HBX")) > 0 then
  	strHBX = request.form("HBX")
  	else
  	strHBX = 0
  	end if
 
  	
  strsql =  "Update tblTempYardTotals set INV_Date = '" & strDate & "', KCOP_Tier1 = " & strKCOP_T1 & ", KCOP_Tier2 = " & strKCOP_T2 & ", KCOP_Tier3 = " & strKCOP_T3 & ", KCOP_Tier4 = " & strKCOP_T4 & ", "_
&" OF_Tier1 = " & strOF_T1 & ", OF_Tier2 = " & strOF_T2 & ", OF_Tier3 = " & strOF_T3 & ", PMX = " & strPMX & ", total_OCC = " & strOCC & ",  total_MXP = " & strMXP & ", "_
&" Total_RF = " & strRF & ", Yard_KCOP = " & strOF & ", SHRED_T5 = " & strShredT5 & ", SHRED_T1 = " & strShred & ", HBX = " & strHBX & ", KBLD = " & strKBLD & ", OF3 = " & strOF3 & " where ID = 16"

	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
Response.redirect("Facility.asp")		
end if
	
%>

<style type="text/css">
.style6 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: arial;
	font-size: x-small;
	text-align: center;
	background-color: #DFEFFF;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style7 {
	text-align: center;
}
.style8 {
	border: 1px solid #000000;
}
.style9 {
	text-align: center;
	border-left-color: #C0C0C0;
	border-left-width: 1px;
	border-right-width: 1px;
	border-top-color: #C0C0C0;
	border-top-width: 1px;
	border-bottom-width: 1px;
}
.style10 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: arial;
	font-size: x-small;
	text-align: center;
	background-color: #FDFEE0;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style11 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: arial;
	font-size: x-small;
	text-align: center;
	background-color: #EAFEE0;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style12 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: arial;
	font-size: x-small;
	text-align: center;
	background-color: #FFB7B7;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
</style>
</head>

<body>
<br>
	
<form name="form1" action="Facility_Default.asp?id=<%= strid%>" method="post" ID="Form1">
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
KCOP, OF, PMX, SHRED, HBX, OCC and MXP Consumption Default<br>
Note:&nbsp;&nbsp; The Daily Inventory Report is the trigger point for adding 
these totals automatically for the day</strong></font></td>
<td align = right><font face="Arial"><a href="Facility.asp">Return</a>&nbsp;</td>


</tr>
	    </table>



	<p>&nbsp;</p>
	<table id="table1" style="width: 40%" class="style8" align="center" cellspacing="0" cellpadding="0">
<tr bgcolor = #CCCCFF>
		<td class="style6" colspan="4" style="height: 34px"><strong>KCOP</strong></td>
		<td class="style6" style="height: 34px"><strong>KBLD</strong></td>
	</tr>
<tr bgcolor = #CCCCFF>
		<td class="style6">1-A</td>
		<td class="style6">1-B</td>
		<td class="style6">2</td>
	<td class="style6">T-5</td>
	<td class="style6">&nbsp;</td>
	</tr>
	<tr>
		<td class="style9"><font face = arial size = 1>
		<input type="text" name="KCOP_T1" size="5" style="width: 50px" value="<%= strKCOP_T1%>" tabindex="1"></td>
		<td class="style9"><font face = arial size = 1>
		<input type="text" name="KCOP_T2" size="5" style="width: 49px" value="<%= strKCOP_T2 %>" tabindex="2"></td>

		<td class="style9">
		<p align="center" class="style7"><font face = arial size = 1>
		<input type="text" name="KCOP_T3" size="5" style="width: 45px; height: 22px;" value="<%= strKCOP_T3 %>" tabindex="3"></td>
		<td class="style9">
		<p align="center" class="style7"><font face = arial size = 1>
		<input type="text" name="KCOP_T4" size="5" style="width: 40px; height: 22px;" value="<%= strKCOP_T4%>" tabindex="4"></td>



		<td class="style9">
		<font face = arial size = 1>
		<input type="text" name="KBLD" size="5" style="width: 40px; height: 22px;" value="<%= strKBLD%>" tabindex="4"></td>



	</tr>
</table>


<br>
	<table id="table2" style="width: 40%" class="style8" align="center" cellspacing="0" cellpadding="0">
<tr bgcolor = #CCCCFF>
		<td class="style10" colspan="3" style="height: 37px"><strong>OF</strong></td>
		<td class="style10" style="height: 37px"><strong>OF3</strong></td>
	</tr>
<tr bgcolor = #CCCCFF>
		<td class="style10">1</td>
		<td class="style10">2</td>
		<td class="style10">3</td>
		<td class="style10">&nbsp;</td>
	</tr>
	<tr>
		<td class="style9"><font face = arial size = 1>
		<input type="text" name="OF_T1" size="5" style="width: 47px" value="<%= strOF_T1 %>" tabindex="5"></td>
		<td class="style9"><font face = arial size = 1>
		<input type="text" name="OF_T2" size="5" style="width: 42px" value="<%= strOF_T2%>" tabindex="6"></td>

		<td class="style9">
		<p align="center" class="style7"><font face = arial size = 1>
		<input type="text" name="OF_T3" size="5" style="width: 43px; height: 22px;" value="<%= strOF_T3%>" tabindex="7"></td>



		<td class="style9">
		<font face = arial size = 1>
		<input type="text" name="OF3" size="5" style="width: 43px; height: 22px;" value="<%= strOF3 %>" tabindex="7"></td>



	</tr>
</table>


<strong><br>
	<table id="table4" style="width: 40%" class="style8" align="center" cellspacing="0" cellpadding="0">
<tr bgcolor = #CCCCFF>
		<td class="style10" colspan="2" style="height: 37px"><strong>SHRED</strong></td>
		<td class="style10" style="height: 37px">HBX</td>
	</tr>
<tr bgcolor = #CCCCFF>
		<td class="style10">1</td>
		<td class="style10">2</td>
		<td class="style10">3</td>
	</tr>
	<tr>
		<td class="style9"><font face = arial size = 1>
		<input type="text" name="SHRED" size="5" style="width: 47px" value="<%= strSHRED %>" tabindex="8"></td>
		<td class="style9"><font face = arial size = 1>
		<input type="text" name="SHRED_T5" size="5" style="width: 42px" value="<%= strSHREDT5 %>" tabindex="9"></td>

		<td class="style9">
		<p align="center" class="style7"><font face = arial size = 1>
		<input type="text" name="HBX" size="5" style="width: 43px; height: 22px;" value="<%= strHBX %>" tabindex="10"></td>



	</tr>
</table>


<br>
</strong>
	<table id="table3" style="width: 40%" class="style8" align="center" cellspacing="0" cellpadding="0">
<tr bgcolor = #CCCCFF>
		<td class="style11" style="height: 43px"><strong>PMX</strong></td>
		<td class="style11" style="height: 43px"><strong>OCC</strong></td>
		<td class="style11" style="height: 43px"><strong>MXP</strong></td>
		<td class="style12" style="height: 43px">RFF Total</td>
		<td class="style12" style="height: 43px">OF Total</td>
	</tr>
	<tr>
		<td class="style9"><font face = arial size = 1>
		<input type="text" name="PMX" size="5" style="width: 48px" value="<%= strPMX%>" tabindex="11"></td>
		<td class="style9"><font face = arial size = 1>
		<input type="text" name="OCC" size="25" style="width: 47px" value="<%= strOCC %>" tabindex="12"></td>

		<td class="style9">
		<p align="center" class="style7"><font face = arial size = 1>
		<input type="text" name="MXP" size="5" style="width: 43px; height: 22px;" value="<%= strMXP %>" tabindex="13"></td>



		<td class="style9">
		<font face = arial size = 1>
		<input type="text" name="RFF" size="20" style="width: 69px" value="<%= strRF %>" tabindex="14"></td>



		<td class="style9">
		<font face = arial size = 1>
		<input type="text" name="OF" size="20" style="width: 69px" value="<%= strOF %>" tabindex="15"></td>



	</tr>
</table>


<p align="center"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></p>
</form>
<!--#include file="Fiberfooter.inc"-->