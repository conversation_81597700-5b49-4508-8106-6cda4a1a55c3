<%
Class ASP_CLS_ProcedureESL

 

 Public Function CESearch(PageNumber, strTeam,  strWA, strSOP, strStatus)
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess


    set CESearch = objDAC.ExecuteSp("asp_cls_CESearch", array(50, PageNumber, strTeam,  strWA, strSOP, strStatus))
    set objDAC = nothing

 End Function

 Public Function CESearchR1(PageNumber, strTeam,  strWA, strSOP, strStatus, strStatusType)
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess


    set CESearchR1 = objDAC.ExecuteSp("asp_cls_CESearchR1", array(50, PageNumber, strTeam,  strWA, strSOP, strStatus, strStatusType))
    set objDAC = nothing

 End Function


Public Function Non_CESearch(PageNumber, strTeam,  strWA, strSOP)
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess


    set Non_CESearch = objDAC.ExecuteSp("asp_cls_Non_CESearch", array(50, PageNumber, strTeam,  strWA, strSOP))
    set objDAC = nothing

 End Function


 
 Public Function ReadTeamList()
   Dim objDAC
   set objDAC = new ASP_CLS_DataAccess
   set ReadTeamList = objDac.ExecuteSp("asp_sp_ReadTeamList", NULL)
    
   set objDAC = Nothing
 End Function




 Public Function ReadWorkArea()
   Dim objDAC
   set objDAC = new ASP_CLS_DataAccess
   set ReadWorkArea = objDac.ExecuteSp("asp_sp_ReadWorkArea", NULL)
    
   set objDAC = Nothing
 End Function
 
Public Function Approver(strBID)
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess


    set Approver = objDAC.ExecuteSp("asp_sp_ApproverWorkArea", array(strBID))
    set objDAC = nothing

 End Function
 
 Public Function AirEntry(strBID)
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess


    set AirEntry = objDAC.ExecuteSp("asp_sp_AirEntry", array(strBID))
    set objDAC = nothing

 End Function

Public Function Stations(strType)
    Dim objDAC

    set objDAC = new ASP_CLS_DataAccess


    set Stations = objDAC.ExecuteSp("asp_sp_Stations", array(strType))
    set objDAC = nothing

 End Function

End Class
%> 