
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Add Exception</TITLE>
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_SessionStringAIG.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->


<!--#include file="classes/asp_cls_headerAIG.asp"-->


<!--#include file="classes/asp_cls_DataAccessAIG.asp"-->
<%  

Dim strSQL, MyRec, strRelease, strDescription
 dim strtoday, strNeeded, strTomorrow, strNow
  strNow= DateAdd("h", -5, Now())
 strtoday = formatdatetime(strnow,2)
 strtomorrow = formatdatetime(dateadd("d", 1, strToday),2)


 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then


	Call SaveData() 

End if
%>
<style type="text/css">
.style1 {
	font-family: Arial;
	font-size: x-small;
}
</style>
</head>

<body>
<form name="form1" action="Exception_add.asp?id=<%=strid%>" method="post">
 
<table border="0" cellpadding="0" cellspacing="0" width = 60% style="border-collapse: collapse" bordercolor="000000" height="25" align = center >
  <tr>
    <td  bgcolor="#FFFFFF" bordercolor="#CCCCFF" height="25" >
    <p align="center"><font face="Arial" size="4">Add Exception to Research  &nbsp;&nbsp;&nbsp; 
    </font>  	</td><td align = center height="25"><font face="Arial"><b><a href="Exception_Search.asp">RETURN</a></b></font></td>

    <td align = center bgcolor="#FFFFFF" height="25">
  <Input name="Submit" type="submit" Value="Submit" style="float: right" ></td>
  </tr>
</table><br><br>
<div align="center">
<table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse; height: 198px;" bordercolor="#808080" width="75%">
    <tr>
    <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="22" align="center" class="style1" >
	<strong>Site</strong></td>
    <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="22" align="center" class="style1" >
	<strong>Release #</strong></font></td>
	 <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="22" align="center" class="style1" >
		<strong>Description</strong></td>
    <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="22" align="center" class="style1" >
	<strong>Date Needed By</strong></td>
   
  </tr>
  
    <tr>
    <td  bordercolor="#CCCCFF" height="47" width = "25%" align="center" style="width: 0%" >  		
	<font face="Arial">	
	<select size="1" name="Site">
	<option>Loudon</option>
	<option>Mobile</option>
	<option>Owensboro</option>
	</select></font></td>
    <td  bordercolor="#CCCCFF" height="47" width = "25%" align="center" style="width: 12%" >  	<font face="Arial">	
	<input type="text" name="Release" size="13"></font></td>
	   <td  bordercolor="#CCCCFF" height="47" width = "25%" align="center" >  	
    <textarea name="Description" style="height: 119px; width: 455px"><%= strDescription%></textarea></td>
    <td bordercolor="#CCCCFF" height="47" width = "25%" align="center" >	
		
	<font face="Arial">	
	<input type="text" name="Date_needed" size="13" value="<% = strtomorrow %>"></font></td>
    	

  </tr>


  </table>
</div></form>  
  

</body>

 <%
  
  Function SaveData()
 
 if isnull(Request.form("Description")) then
 strDescription =  ""
else
 strDescription =  Replace(Request.form("Description"), "'", "''") 
end if

 if isnull(Request.form("Release")) then
 strRelease =  ""
else
 strRelease =  Request.form("Release")
end if

 if isnull(Request.form("Date_needed")) then
 strNeeded =  strToday
else
 strNeeded =  Request.form("Date_needed")
end if

strSite = request.form("Site")

  
  strsql = "Insert into tblExceptions(Release, Date_reported, Author, Resolution_needed, site, status, Issue) "_
  &" Values('" & strRelease  & "', '" & strToday & "', '" & Session("Ename") & "', '" & strNeeded & "', '" & strSite & "', 'New', '" & strDescription & "')"

   
  	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
          Response.redirect("Exception_Search.asp")
  End Function
  
   %><!--#include file="AIGfooter.inc"-->