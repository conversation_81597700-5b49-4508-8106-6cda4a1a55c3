
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Broke Report </TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<%
	Dim strsQL, rstEquip
	strBegDate = DateSerial(Year(now()), Month(now()), 1) 
	if datepart("d", Date()) = 1 then
	strBegDate = DateSerial(Year(now()), Month(now()) -1, 1) 
	end if 
	
	strEndDate = Dateadd("d", -1, Date())
	strToday = Date()

strsql = "SELECT  count(tblcars.cid) as CountofcID FROM tblCars  where left(Broke_Description,3) = 'Tis' and "_
&"  location = 'Broke Center' and date_unloaded > '" & strBegDate & "' and date_unloaded <= '" & strToday & "' and RC_Transload is null"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   strTissueCount = MyRec("CountofCID")

   end if
   MyRec.close

strsql = "SELECT  count(tblcars.cid) as CountofcID FROM tblCars  where left(Broke_Description,3) = 'Tow' and "_
&"  location = 'Broke Center' and date_unloaded > '" & strBegDate & "' and date_unloaded <= '" & strToday & "' and RC_Transload is null"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   strTowelCount = MyRec("CountofCID")

   end if
   MyRec.close
   
strsql = "SELECT  count(tblcars.cid) as CountofcID FROM tblCars  where "_
&"  ((left(Broke_Description,3) <> 'Tow' and left(Broke_Description,3) <> 'Tis') or Broke_Description IS Null) and "_
&"  location = 'Broke Center' and date_unloaded > '" & strBegDate & "' and date_unloaded <= '" & strToday & "' and RC_Transload is null"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   strOtherCount = MyRec("CountofCID")

   end if
   MyRec.close


 strsql = "SELECT  count(tblcars.cid) as CountofcID FROM tblCars  where  "_
&"  (location = 'Broke Center' or location='BROKE CENTER') and date_unloaded > '" & strBegDate & "' and date_unloaded <= '" & strToday & "' and RC_Transload is null"
	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
   If not MyRec.eof then
   strCount = MyRec("CountofCID")

   end if
   MyRec.close

strET = 0
strMT = 0
strMetric = 0
strweight = 0
strDetentionTotal = 0
 strsql = "SELECT tblCars.* FROM tblCars "_
&" WHERE Date_unloaded > '" & strBegDate & "' AND  Date_unloaded <= '" & strToday & "' "_
&" and RC_Transload is null and (location = 'Broke Center' or location='BROKE CENTER') order by Date_unloaded"

	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
	while not MyRec.eof
	strWeight = MyRec("NEt")
	strTrailer = MyRec("Trailer")


 If strweight > 1000 then

strweight  = round(strweight / 2000,3)
strET = strET + strweight
else
strET = strET + strweight
end if
     	strdays = round(datediff("d", MyREc("date_received"), MyRec("Date_unloaded")),0)
  if len(MyRec("Trans_Carrier")) > 1 then
  	
		 strsql3 = "SELECT Free_days, Carrier, Fee FROM tblCarrier  where Carrier = '" & MyRec("Trans_Carrier") & "' "
 else 		 
           strsql3 = "SELECT Free_days, Carrier, Fee FROM tblCarrier  where Carrier = '" & MyRec("Carrier") & "' " 
end if   	
 Set rstEquip3 = Server.CreateObject("ADODB.Recordset")
    rstEquip3.Open strSQL3, Session("ConnectionString")  
    If Not rstEquip3.eof then
    strFee = rstEquip3.fields("Fee")
    strFree = rstEquip3.fields("Free_days")
    strCarrier = rstEquip3("Carrier")
    else
    strFee = 0
    strFree = 0
    strCarrier = ""
    end if  
    rstEquip3.close
    
                     
    If strCarrier = "RAIL"  or strCarrier = "Rail" Then
    
         strdays = int(strdays)
                if strdays = 0 then
          strDetention = 0
          elseif strdays = 1 then
          strDetention = 27.00
          elseif strdays = 2 then
          strDetention = 53.00
          
          elseIf strdays = 3 then 
          strDetention = 80.00
          elseif strdays = 4 then
          strDetention = 106.00
          elseif strdays = 5 then
          strDetention = 145.00
          elseif strdays = 6 then
          strDetention = 185.00
          elseif strdays = 7 then
          strDetention = 262.00
          elseif strdays = 8 then 
          strDetention = 339.00
          elseif strdays > 8 then
          strDetention = round(339.48 + (77.45*(strdays-8)),0)
          end if
  
	 
If left(MyRec("Trailer"), 4) = "GACX" then
strDetention = 0

end if   
    
     strDetentionTotal = strDetention + strDetentionTotal
  
    
  else  
    
    
    
    
    
    if strDays  > strFree then 
  strDetention =   (int(strDays) * strFee) - (strFee * strFree)
    strDetentionTotal = strDetention + strDetentionTotal
 end if 
end if
strDetention = 0

MyRec.movenext
wend
MyRec.close


strCarrier = ""
strMT = 0
strMetric = 0
strweight = 0
strDetention = 0
strCurrentDetentionTotal = 0
 strsql = "SELECT tblCars.* FROM tblCars "_
&" WHERE  (Species = 'Broke' or Species = 'BROKE' ) and Location = 'YARD'"

	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
	while not MyRec.eof
	
     	strdays = round(datediff("d", MyREc("date_received"),strtoday),0)
  if len(MyRec("Trans_Carrier")) > 1 then
  	
		 strsql3 = "SELECT Free_days, Carrier, Fee FROM tblCarrier  where Carrier = '" & MyRec("Trans_Carrier") & "' "
	
 else 		 
           strsql3 = "SELECT Free_days, Carrier, Fee FROM tblCarrier  where Carrier = '" & MyRec("Carrier") & "' " 
end if   	
 Set rstEquip3 = Server.CreateObject("ADODB.Recordset")
    rstEquip3.Open strSQL3, Session("ConnectionString")  
    If Not rstEquip3.eof then
    strFee = rstEquip3.fields("Fee")
    strFree = rstEquip3.fields("Free_days")
    strCarrier = rstEquip3("Carrier")
    else
    strFee = 0
    strFree = 0
    strCarrier = ""
    end if  
    rstEquip3.close
    
    
                     
    If strCarrier = "RAIL"  or strCarrier = "Rail" Then
    
         strdays = int(strdays)
          if strdays = 0 then
          strDetention = 0
          elseif strdays = 1 then
          strDetention = 26.52
          elseif strdays = 2 then
          strDetention = 53.04
          
          elseIf strdays = 3 then 
          strDetention = 79.56
          elseif strdays = 4 then
          strDetention = 106.08
          elseif strdays = 5 then
          strDetention = 145.33
          elseif strdays = 6 then
          strDetention = 184.58
          elseif strdays = 7 then
          strDetention = 262.03
          elseif strdays = 8 then 
          strDetention = 339.48
          elseif strdays > 8 then
          strDetention = 339.48 + (77.45*(strdays-8))
          end if
 
	 
			If left(MyRec("Trailer"), 4) = "GACX" then
			strDetention = 0
			end if   
    
    strCurrentDetentionTotal = strDetention + strCurrentDetentionTotal
  
    
  else  
    
    
    
    
    
    if strDays  > strFree then 
  strDetention =   (int(strDays) * strFee) - (strFee * strFree)
  strCurrentDetentionTotal = strDetention + strCurrentDetentionTotal
 end if 
end if
strDetention = 0

MyRec.movenext
wend
MyRec.close

strFree = 9
strFee = 0
strDays = 0
strDetention = 0
 %>
   <style type="text/css">
.style1 {
	text-align: left;
}
.style2 {
	font-family: Arial, Helvetica, sans-serif;
	text-align: left;
	font-size: x-small;
}
.style3 {
	font-family: Arial, Helvetica, sans-serif;
}
.style4 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.style5 {
	text-decoration: underline;
}
.style6 {
	text-align: left;
	font-size: x-small;
}
.style7 {
	font-size: x-small;
}
</style>
</head><br> 
<%
Set objMail = Server.CreateObject("CDO.Message")

		
			objMail.From = "<EMAIL>"
			strEmailto ="<EMAIL>"
			' strEmailTo = "<EMAIL>"
			objMail.To = strEmailto		
		

			objMail.Subject = "Broke Unload Activity Report MTD through " & strEndDate & " "

strbody = "<table style=width: 100% ><tr><td ><font face=arial size=2><strong>" &  Now()& "</strong></td></tr>"
strbody = strbody & "<tr><td><strong><font face=arial size=2>Date Range: " &  strBegDate & " - " &  strEndDate & "</strong></td></tr>"
strbody = strbody & "<tr><td><strong><font face=arial size=2>Current Trailers on Yard Detention:  $" &  strCurrentDetentionTotal &  "</strong></td></tr>"
strbody = strbody & "<tr><td><strong><font face=arial size=2>MTD Unloaded Trailer Detention: $" &  strDetentionTotal &  "</strong></td></tr></table><br>"
strbody = strbody & " <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=0 align = center><tr>"
strbody = strbody & "<td colspan=2 ><b><font face=Arial size=2>Total Tissue Count: " & strTissueCount & "</font></td></tr>"
strbody = strbody & " <tr><td ><b><font face=Arial size=2>Total Towel Count: " &  strTowelCount & " </font><br></td></tr>"
strbody = strbody & " <tr><td ><b><font face=Arial size=2>Total Other Count: " &  strOtherCount & " </font><br></td></tr>"
 strbody = strbody & " </table><br>"
 strbody = strbody & " <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=0 align = center><tr>"
strbody = strbody & "<td colspan=2 ><b><font face=Arial size=2>Total Trailer Count: " & strCount & "</font></td></tr>"
strbody = strbody & " <tr> <td colspan =2 bgcolor = white>&nbsp;</td></tr>"
strbody = strbody & " <tr><td ><b><font face=Arial size=2>Total Trailer Weight: " &  strET & " T (metric) <br>"


 strbody = strbody & "</font><br></td></tr> </table><br>"

  strMonth = MonthName(datepart("m", strBegDate))
 strbody = strbody & "<font face=Arial size=2><strong>" &  strMonth & " Trailer Unloads</strong><br><br>"
strbody = strbody & "<TABLE cellSpacing=0 cellPadding=0 width=90% border=1 align = left> "
strbody = strbody & "<td  align=left ><font face=Arial size=2><strong>Date Unloaded</strong></font></td>"
strbody = strbody & "<td  align=left ><font face=Arial size=2><strong>Outed By</strong></font></td>"
strbody = strbody & "<td  align=left ><font face=Arial size=2><strong>Release</strong></font></td>"
strbody = strbody & "<td  align=left ><font face=Arial size=2><strong>SAP #</strong></font></td>"
strbody = strbody & "<td  align=left ><font face=Arial size=2><strong>Broke Description</strong></font></td>"
strbody = strbody & "<td  align=left ><font face=Arial size=2><strong>Carrier</strong></font></td>"
strbody = strbody & "<td  align=left ><font face=Arial size=2><strong>Trailer</strong></font></td>"
strbody = strbody & "<td  align=left ><font face=Arial size=2><strong>From Rail Car</strong></font></td>"
strbody = strbody & "<td  align=left ><font face=Arial size=2><strong>Weight (Tons)</strong></font></td>"
strbody = strbody & "<td  align=left ><font face=Arial size=2><strong>Weight (T)</strong></font></td>"
strbody = strbody & "<td  align=left ><font face=Arial size=2><strong>Date Received</strong></font></td>"
strbody = strbody & "<td  align=center ><font face=Arial size=2><strong>Days on Yard</strong></font></td>"
strbody = strbody & "<td  align=center ><font face=Arial size=2><strong>Detention</strong></font></td></tr>"


strweight = ""

 strsql = "SELECT tblCars.* FROM tblCars "_
&" WHERE Date_unloaded > '" & strBegDate & "' AND  Date_unloaded <= '" & strToday & "' "_
&" and RC_Transload is null and location = 'Broke Center' order by Date_unloaded desc"

	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly

       ii = 0
   
   If not MyRec.eof then
       while not MyRec.Eof
       strCID = MyRec("CID")
    if ( ii mod 2) = 0 Then 
   strbody = strbody & "<tr bgcolor=#F1F1F8>"
    else
       strbody = strbody & "<tr bgcolor=#FFFFE8>"
       
  end if
   
      strbody = strbody & " <td  align=left ><font face = arial size = 1>" & MyRec.fields("Date_unloaded") & "&nbsp;</td>"
     strsql2 = "SELECT BID, From_location, To_location, CID FROM tblMovement WHERE to_location = 'Broke Center' AND CID = " & strCID & ""
   	Set MyRec2 = Server.CreateObject("ADODB.Recordset")
	MyRec2.Open strSQL2, Session("ConnectionString"), adOpenForwardOnly
If not MyRec2.eof then
strOutedBy = MyRec2("BID")
else
strOutedBy = ""
end if
MyRec2.close
 
    strbody = strbody & " <td  align=left ><font face = arial size = 1>" & strOutedBY & "&nbsp;</td>"
 
 
    strbody = strbody & " <td  align=left ><font face = arial size = 1>" & MyRec.fields("Release_Nbr") & "&nbsp;</td>"
      strbody = strbody & " <td  align=left ><font face = arial size = 1>" & MyRec.fields("Sap_Nbr") & "&nbsp;</td>"
      
     strbody = strbody & " <td  align=left ><font face = arial size = 1>" & MyRec.fields("Broke_Description") & "&nbsp;</td>"
 if MyRec("Trailer") = "UNKNOWN" then
 strbody = strbody & " <td  align=left ><font face = arial size = 1>" & MyRec.fields("Trans_Carrier") & "&nbsp;</td>"
 strbody = strbody & " <td  align=left ><font face = arial size = 1>" & MyRec.fields("Transfer_trailer_nbr") & "&nbsp;</td>"
 else 
 strbody = strbody & " <td  align=left ><font face = arial size = 1>" & MyRec.fields("Carrier") & "&nbsp;</td>"
 strbody = strbody & " <td  align=left ><font face = arial size = 1>" & MyRec.fields("Trailer") & "&nbsp;</td>"
end if
   
if MyRec("RC_CID") > 0 then
strRCID = MyRec("RC_CID")
strsql = "Select Trailer from tblcars where CID = " & strRCID & ""
Set MyRec2 = Server.CreateObject("ADODB.RecordSet")
		MyRec2.Open strSQL, Session("ConnectionString")
		strTrailer = MyREc2("Trailer")
		MyRec2.close 
		 strbody = strbody & " <td  align=left ><font face = arial size = 1>" & strTrailer & "&nbsp;</td>"	
 else 
	 strbody = strbody & " <td  align=left ><font face = arial size = 1>&nbsp;</td>"
 end if 
     

strweight = MyREc("Net")
strMetric = ""


strSAP = MyRec("SAP_NBR")
strsql = "Select UOM from tblBrokeSAP where SAP = '" & strSAP & "'"
    Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL, Session("ConnectionString")
if not MyRec2.eof then
strUOM = MyREc2("UOM")
if strUOM = "T" then
 If strweight > 1000 then
 strMetric = round(strweight / 2204.6,3)
 else
 strMetric = round((strweight * 2000)/2204.6,3)
 end if

elseif strUOM = "TONS" and strweight > 1000 then

strweight  = round(strweight / 2000,3)
end if
end if


 if strUOM = "T" then 
 strbody = strbody & " <td  align=left ><font face = arial size = 1>&nbsp;</td>"
 else 
  strbody = strbody & " <td  align=left ><font face = arial size = 1>" & strweight & "&nbsp;</td>"

 end if 
  strbody = strbody & " <td  align=left ><font face = arial size = 1>" & strMetric & "&nbsp;</td>"     
      
     strbody = strbody & " <td  align=left ><font face = arial size = 1>" & MyRec.fields("Date_Received") & "&nbsp;</td>"     
     
     	strdays = round(datediff("d", MyREc("date_received"), MyRec("Date_unloaded")),0)
      strbody = strbody & " <td  align=center ><font face = arial size = 1>" & strdays & "&nbsp;</td>"  	
  if len(MyRec("Trans_Carrier")) > 1 then
  	
		 strsql3 = "SELECT Free_days, Carrier, Fee FROM tblCarrier  where Carrier = '" & MyRec("Trans_Carrier") & "' "
 else 		 
           strsql3 = "SELECT Free_days, Carrier, Fee FROM tblCarrier  where Carrier = '" & MyRec("Carrier") & "' " 
end if 
strCarrier = ""  	
 Set rstEquip3 = Server.CreateObject("ADODB.Recordset")
    rstEquip3.Open strSQL3, Session("ConnectionString")  
    If Not rstEquip3.eof then
    strFee = rstEquip3.fields("Fee")
    strFree = rstEquip3.fields("Free_days")
    strCarrier = rstEquip3("Carrier")
    else
    strFee = 0
    strFree = 0
    end if  
    rstEquip3.close
    
                        
    If strCarrier = "RAIL"  or strCarrier = "Rail" Then
    
         strdays = int(strdays)
                 if strdays = 0 then
          strDetention = 0
          elseif strdays = 1 then
          strDetention = 27.00
          elseif strdays = 2 then
          strDetention = 53.00
          
          elseIf strdays = 3 then 
          strDetention = 80.00
          elseif strdays = 4 then
          strDetention = 106.00
          elseif strdays = 5 then
          strDetention = 145.00
          elseif strdays = 6 then
          strDetention = 185.00
          elseif strdays = 7 then
          strDetention = 262.00
          elseif strdays = 8 then 
          strDetention = 339.00
          elseif strdays > 8 then
          strDetention = round(339.48 + (77.45*(strdays-8)),0)
          end if
    	if strdays  > 2 then 
    	strExcess = 1
  			else            
       		
			strExcess = 0
	 end if 
	 
If left(strTrailer, 4) = "GACX" then
strDetention = 0
strExcess = 0
end if   
    
      strbody = strbody & " <td  align=center ><font face = arial size = 1>" & strDetention & "&nbsp;</td>" 
  
  elseif strDays  > strFree then 
  strDetention =   (int(strDays) * strFee) - (strFee * strFree)
    strbody = strbody & " <td  align=center ><font face = arial size = 1>" & strDetention & "&nbsp;</td>" 
    else	
	     strbody = strbody & " <td  align=center ><font face = arial size = 1>0&nbsp;</td>" 
 end if 
     	
       ii = ii + 1
      
       MyRec.MoveNext
     Wend
     end if





	objMail.HTMLBody = strbody
' objMail.Send
	
			Set objMail = nothing 
			if request.querystring("s") = "i" then
				Response.redirect("Send_yard_email.asp")
			else
		Response.redirect("Yard_rail_email.asp")
			   end if
			   %>