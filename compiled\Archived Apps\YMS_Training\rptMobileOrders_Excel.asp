
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">
<TITLE>Recovered Paper Orders</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

<%  
	Dim strsQL, rstEquip

   	Dim objEquipSearch, rstTotals, strCount, strDed, strNet, strTons, rstTotalReceived, strTR, objNew, rstSpecies
  	Dim  strVendor, strBegDate, strEndDate, strSpecies, strMonth, strDateReceived, strDateAdded, rstMonth, strShipWeek
 	Dim  rstVendor
  	Dim objGeneral, strPO, gcount,  strNetC, strOrderC, strNetSC, strOrderSC, strNetS
  	
  	strmonth = request.querystring("id")
  	strNet = 0
  	strNetC = 0
  	strOrderC = 0
  	strNetS = 0
  	strNetSC = 0
  	strOrderSC = 0
  	
  	strsql = "SELECT tblCars.trailer, tblCars.carrier, tblCars.date_received, tblCars.date_unloaded, tblCars.tons_received, tblCars.deduction, tblCars.net, "_
  	&" tblOrder.Species, tblOrder.PO, tblOrder.Vendor, tblOrder.Generator, tblOrder.City, tblOrder.State, tblOrder.Release, tblOrder.Import_month "_
  	&" FROM tblCars RIGHT JOIN tblOrder ON tblCars.OID = tblOrder.OID  where import_month = '" & strMonth & "' order by tblOrder.Vendor, tblorder.Species, tblOrder.release"
  	
  	   Set rstEquip = Server.CreateObject("ADODB.Recordset")
    rstEquip.Open strSQL, Session("ConnectionString")
    If not rstequip.eof then
    strVendor = rstEquip.fields("Vendor")
    strSpecies = rstEquip.fields("Species")
      Response.ContentType = "application/vnd.ms-excel"	
%>
<style type="text/css">
.style1 {
	font-family: Arial;
	font-size: x-small;
}
.style2 {
	font-size: x-small;
}
.style3 {
	border-width: 1px;
	background-color: #FFFFCC;
}
.style4 {
	font-size: xx-small;
}
.style5 {
	font-family: Arial;
}
</style>
</head>

<TABLE borderColor=#E7D1C2 cellSpacing=0 cellPadding=0 width="100%" border=0>  
  <tr><td colspan="5" size = "2" bordercolor="#FFFFFF"><b>
	<font face="Arial">Mobile Recovered Paper Orders&nbsp;&nbsp;&nbsp; <%= strMonth %></font></td>
	<td align = "RIght" bordercolor="#FFFFFF" class="style5">
	<strong><a href="rptMobileOrders_Excel.asp?id=<%= strMonth%>">Export to Excel</a></strong></td></tr></table>


   <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=90% border=1 align = center>
 
      <tr class="tableheader">
      <td  align="left" class="style2"><font face="Arial">Vendor</font></td>
 	<td  align="center" class="style1">Species&nbsp;</td>
<td  align="left" class="style2"><font face="Arial">Generator</font></td>
<td  align="left" class="style2"><font face="Arial">City</font></td>
<td  align="left" class="style2"><font face="Arial">State</font></td>
	
	<td  align="center" class="style2"><font face="Arial">Release</font></td>	
		<td  align="left" class="style2"><font face="Arial">Carrier</font></td>
	<td  align="center"><font face="Arial" size = 1 class="style2">Date<br> Received</font></td>
	
	<td  align="center" class="style2"><font face="Arial">Net</font></td>


      
  	<% 
      Dim ii
       ii = 0
       while not rstEquip.Eof
       
   
 
    if rstEquip.fields("Species") <> strSpecies then %>
     <tr><td bgcolor="#E8D9C8" colspan="9"><font face="arial"><b>
		<span class="style2"><span class="style4"><%= strVendor  %>&nbsp;&nbsp;&nbsp;Species: <%= strSpecies %> &nbsp;&nbsp;&nbsp;Fill Rate: 
     <% if strOrderC > 0 then %><%= round((strNetSC/strOrderSC)*100,2) %> <% else %>0<% end if %>%
    &nbsp;&nbsp;Order Count: <%= strOrderSC %>
     &nbsp;&nbsp;Filled Count: <%= strNetSC %>
      &nbsp;&nbsp;Total Weight:</span> <span class="style4"> <%= strNetS %></span></b> </font>  </td></tr>
	<% strNetSc = 0
    strOrderSC = 0
    strNetS = 0

	end if
	if rstEquip.fields("Vendor") <> strVendor then %>
   <tr> <td colspan="9" class="style3"><font face="arial"><b><span class="style2"><%= strVendor  %> Total&nbsp;&nbsp;&nbsp;Fill Rate: 
   <% if strNetC = 0 or strOrderC = 0 then %>
   0
   <% else %>
   <%= round((strNetC/strOrderC)*100,2)%>
   <% end if %> %
    &nbsp;&nbsp;Order Count: <%= strOrderC %>
     &nbsp;&nbsp;Filled Count: <%= strNetC %>
      &nbsp;&nbsp;Total Weight:</span> <span class="style2"> <%= strNet %></span></b> </font>  </td></tr>
   
    <% strNetc = 0
    strOrderC = 0
    strNet = 0
    
    strNetSc = 0
    strOrderSC = 0
    strNetS = 0


    
    
    
 end if
       if ( ii mod 2) = 0 Then %>
       <tr class=tablecolor1>
    <% else %>
       <tr class=tablecolor2>
    <% end if
    strOrderC = strOrderC + 1
    strOrderSC = strOrderSC + 1 %>
    <td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("Vendor")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Species")%>&nbsp;</td>
<td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("Generator")%>&nbsp;</td>
<td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("City")%>&nbsp;</td>
<td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("State")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Release")%>&nbsp;</td>
<td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("Carrier")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("date_received")%></td>




<% if rstEquip.fields("Net") > 0 then
strNet = strNet + rstEquip.fields("Net")
strNetC = strNetC + 1 
strNetS = strNetS + rstEquip.fields("Net")
strNetSC = strNetSC + 1 
%>
	<td  align="right"><font face="Arial" size = 1><%= round(rstEquip.fields("Net"),3)%></font></td>
<% else %>
	<td  align="right"><font face="Arial" size = 1>0</font></td>
<% end if %>

 

   </tr>
    <% 
       ii = ii + 1
       
       strVendor = rstEquip.fields("Vendor")
       strSpecies = rstEquip.fields("Species")
       rstEquip.MoveNext
     Wend
    %>
   
<% end if %>
  <tr><td bgcolor="#E8D9C8" colspan="9"><font face="arial"><b>
		<span class="style2"><span class="style4"><%= strVendor  %>&nbsp;&nbsp;&nbsp;Species: <%= strSpecies %> &nbsp;&nbsp;&nbsp;Fill Rate: 
     <% if strOrderC > 0 then %><%= round((strNetSC/strOrderSC)*100,2) %> <% else %>0<% end if %>%
    &nbsp;&nbsp;Order Count: <%= strOrderSC %>
     &nbsp;&nbsp;Filled Count: <%= strNetSC %>
      &nbsp;&nbsp;Total Weight:</span> <span class="style4"> <%= strNetS %></span></b> </font>  </td></tr>

<tr>   <td colspan="9" class="style3"><font face="arial" size="2"><b><%= strVendor  %> Total&nbsp;&nbsp;&nbsp; Fill Rate: 
<% if strOrderC = 0 then %>0
<% else %>
<%= round((strNetC/strOrderC)*100,2) %>
<% end if %>%
    &nbsp;&nbsp;Order Count: <%= strOrderC %>
     &nbsp;&nbsp;Filled Count: <%= strNetC %>
      &nbsp;&nbsp;Total Weight: <%= strNet %></b> </font>  </td>
</tr>
</table>