 
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">
<TITLE>Trailer Receiving Report</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

<%  
	Dim strsQL, rstEquip

   	Dim objEquipSearch, rstTotals, strCount, strDed, strNet, strTons, rstTotalReceived, strTR, objNew, rstSpecies
  	Dim  strVendor, strBegDate, strEndDate, strSpecies, strMonth, strDateReceived, strDateAdded, rstMonth, strDays
 	Dim  rstVendor, rstEquip3, strsql3, strFee, strFree
  	Dim objGeneral, strPO, gcount, strShuttle
   	
   	Dim intDirection
  	Dim intPageNumber
   	Dim strPageNav

   intPageNumber = 1

   set objGeneral = new ASP_CLS_General

  if objGeneral.IsSubmit() Then
    Call GetFormData()
    Select Case intDirection
      Case 10: 'goto mocinfo.asp
  
      Case Else:
        Call LoadSearchResults()
    End Select
  else
    intDirection = 0
  end if
Call GetData()
%>

<script language="javascript">
 
  function GotoMOC(MOCID)
  {
    document.forms["form1"].elements["tbl_ID"].value = MOCID;
    document.forms["form1"].elements["Direction"].value = 10;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }

  function Search()
  {
    
    document.forms["form1"].elements["Direction"].value = 1;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }

  function NextPage()
  {
    document.forms["form1"].elements["Direction"].value = 5;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }
  function PrevPage()
  {
    document.forms["form1"].elements["Direction"].value = 6;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }
</script>


<form name="form1" action="Trailer_receiving_report.asp" method="post">
 <input type="hidden" name="Direction" value="1" >
 <input type="hidden" name="tbl_ID" value="" >
 <input type="hidden" name=PageNumber value="<%=intPageNumber%>" >
 
 
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=80%  border=1 align = CENTER>  
  <tr><td  align = left><b><font face="Arial">Trailer Receiving Report</font></b></td>
	<td align = "RIght"><font face="Arial"><b><a href="Loginscreen.asp">HOME</a></b></font></td></tr></table>
	<div align="center">
<TABLE borderColor=#C0C0C0 cellSpacing=0 cellPadding=0 width=80% class="tablecolor1" border=1>  	
	
  <TR>
    <TD bgcolor="#F4F4FF" bordercolor="#F4F4FF"><font face="Arial" size="2"><b>Beginning Arrival Date:</b></font></TD>
   <TD bgcolor="#F4F4FF" bordercolor="#F4F4FF">
   <font face="Arial"><input name="BegDate" size="10" maxlength="10" value="<%=strBegDate%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633"></td>

    <TD bgcolor="#F4F4FF" bordercolor="#F4F4FF"><font face="Arial" size="2"><b>Ending Arrival Date:</b></font></TD>
    <TD bgcolor="#F4F4FF" bordercolor="#F4F4FF" colspan="2">
   <font face="Arial"><input name="EndDate" size="10" maxlength="10" value="<%=strEndDate%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633">
   </td></tr>
   
  <TR > <TD bgcolor="#F4F4FF" bordercolor="#F4F4FF"><font face="Arial" size="2"><b>Species:</b></font></TD>
  <TD bgcolor="#F4F4FF" bordercolor="#F4F4FF"> <select size="1" name="Species">

     <option value="">--- All ---</option>
       <%= objGeneral.OptionListAsString(rstSpecies, "Species", "Species", strSpecies) %>
     </select></td><td bgcolor="#F4F4FF" bordercolor="#F4F4FF"><b>
	<font face="Arial" size="2">Shuttles / Direct</font></b></td>
	<td bgcolor="#F4F4FF" bordercolor="#F4F4FF"><select size="1" name="Shuttle">
	<option <% if strShuttle = "ALL" then %>selected <% end if %> value="ALL">ALL</option>
	<option <% if strShuttle = "YES" then %>selected <% end if %> value="YES">SHUTTLES</option>
	<option <% if strShuttle = "NO" then %>selected <% end if %> value="NO" >DIRECT</option>
	</select></td>
   
    <TD align="center" bgcolor = "#F4F4FF" bordercolor="#F4F4FF">
	<input type="button" onClick="javascript:Search()" value="Search" caption="Search" style="float: right"></TD></TR>
  </TABLE></div>
</form>


  <% if objGeneral.IsSubmit() Then 

%>
   <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=75% border=1 align = center>
    <tr><td colspan="6" bgcolor="white"><font face="Arial" size="2"><b>Search Results</b> - Page <%=intPageNumber%></font></td>
    <td  align="center"><font face="Arial" size = 2>Total:&nbsp;<%=strCount%></font></td>

    </tr>
    <tr><td colspan="7" bgcolor="white" align="right"><%=strPageNav%>&nbsp;</td></tr>
      <tr class="tableheader">
      	<td  align="center" height="30"><font face="Arial" size = 1>Date<br> Received</font></td>
      	<% if strShuttle = "NO" then %>
      		<td  align="center" height="30"><font face="Arial" size = 1>Carrier</font></td>
      			<td  align="center" height="30"><font face="Arial" size = 1>Trailer</font></td>
      			<% elseif strShuttle = "YES" then %> 
      				<td  align="center" height="30"><font face="Arial" size = 1>Shuttle<br>Carrier</font></td>
      				<td  align="center" height="30"><font face="Arial" size = 1>Shuttle<br>Trailer</font></td>
      				<% else %>
      				<td  align="center" height="30"><font face="Arial" size = 1>Carrier</font></td>
      			<td  align="center" height="30"><font face="Arial" size = 1>Trailer</font></td>
      					<td  align="center" height="30"><font face="Arial" size = 1>Shuttle<br>Carrier</font></td>
      				<td  align="center" height="30"><font face="Arial" size = 1>Shuttle<br>Trailer</font></td>
      				<% end if %>
      				 	<td  align="center" height="30"><font face="Arial" size = 1>Species</font></td>
      					<td  align="center" height="30"><font face="Arial" size = 1>Date<br>Unloaded</font></td>
      			

	<td  align="center" height="30"><font face="Arial" size = 1>Days on Yard</font></td>

	<td  align="center" height="30"><font face="Arial" size = 1>Detention <br>To Date</font></td>


      
  	<% 
      Dim ii
       ii = 0
       while not rstEquip.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr class=tablecolor1>
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
  
    
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("date_recejved")%>&nbsp;</td>
	
	<% if strShuttle = "NO" then %>
<td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("Carrier")%>&nbsp;</td>
<td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("Trailer")%>&nbsp;</td>
<% elseif strShuttle = "YES" then %>
<td  >   <font size="1" face="Arial">        <%= rstEquip.fields("Trans_Carrier")%>&nbsp;</font></td>
<td  > <font size="1" face="Arial">&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; <%= rstEquip.fields("Transfer_trailer_nbr")%></font></td>
<% else %>
<td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("Carrier")%>&nbsp;</td>
<% if rstEquip.fields("Trailer") = "UNKNOWN" then %>
<td  align="left"><font face = "arial" size = "1">&nbsp;</td>
<% else %>
<td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("Trailer")%>&nbsp;</td>


<% end if %>
<td  >   <font size="1" face="Arial">        <%= rstEquip.fields("Trans_Carrier")%>&nbsp;</font></td>
<td  > <font size="1" face="Arial">&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; <%= rstEquip.fields("Transfer_trailer_nbr")%></font></td>
<% end if %>

<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Species")%>&nbsp;</td>

<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Date_unloaded")%>&nbsp;</td>



<% 	if isnull(rstEquip.fields("Date_unloaded")) then 
    strDays = int(Now()- cdate(rstEquip.fields("Date_recejved"))+ .5)
  		
  		else
		strdays =   round(datediff("d", rstEquip.fields("date_recejved"), rstEquip.fields("Date_unloaded")),0)
		end if  

if len(rstEquip.fields("Trans_Carrier")) > 1 then
  	
		 strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & rstEquip.fields("Trans_Carrier") & "' "
 else 		 
           strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & rstEquip.fields("Carrier") & "' " 
end if %>
<td  align="center"><font face = "arial" size = "1"><%= strdays  %>&nbsp;</td>


         <%           
 Set rstEquip3 = Server.CreateObject("ADODB.Recordset")
    rstEquip3.Open strSQL3, Session("ConnectionString")  
    If Not rstEquip3.eof then
    strFee = rstEquip3.fields("Fee")
    strFree = rstEquip3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    rstEquip3.close
    if strDays  > strFree then %>
    <td align = center><font face = "arial" size = "1"> $<%= (int(strDays) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font face = "arial" size = "1">$0&nbsp;</font></td>
	<% end if %>

	
  
   </tr>
    <% 
       ii = ii + 1
       rstEquip.MoveNext
     Wend
    %>
   </table>
<table>    <tr><td colspan="7" bgcolor="white" align="right"><%=strPageNav%>&nbsp;</td></tr></table>
<% end if %>


 <% Function GetData()

   set objNew = new ASP_Cls_Fiber
          
	set rstSpecies = objNew.FiberSpecies()
	set rstMonth= objNew.FiberMonth()

strShuttle = Request.form("Shuttle")

End Function
    Function GetFormData()
      intDirection = cint(Request.Form("Direction"))

 	strBegDate = request.form("BegDate")
	strEndDate = request.form("EndDate")
		strSpecies = request.form("Species")
		strShuttle = request.form("Shuttle")
	


      intPageNumber = cint(Request.Form("PageNumber"))

 
    End Function

    Function LoadSearchResults()
      Dim objEquipSearch, objTotals

      if intDirection =1 Then
         intPageNumber = 1
      end if

      if intDirection = 5 Then
         intPageNumber = intPageNumber + 1
      end if
      if intDirection = 6 Then
         intPageNumber = intPageNumber - 1
      end if


	strBegDate= request.form("BegDate")
	strEndDate = request.form("EndDate")


      set objEquipSearch = new ASP_Cls_Fiber
    
 If request.form("Shuttle") = "YES" then
  set rstEquip = objEquipSearch.TR_Shuttles(strBegDate, intPageNumber, strEndDate, strSpecies)
 set rstTotals = objEquipSearch.RPATotals_Shuttles(strBegDate, strEndDate, strSpecies)
 elseif request.form("Shuttle") = "NO" then
   set rstEquip = objEquipSearch.TR_Direct(strBegDate, intPageNumber, strEndDate, strSpecies)
 set rstTotals = objEquipSearch.RPATotals_Direct(strBegDate, strEndDate, strSpecies)
 
 else
 
 set rstEquip = objEquipSearch.TR_All(strBegDate, intPageNumber, strEndDate, strSpecies)
 set rstTotals = objEquipSearch.RPATotals(strBegDate, strEndDate, strSpecies)
end if

     if  not rstTotals.Eof Then
      strCount = rstTotals.fields("CountofOID").value

      
      end if 

     if ( not rstEquip.Eof) Then
      if ( intPageNumber < rstEquip.fields("TotalPage").value ) Then
        strPageNav = "<a href=javascript:NextPage()><B>Next Page</b></a>"
      end if
      if ( intPageNumber > 1 ) Then
         strPageNav = "<a href=javascript:PrevPage()><b>Previous Page</b></a>&nbsp;&nbsp;&nbsp;&nbsp;" & strPageNav
      end if
     end if

        if ( not rstTotals.Eof) Then
     strTR = rstTotals.fields("countofoid").value
     end if
    End Function
 %><!--#include file="Fiberfooter.inc"-->