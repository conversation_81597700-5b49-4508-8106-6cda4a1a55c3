																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Edit Fiber Trucks in Yard</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->




<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate

strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -7, strtdate)

strsql = "SELECT tblSapOPenPO.*  FROM tblSAPOpenPO  "_
&" WHERE tblSAPopenPO.Status is Null order by <PERSON>ur<PERSON><PERSON><PERSON>, item"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=60%  border=1 align = center>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Edit Inbound </b></font><b><font face="Arial">
KDF Loads</font></b></td>
<td align = center>&nbsp;</td>


	<td align = right><b><font face = arial size = 2><a href="SelectWeyInbound.asp">RETURN</a></font></b></td>
</tr>
	    </table>
	<br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=60% class = "tablecolor1" border=1 align = center>  
	 <tr class="tableheader">
<td>&nbsp;</td>
	
		<td  align = left>       <font face="Arial" size="1">PO #</font></td>
	<td  align = left">       <font face="Arial" size="1">Line item #</font></td>
	<td  ><font face="Arial" size="1"><b>Mutli-Item Load</b></font></td>
		<td  ><font face="Arial" size="1"><b>Trailer</b></font></td>
	
	<td  ><font face="Arial" size="1"><b>Brand</b></font></td>
		<td  ><font face="Arial" size="1"><b>SAP #</b></font></td>
			<td  ><font face="Arial" size="1"><b>QTY</b></font></td>
			
	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td> <font size="1" face="Arial"><a href="KDF_Edit.asp?id=<%= MyRec.fields("OID") %>">Edit</a></td>

	<td  ><font size="1" face="Arial"><%= MyRec.fields("PurchDoc")%></font></td>
		<td  ><font size="1" face="Arial"><b><%= MyRec.fields("Item")%>&nbsp;</font></b></td>
		<td  >        <font size="1" face="Arial">  <%= MyRec.fields("Multi_load")%>&nbsp;</font></td>
	<td  > <font size="1" face="Arial"><b><%= MyRec.fields("Trailer")%></font></b></td>
			<td  ><font size="1" face="Arial"><b><%= MyRec.fields("Brand")%>&nbsp;</font></b></td>
		<td  ><font face="Arial" size="1"> <%= MyRec.fields("Material")%></font></td>
		<td><font size="1" face="Arial"><%= MyRec.fields("Loaded_qty")%></font></td>
		
	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>


<!--#include file="Fiberfooter.inc"-->