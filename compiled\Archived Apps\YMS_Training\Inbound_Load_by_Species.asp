 

<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<meta http-equiv="REFRESH" content="60">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Inbound Load Summary</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

<%  
	Dim strsQL, rstEquip, objGeneral, objNew, rstSpecies, strSpecies, MyRec, strCommodity, strDigit


 	Dim  strdate, strDelDate, strcount, strPO


strdate = formatdatetime(now(),2)

   set objGeneral = new ASP_CLS_General
   

strsql = "SELECT tblInbound.Date_to as Delivery_date, Right([Material],8) AS commodity_desc, tblSapAutoImport.Material_desc, Count(tblInbound.PO) AS CountOford_id "_
&" FROM tblSapAutoImport INNER JOIN tblInbound ON tblSapAutoImport.DOC_Nbr = tblInbound.PO "_
&" WHERE (((tblInbound.Destination_City)='MOBILE')) "_
&" GROUP BY tblInbound.Date_to, Right([Material],8), tblSapAutoImport.Material_desc "_
&" ORDER BY tblInbound.Date_to, Right([Material],8)"

   
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 

  
  %>
 

</head>


<table border="0" cellpadding="0" cellspacing="0" width="100%" >


		 
      <tr class="tableheader"> 
      <td  valign = "top" width = 27%><font face = arial size = 2>
     	<b>
     <br> Count by Commodity</b></font><font face = arial size = 1><br>
      <Table width = 100% align = center><tr>
      
      
      
      
      
      <td  align="left"><font face="Arial" size = 1>Date</font></td>
       <td  align="left"><font face="Arial" size = 1>Commodity</font></td>
      <td  align="center"><font face="Arial" size = 1>Count</font></td>


      
  	<% 
      Dim ii
       ii = 0
       while not MyRec.Eof
 
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr class=tablecolor1>
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
     <td  align="left"><font face="Arial" size = 1>
     <%= WeekDayName(weekday(MyRec.fields("Delivery_date")), true) & " " &  MonthName(month(MyRec.fields("Delivery_date")), true) & " " & Day(MyRec.fields("Delivery_date")) %>
</font></td>
     
          <td  align="left"><font face="Arial" size = 1><%= MyRec.fields("commodity_desc")%>-<%= MyRec.fields("Material_Desc") %></font></td>
       
     <td  align="center"><font face="Arial" size = 1><%= MyRec.fields("countoford_id")%></font></td>

     
     </font></td> 
   </tr>
    <% 
       ii = ii + 1
       'strDelDate = MyRec.fields("pickup_dte") +  round((MyRec.fields("transit_miles")/500),0)
      ' strcount = strcount + 1
       MyRec.MoveNext
     Wend

MyRec.close


    %>

</table>
</td><td valign = "top" width = "1%" bgcolor = #C8C8C8>&nbsp;</td>
<td valign = "top" width = 13% >
<% 
  strsql = "SELECT  count(CID) as CountofOrd_id, carrier, date_To as Delivery_date FROM tblInbound where Destination_city = 'MOBILE' and '" & strdate & "' <= date_to"_
&" Group by date_to, carrier order by date_to, carrier" 


   
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
  
  %>   <TABLE >
  
  <Br><b><font face = arial size = 2>Count by Carrier</b></font></b><br>


 
      <tr class="tableheader">
      <td  align="left"><font face="Arial" size = 1>Date <%= strDate %></font></td>
       <td  align="center"><font face="Arial" size = 1>Carrier</font></td>
      <td  align="center"><font face="Arial" size = 1>Count</font></td>


      
  	<% 
    
       ii = 0
       while not MyRec.Eof
    
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor=#DFF9EA>
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
      <td  align="left"><font face="Arial" size = 1>     <%= WeekDayName(weekday(MyRec.fields("Delivery_date")), true) & " " &  MonthName(month(MyRec.fields("Delivery_date")), true) & " " & Day(MyRec.fields("Delivery_date")) %>
&nbsp;</font></td>

     
          <td  align="center"><font face="Arial" size = 1><%= MyRec.fields("carrier")%>&nbsp;</font></td>
       
     <td  align="center"><font face="Arial" size = 1><%= MyRec.fields("countoford_id")%>&nbsp;</font></td>

   </tr>
    <% 
       ii = ii + 1
 
       MyRec.MoveNext
     Wend
    %>
   </table>


<%
MyRec.close

 %>


</td><td  valign = "top" width = "1%" bgcolor=#C8C8C8></td>
  <td  valign = "top" width = 15%>
  <table width = 100%>
 <%   strsql = "SELECT  count(CID) as CountofOrd_id, left(release,1) as Species, date_To as Delivery_date FROM tblInbound "_
 &"  where Destination_city = 'MOBILE'  and (left(release,1) = 'K' or left(release,1) = 'k' "_
  &" or left(release,1) = 'C' or left(release,1) = 'c' "_
   &" or left(release,1) = 'M' or left(release,1) = 'm' "_
    &" or left(release,1) = 'F' or left(release,1) = 'f' "_
  &" or left(release,1) = 'P' or left(release,1) = 'p' "_
   &" or left(release,1) = 'D' or left(release,1) = 'd' "_
 &" or left(release,1) = 'H' or left(release,1) = 'h') "_

&" Group by date_to, left(release,1) Having '" & strdate & "' < Date_to order by date_to, left(release,1)" 


   
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
  
  %>  
  
  <b><font face = arial size = 2>Count by Secondary Fiber Species</b></font></b><br>
 
      <tr class="tableheader">
      <td  align="left"><font face="Arial" size = 1>Date</font></td>
       <td  align="center"><font face="Arial" size = 1>Species</font></td>
      <td  align="center"><font face="Arial" size = 1>Count</font></td>
      
  	<% 
    
       ii = 0
       while not MyRec.Eof
    
    %>
    <% if ( ii mod 2) = 0 Then %>
        <tr bgcolor=#FAFEC7>
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
         <td  align="left"><font face="Arial" size = 1>     <%= WeekDayName(weekday(MyRec.fields("Delivery_date")), true) & " " &  MonthName(month(MyRec.fields("Delivery_date")), true) & " " & Day(MyRec.fields("Delivery_date")) %>
&nbsp;</font></td>
     
          <td  align="left"><font face="Arial" size = 1>
          <%        
          if MyRec.fields("species") = "c"  or MyRec.fields("species") = "C" then %>
              OCC
              <% elseif MyRec.fields("species") = "k" or MyRec.fields("species") = "K" then %>  
               KBLD
            <% elseif MyRec.fields("species") = "d" or MyRec.fields("species") = "D" then %>  
              SHRED
               <% elseif MyRec.fields("species") = "h" or MyRec.fields("species") = "H" then %>  
              HBX

              <% elseif MyRec.fields("species") = "p" or MyRec.fields("species") = "P" then %>  
              PMX
                <% elseif MyRec.fields("species") = "F" or MyRec.fields("species") = "f" then %>  
              OF3

  <% elseif MyRec.fields("species") = "M" or MyRec.fields("species") = "m" then %>  
              MXP
                <% elseif MyRec.fields("species") ="L" or MyRec.fields("species") = "l" then %>  
             LPSBS

      <% elseif MyRec.fields("species") ="W" or MyRec.fields("species") = "w" then %>  
             HWM

       <% end if %>
          
          &nbsp;</font></td>
       
     <td  align="center"><font face="Arial" size = 1><%= MyRec.fields("countoford_id")%>&nbsp;</font></td>

     

   </tr>
    <% 
       ii = ii + 1
     
       MyRec.MoveNext
     Wend
    %>
   </table>


<%
MyRec.close

 %> 
  
  

  
  <td  valign = "top" width = "1%" bgcolor=#C8C8C8></td>
  <td  valign = "top" width = 10%>
 
   <% 
   
  strsql = "SELECT Count(tblSAPOpenPO.OID) AS CountOfOID,  Delivdate "_
  &" FROM tblSAPOpenPO WHERE tblSAPOpenPO.Status is null GROUP BY Delivdate   HAVING '" & strdate & "' <= Delivdate "_
			&" ORDER BY Delivdate"

   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 



  
  %>  
 <table width = 100%>
  
  <Br><b><font face = arial size = 2>BWIF from Wey</b></font></b><br>


 
      <tr class="tableheader">
      <td  align="left"><font face="Arial" size = 1>Date</font></td>
  
      <td  align="center"><font face="Arial" size = 1>Count</font></td>


      
  	<% 
    
       ii = 0
       while not MyRec.Eof
    
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor=#FFEADD  >
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
         <td  align="left"><font face="Arial" size = 1>     <%= WeekDayName(weekday(MyRec.fields("Delivdate")), true) & " " &  MonthName(month(MyRec.fields("Delivdate")), true) & " " & Day(MyRec.fields("Delivdate")) %>
&nbsp;</font></td>
     
           
     <td  align="center"><font face="Arial" size = 1><%= MyRec.fields("countofoid")%>&nbsp;</font></td>

     
     </font></td> 
   </tr>
    <% 
       ii = ii + 1
 
       MyRec.MoveNext
     Wend
    %>
   </table>
  
</td>  <td  valign = "top" width = "1%" bgcolor=#C8C8C8></td>
  <td  valign = "top" width = 15%>
   <TABLE >
  
  
     <tr><td align="center"><b><font face = arial size = 2>Count from OWB</b></font></b></td></tr>
    

      <td  align="left"><font face="Arial" size = 1>Date</font></td>
         <td  align="left" ><font face="Arial" size = 1>Material #</font></td>
      <td  align="center" ><font face="Arial" size = 1>Count</font></td>
      </tr>
      
      <% strsql = "SELECT Count(tblSapAutoImport.ID) AS CountOfID, tblSapAutoImport.Delivery_Date, Right([Material],8) AS Mat FROM tblSapAutoImport "_
&" WHERE (((tblSapAutoImport.Vendor)='OWENSBORO GLOBAL SALES MILL') AND ((tblSapAutoImport.Received_qty)=0)) "_
&" GROUP BY tblSapAutoImport.Delivery_Date, Right([Material],8) "_
&" HAVING (((tblSapAutoImport.Delivery_Date)>20090709) AND ((Right([Material],8))='70000166' Or (Right([Material],8))='70000071')) "_
&" ORDER BY tblSapAutoImport.Delivery_Date"

       Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
  
   %>
         
  	<% 
    
       ii = 0
       while not MyRec.Eof
    
    %>
    <% if ( ii mod 2) = 0 Then %>
           <tr class=tablecolor1>
    <% else %>
       <tr class=tablecolor2>
    <% end if %>

         <td  align="left" ><font face="Arial" size = 1><%= mid(MyRec.fields("Delivery_Date"),5,2)%>-<%= right(MyRec.fields("Delivery_Date"),2)%></font></td>
         <td  align="left" ><font face="Arial" size = 1><%= MyRec.fields("Mat")%></font></td>
      <td  align="center" ><font face="Arial" size = 1><%= MyRec.fields("countofid")%></font></td>

   </tr>
    <% 
       ii = ii + 1
 
       MyRec.MoveNext
     Wend
    %>


   </table>


<%
MyRec.close

 %>
  </tr></table>

