<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>OF Recap</title>
</head>
<%
dim strbody, strCID, strECarrier, strETrailer, strELoad, strEDoc, strEDC, strEDCName, strEAdd, strECity, strEState
dim strECC, strEBCC, strESpecies, MyRec, strsql, MyConn2
dim str7, str6, str5, str4, str3, str2, str1, str0
dim str7c, str6c, str5c, str4c, str3c, str2c, str1c, str0c, strAnchor


str6c = "0"
str5c = "0"
str4c = "0"
str3c = "0"
str2c = "0"
str1c = "0"
str0c = "0"
strAnchor = "10/22/2011"
strAnchorAmt = 122
strNow = formatdatetime(Now(),2)
str0 = strnow

strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars where Trans_Unload_date > '" & strAnchor & "'  and Trans_Unload_date <= '" & str0 & "' "_
&"  AND Species = 'KCOP'"
	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
   	 
   	 strdays0 = 122 - MyRec.fields("countofCID")
   	 MyRec.close



strnow = dateadd("d", -7, strNow)
str7 = strnow


str6 = dateadd("d", 1, strnow)
strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars where Trans_Unload_date > '" & strAnchor & "' and Trans_Unload_date <= '" & str6 & "' "_
&"  AND Species = 'KCOP'"
	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
   	 
   	 strdays6 = 122 - MyRec.fields("countofCID")
   	 MyRec.close

str5 = Dateadd("d", 1, str6)
strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars where Trans_Unload_date > '" & strAnchor & "' and Trans_Unload_date <= '" & str5 & "' "_
&"  AND Species = 'KCOP'"
	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
   	 
   	 strdays5 = 122 - MyRec.fields("countofCID")
   	 MyRec.close


str4 = dateadd("d", 1, str5)
strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars where Trans_Unload_date > '" & strAnchor & "' and Trans_Unload_date <= '" & str4 & "' "_
&"  AND Species = 'KCOP'"
	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
   	 
   	 strdays4 = 122 - MyRec.fields("countofCID")
   	 MyRec.close


str3 = dateadd("d", 1, str4)
strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars where Trans_Unload_date > '" & strAnchor & "' and Trans_Unload_date <= '" & str3 & "' "_
&"  AND Species = 'KCOP'"
	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
   	 
   	 strdays3 = 122 - MyRec.fields("countofCID")
   	 MyRec.close


str2 = dateadd("d", 1, str3)
strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars where Trans_Unload_date > '" & strAnchor & "' and Trans_Unload_date <= '" & str2 & "' "_
&"  AND Species = 'KCOP'"
	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
   	 
   	 strdays2 = 122 - MyRec.fields("countofCID")
   	 MyRec.close


str1 = dateadd("d", 1, str2)
strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars where Trans_Unload_date > '" & strAnchor & "'and Trans_Unload_date <= '" & str1 & "' "_
&"  AND Species = 'KCOP'"
	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
   	 
   	 strdays1 = 122 - MyRec.fields("countofCID")
   	 MyRec.close



strnow = datepart("y", strnow)
stryear = datepart("yyyy", now())

          ' strEmailTo = "<EMAIL>"
        strEmailTo = "<EMAIL>"      

 strEBCC = "<EMAIL>"
                 
                 
			Set objMail = Server.CreateObject("CDO.Message")		
			objMail.From = "<EMAIL>"
			objMail.To = strEmailTo
			'objMail.CC = strECC
			objMail.BCC = strEBCC
	
			objMail.Subject = "KCOP Shuttle Recap"
			
strBody2 = "<table border=0  cellpadding=0 cellspacing=0 width=400><tr><td align = center><font face = arial size = 2>&nbsp; Date Consumed&nbsp; </td><td align = left><font face = arial size = 2># Loads&nbsp; </td><td align = left><font face = arial size = 2>Target&nbsp; </td><td align = left><font face = arial size = 2>Remaining Loads&nbsp; </td></tr>"
			

strsql2 = "SELECT datepart(y, [trans_unload_date]) AS Expr1, Datepart(d, trans_unload_date) as Inv_Date, Datepart(m, trans_unload_date) as Inv_month, Sum(tblCars.Net) AS SumOfNet, Count(tblCars.CID) AS CarCount FROM tblCars "_
&" WHERE  Species = 'KCOP' "_
&" GROUP BY datepart(y, [trans_unload_date]), Datepart(d, trans_unload_date), Datepart(m, trans_unload_date), datepart(yyyy, trans_unload_date) "_
&" HAVING datepart(y, [trans_unload_date]) > '" & strNow & "' and datepart(yyyy, trans_unload_date) = '" & stryear & "' "_
&" ORDER BY datepart(y, [trans_unload_date])"

   	 Set MyRec2 = Server.CreateObject("ADODB.Recordset")
   	 MyRec2.Open strSQL2, Session("ConnectionString")

If not MyRec2.eof then
				While not MyRec2.EOF
strConsumptionDate = MyRec2.fields("Inv_month")  & "/" & MyRec2.fields("Inv_date")
strday = MyRec2.fields("Inv_date")
strMonth = MyRec2.fields("Inv_month")	

if strday = datepart("d", str0) then

 str0c = MyRec2.fields("CarCount")  	
			
elseif strday = datepart("d", str1) then

 str1c = MyRec2.fields("CarCount")
 
 elseif strday = datepart("d", str2) then

 str2c = MyRec2.fields("CarCount")
 
 elseif strday = datepart("d", str3) then

 str3c = MyRec2.fields("CarCount")
 
 elseif strday = datepart("d", str4) then

 str4c = MyRec2.fields("CarCount")
  
 elseif strday = datepart("d", str5) then

 str5c = MyRec2.fields("CarCount")
 
  
 elseif strday = datepart("d", str6) then

 str6c = MyRec2.fields("CarCount")
 
end if



	MyRec2.MoveNext
     			WEND
           		MyRec2.Close
           	
	strbody2 = strbody2 & " <tr><td align = center><font face = arial size = 2> " & str6 &  "</td><td align = left><font face = arial size = 2>" & str6c & "</td><td align = left><font face = arial size = 2>3</td><td align = left><font face = arial size = 2>" & strdays6 & "&nbsp; </td></tr> "
	strbody2 = strbody2 & " <tr><td align = center><font face = arial size = 2> " & str5 &  "</td><td align = left><font face = arial size = 2>" & str5c & "</td><td align = left><font face = arial size = 2>3</td><td align = left><font face = arial size = 2>" & strdays5 & "&nbsp; </td></tr> "	
	strbody2 = strbody2 & " <tr><td align = center><font face = arial size = 2> " & str4 &  "</td><td align = left><font face = arial size = 2>" & str4c & "</td><td align = left><font face = arial size = 2>3</td><td align = left><font face = arial size = 2>" & strdays4 & "&nbsp; </td></tr> "
	strbody2 = strbody2 & " <tr><td align = center><font face = arial size = 2> " & str3 &  "</td><td align = left><font face = arial size = 2>" & str3c & "</td><td align = left><font face = arial size = 2>3</td><td align = left><font face = arial size = 2>" & strdays3 & "&nbsp; </td></tr> "	
	strbody2 = strbody2 & " <tr><td align = center><font face = arial size = 2> " & str2 &  "</td><td align = left><font face = arial size = 2>" & str2c & "</td><td align = left><font face = arial size = 2>3</td><td align = left><font face = arial size = 2>" & strdays2 & "&nbsp; </td></tr> "
	strbody2 = strbody2 & " <tr><td align = center><font face = arial size = 2> " & str1 &  "</td><td align = left><font face = arial size = 2>" & str1c & "</td><td align = left><font face = arial size = 2>3</td><td align = left><font face = arial size = 2>" & strdays1 & "&nbsp; </td></tr> "
	strbody2 = strbody2 & " <tr><td align = center><font face = arial size = 2> " & str0 &  "</td><td align = left><font face = arial size = 2>" & str0c & "</td><td align = left><font face = arial size = 2>3</td><td align = left><font face = arial size = 2>" & strdays0 & "&nbsp; </td></tr> "		

	objMail.HTMLBody = "<font face = arial size = 2>The following is a recap of the KCOP Shuttle loads consumed in the past week: <font color=red>Target is to be 0 Remaining Loads on 11/30. </font><br><br><p align=left><b>Note:  These numbers are based on the trailers/cars outed on shift by Fiber technicians.  The numbers can change based on validation work or subsequent changes to entries. </p></b><br><br>" & strbody2



			' objMail.Send
			Set objMail = nothing
	end if


   Response.redirect("Send_yard_email.asp")

 %>

