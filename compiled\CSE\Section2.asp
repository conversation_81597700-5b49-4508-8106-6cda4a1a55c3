<html>
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>This assessment is intended to document a summary of&nbsp; hazards and 
control measures required to permit safe entry</title>
</head>
<% dim strAirExchange, strVolume, strFtoM, strNaturalAir, strMechanicalAir, strInternalConfig, strConfigReduced
dim strATNonEntryLocation1, strATNonEntryLocation2, strATNonEntryLocation3, strATNonEntryLocation4
dim strATNE1Tech, strATNE2Tech, strATNE3Tech, strATNE4Tech, strCompartName, strNonConductive
Dim strSpaceID, strLocation, strSDescription, strcid,  strAir1
Dim strReducdedList1, strReducedList2, strIllumIssues, strLowVolt, strGFIRequired, strOtherHazImpairRescue, strRescueControl
Dim strOtherHazIDLH, strOtherHazDrop, strDropHazControl, strImmediate_danger, strAnyNonEntryAirTest, strHid, strVenSPReq
Dim strApprover1, strApprover2, strE_name, strHAstatus

strid = Request.querystring("id")
strcid = Request.querystring("cid")
strE_name = Session("Ename")

strsql = "SELECT tblHA.*, tblSOP.LOCATION, tblSOP.SDescription FROM tblHA INNER JOIN tblSOP ON tblHA.SpaceID = tblSOP.SOP_NO "_
  &" where SpaceID = '" & strid & "'"
Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
  If not MyConn.eof then
 strSpaceid = strid
 strLocation = MyConn.fields("Location")
 strSdescription = MyConn.fields("SDescription")
 strHid = MyConn.fields("IDHazAssess")
 strApprover1 = MyConn.fields("HA_Approver_1")
 strApprover2 = MyConn.fields("HA_Approver_2")
 strHAStatus = MyConn.fields("HA_Status")
 end if 
 MyConn.close 
  
 strsql = "SELECT tblCompartment.* from tblCompartment where ID = " & strcid & ""

Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
  If not MyConn.eof then 

 
strVentSPReq = MyConn.fields("Vent_SPReq")
strAirExchange = MyConn.fields("airEexchanges")
strVolume = MyConn.fields("Volume")
strFtoM = MyConn.fields("FtoM")
strNaturalAir = MyConn.fields("NaturalAir")
strMechanicalAir = MyConn.fields("MechanicalAir")

strATNonEntryLocation1 = MyConn.fields("ATNonEntryLocation1")
strATNonEntryLocation2 = MyConn.fields("ATNonEntryLocation2")
strATNonEntryLocation3 = MyConn.fields("ATNonEntryLocation3")
strATNonEntryLocation4 = MyConn.fields("ATNonEntryLocation4")
strATNE1Tech = MyConn.fields("ATNE1Tech")
strATNE2Tech = MyConn.fields("ATNE2Tech")
strATNE3Tech = MyConn.fields("ATNE3Tech")
strATNE4Tech = MyConn.fields("ATNE4Tech")

strInternalConfig = MyConn.fields("InternalConfig")
strCompartName = MyConn.fields("CompartName")
strconfigreduced = MyConn.fields("Config_Reduced")
strReducedList1 = MyConn.fields("ReducedList1")
strReducedList2 = MyConn.fields("ReducedList2")

strOtherHazImpairRescue = Myconn.fields("OtherHazImpairRescue")
strRescueControl = MyConn.fields("RescueControl")
strOtherHazIDLH = MyConn.fields("OtherHazIDLH")
strOtherHazDrop = MyConn.fields("OtherHazDrop")
strDropHazControl = MyConn.fields("DropHazControl")

strImmediate_danger = Myconn.fields("Immediate_danger")

strAnyNonEntryAirTest = MyConn.fields("AnyNonEntryAirTest")
end if
MyConn.close 


	set objGeneral = new ASP_CLS_General
  		 if objGeneral.IsSubmit = True Then
  		 call savedata()
 	
  		
  		 End if
  		 %>


<body>
<form name="form2" action="Section2.asp?cid=<%= strcid %>&id=<%= strid%>"  method="post" ID="Form2"  >
<input type = hidden name = "HAStatus" value = <%= strHAstatus%>>


  <table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="100%">
  <tr>
    <td width="4%" height="19" bgcolor="#FFFFDD">
	<p align="center"><font size="2" face="Arial">
	&nbsp;Space ID</font></td>
    <td width="25%" height="19" bgcolor="#FFFFDD" align="center"><font face = arial size = 2>Space Name</td>
    <td width="28%" height="19" bgcolor="#FFFFDD" align="center"><font face="Arial" size="2">
	Location</font></td>

    <td width="21%" bgcolor="#FFFFDD" height="19">
	<p align="center"><b><font face="Arial">Entry Portals<br></font></b><font face="Arial" size="2"><a target = "_blank" href="Portal_add.asp?id=<%= strhid%>&cid=<%= strcid%>">Add New</a></font></td>
   
  </tr>
  <tr>
    <td width="4%" align = center height="19" bgcolor="#FFFFFF" > <font face = arial size = 2><%= strSpaceID %></td>
    <td width="25%" height="40" bgcolor="#FFFFFF"  ><font face="Arial" size="2">
	<%= strSdescription %></font></td>
	<td bgcolor="#FFFFFF"><font face="Arial" size="2"><%= strLocation %></td>

      <td  bgcolor="#FFFFDD" height="19"><font face="Arial" >
    <% Dim strsqlC, MyConnC
    strsqlC = "SELECT tblPortals.* from tblPortals where Compartment_ID= " &  strcid
    Set MyConnC = Server.CreateObject("ADODB.Recordset") 
   		MyConnC.Open strSQLC, Session("ConnectionString")
  If not MyConnC.eof then %>
  
  	<% 
      Dim ii
       ii = 0
       while not MyConnC.Eof %>
       <font face = arial size = 1><a href="Section3.asp?pid=<%= MyConnc.fields("ID")%>&id=<%= strid%>">View</a>
&nbsp;&nbsp;<%= MyConnC.fields("NameLocPortal")%>&nbsp;

<%	Dim stringWA, MyFile, MyBID
      strsql = "Select Workarea from tblSOP where SOP_NO = '" & strid & "'"
       Set MyFile = Server.CreateObject("ADODB.Recordset") 
   		MyFile.Open strSQL, Session("ConnectionString")
   		stringWA = MyFile.fields("Workarea")
   		
   		MyFile.close 
   		 MyBID = Session("EmployeeID")
   		 strSQL = "SELECT tblApprovers.* from tblApprovers where Work_area = '" & stringWA & "' and P_BID = '" & MyBID & "'"
      Set MyFile = Server.CreateObject("ADODB.Recordset") 
   		MyFile.Open strSQL, Session("ConnectionString")
   		 If not MyFile.eof or  Session("EmployeeID") = "C97338" or Session("BID") = "B54758" then %>


<a target = "_blank" href="Portal_Delete.asp?id=<%= MyConnc.fields("ID")%>">Delete</a>
<% MyFile.close
end if %>
</font>
 <br>
    <%
       ii = ii + 1
       MyConnC.MoveNext
     Wend
     End if
     MyConnc.close
    %>

 </td>
  
  </tr>
</table>
<table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="100%">
  <tr>
    <td  bgcolor="#FFFF97"><b>
	<font face="Arial"><font size="2">Compartment Analysis&nbsp; </font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	Section 2:&nbsp; Compartment Review</font></b></td>
  </tr></table>
<table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#D9E1F9">
  <tr>
    <td width="100%" bgcolor="#FF0000" height="40"><p align="center">
	<span style="font-family: Arial; font-weight: 700">
	<font color="#FFFFFF">When completing this section, consider the hazards associated with one 
	compartment at a time.&nbsp; A compartment is a section of a space that has its 
	own access/egress port(s) separate from another compartment in the space.&nbsp; 
	For each compartment the ventilation, configuration, lighting, required PPE, 
	and non-entry portal air test locations must be provided.&nbsp;<font size="2">
	</font></font> </span></td>
  </tr></table>

  <table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#F2F5FD">
 

  <tr>
    <td bgcolor="#FFFFDD" height="40" >
  <table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" id="table1">
  <tr>
    <td width="81%" height="35" bgcolor="#FFFFDD" align="center"><b>
	<font face="arial">Name of Compartment</font></b></td>
    <td width="13%" height="35" bgcolor="#FFFFDD" align="center">
	<font face="Arial"><a href="rptSection2.asp?cid=<%= strcid%>&id=<%= strid%>">Printer Friendly</a></font></td>
   <td align = right width="6%"><INPUT TYPE="submit" value="Submit"></td>
  </tr>
  <tr>
    <td height="40" align = center bgcolor="#FFFFFF" colspan=3 ><font face="Arial" size="2">
	
	<input type="text" name="CompartName" size="50" value="<%= strCompartName %>" maxlength="50">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </font></td>
  
  </tr>
</table>

	<b><font face="Arial">Ventilation&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	Identify the volume of the compartment (length x width x height)</font></b></td>
   </tr>


 
  <tr>
    <td height="25" ><font size="2" face="Arial">GUIDELINE: Less than 3000 cubic 
	feet or 85 cubic meters = 20 air exchanges p/h or greater than 3000 cubic 
	feet or 85 cubic meters = 10 air changes p/h. &nbsp;&nbsp; </font></TD>
    


  </tr>
  <tr>
    <td height="44" ><font face="Arial" size="2">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	Volume:&nbsp;&nbsp; </font><font face="Arial">
	<!--webbot bot="Validation" s-data-type="Number" s-number-separators=",." -->
	<input type="text" name="volume" size="4" value="<%= strVolume%>">&nbsp;&nbsp;&nbsp;
	<select size="1" name="FtoM">
	<option value = "" >Select UOM</option>
	<option <% if strFtoM = "cubic feet" then%> selected <% end if %>>cubic feet</option>
	<option <% if strFtoM = "cubic meter" then%> selected <% end if %>>cubic meter</option>
	</select>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	</font><font face="Arial" size="2">Recommended Air Exchanges&nbsp; <%= strAirExchange%>&nbsp; 
	p/h</font>
		<p><font face="Arial">&nbsp;<font size="2">&nbsp; In this compartment, 
		will you use natural ventilation or mechanical ventilation during the 
		work phase?<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </font></font>
		<input type="checkbox" name="NaturalAir" <% if strNaturalAir = -1 then %> checked <% end if %> value="ON"><font face="Arial" size="2">&nbsp; 
		Natural Ventilation (2 openings recommended) <br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </font>
		<input type="checkbox" name="MechanicalAir" <% if strMechanicalAir = -1 then %> checked <% end if %> value="ON"><font face="Arial" size="2">&nbsp; 
		Mechanical Ventilation</font><br>
		<font face="Arial" size="2">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
		List any Special Requirements:
	
	</font><font face="Arial"><% if len(strVentSPreq) > 0 then %>
		<input type="text" name="Vent_SpReq" value="<%= server.HTMLEncode(strVentSPReq) %>" size="87" ></TD>
 <% else %>
 
 <input type="text" name="Vent_SpReq" value="<%= strVentSPReq %>" size="87" ></TD>
 <% end if %>
 


  </tr>
</table>
  
    <table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#D9E1F9" height="30">
 

  <tr>
    <td bgcolor="#FFFFDD" height="40" ><b><font face="Arial">Internal 
	Configuration&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	</font></b></td>
   </tr></table>

   <table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#F2F5FD">
 
  <tr>
    <td ><font face="Arial">
	Does this compartment have an internal configuration that could allow someone 
	entering to be trapped or asphyxiated by inward converging walls or by a 
	floor, with slopes downward and tapers to a smaller cross-section.&nbsp;&nbsp;&nbsp;
	<select size="1" name="InternalConfig">
	<option >Select Value</option>
	<option <% if strInternalConfig = -1 then %> selected <% end if %>>Yes</option>
	<option <% if strInternalConfig = 0 then %> selected <% end if %>>No</option>
	</select></font><font size="2" face="Arial"><br></font></td>
    
  </tr></table>
   <table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#F2F5FD">
 
  <tr>
    <td width="100%" ><font face="Arial" size="2">&nbsp;&nbsp;&nbsp; Can it be 
	addressed to reduce the potential for harm?&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	</font><font face="Arial"><select size="1" name="ConfigReduced">
	<option >Select Value</option>
	<option <% If strConfigReduced = -1 then %> selected <% end if %>>Yes</option>
	<option <% If strConfigReduced = 0 then %> selected <% end if %>>No</option>
	</select>&nbsp;&nbsp; <font size="2">If Yes, list what is needed to control 
	hazard: (platform, blocked, etc.)</font></font><font face="Arial" size="2"><br></font></td>
  </tr> 
  
  <tr>
    <td width="100%" ><font face="Arial" size="2">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	1.&nbsp; &nbsp;&nbsp;</font><font face="Arial"><input type="text" name="ReducedList1" size="100" value="<%= strReducedList1%>" maxlength="100"></font><font face="Arial" size="2">
	</font></td>
  </tr> 

 <tr>
    <td width="100%" ><font face="Arial" size="2">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
	2.&nbsp; &nbsp;&nbsp;</font><font face="Arial"><input type="text" name="ReducedList2" size="100" value="<%= strReducedList2%>" maxlength="100"></font><font face="Arial" size="2">
	</td>
  </tr> 
 


  <tr>
   
    <td ><font size="2" face="Arial">&nbsp;</font></td>
  
    <td >&nbsp;</td>

  <td colspan = 2 ><font size="2" face="Arial">&nbsp;</font></td>

  </tr></table>
  <table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#F2F5FD">
 

  <tr>
    <td width="100%" bgcolor="#FFFFDD" height="40"><b><font face="Arial">Other 
	Recognized Safety or Health Hazards:&nbsp; Did we miss anything?</font></b></td>
  </tr>
  <tr>
    <td width="100%"><font face="Arial">Does this compartment contain any other 
	recognized safety or health hazards that could either:</font></td>
  </tr>
 
 
  <tr>
    <td width="100%" >&nbsp;</td>
  </tr></table>
    <table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#F2F5FD">
  <tr>
    <td height="22"><font face="Arial" size="2">&nbsp;&nbsp; </font>
		<input type="checkbox" name="OtherHazImpairRescue" <% if strOtherHazImpairRescue = -1 then %> checked <% end if %> value="ON">
	<font face="Arial" size="2">Impair the ability to self rescue&nbsp;&nbsp;If so, list control:&nbsp; </font>
	<input type="text" name="RescueControl" size="85" value="<%= strRescueControl%>" maxlength="250"></td>
    <td height="22">&nbsp;</td>
   
  </tr>
  <tr>
    <td><font face="Arial" size="2">&nbsp;&nbsp; </font>
		<input type="checkbox" name="OtherHazIDLH" <% if strOtherHazIDLH = -1 then %> checked <% end if %>value="ON" value="ON">
	<font face="Arial" size="2">Result in a situation that presents an immediate 
	danger to life or health?&nbsp; List hazard and control: </font>
	<input type="text" name="Immediate_danger" size="49" value="<%= strImmediate_danger%>"></td>
    <tr><td>
		&nbsp;&nbsp;
		<input type="checkbox" name="OtherHazDrop"  <% if strOtherHazDrop = -1 then %> checked <% end if %> value="ON">
	<font face="Arial" size="2">Can anything from above drop into the space?&nbsp; 
	If it can be controlled, list what is needed to control hazard:&nbsp; </font>
	<input type="text" name="DropHazControl" size="41" value="<%= strDropHazControl%>" maxlength="150"><br><br></td>
   
  

</table>
    <table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#D9E1F9">
 
  <tr><td bgcolor="#FFFFDD" height="40"><font face="Arial"><b>Non-entry Ports&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </b></font></td> </tr>
    </table>

<table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#F2F5FD">
 	   <tr height = 40><td><font face="arial" size="2">Are any air 
		tests done at NON-ENTRY ports in this compartment?&nbsp;&nbsp;&nbsp;
		</font><font face="Arial"><select size="1" name="AnyNonEntryAirTest">
	<option >Select Value</option>
	<option <% if strAnyNonEntryAirTest = -1 then %> selected <% end if %>>Yes</option>
	<option <% if strAnyNonEntryAirTest = 0 then %> selected <% end if %>>No</option>
	</select>&nbsp;&nbsp; <font size="2">If Yes, list all air test locations and 
		note the proper test technique.</font></font></td></tr>
	
	<tr><td>
	
		<font face="Arial" size="2">&nbsp;&nbsp;&nbsp;&nbsp; Location 1&nbsp; </font>
		<input type="text" name="ATNonEntryLocation1" size="42" value="<%= strATNonEntryLocation1 %>" maxlength="100">&nbsp;&nbsp;&nbsp;
		<font face="Arial" size="2">Test Technique:&nbsp; </font><font face="Arial">
		<select size="1" name="ATNE1Tech">
	<option value = "" >Select Value</option>
	<option <% if strATNE1Tech = "Vertical" then %>selected <% end if %>>Vertical</option>
	<option <% if strATNE1Tech = "Horizontal" then %>selected <% end if %>>Horizontal</option>
	</select></font><br></tD>
		</tr>
	<tr><td>
	<font face="Arial" size="2">&nbsp;&nbsp;&nbsp; </font>
		&nbsp;<font face="Arial" size="2">Location 2&nbsp;&nbsp;</font><input type="text" name="ATNonEntryLocation2" size="42" value="<% = strATNonEntryLocation2 %>" maxlength="100">&nbsp;&nbsp;&nbsp;
	<font face="Arial" size="2">Test Technique:&nbsp; </font><font face="Arial">
	<select size="1" name="ATNE2Tech">
<option value = "" >Select Value</option>
	<option <% if strATNE2Tech = "Vertical" then %>selected <% end if %>>Vertical</option>
	<option <% if strATNE2Tech = "Horizontal" then %>selected <% end if %>>Horizontal</option>
	</select></font><br></td>
		</tr>
	
	<tr><td>
	<font face="Arial" size="2">&nbsp;&nbsp;&nbsp; </font>
		&nbsp;<font face="Arial" size="2">Location 3&nbsp;&nbsp;</font><input type="text" name="ATNonEntryLocation3" size="42" value="<% = strATNonEntryLocation3 %>" maxlength="100">&nbsp;&nbsp;&nbsp;
	<font face="Arial" size="2">Test Technique:&nbsp; </font><font face="Arial">
	<select size="1" name="ATNE3Tech">
<option value = "" >Select Value</option>
	<option <% if strATNE3Tech = "Vertical" then %>selected <% end if %>>Vertical</option>
	<option <% if strATNE3Tech = "Horizontal" then %>selected <% end if %>>Horizontal</option>
	</select></font><br></td>
		</tr>
		<tr><td>
	<font face="Arial" size="2">&nbsp;&nbsp;&nbsp; </font>
		&nbsp;<font face="Arial" size="2">Location 4&nbsp;&nbsp;</font><input type="text" name="ATNonEntryLocation4" size="42" value="<% = strATNonEntryLocation4 %>" maxlength="100">&nbsp;&nbsp;&nbsp;
	<font face="Arial" size="2">Test Technique:&nbsp; </font><font face="Arial">
	<select size="1" name="ATNE4Tech">
	<option value = "" >Select Value</option>
	<option <% if strATNE4Tech = "Vertical" then %>selected <% end if %>>Vertical</option>
	<option <% if strATNE4Tech = "Horizontal" then %>selected <% end if %>>Horizontal</option>
	</select><br></font>
	<INPUT TYPE="submit" value="Submit" style="float: right"><br></td>
		</tr>
</table>
</body>

</html>
</form>

<%   Function SaveData()




if isnull(Request.form("CompartName")) then
strCompartName = ""
else
strCompartName = Replace(Request.form("CompartName"), "'", "''") 
end if





if isnull(Request.form("ReducedList1")) then
strReducedList1 = ""
else
strReducedList1 = Replace(Request.form("ReducedList1"), "'", "''") 
end if


if isnull(Request.form("ReducedList2")) then
strReducedList2 = ""
else
strReducedList2 = Replace(Request.form("ReducedList2"), "'", "''") 
end if


if isnull(Request.form("RescueControl")) then
strRescueControl = ""
else
strRescueControl = Replace(Request.form("RescueControl"), "'", "''") 
end if


if isnull(Request.form("DropHazControl")) then
strDropHazControl = ""
else
strDropHazControl = Replace(Request.form("DropHazControl"), "'", "''") 
end if


if isnull(Request.form("Immediate_danger")) then
strImmediate_danger = ""
else
strImmediate_danger = Replace(Request.form("Immediate_danger"), "'", "''") 
end if


if isnull(Request.form("Vent_SPReq")) then
strVentSPReq = ""
else
strVentSPReq = Replace(Request.form("Vent_SPReq"), "'", "''") 
end if

if isnull(Request.form("ATNonEntryLocation1")) then
strATNonEntryLocation1 = ""
else
strATNonEntryLocation1 = Replace(Request.form("ATNonEntryLocation1"), "'", "''") 
end if


if isnull(Request.form("ATNonEntryLocation2")) then
strATNonEntryLocation2 = ""
else
strATNonEntryLocation2 = Replace(Request.form("ATNonEntryLocation2"), "'", "''") 
end if

if isnull(Request.form("ATNonEntryLocation3")) then
strATNonEntryLocation3 = ""
else
strATNonEntryLocation3 = Replace(Request.form("ATNonEntryLocation3"), "'", "''") 
end if

if isnull(Request.form("ATNonEntryLocation4")) then
strATNonEntryLocation4 = ""
else
strATNonEntryLocation4 = Replace(Request.form("ATNonEntryLocation4"), "'", "''") 
end if


If Request.form("NaturalAir") = "ON" Then
strNaturalAir = -1
else
strNaturalAir = 0
end if


If Request.form("MechanicalAir") = "ON" Then
strMechanicalAir = -1
else
strMechanicalAir = 0
end if


if isnull(request.form("volume")) or len(request.form("volume")) < 2 then
strVolume = 0
else
strVolume = Request.form("volume") 
end if

strFtoM = Request.form("FtoM")




If Request.form("InternalConfig") = "Yes" then
strInternalConfig = -1
else
strInternalConfig = 0
end if

If Request.form("AnyNonEntryAirTest") = "Yes" then
strAnyNonEntryAirTest = -1
else
strAnyNonEntryAirTest = 0
end if



If Request.form("ConfigReduced") = "Yes" then
strConfigReduced = -1
else
strConfigReduced= 0
end if




If Request.form("OtherHazImpairRescue") = "ON" Then
strOtherHazImpairRescue = -1
else
strOtherHazImpairRescue = 0
end if

If Request.form("OtherHazIDLH") = "ON" Then
strOtherHazIDLH = -1
else
strOtherHazIDLH = 0
end if

If Request.form("OtherHazDrop") = "ON" Then
strOtherHazDrop = -1
else
strOtherHazDrop = 0
end if



if strvolume > 0 and strvolume < 3001 and strFtoM = "cubic feet" then
strAirExchange = 20
elseif strvolume > 3000 and strFtoM = "cubic feet" then
strAirExchange = 10
elseif strvolume > 0 and strvolume < 86 and strFtoM = "cubic meter" then
strAirExchange = 20
elseif strvolume > 85 and strFtoM = "cubic meter" then
strAirExchange = 10
else
strAirExchange = ""
end if

strATNE1Tech = Request.form("ATNE1Tech")
strATNE2Tech = Request.form("ATNE2Tech")
strATNE3Tech = Request.form("ATNE3Tech")
strATNE4Tech = Request.form("ATNE4Tech")



strsql = "Update tblCompartment set CompartName = '" & strCompartName & "', NaturalAir = " & strNaturalAir & ",  Volume = " & strVolume & ", FtoM = '" & strFtoM & "', "_
&"  MechanicalAir = " & strMechanicalAir & ",  "_
&" InternalConfig = " & strInternalConfig & ", Config_Reduced = " & strConfigReduced & ", "_
&" ReducedList1 = '" & strReducedList1 & "', ReducedList2 = '" & strReducedlist2 & "',  "_
&" OtherHazImpairRescue = " & strOtherHazImpairRescue & ", "_
&" RescueControl = '" & strRescueControl & "', OtherHazIDLH = " & strOtherHazIDLH & ", OtherHazDrop = " & strOtherHazDrop & ",  "_
&" DropHazControl = '" & strDropHazControl & "', Immediate_danger = '" & strImmediate_danger & "',  "_
&" AnyNonEntryAirTest = " & strAnyNonEntryAirTest & ", ATNonEntryLocation1 = '" & strATNonEntryLocation1 & "', airEexchanges = '" & strAirExchange & "',  "_
&" ATNonEntryLocation2 = '" & strATNonEntryLocation2 & "', ATNonEntryLocation3 = '" & strATNonEntryLocation3 & "', ATNonEntryLocation4 = '" & strATNonEntryLocation4 & "', "_
&" ATNE1Tech = '" & strATNE1Tech & "', ATNE2Tech = '" & strATNE2Tech & "', ATNE3Tech = '" & strATNE3Tech & "', ATNE4Tech =  '" & strATNE4Tech & "', "_
&" Vent_SPReq = '" & strVentSPReq & "' where ID = " & strcid & ""
	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close 
			
					if strHAStatus = "Approved"   or  strHAStatus = "APPROVED" then
					
			strsql = "Update tblHA set HA_Status = 'Change Proposal', HA_Approver_1 = Null, HA_Approver_2 = Null where Spaceid = '" & strid & "'"
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			Myconn.close
			end if
			
				If strHAStatus = "Approved" or strHAStatus = "APPROVED" or strHAStatus = "CHANGE PROPOSAL" or strHAStatus = "Change Proposal" then
			
			Response.redirect("Change_log_Compartment.asp?id=" & strid & "&cn=" & strCompartName & "&cid=" & strcid)
		
			end if
End Function			
			
 %><!--#include file="footer.inc"-->