
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Yard Check</TITLE>


<!--#include file="classes/asp_cls_SessionStringOYM.asp"-->
 
<!--#include file="classes/asp_cls_DataAccessOYM.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

    <% Dim MyRec, strsql
   strDate = NOW()
     Response.ContentType = "application/vnd.ms-excel"	

 %>

<style type="text/css">
.style1 {
	border: 1px solid #E6E6E6;
}
.style2 {
	font-weight: bold;
	border: 1px solid #E6E6E6;
	background-color: #FFFFD7;
}

.style3 {
	text-align: center;
}

.auto-style1 {
	font-weight: bold;
	border: 1px solid #E6E6E6;
	background-color: #F2FBFF;
}

</style>


<body bgcolor = "#FCFBF8"> 
 
 <table align = left class="style1" cellspacing="1">
  

<tr>		<td align="left" style="height: 17" class="auto-style1"><font size="2" face="Arial">Carrier</font></td>
			<td align="left" style="height: 17" class="auto-style1"><font size="2" face="Arial">Trailer</font></td></tr>
			
  


<%  
   
	strsql = "SELECT Master_Table.Date_Time_In, Master_Table.Carrier, Master_Table.TRAILER, "_
	&" Master_Table.Inbound_Load_Status, Master_Table.Status, Master_Table.Date_Time_Out,"_
	&"  Master_Table.Attention_Flag, Master_Table.TIME_TO_DOCK, Time_to_Empty "_
	&" FROM Master_Table "_
	&" WHERE Master_Table.Status=9 AND Master_Table.Date_Time_Out Is Null "_
	&" ORDER BY  Carrier, Date_Time_In"

		
		Set MyRec = Server.CreateObject("ADODB.Recordset") 
   		MyRec.Open strSQL, Session("ConnectionString")

i = 0
s = 0
c = ""
do until  MyRec.eof%>
<tr>      

	<% if MyRec.fields("Carrier") = c then %>		
		     <td class="style1"><font face = Arial size = 2>&nbsp;</td> 
    
<% else %>
     <td class="style1"><font face = Arial size = 2><%= MyRec.fields("Carrier").value %>&nbsp;</td>
     <% end if %>

        <td class="style1"><font face = Arial size = 2><%= MyRec.fields("Trailer").value %>&nbsp;</td>

     </tr>
    <%  
       i = i + 1
       s = s + 1
       c  = MyRec.fields("Carrier")
 	 MyRec.MoveNext
	
loop
MyRec.close

      
   
	strsql = "SELECT Master_Table.Date_Time_In, Master_Table.Carrier, Master_Table.TRAILER, "_
	&" Master_Table.Inbound_Load_Status, Master_Table.Status, Master_Table.Date_Time_Out,"_
	&"  Master_Table.Attention_Flag, Master_Table.TIME_TO_DOCK, Time_to_Empty "_
	&" FROM Master_Table "_
	&" WHERE Master_Table.Status=8 AND Master_Table.Date_Time_Out Is Null "_
	&" ORDER BY  Carrier, Date_Time_In"

		
		Set MyRec = Server.CreateObject("ADODB.Recordset") 
   		MyRec.Open strSQL, Session("ConnectionString")

i = 0
s = 0
c = ""
do until  MyRec.eof%>
<tr>  
	<% if MyRec.fields("Carrier") = c then %>		
		     <td class="style1"><font face = Arial size = 2>&nbsp;</td> 
    
<% else %>
     <td class="style1"><font face = Arial size = 2><%= MyRec.fields("Carrier").value %>&nbsp;</td>
     <% end if %>

        <td class="style1"><font face = Arial size = 2><%= MyRec.fields("Trailer").value %>&nbsp;</td>
 

     </tr>
    <%  
       i = i + 1
       s = s + 1
       c  = MyRec.fields("Carrier")
 	 MyRec.MoveNext
	
loop
MyRec.close

      
    %>


<%  dim strnow
strnow = formatdatetime(Now(),0)
   
	strsql = "SELECT Master_Table.Dock,  Master_Table.Carrier,"_
	&"  Master_Table.TRAILER,  "_
	&" Master_Table.Attention_Flag, Master_Table.Status, "_
	&" case when [status]=1 then Dock else CVA_Dock end as sort_Date "_
	&"  FROM Master_Table "_
	&" WHERE Master_Table.Status =1 Or Master_Table.Status=4 "_
	&" ORDER BY sort_date"

		
		Set MyRec = Server.CreateObject("ADODB.Recordset") 
   		MyRec.Open strSQL, Session("ConnectionString")
Dim i
i = 0
do until  MyRec.eof

 %>

<tr>		
     <td class="style1"><font face = Arial size = 2><%= MyRec.fields("Carrier").value %>&nbsp;-&nbsp;<%= MyRec("Sort_Date") %></td>

        <td class="style1"><font face = Arial size = 2><%= MyRec.fields("Trailer").value %>&nbsp;</td>
     </tr>
    <%  
       i = i + 1
 	 MyRec.MoveNext
	
loop
MyRec.close

      
    %>

<%  
strnow = formatdatetime(Now(),0)
   
	strsql = "SELECT Master_Table.Carrier, Master_Table.TRAILER, Master_Table.Inbound_Load_Status,"_
	&"  Master_Table.Inbound_BOL, Appt_Date_Time, Date_Time_In, Master_Table.Status, "_
	&" Master_Table.Date_Time_Out, Master_Table.Attention_Flag, Master_Table.RF_LD_TYPE,"_
	&"  Master_Table.VITALS_TYPE, Master_Table.Chem_LD_Type, Master_Table.Time_Back_to_IB, "_
	
	&" case when Appt_Date_Time < Date_Time_In then Appt_Date_Time else Date_Time_In end  as sort_date "_

	&"  FROM Master_Table "_
	&" WHERE Master_Table.Inbound_Load_Status<>'EMPTY' AND Master_Table.Status=0 "_
 	&" AND Master_Table.Date_Time_Out Is Null AND Master_Table.Date_Time_In Is Not Null "_
 	&" order by Inbound_Load_Status, sort_date desc "

		
		Set MyRec = Server.CreateObject("ADODB.Recordset") 
   		MyRec.Open strSQL, Session("ConnectionString")

i = 0
do until  MyRec.eof

 %>

<tr> 
    
    <td class="style1"><font face = Arial size = 2><%= MyRec.fields("Carrier").value %>&nbsp;</td>
        <td class="style1"><font face = Arial size = 2><%= MyRec.fields("Trailer").value %>&nbsp;</td>

     </tr>
    <%  
       i = i + 1
 	 MyRec.MoveNext
	
loop
MyRec.close

      
strnow = formatdatetime(Now(),0)
   
	strsql = "SELECT Master_Table.Carrier, Master_Table.TRAILER, Master_Table.status,"_
	&" Master_Table.Outbound_BOL, Master_Table.Outbound_Product, "_
	&" datediff(d,Time_BOL_Ready, '" & strnow & "') AS DY,  "_
	&" Master_Table.Attention_Flag, Master_Table.Destination 	FROM Master_Table "_
	&" WHERE Master_Table.Date_Time_Out Is Null "_
	&"	AND (Master_Table.Status =3 Or Master_Table.Status=5) "_
	&"	ORDER BY Master_Table.TRAILER"

		
		Set MyRec = Server.CreateObject("ADODB.Recordset") 
   		MyRec.Open strSQL, Session("ConnectionString")

i = 0
do until  MyRec.eof

 %>

<tr> 
    
    <td class="style1"><font face = Arial size = 2><%= MyRec.fields("Carrier").value %>&nbsp;</td>
        <td class="style1"><font face = Arial size = 2><%= MyRec.fields("Trailer").value %>&nbsp;</td>
 


     </tr>
    <%  
       i = i + 1
 	 MyRec.MoveNext
	
loop
MyRec.close

 
strnow = formatdatetime(Now(),0)
   
	strsql = "SELECT Master_Table.Date_Time_In, Master_Table.Carrier, Master_Table.TRAILER, "_
	&" Master_Table.Inbound_Load_Status, Master_Table.Status, Master_Table.Date_Time_Out,"_
	&"  Master_Table.Attention_Flag,  Master_Table.TIME_TO_DOCK, Time_to_empty  "_
	&" FROM Master_Table "_
	&" WHERE ((((Master_Table.Date_Time_In) Is Not Null) AND ((Master_Table.Inbound_Load_Status)='Empty') "_
	&"	AND ((Master_Table.Status)=0) AND ((Master_Table.Date_Time_Out) Is Null) AND ((Master_Table.TIME_TO_DOCK) Is Null)"_
	&"	AND ((Master_Table.Time_BOL_Ready) Is Null)) OR (((Master_Table.Date_Time_In) Is Not Null) "_
	&"	AND ((Master_Table.Status)=2) AND ((Master_Table.Date_Time_Out) Is Null) "_
	&"	AND ((Master_Table.Time_BOL_Ready) Is Null)) OR (((Master_Table.Date_Time_In) Is Not Null) "_
	&"	AND ((Master_Table.Inbound_Load_Status)='EMPTY') AND ((Master_Table.Status)=0)"_
	&"  AND ((Master_Table.Time_BOL_Ready) Is Null)))"_
	&" OR   (Master_Table.Status=7  AND Master_Table.Date_Time_Out Is Null) "_

	&" ORDER BY  Carrier, Date_Time_In"

		
		Set MyRec = Server.CreateObject("ADODB.Recordset") 
   		MyRec.Open strSQL, Session("ConnectionString")
 
do until  MyRec.eof%>
<tr>  
 
     <td class="style1"><font face = Arial size = 2><%= MyRec.fields("Carrier").value %>&nbsp;</td>
 

        <td class="style1"><font face = Arial size = 2><%= MyRec.fields("Trailer").value %>&nbsp;</td>
 
     </tr>
    <%  
     
 	 MyRec.MoveNext
	
loop
MyRec.close

      
    %>

 </table>


 