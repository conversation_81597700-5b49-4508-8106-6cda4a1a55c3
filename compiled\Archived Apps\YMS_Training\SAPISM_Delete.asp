﻿<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->


<%

dim strID
strID = Request.querystring("id")

    set objGeneral = new ASP_CLS_General
  %>


   <form action="SAPISM_Delete.asp?id=<%= strid%>" method="post" >

                <table width = 100%> 
<tr><td></td><td colspan=2 align = right><font face="Arial"><a href="SAPISM_Species.asp"><b>Return</b></a></font></td></tr>
<% Dim Myrec, strsql1, strFiber_species


strsql1 = "SELECT tblBrokeSAP.* FROM tblBrokeSAP WHERE ID = " & strid & ""

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL1, Session("ConnectionString")

strFiber_species = MyRec.fields("Type") 
MyRec.close
%>
     <tr>
                        <td><Font size = 2 face = Arial> Are you sure you want 
						to delete this item: <b><%= strFiber_species %></b>? <br><br> If so, click the button below.
                           
            </td>
            
        </tr>
        
    </table>

<p>

<Input name="Delete" type="submit" Value="Delete Record" >
</form>


  <% if objGeneral.IsSubmit() Then 


   
    strid = request.querystring("id")

   
    
	Dim strsql, Myconn
	strSQL = "DELETE FROM tblBrokeSAP where ID = " &  strID & ""

			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close



Response.redirect ("SAPISM_Species.asp") 

  end if
%><!--#include file="Fiberfooter.inc"-->