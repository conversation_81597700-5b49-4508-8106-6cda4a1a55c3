﻿<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->


<%

dim strID
Dim Myrec, strsql1, strDate, strTrailer, strTransTrailer, strWeight, strRelease, strComments, strOrgTrailer
strID = Request.querystring("id")

    set objGeneral = new ASP_CLS_General
  %>


   <form action="ReceiptDelete.asp?id=<%= strid%>" method=post name="ReceiptDelete">

                <table width = 100%> 
<tr><td></td><td colspan=2 align = right><font face="Arial"><a href="javascript:history.go(-1);">
	<strong>RETURN</strong></b></a></font></td></tr>
<% 


strsql1 = "SELECT tblCars.* FROM tblCars WHERE CID = " & strid & ""

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL1, Session("ConnectionString")

strTrailer = MyRec.fields("Trailer")
strTransTrailer = MyRec.fields("Transfer_Trailer_Nbr") %>

     <tr>
                        <td><Font size = 2 face = Arial> Are you sure you want to delete the Receipt for Release number <b><%= Myrec.fields("Release_Nbr")%></b>? <br><br> If so, click the button below.
                           
            </td>
            
        </tr>
        
    </table>
<% Myrec.close %>
<p>

<Input name="Update" type="submit" Value="Delete Receipt" >
</form>


  <% if objGeneral.IsSubmit() Then 


Dim strsQL3

    strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL3, Session("ConnectionString")


If not Myrec.eof then
   
    strid = request.querystring("id")
    strDate = formatdatetime(Now(),0)
    strRelease = ""
    strTrailer = ""
    strNet = 0
    
   MyRec.close 
strsql1 = "SELECT tblCars.* FROM tblCars WHERE CID = " & strid & ""

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL1, Session("ConnectionString")

	strTrailer = MyRec.fields("Trailer")
	strTransTrailer = MyRec.fields("Transfer_Trailer_Nbr") 
	strRelease = MyRec.fields("Release_nbr")
	strWeight = MyRec.fields("Net")
	strCarrier = MyRec.fields("Carrier")
	strOrgTrailer = MyRec.fields("Trailer")
	

    If strTrailer = "UNKNOWN" then
    strTrailer = strTransTrailer
    
    end if 
    MyRec.close
    
    strcomments = "Trailer: " & strTrailer & " Net: " & strWeight & " Rel: " & strRelease
    
           strsql = "INSERT INTO tblMovement ( CID, DDate, From_location,  Comment, BID ) "_
		&" SELECT " & strid & ", '" & strDate & "', 'Deleted', '" & strComments & "', '" & Session("EmployeeID") & "'"
        
           set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql
    
    
    
    
	Dim strsql, Myconn
	strSQL = "DELETE FROM tblCars where tblCars.CID = " &  strID & ""

			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close


If (strWeight < 17.5 and strCarrier <> "RAIL" and strOrgTrailer <> "UNKNOWN") or (strWeight > 25.0 and strCarrier <> "RAIL" and strOrgTrailer <> "UNKNOWN") then
            strEmailTo = "<EMAIL>"
            strEmailCC = "<EMAIL>"
        	 strEBCC = "<EMAIL>"
        
                 
			Set objMail = Server.CreateObject("CDO.Message")		
			objMail.From = "<EMAIL>"
			objMail.To = strEmailTo
			objMail.CC = strEmailCC
			objMail.BCC = strEBCC
	
			objMail.Subject = "Please Ignore Re-Weigh Truck Request for Receipt #:" & strID
			strBody2 = "<table border=0  cellpadding=0 cellspacing=0 width=800><tr><td align = center><font face = arial size = 3>&nbsp; Carrier&nbsp; </td><td align = left><font face = arial size = 3>Trailer&nbsp; </td><td align = left><font face = arial size = 3> Release # </td><td align = left><font face = arial size = 3> Weight </td><td align = left><font face = arial size = 3> Receipt # </td></tr>"
			 strbody2 = strbody2 & " <tr><td align = center><font face = arial size = 3> " & strCarrier &  "</td><td align = left><font face = arial size = 3>" & strTrailer & "</td><td align = left><font face = arial size = 3>"  & strRelease & "</td><td align = left><font face = arial size = 3>"  & strWeight & "</td><td align = left><font face = arial size = 3>"  & strID & "</td></tr> "
			objMail.HTMLBody = "<font face = arial size = 2>The following Receipt had been deleted:<br><br>" & strbody2



		'	objMail.Send
			Set objMail = nothing	
end if


Response.redirect ("Edit_Yard_trucks.asp") 
else
Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to delete a Receipt.</font></br>")
MyRec.close
end if




   End if 
%><!--#include file="Fiberfooter.inc"-->