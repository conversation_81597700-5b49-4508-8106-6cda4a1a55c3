﻿<%
Class ASP_CLS_ProcedureBadge

 
 Public Function BadgeSearch()
   Dim objDAC
   set objDAC = new ASP_CLS_DataAccessBadge
   set BadgeSearch = objDac.ExecuteSp("GetERTEmployees_List", Null)
    
   set objDAC = Nothing
 End Function
 
 Public Function BadgeSearchEntry()
   Dim objDAC
   set objDAC = new ASP_CLS_DataAccessBadge
   set BadgeSearchEntry = objDac.ExecuteSp("GetEms_Enter", Null)
    
   set objDAC = Nothing
 End Function


 Public Function BadgeSearchExit(strBID)
   Dim objDAC
   set objDAC = new ASP_CLS_DataAccessBadge
   set BadgeSearchExit = objDac.ExecuteSp("GetERTEmployees_Exit", array(strBID))
    
   set objDAC = Nothing
 End Function

 Public Function BadgeSearchEMS(strBID)
   Dim objDAC
   set objDAC = new ASP_CLS_DataAccessBadge
   set BadgeSearchEMS = objDac.ExecuteSp("EMS", array(strBID))
    
 
   set objDAC = Nothing
 End Function


End Class
%> 