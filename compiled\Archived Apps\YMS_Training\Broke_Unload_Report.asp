

<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">
<TITLE>Broke Unload Report</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

<%  
	Dim strsQL, rstEquip

   	Dim objEquipSearch, rstTotals, strCount, strDed, strNet, strTons, rstTotalReceived, strTR, objNew, rstSpecies
  	Dim  strVendor, strBegDate, strEndDate, strSpecies, strMonth, strDateReceived, strDateAdded, rstMonth, strDays
 	Dim  rstVendor, rstEquip3, strsql3, strFee, strFree, strLocation, strCarrier, rstEquipCount
  	Dim objGeneral, MyRec
   	
   	Dim intDirection

   intPageNumber = 1

   set objGeneral = new ASP_CLS_General

  if objGeneral.IsSubmit() Then
    Call GetFormData()
    Select Case intDirection
      Case 10: 'goto mocinfo.asp
  
      Case Else:
        Call LoadSearchResults()
    End Select
  else
    intDirection = 0
  end if
Call GetData()
	if len(strEndDate) > 3 then
	'ok
	else
	strEndDate = dateadd("d", 1, date())
	end if

%>

<script language="javascript">
 
  function GotoMOC(MOCID)
  {
    document.forms["form1"].elements["tbl_ID"].value = MOCID;
    document.forms["form1"].elements["Direction"].value = 10;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }

  function Search()
  {
    
    document.forms["form1"].elements["Direction"].value = 1;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }

</script>


<style type="text/css">
.style1 {
	font-family: Arial;
	font-size: x-small;
}
.style2 {
	text-align: left;
}
.style3 {
	font-family: Arial, Helvetica, sans-serif;
}
</style>
</head>


<div class="style2">


<form name="form1" action="Broke_Unload_report.asp" method="post">
 <input type="hidden" name="Direction" value="1" >
 <input type="hidden" name="tbl_ID" value="" >

 
 
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=80%  border=1 align = CENTER>  
  <tr><td  align = left><b><font face="Arial">Broke Trailer/Rail&nbsp; Unload Report</font></b></td>
	<td align = "RIght"><font face="Arial"><b><a href="Loginscreen.asp">HOME</a></b></font></td></tr></table>
	<div align="center">
<TABLE borderColor=#C0C0C0 cellSpacing=0 cellPadding=0 width=80% class="tablecolor1" border=1>  	
	
  <TR>
    <TD bgcolor="#F4F4FF" bordercolor="#F4F4FF"><font face="Arial" size="2"><b>Beginning 
	Unload Date:</b></font></TD>
   <TD bgcolor="#F4F4FF" bordercolor="#F4F4FF">
   <font face="Arial"><input name="BegDate" size="10" maxlength="10" value="<%=strBegDate%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633"></td>

    <TD bgcolor="#F4F4FF" bordercolor="#F4F4FF"><font face="Arial" size="2"><b>Ending 
	Unload Date:</b></font></TD>

    <TD bgcolor="#F4F4FF" bordercolor="#F4F4FF" colspan="2">
   <font face="Arial"><input name="EndDate" size="10" maxlength="10" value="<%=strEndDate%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633"></TD>
    <TD bgcolor="#F4F4FF" bordercolor="#F4F4FF" colspan="2">
   <font face="Arial">&nbsp;</td></tr>
   
  <TR > <TD bgcolor="#F4F4FF" bordercolor="#F4F4FF">&nbsp;</TD>
  <TD bgcolor="#F4F4FF" bordercolor="#F4F4FF"> &nbsp;</td><td bgcolor="#F4F4FF" bordercolor="#F4F4FF" class="style1">
	<strong>Location:</strong></td>
	<td bgcolor="#F4F4FF" bordercolor="#F4F4FF" class="style1" >
	<select  name="Location">
	     <option value="">--- All ---</option>
	     <% strsql = "Select distinct location from tblcars where date_unloaded > '7/1/2013' and species = 'BROKE' and location <> 'TRUCKS' order by location"
	        Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
while not MyRec.eof %>

	     
	     
	            <option <% if strLocation = MyRec("Location") then %> selected <% end if %>><%= MyRec("Location") %></option>
  <% MyRec.movenext
  wend
  MyRec.close %>
       
	</select></td>
	<td bgcolor="#F4F4FF" bordercolor="#F4F4FF" class="style1"> &nbsp;<strong>&nbsp;
	</strong>
	&nbsp;</td>
	
   
    <TD align="center" bgcolor = "#F4F4FF" bordercolor="#F4F4FF">
	<input type="button" onClick="javascript:Search()" value="Search" caption="Search" style="float: right"></TD></TR>
  </TABLE></div>
</form>


  <% if objGeneral.IsSubmit() Then 

%>
   <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=80% border=1 align = center>
    <tr><td colspan="6" bgcolor="white"><font face="Arial" size="2"><b>Search Results</b> </font></td>
    
    <%   
    strcount = 0
  %>

    <td  align="center">&nbsp;</td>

    </tr>
    <tr><td colspan="9" bgcolor="white" align="right">&nbsp;</td></tr>
      <tr class="tableheader">
      	<td  align="center" height="30"><font face="Arial" size = 1>Date<br>Unloaded</font></td>
      	<td  align="center" height="30"><font face="Arial" size = 1>Location</font></td>
      		<td  align="center" height="30"><font face="Arial" size = 1>Release</font></td>
      		<td  align="center" height="30"><font face="Arial" size = 1>SAP #</font></td>
      			<td  align="center" height="30"><font face="Arial" size = 1>Broke Description</font></td>

      	
      		<td  align="center" height="30"><font face="Arial" size = 1>Carrier</font></td>
      			<td  align="center" height="30"><font face="Arial" size = 1>Trailer</font></td>
      			
      		
      				<td  align="center" height="30"><font face="Arial" size = 1>Shuttle<br>Carrier</font></td>
      				<td  align="center" height="30"><font face="Arial" size = 1>Shuttle<br>Trailer</font></td>
      			
      					<td  align="center" height="30"><font face="Arial" size = 1>From Rail Car</font></td>
      				 	<td  align="center" height="30"><font face="Arial" size = 1>Species</font></td>
      				<td  align="center" height="30"><font face="Arial" size = 1>Weight (TONS)</font></td>
      				<td  align="center" height="30"><font face="Arial" size = 1>Weight (T)</font></td>
      			<td  align="center" height="30"><font face="Arial" size = 1>Date<br> Received</font></td>

	<td  align="center" height="30"><font face="Arial" size = 1>Days on Yard</font></td>

	<td  align="center" height="30"><font face="Arial" size = 1>Detention <br>To Date</font></td>


      
  	<% 
      Dim ii
       ii = 0
       strMT = 0
       strET = 0
       while not rstEquip.Eof
       strTrailer = ""
       strweight = ""
       strMetric = ""
  
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr class=tablecolor1>
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
  
    
<td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("Date_unloaded")%>&nbsp;</td>	
<td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("Location")%>&nbsp;</td>
<td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("Release_nbr")%></td>
<td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("SAP_NBR")%>&nbsp;</td>
<td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("Broke_Description")%>&nbsp;</td>
<td>   <font size="1" face="Arial"> <%= rstEquip.fields("Carrier")%>&nbsp;</font></td>

<% if rstEquip.fields("Trailer") = "UNKNOWN" then %>
<td  align="left"><font face = "arial" size = "1">&nbsp;</td>
<% else %>
<td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("Trailer")%>&nbsp;</td>
<% end if %>
<td>   <font size="1" face="Arial"> <%= rstEquip.fields("Trans_Carrier")%>&nbsp;</font></td>
<td> <font size="1" face="Arial"><%= rstEquip.fields("Transfer_trailer_nbr")%></font></td>
<td  align="left"><font face = "arial" size = "1">

<% if rstEquip("RC_CID") > 0 then
strRCID = rstEquip("RC_CID")
strsql = "Select Trailer from tblcars where CID = " & strRCID & ""
Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		strTrailer = MyREc("Trailer")
		MyRec.close %>
		<%= strTrailer %>
<% else %>
		&nbsp;
<% end if %>
&nbsp;</td>

<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Species")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1">
<% 

if len(rstEquip("Net")) > 0 then
strweight = rstEquip.fields("Net")
else
strweight = rstEquip("Tons_Received")
end if 

strSAP = rstEquip("SAP_NBR")
strsql = "Select UOM from tblBrokeSAP where SAP = '" & strSAP & "'"
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
if not MyRec.eof then
strUOM = MyREc("UOM")
if strUOM = "T" then
 If strweight > 1000 then
 strMetric = round(strweight / 2204.6,3)
 else
 strMetric = round((strweight * 2000)/2204.6,3)
 end if
 strMT = strMT + strMetric
elseif strUOM = "TONS" and strweight > 1000 then

strweight  = round(strweight / 2000,3)
strET = strET + strweight
else
strET = strET + strweight
end if
end if
%> <% if strUOM = "T" then %>
&nbsp;
<% else %>
<%= strweight %>
<% end if %>
&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%= strMetric %>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("date_received")%>&nbsp;</td>





<% 
		strdays =   round(datediff("d", rstEquip.fields("date_received"), rstEquip.fields("Date_unloaded")),0)
	  

if len(rstEquip.fields("Trans_Carrier")) > 1 then
  	
		 strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & rstEquip.fields("Trans_Carrier") & "' "
 else 		 
           strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & rstEquip.fields("Carrier") & "' " 
end if %>
<td  align="center"><font face = "arial" size = "1"><%= strdays  %>&nbsp;</td>


         <%           
 Set rstEquip3 = Server.CreateObject("ADODB.Recordset")
    rstEquip3.Open strSQL3, Session("ConnectionString")  
    If Not rstEquip3.eof then
    strFee = rstEquip3.fields("Fee")
    strFree = rstEquip3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    rstEquip3.close
    if strDays  > strFree then %>
    <td align = center><font face = "arial" size = "1"> $<%= (int(strDays) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font face = "arial" size = "1">$0&nbsp;</font></td>
	<% end if %>

	
  
   </tr>
    <% 
       ii = ii + 1
       strcount = strcount + 1
       rstEquip.MoveNext
     Wend
    %>
   </table>
   <p align="left" class="style3">Total Count:  <%= strcount %>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
   Total TONS: <%= strET %> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Total T: <%= strMT %></p>
</div>

<% end if %>


 <% Function GetData()

   set objNew = new ASP_Cls_Fiber
          
	set rstSpecies = objNew.FiberSpecies()
	strSpecies = request.form("Species")
		strLocation = request.form("Location")

End Function
    Function GetFormData()
      intDirection = cint(Request.Form("Direction"))

 	strBegDate = request.form("BegDate")
	strEndDate = request.form("EndDate")
		strSpecies = request.form("Species")
	strLocation = request.form("Location")
	strCarrier = request.form("Carrier")

      intPageNumber = cint(Request.Form("PageNumber"))

 
    End Function

    Function LoadSearchResults()
      Dim objEquipSearch, objTotals

    


	strBegDate= request.form("BegDate")
	strEndDate = request.form("EndDate")
	if len(strEndDate) > 3 then
	'ok
	else
	strEndDate = dateadd("d", 1, date())
	end if
	
	strLocation = request.form("Location")


      set objEquipSearch = new ASP_Cls_Fiber
      if strLocation = "" then
 
strsql = "Select tblCars.* from tblCars where Date_unloaded >= '" & strBegDate & "' and date_unloaded <= '" & strEndDate & "' "_
&" and Location <> 'TRUCKS' and Species = 'BROKE' order by Date_unloaded desc"
 
 else
 strsql = "Select tblCars.* from tblCars where Date_unloaded >= '" & strBegDate & "' and date_unloaded <= '" & strEndDate & "' "_
 &" and location = '" & strLocation & "' and Species = 'BROKE' order by date_unloaded desc"
 

 end if
    	 Set rstEquip = Server.CreateObject("ADODB.Recordset")
   	 rstEquip.Open strSQL, Session("ConnectionString")
 
    End Function
 %><!--#include file="Fiberfooter.inc"-->