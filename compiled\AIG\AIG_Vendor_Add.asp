
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Add AIG User</TITLE>
<style type="text/css">
.auto-style1 {
	font-family: Calibri;
	font-size: medium;
}
.auto-style2 {
	font-family: Calibri;
	font-weight: bold;
	font-size: medium;
}
.auto-style3 {
	font-family: Calibri;
	font-weight: bold;
	font-size: medium;
	border-color: #C0C0C0;
	border-width: 1px;
	background-color: #E7EBFE;
}
</style>
</head>

<!--#include file="classes/asp_cls_DataAccessAIG.asp"-->
<!--#include file="classes/asp_cls_headerAIG.asp"-->
<!--#include file="classes/asp_cls_SessionStringAIG.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->
<%

Dim strSQL, MyRec




 set objGeneral = new ASP_CLS_General

  

if objGeneral.IsSubmit() Then


	Call SaveData() 

End if
%>
<body>
<form name="form1" action="AIG_Vendor_add.asp" method="post">
 
<table border="0" cellpadding="0" cellspacing="0" width = 60% style="border-collapse: collapse" bordercolor="000000" height="25" align = center >
  <tr>
    <td  bgcolor="#FFFFFF" bordercolor="#CCCCFF" height="25" >
    <p align="center" class="auto-style1">Add AIG Vendor&nbsp;  	</td>
	  <td align = center height="25" class="auto-style2"><a href="AIG_Vendor_Address.asp">RETURN</a></td>

    <td align = center bgcolor="#FFFFFF" height="25">
  <Input name="Submit" type="submit" Value="Submit" style="float: right" class="auto-style1" ></td>
  </tr>
</table><br class="auto-style1"><br class="auto-style1">
<div align="center">
<table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#808080"  height="10" width="50%" id="table2">
    <tr>
    <td height="22" align="center" class="auto-style3" >SAP Vendor Name</td>
   
  </tr>
  
    <tr>
    <td  bordercolor="#CCCCFF" height="47" width = "25%" align="center" >  	<font face="Arial">	
	<input type="text" name="SAP_vendor" size="52" class="auto-style1"></font></td>
    	

  </tr>  </table>



<table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#808080"  height="10" width="75%">
    <tr>
    <td height="22" align="center" class="auto-style3" >YMS Vendor Name</td>
    <td height="22" align="center" class="auto-style3" >Vendor Number</td>
	  <td height="22" align="center" class="auto-style3" >Remit To Number</td>
   
  </tr>
  
    <tr>
    <td  bordercolor="#CCCCFF" height="47" align="center" >  	<font face="Arial">	
	<input type="text" name="Vendor_name" size="24" class="auto-style1"></font></td>
    <td bordercolor="#CCCCFF" height="47" w align="center" >	
		
	<font face="Arial">	
	<input type="text" name="Vendor_number" size="24" class="auto-style1"></font></td>
    	
 <td bordercolor="#CCCCFF" height="47"  align="center" >	
		
	<font face="Arial">	
	<input type="text" name="Payee_nbr" size="24" class="auto-style1"></font></td>
  </tr>
    </table>

<table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#808080"  height="10" width="75%" id="table1">
    <tr>
    <td height="22" align="center" class="auto-style3" >Vendor Street&nbsp; or PO Address</td>
   
  </tr>
  
    <tr>
    <td  bordercolor="#CCCCFF" height="47" width = "50%" align="center" >  	<font face="Arial">	
	<input type="text" name="Vendor_address" size="63" class="auto-style1"></font></td>
    	

  </tr>
  
 


  </table>

  

<div align="center">
<table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#808080"  height="10" width="75%" id="table3">
    <tr>
    <td height="22" align="center" class="auto-style3" >Vendor City, State, Zip</td>
   
  </tr>
  
    <tr>
    <td  bordercolor="#CCCCFF" height="47" width = "50%" align="center" >  	<font face="Arial">	
	<input type="text" name="Vendor_city" size="63" class="auto-style1"></font></td>
    	

  </tr>
  </table>


<table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#808080"  height="10" width="75%" id="table4">
    <tr>
    <td height="22" align="center" class="auto-style3" >Vendor Email Address</td>
   
  </tr>
  
    <tr>
    <td  bordercolor="#CCCCFF" height="47" width = "50%" align="center" >  	<font face="Arial">	
	<input type="text" name="Email_address" size="63" class="auto-style1"></font></td>
    	

  </tr>
  
       <tr>
    <td height="22" align="center" class="auto-style3" >
	<strong>Weekly Receipt Summary Email Address</strong></td>
   
  </tr>
  
    <tr>
    <td  bordercolor="#CCCCFF" height="47" width = "50%" align="center" >  	
	<textarea name="Summary" rows="2" style="width: 761px" class="auto-style6"><%= strSummary%></textarea></td>
    	

  </tr>



  </table>

</div>

</form>

 <%
  
  Function SaveData()
 Dim strEmail
 If isnull(REquest.form("Email_address")) then
 strEmail = "" 
 else
 strEmail = Request.form("Email_address")
 end if
 
 if isnull(Request.form("Summary")) then
strSummary = ""
else
strSummary = Replace(Request.form("Summary"), "'", "''") 
end if



strsql = "Select Vendor_nbr from tblVendors where Vendor_nbr = '" & request.form("Vendor_number") & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
If not MyRec.eof then
Response.write("<font face=arial size=3 color=red><b>This Vendor number is already being used</b></font>")
else


  
  strsql = "Insert into tblVendors (Vendor, Vendor_nbr, Vendor_name, Vendor_address, Vendor_street, Payee_Nbr, Email_address, Address) "_
  &" Values('" & Request.form("Vendor_name") & "', '" & Request.form("Vendor_number") & "','" & Request.form("SAP_vendor") & "', "_
  &" '" & Request.form("Vendor_address") & "', '" & Request.form("Vendor_city") & "', '" & Request.form("Payee_nbr") & "', '" & strEmail & "', '" & strSummary & "' )"



   
  	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
          Response.redirect("AIG_Vendor_Address.asp")
          
          end if
  End Function
  
   %><!--#include file="AIGfooter.inc"-->