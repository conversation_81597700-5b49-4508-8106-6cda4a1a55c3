﻿<html>

<head>
<style type="text/css">
.style1 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.style2 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	text-align: center;
}
</style>
</head>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<body>
<%

strtoday = Date()


strDay30 = FormatDateTime(DateAdd("d", -30, strtoday), 2)
strDay29 = FormatDateTime(DateAdd("d", -29, strtoday), 2)
strDay28 = FormatDateTime(DateAdd("d", -28, strtoday), 2)
strDay27 = FormatDateTime(DateAdd("d", -27, strtoday), 2)
strDay26 = FormatDateTime(DateAdd("d", -26, strtoday), 2)
strDay25 = FormatDateTime(DateAdd("d", -25, strtoday), 2)
strDay24 = FormatDateTime(DateAdd("d", -24, strtoday), 2)
strDay23 = FormatDateTime(DateAdd("d", -23, strtoday), 2)
strDay22 = FormatDateTime(DateAdd("d", -22, strtoday), 2)
strDay21 = FormatDateTime(DateAdd("d", -21, strtoday), 2)
strDay20 = FormatDateTime(DateAdd("d", -20, strtoday), 2)
strDay19 = FormatDateTime(DateAdd("d", -19, strtoday), 2)
strDay18 = FormatDateTime(DateAdd("d", -18, strtoday), 2)
strDay17 = FormatDateTime(DateAdd("d", -17, strtoday), 2)
strDay16 = FormatDateTime(DateAdd("d", -16, strtoday), 2)
strDay15 = FormatDateTime(DateAdd("d", -15, strtoday), 2)
strDay14 = FormatDateTime(DateAdd("d", -14, strtoday), 2)
strDay13 = FormatDateTime(DateAdd("d", -13, strtoday), 2)
strDay12 = FormatDateTime(DateAdd("d", -12, strtoday), 2)
strDay11 = FormatDateTime(DateAdd("d", -11, strtoday), 2)
strDay10 = FormatDateTime(DateAdd("d", -10, strtoday), 2)
strDay9 = FormatDateTime(DateAdd("d", -9, strtoday), 2)
strDay8 = FormatDateTime(DateAdd("d", -8, strtoday), 2)
strDay7 = FormatDateTime(DateAdd("d", -7, strtoday), 2)
strDay6 = FormatDateTime(DateAdd("d", -6, strtoday), 2)
strDay5 = FormatDateTime(DateAdd("d", -5, strtoday), 2)
strDay4 = FormatDateTime(DateAdd("d", -4, strtoday), 2)
strDay3 = FormatDateTime(DateAdd("d", -3, strtoday), 2)
strDay2 = FormatDateTime(DateAdd("d", -2, strtoday), 2)
strDay1 = FormatDateTime(DateAdd("d", -1, strtoday), 2)


strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay30 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay30t = MyRec.fields("Target")
		end if 
		
strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay29 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay29t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay28 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay28t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay27 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay27t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay26 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay26t = MyRec.fields("Target")
		end if
		 
strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay25 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay25t = MyRec.fields("Target")
		end if 
strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay24 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay24t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay23 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay23t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay22 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay22t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay21 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay21t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay20 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay20t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay19 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay19t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay18 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay18t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay17 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay17t = MyRec.fields("Target")
		end if 
strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay16 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay16t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay15 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay15t = MyRec.fields("Target")
		end if 
strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay14 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay14t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay13 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay13t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay12 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay12t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay11 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay11t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay10 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay10t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay9 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay9t = MyRec.fields("Target")
		end if 
		
strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay8 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay8t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay7 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay7t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay6 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay6t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay5 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay5t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay4 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay4t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay3 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay3t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay2 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay2t = MyRec.fields("Target")
		end if 

strsql = "SELECT Target from tblRailTarget where Inv_date = '" & strDay1 & "'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay1t = MyRec.fields("Target")
		end if 

strDay30From = strDay30 & " " &  "00:00:01 AM" 
strDay30To = strDay30 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay30From & "' AND Date_unloaded <= '" & strDay30To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay30c = MyRec.fields("CCID")
		end if 

strDay29From = strDay29 & " " &  "00:00:01 AM" 
strDay29To = strDay29 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay29From & "' AND Date_unloaded <= '" & strDay29To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay29c = MyRec.fields("CCID")
		end if 

strDay28From = strDay28 & " " &  "00:00:01 AM" 
strDay28To = strDay28 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay28From & "' AND Date_unloaded <= '" & strDay28To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay28c = MyRec.fields("CCID")
		end if 

strDay27From = strDay27 & " " &  "00:00:01 AM" 
strDay27To = strDay27 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay27From & "' AND Date_unloaded <= '" & strDay27To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay27c = MyRec.fields("CCID")
		end if 

strDay26From = strDay26 & " " &  "00:00:01 AM" 
strDay26To = strDay26 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay26From & "' AND Date_unloaded <= '" & strDay26To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay26c = MyRec.fields("CCID")
		end if 

strDay25From = strDay25 & " " &  "00:00:01 AM" 
strDay25To = strDay25 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay25From & "' AND Date_unloaded <= '" & strDay25To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay25c = MyRec.fields("CCID")
		end if 

strDay24From = strDay24 & " " &  "00:00:01 AM" 
strDay24To = strDay24 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay24From & "' AND Date_unloaded <= '" & strDay24To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay24c = MyRec.fields("CCID")
		end if 

strDay23From = strDay23 & " " &  "00:00:01 AM" 
strDay23To = strDay23 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay23From & "' AND Date_unloaded <= '" & strDay23To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay23c = MyRec.fields("CCID")
		end if 

strDay22From = strDay22 & " " &  "00:00:01 AM" 
strDay22To = strDay22 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay22From & "' AND Date_unloaded <= '" & strDay22To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay22c = MyRec.fields("CCID")
		end if 

strDay21From = strDay21 & " " &  "00:00:01 AM" 
strDay21To = strDay21 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay21From & "' AND Date_unloaded <= '" & strDay21To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay21c = MyRec.fields("CCID")
		end if 

strDay20From = strDay20 & " " &  "00:00:01 AM" 
strDay20To = strDay20 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay20From & "' AND Date_unloaded <= '" & strDay20To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay20c = MyRec.fields("CCID")
		end if 

strDay19From = strDay19 & " " &  "00:00:01 AM" 
strDay19To = strDay19 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay19From & "' AND Date_unloaded <= '" & strDay19To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay19c = MyRec.fields("CCID")
		end if 

strDay18From = strDay18 & " " &  "00:00:01 AM" 
strDay18To = strDay18 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay18From & "' AND Date_unloaded <= '" & strDay18To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay18c = MyRec.fields("CCID")
		end if 

strDay17From = strDay17 & " " &  "00:00:01 AM" 
strDay17To = strDay17 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay17From & "' AND Date_unloaded <= '" & strDay17To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay17c = MyRec.fields("CCID")
		end if 

strDay16From = strDay16 & " " &  "00:00:01 AM" 
strDay16To = strDay16 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay16From & "' AND Date_unloaded <= '" & strDay16To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay16c = MyRec.fields("CCID")
		end if 

strDay15From = strDay15 & " " &  "00:00:01 AM" 
strDay15To = strDay15 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay15From & "' AND Date_unloaded <= '" & strDay15To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay15c = MyRec.fields("CCID")
		end if 

strDay14From = strDay14 & " " &  "00:00:01 AM" 
strDay14To = strDay14 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay14From & "' AND Date_unloaded <= '" & strDay14To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay14c = MyRec.fields("CCID")
		end if 

strDay13From = strDay13 & " " &  "00:00:01 AM" 
strDay13To = strDay13 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay13From & "' AND Date_unloaded <= '" & strDay13To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay13c = MyRec.fields("CCID")
		end if 

strDay12From = strDay12 & " " &  "00:00:01 AM" 
strDay12To = strDay12 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay12From & "' AND Date_unloaded <= '" & strDay12To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay12c = MyRec.fields("CCID")
		end if 

strDay11From = strDay11 & " " &  "00:00:01 AM" 
strDay11To = strDay11 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay11From & "' AND Date_unloaded <= '" & strDay11To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay11c = MyRec.fields("CCID")
		end if 

strDay10From = strDay10 & " " &  "00:00:01 AM" 
strDay10To = strDay10 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay10From & "' AND Date_unloaded <= '" & strDay10To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay10c = MyRec.fields("CCID")
		end if 

strDay9From = strDay9 & " " &  "00:00:01 AM" 
strDay9To = strDay9 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay9From & "' AND Date_unloaded <= '" & strDay9To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay9c = MyRec.fields("CCID")
		end if 

strDay8From = strDay8 & " " &  "00:00:01 AM" 
strDay8To = strDay8 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay8From & "' AND Date_unloaded <= '" & strDay8To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay8c = MyRec.fields("CCID")
		end if 

strDay7From = strDay7 & " " &  "00:00:01 AM" 
strDay7To = strDay7 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay7From & "' AND Date_unloaded <= '" & strDay7To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay7c = MyRec.fields("CCID")
		end if 

strDay6From = strDay6 & " " &  "00:00:01 AM" 
strDay6To = strDay6 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay6From & "' AND Date_unloaded <= '" & strDay6To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay6c = MyRec.fields("CCID")
		end if 

strDay5From = strDay5 & " " &  "00:00:01 AM" 
strDay5To = strDay5 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay5From & "' AND Date_unloaded <= '" & strDay5To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay5c = MyRec.fields("CCID")
		end if 

strDay4From = strDay4 & " " &  "00:00:01 AM" 
strDay4To = strDay4 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay4From & "' AND Date_unloaded <= '" & strDay4To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay4c = MyRec.fields("CCID")
		end if 

strDay3From = strDay3 & " " &  "00:00:01 AM" 
strDay3To = strDay3 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay3From & "' AND Date_unloaded <= '" & strDay3To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay3c = MyRec.fields("CCID")
		end if 

strDay2From = strDay2 & " " &  "00:00:01 AM" 
strDay2To = strDay2 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay2From & "' AND Date_unloaded <= '" & strDay2To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay2c = MyRec.fields("CCID")
		end if 

strDay1From = strDay1 & " " &  "00:00:01 AM" 
strDay1To = strDay1 & " " & "11:59:59 PM" 
strsql = "SELECT COUNT(CID) AS CCID FROM tblCars where Date_unloaded >= '" & strDay1From & "' AND Date_unloaded <= '" & strDay1To & "' AND Location = 'RF' AND  (Species = 'PMX' or Species = 'KCOP' or Species = 'OF') AND Carrier = 'RAIL'"
		Set MyRec = Server.CreateObject("ADODB.RecordSet")
		MyRec.Open strSQL, Session("ConnectionString")
		If not MyRec.eof then
		strDay1c = MyRec.fields("CCID")
		end if 

strDay30g = strDay30c - strDay30t
strDay29g = strDay29c - strDay29t
strDay28g = strDay28c - strDay28t
strDay27g = strDay27c - strDay27t
strDay26g = strDay26c - strDay26t
strDay25g = strDay25c - strDay25t
strDay24g = strDay24c - strDay24t
strDay23g = strDay23c - strDay23t
strDay22g = strDay22c - strDay22t
strDay21g = strDay21c - strDay21t
strDay20g = strDay20c - strDay20t
strDay19g = strDay19c - strDay19t
strDay18g = strDay18c - strDay18t
strDay17g = strDay17c - strDay17t
strDay16g = strDay16c - strDay16t
strDay15g = strDay15c - strDay15t
strDay14g = strDay14c - strDay14t
strDay13g = strDay13c - strDay13t
strDay12g = strDay12c - strDay12t
strDay11g = strDay11c - strDay11t
strDay10g = strDay10c - strDay10t
strDay9g = strDay9c - strDay9t
strDay8g = strDay8c - strDay8t
strDay7g = strDay7c - strDay7t
strDay6g = strDay6c - strDay6t
strDay5g = strDay5c - strDay5t
strDay4g = strDay4c - strDay4t
strDay3g = strDay3c - strDay3t
strDay2g = strDay2c - strDay2t
strDay1g = strDay1c - strDay1t
strDay30rg = strDay30g
strDay29rg = strDay30rg + strDay29g
strDay28rg = strDay29rg + strDay28g
strDay27rg = strDay28rg + strDay27g
strDay26rg = strDay27rg + strDay26g
strDay25rg = strDay26rg + strDay25g
strDay24rg = strDay25rg + strDay24g
strDay23rg = strDay24rg + strDay23g
strDay22rg = strDay23rg + strDay22g
strDay21rg = strDay22rg + strDay21g
strDay20rg = strDay21rg + strDay20g
strDay19rg = strDay20rg + strDay19g
strDay18rg = strDay19rg + strDay18g
strDay17rg = strDay18rg + strDay17g
strDay16rg = strDay17rg + strDay16g
strDay15rg = strDay16rg + strDay15g
strDay14rg = strDay15rg + strDay14g
strDay13rg = strDay14rg + strDay13g
strDay12rg = strDay13rg + strDay12g
strDay11rg = strDay12rg + strDay11g
strDay10rg = strDay11rg + strDay10g
strDay9rg = strDay10rg + strDay9g
strDay8rg = strDay9rg + strDay8g
strDay7rg = strDay8rg + strDay7g
strDay6rg = strDay7rg + strDay6g
strDay5rg = strDay6rg + strDay5g
strDay4rg = strDay5rg + strDay4g
strDay3rg = strDay4rg + strDay3g
strDay2rg = strDay3rg + strDay2g
strDay1rg = strDay2rg + strDay1g

%>





<table border="1">
	<tr>
		<td style="padding:5px;" class="style1">Date</td>
		<td style="padding:5px;" class="style2">Cars Unloaded</td>
		<td style="padding:5px;" class="style2">Target</td>
		<td style="padding:5px;" class="style2">Gap (Target-cars unloaded)</td>
		<td style="padding:5px;" class="style2">Rolling 30 day GAP</td>
	</tr>

	<tr>
		<td class="style1"><%=strDay1 %></td><td class="style2"><%=strDay1c %></td>
		<td class="style2"><%=strDay1t %></td><td class="style2"><%=strDay1g %></td>
		<td class="style2"><%=strDay1rg %></td>
	</tr>

	<tr>
		<td class="style1"><%=strDay2 %></td><td class="style2"><%=strDay2c %></td>
		<td class="style2"><%=strDay2t %></td><td class="style2"><%=strDay2g %></td>
		<td class="style2"><%=strDay2rg %></td>
	</tr>

	<tr>
		<td class="style1"><%=strDay3 %></td><td class="style2"><%=strDay3c %></td>
		<td class="style2"><%=strDay3t %></td><td class="style2"><%=strDay3g %></td>
		<td class="style2"><%=strDay3rg %></td>
	</tr>

	<tr>
		<td class="style1"><%=strDay4 %></td><td class="style2"><%=strDay4c %></td>
		<td class="style2"><%=strDay4t %></td><td class="style2"><%=strDay4g %></td>
		<td class="style2"><%=strDay4rg %></td>
	</tr>

	<tr>
		<td class="style1"><%=strDay5 %></td><td class="style2"><%=strDay5c %></td>
		<td class="style2"><%=strDay5t %></td><td class="style2"><%=strDay5g %></td>
		<td class="style2"><%=strDay5rg %></td>
	</tr>

	<tr>
		<td class="style1"><%=strDay6 %></td><td class="style2"><%=strDay6c %></td>
		<td class="style2"><%=strDay6t %></td><td class="style2"><%=strDay6g %></td>
		<td class="style2"><%=strDay6rg %></td>
	</tr>

	<tr>
		<td class="style1"><%=strDay7 %></td><td class="style2"><%=strDay7c %></td>
		<td class="style2"><%=strDay7t %></td><td class="style2"><%=strDay7g %></td>
		<td class="style2"><%=strDay7rg %></td>
	</tr>

	<tr>
		<td class="style1"><%=strDay8 %></td><td class="style2"><%=strDay8c %></td>
		<td class="style2"><%=strDay8t %></td><td class="style2"><%=strDay8g %></td>
		<td class="style2"><%=strDay8rg %></td>
	</tr>

	<tr>
		<td class="style1"><%=strDay9 %></td><td class="style2"><%=strDay9c %></td>
		<td class="style2"><%=strDay9t %></td><td class="style2"><%=strDay9g %></td>
		<td class="style2"><%=strDay9rg %></td>
	</tr>

	<tr>
		<td class="style1"><%=strDay10 %></td><td class="style2"><%=strDay10c %></td>
		<td class="style2"><%=strDay10t %></td><td class="style2"><%=strDay10g %></td>
		<td class="style2"><%=strDay10rg %></td>
	</tr>

	<tr>
		<td class="style1"><%=strDay11 %></td><td class="style2"><%=strDay11c %></td>
		<td class="style2"><%=strDay11t %></td><td class="style2"><%=strDay11g %></td>
		<td class="style2"><%=strDay11rg %></td>
	</tr>

	<tr>
		<td class="style1"><%=strDay12 %></td><td class="style2"><%=strDay12c %></td>
		<td class="style2"><%=strDay12t %></td><td class="style2"><%=strDay12g %></td>
		<td class="style2"><%=strDay12rg %></td>
	</tr>

	<tr>
		<td class="style1"><%=strDay13 %></td><td class="style2"><%=strDay13c %></td>
		<td class="style2"><%=strDay13t %></td><td class="style2"><%=strDay13g %></td>
		<td class="style2"><%=strDay13rg %></td>
	</tr>

	<tr>
		<td class="style1"><%=strDay14 %></td><td class="style2"><%=strDay14c %></td>
		<td class="style2"><%=strDay14t %></td><td class="style2"><%=strDay14g %></td>
		<td class="style2"><%=strDay14rg %></td>
	</tr>

	<tr>
		<td class="style1"><%=strDay15 %></td><td class="style2"><%=strDay15c %></td>
		<td class="style2"><%=strDay15t %></td><td class="style2"><%=strDay15g %></td>
		<td class="style2"><%=strDay15rg %></td>
	</tr>

	<tr>
		<td class="style1"><%=strDay16 %></td><td class="style2"><%=strDay16c %></td>
		<td class="style2"><%=strDay16t %></td><td class="style2"><%=strDay16g %></td>
		<td class="style2"><%=strDay16rg %></td>
	</tr>

	<tr>
		<td class="style1"><%=strDay17 %></td><td class="style2"><%=strDay17c %></td>
		<td class="style2"><%=strDay17t %></td><td class="style2"><%=strDay17g %></td>
		<td class="style2"><%=strDay17rg %></td>
	</tr>

	<tr>
		<td class="style1"><%=strDay18 %></td><td class="style2"><%=strDay18c %></td>
		<td class="style2"><%=strDay18t %></td><td class="style2"><%=strDay18g %></td>
		<td class="style2"><%=strDay18rg %></td>
	</tr>

	<tr>
		<td class="style1"><%=strDay19 %></td><td class="style2"><%=strDay19c %></td>
		<td class="style2"><%=strDay19t %></td><td class="style2"><%=strDay19g %></td>
		<td class="style2"><%=strDay19rg %></td>
	</tr>

	<tr>
		<td class="style1"><%=strDay20 %></td><td class="style2"><%=strDay20c %></td>
		<td class="style2"><%=strDay20t %></td><td class="style2"><%=strDay20g %></td>
		<td class="style2"><%=strDay20rg %></td>
	</tr>

	<tr>
		<td class="style1"><%=strDay21 %></td><td class="style2"><%=strDay21c %></td>
		<td class="style2"><%=strDay21t %></td><td class="style2"><%=strDay21g %></td>
		<td class="style2"><%=strDay21rg %></td>
	</tr>

	<tr>
		<td class="style1"><%=strDay22 %></td><td class="style2"><%=strDay22c %></td>
		<td class="style2"><%=strDay22t %></td><td class="style2"><%=strDay22g %></td>
		<td class="style2"><%=strDay22rg %></td>
	</tr>

	<tr>
		<td class="style1"><%=strDay23 %></td><td class="style2"><%=strDay23c %></td>
		<td class="style2"><%=strDay23t %></td><td class="style2"><%=strDay23g %></td>
		<td class="style2"><%=strDay23rg %></td>
	</tr>

	<tr>
		<td class="style1"><%=strDay24 %></td><td class="style2"><%=strDay24c %></td>
		<td class="style2"><%=strDay24t %></td><td class="style2"><%=strDay24g %></td>
		<td class="style2"><%=strDay24rg %></td>
	</tr>

	<tr>
		<td class="style1"><%=strDay25 %></td><td class="style2"><%=strDay25c %></td>
		<td class="style2"><%=strDay25t %></td><td class="style2"><%=strDay25g %></td>
		<td class="style2"><%=strDay25rg %></td>
	</tr>

	<tr>
		<td class="style1"><%=strDay26 %></td><td class="style2"><%=strDay26c %></td>
		<td class="style2"><%=strDay26t %></td><td class="style2"><%=strDay26g %></td>
		<td class="style2"><%=strDay26rg %></td>
	</tr>

	<tr>
		<td class="style1"><%=strDay27 %></td><td class="style2"><%=strDay27c %></td>
		<td class="style2"><%=strDay27t %></td><td class="style2"><%=strDay27g %></td>
		<td class="style2"><%=strDay27rg %></td>
	</tr>

	<tr>
		<td class="style1"><%=strDay28 %></td><td class="style2"><%=strDay28c %></td>
		<td class="style2"><%=strDay28t %></td><td class="style2"><%=strDay28g %></td>
		<td class="style2"><%=strDay28rg %></td>
	</tr>

	<tr>
		<td class="style1"><%=strDay29 %></td><td class="style2"><%=strDay29c %></td>
		<td class="style2"><%=strDay29t %></td><td class="style2"><%=strDay29g %></td>
		<td class="style2"><%=strDay29rg %></td>
	</tr>

	<tr>
		<td class="style1"><%=strDay30 %></td><td class="style2"><%=strDay30c %></td>
		<td class="style2"><%=strDay30t %></td><td class="style2"><%=strDay30g %></td>
		<td class="style2"><%=strDay30rg %></td>
	</tr>

</table>

















</body>
</html>