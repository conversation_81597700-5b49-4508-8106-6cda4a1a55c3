																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Trailer Log</TITLE>


<!--#include file="classes/asp_cls_headerOYM.asp"-->
<!--#include file="classes/asp_cls_SessionStringOYM.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->


<% Dim MyRec, strsql, strstartdate
strid = request.querystring("id")


set objGeneral = new ASP_CLS_General


  if objGeneral.IsSubmit = True Then
  
  strLOTime = request.form("Log_out")
  
  strsql = "Select tblTrailerLog.* from tblTrailerLog where ID = " & strid
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    strTrailer = MyRec("Trailer")
    strCarrier = MyREc("Carrier")
 
    MyRec.close
     strProduct = Request.form("Product")
 

strsql = "Update tblTrailerLog set  Location = '" & request.form("WH") & "', Product='" & strProduct & "' where ID = " & strid  
Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 
			
		

			
			
	strsql = "Insert into tblTrailerLogHistory (Trailer, Date_time, Carrier, Entry_by, Entry_page, Location, Action_type, Product, TID) select '" & strTrailer & "', '" & strLoTime & "', '" & strCarrier & "', "_
		&" '" &  SEssion("EmployeeID")  & "', 'WH_to_WH', '" & request.form("WH") & "', 'Moved to new WH', '" & strProduct & "', " & strID & ""
 
			
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
		
			
			
			Response.redirect("OYM_Trailer_Log.asp")
			end if


strsql = "Select tblTrailerLog.* from tblTrailerLog where ID = " & strid
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<style type="text/css">
.style3 {
	border: 1px solid #000000;
}
.style4 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #FFFFE1;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.style5 {
	border: 1px solid #DDDDDD;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.style6 {
	text-align: center;
}
.style7 {
	border: 1px solid #DDDDDD;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	text-align: center;
}
.style8 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #FFFFE1;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	text-align: center;
}
.style27 {
	font-size: small;
}
</style>
</head>

<body>
<br>
 <form name="form1" action="WH_to_WH.asp?id=<%= strid %>"  method="post" >
 <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>


<td class="style6"><b><font face="Arial">Move Trailer to another Warehouse 
<br>
<br>
Detention will not stop<br>
<br>
<br>
</font></b></td>
<td align="right"><b><font face="Arial"><a href="javascript:history.go(-1);">RETURN
</a></font></b></td>
</tr>
	    </table>
	
	
	<TABLE cellSpacing=0 class = "style3" align = center style="width: 75%">  
	 <tr class="tableheader">

				<td  align = left class="style4"  >     Trailer</td>
				<td  align = left class="style4"  >     Carrier</td>
				<td  align = left class="style4"  >     Product</td>
				 
				<td  align = left class="style4"  >     Date/Time In</td>
				<td  align = left class="style4"  >     Location</td>			 
			 			 
			 

			 
				<td  align = left class="style4"  >     New Location</td>			 
			 			 
			 

			 
				<td class="style8"  >     &nbsp;Log out to WH Time&nbsp;</td>			 
			 			 
			 

			 
				<td class="style8"  >     &nbsp;</td>			 
			 			 
			 

			 
	</tr>

 
   <tr>

	<td class="style5"  ><%= MyRec.fields("Trailer")%></td>
	
	<td class="style5"  >&nbsp;<%= MyRec.fields("Carrier")%></td>
		<td class="style5"  > <input type="text" name="Product" value="<%= MyRec.fields("Product")%>"></td>
	 
	<td class="style5"  >&nbsp;<%= MyRec.fields("Date_Time_in")%></td>	
	<td class="style5"  >&nbsp;<%= MyRec.fields("Location")%></td>	
 
 

	<td class="style5"  >
	<font face="Arial">
	<select name="WH" style="font-weight: 300" size="1">
	<option selected value="*">All</option>
	<option <% if strWH = "ORA #4" then %> selected <% end if %>>ORA #4</option>
	<option <% if strWH = "B.F.A. /Ragu" then %> selected <% end if %>>B.F.A. /Ragu</option>
 	<option <% if strWH = "Westinghouse" then %> selected <% end if %>>Westinghouse</option>
     </select></font></td>	
 
 

	<td class="style7"  ><input type="text" name="Log_out" value="<%= now() %>"> </td>	
 
 

	<td class="style7"  ><font face="calibri" size="3"><span class="style27"><INPUT TYPE="submit" value="Submit"></td>	
 
 

	</tr>

 <% MyRec.close %>
</table>
</form>
<!--#include file="OYMfooter.inc"-->