<html>
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->

<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Add Action Item</title>
</head>
<% dim strsql, MyRec, strid, strBID, strE_Name, strP_Date

strBID = Session("EmployeeID")
strE_name = Session("Ename")

strP_date = formatdatetime(strnow,2)
	


strid = Request.querystring("id")
	set objGeneral = new ASP_CLS_General
  		 if objGeneral.IsSubmit = True Then
  		 strid = Request.querystring("id")
  		 strBID = Request.form("BID")
  		 strE_Name = ReturnEname(StrBID)
  		 strP_Date = Request.form("P_date")
  		 strE_Name2 = Replace(strE_Name, "'", "''")
  		 
   	 strsql =  "INSERT INTO tblActionLog(Haz_ID, Type, BID, E_name, P_date) SELECT " & strid & ", "_
   	 &" 'Hazard Participant', '" & strBID & "', '" & strE_name2 & "', '" & strP_Date & "'"
 			Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 
			
			
	
		
			
 		
			
			
			Response.redirect("HA_Action_log.asp?id=" & strid)
End if

 %>
 <body bgcolor="#E1E8FF">
 <form name="form2" action=HA_Action_new.asp?id=<%= strid%>  method="post" ID="Form2"  >
	<p align="center"><br><font face="Arial"><b>Add New Hazard Assessment 
	Participant</b></font><br><br>
 <INPUT TYPE="submit" value="Submit" style="float: right">
  </p>
  <table border="1" cellpadding="0" align = center cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="50%" id="table1">
  <tr>
    <td  height="19" bgcolor="#FFFFDD">
	<p align="center"><font face="Arial" size="2">Action Type</font></td>
    <td  height="19" bgcolor="#FFFFDD" align="center">
	<font face="arial" size="2">BID</font></td>
    <td  height="19" bgcolor="#FFFFDD" align="center">
	<font face="Arial" size="2">Date</font></td>
   
  </tr>
  <tr>
    <td align = center height="19" bgcolor="#FFFFFF" > <font face="Arial">Hazard 
	Assessment Participant</font></td>
    <td  height="19" bgcolor="#FFFFFF"  ><font face="Arial" size="2">
	<p align="center">
	<font face="Arial">
		<input type="text" name="BID" size="10" value="<%= strBID %>"></font></td>
	 <td  height="19" bgcolor="#FFFFFF">
		<p align="center"><font face="Arial" size="2">&nbsp;</font><font face="Arial"><input type="text" name="P_Date" size="12" value="<%= strP_date%>"></font></td>
  
  </tr>
</table>
</form>