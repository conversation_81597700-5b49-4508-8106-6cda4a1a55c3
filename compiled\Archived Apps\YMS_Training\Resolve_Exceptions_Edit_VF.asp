<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 14.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Resolve Exceptions VF</title>
<style type="text/css">
.style1 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.style2 {
	text-align: right;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID, objMOC, rstFiber, strCarrier, objGeneral
      
    Dim strTrailer
    Dim strReleaseNbr

    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strOther, strR, strsql3, strSpecies, MyRec2, strsql2, rstSpecies, strBales

  strId  = Trim(Request.QueryString("id")) 


 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
 
		Call SaveData() 
	

	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to edit a Receipt.</font></br>")
	MyRec.close
	end if

  
ELSE


strsql = "SELECT tblCars.* from tblCars WHERE tblCars.CID = " & strid

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


    strTrailer = MyRec.fields("Trailer")
    strReleaseNbr = MyRec.fields("Release_nbr")

    strGrossWeight = MyRec.fields("Gross_weight")
     strTareWeight = MyRec.fields("Tare_weight")
     strTonsReceived = MyRec.fields("Tons_Received")
    strDateReceived = MyRec.fields("Date_received")
 
    strOther = MyRec.fields("OTher_comments")
    strCarrier = MyRec.fields("Carrier")
   
    strBales = MyRec.fields("Bales_VF")
    
    strSpecies = MyRec.fields("Species")

MyRec.close
Call getdata()
	end if

%>



<body>
<table width = 100%><tr><td width = 33%></td><td align = center width = 34%><b><font face="Arial" size="4">
Edit Trailer Receipt <%= strID%></font> </b></td><td align = right width = 33%><a href="Resolve_Excpetions_VF.asp"><b><font face = arial size = 2>RETURN</a></font></b></td></tr></table>




<form name="form1" action="Resolve_Exceptions_Edit_VF.asp?id=<%=strid%>&r=<%= strR %>" method="post">
<div align="center">
<table border="1" cellspacing="0" bordercolor="#C0C0C0" width="60%" bgcolor="#FFFFD5" style="border-collapse: collapse" cellpadding="0">
<tr>
    <td bgcolor="#FFFFD5">&nbsp;</td>

    <td  bgcolor="#FFFFD5">&nbsp;</td>
  </tr>
  <tr>
    <td  align = right bgcolor="#FFFFD5" >
   <b>
   <font face="Arial" size="2">Trailer:&nbsp;</font></b></td>
<td  align = left bgcolor="#FFFFD5">

      <input type="text" name="Trailer" size="15" value = "<%= strTrailer%>" tabindex="1"></td></tr>

  </tr>
  <tr>

      <td  bgcolor="#FFFFD5" align = right>
  &nbsp;</td>
<td  align = left bgcolor="#FFFFD5">

      &nbsp;</td></tr>
<tr>
    <td  bgcolor="#FFFFD5">  
	<p align="right">  <font face="Arial" size="2"><b>Select Carrier:&nbsp;</b></font></td>
    <td bgcolor="#FFFFD5">   <select name="Carrier" tabindex="3">
 	<option value="" selected>  Select Carrier (Required)</option>
      <%= objGeneral.OptionListAsString(rstFiber, "Carrier", "CDesc", strCarrier) %>
     </select></td>
  </tr><tr><td  align="right" bgcolor="#FFFFD5">  <font face="Arial" size="2"><b>Species:&nbsp;</b></font&nbsp;</td>

    <td bgcolor="#FFFFD5">

     <select size="1" name="Species" tabindex="4">

     <option value="">---Select ---</option>
       <%= objGeneral.OptionListAsString(rstSpecies, "Fiber_species", "Fiber_species", strSpecies) %>
     </select> &nbsp;</td>
  </tr>
   
<tr>
    <td  bgcolor="#FFFFD5">&nbsp;</td>

    <td  bgcolor="#FFFFD5">&nbsp;</td>
  </tr>

  <tr>
      <td  bgcolor="#FFFFD5" colspan="2" class="style1"><strong>Enter Either 
		Tons Received or Gross and Tare </strong></td>

  </tr>

<% If left(strReleaseNbr,1) = "2" or left(strReleaseNbr,1) = "C" then %>
<% else %>
       <tr>
    <td align = right bgcolor="#FFFFD5" >
   <font face="Arial" size="2"><b>Gross Weight:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFD5">

      <input type="text" name="Gross_Weight" size="15" value = "<%= strGrossWeight%>" style="width: 90px" tabindex="5"></td></tr>
      <tr>
          <td  bgcolor="#FFFFD5" align=right>
   <font face="Arial" size="2"><b>Tare Weight:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFD5">

      <input type="text" name="Tare_Weight" size="15" value = "<%= strTareWeight%>" style="width: 88px" tabindex="6">

</td></tr>

<% end if %>

 
        <tr>
          <td  bgcolor="#FFFFD5" align = right>
   <font face="Arial" size="2"><b>Tons Received:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFD5">
<font face="Arial" size="2">
      <input type="text" name="Tons_Received" size="15" value = "<%= strTonsReceived%>" style="width: 88px" tabindex="7">&nbsp;

</td></tr>

 
<tr>
    <td  bgcolor="#FFFFD5" class="style2" style="height: 23px"><font face="Arial" size="2"><b>VF Bales:</b></font></td>

    <td  bgcolor="#FFFFD5" style="height: 23px">
<font face="Arial" size="2">
      <input type="text" name="Bales" size="15" value = "<%= strBales%>" style="width: 88px" tabindex="7"></td>
  </tr>
  
  <tr>
    <td  bgcolor="#FFFFD5" class="style2"><font face="Arial" size="2"><b>&nbsp;</b></font></td>

    <td  bgcolor="#FFFFD5">
<font face="Arial" size="2">
      &nbsp;</td>
  </tr>

       <tr>
          <td  align = right bgcolor="#FFFFD5" >
    <font face="Arial" size="2"><b>Date Received:&nbsp;</b></font></td>
<td align = left bgcolor="#FFFFD5">

      <input type="text" name="Date_Received" size="15" value = "<%= strDateReceived%>" tabindex="8"></td></tr>
         <tr>
          <td  align = right bgcolor="#FFFFD5" >
  <font face="Arial" size="2"><b>Other:&nbsp;</b></font></td >
   <td align = left bgcolor="#FFFFD5">   
	<input type="text" name="Other_Comments" size="25" value = "<%= strOther%>" tabindex="12"></td></tr>
<tr>
    <td  bgcolor="#FFFFD5">&nbsp;</td>

    <td bgcolor="#FFFFD5">&nbsp;</td>
  </tr>

  <tr>
    <td bgcolor="#FFFFD5" height="34">&nbsp;</td>

    <td align = left bgcolor="#FFFFD5" height="34"><Input name="Update" type="submit" Value="Submit" ></td>
  </tr>
</table>

</div>

</form>
</body>
<%
 Function GetData()
  set objMOC = new ASP_CLS_FIBER
  set rstFiber = objMOC.FiberCarrier()
    set rstSpecies = objMOC.VFSpecies()

End Function


Function SaveData()
strid = request.querystring("id")
strTrailer = Request.form("Trailer")
strCarrier = Replace(Request.form("Carrier"), "'", "''")
strOther = Replace(Request.form("Other_Comments"), "'", "''") 
strDateReceived = Request.form("Date_received")

strSpecies = Request.form("Species")
If len(Request.form("Tons_received")) > 0 then
strTonsReceived = Request.form("Tons_received")

         strSql = "Update tblCars Set Carrier = '" & strCarrier & "', Other_Comments = '" & strOther & "', Trailer = '" & strTrailer & "', "_
         &" Date_Received = '" & strDateReceived & "', Species = '" & strSpecies & "', "_    
         &" Tons_Received = " & strTonsReceived & ", Net = " & strTonsReceived & " where CID = " & strid & ""
else
If len(request.form("Gross_Weight")) > 0 then
strGrossWeight = Request.form("Gross_Weight")
else
strGrossWeight = 0
end if
if len(request.form("Tare_Weight")) > 0 then 
strTareWeight = Request.form("Tare_Weight")
else
strTareWeight = 0
end if
if strgrossweight = 0 or strTareweight = 0 then
strTonsReceived = 0
else
strTonsReceived = round((strgrossweight - strTareweight)/2000,3)
end if

         strSql = "Update tblCars Set Carrier = '" & strCarrier & "', Other_Comments = '" & strOther & "', Trailer = '" & strTrailer & "', "_
         &" Date_Received = '" & strDateReceived & "', Species = '" & strSpecies & "', "_
           
         &" Gross_Weight = " & strGrossWeight & ", "_
         &" Tare_Weight = " & strTareWeight & ", "_
         &" Tons_Received = " & strTonsReceived & ", Net = " & strTonsReceived & " where CID = " & strid & ""

end if
         
 	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
         
         If len(Request.form("Bales")) > 0 then
         strBales = Request.form("Bales")
            strSql = "Update tblCars Set Bales_VF = " & strBales & " where CID = " & strid & ""
 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 

end if
         
  
Response.redirect ("Resolve_Exceptions_VF.asp")



End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->