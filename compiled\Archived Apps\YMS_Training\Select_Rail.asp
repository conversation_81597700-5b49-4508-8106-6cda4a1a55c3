																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>In Transit Rail Cars </TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->


<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate

strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -7, strtdate)


  set objGeneral = new ASP_CLS_General
  
    if objGeneral.IsSubmit() Then	
	   For Each Item In Request.Form
           If Left(Item,4) = "ETA-" Then
		      If Request(Item) <> Request("hid" & Item) Then
		         strsql = "Update tblVirginFiber SET ETA = '" & Request(Item) & "' WHERE VID = " & Replace(Item,"ETA-","")
			     'Response.Write strsql & "<BR>"
			     Set MyConn99 = Server.CreateObject("ADODB.Connection")
			     MyConn99.Open Session("ConnectionString")
			     MyConn99.Execute strSQL
			     MyConn99.Close
			     Set MyConn99 = Nothing
		      End If
		   End If
	   Next
    strsql = "SELECT * from tblVirginFiber where status = 'Inbound'  and Trailer is not null and vendor = 'Corporate' order by Trailer"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
          while not MyRec.Eof

 
 
 strRelease = MyRec("Release")

strsql2 = "Select Release_nbr from tblCars where Release_nbr  = '" & strRelease & "'"
  Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL2, Session("ConnectionString")
    If not MyRec2.eof then 
    'skip record
    else
    strvid = MyREc("VID")

strE = Request.form("ETA-" & strVID)
     ' response.write("strE" & strE)
     
     strsql3 = "Update tblVirginFiber set ETA = '" & strE & "' where VID = " & strVID
     		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			'MyConn.Execute strSQL3
			MyConn.Close

     
     
     
end if
MyRec.movenext
wend
MyRec.close
    
    

    
    end if

strsql = "SELECT * from tblVirginFiber where status = 'Inbound'  and Trailer is not null and vendor = 'Corporate' order by Trailer"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<style type="text/css">
.style1 {
	font-family: Arial;
	font-weight: bold;
	text-align: right;
}
.style2 {
	text-align: right;
}
.style3 {
	font-size: xx-small;
	font-family: Arial, Helvetica, sans-serif;
}
.auto-style1 {
	font-size: x-small;
}
.auto-style2 {
	font-size: x-small;
	font-weight: bold;
}
.auto-style3 {
	font-size: x-small;
	font-family: Arial, Helvetica, sans-serif;
	font-weight: bold;
}
</style>
</head>

<body>
<br>
<form name="form1" action="Select_Rail.asp" method="post">	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>
<tr> <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<%  IF SEssion("EMployeeID") = "B53909" or Session("EmployeeID") = "C97338" or Session("EmployeeID") = "C28802"  or Session("EmployeeID") = "F00390" then %>
<td><Input name="Update" type="submit" Value="Submit" style="font-weight: 700" tabindex="8" ></td>
<% end if %>
<td align = center>
<p align="left"><b><font face="Arial">Select Recovered Fiber Rail Car Load to Receive </font></b></td>
<td class="style1"><a href="Select_Rail_Export.asp">EXPORT</a></td>

<td class="style1"><a href="Generic_Receipt_Rail.asp">Enter Rail Exception if Car is not on List</a></td>

</tr>
	    </table>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1 align = center>  
	 <tr class="tableheader">
<td style="height: 12px"></td>

				<td  align = left style="height: 12px" class="auto-style2">     
				<font face="Arial">	Trailer/Car</font></td>
		<td style="height: 12px" class="auto-style2"  ><font face="Arial">Date Shipped</font></td>
	
		<td  align = left style="height: 12px" class="auto-style2">     
		<font face="Arial">ETA</font></td>
		<td  align = left class="auto-style3" style="height: 12px">     Release</td>
	
		<td  align = left style="height: 12px" class="auto-style2">     
		<font face="Arial">Vendor</font></td>
			<td  align = left class="auto-style3" style="height: 12px"> City</td>
		<td style="height: 12px" class="auto-style2"  ><font face="Arial">Item Description</font></td>
			<td style="height: 12px" class="auto-style2"  ><font face="Arial">SAP</font></td>
				<td style="height: 12px" class="auto-style2"  >
				<font face="Arial">PO</font></td>
		<td style="height: 12px" class="auto-style2"  ><font face="Arial">Units</font></td>
		<td  align = left style="height: 12px" class="auto-style2">     
		<font face="Arial">	Tons</font></td>
		<td style="height: 12px" class="auto-style2"  ><font face="Arial">UOM</font></td>
			<td style="height: 12px" class="auto-style2"  ><font face="Arial">Comments</font></td>
		
	</tr>

 <%  Dim ii
       ii = 0
       while not MyRec.Eof

 
 
 strRelease = MyRec("Release")

strsql2 = "Select Release_nbr from tblCars where Release_nbr  = '" & strRelease & "'"
  Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL2, Session("ConnectionString")
    If not MyRec2.eof then 
    'skip record
    else
 
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td class="auto-style1"> <font face="Arial">



<a href="EnterRail.asp?id=<%= MyRec.fields("Release") %>">Receive</a></td>

	<td  ><font size="1" face="Arial"><span class="auto-style1"><%= MyRec.fields("Trailer")%>&nbsp;</span></font></td>
	
<td  ><font size="1" face="Arial"><span class="auto-style1"><%= MyRec.fields("Date_shipped")%>&nbsp;</span></font></td>
<% if datediff("d", MyRec.fields("ETA"), strTdate) > 0 then %>	
<td bgcolor="#FEDAD6" class="auto-style1"  >
<% else %>
<td>
<span class="auto-style1">
<% end if %></span><font size="1" face="Arial">
<span class="auto-style1">
<%  IF SEssion("EMployeeID") = "B53909" or Session("EmployeeID") = "C97338"  or Session("EmployeeID") = "C28802"  or Session("EmployeeID") = "F00390" then %>
</span>
<input type="text" name="ETA-<%= MyRec("VID") %>" value="<%= MyREc("ETA") %>" class="auto-style1" style="width: 77px">
<input type="hidden" name="hidETA-<%= MyRec("VID") %>" value="<%= MyREc("ETA") %>">

<span class="auto-style1">
<% else %>

<%= MyRec.fields("ETA")%>
<% end if %>&nbsp;</span></font></td>
<td  ><font size="1" face="Arial"><span class="auto-style1"><%= MyRec.fields("Release")%>&nbsp;</span></font></td>

	<td  ><font size="1" face="Arial"> 	<span class="auto-style1"> 
	
		
	<% strV = ""
	strG = ""
	strCity = ""
	if len(strRelease) > 3 then
	
	strsql = "SELECT Release, Vendor, Generator, City from tblOrder where   tblOrder.Release='" & strRelease & "'"
 Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
    if  not MyConn.eof then
    strV = MyCOnn("Vendor")
    strG = MyConn("Generator")
    strCity = MyConn("City")
    end if
    MyConn.close
    end if %>

	
	
	
	<%= strV %> - <%= strG %></span></font></td>
<td><font size="1" face="Arial"><span class="auto-style1"><%= strCity %>&nbsp;</span></td>
 
	
<td  ><font size="1" face="Arial"><span class="auto-style1"><%= MyRec.fields("Species")%>&nbsp;</span></font></td>
<td  ><font size="1" face="Arial"><span class="auto-style1"><%= MyRec.fields("SAP_NBR")%>&nbsp;</span></font></td>
<td  ><font size="1" face="Arial"><span class="auto-style1"><%= MyRec.fields("PO")%>&nbsp;</span></font></td>
	<td  ><font size="1" face="Arial"><span class="auto-style1"><%= MyRec.fields("Bales_VF")%>&nbsp;</span></font></td>	
<td  ><font size="1" face="Arial"><span class="auto-style1"><%= MyRec.fields("Tons")%>&nbsp;</span></font></td>
	<td><font size="1" face="Arial"><span class="auto-style1"><%= MyRec.fields("UOM")%>&nbsp;</span></font></td>
		<td  ><font size="1" face="Arial"><%= MyRec.fields("Other_comment")%>&nbsp;</font></td>
</tr>

 <%   end if
       ii = ii + 1
       MyRec.MoveNext
     Wend
     MyRec2.close
    %>
</table>
<%

strsql = "SELECT * from tblVirginFiber where status = 'Inbound'  and Trailer is not null and vendor <> 'Corporate' order by Trailer"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>
</form>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>
<tr> <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center>
<p align="left"><b><font face="Arial">Select Virgin Fiber Rail Car Load to Receive</font></b></td>

<td class="style2">&nbsp;</td>

</tr>
	    </table>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1 align = center>  
	 <tr class="tableheader">
<td class="auto-style1">&nbsp;</td>

				<td  align = left class="auto-style2">     <font face="Arial">	Trailer/Car</font></td>
		<td class="auto-style2"  ><font face="Arial">Date Shipped</font></td>
	
		<td  align = left class="auto-style2">     <font face="Arial">ETA</font></td>
		
		<td  align = left class="auto-style2">     <font face="Arial">Vendor</font></td>
		<td class="auto-style2"  ><font face="Arial">Item Description</font></td>
			<td class="auto-style2"  ><font face="Arial">SAP</font></td>
				<td class="auto-style2"  ><font face="Arial">PO</font></td>
		<td class="auto-style2"  ><font face="Arial">Units</font></td>
		<td  align = left class="auto-style2">     <font face="Arial">	Tons</font></td>
		<td class="auto-style2"  ><font face="Arial">UOM</font></td>
			<td class="auto-style2"  ><font face="Arial">Comments</font></td>
		
	</tr>

 <% 
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td class="auto-style1"> <font face="Arial">

<a href="VF_Login.asp?p=ER&id=<%= MyRec.fields("VID") %>">Receive</a></td>

	<td  ><font size="1" face="Arial"><span class="auto-style1"><%= MyRec.fields("Trailer")%>&nbsp;</span></font></td>
	
<td  ><font size="1" face="Arial"><span class="auto-style1"><%= MyRec.fields("Date_shipped")%>&nbsp;</span></font></td>
<% if datediff("d", MyRec.fields("ETA"), strTdate) > 0 then %>	
<td bgcolor="#FEDAD6" class="auto-style1"  >
<% else %>
<td>
<span class="auto-style1">
<% end if %></span><font size="1" face="Arial"><span class="auto-style1"><%= MyRec.fields("ETA")%>&nbsp;</span></font></td>
	<td  ><font size="1" face="Arial"><span class="auto-style1"><%= MyRec.fields("Vendor")%>&nbsp;</span></font></td>
	
<td  ><font size="1" face="Arial"><span class="auto-style1"><%= MyRec.fields("Species")%>&nbsp;</span></font></td>
<td  ><font size="1" face="Arial"><span class="auto-style1"><%= MyRec.fields("SAP_NBR")%>&nbsp;</span></font></td>
<td  ><font size="1" face="Arial"><span class="auto-style1"><%= MyRec.fields("PO")%>&nbsp;</span></font></td>
	<td  ><font size="1" face="Arial"><span class="auto-style1"><%= MyRec.fields("Bales_VF")%>&nbsp;</span></font></td>
	
<td  ><font size="1" face="Arial"><span class="auto-style1"><%= MyRec.fields("Tons")%>&nbsp;</span></font></td>
	<td  ><font size="1" face="Arial"><span class="auto-style1"><%= MyRec.fields("UOM")%>&nbsp;</span></font></td>
		<td  ><font size="1" face="Arial"><span class="auto-style1"><%= MyRec.fields("Other_comment")%>&nbsp;</span></font></td>
</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>

<!--#include file="Fiberfooter.inc"-->