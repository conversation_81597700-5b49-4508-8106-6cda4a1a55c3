<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Truck Load Transfer from Rail Car</title>
<style type="text/css">
.style1 {
	text-align: right;
}
.style3 {
	font-family: Arial;
}
.style4 {
	font-family: Arial;
	font-weight: bold;
	font-size: x-small;
}
.style5 {
	font-size: x-small;
}
.style7 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.style28 {
	color: #9F112E;
}
.style29 {
	border-style: solid;
	border-width: 1px;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	text-align: left;
	background-color: #EAF1FF;
}
.style30 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
}
.style31 {
	font-family: Arial, Helvetica, sans-serif;
	color: #FF0000;
}
.style32 {
	border-style: solid;
	border-width: 0;
	text-align: right;
	background-color: #EAF1FF;
}
.style34 {
	border-style: solid;
	border-width: 1px;
	background-color: #EAF1FF;
}
.style35 {
	border-style: solid;
	border-width: 0;
	font-family: Arial;
	font-weight: bold;
	font-size: x-small;
	background-color: #EAF1FF;
}
.style36 {
	border-style: solid;
	border-width: 1px;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	background-color: #EAF1FF;
}
.style37 {
	font-weight: bold;
	border-style: solid;
	border-width: 0;
	background-color: #EAF1FF;
}
.style38 {
	border-style: solid;
	border-width: 0;
	background-color: #EAF1FF;
}
.style39 {
	border-style: solid;
	border-width: 0;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	background-color: #EAF1FF;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID, objMOC, rstFiber, strCarrier, strSpecies
      
    Dim strTrailer, strSAP, strPO
    Dim strReleaseNbr
    Dim strRECNbr
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strGenerator
    Dim strGenCity
    Dim strGenState
    Dim strOther, strR, strsql3, strTransTrailer

       strId  = Trim(Request.QueryString("id")) 

 set objMOC = new ASP_CLS_FIBER
        set rstFiber = objMOC.FiberCarrier()

  
        strDateReceived = formatdatetime(Now(),0)


 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
 If len(request.form("Net")) > 0 then
		Call SaveData() 
else
response.redirect("TransRtoY.asp?id=" & strid & "&e=y")
end if
	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to Transfer a Load.</font></br>")
	MyRec.close
	end if

  
ELSE


strsql = "SELECT tblCars.* from tblCars WHERE tblCars.CID = " & strid

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


    strRail = MyRec.fields("Trailer")
      
        strSpecies = MyRec.fields("Species")
        strGrade = MyRec("Grade")
        strVendor = MyRec("Vendor")
        strGenerator = MyRec("Generator")
        strPO = MYRec("PO")
        strSAP = MyRec("SAP_Nbr")
        strRCWeight = MyREc("Net")
        strRCBales = MyRec("Bales_VF")

    

MyRec.close
strUsed = 0
if strGrade = "VF" Then 

strsql = "Select Bales_VF from tblCars where RC_CID = " & strid
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then
    while not MyRec.eof
    strUsed = strUsed + MyRec("Bales_VF")
    MyRec.movenext
    wend
    end if
    
    strRemaining = strRcBales - strUsed

else
strsql = "Select Net from tblCars where RC_CID = " & strid
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then
    while not MyRec.eof
    strUsed = strUsed + MyRec("NET")
    MyRec.movenext
    wend
    end if
    
    strRemaining = strRcWeight - strUsed

end if
	end if

%>



<body><br>
<table width = 100%><tr><td width = 33%></td>
	<td align = center style="width: 38%"><b><font face="Arial" size="4">
Transfer Load from Rail Car to Truck in Yard</font></b></td><td align = right width = 33%><a href="SelectTransferFromRail.asp"><b><font face = arial size = 2>RETURN</a></font></b></td></tr></table>

<% if request.querystring("e") = "y" then %><br>
<font face="arial" size="4" color="red"><b>You must enter the weight</b></font>

<% end if %>


<form name="form1" action="TransRtoY.asp?id=<%=strid%>" method="post">
<input type="hidden" name="Rail" value="<%= strRail %>">
<input type="hidden" name="Species" value="<%= strSpecies %>">
<input type="hidden" name="Grade" value="<%= strGrade %>">
<input type="hidden" name="Vendor" value="<%= strVendor %>">
<input type="hidden" name="Generator" value="<%= strGenerator %>">
<input type="hidden" name="SAP" value="<%= strSAP %>">
<input type="hidden" name="PO" value="<%= strPO %>">
<input type="hidden" name="Remaining" value="<%= strRemaining %>">
<div align="center">
<table cellspacing="0" bordercolor="#C0C0C0" style="width: 75%;" cellpadding="0" class="style30">
<tr>
    <td class="style29"><strong>Rail Car #:<%= strRail %>&nbsp;</strong></td>

    <td class="style29">&nbsp;</td>

    <td class="style36"><strong>Rail Car Receipt #:&nbsp;<%= strID %></strong></td>
    <% if strGrade = "VF" Then %>
        <td class="style36"><strong>R/C Org Bales:&nbsp;<%= strRCBales %></strong></td>
         <td class="style36"><strong>R/C Remaining Bales:&nbsp;<%= strRemaining %></strong></td>
    <% else %>

    <td class="style36"><strong>R/C Org Weight:&nbsp;<%= strRCWEight %></strong></td>
      <td class="style36"><strong>R/C Remaining Weight:&nbsp;<%= strRemaining %></strong></td>
<% end if %>
  
 <td class="style36"><strong>Grade: &nbsp;<%= strGrade %></strong></td>
    <td class="style34">
<span class="style4">Species:&nbsp;</span><span class="style3"><span class="style5"><b><%= strSpecies%>

<% if strGrade = "VF" Then %>
<font face="Arial">&nbsp;&nbsp;BTF:&nbsp;
<% strsql = "SELECT Other from tblVFSpecies where Fiber_Species = '" & strSpecies & "'"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then
    strFactor = MyRec("Other")
    else
    strFactor = 0
    end if
    MyRec.close
 %>
    <%= strFactor %></font><% end if %>
   </b></span></span></td>
  </tr>
   <tr>
    <td  align = right width="276" class="style38" colspan="6" >
   &nbsp;</td>
</tr>
  <tr>
    <td  align = right width="276" class="style37" >
   <font face="Arial" size="2">Trailer:&nbsp;</font></td>
<td  align = left class="style35" colspan="5">

      <input type="text" name="Trans_Trailer" size="23" style="width: 143px" tabindex="1"></td>
</tr>
    <tr>

      <td align = right width="276" class="style38" colspan="6" style="height: 13px">
  	</td>
</tr>
      
  <tr>
    <td width="276" class="style32">
    <span class="style7"><strong>Carrie</strong></span>r:</td>

    <td colspan="5" class="style37">

<font face = arial size = 3 color = red>
<span class="style28">

      <font face="Arial">   
	<select name="Carrier" style="font-weight: 700; width: 235px;" tabindex="2">
 	<option value="" selected>  Select Carrier (Required)</option>
      <%= objGeneral.OptionListAsString(rstFiber, "Carrier", "CDesc", strCarrier) %>
     </select></font></td>
  </tr>



  <tr>
    <td width="276" class="style32" style="height: 26px">
    </td>

    <td colspan="5" style="height: 26px" class="style38">

      </td>
  </tr>

<% if strGrade = "VF" Then %>
      <tr>
          <td  align = right width="276" class="style39" style="height: 26px" >
    <strong>Bales:</strong></td>
<td align = left colspan="5" style="height: 26px" class="style38">

      <input type="text" name="Bales" size="23" style="width: 45px; height: 26px;">&nbsp;
		<strong>&gt;&gt;&gt;</strong><span class="style31">This is a Required field -&nbsp; enter the number of bales on the trailer</span></td></tr>

<%
else %>

       <tr>
          <td  align = right width="276" class="style38" >
    <span class="style7"><strong>Broke Type</strong></span>:</td>
<td align = left colspan="5" class="style37">

      <font size="2" face = arial>
	<select name="Broke_Description" size="1" class="style1" >
 <option value="">--Select Broke Type --</option>
 <% strsql = "Select SAP, TYPE from tblBrokeSap where Category = 'BROKE' "
    	 Set MyConn = Server.CreateObject("ADODB.Recordset")
   	 MyConn.Open strSQL, Session("ConnectionString")
while not MyConn.eof
%>
 
 
       <option ><%= MyConn("Type") %></option>
     <% MyConn.movenext
     wend
     MyConn.close %>
          
     </select>&nbsp;&nbsp;&nbsp;&nbsp; (If Broke, you must select the Type)</font></td></tr>



       <tr>
          <td  align = right width="276" class="style38" >
    &nbsp;</td>
<td align = left colspan="5" class="style37">

      &nbsp;</td></tr>



       <tr>
          <td  align = right width="276" class="style38" >
    <span class="style7"><strong>Broke Packaging Format:</strong></span>&nbsp;&nbsp; </td>
<td align = left colspan="5" class="style37">
 <select name="Format"   size="1" tabindex="5">
	<option selected value="">  Select Packaging Format</option>
 	<option   >Bales</option>
<option   >Rolls</option>
<option  >Gaylords</option>

 	</select>  <font size="2" face = arial> 
	<span class="style9">(If BROKE, you must select packaging format) 
	</span> </font></font>&nbsp;</td></tr>



       <tr>
   
          <td  align = right width="276" class="style38" colspan="6" >
    &nbsp;</td>
</tr>

 

       <tr>
          <td  align = right width="276" class="style39" style="height: 26px" >
    <strong>Tons or Pounds (on trailer):</strong></td>
<td align = left colspan="5" style="height: 26px" class="style38">

      <input type="text" name="Net" size="23" style="width: 86px; height: 26px;">&nbsp;
		<strong>&gt;&gt;&gt;</strong><span class="style31">This is a Required field - you 
		must enter the product weight on the trailer</span></td></tr>

    <% end if %>

       <tr>
          <td  align = right width="276" class="style38" colspan="6" >
    &nbsp;</td>
</tr>



       <tr>
          <td  align = right width="276" class="style37" >
    <font face="Arial" size="2">Date Transferred to Yard:&nbsp;</font></td>
<td align = left colspan="5" class="style38">

      <input type="text" name="Date_Received" size="23" value = "<%= strDateReceived%>" style="width: 168px"></td></tr>
<tr>
    <td width="276" class="style38" colspan="6"></td>

  </tr>

  <tr>
	<td width="276" height="22" class="style37">
	<p align="right">
    <font face="Arial" size="2">&nbsp;Print Receipt:</font></td>
  <td height="22" colspan="5" class="style38"> <font size="1" face="Verdana">  
	<input type="checkbox"  value="ON" name="Print_receipt" checked></font></td>
</tr>
<tr>
	<td width="276" class="style38" colspan="6"></td>
</tr>

  <tr>
    <td width="276" class="style38"></td>

    <td align = left colspan="5" class="style38"><Input name="Update" type="submit" Value="Submit" ></td>
  </tr>
</table>

</div>

</form>
</body>
<%
 



 Function SaveData()
 dim strNow
   strNow = formatdatetime(Now(),0)
   strRailid = request.querystring("id")
 
strTrailer = Request.form("Trans_Trailer")
strCarrier = Replace(Request.form("Carrier"), "'", "''")

strDateReceived = formatdatetime(Request.form("Date_received"),2)
strRightnow = formatdatetime(Now,0)
if request.form("Grade") = "VF" then
strBrokeDescription = ""
else
strBrokeDescription = request.form("Broke_Description")
end if
if len(request.form("Net")) > 0 then
strNet = request.form("Net")
else
strNet = 0
end if
strBales = 0
if request.form("Grade") = "VF" then
strSpecies = request.form("Species")
strBales = request.form("Bales")
strsql = "SELECT Other from tblVFSpecies where Fiber_Species = '" & strSpecies & "'"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then
    strFactor = MyRec("Other")
    else
    strFactor = 0
    end if
    MyRec.close

strNet = round(strBales * strFactor,3)
end if
If strNet > 1000 then
strNet = round(strNet/2000, 3)
end if

         strSql = "Insert into tblCars (Trans_Carrier, Date_received, Transfer_Date, Location, RC_CID, Vendor, Generator, SAP_Nbr, PO, Trailer, Transfer_Trailer_nbr, "_
         &"  Species, Grade, Entry_Time, Entry_BID, Entry_Page, Net, Tons_received, PMO_Nbr, Broke_Description, Bales_VF, Format_PKG)"_
         &" Select '" & strCarrier & "','" & strDateReceived & "',  '" & strDateReceived & "', 'YARD', " & strRailid & ", '" & request.form("Vendor") & "', '" & request.form("Generator") & "',"_
         &" '" & request.form("SAP") & "', '" & request.form("PO") & "', 'UNKNOWN', '" & strTrailer & "', "_
         &" '" & request.form("Species") & "', '" & request.form("Grade") & "', '" & strRightnow & "',"_
         &" '" & Session("EmployeeID") & "', 'TransRtoY.asp', " & strNet & ", " & strNet & ", 'RAIL CAR', '" & strBrokeDescription & "', " & strBales & ", '" & request.form("Format") & "'"

         
 
	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
			strsql = "Update tblCars set RC_Transload = 'YES', Date_unloaded = '" & strRightNow & "' where CID = " & strRailid & ""
				Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
			if round(request.form("Remaining") - strNet,3) = 0 then
			strsql = "Update tblCars set Location = 'TRUCKS' where CID = " & strRailid & ""

	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
end if
Dim strlast
strsql = "Select Max(CID) as MAXofCID from tblCars"

   	 Set MyConn = Server.CreateObject("ADODB.Recordset")
   	 MyConn.Open strSQL, Session("ConnectionString")
   	 strlast = MyConn.fields("MaxofCID")
   	 MyConn.close
 
Response.redirect ("Transfer_RtoTReceipt.asp?id=" & strlast)

End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->