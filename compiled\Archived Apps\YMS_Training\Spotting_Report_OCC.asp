																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>OCC Spotting Report</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->



<% Dim MyRec, strsql, strDays, strDate, gVendor, gCount, gTCount, strsql1, MyConn, MyRec1, strsql2, strsql3, MyRec3, strFee, strFree
Dim strtdate, strYdate, strAdate, icount5, icountOF, strdateone


strtdate = formatdatetime(now(),2)
strYdate = dateadd("h", -60, Now())
strAdate = dateadd("d", -5, strtdate)
strYdate = formatdatetime(strYdate)
strAdate = formatdatetime(strAdate)
strdateone = Now()
strdateone = formatdatetime(strdateone)



strsql2 = "DELETE FROM tblTempSpotReport WHERE (((tblTempSpotReport.TID)>0))"
set MyRec1 = new ASP_CLS_DataAccess
         MyRec1.ExecuteSql strSql2 

	strdate = formatdatetime(now(),2)
gcount = 0
gVendor = ""

strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE date_Received is not null AND Species='OCC'  AND Location='Yard' "_
 &" AND Trailer Is Not Null  and Rejected is null   ORDER BY Vendor, Date_received"

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
       while not MyRec.Eof
       strSpecies = MyRec("Species")
          if len(MyRec.fields("Generator")) > 0 then 
strGenerator = Replace(MyRec.fields("Generator"), "'", "''")
else
strGenerator = ""
end if 

if len(MyRec.fields("Gen_city")) > 0 then 
strGenCity = Replace(MyRec.fields("Gen_city"), "'", "''")
else
strGenCity = ""
end if 
 
   			
     if MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      
              If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier,  Rec_nbr, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "',  "_
     &"  '" & MyRec.fields("PS") & "', '" & MyRec.fields("Vendor") & "', '" & strGenerator & "', '" & strGencity & "', "_
     &" '" & strAudit & "', " & strDays & ",  '" & strSpecies & "'"
     
     else
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "', '" & MyRec.fields("Vendor") & "', '" & strGenerator & "', '" & strGencity & "', "_
     &" '" & strAudit & "', " & strDays & ",  '" & strSpecies & "'"
end if
    set MyConn = new ASP_CLS_DataAccess
  '  Response.write("<br>strsql: " & strsql1)
         MyConn.ExecuteSql strSql1
         
        
       MyRec.MoveNext
 
       
       wend
       MyRec.close
       

     
%>


<body>

<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=0>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><b>
<font face="arial" size="4" >Spotting Report <%= strDate%><br>&nbsp;</font></b></td>
<td align = center>&nbsp;</td>


</tr>
	    </table>
<%        

strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where Type = 'OCC'  and days > 4.9 order by days desc "

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
if Not MyRec.eof then %>
<p align = center><b>
<font face="arial" size="4" >Critical Loads (On the Yard More than 5 Days - Consume 
First)<br>OCC</p>	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center  ><font face="Arial" size="2"><b>Request<br> to Spot</b></font></td>
		<td align = center  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center><p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left><font face="Arial" size="2"><b>Release</b></font></td>
<td align=left><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left><p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>
<td align="center" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>
	</tr>

 <% Dim ii
       ii = 0
       icount = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    
 

<td bordercolor="#000000">


	<p align="center"><input type="checkbox" name="C1" value="ON"></p></td>

	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Rec_nbr")%>&nbsp;</font></td>
    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
         
       <%   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %>   
    <% if MyRec.fields("Days") > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>	
	


	</tr>

 <%     If Myrec.fields("Carrier") = "RAIL" or MyRec.fields("Carrier") = "Rail" then
'skip
else
       	icount = icount + 1
end if 
 		 ii = ii + 1
       MyRec.MoveNext   
    
 Wend
  MyRec.close%>

</table><br>
<% end if ' if no OCC %>

<%   ' MXP


strsql2 = "DELETE FROM tblTempSpotReport WHERE (((tblTempSpotReport.TID)>0))"
set MyRec1 = new ASP_CLS_DataAccess
         MyRec1.ExecuteSql strSql2 

	strdate = formatdatetime(now(),2)
gcount = 0
gVendor = ""

strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE date_Received is not null AND (Species='MXP' or Species = 'mxp') AND Location='Yard' "_
 &" AND Trailer Is Not Null  and Rejected is null ORDER BY Vendor, Date_received"




 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
       while not MyRec.Eof

    
    
     if MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      
   If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_Nbr,  Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "', '" & MyRec.fields("PS") & "', "_
     &"  '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "',  "_
     &" '" & strAudit & "', " & strDays & ",  'MXP'"
     
     else
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days, [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "', '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "', "_
     &" '" & strAudit & "', " & strDays & ", 'MXP'"
end if
    set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql1
         
         
     
       	If Myrec.fields("Carrier") = "RAIL" or MyRec.fields("Carrier") = "Rail" then
'skip
else
       	icount = icount + 1
end if 
 		 ii = ii + 1
       MyRec.MoveNext   
    
 Wend
  MyRec.close
       
       
 


strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where Type = 'MXP'  and days > 4.9 order by days desc "

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    If not MyRec.eof then
     
%>

<p align = center><b>
<font face="arial" size="4" >Critical Loads (On the Yard More than 5 Days - Consume 
First)<br>MXP</p>	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center  ><font face="Arial" size="2"><b>Request<br> to Spot</b></font></td>

		<td align = center  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center>
		<p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left><font face="Arial" size="2"><b>Release</b></font></td>

<td align=left><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left>
<p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>

<td align="center" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>


	</tr>

 <% 
       ii = 0

       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
 

<td bordercolor="#000000">


	<p align="center"><input type="checkbox" name="C1" value="ON"></p></td>

	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Rec_nbr")%>&nbsp;</font></td>

    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
         
       <%   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %>   
    <% if MyRec.fields("Days") > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>	
	


	</tr>

 <%   
       	If Myrec.fields("Carrier") = "RAIL" or MyRec.fields("Carrier") = "Rail" then
'skip
else
       	icount = icount + 1
end if 
 		 ii = ii + 1
       MyRec.MoveNext
    
    
	%>

<%  Wend
 MyRec.close%>

</table> <br>
<% end if ' if no MXP %>

<%   ' LPSBS


strsql2 = "DELETE FROM tblTempSpotReport WHERE (((tblTempSpotReport.TID)>0))"
set MyRec1 = new ASP_CLS_DataAccess
         MyRec1.ExecuteSql strSql2 

	strdate = formatdatetime(now(),2)
gcount = 0
gVendor = ""

strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE date_Received is not null AND (Species='LPSBS' or Species = 'LPSBS') AND Location='Yard' "_
 &" AND Trailer Is Not Null  and Rejected is null   ORDER BY Vendor, Date_received"




 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
       while not MyRec.Eof

    
    
     if MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      
   If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier,   Rec_nbr, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "', '" & MyRec.fields("PS") & "',  "_
     &"  '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "',"_
     &" '" & strAudit & "', " & strDays & ",  'LPSBS'"
     
     else
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days, [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "', '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "', "_
     &" '" & strAudit & "', " & strDays & ", 'LPSBS'"
end if
    set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql1
         
         
     
       	If Myrec.fields("Carrier") = "RAIL" or MyRec.fields("Carrier") = "Rail" then
'skip
else
       	icount = icount + 1
end if 
 		 ii = ii + 1
       MyRec.MoveNext   
    
 Wend
  MyRec.close
       
       
 


strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where Type = 'LPSBS'  and days > 4.9 order by days desc "

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    If not MyRec.eof then
     
%>

<p align = center><b>
<font face="arial" size="4" >Critical Loads (On the Yard More than 5 Days - Consume 
First)<br>LPSBS</p>	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center  ><font face="Arial" size="2"><b>Request<br> to Spot</b></font></td>

		<td align = center  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center>
		<p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left><font face="Arial" size="2"><b>Release</b></font></td>

<td align=left><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left>
<p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>

<td align="center" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>


	</tr>

 <% 
       ii = 0

       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
 

<td bordercolor="#000000">


	<p align="center"><input type="checkbox" name="C1" value="ON"></p></td>

	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Rec_nbr")%>&nbsp;</font></td>

    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
         
       <%   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %>   
    <% if MyRec.fields("Days") > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>	
	


	</tr>

 <%   
       	If Myrec.fields("Carrier") = "RAIL" or MyRec.fields("Carrier") = "Rail" then
'skip
else
       	icount = icount + 1
end if 
 		 ii = ii + 1
       MyRec.MoveNext
    
    
	%>

<%  Wend
  MyRec.close%>

</table> <br>

<% end if ' if no LPSBS %>

<%   ' HWM


strsql2 = "DELETE FROM tblTempSpotReport WHERE (((tblTempSpotReport.TID)>0))"
set MyRec1 = new ASP_CLS_DataAccess
         MyRec1.ExecuteSql strSql2 

	strdate = formatdatetime(now(),2)
gcount = 0
gVendor = ""

strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE date_Received is not null AND  Species='HWM'   AND Location='Yard' "_
 &" AND Trailer Is Not Null  and Rejected is null  ORDER BY Vendor, Date_received"




 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
       while not MyRec.Eof

    
    
     if MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      
   If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr,  Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "', '" & MyRec.fields("PS") & "', "_
     &"  '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "',  "_
     &" '" & strAudit & "', " & strDays & ",  'HWM'"
     
     else
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days, [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "', '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "', "_
     &" '" & strAudit & "', " & strDays & ", 'HWM'"
end if
    set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql1
         
         
     
       	If Myrec.fields("Carrier") = "RAIL" or MyRec.fields("Carrier") = "Rail" then
'skip
else
       	icount = icount + 1
end if 
 		 ii = ii + 1
       MyRec.MoveNext   
    
 Wend
  MyRec.close
       
       
 


strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where Type = 'HWM'  and days > 4.9 order by days desc "

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    If not MyRec.eof then
     
%>

<p align = center><b>
<font face="arial" size="4" >Critical Loads (On the Yard More than 5 Days - Consume 
First)<br>HWM</p>	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center  ><font face="Arial" size="2"><b>Request<br> to Spot</b></font></td>

		<td align = center  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center>
		<p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left><font face="Arial" size="2"><b>Release</b></font></td>

<td align=left><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left>
<p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>

<td align="center" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>


	</tr>

 <% 
       ii = 0

       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
 

<td bordercolor="#000000">


	<p align="center"><input type="checkbox" name="C1" value="ON"></p></td>

	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Rec_nbr")%>&nbsp;</font></td>

    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
         
       <%   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %>   
    <% if MyRec.fields("Days") > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>	
	


	</tr>

 <%   
       	If Myrec.fields("Carrier") = "RAIL" or MyRec.fields("Carrier") = "Rail" then
'skip
else
       	icount = icount + 1
end if 
 		 ii = ii + 1
       MyRec.MoveNext
    
    
	%>

<%  Wend
 MyRec.close%>

</table> <br>
<% end if ' if no HWM %>


<%   ' SHRED


strsql2 = "DELETE FROM tblTempSpotReport WHERE (((tblTempSpotReport.TID)>0))"
set MyRec1 = new ASP_CLS_DataAccess
         MyRec1.ExecuteSql strSql2 

	strdate = formatdatetime(now(),2)
gcount = 0
gVendor = ""

strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE date_Received is not null AND  (Species='SHRED'  and (SHRED_OCC = -1 or SHRED_OCC = 1))  AND Location='Yard' "_
 &" AND Trailer Is Not Null  and Rejected is null  ORDER BY Vendor, Date_received"




 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
       while not MyRec.Eof

    
    
     if MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      
   If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr,  Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "', '" & MyRec.fields("PS") & "', "_
     &"  '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "',  "_
     &" '" & strAudit & "', " & strDays & ",  'SHRED'"
     
     else
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days, [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "', '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "', "_
     &" '" & strAudit & "', " & strDays & ", 'SHRED'"
end if
    set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql1
         
         
     
       	If Myrec.fields("Carrier") = "RAIL" or MyRec.fields("Carrier") = "Rail" then
'skip
else
       	icount = icount + 1
end if 
 		 ii = ii + 1
       MyRec.MoveNext   
    
 Wend
  MyRec.close
       
       
 


strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where Type = 'SHRED'  and days > 4.9 order by days desc "

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    If not MyRec.eof then
     
%>

<p align = center><b>
<font face="arial" size="4" >Critical Loads (On the Yard More than 5 Days - Consume 
First)<br>SHRED</p>	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center  ><font face="Arial" size="2"><b>Request<br> to Spot</b></font></td>

		<td align = center  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center>
		<p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left><font face="Arial" size="2"><b>Release</b></font></td>

<td align=left><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left>
<p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>

<td align="center" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>


	</tr>

 <% 
       ii = 0

       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
 

<td bordercolor="#000000">


	<p align="center"><input type="checkbox" name="C1" value="ON"></p></td>

	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Rec_nbr")%>&nbsp;</font></td>

    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
         
       <%   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %>   
    <% if MyRec.fields("Days") > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>	
	


	</tr>

 <%   
       	If Myrec.fields("Carrier") = "RAIL" or MyRec.fields("Carrier") = "Rail" then
'skip
else
       	icount = icount + 1
end if 
 		 ii = ii + 1
       MyRec.MoveNext
    
    
	%>

<%  Wend
 MyRec.close%>

</table> <br>
<% end if ' if no SHRED %>


<% ' OCC 2 1/2 to 4 days


strsql2 = "DELETE FROM tblTempSpotReport WHERE (((tblTempSpotReport.TID)>0))"
set MyRec1 = new ASP_CLS_DataAccess
         MyRec1.ExecuteSql strSql2 

	strdate = formatdatetime(now(),2)


strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE date_Received > '" & strAdate & "'  and date_received <= '" & strYdate & "' AND Species='OCC' AND Location='Yard' "_
 &" AND Trailer Is Not Null and Rejected is null and (Oasis_Status = 'IN' or Oasis_Status is null) ORDER BY Generator, Date_received"
 


 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
       while not MyRec.Eof

    
    
     if MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      
   If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr,  Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "', '" & MyRec.fields("PS") & "', "_
     &"  '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "',"_
     &" '" & strAudit & "', " & strDays & ",  'OCC'"
     
     else
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days, [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "', '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "', "_
     &" '" & strAudit & "', " & strDays & ", 'OCC'"
end if
    set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql1
         
     	icount = 1 + icount
       MyRec.MoveNext
       
       wend
       MyRec.close
       
       
 

strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where Type = 'OCC'   order by days desc "

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    If not MyRec.eof then 
     
%>


<br>
	


<p align = center><b>
<font face="arial" size="4" >Additional Loads (On the Yard 2 1/2 - 4 Days - Consume Next)<br>OCC</p>	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center  ><font face="Arial" size="2"><b>Request<br> to Spot</b></font></td>

		<td align = center  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center>
		<p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left><font face="Arial" size="2"><b>Release</b></font></td>
<td align=left><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left>
<p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>

<td align="center" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>


	</tr>

 <% 
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    
 

<td bordercolor="#000000">


	<p align="center"><input type="checkbox" name="C1" value="ON"></p></td>

	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Rec_nbr")%>&nbsp;</font></td>
    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
         
       <%   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %>   
    <% if MyRec.fields("Days") > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>	
	


	</tr>

 <%   
      If Myrec.fields("Carrier") = "RAIL" or MyRec.fields("Carrier") = "Rail" then
'skip
else
       	icount = icount + 1
end if 
 		 ii = ii + 1
       MyRec.MoveNext
    
    
	%>

<%  Wend %>
<% MyRec.close
%>

</table>
<br>
<% end if 'if no OCC %>

<% ' MXP 2 1/2 to 4 days


strsql2 = "DELETE FROM tblTempSpotReport WHERE (((tblTempSpotReport.TID)>0))"
set MyRec1 = new ASP_CLS_DataAccess
         MyRec1.ExecuteSql strSql2 

	strdate = formatdatetime(now(),2)


strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE date_Received > '" & strAdate & "'  and date_received <= '" & strYdate & "' AND (Species='MXP' or Species = 'mxp') AND Location='Yard' "_
 &" AND Trailer Is Not Null and Rejected is null  ORDER BY Generator, Date_received"
 


 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
       while not MyRec.Eof

    
     if MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      
   If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr,  Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "', '" & MyRec.fields("PS") & "', "_

     &"  '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "', "_
     &" '" & strAudit & "', " & strDays & ",  'MXP'"
     
     else
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days, [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "', '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "', "_
     &" '" & strAudit & "', " & strDays & ", 'MXP'"
end if
    set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql1
         
     	icount = 1 + icount
       MyRec.MoveNext
  
       
       wend
       MyRec.close
       
       
 

strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where Type = 'MXP'   order by days desc "

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
         if not MyRec.eof then 
   
%>


<br>
	


<p align = center><b>
<font face="arial" size="4" >Additional Loads (On the Yard 2 1/2 - 4 Days - Consume Next)<br>
MXP</p>	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center  ><font face="Arial" size="2"><b>Request<br> to Spot</b></font></td>

		<td align = center  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center>
		<p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left><font face="Arial" size="2"><b>Release</b></font></td>
<td align=left><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left>
<p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>

<td align="center" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>


	</tr>

 <% 
       ii = 0
  
     
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    
 

<td bordercolor="#000000">


	<p align="center"><input type="checkbox" name="C1" value="ON"></p></td>

	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Rec_nbr")%>&nbsp;</font></td>
    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
         
       <%   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %>   
    <% if MyRec.fields("Days") > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>	
	


	</tr>

 <%   
     If Myrec.fields("Carrier") = "RAIL" or MyRec.fields("Carrier") = "Rail" then
'skip
else
       	icount = icount + 1
end if 
 		 ii = ii + 1
       MyRec.MoveNext
    
    
	%>

<%  Wend %>
<% MyRec.close
%>

</table>
<br>
<% end if ' if not MXP %>


<% ' LPSBS 2 1/2 to 4 days


strsql2 = "DELETE FROM tblTempSpotReport WHERE (((tblTempSpotReport.TID)>0))"
set MyRec1 = new ASP_CLS_DataAccess
         MyRec1.ExecuteSql strSql2 

	strdate = formatdatetime(now(),2)


strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE date_Received > '" & strAdate & "'  and date_received <= '" & strYdate & "' AND  Species='LPSBS'   AND Location='Yard' "_
 &" AND Trailer Is Not Null and Rejected is null   ORDER BY Generator, Date_received"
 


 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
       while not MyRec.Eof

    
     if MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      
   If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier,  Rec_nbr, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "',  '" & MyRec.fields("PS") & "', "_

     &"  '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "', "_
     &" '" & strAudit & "', " & strDays & ",  'LPSBS'"
     
     else
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days, [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "', '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "', "_
     &" '" & strAudit & "', " & strDays & ", 'LPSBS'"
end if
    set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql1
         
     	icount = 1 + icount
       MyRec.MoveNext
  
       
       wend
       MyRec.close
       
       
 

strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where Type = 'LPSBS'   order by days desc "

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    If not MyRec.eof then
   
%>


<br>
	


<p align = center><b>
<font face="arial" size="4" >Additional Loads (On the Yard 2 1/2 - 4 Days - Consume Next)<br>
LPSBS</p>	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center  ><font face="Arial" size="2"><b>Request<br> to Spot</b></font></td>

		<td align = center  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center>
		<p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left><font face="Arial" size="2"><b>Release</b></font></td>
<td align=left><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left>
<p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>

<td align="center" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>


	</tr>

 <% 
       ii = 0
      
     
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    
 

<td bordercolor="#000000">


	<p align="center"><input type="checkbox" name="C1" value="ON"></p></td>

	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Rec_nbr")%>&nbsp;</font></td>
    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
         
       <%   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %>   
    <% if MyRec.fields("Days") > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>	
	


	</tr>

 <%   
     If Myrec.fields("Carrier") = "RAIL" or MyRec.fields("Carrier") = "Rail" then
'skip
else
       	icount = icount + 1
end if 
 		 ii = ii + 1
       MyRec.MoveNext
    
    
	%>

<%  Wend %>
<% MyRec.close
 %>

</table>
<br>
<% end if ' if not LBSP %>


<% ' HWM 2 1/2 to 4 days


strsql2 = "DELETE FROM tblTempSpotReport WHERE (((tblTempSpotReport.TID)>0))"
set MyRec1 = new ASP_CLS_DataAccess
         MyRec1.ExecuteSql strSql2 

	strdate = formatdatetime(now(),2)


strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE date_Received > '" & strAdate & "'  and date_received <= '" & strYdate & "' AND  Species='HWM' AND Location='Yard' "_
 &" AND Trailer Is Not Null and Rejected is null   ORDER BY Generator, Date_received"
 


 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
       while not MyRec.Eof

    
     if MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      
   If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr,  Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "', '" & MyRec.fields("PS") & "', "_
     &" '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "', "_
     &" '" & strAudit & "', " & strDays & ",  'HWM'"
     
     else
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days, [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "', '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "', "_
     &" '" & strAudit & "', " & strDays & ", 'HWM'"
end if
    set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql1
         
     	icount = 1 + icount
       MyRec.MoveNext
  
       
       wend
       MyRec.close
       
       
 

strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where Type = 'HWM'   order by days desc "

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then 
   
%>


<br>
	


<p align = center><b>
<font face="arial" size="4" >Additional Loads (On the Yard 2 1/2 - 4 Days - Consume Next)<br>
HWM</p>	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center  ><font face="Arial" size="2"><b>Request<br> to Spot</b></font></td>

		<td align = center  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center>
		<p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left><font face="Arial" size="2"><b>Release</b></font></td>
<td align=left><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left>
<p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>

<td align="center" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>


	</tr>

 <% 
       ii = 0
 
     
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    
 

<td bordercolor="#000000">


	<p align="center"><input type="checkbox" name="C1" value="ON"></p></td>

	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Rec_nbr")%>&nbsp;</font></td>
    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
         
       <%   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %>   
    <% if MyRec.fields("Days") > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>	
	


	</tr>

 <%   
     If Myrec.fields("Carrier") = "RAIL" or MyRec.fields("Carrier") = "Rail" then
'skip
else
       	icount = icount + 1
end if 
 		 ii = ii + 1
       MyRec.MoveNext
    
    
	%>

<%  Wend %>
<% MyRec.close
  %>

</table>
<br>
<% end if ' If not HWM %>

<% ' SHRED 2 1/2 to 4 days


strsql2 = "DELETE FROM tblTempSpotReport WHERE (((tblTempSpotReport.TID)>0))"
set MyRec1 = new ASP_CLS_DataAccess
         MyRec1.ExecuteSql strSql2 

	strdate = formatdatetime(now(),2)


strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE date_Received > '" & strAdate & "'  and date_received <= '" & strYdate & "' AND  Species='SHRED' and (SHRED_OCC = 1 or SHRED_OCC = -1) AND Location='Yard' "_
 &" AND Trailer Is Not Null and Rejected is null   ORDER BY Generator, Date_received"
 


 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
       while not MyRec.Eof

    
     if MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      
   If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr,  Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "', '" & MyRec.fields("PS") & "', "_
     &" '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "', "_
     &" '" & strAudit & "', " & strDays & ",  'SHRED'"
     
     else
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days, [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "', '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "', "_
     &" '" & strAudit & "', " & strDays & ", 'SHRED'"
end if
    set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql1
         
     	icount = 1 + icount
       MyRec.MoveNext
  
       
       wend
       MyRec.close
       
       
 

strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where Type = 'SHRED'   order by days desc "

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then 
   
%>


<br>
	


<p align = center><b>
<font face="arial" size="4" >Additional Loads (On the Yard 2 1/2 - 4 Days - Consume Next)<br>
SHRED</p>	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center  ><font face="Arial" size="2"><b>Request<br> to Spot</b></font></td>

		<td align = center  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center>
		<p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left><font face="Arial" size="2"><b>Release</b></font></td>
<td align=left><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left>
<p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>

<td align="center" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>


	</tr>

 <% 
       ii = 0
 
     
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    
 

<td bordercolor="#000000">


	<p align="center"><input type="checkbox" name="C1" value="ON"></p></td>

	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Rec_nbr")%>&nbsp;</font></td>
    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
         
       <%   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %>   
    <% if MyRec.fields("Days") > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>	
	


	</tr>

 <%   
     If Myrec.fields("Carrier") = "RAIL" or MyRec.fields("Carrier") = "Rail" then
'skip
else
       	icount = icount + 1
end if 
 		 ii = ii + 1
       MyRec.MoveNext
    
    
	%>

<%  Wend %>
<% MyRec.close
  %>

</table>
<br>
<% end if ' If not SHRED %>





<% 'OCC loads less than 2 1/2 days %>

<% if icount < 15 then     

strsql2 = "DELETE FROM tblTempSpotReport WHERE TID > 0 "
set MyRec1 = new ASP_CLS_DataAccess
         MyRec1.ExecuteSql strSql2 

	strdate = formatdatetime(now(),2)
gcount = 0
gVendor = ""

strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE date_Received > '" & strYDate & "'  and date_received <= '" & strdateone & "' AND Species='OCC' AND Location='Yard' "_
 &" AND Trailer Is Not Null and Rejected is null   ORDER BY Generator, Date_received"
 


 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

       while not MyRec.Eof

    
    
 if  lcase(MyRec.fields("Generator")) = gVendor and gcount >1 then
    
       MyRec.MoveNext
    else 
   			 if lcase(MyRec.fields("Generator")) <> gVendor then
   			 gcount = 0
   			 end if
     if MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      
                if len(MyRec.fields("Generator")) > 0 then 
strGenerator = Replace(MyRec.fields("Generator"), "'", "''")
else
strGenerator = ""
end if 

if len(MyRec.fields("Gen_city")) > 0 then 
strGenCity = Replace(MyRec.fields("Gen_city"), "'", "''")
else
strGenCity = ""
end if 

      
   If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "',  '" & MyRec.fields("PS") & "', "_
     &"  '" & MyRec.fields("Vendor") & "', '" & strGenerator & "', '" & StrGenCity & "', "_

     &" '" & strAudit & "', " & strDays & ",  'OCC'"
     
     else
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days, [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "', '" & MyRec.fields("Vendor") & "', '" & strGenerator & "', '" & StrGenCity & "', "_
     &" '" & strAudit & "', " & strDays & ", 'OCC'"
end if
    set MyConn = new ASP_CLS_DataAccess
   
         MyConn.ExecuteSql strSql1
         
         
    	gVendor = lcase(MyRec.fields("Generator"))
     	gcount = 1 + gcount
       MyRec.MoveNext
       end if
       
       wend
       MyRec.close
       
       
 

strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where Type = 'OCC'   order by days desc "

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    If not MyRec.eof then 
     
%>


<br>
	


<p align = center><b>
<font face="arial" size="4" >Additional Loads (On the Yard Current - 2 1/2 days - 
Consume last)<br>OCC</p>	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center  ><font face="Arial" size="2"><b>Request<br> to Spot</b></font></td>

		<td align = center  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center>
		<p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left><font face="Arial" size="2"><b>Release</b></font></td>
<td align=left><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left>
<p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>

<td align="center" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>


	</tr>

 <% 
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    
 

<td bordercolor="#000000">


	<p align="center"><input type="checkbox" name="C1" value="ON"></p></td>

	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Rec_nbr")%>&nbsp;</font></td>
    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
         
       <%   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %>   
    <% if MyRec.fields("Days") > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>	
	


	</tr>

 <%   
       
 		 ii = ii + 1
       MyRec.MoveNext
    
    
	%>

<%  Wend %>
<% MyRec.close
 %>

</table>
<br>
<% end if ' icount
end if  ' if not OCC %>
<% ' MXP



strsql2 = "DELETE FROM tblTempSpotReport WHERE (((tblTempSpotReport.TID)>0))"
set MyRec1 = new ASP_CLS_DataAccess
         MyRec1.ExecuteSql strSql2 

	strdate = formatdatetime(now(),2)
gcount = 0
gVendor = ""

strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE date_Received > '" & strYDate & "'  and date_received <= '" & strdateone & "' AND (Species='MXP'  or Species = 'mxp' ) AND Location='Yard' "_
 &" AND Trailer Is Not Null and Rejected is null   ORDER BY Generator, Date_received"
 


 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
       while not MyRec.Eof

    
     if MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      
   If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier,  Rec_nbr, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "', '" & MyRec.fields("PS") & "', "_

     &"  '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "', "_
     &" '" & strAudit & "', " & strDays & ",  'MXP'"
     
     else
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days, [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "', '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "', "_
     &" '" & strAudit & "', " & strDays & ", 'MXP'"
end if
    set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql1
         
       
       MyRec.MoveNext
   
       
       wend
       MyRec.close
       
       
 

strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where Type = 'MXP'   order by days desc "

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    If not MyRec.eof then 
     
%>


<br>
	


<p align = center><b>
<font face="arial" size="4" >Additional Loads (On the Yard Current - 2 1/2 days - 
Consume last)<br>MXP</p>	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center  ><font face="Arial" size="2"><b>Request<br> to Spot</b></font></td>

		<td align = center  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center>
		<p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left><font face="Arial" size="2"><b>Release</b></font></td>
<td align=left><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left>
<p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>

<td align="center" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>


	</tr>

 <% 
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    
 

<td bordercolor="#000000">


	<p align="center"><input type="checkbox" name="C1" value="ON"></p></td>

	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Rec_nbr")%>&nbsp;</font></td>
    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
         
       <%   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %>   
    <% if MyRec.fields("Days") > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>	
	


	</tr>

 <%   
       
 		 ii = ii + 1
       MyRec.MoveNext
    
    
	%>

<%  Wend %>
<% MyRec.close%>

</table>
<br>

<% end if %>





<% ' LPSBS



strsql2 = "DELETE FROM tblTempSpotReport WHERE (((tblTempSpotReport.TID)>0))"
set MyRec1 = new ASP_CLS_DataAccess
         MyRec1.ExecuteSql strSql2 

	strdate = formatdatetime(now(),2)
gcount = 0
gVendor = ""

strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE date_Received > '" & strYDate & "'  and date_received <= '" & strdateone & "' AND  Species='LPSBS'    AND Location='Yard' "_
 &" AND Trailer Is Not Null and Rejected is null and (Oasis_Status = 'IN' or Oasis_Status is null) ORDER BY Generator, Date_received"
 


 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
       while not MyRec.Eof

    
     if MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      
   If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, Vendor,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "',  '" & MyRec.fields("PS") & "', "_
     &"  '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "', "_
     &" '" & strAudit & "', " & strDays & ",  'LPSBS'"
     
     else
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days, [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "', '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "', "_
     &" '" & strAudit & "', " & strDays & ", 'LPSBS'"
end if
    set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql1
         
       
       MyRec.MoveNext
   
       
       wend
       MyRec.close
       
       
 

strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where Type = 'LPSBS'   order by days desc "

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    If not MyRec.eof then      
%>


<br>
	


<p align = center><b>
<font face="arial" size="4" >Additional Loads (On the Yard Current - 2 1/2 days - 
Consume last)<br>LPSBS</p>	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center  ><font face="Arial" size="2"><b>Request<br> to Spot</b></font></td>

		<td align = center  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center>
		<p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left><font face="Arial" size="2"><b>Release</b></font></td>
<td align=left><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left>
<p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>

<td align="center" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>


	</tr>

 <% 
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    
 

<td bordercolor="#000000">


	<p align="center"><input type="checkbox" name="C1" value="ON"></p></td>

	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Rec_nbr")%>&nbsp;</font></td>
    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
         
       <%   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %>   
    <% if MyRec.fields("Days") > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>	
	


	</tr>

 <%   
       
 		 ii = ii + 1
       MyRec.MoveNext
    
    
	%>

<%  Wend %>
<% MyRec.close%>

</table>
<br><br>
<% end if ' if not LPSBS %>
	
	
<% ' HWM



strsql2 = "DELETE FROM tblTempSpotReport WHERE (((tblTempSpotReport.TID)>0))"
set MyRec1 = new ASP_CLS_DataAccess
         MyRec1.ExecuteSql strSql2 

	strdate = formatdatetime(now(),2)
gcount = 0
gVendor = ""

strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE date_Received > '" & strYDate & "'  and date_received <= '" & strdateone & "' AND Species='HWM'   AND Location='Yard' "_
 &" AND Trailer Is Not Null and Rejected is null   ORDER BY Generator, Date_received"
 


 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
       while not MyRec.Eof

    
     if MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      
   If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier,  Vendor, Rec_nbr,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "', '" & MyRec.fields("PS") & "', "_

     &" '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "', "_
     &" '" & strAudit & "', " & strDays & ",  'HWM'"
     
     else
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days, [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "', '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "', "_
     &" '" & strAudit & "', " & strDays & ", 'HWM'"
end if
    set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql1
         
       
       MyRec.MoveNext
   
       
       wend
       MyRec.close
       
       
 

strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where Type = 'HWM'   order by days desc "

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    If not MyRec.eof then 
     
%>


<br>
	


<p align = center><b>
<font face="arial" size="4" >Additional Loads (On the Yard Current - 2 1/2 days - 
Consume last)<br>HWM</p>	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center  ><font face="Arial" size="2"><b>Request<br> to Spot</b></font></td>

		<td align = center  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center>
		<p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left><font face="Arial" size="2"><b>Release</b></font></td>
<td align=left><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left>
<p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>

<td align="center" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>


	</tr>

 <% 
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    
 

<td bordercolor="#000000">


	<p align="center"><input type="checkbox" name="C1" value="ON"></p></td>

	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Rec_nbr")%>&nbsp;</font></td>
    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
         
       <%   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %>   
    <% if MyRec.fields("Days") > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>	
	


	</tr>

 <%   
       
 		 ii = ii + 1
       MyRec.MoveNext
    
    
	%>

<%  Wend %>
<% MyRec.close%>

</table>
<br>

 


<br>
<% end if ' if not HWM %>

<% ' SHRED



strsql2 = "DELETE FROM tblTempSpotReport WHERE (((tblTempSpotReport.TID)>0))"
set MyRec1 = new ASP_CLS_DataAccess
         MyRec1.ExecuteSql strSql2 

	strdate = formatdatetime(now(),2)
gcount = 0
gVendor = ""

strsql = "SELECT tblCars.* FROM tblCars "_
 &" WHERE date_Received > '" & strYDate & "'  and date_received <= '" & strdateone & "' AND Species='SHRED' and (SHRED_OCC = 1 or SHRED_OCC = -1)   AND Location='Yard' "_
 &" AND Trailer Is Not Null and Rejected is null   ORDER BY Generator, Date_received"
 


 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
       while not MyRec.Eof

    
     if MyRec.fields("Weigh_required") = "W" or (MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then
     strAudit = "W" 
     else
     strAudit = ""
     end if
      strDays = round(Now()- cdate(MyRec.fields("Date_received")),1) 
      
   If MyRec.fields("Trailer") = "UNKNOWN" then
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier,  Vendor, Rec_nbr,  Generator, Gen_City, Audit, Days,  [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("transfer_trailer_nbr") & "', '" & MyRec.fields("Trans_Carrier") & "', '" & MyRec.fields("PS") & "', "_

     &" '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "', "_
     &" '" & strAudit & "', " & strDays & ",  'SHRED'"
     
     else
    
   strsql1 = "Insert into tblTempSpotReport (Date_received, Trailer, Carrier, Rec_nbr, PO, Vendor,  Generator, Gen_City, Audit, Days, [Type] ) "_
     &" SELECT  '" & MyRec.fields("Date_received") & "', '" & MyRec.fields("Trailer") & "', '" & MyRec.fields("Carrier") & "', '" & MyRec.fields("Release_nbr") & "', "_
     &" '" & MyRec.fields("PO") & "', '" & MyRec.fields("Vendor") & "', '" & MyRec.fields("Generator") & "', '" & MyRec.fields("Gen_city") & "', "_
     &" '" & strAudit & "', " & strDays & ", 'SHRED'"
end if
    set MyConn = new ASP_CLS_DataAccess
         MyConn.ExecuteSql strSql1
         
       
       MyRec.MoveNext
   
       
       wend
       MyRec.close
       
       
 

strsql = "SELECT tblTempSpotReport.* FROM tblTempSpotReport where Type = 'SHRED'   order by days desc "

 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    If not MyRec.eof then 
     
%>


<br>
	


<p align = center><b>
<font face="arial" size="4" >Additional Loads (On the Yard Current - 2 1/2 days - 
Consume last)<br>SHRED</p>	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
		<td align = center  ><font face="Arial" size="2"><b>Request<br> to Spot</b></font></td>

		<td align = center  ><font face="Arial" size="2"><b>Date <br>Received</b></b></font></td>
    	<td align=center>
		<p align="left"><font face="Arial" size="2"><b>Trailer</b></font></td>
		<td align=left><font face="Arial" size="2"><b>Carrier</b></font></td>
<td align=left><font face="Arial" size="2"><b>Release</b></font></td>
<td align=left><font face="Arial" size="2"><b>&nbsp;Vendor</b></font></td>
<td align=left><font face="Arial" size="2"><b>Generator</b></font></td>
<td align=left>
<p><font face="Arial" size="2"><b>Generator<br>City</b></font></td>

<td align="center" ><font face="Arial" size="2"><b>To Date <br>Detention</b></font></td>
<td align="center" ><font face="Arial" size="2"><b>Days <br>in Yard</b></font></td>


	</tr>

 <% 
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    
 

<td bordercolor="#000000">


	<p align="center"><input type="checkbox" name="C1" value="ON"></p></td>

	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align="left" > <font size="2" face="Arial"> &nbsp;<%= MyRec.fields("Trailer")%>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
<td  >   <font size="2" face="Arial">&nbsp;        <%= MyRec.fields("Rec_nbr")%>&nbsp;</font></td>
    <td  >  <font size="2" face="Arial">     <%= MyRec.fields("Vendor")%></font></td>
        <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Generator")%>&nbsp;</font></td>
            <td  >  <font size="2" face="Arial"> <%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
         
       <%   
strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    %>   
    <% if MyRec.fields("Days") > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(MyRec.fields("Days")) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"> <%= MyRec.fields("Days")%>&nbsp;</font></td>	
	


	</tr>

 <%   
       
 		 ii = ii + 1
       MyRec.MoveNext
    
    
	%>

<%  Wend %>
<% MyRec.close%>

</table>
<br>

 


<br>
<% end if ' if not SHRED %>

<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=0>

 
<br>
<table align = center><tr><font face = arial><td colspan = 4>
	<font face="Arial" size="2"><b>Requested Trailers Not on this Sheet</b></font></font></td></tr>

<tr><td><font face="Arial" size="2">Trailer #:</font></td><td>
	<font face="Arial" size="2">______________</font></td>
<font face="Arial" size="2" ><td>
	<font face="Arial" size="2">Reason for Requesting:</font></td><td>
	<font face="Arial" size="2">__________________________________________________________</font></td></tr>


<tr><td><font face="Arial" size="2">Trailer #:</font></td><td>
	<font face="Arial" size="2">______________</font></td><td>
	<font face="Arial" size="2" >Reason for Requesting:</td><td>
	<font face="Arial" size="2">__________________________________________________________</font></td></tr>


<tr><td><font face="Arial" size="2">Trailer #:</font></td><td>
	<font face="Arial" size="2">______________</font></td><td>
	<font face="Arial" size="2" >Reason for Requesting:</td><td>
	<font face="Arial" size="2">__________________________________________________________</font></td></tr>
<tr><td colspan = 4><font face="Arial" size="2"><b>Spotting Issues with Railserve</b></font></font></td></tr>

<tr><td><font face="Arial" size="2">Trailer #:</font></td><td>
	<font face="Arial" size="2">______________</font></td>
<font face="Arial" size="2" ><td>
	S<font face="Arial" size="2">potting Issue</font></font><font face="arial" size="4" ><font face="Arial" size="2">:</font></td><td>
	<font face="Arial" size="2">__________________________________________________________</font></td></tr>

<tr><td><font face="Arial" size="2">Trailer #:</font></td><td>
	<font face="Arial" size="2">______________</font></td><td>
	<font face="Arial" size="2">Spotting Issue:</font></td><td>
	<font face="Arial" size="2">__________________________________________________________</font></td></tr>


</table>
