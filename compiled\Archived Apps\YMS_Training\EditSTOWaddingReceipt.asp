<html xmlns:m="http://schemas.microsoft.com/office/2004/12/omml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Edit STO  Trailer Receipt</title>
<style type="text/css">
.style28 {
	color: #9F112E;
}
.style29 {
	font-weight: bold;
	font-family: Arial, Helvetica, sans-serif;
}
.style30 {
	text-align: right;
	font-family: Arial, Helvetica, sans-serif;
}
.style31 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.style32 {
	font-weight: bold;
	text-align: center;
}
.style33 {
	color: #000000;
}
.auto-style1 {
	border-width: 1px;
	font-weight: bold;
	text-align: center;
	background-color: #EAF1FF;
}
.auto-style2 {
	border-width: 1px;
	text-align: right;
	font-family: Arial, Helvetica, sans-serif;
	background-color: #EAF1FF;
}
.auto-style3 {
	font-weight: bold;
	border-width: 1px;
	background-color: #EAF1FF;
}
.auto-style4 {
	border-width: 1px;
	background-color: #EAF1FF;
}
.auto-style5 {
	border-width: 1px;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	background-color: #EAF1FF;
}
.auto-style6 {
	color: #000000;
	font-size: x-small;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID, strLoad, MyConn, objMOC, rstFiber, strLocation, strSAP, rstTrailer , strTrailerWeight , strTractor   
    Dim strTrailer, strCarrier, strSQL3, MyConn3, strVID, strUOM, strPounds, strRail, strDate_shipped
    Dim strLoadNbr, strTrailerTID, strBales, stralert
    Dim strRECNbr
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strGenerator
    Dim strGenCity
    Dim strGenState, gAuditStatus
    Dim strOther, strR,  gSpecies, gPO, gSAP_Nbr, gVendor, gOID, strNet, gWeight_required, gTrailerStatus, strKCWeighed
    Dim MyRec5, strsql5, strCarID

	Dim strCID

	strCID = Request.querystring("cid")

 	set objGeneral = new ASP_CLS_General



if objGeneral.IsSubmit() Then
 
 	   strTrailer = Request.form("Trailer")
	   strOther = Replace(Request.form("Other_Comments"), "'", "''") 
	   strDateReceived = Request.form("Date_received")
	   strSAPWeight = request.form("SAP_Weight")
	    strLoad = request.form("Load")
	
	   strCarrier = Request.form("Carrier")

	   strGenerator = Replace(Request.form("Generator"), "'", "''")
 
	     
		     Call SaveData() 
		    Response.redirect ("Edit_STO_Trucks.asp")
	       	
   	 

end if
%>



<body>
 
<p align = center><font face = arial size = 4 color="teal"><b>

<font face = arial size = 3 color = red>
<span class="style28">



<br>
<% strsql =  "select Carrier, Species, Grade, SAP_Nbr, Vendor, Date_received, STO_Number, Location_unloaded, Location, "_
	&"  Generator,  Trailer, Other_Comments, Tons_Received, Net,   Entry_Time, Entry_BID, Entry_Page, Status, PO, BOL from tblCars where CID = " & strCID
	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
	 If not Myrec.eof Then
		strCarrier = MyRec("Carrier")
		strSpecies = MyRec("Species")
		strGrade = MyRec("Grade")
		strSAP_Nbr = MyRec("SAP_Nbr")
		strVendor = MyRec("Vendor")
		strDatereceived = MyRec("Date_received")
		strSTO_Number = MyRec("STO_Number")
		strLocation_unloaded = MyRec("Location_unloaded")
		strLocation = MyRec("Location")
		strGenerator = MyRec("Generator")
		strTrailer = MyRec("Trailer")
		strOther_Comments = MyRec("Other_Comments")
		strTons_Received = MyRec("Tons_Received")
		strNet = MyRec("Net")
		strStatus = MyRec("Status")
     End If
	 MyRec.close
	 Set MyRec = Nothing
%>
<table border="0" width = 100%><tr><td width = 33% style="height: 24px">
<font face="Arial" size="2"><b>Species:</b> <%= strSpecies%>&nbsp;&nbsp;
<BR><b>SAP Nbr:</b> <%= strSAP_Nbr %>

</td><td align = center style="height: 24px; width: 50%;"><b><font face="Arial" size="4">
Edit Trailer Receipt for STO </font> </b></td>
	<td align = right width = 33% style="height: 24px"><b><font face = arial size = 2><a href="Edit_STO_Trucks.asp">RETURN</a></font></b></td></tr></table>

<form name="form1" action="EditSTOWaddingReceipt.asp?cid=<%=strCID%>" method="post">
<div align="center">
<table border="1" cellspacing="0" width="70%" bgcolor="#FFFFEA" style="border-collapse: collapse" cellpadding="0" bordercolorlight="#C0C0C0">
  <tr>
    <td  align = right width="17%" style="height: 42px" class="auto-style3" >
   <font face="Arial" size="2">Trailer:&nbsp;</font></td>
<td  align = left style="height: 42px" class="auto-style4">

      <font face="Arial">

      <input name="Trailer" size="15" value = "<%= strTrailer%>" style="font-weight: 700; width: 133px;" tabindex="1"><b>
		</b>&nbsp;&nbsp;</font></td>
<td  align = left width="29%" style="height: 42px" class="auto-style4">&nbsp;</td></tr>
  <tr>

      <td align = right width="17%" style="height: 33px" class="auto-style3">
	<font face="Arial" size="2">Select Carrier:</font></td>
<td  align = left colspan="2" style="height: 33px" class="auto-style4">

      <font face="Arial">   
	<select name="Carrier" style="font-weight: 700">
 	<option value="" selected>  Select Carrier (Required)</option>
 	<% strsql = "Select Carrier,C_Description, Weight from tblCarrier order by Carrier"
 	   	 Set MyRec4 = Server.CreateObject("ADODB.Recordset")
   	 MyRec4.Open strSQL, Session("ConnectionString")
   	 while not MyRec4.eof %>
   	 <option <% if strCarrier = MyRec4("Carrier") then %> selected <% end if %> value="<%= MyRec4("Carrier") %>"><%= MyRec4("Carrier") %>&nbsp;<%= MyRec4("C_Description") %>&nbsp;<%= MyRec4("Weight") %></option>"

 <%MyRec4.Movenext
 wend
 MyRec4.close %>
     </select></font><font face="Arial" size="2"><b>    

      &nbsp;</td>
</tr>



       <tr>
		<td  align = right width="17%" style="height: 34px" class="auto-style3" ><font face = arial size = 4 color="teal">

<span class="style28">

		<font face="Arial" size="2" color="red">
		<span class="style33">Weight from&nbsp;Shipping Ticket</span>:</font></td>
<td align = left colspan="2" style="height: 34px" class="auto-style4"> 

<font face = arial size = 3 color = red>

<font face="Arial">  
<span class="style28">

	<strong>  
	<input name="SAP_Weight" size="15" value="<%= strTons_Received%>" tabindex="6"></strong></span><span class="auto-style6"><strong> </strong>
Tons</td>
</tr>

       <tr>
		<td  align = right width="17%" style="height: 34px" class="auto-style4" >

<font face = arial size = 3 color = red>
		<b>

<span class="style28">

		<font face="Arial" size="2" color="red">
		<span class="style33">STO Delivery #</span></font></span></b><span class="style28"><font face="Arial" size="2" color="red"><span class="style33">: </span></font></td>
<td align = left colspan="2" style="height: 34px" class="auto-style3"> 

<font face = arial size = 3 color = red>
<span class="style28">

<font face="Arial">  
	<input name="Load" size="15" value="<%= strSTO_Number %>" tabindex="6"></td>
</tr>

       <tr>
		<td  align = right width="17%" style="height: 34px" class="auto-style3" ><font face="Arial" size="2">Date Received:&nbsp;</font></td>
<td align = left colspan="2" style="height: 34px" class="auto-style4"> <font face="Arial"> 
<input name="Date_Received" size="15" value = "<%= strDateReceived %>" style="font-weight: 700" tabindex="7"></font></td>
</tr>
              <tr>
          <td  align = right width="17%" style="height: 38px" class="auto-style3" >
   <font face="Arial" size="2">Site Shipped From:&nbsp;</font></td>
<td align = left colspan="2" style="height: 38px" class="auto-style4">
      <font face="Arial">
      <input name="Generator" size="25" value = "<%= strGenerator %>" style="font-weight: 700" tabindex="8"></font></TD></tr>
         <tr>
          <td  align = right width="17%" style="height: 37px" class="auto-style3" >
  <font face="Arial" size="2">Comments:&nbsp;</font></td >
   <td align = left colspan="2" style="height: 37px" class="auto-style4">   <font face="Arial">   
	<input name="Other_Comments" size="25" value = "<%= strOther_Comments%>" style="font-weight: 700; width: 364px;" tabindex="9">&nbsp;&nbsp;&nbsp;</td></tr>
<tr>
    <td width="17%" class="auto-style2" style="height: 29px">
	<font face = arial size = 3>
	<span class="style29">

	<font size="2">Location:&nbsp;</font></td>

    <td colspan="2" class="auto-style5" style="height: 29px">YARD</td>
  </tr>

    
  <tr>
    <td width="17%" colspan="3" class="auto-style1">

<font face = arial size = 3 color = red>
<span class="style28">

	<font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></td>

  </tr>

    </table>

</div>

</form>

<%  

Function SaveData()
	
	
	strSAPWeight = REquest.form("SAP_Weight")
	strLoad = request.form("Load")
	
	strsql =  "UPDATE tblCars SET Carrier = '" & strCarrier & "',  Generator = '" & strGenerator & "', "_
	&"  Vendor = '" & strGenerator & "', Date_received = '" & strDateReceived & "', STO_Number = '" & strLoad & "',  "_
	&"  Trailer = '" & strTrailer & "', Other_Comments = '" & strOther & "', "_
	&" Tons_Received = " & strSAPWEight & ", Net = " & strSAPWEight & ",     "_
	&"  Status = '" & strSAPWEight & "' where CID = " & strCID
    '  Response.Write strsql
	'Response.end
	        Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			

End Function %>
<!--#include file="Fiberfooter.inc"-->