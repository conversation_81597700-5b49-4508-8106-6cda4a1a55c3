																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Temporarily Out Trailers for Daily Inventory Report</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->

	<!--#include file="classes/asp_cls_headerFIBER.asp"-->

<% Dim MyRec, strsql, strDays, strDate, gSpecies, gCount, gTCount, strsql3, MyRec3, strid


strdate = formatdatetime(now(),2)
gcount = 0
gSpecies = ""

    strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "' and User_type = 'A'"


    Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL3, Session("ConnectionString")



If not Myrec2.eof then
strAdmin = "YES"
else
strAdmin = ""
end if


MyRec2.close 

IF Session("EmployeeID") = "D10480" then
strAdmin = "YES"
end if

	 if strAdmin = "" then 
	 else


strsql = "SELECT tblCars.*,  [date_received] AS Sort_Date FROM tblCars WHERE Location <> 'BALDWIN' and (((tblCars.Date_received) Is Not Null) AND (Date_unloaded is null and Trans_unload_date is null) AND ((tblCars.Trailer) Is Not Null)) ORDER BY tblCars.Species, Sort_Date "   
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<style type="text/css">
.style1 {
	font-size: x-small;
	font-weight: bold;
}
.style2 {
	font-size: x-small;
}
</style>
</head>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=0>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><b>
<font face="arial" size="4" >Temporarily Out Trailer for Daily Inventory Report</font></b></span></td>
<td align = center>&nbsp;</td>


</tr>
	    </table>
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">
<td align = center class="style1" ><font face="Arial">Temporarily Out</font></td>
	<td class="style1"  ><font face="Arial">Species</font></td>
		<td align = center  ><font face="Arial" size="1" class="style2"><b>Date <br>Received</b></b></font></td>
		<td align = center  ><font face="Arial" size="1" class="style2"><b>Door</b></b></font></td>
    	<td class="style1" ><font face="Arial">&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; Trailer</font></td>
		<td class="style1" ><font face="Arial">Carrier</font></td>
	<td ><font face="Arial" size="1" class="style1">REC Nbr</font></td>
	<td class="style1" ><font face="Arial">PO</font></td>
<td width="69" >
<p align="center" class="style1"><font face="Arial">Vendor</font></td>

<td align="center" class="style1" ><font face="Arial">Other</font></td>

	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
       strid = MyRec("CID")
       if MyRec("Oasis_status") = "OUT" then
       strTemp = "OUT"
       else
       strTemp = "IN"
       end if
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    
    <% if  UCASE(MyRec.fields("Species")) = UCASE(gSpecies) or gcount = 0 then %>
	<td ALIGN = CENTER><font size="2" face="Arial"><a href="Temp_Out.asp?s=<%= strTemp %>&id=<%= strid %>">
	<% if strTemp = "OUT" then %>
	Remove Temp Out
	<% else %>
Out
	<% end if %></a>&nbsp;</font></td>


        
        <% if isnull(MyRec.fields("Transfer_date")) then %>
        <td>       <font size="2" face="Arial">       <%= MyRec.fields("Species")%>
        <% if MyRec.fields("Rejected") = "YES" Then %>
        - Rejected
        <% end if %></font></td>
	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Door")%></font></td>
		<td  > <font size="2" face="Arial"> &nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;<%= MyRec.fields("Trailer")%></font></td>
	<td  >   <font size="2" face="Arial">        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
	<% if isnull(MyRec.fields("Rec_number")) then %>
		 <td  >  <font size="2" face="Arial">        <%= MyRec.fields("CID")%>&nbsp;</font></td>
		 <% else %>
	 <td  >  <font size="2" face="Arial">        <%= MyRec.fields("REC_number")%>&nbsp;</font></td>
	 <% end if %>
        <% else %>
        	<td>       <font size="2" face="Arial">       <%= MyRec.fields("Species")%>&nbsp;&nbsp:SHUTTLE</font></td>
		<td align = center width="69" ><font size="2" face="Arial"><%= MyRec.fields("Transfer_Date")%>&nbsp;</font></td>
		<td align = center width="69" ><font size="2" face="Arial">&nbsp;</font></td>
			<td  > <font size="2" face="Arial">&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; <%= MyRec.fields("Transfer_trailer_nbr")%></font></td>
		<td  >   <font size="2" face="Arial">        <%= MyRec.fields("Trans_Carrier")%>&nbsp;</font></td>
		   <td  >  <font size="2" face="Arial">        <%= MyRec.fields("PMO_nbr")%>&nbsp;</font></td>
		<% end if %>
		

		
 
   <td  >  <font size="2" face="Arial">        <%= MyRec.fields("PO")%>&nbsp;</font></td>
    <td  >  <font size="2" face="Arial">        <%= MyRec.fields("Vendor")%>&nbsp;</font></td>
          
	
	<td><font size="2" face="Arial"><%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>

	</tr>

 <%    gcount = gcount + 1
       gTcount = gTcount + 1
 		gSpecies = UCASE(MyRec.fields("Species"))
       ii = ii + 1
       MyRec.MoveNext
    
    %>
<% else %>
	
	  <tr class=tablecolor2>
       	<td ALIGN = CENTER><font size="2" face="Arial"><a href="Temp_Out.asp?s=<%= strTemp %>&id=<%= strid %>">
	<% if strTemp = "OUT" then %>
	Remove Temp Out
	<% else %>
	Out
	<% end if %></a>&nbsp;</font></td>
 
        <% if isnull(MyRec.fields("Transfer_date")) then %>
        <td>        <font size="2" face="Arial">         <%= MyRec.fields("Species")%>
        <% if MyRec.fields("Rejected") = "YES" Then %>
        - Rejected
        <% end if %></font></td>
        		<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
        			<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Door")%></font></td>
        		<td  > <font size="2" face="Arial">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <%= MyRec.fields("Trailer")%></font></td>
        		<td  >    <font size="2" face="Arial">        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
        		<% if isnull(MyRec.fields("Rec_number")) then %>
        		        		  <td> <font size="2" face="Arial">        <%= MyRec.fields("CID")%></font></td>
        		        		  <% else %>
        		  <td> <font size="2" face="Arial">        <%= MyRec.fields("REC_number")%></font></td>
        		  <% end if %>
        <% else %>
        	<td>       <font size="2" face="Arial">       <%= MyRec.fields("Species")%>&nbsp;&nbsp:SHUTTLE</font></td>
		<td   ><font size="2" face="Arial"><%= MyRec.fields("Transfer_Date")%>&nbsp;</font></td>
			<td   ><font size="2" face="Arial">&nbsp;</font></td>
			<td  > <font size="2" face="Arial">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <%= MyRec.fields("Transfer_Trailer_nbr")%></font></td>
				<td  >  <font size="2" face="Arial">   <%= MyRec.fields("Trans_Carrier")%>&nbsp;</font></td>
				<td> <font size="2" face="Arial"><%= MyRec.fields("PMO_nbr")%>&nbsp;</font></td>


		<% end if %>
		
<td>

     
        <font size="2" face="Arial">
        <%= MyRec.fields("PO")%></font></td>
        <td  >
        <font size="2" face="Arial">
        <%= MyRec.fields("Vendor")%></font></td>

	<td><font size="2" face="Arial"><%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>

	</tr>
		
	<% gcount = 1
	 gTcount = gTcount + 1
	 gSpecies = UCASE(MyRec.fields("Species"))
       ii = ii + 1
       MyRec.MoveNext
    
	 end if %>

<%  Wend %>

</table>
<% end if %>