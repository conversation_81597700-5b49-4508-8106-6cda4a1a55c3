<html>
<!--#include file="classes/asp_cls_SessionString.asp"-->


<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Update Entry Status</title>
</head>
<% dim strsql, MyRec, strid, strstatus


	

strid = Request.querystring("p")
strstatus = Request.querystring("id")
If request.querystring("id") = "Yes" then
strstatus = "No"
elseif request.querystring("id") = "No" then
strstatus = "Yes"
end if



 strsql =  "Update tblPermit set Entry_approval = '" & strstatus & "' where PID = " & strid & ""
 Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 
			
			
Response.redirect("Guards.asp")

 %>
