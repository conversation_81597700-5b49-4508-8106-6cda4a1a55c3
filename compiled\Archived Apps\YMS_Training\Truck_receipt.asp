																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">
<% IF Request.querystring("r") = "ER" then %>
<TITLE>Rail Receipt</TITLE>
<% else %>
<TITLE>Truck Receipt</TITLE>
<% end if %>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->




<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate, strid, strTrailer, strDateReceived, MyConn, strcid, strslq3, strpounds

strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -7, strtdate)
strid = request.querystring("id")
strpounds = request.querystring("p")
strTrailer = request.querystring("t")
strDateReceived = Request.querystring("d")

strsql = "SELECT tblCars.* FROM tblCars WHERE cid  = " & strid
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    
    strcid = MyRec.fields("CID")
        strTID = MYRec("Trailer_TID")
    
    if strTID > 1 then
    strsql2 = "Select Trailer_Description, Weight from tblTrailerOptions where TID = " & strTID
        Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL2, Session("ConnectionString")
    StrTD = MyRec2("Trailer_Description")
    strTW = MyRec2("Weight")
    MyRec2.close
    end if

    
  strsql3 = "Update tblcars set Rec_number = " & strcid & " where CID = " & strcid  
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL3
			MyConn.Close
			
%>
<body onload="if (window.print) {window.print()}">

<p align="center"><img height="40" src="kcc40white2.gif" width="450"><br><br><br>
	
</p>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Receipt for Load at Kimberly-Clark 
<% if request.querystring("r") = "ER" then %>
<a href="Select_Rail.asp"><b>Mobile</b></a></font></td>
<% elseif request.querystring("r") = "B" then %>
<a href="SelectBrokeRelease.asp"><b>Mobile</b></a></font></td>

<% else %>
<a href="SelectRelease.asp"><b>Mobile</b></a></font></td>
<% end if %>
<td align = center>&nbsp;</td>


</tr>
	    </table><br><br><br>
	
<table width = 100%>
<Tr>	<td align="left"><b><font face="Arial">Receipt Nbr:&nbsp;<%= MyRec.fields("CID")%>
<% if request.querystring("a") = "Y" then %>
<br>
Weight ends in "00".&nbsp;&nbsp; Receiver indicates a valid weight ticket was present.
<% end if %>



</font></b></td>
	<td align="right"><b><font face="Arial">Date Received:&nbsp;<%= MyRec.fields("Date_Received")%></font></b></td></Tr>


</table>	<br><br><br><br>

<table border="1" width="100%" id="table1" cellspacing="1" bordercolorlight="#808080" bordercolor="#000000">

			<tr>
		<td bordercolor="#808080" align="center" colspan="6">&nbsp;</td>
		
		<td bordercolor="#808080" align="center" class="style3"><strong><font face="Arial">SAP 
		Quantity</strong></td>
		
		<td bordercolor="#808080" align="center" colspan="2">&nbsp;</td>
		
	</tr>

		<tr>
		<td bordercolor="#808080" align="center"><b><font face="Arial">Carrier</font></b></td>
		<% if request.querystring("r") = "ER" then %>
		<td bordercolor="#808080" align="center"><b><font face="Arial">Car #</font></b></td>

		<% else %>
		<td bordercolor="#808080" align="center"><b><font face="Arial">Trailer #</font></b></td>
		<% end if %>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">Species</font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">Format</font></td>
		<td bordercolor="#808080" align="center"><b><font face="Arial">PO #</font></b></td>
		<td bordercolor="#808080" align="center"><b><font face="Arial">Release<br> 
		or BOL #</font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">Tons <br>
		Received</font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">Pounds<br>
		Received</font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">Bales</font></b></td>
		
	</tr>
			<tr>
	
		<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("Carrier")%></font></b></td>
			<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("Trailer")%></font></b></td>
		<% if MyRec("Species") = "BROKE" then 
		if len(MyRec("Release_Nbr")) > 1 then 
		strRelease = MyRec("Release_nbr")
		
 			strsql = "Select Broke_Description from tblOrder where Release = '" & strRelease & "'"
			 Set MyRec4 = Server.CreateObject("ADODB.Recordset")
   			 MyRec4.Open strSQL, Session("ConnectionString")  
   			 If Not Myrec4.eof then %>

				<td align="center"><font size="2" face="Arial"><b><%= MyRec.fields("Species")%> &nbsp;<%= MyRec("Sap_Nbr") %>&nbsp;<%= MyRec4.fields("Broke_Description")%>&nbsp;</font></td>
				<% else %>
			<td align="center"><font size="2" face="Arial"><b><%= MyRec.fields("Species")%> &nbsp;<%= MyRec("Sap_Nbr") %></font></td>
				<% end if 
				MyRec4.close
			else %>


			<td align="center"><b><font face="Arial"><%= MyRec.fields("Species")%>&nbsp;<%= MyRec("Sap_Nbr") %>-<%= MyRec("Broke_Description") %></font></b></td>
			<% end if
			else %>
						<td align="center"><b><font face="Arial"><%= MyRec.fields("Species")%>&nbsp;</font></b></td>

<% end if %>
			<td align="center"><b><font face="Arial"><%= MyRec.fields("Format_Pkg")%>&nbsp;</font></b></td>

		<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("PO")%></font></b></td>
						<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("Release_Nbr")%></font></b></td>
			<td align="center"><font face="Arial"><b><%= MyRec.fields("Tons_received")%></b></font></td>
			<td align="center"><font face="Arial"><b><%= formatnumber(strpounds,0) %></b></font></td>

					<td align="center"><font face="Arial"><b>
			<% if len(MyRec("Bales_RF")) > 0 then
			if MyRec("Bales_RF") > 0 then%>
			<%= MyRec.fields("Bales_RF")%>
			<% else %>	
			
			<%= MyRec.fields("Bales_VF")%>
			<% end if 
			else %>		
			
			<%= MyRec.fields("Bales_VF")%>

			<% end if %>&nbsp;</b></font></td>
	</tr>
	</table>
	<table>
	<tr>
		<td colspan = 6>&nbsp;</td>
	</tr>
		<tr>
		<td colspan = 6>&nbsp;</td>
	</tr></table>
	<table width = 50% align = center>
	<tr>
		<td valign="top"><b><font face="Arial">Vendor:<br>
		<%= MyRec.fields("Vendor")%></font></b></td>			
		 
		<% if len(MyRec.fields("Generator")) > 0 then %>
		<td valign="top" ><b><font face="Arial">Generator:<br><%= MyRec.fields("Generator")%> <br>
		<%= MyRec.fields("Gen_City")%>&nbsp;<%= MyRec.fields("Gen_State")%></font></b></td>
		<% end if %>
		<% if strTID > 1 then %>
			<td valign="top" ><b><font face="Arial">Trailer Selected:<br><%= strTD %> - <%= strTW %>  
 </font></b></td>
<% end if %>

	<% if len(MyRec("Trailer_weight")) > 2  then %>
			<td valign="top" ><b><font face="Arial">Trailer Weight:&nbsp;<%=MyRec("Trailer_weight") %>   
 </font></b></td>
<% end if %>

	</tr></table>
<br><br>
<table width = 50% align = center class="style4"><tr><td class="style5"> <font face="Arial">
	<strong>TARE WT (For Ticket Edit):</strong></td><td><font face="Arial"><strong>
	<% if len(MyRec("Tare_weight")) > 0 then %>
	
	<%= formatnumber(MyRec("Tare_weight"),0) %>
	<% end if %></strong></td><td>
		&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
		&nbsp;</td>
<td class="style6"><font face="Arial"><strong>This data for SCALE SYSTEM ONLY</strong></td></tr></table><br>


		<% if len(MyRec.fields("Other_comments")) > 0 then %>
		<table width = 100% align = center><tr><td align = center>
	<b><font face="Arial"><%= MyRec.fields("Other_Comments")%></td></tr></table>
		<% end if %>
	&nbsp;<p align = center>&nbsp;</p>
		<div align="center">
		<table width = 80% border="1">
	<tr><td align="center">
		<p><b><font face="Arial">SAP DOC ID</font></b></td>
		<td align="center"><b><font face="Arial">Receiver Name</font></b></td>
		
		<td align="center"><b><font face="Arial">Initials</font></b></td>
		
	</tr>
	
	<tr><% if MyRec.fields("Species") = "UNRESOLVED" or MyRec.fields("Species") = "Unresolved" then %>
	<td align="center" bgcolor="#E4E4E4"><b><font face="Arial" color="red">
	<strong>DO NOT ENTER INTO SAP</strong></font></b></td>
	<% else %>
		<% end if %>
		<td align="center">&nbsp;</td>
		<td align="center"><font face = Arial><b><%= session("Ename") %></td>
	
		<td align="center">&nbsp;</td>
	</tr>
	
</table></div>
<style type="text/css" >
.break(pagebreak-after: always)
.style1 {
	font-size: x-large;
	font-weight: bold;
}
.style2 {
	font-size: x-large;
}
.style3 {
	font-family: Arial;
}
.style4 {
	font-family: Arial, Helvetica, sans-serif;
	border: 1px solid #000000;
}
.style5 {
	text-align: left;
}
.style6 {
	text-align: center;
}
.style7 {
	font-family: Arial, Helvetica, sans-serif;
}
</style>
<% if MyRec.fields("Weigh_required") = "W" then %>
<br class="break">
<br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br>
<table width="100%" >

<p align="center"><img height="40" src="kcc40white2.gif" width="450"><br><br><br><br>
	
</p>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><b><font face="Arial"><span class="style2">Weigh Truck at Kimberly-Clark 
</span> 
<a href="SelectRelease.asp"><span class="style1">Mobile</span></a></font></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br><br><br>
	
<table width = 100%>
<Tr>	<td align="left"><b><font face="Arial">Receipt Nbr:&nbsp;<%= MyRec.fields("CID")%></font></b></td>
	<td align="right"><b><font face="Arial">Date Received:&nbsp;<%= MyRec.fields("Date_Received")%></font></b></td></Tr>


</table>	<br><br><br><br>

<table border="1" width="100%" id="table1" cellspacing="1" bordercolorlight="#808080" bordercolor="#000000">

	
		<tr>
		<td bordercolor="#808080" align="center"><b><font face="Arial">Carrier</font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">Trailer #</font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">Species</font></b></td>
	
		<td bordercolor="#808080" align="center"><b><font face="Arial">Release<br> 
		or BOL #</font></b></td>
		
	
		
	</tr>
			<tr>
	
		<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("Carrier")%></font></b></td>
			<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("Trailer")%></font></b></td>
		
		<td align="center"><b><font face="Arial"><%= MyRec.fields("Species")%>&nbsp;</font></b></td>
		
			<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("Release_Nbr")%></font></b></td>
			
	</tr>
	</table>
	
	<% end if %>