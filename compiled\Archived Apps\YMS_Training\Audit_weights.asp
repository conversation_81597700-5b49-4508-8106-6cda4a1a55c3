																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 6.0">

<TITLE>Assign Actual Weight for Truck Receipt</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->



<% Dim MyRec, strsql

strsql = "SELECT tblCars.* FROM tblCars WHERE ((((tblCars.Grade)='RF') AND ((tblCars.Trailer) Is Not Null) AND ((tblCars.Location)='yard')) and Tons_Received = 21 and Trailer <> 'UNKNOWN') OR trailer_status = 'OPEN' order by Trailer"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><b>
<font face="arial" size="4" >Assign Actual Weight</font></b></span></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1>  
	 <tr class="tableheader">
<td>&nbsp;</td>
	<td  > <font face="Arial" size="2"><b>Trailer</b></font></td>
	<td  > <font face="Arial" size="1"><b>Status</b></font></td>
	<td  align = center> <font face="Arial" size="1"><b>Weight<br>Required</b></font></td>
		<td  align = center> <font face="Arial" size="1"><b>Enter<br> Audit Weight</b></font></td>
		<td  ><font face="Arial" size="1"><b>Carrier</b></font></td>
		<td  ><font face="Arial" size="1">Species</font></td>
    
		<td  ><font face="Arial" size="1">Vendor</font></td>
		<td  >
        <font face="Arial" size="1">PO Number</font></td>
		<td  >
        <p align="center">
        <font face="Arial" size="1">Release<br> Number</font></td>
	
		<td  >
        <font face="Arial" size="1">REC <br>Number</font></td>
		<td  >
        <p align="center">
        <font face="Arial" size="1">Gross<br> Weight</font></td>
		<td  >
        <p align="center">
        <font face="Arial" size="1">Tare<br> Weight</font></td>
		<td  >        <p align="center">        <font face="Arial" size="1">Tons<br> Received</font></td>
		<td  >        <p align="center">        <font face="Arial" size="1">Audit<br> Tons</font></td>
		<td  >
        <font face="Arial" size="1">Date<br> Received</font></td>
		<td  >
        <font face="Arial" size="1">Generator</font></td>
		<td  >
        <font face="Arial" size="1">Generator<br> City</font></td>
		<td  >
        <font face="Arial" size="1">Gen<br> State</font></td>
		<td  >
        <font size="1" face="Arial">Other</font></td>
	<td  >
        <font size="1" face="Arial">Delete<br>Receipt</font></td>

	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td> <font size="1" face="Arial">


<a href="TruckReceiptEdit.asp?id=<%= MyRec.fields("CID") %>">Edit</a></td>

	<td  >        <font size="2" face="Arial"><b>        <%= MyRec.fields("Trailer")%></font></b></td>
    	<td  >        <font size="1" face="Arial"><b>        <%= MyRec.fields("Trailer_status")%>&nbsp;</font></b></td>   
        	<% if MyRec.fields("Weigh_required") = "W" then %>
	<td > <font face="Arial" size="1"><b>Yes</b></font></td>
	<% else %>
	<td > <font face="Arial" size="1"><b>&nbsp;</b></font></td>
	<% end if %>	<td  >        <font size="1" face="Arial">
	<% if MyRec.fields("Weigh_required") = "W" then %>
<a href="Truck_audit_weights.asp?id=<%= MyRec.fields("CID") %>">Enter</a></td>
<% else %>
&nbsp;
<% end if %></td>
			<td  >
        <font size="1" face="Arial"><b>
        <%= MyRec.fields("Carrier")%>&nbsp;</font></b></td>


		<td  >
        <font face="Arial" size="1">
        <%= MyRec.fields("Species")%></font></td>
		<td>
        <font size="1" face="Arial">
        <%= MyRec.fields("Vendor")%></font></td>
		<td  >
        <font size="1" face="Arial">
        <%= MyRec.fields("PO")%></font></td>
		<td  >
        <font size="1" face="Arial">
        <%= MyRec.fields("Release_Nbr")%></font></td>
	<td>
		 <font size="1" face="Arial">
        <%= MyRec.fields("REC_Number")%></font></td>
		<td align = right >
		 <font size="1" face="Arial">
        <%= MyRec.fields("Gross_Weight")%>&nbsp;</font></td>
		<td align = right >
		 <font size="1" face="Arial">
        <%= MyRec.fields("Tare_Weight")%>&nbsp;</font></td>
		<td align = right  >		 <font size="1" face="Arial">        <%= MyRec.fields("Tons_received")%>&nbsp;</font></td>
			<td align = right  >		 <font size="1" face="Arial">        <%= MyRec.fields("Audit_tons")%>&nbsp;</font></td>
		<td  >
		 <font size="1" face="Arial">
        <%= MyRec.fields("Date_Received")%></font></td>
       <td  >
		 <font size="1" face="Arial">
        <%= MyRec.fields("Generator")%></font></td>
		<td  >
		 <font size="1" face="Arial">
        <%= MyRec.fields("Gen_City")%></font></td>
		<td  >
		 <font size="1" face="Arial">
        <%= MyRec.fields("Gen_State")%></font></td>
		<td  >
		 <font size="1" face="Arial">
        <%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>
<td align = center >
		 <font size="1" face="Arial"><a href="ReceiptDelete.asp?id=<%= Myrec.fields("CID")%>">
      Delete</a></font></td>
	
	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>


<!--#include file="Fiberfooter.inc"-->