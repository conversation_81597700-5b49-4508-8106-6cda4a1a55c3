																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Duplicates</TITLE>
<!--#include file="classes/asp_cls_DataAccessAIG.asp"-->
<!--#include file="classes/asp_cls_headerAIG.asp"-->
<!--#include file="classes/DBConnectionString.inc"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->


<% Dim MyRec, strsql, MyConn, strUserType

    BuildScreen()
   

   Sub Buildscreen



strsql = "SELECT tblInbound.[Release], tblInbound.[Load_nbr], tblInbound.[Carrier], tblInbound.[Trailer], tblInbound.[Date_to], "_
&" tblInbound.[Ship_From_name], tblInbound.[PO], tblInbound.[Destination_Plant], tblInbound.[Destination_City], tblInbound.[Ship_Status] "_
&" FROM tblInbound "_
&" WHERE (((tblInbound.[Release]) In (SELECT [Release] FROM [tblInbound] As Tmp GROUP BY [Release] HAVING Count(*)>1 ) And (tblInbound.[Release])<>'0')) "_
&" ORDER BY Destination_city, release" 
    Set MyRec = Server.CreateObject("ADODB.Recordset")
MyRec.Open strSQL, Session("ConnectionString2")


%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>


<td align = center><b>
<font face="arial" size="4" >Duplicate Inbound Shipment Release Numbers</font></b></span></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=80% class = "tablecolor1" border=1 align = center>  
	 <tr class="tableheader">
	 
	 	<td  >  <font face="Arial" size="2">Site</font></td>
	<td>  <font face="Arial" size="2">Release Number</font></td>
	<td>  <font face="Arial" size="2">Load Number</font></td>
<td>  <font face="Arial" size="2">Trailer</font></td>
	<td>  <font face="Arial" size="2">PO #</font></td>
		<td  >  <font face="Arial" size="2">Ship From</font></td>
		<td  >  <font face="Arial" size="2">Date Expected</font></td>
        <td  > <font face="Arial" size="2">Carrier</font></td>
		       <td  > <font face="Arial" size="2">Status</font></td>


	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>

	<td  >      <font size="2" face="Arial">        <%= MyRec.fields("Destination_City")%>&nbsp;</font></td>
      	<td  >      <font size="2" face="Arial">        <%= MyRec.fields("Release")%>&nbsp;</font></td>
      		<td  >        <font size="2" face="Arial">        <%= MyRec.fields("Load_nbr")%>&nbsp;</font></td>
      		<td  >        <font size="2" face="Arial">        <%= MyRec.fields("Trailer")%>&nbsp;</font></td>

      		<td  >      <font size="2" face="Arial">        <%= MyRec.fields("PO")%>&nbsp;</font></td>
      				<td  >      <font size="2" face="Arial">        <%= MyRec.fields("Ship_from_name")%>&nbsp;</font></td>

      			<td  >      <font size="2" face="Arial">        <%= MyRec.fields("Date_to")%>&nbsp;</font></td>
		
			<td  >        <font size="2" face="Arial">        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
			<td  >        <font size="2" face="Arial">        <%= MyRec.fields("Ship_Status")%>&nbsp;</font></td>
	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
     MyRec.close
    %>
</table>

<% End Sub %><!--#include file="AIGfooter.inc"-->