
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">


<TITLE>Select Receipt's Release Number to Adjust </TITLE>
 
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
 

 <%    Dim objMOC,  strID

       Dim objGeneral
	
       set objGeneral = new ASP_CLS_General

if objGeneral.IsSubmit() Then

If len(Request.form("Release")) > 1 then
strid = Request.form("Release")

Response.redirect("Release_Adjust.asp?id=" & strid)

 

else
Response.write("<font size = 3 color = red face = arial><b>Please Enter a Release Number</b></font>")
end if

End if
%>
<SCRIPT LANGUAGE="JavaScript">
function formHandler(Form1){
document.forms["Form1"].submit();
}
</SCRIPT>

<style type="text/css">
.style4 {
	font-size: x-small;
}
.style5 {
	color: #FF0000;
	font-family: Arial, Helvetica, sans-serif;
}
</style>
</head>

<body>
<% If session("EMployeeID") = "C97338" or Session("EmployeeID") = "U17097" or Session("EmployeeID") = "B55404" then %>
<form name="form1" action="Select_Receipt_Adj.asp" method="post" >

<p>&nbsp;</p>
<div align="center">
<table border="1" cellpadding="0" class = "tablecolor1" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="75%" id="AutoNumber2">
<tr>    <TD align = center bgcolor="#FFFFB3">  <p>&nbsp;</p> <b>
		<font face="Arial"><br><br></font></b>&nbsp;
     <font face="Arial">
		
     	<p>&nbsp;</p>
	
<p align="center"><br>&nbsp;</TD>
       <TD align = center bgcolor="#F2F2FF">  <p>&nbsp;</p> 
		<font face="Arial" size="4"> 
		<b>Enter Release Number to Adjust&nbsp; <br>
		</b></font><br>
		<font face="Arial" size="3"><b><br></b></font>&nbsp;&nbsp;&nbsp;
     <font face = arial size = 1>
		<span class="style4">
		<input type="text" name="Release" size="20" style="width: 104px"></span></font><font size="3"><br>
		<p align="center"><input type="submit" value="Continue" id=submit1 name=submit1><br>&nbsp;</TD>
       <TD align = center bgcolor="#FFFFB3">  <p>&nbsp;</p> <b>
		<font face="Arial"><br><br></font></b>&nbsp;
     <font face="Arial">
		
     	<p>&nbsp;</p>
	
<p align="center"><br>&nbsp;</TD></tr>

</table>
</div>
</form>
<% else %>
<p class="style5"><strong>You do not have authorization to this page</strong></p>
<% end if %><!--#include file="Fiberfooter.inc"-->