																

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>YMS Yard Exception Report</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->





<%
Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate, strsql3

strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -60, strtdate)

    	
strsql = "Select ID from tblMIssed where Log_Date = '" & strtdate & "'"
    Set MyConn2 = Server.CreateObject("ADODB.Recordset")
    MyConn2.Open strSQL, Session("ConnectionString")
    If not MyConn2.eof then 
    strMissedid = Myconn2("ID")
    else
    strMissedID = 99
    end if
    
   

Function StripOut(From, What) 

    Dim i 
	 
    StripOut = From
    for i = 1 to len(What)
	StripOut = Replace(StripOut, mid(What, i, 1), "")
    next 
	 
	End Function
	
	What = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"



strsql = "SELECT Last_Update from tblLazer "

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    strLastUpdate = MyRec("Last_Update")
    MyRec.close  

strsql = "SELECT max(Audit_date) as Last_Date from tblLazer"
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    strLastDate = MyRec("Last_Date")
    MyRec.close
  strTodate = strTdate & " 12:01:00 AM"  
   
 sdiff = datediff("h", strTodate, strLastUpdate)
 If strMissedid = 99 and  sDiff > 0 then
 

  Response.redirect("Rental_Trailer_Email.asp")
    else


strsql = "SELECT tblCars.* FROM tblCars where tblCars.species='KDF' AND tblCars.location='YARD' and Entry_time <= '" & strLastDate & "' "


    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<style type="text/css">
.style1 {
	border-style: solid;
	border-width: 1px;
}
.style2 {
	font-family: Calibri;
	font-size: small;
}
.style3 {
	font-family: Calibri;
	font-weight: bold;
	font-size: small;
}
.style4 {
	font-family: Calibri;
	font-weight: bold;
}
.style5 {
	font-family: Calibri;
	font-weight: bold;
	font-size: small;
	text-align: left;
}
.style6 {
	border: 1px solid #C0C0C0;
	font-family: Calibri;
	font-weight: bold;
	font-size: small;
}
.style7 {
	border: 1px solid #C0C0C0;
	font-family: Calibri;
	font-weight: bold;
	font-size: small;
	text-align: center;
	background-color: #FFFFCC;
}
.style8 {
	border: 1px solid #C0C0C0;
}
.style9 {
	border: 1px solid #C0C0C0;
	font-family: Calibri;
	font-weight: bold;
	font-size: small;
	background-color: #FFFFCC;
}
.style10 {
	font-family: Calibri;
}
.style11 {
	color: #800080;
}
.style12 {
	text-align: right;
}
.style13 {
	border: 1px solid #C0C0C0;
	font-family: Calibri;
	font-size: small;
}
.style14 {
	border: 1px solid #C0C0C0;
	background-color: #FFFFCC;
}
.style15 {
	border: 1px solid #C0C0C0;
	font-family: Calibri;
	font-size: small;
	background-color: #FFFFCC;
}
.style16 {
	border: 1px solid #C0C0C0;
	background-color: #FFFFFF;
}
.style17 {
	border: 1px solid #C0C0C0;
	font-family: Calibri;
	font-weight: bold;
	font-size: small;
	background-color: #FFFFFF;
}
.style18 {
	border: 1px solid #C0C0C0;
	font-family: Calibri;
	font-size: small;
	background-color: #FFFFFF;
}
</style>
</head>



<body>
<div class="style12">
<span class="style10"><strong><a href="Lazer Exceptions_Excel.asp">EXCEL</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp;<span class="style11">Last Update: <%= strLastUpdate %>&nbsp;&nbsp;&nbsp;&nbsp; Last Lazer Audit Time: <%= strLastDate %></span>&nbsp;</strong></span><br>
	
</div>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD align = left class="style4">1)&nbsp; KDF Trailers in YMS Yard, not in Lazer Report </td>



</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 class = "style1" style="width: 75%">  
	 <tr class="tableheader">

	<td class="style6">  YMS Receipt #</td>
		<td class="style8"  > <p class="style5">  Carrier</td>
	<td class="style8"  > <p class="style5">       Trailer</td>

    <td class="style6"  >Vendor</td>
    <td class="style6"  >PO</td>
    <td class="style6"  >BOL #</td>
       <td class="style6"  >SAP #</td>
          <td class="style6"  >Brand</td>
    <td class="style6"  >Date Received</td>
	</tr>

 <% Dim ii
    
       while not MyRec.Eof
          strTrailer3 = MyRec("Trailer")
       strTrailer4 = stripout(strTrailer3,What)
       
            icount = 0
       
       strsql3 = "Select Trailer from tblLazer where len(Trailer) > 1"
        Set MyRec4 = Server.CreateObject("ADODB.Recordset")
    MyRec4.Open strSQL3, Session("ConnectionString")
    while not MyRec4.eof
    
    strLTrailer = MyRec4("Trailer")

    strLTrailer2 = Trim(stripout(strLTrailer,What))  
    if strLTrailer2 = strTrailer4 then
    icount = 1
    end if
    MyRec4.movenext
    wend
    MyRec4.close
 
If icount = 0 then

    
    if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>

<td class="style13"> <%= MyRec.fields("CID") %></td>
		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Carrier")%></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Trailer")%></span>&nbsp;</font></td>


		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Vendor")%></span>&nbsp;</font></td>
			<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("PO")%></span>&nbsp;</font></td>
				<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Rec_Number")%></span>&nbsp;</font></td>
					<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Sap_NBR")%></span>&nbsp;</font></td>
						<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Brand")%></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Date_received")%></span>&nbsp;</font></td>
	</tr>

 <%  end if

      
       MyRec.MoveNext
     Wend
     
     MyRec.close
     
strsql = "SELECT tblCars.* FROM tblCars  "_
&" WHERE species='KDF' AND tblCars.location='YARD' and tblCars.trailer = 'UNKNOWN'"


    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
        
       while not MyRec.Eof
           strTrailer3 = MyRec("Transfer_trailer_nbr")
       strTrailer4 = stripout(strTrailer3,What)
       
             icount = 0
       
       strsql3 = "Select Trailer from tblLazer where len(Trailer) > 1"
        Set MyRec4 = Server.CreateObject("ADODB.Recordset")
    MyRec4.Open strSQL3, Session("ConnectionString")
    while not MyRec4.eof
    
    strLTrailer = MyRec4("Trailer")

    strLTrailer2 = Trim(stripout(strLTrailer,What))  
    if strLTrailer2 = strTrailer4 then
    icount = 1
    end if
    MyRec4.movenext
    wend
    MyRec4.close
 
If icount = 0 then
   
    if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>

<td class="style13"> <%= MyRec.fields("CID") %></td>
		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Trans_Carrier")%></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Transfer_Trailer_nbr")%></span>&nbsp;</font></td>


		<td class="style8"  ><font size="1" face="Arial"><span class="style2">SHUTTLE</span>&nbsp;</font></td>
			<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("PO")%></span>&nbsp;</font></td>
				<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Rec_Number")%></span>&nbsp;</font></td>
					<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Sap_NBR")%></span>&nbsp;</font></td>
						<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Brand")%></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Date_received")%></span>&nbsp;</font></td>
	</tr>

 <%  
 end if
  
       MyRec.MoveNext
     Wend
     
     MyRec.close %>

</table>
	<br>
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1><tr>

 <TD align = left class="style4">2)&nbsp; KDF Trailers in Lazer Report, not in YMS </td> </tr>
	    </table>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 class = "style1" style="width: 75%">  
	 <tr class="tableheader">
	<td class="style3">&nbsp;</td>
		<td class="style3">&nbsp;</td>
		<td  > &nbsp;</td>
			<td class="style3"  >&nbsp;</td>
				<td class="style3"  >&nbsp;</td>
				<td class="style3"  >&nbsp;</td>
    	<td class="style7" colspan="5"  >&nbsp;Information from last receipt unloaded with 
		that Trailer Number</td>


	</tr>

	 <tr class="tableheader">
	<td class="style6">Location</td>
		<td class="style6">Dock</td>
		<td class="style8"> <p class="style5"> Carrier</td>
		<td class="style8"> <p class="style5">       Trailer</td>
			<td class="style6">Status</td>
				<td class="style6">Audit Date</td>
    	<td class="style9">Date Unloaded</td>


<td class="style9"  >Species </td>

<td class="style9"  >YMS Receipt #</td>
<td class="style9"  >Date Received</td>


	</tr>

<% strsql = "SELECT * from tblLazer where Species = 'KDF' or Species = 'KNOCK DOWNS' order by Trailer"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then     
    while not MyRec.eof
    icount = 0
    strTrailer = MyRec("Trailer")      
    strTrailer4 = stripout(strTrailer,What)
    
    
strsql3 = "Select tblCars.* from tblCars where  Location = 'YARD'"
    Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL3, Session("ConnectionString")
    while not MyConn.eof 
   
    if len(MyConn("Transfer_Trailer_nbr")) > 1 then
    strT = MyConn("Transfer_Trailer_nbr")
    else
    strT = MyConn("Trailer")
    end if
 
   
    strT2 = stripout(strT,What)
    if strT2 = strTrailer4 then
    icount = 1
      
    end if
       MyConn.movenext
    wend
    MyConn.close
   
If icount = 0 then
    strsql9 = "Select Date_unloaded, Species, Date_received, CID from tblCars where  "_
    &"  Trailer = '" & strTrailer & "' or transfer_trailer_Nbr  = '" & strTrailer & "' or Trailer = '" & strTrailer4 & "' or transfer_trailer_Nbr  = '" & strTrailer4 & "' order by Date_unloaded desc"
   Set MyConn2 = Server.CreateObject("ADODB.Recordset")
    MyConn2.Open strSQL9, Session("ConnectionString")
    If not MyConn2.eof then
    strUnload = MyConn2("Date_Unloaded")
    strSpecies = MyConn2("Species")
    strREceipt  = MyConn2("CID")
    strDateR = MyConn2("Date_Received")
    
    else
    strUnload = ""
    strSpecies = ""
    strReceipt = ""
    strDateR = ""
    end if
    MyConn2.close

   if strUnload <> "" and strUnload >  strLastDate then
 ' skip
 else


    if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    <td class="style13"> <%= MyRec.fields("Location") %></td>
<td class="style13"> <%= MyRec.fields("Slot") %>&nbsp;</td>
<td class="style13"> <%= MyRec.fields("Carrier") %></td>

	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= strTrailer %></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Status")%></span>&nbsp;</font></td>
		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Audit_Date")%></span>&nbsp;</font></td>
		<td bgcolor="#FFFFCC" class="style8" ><font size="1" face="Arial"><span class="style2"><%= strUnload%></span>&nbsp;</font></td>
<td bgcolor="#FFFFCC" class="style8" ><font size="1" face="Arial"><span class="style2"><%= strSpecies%></span>&nbsp;</font></td>
<td bgcolor="#FFFFCC" class="style8" ><font size="1" face="Arial"><span class="style2"><%= strReceipt%></span>&nbsp;</font></td>
<td bgcolor="#FFFFCC" class="style8" ><font size="1" face="Arial"><span class="style2"><%= strDateR%></span>&nbsp;</font></td>
	</tr>


  <% end if 
 end if

  MyRec.movenext
  wend
  end if
  MyRec.close%>
  </table>
  
  
  

<br>
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1><tr>

 <TD align = left class="style4">3)&nbsp; Fiber Trailers in YMS with different Commodity in Lazer Report </td> </tr>
	    </table>
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 class = "style1" style="width: 95%">  
	 <tr class="tableheader">

	<td class="style6">  YMS Receipt #</td>
		<td class="style8"  > <p class="style5">  Carrier</td>
	<td class="style8"  > <p class="style5">       Trailer</td>

    <td class="style6"  >Vendor</td>
    <td class="style6"  >PO</td>
    <td class="style6"  >Release #</td>
        <td class="style6"  >YMS Species</td>
          <td class="style6"  >Lazer Species</td>
       <td class="style6"  >SAP #</td>
    <td class="style6"  >Date Received</td>
        <td class="style6"  >Audit Date</td>
    <td class="style6"  >Location</td>

	</tr>
	
		
<% strsql = "SELECT tblCars.* FROM tblCars where (Species = 'BROKE' or Grade = 'BROKE') AND tblCars.location='YARD' "

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    
While not MyRec.eof
strTrailer = MyRec("Trailer")
strCarrier = MyREc("Carrier")
strVendor = MyRec("Vendor")
if strTrailer = "UNKNOWN" then
strTrailer = MyRec("Transfer_Trailer_nbr")
strCarrier = MyRec("Trans_Carrier")
strVendor = "SHUTTLE"
strTrailer5 = stripout(strTrailer,what)
else
strTrailer5 = strTrailer
end if
strsql2 = "Select Species,  Audit_Date, Location, Slot from tblLazer where Trailer = '" & strTrailer & "' or Trailer = '" & strTrailer5 & "'"

   Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL2, Session("ConnectionString")
    If not MyRec2.eof then
    If MyRec2("Species") = "BROKE"  then
    'do nothing
    else
      strLocation = MyRec2("Location")
    if left(strLocation,10) = "MILL DOORS" then
    'do nothing
    else

  ii = 0
 
 
     if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
	<td class="style13"> <%= MyRec.fields("CID") %></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= strCarrier %></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= strTrailer %></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= strVendor %></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("PO")%></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Release_nbr")%></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Species")%></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec2("Species") %></span>&nbsp;</font></td>						
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Sap_NBR")%></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Date_received")%></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec2.fields("Audit_date")%></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec2.fields("Location")%></span>&nbsp;</font></td>
	</tr>

 <%  strlocation = ""
 end if
 
 end if
 end if
 MyRec2.close

       MyRec.MoveNext
     Wend
    %>
    
   
    
    
    

	
<% strsql = "SELECT tblCars.* FROM tblCars where (Species = 'KCOP' or Species = 'PMX' or Species = 'OF' or Species = 'MXP')  AND tblCars.location='YARD' order by Species"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    
While not MyRec.eof
strTrailer = MyRec("Trailer")
strCarrier = MyREc("Carrier")
strVendor = MyRec("Vendor")
if strTrailer = "UNKNOWN" then
strTrailer = MyRec("Transfer_Trailer_nbr")
strCarrier = MyRec("Trans_Carrier")
strVendor = "SHUTTLE"
end if

strsql2 = "Select Species, Audit_Date, Location, Slot from tblLazer where Trailer = '" & strTrailer & "'"


   Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL2, Session("ConnectionString")
    If not MyRec2.eof then
    If  (MyRec2("Species") = "KCOP" or (MyRec2("Species") = "OF" and MyRec("Species") = "OF") or (MyRec2("Species") = "PMX" and MyRec("Species") = "PMX") or (MyRec2("Species") = "MXP" and MyRec("Species") = "MXP")) then
    'do nothing
    else
    strLocation = MyRec2("Location")
    if left(strLocation,10) = "MILL DOORS" then
    'do nothing
    else
  ii = 0
 
 
     if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td class="style13"> <%= MyRec.fields("CID") %></td>
		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= strCarrier %></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= strTrailer %></span>&nbsp;</font></td>


		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= strVendor %></span>&nbsp;</font></td>
			<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("PO")%></span>&nbsp;</font></td>
				<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Release_nbr")%></span>&nbsp;</font></td>
								<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Species")%></span>&nbsp;</font></td>
						<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec2("Species") %></span>&nbsp;</font></td>						
					<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Sap_NBR")%></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Date_received")%></span>&nbsp;</font></td>
		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec2.fields("Audit_date")%></span>&nbsp;</font></td>

	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec2.fields("Location")%></span>&nbsp;</font></td>



	</tr>

 <%  strlocation = ""
 end if
 end if
 end if
 MyRec2.close

       MyRec.MoveNext
     Wend
     MyRec.close
    %>
    
    
 	
<% strsql = "SELECT tblCars.* FROM tblCars where (Species = 'OCC' or Species = 'MXP')  AND tblCars.location='YARD' order by Species"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    
While not MyRec.eof
strTrailer = MyRec("Trailer")
strCarrier = MyREc("Carrier")
strVendor = MyRec("Vendor")
if strTrailer = "UNKNOWN" then
strTrailer = MyRec("Transfer_Trailer_nbr")
strCarrier = MyRec("Trans_Carrier")
strVendor = "SHUTTLE"
end if

strsql2 = "Select  Species, Audit_Date, Location, Slot from tblLazer where Trailer = '" & strTrailer & "'"

   Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL2, Session("ConnectionString")
    If not MyRec2.eof then
    If (MyRec2("Species") = "OCC" or ( Trim(MyRec2("Species")) = "MXP" and Trim(MyRec("Species")) = "MXP")) then
    'do nothing
    else
      strLocation = MyRec2("Location")
    if left(strLocation,10) = "MILL DOORS" then
    'do nothing
    else

  ii = 0
 
 
     if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td class="style13"> <%= MyRec.fields("CID") %></td>
		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= strCarrier %></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= strTrailer %></span>&nbsp;</font></td>


		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= strVendor %></span>&nbsp;</font></td>
			<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("PO")%></span>&nbsp;</font></td>
				<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Release_nbr")%></span>&nbsp;</font></td>
								<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Species")%></span>&nbsp;</font></td>
						<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec2("Species") %></span>&nbsp;</font></td>						
					<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Sap_NBR")%></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Date_received")%></span>&nbsp;</font></td>
		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec2.fields("Audit_date")%></span>&nbsp;</font></td>

	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec2.fields("Location")%></span>&nbsp;</font></td>



	</tr>

 <%  strLocation = ""
 end if
 end if
 end if
 MyRec2.close

       MyRec.MoveNext
     Wend
     MyRec.close

    %>   
  <% strsql = "SELECT tblCars.* FROM tblCars where Grade= 'VF'  AND tblCars.location='YARD' order by Species"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    
While not MyRec.eof
strTrailer = MyRec("Trailer")
strCarrier = MyREc("Carrier")
strVendor = MyRec("Vendor")
if strTrailer = "UNKNOWN" then
strTrailer = MyRec("Transfer_Trailer_nbr")
strCarrier = MyRec("Trans_Carrier")
strVendor = "SHUTTLE"
end if

strsql2 = "Select Species, Audit_Date, Location, Slot from tblLazer where Trailer = '" & strTrailer & "'"

   Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL2, Session("ConnectionString")
    If not MyRec2.eof then
    If MyRec2("Species") = "OCC" or MyREc2("Species") = "KCOP" or MyRec2("Species") = "OF" or MyRec2("Species") = "MXP" or MyRec2("Species") = "PMX" then
   
  ii = 0
 
 
     if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td class="style13"> <%= MyRec.fields("CID") %></td>
		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= strCarrier %></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= strTrailer %></span>&nbsp;</font></td>


		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= strVendor %></span>&nbsp;</font></td>
			<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("PO")%></span>&nbsp;</font></td>
				<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Release_nbr")%></span>&nbsp;</font></td>
								<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Species")%></span>&nbsp;</font></td>
						<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec2("Species") %></span>&nbsp;</font></td>						
					<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Sap_NBR")%></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Date_received")%></span>&nbsp;</font></td>
		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec2.fields("Audit_date")%></span>&nbsp;</font></td>

	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec2.fields("Location")%></span>&nbsp;</font></td>



	</tr>

 <%  end if
 end if
 MyRec2.close

       MyRec.MoveNext
     Wend
     MyRec.close

    %>   
  	
<% strsql = "SELECT tblCars.* FROM tblCars where (Species = 'KDF' )  AND tblCars.location='YARD' order by Species"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    
While not MyRec.eof
strTrailer = MyRec("Trailer")
strCarrier = MyREc("Carrier")
strVendor = MyRec("Vendor")
if strTrailer = "UNKNOWN" then
strTrailer = MyRec("Transfer_Trailer_nbr")
strCarrier = MyRec("Trans_Carrier")
strVendor = "SHUTTLE"
end if

strsql2 = "Select  Species, Audit_Date, Location, Slot from tblLazer where Trailer = '" & strTrailer & "'"

   Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL2, Session("ConnectionString")
    If not MyRec2.eof then
    If MyRec2("Species") = "KDF"  then
    'do nothing
    else
      strLocation = MyRec2("Location")
    if left(strLocation,10) = "MILL DOORS" then
    'do nothing
    else

  ii = 0
 
 
     if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td class="style13"> <%= MyRec.fields("CID") %></td>
		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= strCarrier %></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= strTrailer %></span>&nbsp;</font></td>


		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= strVendor %></span>&nbsp;</font></td>
			<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("PO")%></span>&nbsp;</font></td>
				<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Release_nbr")%></span>&nbsp;</font></td>
								<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Species")%></span>&nbsp;</font></td>
						<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec2("Species") %></span>&nbsp;</font></td>						
					<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Sap_NBR")%></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Date_received")%></span>&nbsp;</font></td>
		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec2.fields("Audit_date")%></span>&nbsp;</font></td>

	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec2.fields("Location")%></span>&nbsp;</font></td>



	</tr>

 <%  strLocation = ""
 end if
 end if
 end if
 MyRec2.close

       MyRec.MoveNext
     Wend
     MyRec.close

    %>   
 
</table>
	

<br>
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1><tr>

 <TD align = left class="style4">4)&nbsp; Fiber Trailers in YMS Yard, not in Lazer Report </td></tr>
	    </table>
	
	<% strsql = "SELECT tblCars.* FROM tblCars where (grade='RF' or Grade = 'BROKE') and tblCars.Trailer <> 'UNKNOWN' "_
&"  and Species <> 'ROCC' and Entry_time <= '" & strLastDate & "' "_
&"  and tblCars.Carrier <> 'RAIL' AND tblCars.location='YARD' order by Species, Trailer"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString") %>

	<TABLE borderColor=white cellSpacing=0 cellPadding=0 class = "style1" style="width: 75%">  
	 <tr class="tableheader">

	<td class="style6">  YMS Receipt #</td>

	<td class="style6">  Species</td>
		<td class="style8"  > <p class="style5">  Carrier</td>
	<td class="style8"  > <p class="style5">       Trailer</td>

    <td class="style6"  >Vendor</td>
    <td class="style6"  >PO</td>
    <td class="style6"  >Release #</td>

    <td class="style6"  >Date Received</td>
        <td class="style6"  >Time of Receipt</td>
	</tr>

 <%   
    
       while not MyRec.Eof
       
       strTrailer3 = MyRec("Trailer")
       strTrailer4 = Trim(stripout(strTrailer3,What))
       
           icount = 0
       
       strsql3 = "Select Trailer from tblLazer where len(Trailer) > 1"
        Set MyRec4 = Server.CreateObject("ADODB.Recordset")
    MyRec4.Open strSQL3, Session("ConnectionString")
    while not MyRec4.eof
    
    strLTrailer = MyRec4("Trailer")
 	if len(strLtrailer) > 1 then
    strLTrailer2 = Trim(stripout(strLTrailer,What))
  
    if strLTrailer2 = strTrailer4 then
    icount = 1
    end if
    end if
    MyRec4.movenext
    wend
    MyRec4.close
 
If icount = 0 then
    if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>

<td class="style13"> <%= MyRec.fields("CID") %></td>

<td class="style13"><%= MyRec.fields("Species") %> &nbsp;</td>
		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Carrier")%></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Trailer")%></span>&nbsp;</font></td>


		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Vendor")%></span>&nbsp;</font></td>
			<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("PO")%></span>&nbsp;</font></td>
				<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Release_Nbr")%></span>&nbsp;</font></td>
		
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Date_received")%></span>&nbsp;</font></td>
		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Entry_Time")%></span>&nbsp;</font></td>
	</tr>

 <%  end if 

      
       MyRec.MoveNext
     Wend
     MyRec.close
     
      strsql = "SELECT tblCars.* FROM tblCars WHERE (grade='RF' or Grade = 'BROKE') and tblcars.Trailer = 'UNKNOWN' and Entry_time <= '" & strLastDate & "'  "_
&"   AND tblCars.location='YARD' order by Species, Transfer_Trailer_nbr"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString") 
    
           while not MyRec.Eof
           icount = 0
       strTrailer3 = MyRec("Transfer_Trailer_nbr")
       strTrailer4 = Trim(stripout(strTrailer3,What))
       
       strsql3 = "Select Trailer from tblLazer where len(Trailer) > 1"
        Set MyRec4 = Server.CreateObject("ADODB.Recordset")
    MyRec4.Open strSQL3, Session("ConnectionString")
    while not MyRec4.eof
    strLTrailer = MyRec4("Trailer")
    if len(strLTrailer) > 1 then
    strLTrailer2 = Trim(stripout(strLTrailer,What))
    if strLTrailer2 = strTrailer4 then
    icount = 1
    end if
    end if
    MyRec4.movenext
    wend
    MyRec4.close
 
If icount = 0 then

       
    if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>

<td class="style13"> <%= MyRec.fields("CID") %></td>

<td class="style13"><%= MyRec.fields("Species") %> &nbsp;</td>
		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Trans_Carrier")%></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Transfer_Trailer_nbr")%></span>&nbsp;</font></td>


		<td class="style8"  ><font size="1" face="Arial"><span class="style2">SHUTTLE</span>&nbsp;</font></td>
			<td class="style8"  ><font size="1" face="Arial"><span class="style2">&nbsp;</span>&nbsp;</font></td>
				<td class="style8"  ><font size="1" face="Arial"><span class="style2">&nbsp;</span>&nbsp;</font></td>
		
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Date_received")%></span>&nbsp;</font></td>
		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Entry_Time")%></span>&nbsp;</font></td>
	</tr>

 <%  end if 

      
       MyRec.MoveNext
     Wend
     MyRec.close

    %>
</table>
<br>
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1><tr>

 <TD align = left class="style4">5)&nbsp; Fiber Trailers in Lazer Report, not in YMS </td> </tr>
	    </table>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 class = "style1" style="width: 90%">  
	 <tr class="tableheader">
	<td class="style3">&nbsp;</td>
		<td class="style3">&nbsp;</td>
		<td  > &nbsp;</td>
			<td class="style3"  >&nbsp;</td>
				<td class="style3"  >&nbsp;</td>
				<td class="style3"  >&nbsp;</td>
    	<td class="style7" colspan="5"  >&nbsp;Information from last receipt unloaded with 
		that Trailer Number</td>


	</tr>

	 <tr class="tableheader">
	<td class="style6">Location</td>
		<td class="style6">Species</td>
		<td class="style8"> <p class="style5"> Carrier</td>
		<td class="style8"> <p class="style5">       Trailer</td>
			<td class="style6">Status</td>
				<td class="style6">Audit Date</td>
    	<td class="style9">Date Unloaded</td>
    	<td class="style9">Location</td>

<td class="style9"  >Species </td>

<td class="style9"  >YMS Receipt #</td>
<td class="style9"  >Date Received</td>


	</tr>

<% strsql = "SELECT * from tblLazer where Species = 'OCC' or Species = 'KCOP'  or Species = 'MXP' or Species = 'BROKE' OR Species = 'FIBER' order by Trailer"
'strsql = "SELECT * from tblLazer  order by Trailer"
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then
     
    while not MyRec.eof
      icount = 0
    strUnload = ""
    strTrailer = MyRec("Trailer")
    strTrailer2 = stripout(strTrailer,What)
    

strsql3 = "Select Trailer, Transfer_Trailer_nbr from tblCars where Location = 'YARD'"
    Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL3, Session("ConnectionString")
    while not MyConn.eof 
   
    if len(MyConn("Transfer_Trailer_nbr")) > 0 then
    strT = MyConn("Transfer_Trailer_nbr")
    else
    strT = MyConn("Trailer")
    end if
 
   
    strT2 = stripout(strT,What)
    if strT2 = strTrailer2 then
    icount = 1
    end if
       MyConn.movenext
    wend
    MyConn.close
   
If icount = 0 then
    
    strsql9 = "Select Date_unloaded, Species, Date_received, CID, Transfer_trailer_nbr, Location from tblCars where  (Trailer = '" & strTrailer & "' or Trailer = '" & strTrailer2 & "' or Transfer_Trailer_nbr = '" & strTrailer & "' or Transfer_Trailer_nbr = '" & strTrailer2 & "') order by Date_unloaded desc"
   Set MyConn2 = Server.CreateObject("ADODB.Recordset")
    MyConn2.Open strSQL9, Session("ConnectionString")
    If not MyConn2.eof then
    strUnload = MyConn2("Date_Unloaded")
    strSpecies = MyConn2("Species")
    strREceipt  = MyConn2("CID")
    strDateR = MyConn2("Date_Received")
    strLocation = MyConn2("Location")
    strTransfer = MyConn2("Transfer_Trailer_nbr")
    else
    strUnload = ""
    strSpecies = ""
    strReceipt = ""
    strDateR = ""
    strLocation = ""
    strTransfer = ""
    end if
    MyConn2.close

 if strUnload <> "" and strUnload >  strLastDate then
 ' skip
 else

    if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    <td class="style13"> <%= MyRec.fields("Location") %></td>
	<td class="style13"> <%= MyRec.fields("Species") %></td>
	<td class="style13"> <%= MyRec.fields("Carrier") %></td>

	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><% if len(strTransfer) > 1 then %>
	<%= strTransfer %>
	<% else %>
	<%= MyRec.fields("Trailer")%>
	<% end if %></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Status")%></span>&nbsp;</font></td>
		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Audit_Date")%></span>&nbsp;</font></td>
		<td bgcolor="#FFFFCC" class="style8" ><font size="1" face="Arial"><span class="style2"><%= strUnload%></span>&nbsp;</font></td>
		<td bgcolor="#FFFFCC" class="style8" ><font size="1" face="Arial"><span class="style2"><%= strLocation%></span>&nbsp;</font></td>

<td bgcolor="#FFFFCC" class="style8" ><font size="1" face="Arial"><span class="style2"><%= strSpecies%></span>&nbsp;</font></td>
<td bgcolor="#FFFFCC" class="style8" ><font size="1" face="Arial"><span class="style2"><%= strReceipt%></span>&nbsp;</font></td>
<td bgcolor="#FFFFCC" class="style8" ><font size="1" face="Arial"><span class="style2"><%= strDateR%></span>&nbsp;</font></td>
	</tr>


  <% end if 
  end if


  MyRec.movenext
  wend
  end if
  MyRec.close%>
  </table><br>
  <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1><tr>

 <TD align = left class="style4">6)&nbsp; Fiber Trailers in YMS with different Carrier in Lazer Report </td> </tr>
	    </table>
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 class = "style1" style="width: 95%">  
	 <tr class="tableheader">

	<td class="style6">  YMS Receipt #</td>
		<td class="style8"  > <p class="style5"> YMS Carrier</td>
				<td class="style8"  > <p class="style5"> Lazer Carrier</td>

	<td class="style8"  > <p class="style5">    YMS   Trailer</td>
	<td class="style8"  > <p class="style5">    Lazer   Trailer</td>

    <td class="style6"  >Vendor</td>
    <td class="style6"  >PO</td>
    <td class="style6"  >Release #</td>
        <td class="style6"  >YMS Species</td>
          <td class="style6"  >Lazer Species</td>
       <td class="style6"  >SAP #</td>
    <td class="style6"  >Date Received</td>
        <td class="style6"  >Audit Date</td>
    <td class="style6"  >Location</td>

	</tr>

  <%
  
strsql = "Select tblCars.* from tblCars where location = 'YARD' and Trailer <> 'UNKNOWN'"
   Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then
     while not MyRec.eof
    icount = 0
    strTrailer = MyREc("Trailer")
    strTrailer2 = stripout(strTrailer,What)
    strCarrier = MyRec("Carrier")
    
           strsql3 = "Select Species, Carrier, Audit_Date, Trailer from tblLazer where len(Trailer) > 1"
        Set MyRec4 = Server.CreateObject("ADODB.Recordset")
    MyRec4.Open strSQL3, Session("ConnectionString")
    while not MyRec4.eof    
    strLTrailer = MyRec4("Trailer") 
    strLTrailer2 = Trim(stripout(strLTrailer,What))  
    if strLTrailer2 = strTrailer2 then
    strLazerSpecies = MyRec4("Species")
     strLazerCarrier = MyRec4("carrier")
     strAuditDate = MyREc4("Audit_date")
       if Trim(strLazerCarrier) <> Trim(strCarrier) then
       strLazer = strLtrailer2
        icount = 1
        else
        strLazer = ""
   		 end if
    end if
    MyRec4.movenext
    wend
    MyRec4.close

    
if icount = 1 then

      if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
   <% strsql4 = "Select Trailer_nbr from tblRentedTrailer where Trailer_nbr = '" & strLTrailer & "' or Trailer_nbr = '" & strLTrailer2 & "'"
     Set MyRec5 = Server.CreateObject("ADODB.Recordset")
    MyRec5.Open strSQL4, Session("ConnectionString")
    if not MyRec5.eof then
    'don't display
    else %>
   
<td class="style13"> <%= MyRec.fields("CID") %></td>
		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Carrier")%></span>&nbsp;</font></td>
				<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= strLazerCarrier %></span>&nbsp;</font></td>

	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Trailer")%></span>&nbsp;</font></td>
		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= strLazer %>&nbsp;</span>&nbsp;</font></td>

		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Vendor")%></span>&nbsp;</font></td>
			<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("PO")%></span>&nbsp;</font></td>
				<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Release_nbr")%></span>&nbsp;</font></td>
								<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Species")%></span>&nbsp;</font></td>
						<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= strLazerSpecies %></span>&nbsp;</font></td>						
					<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Sap_NBR")%></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Date_received")%></span>&nbsp;</font></td>
		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= strAuditDate %></span>&nbsp;</font></td>

	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Location")%></span>&nbsp;</font></td>



	</tr>

	
 <%   end if
 end if
           MyRec.MoveNext
       wend
       end if
       MyRec.close %>
       
       
   <%strsql = "Select tblCars.* from tblCars where location = 'YARD' and Trailer = 'UNKNOWN'"
   Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then
    while not MyRec.eof
    icount = 0
    strTrailer = MyREc("Transfer_Trailer_nbr")
    strTrailer2 = stripout(strTrailer,What)
    strCarrier = MyRec("Trans_Carrier")
    
           strsql3 = "Select Species, Carrier, Audit_Date, Trailer from tblLazer where len(Trailer) > 1"
        Set MyRec4 = Server.CreateObject("ADODB.Recordset")
    MyRec4.Open strSQL3, Session("ConnectionString")
    while not MyRec4.eof    
    strLTrailer = MyRec4("Trailer") 
    strLTrailer2 = Trim(stripout(strLTrailer,What))  
    if strLTrailer2 = strTrailer2 then
    strLazerSpecies = MyRec4("Species")
     strLazerCarrier = MyRec4("carrier")
     strAuditDate = MyREc4("Audit_date")
       if Trim(strLazerCarrier) <> Trim(strCarrier) then
       strLazer = strLtrailer2
        icount = 1
        else
        strLazer = ""
   		 end if
    end if
    MyRec4.movenext
    wend
    MyRec4.close

    
if icount = 1 then

      if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td class="style13"> <%= MyRec.fields("CID") %></td>
		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Trans_Carrier")%></span>&nbsp;</font></td>
				<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= strLazerCarrier %></span>&nbsp;</font></td>

	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Transfer_Trailer_nbr")%></span>&nbsp;</font></td>
		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= strLazer%></span>&nbsp;</font></td>

	<td class="style8"  ><font size="1" face="Arial"><span class="style2">SHUTTLE</span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("PO")%></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Release_nbr")%></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Species")%></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= strLazerSpecies %></span>&nbsp;</font></td>						
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Sap_NBR")%></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Date_received")%></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= strAuditDate %></span>&nbsp;</font></td>

	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Location")%></span>&nbsp;</font></td>



	</tr>
	
 <%   end if
           MyRec.MoveNext
       wend
       end if
       MyRec.close %>

  </table>


	<br>
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1><tr>

 <TD align = left class="style4">7)&nbsp; Trailers without a Species in Lazer Report</td> </tr>
	    </table>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 class = "style1" style="width: 75%">  
	 <tr class="tableheader">
	<td class="style3">&nbsp;</td>
		<td class="style3">&nbsp;</td>
		<td  > &nbsp;</td>
			<td class="style3"  >&nbsp;</td>
				<td class="style3"  >&nbsp;</td>
				<td class="style3"  >&nbsp;</td>
    	<td class="style7" colspan="5"  >&nbsp;Information from last receipt  with 
		that Trailer Number</td>


	</tr>

	 <tr class="tableheader">
	<td class="style6">Location</td>
		<td class="style6">Dock</td>
		<td class="style8"> <p class="style5"> Carrier</td>
		<td class="style8"> <p class="style5">       Trailer</td>
			<td class="style6">Status</td>
				<td class="style6">Audit Date</td>
    	<td class="style9">Date Unloaded</td>


<td class="style9"  >Species </td>

<td class="style9"  >YMS Receipt #</td>
<td class="style9"  >Date Received</td>


	</tr>

<% strsql = "SELECT * from tblLazer where (Species = Null or Species = '') and (Status <> 'DAMAGED' and Status <> 'DOCK') and left(Location,2) <> 'DC' order by Trailer"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    if not MyRec.eof then
    while not MyRec.eof
    icount = 0
    strTrailer = MyRec("Trailer")  
    if len(strTrailer)> 0 then    
    strTrailer4 = stripout(strTrailer,What)
    else
    strtrailer4 = strTrailer
    end if
    
    strsql9 = "Select Date_unloaded, Species, Date_received, CID from tblCars where  "_
    &"  Trailer = '" & strTrailer & "' or transfer_trailer_Nbr  = '" & strTrailer & "' or Trailer = '" & strTrailer4 & "' or transfer_trailer_Nbr  = '" & strTrailer4 & "' order by Date_unloaded desc"
   Set MyConn2 = Server.CreateObject("ADODB.Recordset")
    MyConn2.Open strSQL9, Session("ConnectionString")
    If not MyConn2.eof then
    strUnload = MyConn2("Date_Unloaded")
    strSpecies = MyConn2("Species")
    strREceipt  = MyConn2("CID")
    strDateR = MyConn2("Date_Received")
    
    else
    strUnload = ""
    strSpecies = ""
    strReceipt = ""
    strDateR = ""
    end if
    MyConn2.close
    
    if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    <td class="style13"> <%= MyRec.fields("Location") %></td>
<td class="style13"> <%= MyRec.fields("Slot") %>&nbsp;</td>
<td class="style13"> <%= MyRec.fields("Carrier") %></td>

	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= strTrailer %></span>&nbsp;</font></td>
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Status")%></span>&nbsp;</font></td>
		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Audit_Date")%></span>&nbsp;</font></td>
		<td bgcolor="#FFFFCC" class="style8" ><font size="1" face="Arial"><span class="style2"><%= strUnload%></span>&nbsp;</font></td>
<td bgcolor="#FFFFCC" class="style8" ><font size="1" face="Arial"><span class="style2"><%= strSpecies%></span>&nbsp;</font></td>
<td bgcolor="#FFFFCC" class="style8" ><font size="1" face="Arial"><span class="style2"><%= strReceipt%></span>&nbsp;</font></td>
<td bgcolor="#FFFFCC" class="style8" ><font size="1" face="Arial"><span class="style2"><%= strDateR%></span>&nbsp;</font></td>
	</tr>


  <% 


  MyRec.movenext
  wend
  end if
  MyRec.close%>
  </table>
  
  
	<br>
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1><tr>

 <TD align = left class="style4">8)&nbsp; Rented Trailers not on Lazer Report</td> </tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 class = "style1" style="width: 15%">  

	 <tr class="tableheader">
		<td class="style8"> <p class="style5">       Trailer</td>


	</tr>


<% strsql = "SELECT tblRentedTrailer.Trailer_Nbr, tblLazer.Trailer "_
&" FROM tblRentedTrailer LEFT JOIN tblLazer ON tblRentedTrailer.Trailer_Nbr = tblLazer.Trailer WHERE tblLazer.Trailer Is Null and OOS is null"
   Set MyConn2 = Server.CreateObject("ADODB.Recordset")
    MyConn2.Open strSQL, Session("ConnectionString")
    If not MyConn2.eof then 
    while not MyConn2.eof%>

       <tr bgcolor="EEF2F6">
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyConn2("Trailer_nbr") %></span>&nbsp;</font></td>
	
	</tr>
<% MyConn2.movenext 
wend
end if
MyConn2.close %>

  </table>
  
  
	<br>
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1><tr>

 <TD align = left class="style4">9)&nbsp; Fiber Loads Inbound at DC</td> </tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 class = "style1" style="width: 25%">  

	 <tr class="tableheader">
		<td class="style8"> <p class="style5">       Trailer</td>
<td class="style8"> <p class="style5">       Audit Date</td>

	</tr>


<% strTendays = dateadd("d", -11, now())

strsql = "SELECT distinct Transfer_trailer_Nbr, Trailer from tblCars where Date_received > '" & strTenDays & "' and (Trailer = 'UNKNOWN' or Entry_Page = 'DC_Receipt')"
   Set MyConn2 = Server.CreateObject("ADODB.Recordset")
    MyConn2.Open strSQL, Session("ConnectionString")
    If not MyConn2.eof then 
    while not MyConn2.eof
    if MyConn2("Trailer") = "UNKNOWN" then 
    strTrailer = MyConn2("Transfer_trailer_Nbr")
    else
    strTrailer = MyCOnn2("Trailer")
    end if
    
    strsql2 = "Select Trailer, Audit_date from tblLazer where Trailer  = '" & strTrailer & "' and Location = 'DC YARD INBOUND'"
    
     Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL2, Session("ConnectionString")
    If not MyConn.eof then 

    
    %>

       <tr bgcolor="EEF2F6">
	<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyConn("Trailer") %></span>&nbsp;</font></td>
		<td class="style8"  ><font size="1" face="Arial"><span class="style2"><%= MyConn("Audit_Date") %></span>&nbsp;</font></td>

	</tr>
<% end if
MyConn.close


MyConn2.movenext 
wend
end if
MyConn2.close %>

  </table>
 

<% end if %>


	<br>
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1><tr>

 <TD align = left class="style4">10)&nbsp; Lazer Loads on STO Report</td> </tr>
	    </table>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 class = "style1" style="width: 90%">  

	 <tr class="tableheader">
	<td class="style6">Location</td>
	<td class="style17"  >Lazer Date</td>
		<td class="style9">Plant</td>
		<td class="style14"> <p class="style5"> Carrier</td>
		<td class="style14"> <p class="style5">       Trailer</td>
			<td class="style9">Status</td>
				<td class="style9">Arrival Date</td>
    	<td class="style9">STO Number</td>
    	<td class="style9">Shipment Number</td>
<td class="style9"  >SAP #</td>



	</tr>

       <tr class=tablecolor2>
       <% strsql = "SELECT tblLazer.Location, Audit_Date, tblSTOInbound.* FROM tblLazer INNER JOIN tblSTOInbound ON tblLazer.Trailer = tblSTOInbound.Trailer"
    
     Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyREc.Open strSQL, Session("ConnectionString")
    If not MyRec.eof then 
    while not MyRec.eof %>

    <td class="style13"> <%= MyRec.fields("Location") %></td>
    <td class="style16" ><font size="1" face="Arial"><span class="style2"><%= MyRec("Audit_date") %></span>&nbsp;</font></td>
	<td class="style15"> <%= MyRec.fields("Ship_From_Name") %></td>
	<td class="style15"> <%= MyRec.fields("Ship_From_City") %></td>

	<td class="style14"  ><font size="1" face="Arial"><span class="style2"> 	<%= MyRec.fields("Trailer")%> </span>&nbsp;</font></td>
	<td class="style14"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Shipment_Status")%></span>&nbsp;</font></td>
		<td class="style14"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Arrival_Date")%></span>&nbsp;</font></td>
		<td bgcolor="#FFFFCC" class="style8" ><font size="1" face="Arial"><span class="style2"><%= MyREc("PO") %></span>&nbsp;</font></td>
		<td bgcolor="#FFFFCC" class="style8" ><font size="1" face="Arial"><span class="style2"><%= MyRec("Shipment_nbr") %></span>&nbsp;</font></td>

<td bgcolor="#FFFFCC" class="style8" ><font size="1" face="Arial"><span class="style2"><%= MyRec("SAP_Nbr")%></span>&nbsp;</font></td>

	</tr>
<% MyRec.movenext
wend
end if
MyRec.close %>

  </table><br>
 
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1><tr>

 <TD align = left class="style4">11)&nbsp; Lazer Loads on Inbound Report that 
	are not in YMS</td> </tr>
	    </table>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 class = "style1" style="width: 90%">  

	 <tr class="tableheader">
	<td class="style6">Location</td>
	<td class="style17"  >Lazer Date</td>
	<td class="style16"> <p class="style5"> Carrier</td>
		<td class="style17"  >Species</td>
		
		<td class="style16"> <p class="style5">       Trailer</td>
					<td class="style9">Status</td>
				<td class="style9">Date</td>
    	<td class="style9">PO</td>
    		<td class="style9">Release</td>
    	<td class="style9">Shipment Number</td>
<td class="style9">Ship from</td>
 
 

	</tr>

       <tr class=tablecolor2>

  
 <% strsql = "SELECT tblLazer.Trailer, tblLazer.Species, Audit_date, tblLazer.Location, tblLazer.Carrier, tblInboundHistory.Shipment_Number,  "_
 &"  tblInboundHistory.Date_to, tblInboundHistory.Ship_From_name, tblInboundHistory.Ship_From_City, tblInboundHistory.Ship_From_State, tblInboundHistory.PO, "_
 &"  tblInboundHistory.Release, tblInboundHistory.Destination_City, tblInboundHistory.Ship_Status "_   
&" FROM tblLazer INNER JOIN tblInboundHistory ON tblLazer.Trailer = tblInboundHistory.Trailer where Destination_City = 'MOBILE'"

 
     Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyREc.Open strSQL, Session("ConnectionString")
    If not MyRec.eof then 
    while not MyRec.eof
    strTrailer = MyRec("Trailer")
    strDateTo = MyREc("Date_to")
    
strsql2 = "Select Trailer from tblCars where Trailer = '" & strTrailer & "' and datediff(d, Date_Received, '" & strDateTo & "') > -5"

     Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyREc2.Open strSQL2, Session("ConnectionString")
    If not MyRec2.eof then 
    'skip
    else


 %>
    <td class="style13"> <%= MyRec.fields("Location") %></td>
    <td class="style16" ><font size="1" face="Arial"><span class="style2"><%= formatdatetime(MyRec("Audit_date"),2) %></span>&nbsp;</font></td>
	
	<td class="style18"> <%= MyRec.fields("Carrier") %></td>
	<td class="style16" ><font size="1" face="Arial"><span class="style2"><%= MyRec("Species")%></span>&nbsp;</font></td>
		<td class="style16"  ><font size="1" face="Arial"><span class="style2"> 	<%= MyRec.fields("Trailer")%> </span>&nbsp;</font></td>
 


	<td class="style14"  ><font size="1" face="Arial"><span class="style2"><%= MyRec.fields("Ship_Status")%></span>&nbsp;</font></td>
		<td class="style14"  ><font size="1" face="Arial"><span class="style2"><%= formatdatetime(MyRec.fields("Date_To"),2)%></span>&nbsp;</font></td>
		<td bgcolor="#FFFFCC" class="style8" ><font size="1" face="Arial"><span class="style2"><%= MyREc("PO") %></span>&nbsp;</font></td>
				<td bgcolor="#FFFFCC" class="style8" ><font size="1" face="Arial"><span class="style2"><%= MyREc("Release") %></span>&nbsp;</font></td>

		<td bgcolor="#FFFFCC" class="style8" ><font size="1" face="Arial"><span class="style2"><%= MyRec("Shipment_number") %></span>&nbsp;</font></td>
<td bgcolor="#FFFFCC" class="style8" ><font size="1" face="Arial"><span class="style2"><%= MyRec("Ship_From_name") %>&nbsp;<%= MyRec("Ship_From_City") %> &nbsp;<%= MyRec("Ship_From_State") %></span>&nbsp;</font></td>
 
 
	</tr>
<% end if
MyRec2.close
MyRec.movenext
wend
end if
MyRec.close %>

  </table><br>
