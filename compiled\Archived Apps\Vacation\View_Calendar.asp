﻿<html xmlns:m="http://schemas.microsoft.com/office/2004/12/omml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_sessionString.asp"--> 

<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 14.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>View</title>

<% 

For Each Item IN Request.Form
   If Len(Request(Item)) > 0 AND Len(strExportURL) = 0 Then
      strExportURL = "?"
   ElseIf Len(Request(Item)) > 0 AND Len(strExportURL) > 0 Then
      strExportURL = strExportURL & "&"
   End If
   If Len(Request(Item)) > 0 Then  
      strExportURL = strExportURL & Item & "=" & Server.UrlEncode(Request(Item))
   End If
Next
'Response.Write strExportURL


   Dim  strsql, strID 
 
 strRegion = Session("Region")
 strProgram = Session("Program")
 strMill = Session("Mill")
 strMachine = Session("Machine")
 strResource = Session("Resource")
 strLead = Session("Lead")
 strPurpose = Session("Purpose")
 strMonth = Session("Month")
 strStartingMonth = Session("StartingMonth")

    set objGeneral = new ASP_CLS_General

 if objGeneral.IsSubmit() Then

 strRegion = request.form("Region")
 strProgram = request.form("Program")
 strMill = request.form("Mill")
 strMachine = request.form("Machine")
 strMonth = request.form("Month")
 strResource = request.form("Resource")
 strLead = request.form("Lead")
 strPurpose = request.form("Purpose")
 strStartingMonth = request.form("StartingMonth")

  end if


 Session("Region") = strRegion
 Session("Program") = strProgram
 Session("Mill") = strMill
 Session("Machine") = strMachine
 Session("Month") = strMonth
 Session("Resource") = strResource
 Session("Lead") = strLead
 Session("Purpose") = strPurpose
 Session("StartingMonth") = strStartingMonth

if len(trim(Session("StartingMonth"))) = 0 then
  Session("StartingMonth") = datepart("m", now())
  'response.write("Hi")
end if

    set objGeneral = new ASP_CLS_General
    
   

   
   %>
   <style type="text/css">
.style5 {
	border-width: 1px;
	background-color: #FFFFFF;
	text-align: right;
	font-family: Calibri;
	font-size: medium;
}
.style6 {
	font-family: Arial;
	font-size: xx-small;
}

.style26{
	font-family: Arial;
	font-size: xx-small;
	background-color: #000000;
	color: #FFFFFF;
}

.style27{
	font-family: Arial;
	font-size: xx-small;
	background-color: #FFFFFF;
	color: #000000
}


.style7 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: xx-small;
	background-color: #EAF1FF;
}
.style9 {
	border-width: 1px;
	background-color: #FFFFFF;
	text-align: center;
}
.style10 {
	font-family: Calibri;
	font-size: x-small;
}
.style11 {
	border-width: 1px;
	text-align: left;
	font-family: Calibri;
	font-size: x-small;
	background-color: #EAF1FF;
}
.style12 {
	font-family: Calibri;
}
.style13 {
	font-size: x-small;
}
.style16 {
	text-align: left;
}
.style17 {
	border: 1px solid #808080;
}
.style28 {
	background-color: #EAF1FF;
}
.style29 {
	font-family: Calibri;
	font-size: x-small;
	background-color: #EAF1FF;
}
.style30 {
	font-family: Arial;
	font-size: small;
	background-color: #EAF1FF;
}
.style31 {
	font-family: Arial, Helvetica, sans-serif;
}
.style33 {
	color: #FFFFFF;
}
.style34 {
	border-color: #000000;
	border-width: 1px;
	color: #FFFFFF;
}
.style35 {
	border-color: #000000;
	border-width: 1px;
	font-family: Arial, Helvetica, sans-serif;
	text-align: center;
}

div.wrapper {
    overflow:hidden;
    overflow-y: scroll;
    height: 600px; // change this to desired height
}
</style>

 <script language="javascript">
function openWindow(pURL)
{
	myWindow = window.open(pURL, "myLittleCalendar", 'toolbar=no,location=no,directories=no,status=no,menubar=no,scrollbars=no,resizable=no,width=170,height=270');
}


</script>


 
 <% intYear = datepart("yyyy", date()) %>

 
<div class="style16">
  
<br>
   <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class="tablecolor1" border=1>

      <tr class="tableheader">
	<td class="style9"   style="height: 34px" width="50%" align="= center"> <a href="CreateCalendar.asp?s=<%=Request.querystring("s")%>">
	<img  border="0" src="images/Add_new.png"></a>
	</font>
 
	</td>
	<td bgcolor="red" class="style35" style="width: 100px"><strong>&nbsp;&nbsp;</strong><span class="style33"><strong>&nbsp;On-Site&nbsp;&nbsp;&nbsp; Resource&nbsp;</strong></span></td> 
	<td bgcolor="green" class="style35" style="width: 100px"><strong>&nbsp;&nbsp;&nbsp; 
	</strong> <span class="style33">
	<strong>On-Site&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Staff</strong></span><strong>&nbsp;</strong></td> 
	<td bgcolor="yellow" class="style35" style="width: 100px">&nbsp;&nbsp;&nbsp;<strong> Remote&nbsp;&nbsp; Staff</strong></td>
 
	<td bgcolor="blue" class="style34" style="width: 100px"><strong>&nbsp;&nbsp;&nbsp; 
	<span class="style31">&nbsp;Vacation</span></strong></td>
 

	<td class="style5" style="height: 34px"><font size="4"><a href="View_Calendar_Excel.asp<%=strExportURL%>" target="_blank">
	Export</a></font></td>
</tr></table>


<form name="form1" action="View_Calendar.asp" method="post">

 
<TABLE cellSpacing=1 cellPadding=2 class = "style17" style="width: 95%" align="center">  
  <TR>
    <TD class="style29"  >Region</TD>
    <TD align="left" class="style28">	<font face="Arial" size="2">
     <strong><span class="style12"><span class="style13">
     <select name="Region" size="1" tabindex="1" style="height: 22px">
       <option value = "">--- All---</option>
   
  <% 
 
    strsql = "SELECT Distinct Region from tbl_GPS_Calendar where len(Region) > 1 order by Region"
  
     Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
while not MyConn.eof %>
 
<option <% if strRegion = MyConn("Region") then %> selected <% end if %> value="<%=MyConn("Region") %>"><%= MyConn("Region") %></option>
 
<% MyConn.movenext
wend
MyConn.close %>
     </select></span></span></strong> </font>
				</TD>
    <TD align="left" class="style11">	&nbsp;</TD>
     
    <TD class="style29" >Signature Program</TD>
    <TD   align="left" style="width: 186px" class="style28">

     &nbsp;<span class="style10"><strong>
	     <select name="Program" size="1" tabindex="10">
       <option value = "">--- All---</option>
   
  <% 
 
    strsql = "SELECT Distinct Program from tbl_GPS_Calendar where len(Program) > 2 order by Program"
  
     Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
while not MyConn.eof %>
 
<option <% if strProgram = MyConn("Program") then %> selected <% end if %>  ><%= MyConn("Program") %></option>
 
<% MyConn.movenext
wend
MyConn.close %>
     </select></strong></span></TD>
   
    <TD   align="left" class="style29">

     Mill</TD>
   
    <TD   align="left" class="style28">     

  &nbsp;<font size="2" face="Arial"><strong><span class="style12"><span class="style13"><select name="Mill" size="1" tabindex="1" style="height: 22px">
       <option value = "">--- All---</option>
   
  <% if len(strMill) = 0 then
       strMill = 0
     end if
 
    strsql = "SELECT  ID, Mill from tblMill_KC order by Mill"
  
     Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
while not MyConn.eof %>
 
<option <% if cint(strMill) = cint(MyConn("ID")) then %> selected <% end if %> value="<%=MyConn("ID") %>"><%= MyConn("Mill") %></option>
 
<% MyConn.movenext
wend
MyConn.close %>
     </select></span></span></strong></font></TD>
   
    <TD   align="left" class="style29">     

     Machine</TD>
   
    <TD   align="left" class="style28">     

     <font size="2" face="Arial"> <strong><span class="style12">
		<span class="style13">
	<input type="text" name="Machine"  value="<%=strMachine%>" style="width: 143px" tabindex="2"></span></span></strong></font></TD>
   
    <TD class="style29"  >Month </TD>
   
    <TD class="style28"  ><select name="Month">
                            <option <%if strMonth = "" Then%> selected <%End If%> value="">
							&nbsp;</option>
                            <option <%if strMonth = "1" Then%> selected <%End If%> value="1">
							Jan</option>
                            <option <%if strMonth = "2" Then%> selected <%End If%> value="2">
							Feb</option>
                            <option <%if strMonth = "3" Then%> selected <%End If%> value="3">
							Mar</option>
                            <option <%if strMonth = "4" Then%> selected <%End If%> value="4">
							Apr</option>
                            <option <%if strMonth = "5" Then%> selected <%End If%> value="5">
							May</option>
                            <option <%if strMonth = "6" Then%> selected <%End If%> value="6">
							Jun</option>
                            <option <%if strMonth = "7" Then%> selected <%End If%> value="7">
							Jul</option>
                            <option <%if strMonth = "8" Then%> selected <%End If%> value="8">
							Aug</option>
                            <option <%if strMonth = "9" Then%> selected <%End If%> value="9">
							Sept</option>
                            <option <%if strMonth = "10" Then%> selected <%End If%> value="10">
							Oct</option>
                            <option <%if strMonth = "11" Then%> selected <%End If%> value="11">
							Nov</option>
                            <option <%if strMonth = "12" Then%> selected <%End If%> value="12">
							Dec</option>

                            </select></TD>
   
    </TR>
  <TR>
    <TD class="style29">Global Resource</TD>
    <TD  class="style30">
   <font face="Arial">
	<select name="Resource" size="1" tabindex="3">
       <option value = "">--- Select---</option>
   
  <% 
 
    strsql = "SELECT Emp_Name from tbl_GPS_Resource order by Emp_Name"
  
     Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
while not MyConn.eof %>
 
<option <% if strResource = MyConn("Emp_Name") then %> selected <% end if %> value="<%=MyConn("Emp_Name") %>"><%= MyConn("Emp_Name") %></option>
 
<% MyConn.movenext
wend
MyConn.close %>
     </select>      </font></TD>
    <TD class="style29">
  &nbsp;</TD>
     <TD class="style29" >Regional Lead</TD>
     <TD   class="style30" style="width: 186px">
     <font size="2" face="Arial"> <strong><span class="style10">&nbsp;</span><font face="Arial"><span class="style12"><span class="style13"><input type="text" name="Lead"   value="<%=strLead%>" tabindex="4">
    	</span></span></font><span class="style12"><span class="style13">&nbsp;&nbsp; &nbsp;</span></span></strong></font></TD>
       
     <TD   class="style29">
     Primary Purpose</TD>
       
     <TD   class="style30">
     <font size="2" face="Arial"> <strong><span class="style12">		<span class="style13">
		&nbsp;<input type="text" name="Purpose" id="txtPrimaryGoal" value="<%=strPurpose%>" tabindex="6"></span></span></strong></font></TD>
       
     <TD   class="style29">
     &nbsp;Starting Month</TD>
       
 <TD   class="style29"><% strviewyear = right(intyear, 2) %>
&nbsp;<select name="StartingMonth">
                            <option <%if strStartingMonth = "" Then%> selected <%End If%> value="">
							&nbsp;</option>
                            <option <%if strStartingMonth = "1" Then%> selected <%End If%> value="1">
							Jan <%=strviewyear%></option>
                            <option <%if strStartingMonth = "2" Then%> selected <%End If%> value="2">
							Feb <%=strviewyear%></option>
                            <option <%if strStartingMonth = "3" Then%> selected <%End If%> value="3">
							Mar <%=strviewyear%></option>
                            <option <%if strStartingMonth = "4" Then%> selected <%End If%> value="4">
							Apr <%=strviewyear%></option>
                            <option <%if strStartingMonth = "5" Then%> selected <%End If%> value="5">
							May <%=strviewyear%></option>
                            <option <%if strStartingMonth = "6" Then%> selected <%End If%> value="6">
							Jun <%=strviewyear%></option>
                            <option <%if strStartingMonth = "7" Then%> selected <%End If%> value="7">
							Jul <%=strviewyear%></option>
                            <option <%if strStartingMonth = "8" Then%> selected <%End If%> value="8">
							Aug <%=strviewyear%></option>
                            <option <%if strStartingMonth = "9" Then%> selected <%End If%> value="9">
							Sept <%=strviewyear%></option>
                            <option <%if strStartingMonth = "10" Then%> selected <%End If%> value="10">
							Oct <%=strviewyear%></option>
                            <option <%if strStartingMonth = "11" Then%> selected <%End If%> value="11">
							Nov <%=strviewyear%></option>
                            <option <%if strStartingMonth = "12" Then%> selected <%End If%> value="12">
							Dec <%=strviewyear%></option>

                            </select></TD>
       
     <TD class="style28">
     &nbsp;<font face="Arial"><a href="View_CalendarByMonth.asp">View By Month</a></font></TD>
       
     <TD class="style28">
     <input type="submit"  value="Search"></TD>
       
      </TR>
      
  </TABLE></form>




    <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class="tablecolor1" border=1>     
	<%  strsql = "Select tbl_GPS_Calendar.* from tbl_GPS_Calendar where ID > 0 "       
       if len(strRegion) > 1 then
          strsql = strsql & " and Region = '" & strRegion & "'"
       end if
      
      if strMill > 0 then
        strsql = strsql & " and Mill_ID = " & strMill & ""
      end if
      
      if len(strProgram) > 1 then
        strsql = strsql & " and Program = '" & strProgram & "'"
      end if
      
      if len(strMonth) > 0 then
        strsql = strsql & " and datepart(month, start_week) <= " & strmonth & " and datepart(month, end_week) >= " & strmonth & " "
      elseif len(strStartingMonth) > 0 then
        strsql = strsql & " and (datepart(month, start_week) >= " & strstartingmonth & " or datepart(month, end_week) >= " & strstartingmonth & ") "
      end if

       if strResource <> "" then
strResource = "%" & strResource & "%"
strsql = strsql & " and Global_Resource Like '" & strResource & "'"
end if

       if strMachine <> "" then
strMachine = "%" & strMachine & "%"
strsql = strsql & " and Machine Like '" & strMachine & "'"
end if

       if strLead <> "" then
strLead = "%" & strLead & "%"
strsql = strsql & " and Regional_Lead Like '" & strLead & "'"
end if

       if strPurpose <> "" then
strPurpose = "%" & strPurpose & "%"
strsql = strsql & " and Purpose Like '" & strPurpose & "'"
end if

if Request.querystring("s") = "Mill" then
  strsql = strsql &  " order by Mill_ID"
elseif Request.querystring("s") = "Program" then
  strsql = strsql &  " order by Program"
elseif Request.querystring("s") = "Resource" then
  strsql = strsql &  " order by Global_Resource"
else 
  strsql = strsql &  " order by Start_week"
end if

    'Response.write strsql   
   	Set MyRec = Server.CreateObject("ADODB.Recordset")
	MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
	If NOT MyRec.EOF Then
	   
	end If
	
	dim aryDates(52)
	dim aryDatesWeekNum(52)
 If Len(Session("Month")) > 0 Then
       strMonth = CInt(Session("Month"))
    Else
	   strMonth = Month(Now())
	End If
	
 If Len(Session("Month")) > 0 Then
       strStartMonth = CInt(Session("Month"))
    Elseif len(strStartingMonth) > 0 then
	   strStartMonth = CInt(strStartingMonth)
	else
	   strStartMonth = 1
	End If
	
	'response.write("<br>StartMonth:" & strStartMonth & "<br>")

	If Weekday(CDate(strStartMonth & "/1/" & intYear)) = 2 Then
	   startdate = CDate(strStartMonth & "/1/" & intYear)
	ElseIf Weekday(CDate(strStartMonth & "/2/" & intYear)) = 2 Then
	   startdate = CDate(strStartMonth & "/2/" & intYear)
	ElseIf Weekday(CDate(strStartMonth & "/3/" & intYear)) = 2 Then
	   startdate = CDate(strStartMonth & "/3/" & intYear)
	ElseIf Weekday(CDate(strStartMonth & "/4/" & intYear)) = 2 Then
	   startdate = CDate(strStartMonth & "/4/" & intYear)
	ElseIf Weekday(CDate(strStartMonth & "/5/" & intYear)) = 2 Then
	   startdate = CDate(strStartMonth & "/5/" & intYear)
	ElseIf Weekday(CDate(strStartMonth & "/6/" & intYear)) = 2 Then
	   startdate = CDate(strStartMonth & "/6/" & intYear)
	ElseIf Weekday(CDate(strStartMonth & "/7/" & intYear)) = 2 Then
	   startdate = CDate(strStartMonth & "/7/" & intYear)
	End If

	'If Weekday(CDate("1/1/" & intYear)) = 2 Then
	'   startdate = CDate("1/1/" & intYear)
	'ElseIf Weekday(CDate("1/2/" & intYear)) = 2 Then
	'   startdate = CDate("1/2/" & intYear)
	'ElseIf Weekday(CDate("1/3/" & intYear)) = 2 Then
	'   startdate = CDate("1/3/" & intYear)
	'ElseIf Weekday(CDate("1/4/" & intYear)) = 2 Then
	'   startdate = CDate("1/4/" & intYear)
	'ElseIf Weekday(CDate("1/5/" & intYear)) = 2 Then
	'   startdate = CDate("1/5/" & intYear)
	'ElseIf Weekday(CDate("1/6/" & intYear)) = 2 Then
	'   startdate = CDate("1/6/" & intYear)
	'ElseIf Weekday(CDate("1/7/" & intYear)) = 2 Then
	'   startdate = CDate("1/7/" & intYear)
	'End If

	'Response.Write startdate
 
	

	'startdate = "1/2/2017"
	dim Mondays(13)
	Mondays(1) = 0
	Mondays(2) = 0
    Mondays(3) = 0
    Mondays(4) = 0
    Mondays(5) = 0
    Mondays(6) = 0
    Mondays(7) = 0
    Mondays(8) = 0
    Mondays(9) = 0
    Mondays(10) = 0
    Mondays(11) = 0
    Mondays(12) = 0

	dim Mondays2(13)
	Mondays2(1) = 0
	Mondays2(2) = 0
    Mondays2(3) = 0
    Mondays2(4) = 0
    Mondays2(5) = 0
    Mondays2(6) = 0
    Mondays2(7) = 0
    Mondays2(8) = 0
    Mondays2(9) = 0
    Mondays2(10) = 0
    Mondays2(11) = 0
    Mondays2(12) = 0

    intMonth = Month(DateAdd("d",7 * i,startdate))
    'response.write("Month: " & intmonth & "<br>")
    z = 0
    
        if len(Session("month")) > 0 then
      'response.write("MONTH")
	  'response.write("Start Date: " & startdate & "<br>")
      strstarti2 = 0
      if Session("month") <> 12 then
      strnextmonth = Session("month") + 1 & "/1/" & intYear
      else
      strnextmonth = "1/1/" & intYear + 1
      end if
      'response.write("StrNextMonth: " & strnextmonth & "<br>")
      strmonthenddate = DateAdd ("d", -1, strnextmonth)
      'response.write("strmonthenddate: " & strmonthenddate & "<br>")
      strnumberweeks = datediff("d", startdate, strmonthenddate) / 7
      'response.write("beginning week: " & DatePart("ww", startdate) & "<br>")
      'response.write("ending week: " & DatePart("ww", strmonthenddate) & "<br>")
      strendi2 = strnumberweeks
      'response.write("<br>Number of weeks: " & strendi2 & "<br>")
    elseif len(strstartingmonth) > 0 then
      'response.write("STARTING MONTH")
      strstarti2 = 0
      strnumberweeks = 51
      strendi2 = strnumberweeks
    else
      'response.write("NONE")
      strstarti2 = 0
      strendi2 = 52
    end if

    
    
	For i = strstarti2 to strendi2
		strTrue = "False"
	    If intMonth <> Month(DateAdd("d",7 * i,startdate)) Then
	       strTrue = "True"
	       z = 0
	       intMonth = Month(DateAdd("d",7 * i,startdate))
	    End If  
	    if (len(Session("Month")) > 0 and strTrue <> "True") or len(Session("Month")) = 0 then     
	      z = z + 1
	      if Year(DateAdd("d",7 * i,startdate)) > intyear then
	        Mondays2(Month(DateAdd("d",7 * i,startdate))) = Mondays2(Month(DateAdd("d",7 * i,startdate))) + 1
	        'response.write(Mondays(Month(DateAdd("d",7 * i,startdate))))
	        aryDates(i) = DateAdd("d",7 * i,startdate)
	        'response.write("<br>AryDates: " & DateAdd("d",7 * i,startdate) & "<br>")
	        aryDatesWeekNum(i) = z
	        'response.write("<br>AryDatesWeek: " & z & "<br>")
	      else
	        Mondays(Month(DateAdd("d",7 * i,startdate))) = Mondays(Month(DateAdd("d",7 * i,startdate))) + 1
	        'response.write(Mondays(Month(DateAdd("d",7 * i,startdate))))
	        aryDates(i) = DateAdd("d",7 * i,startdate)
	        'response.write("<br>AryDates: " & DateAdd("d",7 * i,startdate) & "<br>")
	        aryDatesWeekNum(i) = z
	        'response.write("<br>AryDatesWeek: " & z & "<br>")
	      end if
	    end if
 	Next
	%>
	<table border="1" id="tableone">
	<tr class="style6">
	<td id="milltd"><a href="View_Calendar.asp?s=Mill">Mill</a></td>
	<td id="machinetd">Machine</td>
		<td id="programtd"><a href="View_Calendar.asp?s=Program">Program</a></td>
	<td id="globalresourcetd"><a href="View_Calendar.asp?s=Resource">Global Resource</a></td>
		<td id="regionalleadtd">Regional Lead</td>
		<td id="activitytd">Activity</td>
			<td id="purposetd">Purpose</td>

	<%
    'startdate = "1/2/2017"
    if len(Session("month")) > 0 then
      'response.write("MONTH")
      strstarti = Session("month")
      strendi = Session("month")
    elseif len(strstartingmonth) > 0 then
      'response.write("STARTING MONTH")
      strstarti = strstartingmonth
      strendi = 11 + strstartingmonth
    else
      'response.write("NONE")
      strstarti = 1
      strendi = 12
    end if

	'response.write(Mondays(12))
	

	For i = strstarti to strendi
	stryear = intyear
	strmonthi = i
	strmon = 0
	
	if i > 12 then 
	  strmonthi = i - 12
	  stryear = intyear + 1
	  strmon = 1
	end if

      if strmon = 1 then	
	  If ( strmonthi mod 2) = 0 Then
	%>
	<td class="style26" colspan="<%=Mondays2(strmonthi)%>"><%=MonthName(strmonthi)%>&nbsp;<%=right(stryear,2)%></td>
	<%
	Else%>
	<td class="style27" colspan="<%=Mondays2(strmonthi)%>"><%=MonthName(strmonthi)%>&nbsp;<%=right(stryear,2)%></td>
	<%End If
      else
	  If ( strmonthi mod 2) = 0 Then
	%>
	<td class="style26" colspan="<%=Mondays(strmonthi)%>"><%=MonthName(strmonthi)%>&nbsp;<%=right(stryear,2)%></td>
	<%
	Else%>
	<td class="style27" colspan="<%=Mondays(strmonthi)%>"><%=MonthName(strmonthi)%>&nbsp;<%=right(stryear,2)%></td>
	<%End If
      end if
	Next
	%>
	<td><a href="View_Calendar.asp">Clear Sort</a></td>
	</tr>
	<tr class="style6"><td colspan="7"><b>&nbsp;</b></td>
		<%		
		
	For i = strstarti2 to strendi2 %>
	<%  %>
	<%If Len(Day(DateAdd("d",7 * i,startdate))) = 1 Then%>
	<td width="12px" align="right"><%=Day(DateAdd("d",7 * i,startdate))%></td>
	<%Else%>
	<td width="12px" align="right"><%=Day(DateAdd("d",7 * i,startdate))%></td>
	<%End If%>
	<%
	Next
	%>
		<td id="commentstd">Comments</td>

	</tr>
</table>
<div id="wrapper" class="wrapper" style="border-bottom:thin black solid;">
	<table border="1" id="tabletwo" style="margin-top:-12px;">
	<tr class="style6" style="visibility:hidden;line-height: 0;">
	<td id="milltd"><a href="View_Calendar.asp?s=Mill">Mill</a></td>
	<td id="machinetd">Machine</td>
		<td id="programtd"><a href="View_Calendar.asp?s=Program">Program</a></td>
	<td id="globalresourcetd"><a href="View_Calendar.asp?s=Resource">Global Resource</a></td>
		<td id="regionalleadtd">Regional Lead</td>
		<td id="activitytd">Activity</td>
			<td id="purposetd">Purpose</td>

	<%
    'startdate = "1/2/2017"
    if len(Session("month")) > 0 then
      'response.write("MONTH")
      strstarti = Session("month")
      strendi = Session("month")
    elseif len(strstartingmonth) > 0 then
      'response.write("STARTING MONTH")
      strstarti = strstartingmonth
      strendi = 11 + strstartingmonth
    else
      'response.write("NONE")
      strstarti = 1
      strendi = 12
    end if

	'response.write(Mondays(12))
	

	For i = strstarti to strendi
	stryear = intyear
	strmonthi = i
	strmon = 0
	
	if i > 12 then 
	  strmonthi = i - 12
	  stryear = intyear + 1
	  strmon = 1
	end if

      if strmon = 1 then	
	  If ( strmonthi mod 2) = 0 Then
	%>
	<td class="style26" colspan="<%=Mondays2(strmonthi)%>"><%=MonthName(strmonthi)%>&nbsp;<%=right(stryear,2)%></td>
	<%
	Else%>
	<td class="style27" colspan="<%=Mondays2(strmonthi)%>"><%=MonthName(strmonthi)%>&nbsp;<%=right(stryear,2)%></td>
	<%End If
      else
	  If ( strmonthi mod 2) = 0 Then
	%>
	<td class="style26" colspan="<%=Mondays(strmonthi)%>"><%=MonthName(strmonthi)%>&nbsp;<%=right(stryear,2)%></td>
	<%
	Else%>
	<td class="style27" colspan="<%=Mondays(strmonthi)%>"><%=MonthName(strmonthi)%>&nbsp;<%=right(stryear,2)%></td>
	<%End If
      end if
	Next
	%>
	<td><a href="View_Calendar.asp">Clear Sort</a></td>
	</tr>
	<tr class="style6" style="visibility: hidden;height:0;line-height: 0;padding: 0; margin: 0;border:none;border-collapse:collapse;"><td colspan="7"><b>&nbsp;</b></td>
		<%		
		
	For i = strstarti2 to strendi2 %>
	<%  %>
	<%If Len(Day(DateAdd("d",7 * i,startdate))) = 1 Then%>
	<td width="12px" align="right"><%=Day(DateAdd("d",7 * i,startdate))%></td>
	<%Else%>
	<td width="12px" align="right"><%=Day(DateAdd("d",7 * i,startdate))%></td>
	<%End If%>
	<%
	Next
	%>
		<td id="commentstd">Comments</td>

	</tr>

<%  ii = 0
	While not MyRec.eof
	   if ( ii mod 2) = 0 Then 
	      strbgcolor = "blue"
	   else
	      strbgcolor = "green"
	   
	   End If  
	   strid = MyRec("ID")
	   
	   strMill_ID = MyRec("Mill_ID")
	   
	   if len(strMill_ID) > 0 then 
	   strsql = "Select MIll from tblMill_KC where ID = " & strMill_ID & ""
 Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
if not MyConn.eof then
txtMillName = MyConn("Mill")
end if
MyConn.close
else
txtMillName = ""
end if 
%>
	
      <tr class="style6">
      <% if len(txtMillName) > 2 then %>
      <td id="milltd1"><a href="Edit.asp?id=<%= strid %>&s=<%=Request.querystring("s")%>"><%= txtMillName%></a>&nbsp;</td>
      <% else %>
            <td id="milltd1"><a href="Edit.asp?id=<%= strid %>&s=<%=Request.querystring("s")%>">Edit</a>&nbsp;</td>

      <% end if %>
      <td id="machinetd1"><%=MyRec("Machine")%>&nbsp;</td>
          <td id="programtd1"><%=MyRec("Program")%>&nbsp;</td>
      <td id="globalresourcetd1"><%=MyRec("Global_Resource")%>&nbsp;</td>
           <td id="regionalleadtd1"><%=MyRec("Regional_lead")%>&nbsp;</td>

      <td id="activitytd1"><%=MyRec("Activity")%></td>
            <td id="purposetd1"><%=MyRec("Purpose")%>&nbsp;</td>
          

		<%
	'Response.write MyRec("Color_code") & " - " & CDate(MyRec("Start_Week")) & " - " & CDate(MyRec("End_Week")) & "<BR>"
	For i = strstarti2 to strendi2
	    If MyRec("Color_code") = "OSS" Then
	      'strbgcolor2 = strbgcolor
	      strbgcolor2 = "green"
	    ElseIf MyRec("Color_Code") = "OSR" Then
	      ' if ( i mod 2) = 0 Then  
	      '    strbgcolor2 = "white"
	      ' else
	          'strbgcolor2 = strbgcolor
	          strbgcolor2 = "red"
	      ' end If
	    ElseIf MyRec("Color_Code") = "RS" Then
	      ' if ( i mod 2) = 0 Then  
	      '    strbgcolor2 = "white"
	      ' else
	          'strbgcolor2 = strbgcolor
	          strbgcolor2 = "yellow"
	       'end If	 
	    ElseIf MyRec("Color_Code") = "V" Then
	       'if ( i mod 2) = 0 Then  
	       '   strbgcolor2 = "white"
	       'else
	          'strbgcolor2 = strbgcolor
	          strbgcolor2 = "blue"
	       'end If		        '  If (aryDatesWeekNum(i) * 7 - 6) <= Day(MyRec("Due_Date"))   AND Day(MyRec("Due_Date")) <= (aryDatesWeekNum(i) * 7) Then
	            ' strbgcolor2 = strbgcolor
	          '  strbgcolor2 = "white"
	            
	        ' Else
	          '   strbgcolor2 = "white"
	         'End If
   
	    
	    End If
		'If MyRec("Color_Code") = "V" Then
		'Response.Write MyRec("Color_Code") & " - " & DateAdd("d",7 * i,startdate) & " - " &  MyRec("Start_Week") & " - " &  MyRec("End_Week") & "<BR>"
		'Response.Write MyRec("Color_Code") & " - " & CDate(MyRec("Start_Week")) & " <= " &  DateAdd("d",7 * i,startdate) & " And " & DateAdd("d",7 * i,startdate) & " <= "  & MyRec("End_Week") & "<BR>"

		If (CDate(MyRec("Start_Week")) <= DateAdd("d",(7 * i) + 0,startdate) And DateAdd("d",(7 * i) + 0,startdate) <= CDate(MyRec("End_Week"))) Or _
		   (CDate(MyRec("Start_Week")) <= DateAdd("d",(7 * i) + 1,startdate) And DateAdd("d",(7 * i) + 1,startdate) <= CDate(MyRec("End_Week"))) Or _
		   (CDate(MyRec("Start_Week")) <= DateAdd("d",(7 * i) + 2,startdate) And DateAdd("d",(7 * i) + 2,startdate) <= CDate(MyRec("End_Week"))) Or _
		   (CDate(MyRec("Start_Week")) <= DateAdd("d",(7 * i) + 3,startdate) And DateAdd("d",(7 * i) + 3,startdate) <= CDate(MyRec("End_Week"))) Or _
		   (CDate(MyRec("Start_Week")) <= DateAdd("d",(7 * i) + 4,startdate) And DateAdd("d",(7 * i) + 4,startdate) <= CDate(MyRec("End_Week"))) Or _
		   (CDate(MyRec("Start_Week")) <= DateAdd("d",(7 * i) + 5,startdate) And DateAdd("d",(7 * i) + 5,startdate) <= CDate(MyRec("End_Week"))) Or _
		   (CDate(MyRec("Start_Week")) <= DateAdd("d",(7 * i) + 6,startdate) And DateAdd("d",(7 * i) + 6,startdate) <= CDate(MyRec("End_Week"))) THEN
		
		'If DateAdd("d",7 * i,startdate) >= CDate(MyRec("Start_Week")) And DateAdd("d",7 * i,startdate) <= CDate(MyRec("End_Week")) Then
		   'Response.Write "Found<BR>"
        Else
           strbgcolor2 =  "white"
		End If
		'End If
		%>
	<%
	if strbgcolor2 = "white" Then%>
	<td bgcolor="<%=strbgcolor2%>" width="12px">&nbsp;</td>
	<%else%>
	<td title="<%=aryDates(i)%> - <%=MyRec("Activity")%>" bgcolor="<%=strbgcolor2%>" width="12px">
	&nbsp;</td>
	
	
	<%end If%>
	<%
	Next
	%>
	         <td id="commentstd1" style="min-width:54px;"><div style="min-width:54px"><%=MyRec("Comments")%></div>&nbsp;</td>
	</tr>
	   
	  	
 
 
    <%   
     
       ii = ii + 1

       MyRec.MoveNext
     Wend
     MyRec.close
    %>

</table>  
</div><br>

<table> 
 
 <script language="javascript">
 
/*
//alert("Milltd:" + document.getElementById("milltd").offsetWidth + "Milltd1:" + document.getElementById("milltd1").offsetWidth);
if (document.getElementById("milltd").offsetWidth < document.getElementById("milltd1").offsetWidth) {
  document.getElementById("milltd").style.width = document.getElementById("milltd1").offsetWidth -4 ; }
else {
  document.getElementById("milltd1").style.width = document.getElementById("milltd").offsetWidth -4; };
//alert("New width:" + document.getElementById("milltd").offsetWidth + "  Old Width: " + document.getElementById("milltd1").offsetWidth);

if (document.getElementById("machinetd").offsetWidth < document.getElementById("machinetd1").offsetWidth) {
  document.getElementById("machinetd").style.width = document.getElementById("machinetd1").offsetWidth -4 ; }
else {
  document.getElementById("machinetd1").style.width = document.getElementById("machinetd").offsetWidth -4; };
alert("New width:" + document.getElementById("machinetd").offsetWidth + "  Old Width: " + document.getElementById("machinetd1").offsetWidth);

if (document.getElementById("programtd").offsetWidth < document.getElementById("programtd1").offsetWidth) {
  document.getElementById("programtd").style.width = document.getElementById("programtd1").offsetWidth -4 ; }
else {
  document.getElementById("programtd1").style.width = document.getElementById("programtd").offsetWidth -4; };
alert("New width:" + document.getElementById("programtd").offsetWidth + "  Old Width: " + document.getElementById("programtd1").offsetWidth);

if (document.getElementById("globalresourcetd").offsetWidth < document.getElementById("globalresourcetd1").offsetWidth) {
  document.getElementById("globalresourcetd").style.width = document.getElementById("globalresourcetd1").offsetWidth -4 ; }
else {
  document.getElementById("globalresourcetd1").style.width = document.getElementById("globalresourcetd").offsetWidth -4; };
alert("New width:" + document.getElementById("globalresourcetd").offsetWidth + "  Old Width: " + document.getElementById("globalresourcetd1").offsetWidth);

if (document.getElementById("regionalleadtd").offsetWidth < document.getElementById("regionalleadtd1").offsetWidth) {
  document.getElementById("regionalleadtd").style.width = document.getElementById("regionalleadtd1").offsetWidth -4 ; }
else {
  document.getElementById("regionalleadtd1").style.width = document.getElementById("regionalleadtd").offsetWidth -4; };
alert("New width:" + document.getElementById("regionalleadtd").offsetWidth + "  Old Width: " + document.getElementById("regionalleadtd1").offsetWidth);

if (document.getElementById("activitytd").offsetWidth < document.getElementById("activitytd1").offsetWidth) {
  document.getElementById("activitytd").style.width = document.getElementById("activitytd1").offsetWidth -4 ; }
else {
  document.getElementById("activitytd1").style.width = document.getElementById("activitytd").offsetWidth -4; };
alert("New width:" + document.getElementById("activitytd").offsetWidth + "  Old Width: " + document.getElementById("activitytd1").offsetWidth);
*/
if (document.getElementById("purposetd").offsetWidth < document.getElementById("purposetd1").offsetWidth) {
  document.getElementById("purposetd").style.width = document.getElementById("purposetd1").offsetWidth -4 ; }
else {
  document.getElementById("purposetd1").style.width = document.getElementById("purposetd").offsetWidth -4; };
//alert("New width:" + document.getElementById("purposetd").offsetWidth + "  Old Width: " + document.getElementById("purposetd1").offsetWidth);

if (document.getElementById("commentstd").offsetWidth < document.getElementById("commentstd1").offsetWidth) {
  document.getElementById("commentstd").style.width = document.getElementById("commentstd1").offsetWidth -4 ; }
else {
  document.getElementById("commentstd1").style.width = document.getElementById("commentstd").offsetWidth -4; };
//alert("New width:" + document.getElementById("commentstd").offsetWidth + "  Old Width: " + document.getElementById("commentstd1").offsetWidth); 


//alert("Milltd:" + document.getElementById("milltd").offsetWidth + "Milltd1:" + document.getElementById("milltd1").offsetWidth);
//alert("Machine: " + document.getElementById("machinetd1").style.width);
if (document.getElementById("machinetd1").offsetWidth < 45) {
  document.getElementById("machinetd1").style.width = 45; };
  document.getElementById("tableone").style.width = document.getElementById("tabletwo").offsetWidth ; 
  document.getElementById("milltd").style.width = document.getElementById("milltd1").offsetWidth -4 ; 
  document.getElementById("machinetd").style.width = document.getElementById("machinetd1").offsetWidth -4 ; 
  document.getElementById("programtd").style.width = document.getElementById("programtd1").offsetWidth -4 ; 
  document.getElementById("globalresourcetd").style.width = document.getElementById("globalresourcetd1").offsetWidth -4 ; 
  document.getElementById("regionalleadtd").style.width = document.getElementById("regionalleadtd1").offsetWidth -4 ; 
  document.getElementById("activitytd").style.width = document.getElementById("activitytd1").offsetWidth -4 ; 
  //document.getElementById("purposetd").style.width = eval(document.getElementById("purposetd1").offsetWidth - 5) ; 
//alert("Mill:" + document.getElementById("milltd").offsetWidth + "<br>Machine:" + document.getElementById("machinetd").offsetWidth + "<br>Program:" + document.getElementById("programtd").offsetWidth + "<br>Global Resource:" + document.getElementById("globalresourcetd").offsetWidth + "<br>Regional Lead:" + document.getElementById("regionalleadtd").offsetWidth + "<br>Activity:" + document.getElementById("activitytd").offsetWidth + "<br>Purpose:" + document.getElementById("purposetd").offsetWidth);
//alert("Machine: " + document.getElementById("machinetd").style.width);

</script>
