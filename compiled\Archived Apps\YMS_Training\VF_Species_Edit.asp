																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Fiber Species </TITLE>


<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 



<% Dim MyRec, strsql, MyRec2, strsql2,  MyConn3, strsql3,  strSchedule_Agreement

strdate = formatdatetime(Now(),2)

  set objGeneral = new ASP_CLS_General
  strid = Request.querystring("id")
  
  strsql = "Select * from tblVFSpecies where ID = " & strid
  
      Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

  

  if objGeneral.IsSubmit() Then	
  Dim strSpecies,  strBT_Factor, strUOM
  
  	strSpecies = request.form("Species")
  	

  	
  	If len(request.form("SAP")) > 0 then
  	strSAP = request.form("SAP")
  	else
  	strSAP = ""
  	end if 
  	
  	If len(request.form("UOM")) > 0 then
  	strUOM = request.form("UOM")
  	else
  	strUOM = 0
  	end if
  	
  
  		
  	If len(request.form("Schedule_Agreement")) > 0 then
  	strSchedule_Agreement= request.form("Schedule_Agreement")
  	else
  	strSchedule_Agreement = ""
  	end if
 
  	
  If len(request.form("BT_Factor")) > 1 then	
 strBT_Factor = Request.form("BT_Factor")
 else
 strBT_Factor = ""
 end if 
 
        	
  	
	strsql =  "Update tblVFSpecies  set Grade = '" & request.form("Grade") & "', Fiber_Species = '" & strSpecies & "',  SAP = '" & strSAP & "', UOM = '" & strUOM & "', "_
	&" Schedule_Agreement = '" & strSchedule_Agreement & "', Other = '" & strBT_Factor & "' where ID = " & strid
	
	
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
Response.redirect("VF_Species.asp")		
end if

	
%>

<style type="text/css">
.style1 {
	font-family: arial;
	font-size: x-small;
}
.style3 {
	border-left: 1px solid #808080;
	border-top: 1px solid #808080;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style4 {
	background-color: #FFFFD7;
}
.style5 {
	border-right: 1px solid #808080;
	border-bottom: 1px solid #808080;
	text-align: center;
	border-left-style: solid;
	border-left-width: 1px;
	border-top-style: solid;
	border-top-width: 1px;
}
.style6 {
	border-right: 1px solid #808080;
	border-bottom: 1px solid #808080;
	font-family: arial;
	font-size: x-small;
		text-align: center;
	border-left-style: solid;
	border-left-width: 1px;
	border-top-style: solid;
	border-top-width: 1px;
}
.style7 {
	border-right: 1px solid #808080;
	border-bottom: 1px solid #808080;
	font-family: arial;
	font-size: xx-small;
	text-align: center;
	border-left-style: solid;
	border-left-width: 1px;
	border-top-style: solid;
	border-top-width: 1px;
}
.style10 {
	font-size: x-small;
}
.style11 {
	text-align: center;
}
</style>
</head>

<body>
<br>
	
<form name="form1" action="VF_Species_Edit.asp?id=<%= strid%>" method="post" ID="Form1">
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Edit Species </b></font></td>
<td align = right><font face="Arial"><a href="VF_Species.asp">Return</a>&nbsp;</td>


</tr>
	    </table>



	<p>&nbsp;</p>
	<table id="table1" style="width: 75%" align="center" class="style3">
<tr bgcolor = #CCCCFF>
		<td bgcolor="#FFFFD7" class="style6" style="height: 70px">

Species</td>
		<td bgcolor="#FFFFD7" class="style6" style="height: 70px">

SAP #</td>
		<td bgcolor="#FFFFD7" class="style7" style="height: 70px">

<span class="style10">U</span><span class="style1">OM</span></td>
		<td bgcolor="#FFFFD7" class="style6" style="height: 70px">

<span class="style4">Grade</span>&nbsp; <br>
(Used to determine which dropdown list to include this species on for Transfers 
from Warehouse)</td>
		<td bgcolor="#FFFFD7" style="height: 70px" class="style5">

<p align="center" class="style1">Schedule 
		Agreement</td>
		<td bgcolor="#FFFFD7" class="style6" style="height: 70px">

Bale to Ton Factor</td>
	</tr>
	<tr>
		<td class="style5"><font face = arial size = 1>
		<input type="text" name="Species" size="20" style="width: 178px" value='<%= MyRec.fields("Fiber_Species") %>'></td>
		<td class="style5"><font face = arial size = 1>
		<input type="text" name="SAP" size="20" value='<%= MyRec.fields("SAP") %>' style="width: 115px"></td>
		<td class="style5"><font face = arial size = 1>
		<input type="text" name="UOM" size="20" style="width: 43px" value='<%= MyRec.fields("UOM") %>'></td>
		<td class="style5"><font face = arial size = 1><select name="Grade">
		<option value="">--Select--</option>
		<option <% if strgrade = "BROKE" then %>selected <% end if %>>BROKE</option>
		<option <% if strgrade = "RF" then %>selected <% end if %>>RF</option>
		<option <% if strgrade = "VF" then %>selected <% end if %>>VF</option>
		</select>
		
		
		</td>
		<td class="style5">
		<p align="center" class="style11"><font face = arial size = 1>
<input type="text" name="Schedule_Agreement" size="39" value='<%= MyRec.fields("Schedule_Agreement") %>' style="width: 175px"></td>
		<td class="style5">
		<font face = arial size = 1>
<input type="text" name="BT_Factor" size="39" style="width: 108px" value='<%= MyRec.fields("Other") %>'></td>
	</tr>
</table>
<br>
		

<p align="center"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></p>
</form>
<% MyRec.close %><!--#include file="Fiberfooter.inc"-->