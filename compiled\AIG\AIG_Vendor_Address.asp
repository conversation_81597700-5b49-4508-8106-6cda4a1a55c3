																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>AIG Vendors</TITLE>
<!--#include file="classes/asp_cls_DataAccessAIG.asp"-->
<!--#include file="classes/asp_cls_headerAIG.asp"-->
<!--#include file="classes/asp_cls_SessionStringAIG.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->




<% Dim MyRec, strsql, MyConn, strUserType




strsql = "SELECT tblAIGUserType.* FROM tblAIGUserType where  BID = '" & Session("EmployeeID") & "' and User_type = 'V'" 

    Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
    If not MyConn.eof then
    struserType = MyConn.fields("User_type")
    else
   
    strUserType = ""
    end if
    If strUserType = "" and Session("EmployeeID") <> "C97338"  and Session("EmployeeID") <> "U10090" and Session("EmployeeID") <> "B96138" then

    Response.write ("<br><br><font face = arial size = 3><b>You do not have authorization to view this page</b></font>")
   else
    BuildScreen()
    end if 

    %>
    <%Sub Buildscreen



strsql = "SELECT tblVendors.* FROM tblVendors order by Vendor " 
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<style type="text/css">
.style1 {
	font-family: Arial;
}
.auto-style1 {
	font-family: Arial;
	font-size: x-small;
}
</style>
</head>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>
<% if strUsertype = "D" or Session("EmployeeID") = "U10090"  or Session("EmployeeID") = "B96138" then %>
<td align = left width = 10%></td>
<% else %>
<td align = left width = 10% class="style1"><a href="AIG_Vendor_add.asp">Add New</a></td>
<% End if %>
<td align = center><b>
<font face="arial" size="4" >AIG Vendors</font></b></span></td>

<td align = center><b>
<font face="arial" size="4" >	<% if strusertype = "D" or Session("EmployeeID") = "U10090" or Session("EmployeeID") = "B96138" then %>
</font></b></td>


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1 align = center>  
	 <tr class="tableheader">
<td>&nbsp;</td>
	<td  >  <font face="Arial" size="2">Vendor</font></td>
	<td  >  <font face="Arial" size="2">Vendor Number</font></td>
		<td  >  <font face="Arial" size="2">Remit to Number</font></td>
		<td  >  <font face="Arial" size="2">Vendor Name</font></td>
        <td  > <font face="Arial" size="2">Vendor Address</font></td>
             <td  > <font face="Arial" size="2">City, State, Zip</font></td>
               <td  > <font face="Arial" size="2">Invoice Summary Email</font></td>
               <td class="auto-style1"  > <font size="2">Weekly Receipt Email</font></td>
	<td>&nbsp;</td>
		


	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    	<% if strusertype = "D" or Session("EmployeeID") = "U10090" or Session("EmployeeID") = "B96138" then %>
			<td> &nbsp;</td>	
			<% else %>
<td> <font size="2" face="Arial"><a href="AIG_Vendor_edit.asp?id=<%= MyRec.fields("ID") %>">Edit</a></td>	
<% end if %>
	<td  >      <font size="2" face="Arial">        <%= MyRec.fields("Vendor")%>&nbsp;</font></td>
      	<td  >      <font size="2" face="Arial">        <%= MyRec.fields("Vendor_nbr")%>&nbsp;</font></td>
      	  	<td  >      <font size="2" face="Arial">        <%= MyRec.fields("Payee_Nbr")%>&nbsp;</font></td>
      		<td  >      <font size="2" face="Arial">        <%= MyRec.fields("Vendor_name")%>&nbsp;</font></td>
			<td  >        <font size="2" face="Arial">        <%= MyRec.fields("Vendor_Address")%>&nbsp;</font></td>
			<td  >        <font size="2" face="Arial">        <%= MyRec.fields("Vendor_Street")%>&nbsp;</font></td>
			<td  >        <font size="2" face="Arial">        <%= MyRec.fields("Email_Address")%>&nbsp;</font></td>
				<td  >        <font size="2" face="Arial">        <%= MyRec.fields("Address")%>&nbsp;</font></td>

				<% if strusertype = "D" or Session("EmployeeID") = "U10090" or Session("EmployeeID") = "B96138" then %>
			<td> &nbsp;</td>	
			<% else %>
	<td> <font size="2" face="Arial"><a href="AIG_Delete_vendor.asp?id=<%= MyRec.fields("ID") %>">Delete</a></td>	
	<% end if %>
	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
     MyRec.close
    %>
</table>

<% End Sub %><!--#include file="AIGfooter.inc"-->