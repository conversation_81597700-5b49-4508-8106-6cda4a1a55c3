<html>
<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_Procedure.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 6.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Update Permit Status</title>
</head>
<% dim strsql, MyRec, strid, strwa, strSpaceID, strTitle, strStatus, strDate

strid = Request.querystring("id")
strwa = request.querystring("wa")
      strsql = "SELECT tblSOP.SOP_NO, tblSOP.SDescription, tblPermit.Date_issued, tblPermit.Date_expired, tblSOP.WorkArea, "_
      &" tblPermit.Permit_status, tblPermit.Status_date,  tblPermit.Safety_Date, tblPermit.PID "_
		&" FROM tblPermit INNER JOIN tblSOP ON tblPermit.Space_ID = tblSOP.SOP_NO "_
		&" WHERE tblPermit.PID = " & strid
       Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly 
					
			strTitle = MyRec.fields("SDescription")
			strSpaceID = MyRec.fields("SOP_NO")
			strstatus = MyRec.fields("Permit_status")
			MyRec.close


set objGeneral = new ASP_CLS_General
  		 if objGeneral.IsSubmit = True Then
  		strid = request.querystring("id") 
		strnow = dateadd("h", -5, now())
  		strDate = formatdatetime(strnow,2)
  		strstatus = request.form("Status")
  		strwa = request.querystring("wa")
  		
  		if strstatus = "" then
  		 strsql =  "Update tblPermit set Permit_Status = '" & strStatus & "', status_date = Null, Close_BID = Null where PID = " & strid
  		 else 
 	
  	
 strsql =  "Update tblPermit set Permit_Status = '" & strStatus & "', status_date = '" & strdate & "', Close_BID = '" & Session("EmployeeID") & "' where PID = " & strid
 end if 
 Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 


Response.redirect("Work_Area_summary.asp?id=" & strwa)
end if 


 		strSQL = "SELECT * from tblAuthorizers where P_Type = 'A'  and P_BID = '" & Session("Employeeid") & "' and work_area = '" & strwa & "'"
		        Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly	
			
			If not MyRec.eof then
 %>
 
<body><table width = 100%><tr><td align="left"><b><font face="arial">Update Permit Status:</font></b>
 </td><td align = right><font face = arial size = 3><a href="javascript:history.go(-1);"><b>Return</b></a></td></tr></table>
 <br><br>
 </p>
 <form name="form1" action="Permit_status_edit.asp?id=<%= strID%>&wa=<%= strwa%>"  method="post" ID="Form1"  >
 
 <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="75%"  align = center bgcolor="#D9E1F9">
   
  <tr>
    <td   bgcolor="#FFFFDD" >
	<p align="center"><font face="Arial" size="2">Space ID</font></td>
    <td bgcolor="#FFFFDD" align="center" width="413">
	<font face="arial" size="2">Space Name</font></td>

   
    <td bgcolor="#FFFFDD" align="center">
	<font face="Arial" size="2">Permit Status</font></td>

   
  </tr>
  <tr>
    <td align = center bgcolor="#FFFFFF" ><font face="Arial" size="2"> <%= strSpaceID%>
	&nbsp;</td>
    <td  bgcolor="#FFFFFF" width="413"  >
	<p align="center">&nbsp;<font face="Arial" size="2"><%= strTitle %></td>
	
    <td  bgcolor="#FFFFFF"  >
	<p align="center"><select size="1" name="Status">
	<option value = "">Select Status</option>
	<option <% if strstatus = "Permit Not Used" then %> selected <% end if %>>Permit Not Used</option>
	<option <% if strstatus = "Review Complete" then %> selected <% end if %>>Review Complete</option>
	</select></td>
	
  </table> 
	<p align = center>&nbsp;<INPUT TYPE="submit" value="Submit"></p>
	</form>
	
	<% else %>
	<body><table><tr><td><font face =arial size = 3><b><br><br><br>You do not have authorization to view this page.  Only Team Administrators can close out a permit.</b></b></font></td></tr></table></body>

<% end if %><!--#include file="footer.inc"-->