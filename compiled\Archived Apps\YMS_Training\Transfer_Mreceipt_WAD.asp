																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Transfer Wadding Merchants Receipt</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->




<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate, strid, strTrailer, strDateReceived, strMySpecies, strSAP, MyConn, strsql3

strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -7, strtdate)
strid = request.querystring("id")




strsql = "SELECT tblCars.* FROM tblCars WHERE CID = " & strid & "" 

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
 
strsap = MyRec("SAP_NBR")
%>
<style type="text/css">
.style1 {
	font-family: Arial;
	font-weight: bold;
}
.style2 {
	font-family: Arial, Helvetica, sans-serif;
}
</style>
</head>

<body onload="if (window.print) {window.print()}">

<p align="center"><img height="40" src="kcc40white2.gif" width="450"><br><br><br>
	
</p>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><b><font face="Arial">Material Movement Order at Kimberly-Clark 
<a href="WAD_TSF_Merchants.asp"><b>Mobile</b></a></font></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br><br><br>
	
<table width = 100%>
<Tr>	<td align="left"><b><font face="Arial">Receipt Nbr:&nbsp;<%= MyRec.fields("CID")%></font></b></td>
	<td align="right"><b><font face="Arial">Date Shipped:&nbsp;<%= MyRec.fields("Date_Received")%></font></b></td></Tr>


</table>	<br><br><br><br>

<table border="1" width="100%" id="table1" cellspacing="1" bordercolorlight="#808080" bordercolor="#000000">

	
		<tr>
		<td bordercolor="#808080" align="center"><b><font face="Arial">Ship From</font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">Ship To</font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">Ticket #</font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">Trailer/Car #</font></b></td>
		<td bordercolor="#808080" align="center"><b><font face="Arial">Carrier</font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">Quantity (lbs)</font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">Sap #</font></b></td>
		
			<td bordercolor="#808080" align="center"><b><font face="Arial">Description</font></font></b></td>
		
		<td bordercolor="#808080" align="center"><b><font face="Arial">	Species</font></b></td>
		
	</tr>
			<tr>
	
		<td align="center"><b><font face="Arial">Merchants Warehouse (WAD2)</font></b></td>
			<td align="center"><b><font face="Arial">KC (WAD1)</font></b></td>
		
			<td align="center"><b><font face="Arial"><%= MyRec("WH_TIcket") %></font></b></td>
		
		<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("Transfer_Trailer_NBR")%></font></b></td>
			<td align="center">&nbsp;<font face="Arial"><b><%= MyRec.fields("Trans_Carrier")%></b></td>
			<td align="center"><b><font face="Arial">&nbsp;<%= MyRec.fields("Net")%></font></b></td>
 
		
			<td align="center"><font face="Arial"><b><%= strSAP %></b>&nbsp;</td>
 
		
				<td align="center"><font face="Arial"><b><%= MyRec("WAD_Description") %></b>&nbsp;</td>
 
			<td align="center"><font face="Arial"><b>WADDING</b></font></td>

	</tr>
	</table>
	<p>
<br><br>

		<% if len(MyRec.fields("Other_comments")) > 0 then %>
		</p>
		<table width = 100% align = center><tr><td align = center>
	<b><font face="Arial"><%= MyRec.fields("Other_Comments")%></td></tr></table>
		<% end if %>
	&nbsp;<p align = center>&nbsp;</p>
		<div align="center">
		<table width = 80% border="1">
	<tr><td align="center">
		<p><b><font face="Arial">SAP </font></b><span class="style1">Doc ID</span></td>
		<td align="center"><b><font face="Arial">Shipment Loaded By</font></b></td>
		
		<td align="center"><b><font face="Arial">Receiver's Name</font></b></td>
		
		<td align="center"><b><font face="Arial">Initials</font></b></td>
		
	</tr>
	
	<tr>
				<td align="center"><b><font face="Arial"><%= MyRec.fields("SAP_DOC_ID") %>&nbsp;<span style="font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;
mso-fareast-font-family:Calibri;mso-fareast-theme-font:minor-latin;mso-ansi-language:
EN-US;mso-fareast-language:EN-US;mso-bidi-language:AR-SA">DO <u>NOT</u> ENTER IN 
				SAP</span></font></b></td>
		<td align="center"><font face = Arial><b>&nbsp;</td>
	
		<td align="center"><font face="Arial"><%= Session("Ename")%>&nbsp;</td>
	
		<td align="center">&nbsp;</td>
	</tr>
	
</table></div>
	</div>