																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>DC Warehouse Trucks</TITLE>
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 





<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate


strsql = "SELECT tblCars.* FROM tblCars WHERE Location='DC WHSE' and Trailer <> 'UNKNOWN' and Date_received > '9/30/09'  and OB_Time is Null order by Date_unloaded desc "

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>DC Warehouse Trucks</b></font></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1>  
	 <tr class="tableheader">
<td>&nbsp;</td>
			<td  > <p align="center">       <font face="Arial" size="1">Print<br> Receipt</font></td>
		<td  ><font face="Arial" size="1"><b>PMO #</b></font></td>
	<td  ><font face="Arial" size="1"><b>Date Transferred</b></font></td>
		<td  ><font face="Arial" size="1"><b>Trailer</b></font></td>
		<td  ><font face="Arial" size="1"><b>Carrier</b></font></td>
		<td  ><font face="Arial" size="1">Species</font></td>
    
		<td  ><font face="Arial" size="1">Vendor</font></td>
		<td  ><font face="Arial" size="1">Generator</font></td>
		<td  ><font face="Arial" size="1">City</font></td>
	<td  ><font face="Arial" size="1">PO</font></td>

		<td  >       <p align="center">       <font face="Arial" size="1">Bales</font></td>
	
        <td><p align="center">        <font face="Arial" size="1">Tons<br> Received</font></td>
		<td  >        <font size="1" face="Arial">Other</font></td>
	<td  >        <font size="1" face="Arial">Put back in Yard</font></td>
	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td> <font size="1" face="Arial"><a href="DC_Out.asp?id=<%= MyRec.fields("CID") %>">Out</a></td>
	<td align = center> <font size="1" face="Arial"><a href="Truck_receipt_print.asp?id=<%= MyRec.fields("CID") %>"><%= MyRec.fields("CID")%></a></td>

<td  ><font size="1" face="Arial"><b><%= MyRec.fields("Pnbr")%>&nbsp;</font></b></td>
<td  ><font size="1" face="Arial"><b><%= MyRec.fields("Date_unloaded")%>&nbsp;</font></b></td>

	<td  > <font size="1" face="Arial"><b><%= MyRec.fields("Trailer")%></font></b></td>
			<td  ><font size="1" face="Arial"><b><%= MyRec.fields("Carrier")%>&nbsp;</font></b></td>


		<td  ><font face="Arial" size="1"> <%= MyRec.fields("Species")%>&nbsp;</font></td>
		<td><font size="1" face="Arial"><%= MyRec.fields("Vendor")%>&nbsp;</font></td>
			<td><font size="1" face="Arial"><%= MyRec.fields("Generator")%>&nbsp;</font></td>
				<td><font size="1" face="Arial"><%= MyRec.fields("Gen_City")%>&nbsp;</font></td>
		<td  >        <font size="1" face="Arial">        <%= MyRec.fields("PO")%>&nbsp;</font></td>
	<td  >  <font size="1" face="Arial">        <%= MyRec.fields("Bales_RF")%>&nbsp;</font></td>
		<td align = right >		 <font size="1" face="Arial">        <%= MyRec.fields("Net")%>&nbsp;</font></td>
		
		<td  >	 <font size="1" face="Arial">        <%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>
<td> <font size="1" face="Arial"><a href="DC_Back_to_Yard.asp?id=<%= MyRec.fields("CID") %>">Put Back</a></td>

	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>


<!--#include file="Fiberfooter.inc"-->