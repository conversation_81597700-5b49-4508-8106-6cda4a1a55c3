																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>OCC Yard Inventory Report</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->



<% Dim MyRec, strsql, strDays, strDate, gSpecies, gCount, gTCount, strsql3, MyRec3, gTotalweight, MyRec2, strRelease

	strdate = formatdatetime(now(),2)
gcount = 0
gSpecies = ""
gTotalweight = 0
 


If Session("EmployeeID") = "B55548" or Session("EmployeeID") = "C97338" or Session("EmployeeID") = "B53909"  then 
strAdmin = "YES"
end if

 
 %>

<style type="text/css">
.style1 {
	font-family: Arial;
	font-size: x-small;
}
.style2 {
	font-family: Arial;
	font-weight: bold;
}
.style5 {
	font-weight: bold;
	font-size: x-small;
	text-align: left;
}
.style9 {
	font-size: x-small;
}
.style10 {
	font-weight: bold;
	font-size: x-small;
}
.style11 {
	font-weight: bold;
}
.style12 {
	font-family: Arial, Helvetica, sans-serif;
}
.style13 {
	text-align: center;
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style14 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-size: x-small;
	text-align: center;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style15 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-size: x-small;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style16 {
	border: 1px solid #000000;
}
</style>
</head>

<body onload="if (window.print) {window.print()}">

<br>
	
<%	strsql = "SELECT tblCars.*,  [date_received] AS Sort_Date FROM tblCars WHERE (((tblCars.Date_received) Is Not Null) "_
&"  AND ((tblCars.Location)='Yard') AND ((tblCars.Trailer) Is Not Null))  and (Species='OCC' or Grade = 'OCC'  OR Species = 'MXP'  OR Species = 'USBS' or Species = 'SWL' ) "_
&"  ORDER BY tblCars.Species, Sort_Date "   
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


%>

<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=0>

 <TD align = left><font size="2" face = arial>&nbsp;<a href="YardReportOCCPF.asp" target="_blank">Printer Friendly</a></font></td>
<td align = center><b>
<font face="arial" size="4" >OCC Yard Inventory Report for <%= strDate%></font></b></span></td>
<td align = center>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;</td>


</tr>
	    </table>
<p align="right"><font face="Arial">Yard Physically Checked by 
_______________________<br>Signature&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </font>&nbsp; </p>

	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "style9" border=0>  
	 <tr class="tableheader">


		
		<td align = center  ><font face="Arial" size="2" class="style9"><b>Sent to the<br> Warehouse</td>


		<td align = center  ><font face="Arial" size="2" class="style9"><b>Date <br>Received</b></b></font></td>
    	<td class="style5" ><font face="Arial">Trailer</font></td>
		<td class="style10" ><font face="Arial">Carrier</font></td>
		<td class="style10" ><font face="Arial">Species</td>
			<td class="style10" ><font face="Arial">Vendor</font></td>
	<td class="style5"><span class="style2">Receipt #</span><font face="Arial"><span class="style11">
	</span></font></td>
		<td class="style5"><span class="style2">Release #</span><font face="Arial"><span class="style11">
	</span></font></td>

<td align = left class="style1"><strong>Weight</strong></td>


<td align=center><font face="Arial" size="1" class="style10">To Date<br>Detention</font></td>
<td align="center" ><font face="Arial" size="1" class="style10">Days <br>in Yard</font></td>

<td align="center" class="style10" ><font face="Arial">Type</font></td>
<td align="center" class="style10" ><font face="Arial">Comments</font></td>
<td align = center class="style10" ><font face="Arial">Checkoff</font></td>
	</tr>

 <% Dim ii
       ii = 0
             strMT = 0
       strET = 0   
    

       while not MyRec.Eof
       if strAdmin = "YES" then
    
           strRelease = MyRec.fields("Release_nbr")
                  strweight = ""
       strMetric = ""

    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
        
        <% if isnull(MyRec.fields("Transfer_date")) then %>
      
	<td align = center class="style12" ><font face="Arial">	<%= MyRec("Commodity") %>	</td>
      
	<td align = center ><font face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align = left > <font size="2" face="Arial" class="style9"> &nbsp; <%= MyRec.fields("Trailer")%>&nbsp; </font></td>
	<td  >   <font size="2" face="Arial">  <span class="style9">  <%= MyRec.fields("Carrier")%></span>&nbsp;</font></td>
	<td  >   <font size="2" face="Arial">  <span class="style9">  <%= MyRec.fields("Species")%></span>&nbsp;</font></td>
		<td  >   <font size="2" face="Arial">  <span class="style9">  <%= MyRec.fields("Vendor")%></span>&nbsp;</font></td>

	 <td  >  <font size="2" face="Arial"><span class="style9"><%= MyRec.fields("REC_number")%></span>&nbsp;</font></td>
	 
	 <td  >  <font size="2" face="Arial"><span class="style9"><%= MyRec.fields("Release_nbr")%></span>&nbsp;</font></td>

        <% else %>
     		<td align = center ><a href="Send_WH.aso?id=<%= MyREc("CID") %>">N</a></td>

		<td align = center ><font face="Arial"><%= MyRec.fields("Transfer_Date")%></font></td>
	
			<td align = left  > <font size="2" face="Arial" class="style9">&nbsp; <%= MyRec.fields("Transfer_trailer_nbr")%></font></td>
		<td  >   <font size="2" face="Arial"> <span class="style9"> <%= MyRec.fields("Trans_Carrier")%></span>&nbsp;</font></td>
				<td  >   <font face="Arial"> &nbsp;</font></td>

		   <td  >  <font size="2" face="Arial">        <span class="style9">        <%= MyRec.fields("PMO_nbr")%></span>&nbsp;</font></td>
		   	   <td  >  <font face="Arial">  &nbsp;</font></td>
		<% end if %>
		

		
 
            <td  >  <font size="2" face="Arial">   
          	<span class="style9">   
          <% 
          If MyRec("Carrier") = "RAIL" then
          
          
          strUsed = 0
          strCID = MyRec("CID")
          strRCWeight = MyRec("Net")
strsql = "Select Net from tblCars where RC_CID = " & strCid
 Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL, Session("ConnectionString")
    if not MyRec2.eof then
    while not MyRec2.eof
    strUsed = strUsed + MyRec2("NET")
    MyRec2.movenext
    wend
    end if
    MyRec2.close
    
    strRemaining = strRcWeight - strUsed
end if

if len(MyRec("Net")) > 0 then
strweight = MyRec.fields("Net")
else
strweight =MyRec("Tons_Received")
end if 

strSAP = MyREc("SAP_NBR")
strsql = "Select UOM from tblBrokeSAP where SAP = '" & strSAP & "'"
    Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL, Session("ConnectionString")
if not MyRec2.eof then
strUOM = MyREc2("UOM")
if strUOM = "T" then
 If strweight > 1000 then
 strMetric = round(strweight / 2204.6,3)
 else
 strMetric = round((strweight * 2000)/2204.6,3)
 strRemaining = round((strRemaining * 2000)/2204.6,3)
 end if
 strMT = strMT + strMetric
elseif strUOM = "TONS" and strweight > 1000 then

strweight  = round(strweight / 2000,3)
strET = strET + strweight
else
strET = strET + strweight
end if
end if
%> <% if strUOM = "T" then 
	If MyRec("Carrier") = "RAIL" then %>
	<%= strRemaining %> T
	<% else %>
	<%= strMetric %> T
	<% end if %>
<% else 
	If MyRec("Carrier") = "RAIL" then %>
	<%= strRemaining %>&nbsp;Tons
	<% else %>
	<%= strweight %> Tons
<%
end if 
 end if %>
&nbsp; </span></td>

                  
         <% if isnull(MyRec.fields("Transfer_date")) then
             strDays = round(Now()- cdate(MyRec.fields("Date_received")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

             else
             strDays = round(Now()- cdate(MyRec.fields("Transfer_date")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Trans_Carrier") & "' "
             end if  
          
 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    if strDays  > strFree then %>
    <td align = center><font size="2" face="Arial"> <span class="style9">$<%= (int(strDays) * strFee) - (strFee * strFree) %></span>&nbsp;</font></td>
	<% else %>
	  <td align = center><font face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font face="Arial"><%= strDays%></font></td>	


	
	<td><font size="2" face="Arial"><%= MyRec("SAP_NBR")%>  &nbsp;</font></td>



	

	
	<td><font size="2" face="Arial">
	    <span class="style9">
	    <% if MyRec.fields("Rejected") = "YES" then %>
       REJECTED&nbsp;&nbsp; 
        <% end if %>
	
	<%= MyRec.fields("Other_Comments")%></span>&nbsp;</font></td>
	<td ALIGN = CENTER><font size="2" face="Arial"><span class="style9"><input type="checkbox" name="C1" value="ON"></span>&nbsp;</font></td>
	</tr>

 <%    gcount = gcount + 1
       gTcount = gTcount + 1
       
 	
       ii = ii + 1
       
       
 

       
       end if
       MyRec.MoveNext
       wend
    MyRec.close
    %>
<tr>
<td>&nbsp;</td>
<td><font face = arial size = 2 class="style10">Total:&nbsp;<%= gcount%></font></td>
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
   <td>&nbsp;</td>
   <td>&nbsp;</td>
       <td><font face = arial size = 2><b>
  		<span class="style9">
  <%= strET %> Tons<br>
  <%= strMT %> T</span></b><span class="style9"> &nbsp;</span></td>
<td>&nbsp;</td>	
<td>&nbsp;</td>
<td>&nbsp;</td>
<td>&nbsp;</td>
   <td>&nbsp;</td>

	</tr>

</table>
 
