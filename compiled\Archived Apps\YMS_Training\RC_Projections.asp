																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Rail Car Projections </TITLE>

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 



<% Dim MyRec, strsql, strstartdate
strStartdate = formatdatetime(now(),2)

'strsql = "SELECT * from tblRailTarget where Inv_Date > '" & strStartdate & "' order by Inv_date"
strsql = "SELECT * from tblRailTarget "
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<style type="text/css">
.style1 {
	border-style: solid;
	border-width: 1px;
}
</style>
</head>

<body>

<% if Session("EmployeeID") = "C97338" or Session("EmployeeID") = "B96138"  or Session("EmployeeID") = "B48888" or Session("EmployeeID") = "B55548" or Session("EmployeeID") = "C66556" or Session("EmployeeID") = "B53909" or Session("EmployeeID") = "B99660" then %>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>


<td align = left><b><font face="Arial">Rail Car Target</font></b></td>
<td align = right><b><font face="Arial"><a href="RCP_default.asp">Change Defaults</a></font></b></td>

<td align = right><font face="Arial"><a href="RCP_add.asp"><b>Add New</b></a>&nbsp;</td>


</tr></table><br>
	 
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 class = "style1" align = center style="width: 35%">  
	 <tr class="tableheader">
<td>&nbsp;</td>

				<td  align = left>     <font face="Arial" size="2">	<b>Target Date</b></font></td>
		<td  ><b><font face="Arial" size="2">RFF Total</font></b></td>
	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td> <font size="2" face="Arial">

<a href="RCP_edit.asp?id=<%= MyRec.fields("ID") %>">

Edit</a></td>

	<td  ><font size="2" face="Arial"><%= MyRec.fields("INV_Date")%>&nbsp;</font></td>
	
<td  ><font size="2" face="Arial"><%= MyRec.fields("Target")%>&nbsp;</font></td>
</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>
<% else %>
<p align="center"><font face="arial" size="3"><b>You do not have authorization to view this page</b></font></p>
<% end if %><!--#include file="Fiberfooter.inc"-->