																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Wadding Yard Inventory Report</TITLE>
<style type="text/css">
.auto-style1 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.auto-style2 {
	font-size: x-small;
	font-weight: normal;
}
.auto-style3 {
	font-size: x-small;
}
.auto-style4 {
	font-size: x-small;
	font-weight: normal;
	font-family: Arial;
}
</style>
</head>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->



<% Dim MyR<PERSON>, strsql, strDays, strDate, gSpecies, gCount, gTCount, strsql3, MyRec3

strdate = formatdatetime(now(),2)
gcount = 0
gSpecies = ""

strsql = "SELECT tblCars.*,  [date_received] AS Sort_Date FROM tblCars WHERE (Location ='YARD' or Location = 'AT DOOR') and (Species = 'WADDING' or  Grade = 'WADDING') ORDER BY  Species, Sort_Date "   
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=0>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><b>
<font face="arial" size="4" >Wadding Yard Inventory Report for <%= strDate%></font></b></span></td>
<td align = center>&nbsp;</td>


</tr>
	    </table>
<p align="right"><font face="Arial">Yard Physically Checked by 
_______________________<br>Signature&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </font>&nbsp; </p>

	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=0>  
	 <tr class="tableheader">

	<td class="auto-style3"  ><font face="Arial">Species</font></td>

	<td class="auto-style1"  >SAP #</td>
		<td align = center  ><font face="Arial" size="1" class="auto-style3">Date <br>Received</b></font></td>
		<td align = center  ><font face="Arial" size="1" class="auto-style3">Door</b></font></td>
    	<td class="auto-style2" ><font face="Arial">&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; Trailer</font></td>
		<td class="auto-style2" ><font face="Arial">Carrier</font></td>
		
	<td class="auto-style2" ><font face="Arial">REC Nbr</font></td>
	<td class="auto-style4" >Ticket #</td>
<td align = left class="auto-style2"><font face="Arial">Vendor</font></td>
<td align = left class="auto-style2"><font face="Arial">&nbsp;</font></td>

<td align=center><font face="Arial" size="1" class="auto-style3">To Date<br>Detention</font></td>
<td align="center" ><font face="Arial" size="1" class="auto-style3">Days <br>in Yard</font></td>

<td align = left class="auto-style2" ><font face="Arial">Comments</font></td>
<td align = center class="auto-style2" ><font face="Arial">Checkoff</font></td>
	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    
    
    <% if  MyRec.fields("Species") = gSpecies or gcount = 0 then %>

  
        <td> <font size="2" face="Arial">       <%= MyRec.fields("Species")%></font></td>
        <td>  <font size="2" face="Arial">       <%= MyRec.fields("SAP_Nbr")%></font></td>

	<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
		<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Door")%></font></td>
		<td  > <font size="2" face="Arial"> &nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;
		<% if MyRec("Trailer") = "UNKNOWN" then %> <%= MyRec("Transfer_Trailer_Nbr") %>
		 <% else %>
		<%= MyRec.fields("Trailer")%>
		<% end if %></font></td>
	<td  >   <font size="2" face="Arial">   
		<% if MyRec("Trailer") = "UNKNOWN" then %> <%= MyRec("Trans_Carrier") %>
		 <% else %>


     <%= MyRec.fields("Carrier")%>
     <% end if %>&nbsp;</font></td>
 
	
	<% if isnull(MyRec.fields("Rec_number")) then %>
		 <td  >  <font size="2" face="Arial">        <%= MyRec.fields("CID")%>&nbsp;</font></td>
		 <% else %>
	 <td  >  <font size="2" face="Arial">        <%= MyRec.fields("REC_number")%>&nbsp; </font></td>
	 <% end if %>

		

		
 
   <td  >  <font size="2" face="Arial">        <%= MyRec.fields("WH_Ticket")%>&nbsp;</font></td>
    <td  >  <font size="2" face="Arial">        <%= MyRec.fields("PMO_NBR")%>&nbsp;</font></td>
   <td  >  <font size="2" face="Arial"> 
         <% if MyRec.fields("Weigh_required") = "W" and MyRec.fields("Audit_tons") > 0  Then %>
      WC
       <% elseif (MyRec.fields("Weigh_required") = "W" and isnull(MyRec.fields("Audit_tons"))) or ( MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then %>W
        <% else %> &nbsp; <% end if %></td>
     
         <% 
             strDays = round(Now()- cdate(MyRec.fields("Date_received")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

       
          
 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    if strDays  > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(strDays) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"><%= strDays%></font></td>	
	
	<td align="left"><font size="2" face="Arial"><%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>


	<td ALIGN = CENTER><font size="2" face="Arial"><input type="checkbox" name="C1" value="ON">&nbsp;</font></td>
	</tr>

 <%    gcount = gcount + 1
       gTcount = gTcount + 1
 		gSpecies = MyRec.fields("Species")
       ii = ii + 1
       MyRec.MoveNext
    
    %>
<% else %>
<td colspan = 14><font face = arial size = 2><b></b>Total:&nbsp;<%= gcount%></b></font></td>


	</tr>
	<TR><td colspan = 14>&nbsp;</td>


	</tr>
	
	  <tr class=tablecolor2>
        
        <% if isnull(MyRec.fields("Transfer_date")) then %>
        <td>        <font size="2" face="Arial">         <%= MyRec.fields("Species")%></font></td>
           <td>       <font size="2" face="Arial">       <%= MyRec.fields("SAP_Nbr")%></font></td>
 
        		<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Date_received")%></font></td>
        			<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Door")%></font></td>
        		<td  > <font size="2" face="Arial">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <%= MyRec.fields("Trailer")%></font></td>
        		<td  >    <font size="2" face="Arial">        <%= MyRec.fields("Carrier")%>&nbsp;</font></td>
  
        		<% if isnull(MyRec.fields("Rec_number")) then %>
        		        		  <td> <font size="2" face="Arial">        <%= MyRec.fields("CID")%></font></td>
        		        		  <% else %>
        		  <td> <font size="2" face="Arial">        <%= MyRec.fields("REC_number")%>  </font></td>
        		  <% end if %>
        <% else %>
        	<td>       <font size="2" face="Arial">       <%= MyRec.fields("Species")%>&nbsp;:SHUTTLE</font></td>
        	<td>       <font size="2" face="Arial">       <%= MyRec.fields("SAP_Nbr")%></font></td>
		<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Transfer_Date")%>&nbsp;</font></td>
					<td align = center ><font size="2" face="Arial"><%= MyRec.fields("Door")%></font></td>

			<td  > <font size="2" face="Arial">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <%= MyRec.fields("Transfer_Trailer_nbr")%></font></td>
				<td  >  <font size="2" face="Arial">   <%= MyRec.fields("Trans_Carrier")%>&nbsp;</font></td>
		 
	 		<% if isnull(MyRec.fields("Rec_number")) then %>
        		        		  <td> <font size="2" face="Arial">        <%= MyRec.fields("CID")%></font></td>
        		        		  <% else %>
        		  <td> <font size="2" face="Arial">        <%= MyRec.fields("REC_number")%>  </font></td>
        		  <% end if %>

		<% end if %>
		
<td>   
        <font size="2" face="Arial">      <%= MyRec.fields("WH_Ticket")%></font></td>
        <td  >        <font size="2" face="Arial">        <%= MyRec.fields("PMO_NBR")%></font></td>
        <td  >  <font size="2" face="Arial">       
              <% if MyRec.fields("Weigh_required") = "W" and MyRec.fields("Audit_tons") > 0  Then %>
      WC
       <% elseif (MyRec.fields("Weigh_required") = "W" and isnull(MyRec.fields("Audit_tons"))) or ( MyRec.fields("Tons_received") = 21  and MyRec.fields("Trailer") <> "UNKNOWN") then %>W
        <% else %> &nbsp; <% end if %></td>

         <% 
             strDays = round(Now()- cdate(MyRec.fields("Date_received")),1)
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

        
          
 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    end if  
    MyRec3.close
    if strDays  > strFree then %>
    <td align = center><font size="2" face="Arial"> $<%= (int(strDays) * strFee) - (strFee * strFree) %>&nbsp;</font></td>
	<% else %>
	  <td align = center><font size="2" face="Arial">$0&nbsp;</font></td>
	<% end if %>
	<td align = center><font size="2" face="Arial"><%= strDays%></font></td>

	

	<td align="left"><font size="2" face="Arial"><%= MyRec.fields("Other_Comments")%>&nbsp;</font></td>
	<td ALIGN = CENTER><font size="2" face="Arial"><input type="checkbox" name="C1" value="ON">&nbsp;</font></td>
	</tr>
		
	<% gcount = 1
	 gTcount = gTcount + 1
	 gSpecies = MyRec.fields("Species")
       ii = ii + 1
       MyRec.MoveNext
    
	 end if %>

<%  Wend %>
	  <tr class=tablecolor2>
<td colspan = 14><font face = arial size = 2><b></b>Total:&nbsp;<%= gcount%></b></font></td>

	</tr>
	<TR><td colspan = 14>&nbsp;</td>



	</tr>
<TR><td colspan = 14><font face = arial size = 2><b></b>Grand Total:&nbsp;<%= gTcount%></b></font></td>



	</tr>

</table>