<%
'=========================================
' Error Information Page
' Displays detailed error information
'=========================================

Dim strErrSource, strErrDesc, strErrNumber
strErrSource = Request.QueryString("ErrSource")
strErrDesc = Request.QueryString("ErrDesc")
strErrNumber = Request.QueryString("ErrNumber")

' URL decode the parameters
if strErrDesc <> "" then
    strErrDesc = Server.URLDecode(strErrDesc)
end if

if strErrSource <> "" then
    strErrSource = Server.URLDecode(strErrSource)
end if

Response.Status = "500 Internal Server Error"
%>
<!DOCTYPE html>
<html>
<head>
    <title>Server Error</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .error-container { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 4px; }
        .error-title { color: #856404; font-weight: bold; margin-bottom: 10px; }
        .error-details { color: #856404; }
        .error-source { margin-top: 10px; font-size: 0.9em; color: #6c757d; }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-title">Server Error</div>
        <div class="error-details">
            <% if strErrNumber <> "" and strErrDesc <> "" then %>
                <strong>Error Number:</strong> <%= strErrNumber %><br>
                <strong>Description:</strong> <%= strErrDesc %><br>
            <% else %>
                An error occurred while processing your request.
            <% end if %>
        </div>
        <% if strErrSource <> "" then %>
        <div class="error-source">
            <strong>Source:</strong> <%= strErrSource %>
        </div>
        <% end if %>
    </div>
    
    <p><a href="javascript:history.back()">Go Back</a></p>
</body>
</html>
