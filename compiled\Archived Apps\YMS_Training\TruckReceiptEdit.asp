<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 14.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Recovered Paper Assign Actual Weight</title>
</head>
<%
    Dim strSQL, MyRec, strID, objMOC, rstFiber, strCarrier
      
    Dim strTrailer
    Dim strReleaseNbr
    Dim strRECNbr
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strGenerator
    Dim strGenCity
    Dim strGenState
    Dim strOther, strR, strsql3

       strId  = Trim(Request.QueryString("id")) 


 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
 
		Call SaveData() 

	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to delete a Receipt.</font></br>")
	MyRec.close
	end if

  
ELSE


strsql = "SELECT tblCars.* from tblCars WHERE tblCars.CID = " & strid

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


    strTrailer = MyRec.fields("Trailer")
    strReleaseNbr = MyRec.fields("Release_nbr")
    strRECNbr = MyRec.fields("rec_number")
    strGrossWeight = MyRec.fields("Gross_weight")
    strTareWeight = MyRec.fields("Tare_weight")
    strTonsReceived = MyRec.fields("Tons_Received")
    strDateReceived = MyRec.fields("Date_received")
    strGenerator = MyRec.fields("Generator")
    strGenCity = MyRec.fields("Gen_City")
    strGenState = MyRec.fields("Gen_state")
    strOther = MyRec.fields("OTher_comments")
    strCarrier = MyRec.fields("Carrier")
    strR = left(strReleaseNbr,1)
    
    if isnull(strGrossWeight) then
    strGrossWeight = 0
    end if
    If Isnull(strTareWeight) then
    strTareWeight = 0
    end if

MyRec.close
Call getdata()
	end if

%>



<body>

<table width = 100%><tr><td width = 33%></td><td align = center width = 34%><b><font face="Arial" size="4">
Edit Trailer Receipt</font> </b></td><td align = right width = 33%><a href="AssignActual.asp"><b><font face = arial size = 2>RETURN</a></font></b></td></tr></table>



<form name="form1" action="TruckReceiptEdit.asp?id=<%=strid%>&r=<%= strR%>" method="post">
<table border="1" cellspacing="0" bordercolor="#CCCCFF" width="60%" bgcolor="#CCCCFF" style="border-collapse: collapse" cellpadding="0" align = center height="393">
<tr>
    <td bgcolor="#CCCCFF">&nbsp;</td>

    <td  bgcolor="#CCCCFF">&nbsp;</td>
  </tr>
  <tr>
    <td  align = right bgcolor="#CCCCFF" >
   <font face="Arial" size="2"><b>Trailer:&nbsp;</font></b></td>
<td  align = left>

      <input type="text" name="Trailer" size="15" value = "<%= strTrailer%>"></td></tr>

  </tr>
  <tr>

      <td  bgcolor="#CCCCFF" align = right>
  <font face="Arial" size="2"><b>Receipt Number:&nbsp;</b></font></td>
<td  align = left>

      <input type="text" name="REC_Number" size="15" value = "<%= strRECNbr%>"></td></tr>
<tr>
    <td  bgcolor="#CCCCFF">  
	<p align="right">  <font face="Arial" size="2"><b>Select Carrier:&nbsp;</b></font></td>
    <td bgcolor="#CCCCFF">   <select name="Carrier">
 	<option value="" selected>  Select Carrier (Required)</option>
      <%= objGeneral.OptionListAsString(rstFiber, "Carrier", "CDesc", strCarrier) %>
     </select></td>
  </tr>
    <td  bgcolor="#CCCCFF">&nbsp;</td>

    <td bgcolor="#CCCCFF">&nbsp;</td>
  </tr>


       <tr>
    <td align = right bgcolor="#CCCCFF" >
   <font face="Arial" size="2"><b>Gross Weight:&nbsp;</b></font></td>
<td align = left>

      <input type="text" name="Gross_Weight" size="15" value = <%= strGrossWeight%>></td></tr>
      <tr>
          <td  bgcolor="#CCCCFF" align=right>
   <font face="Arial" size="2"><b>Tare Weight:&nbsp;</b></font></td>
<td align = left>

      <input type="text" name="Tare_Weight" size="15" value = <%= strTareWeight%>>

</td></tr>

      <tr>
          <td  bgcolor="#CCCCFF" align=right>
   <font face="Arial" size="2"><b>Tons:&nbsp;</b></font></td>
<td align = left>

      <font face="Arial" size="2"> <%= strTonsReceived%>

</td></tr>



<tr>
    <td  bgcolor="#CCCCFF">&nbsp;</td>

    <td  bgcolor="#CCCCFF">&nbsp;</td>
  </tr>
       <tr>
          <td  align = right bgcolor="#CCCCFF" >
    <font face="Arial" size="2"><b>Date Received:&nbsp;</b></font></td>
<td align = left>

      <input type="text" name="Date_Received" size="15" value = "<%= strDateReceived%>"></td></tr>
              <tr>
          <td  align = right bgcolor="#CCCCFF" >
   <font face="Arial" size="2"><b>Generator:&nbsp;</b></font></td>
<td align = left>
      <input type="text" name="Generator" size="25" value = "<%= strGenerator %>"></TD></tr>
        <tr>
          <td  align = right bgcolor="#CCCCFF" >
    <font face="Arial" size="2"><b>Generator City:&nbsp;</b></font></td>
<td align = left>
      <input type="text" name="Gen_City" size="15" value ="<%= strGenCity%>"></td></tr>

           <tr>
          <td align = right bgcolor="#CCCCFF" >
   <font face="Arial" size="2"><b>Generator State:&nbsp;</b></font></td>
<td align = left>
      <input type="text" name="Gen_State" size="15" value = <%= strGenState%>></td></tr>
         <tr>
          <td  align = right bgcolor="#CCCCFF" >
  <font face="Arial" size="2"><b>Other:&nbsp;</b></font></td >
   <td align = left>   <input type="text" name="Other_Comments" size="25" value = "<%= strOther%>"></td></tr>
   
   <tr>
    <td  bgcolor="#CCCCFF">&nbsp;</td>

    <td  bgcolor="#CCCCFF">&nbsp;</td>
  </tr>
<tr>
    <td  bgcolor="#CCCCFF">
	<p align="right"><font face="Arial" size="2"><b>Leave this load open?&nbsp; </b></font></td>

    <td bgcolor="#CCCCFF"><select size="1" name="Trailer_status">
	<option selected>Yes</option>
	<option>No</option>
	</select></td>
  </tr>

  <tr>
    <td bgcolor="#CCCCFF">&nbsp;</td>

    <td align = left bgcolor="#CCCCFF">
	<Input name="Update" type="submit" Value="Submit" style="float: right" ></td>
  </tr>
</table>

</form>
</body>
<%
 Function GetData()
  set objMOC = new ASP_CLS_FIBER
        set rstFiber = objMOC.FiberCarrier()
End Function



 Function SaveData()
 strid = request.querystring("id")
strTrailer = Request.form("Trailer")
strCarrier = Replace(Request.form("Carrier"), "'", "''")
strOther = Replace(Request.form("Other_Comments"), "'", "''") 
strDateReceived = Request.form("Date_received")
strRECNbr = Request.form("Rec_Number")

strGenerator = Replace(Request.form("Generator"), "'", "''")
strGenCity = Replace(Request.form("Gen_City"), "'", "''")
strGenState = Request.form("Gen_State")

strGrossWeight = Request.form("Gross_Weight")
strTareWeight = Request.form("Tare_Weight")
strTonsReceived = round((strgrossweight - strTareweight)/2000,3)

         strSql = "Update tblCars Set Carrier = '" & strCarrier & "', Other_Comments = '" & strOther & "', Trailer = '" & strTrailer & "', "_
         &" Date_Received = '" & strDateReceived & "', "_
         &" Rec_Number = '" & strRECNbr & "', "_
         &" Gross_Weight = " & strGrossWeight & ", "_
         &" Tare_Weight = " & strTareWeight & ", "_
         &" Tons_Received = " & strTonsReceived & ", Net = " & strTonsReceived & ", "_
         &" Generator = '" & strGenerator & "', "_
         &" Gen_City = '" & strGenCity & "', "_
         &" Gen_State = '" & strGenState & "' where CID = " & strid & ""


         
 	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 


If request.form("Trailer_status") = "Yes" then
      strsql = "Update tblCars set Trailer_status = 'OPEN' where CID = " & strid & ""
      else
      call SendEmail()
       strsql = "Update tblCars set Trailer_status = null where CID = " & strid & ""
      
      end if
      
      	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
        
         
      Response.redirect ("AssignActual.asp")



End Function

Function SendEmail()
dim strbody,  strECarrier, strETrailer, strELoad, strEDoc, strEDC, strEDCName, strEAdd, strECity, strEState
dim strECC, strEBCC



           strEmailTo = "<EMAIL>"
     
            strEBCC = "<EMAIL>"
                 
			Set objMail = Server.CreateObject("CDO.Message")		
			objMail.From = "<EMAIL>"
			objMail.To = strEmailTo
		
			objMail.BCC = strEBCC
			objMail.Subject = "Weight entered on YMS website "
			strBody2 = "<table border=0  cellpadding=0 cellspacing=0 width=800><tr><td align = center><font face = arial size = 2>&nbsp; Carrier&nbsp; </td><td align = left><font face = arial size = 2>Trailer&nbsp; </td><td align = left><font face = arial size = 2> Receipt # </td><td align = center><font face = arial size = 2>&nbsp; Tons received&nbsp;</td><td align = center><font face = arial size = 2>Generator</td><td align = center><font face = arial size = 2>City</td></tr>"
			
			
		
	
		
			

  strbody2 = strbody2 & " <tr><td align = center><font face = arial size = 1> " & strCarrier &  "</td><td align = left><font face = arial size = 1>" & strTrailer & "</td><td align = left><font face = arial size = 1>"  & strRECnbr & "</td><td align = center><font face = arial size = 1>"  & strTonsreceived & "</td><td align = center><font face = arial size = 1>" & strGenerator & "</td><td align = left><font face = arial size = 1>" & strGenCity & "  </td></tr> "
		    


	
	objMail.HTMLBody = "<font face = arial size = 2>The following truck(s) had the weight changed:<br><br>" & strbody2



			objMail.Send
			Set objMail = nothing
		
			

		
		



end Function





 %>

</html>
<!--#include file="Fiberfooter.inc"-->