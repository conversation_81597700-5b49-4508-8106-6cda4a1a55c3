																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE> Cross Reference list</TITLE>

<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_Session.asp"-->
<!--#include file="classes/asp_cls_DataAccess.asp"-->


<% 

 

if Session("EmployeeID") = "C97338" or Session("EmployeeID") = "B55548" then 
strPost = "OK"
end if


strsql = "SELECT tbl_Stores_IDs.* from tbl_Stores_IDs order by Display_name"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>
<style type="text/css">
.style1 {
	border: 1px solid #000000;
}
.style2 {
	border: 1px solid #C0C0C0;
}
.style3 {
	text-align: center;
	border: 1px solid #C0C0C0;
}
.style5 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style6 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	text-align: center;
}
.style7 {
	font-size: small;
}
.style8 {
	border-width: 1px;
}
.auto-style1 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: Arial;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	text-align: center;
	background-color: #E7EBFE;
	font-size: x-small;
}
.auto-style2 {
	font-size: small;
	font-weight: bold;
}
</style>
</head>

<body>
<br>
<% if strPost = "OK" then%>	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=60%  border=1 align = center>
<tr>
 <TD align = left style="width: 15%" class="style8"><font size="2" face = arial>
	<span class="style7">

 <a href="CR_Add.asp"><strong>Add New</strong></a>
 </td>
<td align = center>
<font face="arial" > <span class="auto-style2">Cross Reference List</span></font><span class="style7">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span> </td>
<td align = center class="style7">&nbsp;</td>


</tr>
	    </table>
	
	
	<TABLE align = center style="width: 35%" class="style1">  
	 <tr bgcolor="#FFFFCC">
 
	<td  align="left" class="auto-style1" >  <strong>&nbsp;Name</strong></td>
	<td class="auto-style1" >  <strong>Job Title</strong></td>
	<td class="auto-style1" >  <strong>Login ID</strong></td>
		<td class="auto-style1" >  <strong>Badge</strong></td>
		

<td  align="left" class="auto-style1" > &nbsp;</td>



	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
   
    %>
    <tr>
	<td  align="left" class="style2" > <font size="2" face="Arial"> <%= MyRec.fields("Display_Name")%>&nbsp;</font></td>
	   <td  align="left" class="style2"> <font size="2" face="Arial">   <%= MyRec.fields("Job_Title")%>&nbsp;</font>  </td>
          <td  class="style3"> <font size="2" face="Arial">   <%= MyRec.fields("Logon_ID")%>&nbsp;</font>  </td>
     <td  class="style3"> <font size="2" face="Arial">   <%= MyRec.fields("Badge")%>&nbsp;</font>  </td>
       
          <td style="height: 30px" class="style3"> <font size="2" face="Arial">
<a href="CR_Edit.asp?id=<%= MyRec.fields("ID") %>">Edit</a> </td>	
	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
     MyRec.close
    %>
</table>

<% end if %>


<!--#include file="footer.inc"-->

