<html>
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 6.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Approve Hazard Assessment</title>
</head>
<% dim strsql, MyRec, strid, strBID, strE_Name, strP_Date, MyConn, MyConn2, strsql2, strApprover1, strApprover2
Dim stra, strb, strStatus, strStatus1


strBID = Session("EmployeeID")
strE_name = Session("Ename")
strnow = dateadd("h", -5, now())
strP_date = formatdatetime(strnow,2)
		 strBID = Session("EmployeeID")
  		 strE_Name = ReturnEname(StrBID)

strid = Request.querystring("id")

     Dim strWA
      strsql = "Select Workarea from tblSOP where SOP_NO = '" & strid & "'"
       Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
   		strWA = MyConn.fields("Workarea")
   		
   		MyConn.close 
   		
   	strsql = "Select HA_Approver_1, HA_Approver_2, HA_Approval_date from tblHA where SpaceID = '" & strid & "'"
       Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
   		strApprover1 = MyConn.fields("HA_Approver_1")
   		 		strApprover2 = MyConn.fields("HA_Approver_2")
   		 		
   		MyConn.close 

	
	set objGeneral = new ASP_CLS_General
  		 if objGeneral.IsSubmit = True Then
  		 strid = Request.querystring("id")
  		 strBID = Session("EmployeeID")
  		 strE_Name = ReturnEname(StrBID)
  		 strP_Date = Request.form("P_date")
  		 stra = request.querystring("a")
  		 strb = request.querystring("b")
  		
  		 
  		 strSQL2 = "SELECT tblApprovers.* from tblApprovers where Work_area = '" & strWA & "' and P_BID = '" & strBID & "'"
      Set MyConn2 = Server.CreateObject("ADODB.Recordset") 
   		MyConn2.Open strSQL2, Session("ConnectionString")
   		 If not MyConn2.eof then
  		 
  		 	
	if isnull(strApprover1)  and isnull(strApprover2) then
			
			strsql =  "Update tblHA SET [HA_Approver_1] = '" & strE_name & "', [HA_Approval_date] = '" & strP_date & "' where SpaceID = '" & strid & "'"
 			Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 
			
		 strsql =  "INSERT INTO tblChangeLog ( Approver1, Change_Date, Change_Bid, Approver1_date, SOP_NO, Change_Desc) "_
 &" SELECT '" & strE_name & "', '" & strP_date & "', '" & strBID & "', '" & strP_Date & "', '" & strid & "',  'Approve Hazard Assessment'"
 		Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 
						
			Response.redirect("Change_history.asp?id=" & strid)
			
	end if 
	

	
	if len(strApprover1) > 0 and isnull(strApprover2) then
		strSQL = "SELECT tblApprovers.* from tblApprovers where P_Name = '" & strApprover1 & "'"
   	   Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
		strStatus = MyConn.fields("P_type")
		MyConn.close
		
		strSQL = "SELECT tblApprovers.* from tblApprovers where P_Name = '" & strE_name & "'"
     	 Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
		strStatus1 = MyConn.fields("P_type")
		MyConn.close


	
	
		If strApprover1 = strE_name then
		Response.write("<font face = arial size = 2 color = red><b><br> You can not be Approver 1 and Approver 2.</font></b>")
		elseIf strStatus = "H" and strStatus1 = "H" then 
	
	
		Response.write("<font face = arial size = 2 color = red><br><b>You must be a salaried person to approve the hazard assessment.</font></b>")
		else
			
			strsql =  "Update tblHA SET [HA_Status] = 'Approved', [HA_Approver_2] = '" & strE_name & "', [HA_Approval_date] = '" & strP_date & "' where SpaceID = '" & strid & "'"
 			Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 
			
					
		 strsql =  "INSERT INTO tblChangeLog ( Approver2, Change_date, Change_Bid, Approver2_date, SOP_NO, Change_Desc) "_
         &" SELECT '" & strE_name & "', '" & strP_Date & "', '" & strBID & "', '" & strP_Date & "', '" & strid & "',  'Approve Hazard Assessment'"
 		    Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 
		
			
		
 	
			MyConn2.close
			
			Response.redirect("Change_history.asp?id=" & strid)
			end if
			
	elseif  len(strApprover2) > 0 and isnull(strApprover1) then		
			
			strSQL = "SELECT tblApprovers.* from tblApprovers where P_Name = '" & strApprover2 & "'"
   	   Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
		strStatus = MyConn.fields("P_type")
		MyConn.close
		
		strSQL = "SELECT tblApprovers.* from tblApprovers where P_Name = '" & strE_name & "'"
     	 Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
		strStatus1 = MyConn.fields("P_type")
		MyConn.close


	
	
		If strApprover2 = strE_name then
		Response.write("<font face = arial size = 2 color = red><b><br> You can not be Approver 1 and Approver 2.</font></b>")
		elseIf strStatus = "H" and strStatus1 = "H" then 
	
	
		Response.write("<font face = arial size = 2 color = red><br><b>You must be a salaried person to approve the hazard assessment.</font></b>")
		else
			
			strsql =  "Update tblHA SET [HA_Status] = 'Approved', [HA_Approver_1] = '" & strE_name & "', [HA_Approval_date] = '" & strP_date & "' where SpaceID = '" & strid & "'"
 			Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 
			
					
		 strsql =  "INSERT INTO tblChangeLog ( Approver1, Change_date, Change_Bid, Approver2_date, SOP_NO, Change_Desc) "_
         &" SELECT '" & strE_name & "', '" & strP_Date & "', '" & strBID & "', '" & strP_Date & "', '" & strid & "',  'Approve Hazard Assessment'"
 		    Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 
		
			
		
 	
			MyConn2.close
			
			Response.redirect("Change_history.asp?id=" & strid)
			end if

			
			
			
			
		
	end if 	
			else
			MyConn2.close
			
			Response.write("<font face = arial size = 3><br>You do not have approval authorization for this Hazard Assessment." )
			end if
		
End if

 %>
 <body >
 <form name="form2" action="Approve.asp?id=<%= strid%>" method="post" ID="Form2"  >

	<p align="center"><br><font face="Arial"><b>Hazard Assessment Approval for <%= strid%></b></font></br>
	<font face="Arial">Current Approver 1: <%= strApprover1 %>&nbsp;&nbsp;&nbsp;&nbsp;Approver 2: <%= strApprover2 %></font></p>

  </p>
     <% if len(strApprover1) > 0  and len(strApprover2) > 0 then 
  else %>
 <INPUT TYPE="submit" value="Submit" style="float: right">
  <table border="1" cellpadding="0" align = center cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="70%" id="table1">

 <tr>
    <td  height="19" bgcolor="#FFFFDD">
	<p align="center"><font face="Arial" size="2">Person Approving</font></td>
	    <td  height="19" bgcolor="#FFFFDD" align="center">
	<font face="arial" size="2">Work Area</font></td>
    <td  height="19" bgcolor="#FFFFDD" align="center">
	<font face="arial" size="2">BID</font></td>
    <td  height="19" bgcolor="#FFFFDD" align="center">
	<font face="Arial" size="2">Date</font></td>
   
  </tr>

  <tr>
    <td align = center height="19" bgcolor="#FFFFFF" > <font face="Arial" size = 2>

    <%= strE_name %>
	</font></td>
	<td  height="19" bgcolor="#FFFFFF"  align = center ><font face="Arial" size="2" align = center><% = strWA%></td>
    <td  height="19" bgcolor="#FFFFFF"  ><font face="Arial" size="2">
	<p align="center">
	<font face="Arial">
	<%= strBID %></font></td>
	 <td  height="19" bgcolor="#FFFFFF">
		<p align="center"><font face="Arial" size="2">&nbsp;</font>
		<font face="Arial"><input type="text" name="P_Date" size="12" value="<%= strP_date%>"></font></td>
  
  </tr>


</table>
  <% end if %>

<br>
 

      
       <table border = 1 bordercolor="#000000" align = center><tr><td bordercolor="#D6D6F5" ><font face = arial size = 2>
      HA Approvers for Work Area <%= strWA %></td><td bordercolor="#D6D6F5"><font face = arial size = 2>Hourly/Salary</td></tr>
      
    <%  strSQL = "SELECT tblApprovers.* from tblApprovers where Work_area = '" & strWA & "'"
      Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
   		 While not MyConn.eof%>
   		     <tr><td align = center><font face = arial size = 2>
   		     <%= MyConn.fields("P_name")%></td><td align = center><font face = arial size = 1><%= MyConn.fields("P_type")%>&nbsp;</td></tr> 
         
         <%
           MyConn.MoveNext
     Wend
     MyConn.close %>
     </table>
     
</form>
<!--#include file="footer.inc"-->