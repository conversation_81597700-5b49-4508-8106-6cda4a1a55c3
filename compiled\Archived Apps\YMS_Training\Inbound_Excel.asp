 
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">
<TITLE>Recovered Paper Orders</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
 
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->

 
<!--#include file="classes/asp_cls_General.asp"-->
 

<%  
	Dim strsQL, rstEquip, objGeneral, objNew, rstSpecies, strSpecies, MyRec, strCommodity, strDigit, strDigitLC, MyConn


 	Dim  strdate, strDelDate, strcount, strPO, strRelease,  strStatus, strReleaseNo, strsql2, MyRec2
Response.ContentType = "application/vnd.ms-excel"

strdate = formatdatetime(now(),2)
strdate = dateadd("d", -2, strdate)

   set objGeneral = new ASP_CLS_General

 
 
  strCommodity = Request.querystring("c")
  strSpecies = Request.querystring("s")
  
 	 strPO = Request.querystring("p")
 	 If len(strPO) > 4 then
 	 'do nothing
  	else 
  	strPO = Null
 	 End if
 	 

If strPO <> 0 then

    strsql = "SELECT tblInbound.* FROM tblInbound where Destination_city = 'MOBILE' and '" & strdate & "' <= date_to "_
&" and PO = '" & strpo & "' "_
&" order by left(release,1), date_to" 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
	 MyRec.Open strSQL, Session("ConnectionString") 

  		 elseif strCommodity  = "SECONDARY FIBER" then
	 

    strsql = "SELECT tblInbound.* FROM tblInbound where Destination_city = 'MOBILE' and '" & strdate & "' <= date_to "_
    &" AND (left(release,1) = 'C' or  left(release,1) = 'c' or left(release,1) = 'K' or  left(release,1) = 'k' or left(release,1) = 'S' or  left(release,1) = 's' or "_
    &" left(release,1) = 'P' or  left(release,1) = 'p' or left(release,1) = 'F' or  left(release,1) = 'f' or left(release,1) = 'M' or  left(release,1) = 'm' or "_
	&" left(release,1) = 'R' or  left(release,1) = 'r' or left(release,1) = 'H' or  left(release,1) = 'h' or left(release,1) = 'D' or  left(release,1) = 'd' or "_
	&" left(release,1) = 'B' or  left(release,1) = 'b') order by left(release,1), date_to" 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
	 MyRec.Open strSQL, Session("ConnectionString") 


elseif   strSpecies <> "" then
  		if strSpecies = "OCC" then
 	 strDigit = "C"
 	 strDigitLC = "c"
  	elseif strSpecies = "KCOP" then
  	strDigit = "K"
  	strDigitLC = "k"
  	elseif strSpecies = "SWL" then
 	 strDigit = "S"
    elseif strSpecies = "PMX" then
  	strDigit = "P"
  	strDigitLC = "p"
   elseif strSpecies = "DLK"  or strSpecies = "IGS" then
  	strDigit = "6"
  	elseif strSpecies = "WLS" then
  	strDigit = "8"
  	 elseif strSpecies = "SBS" then
  	strDigit = "7"
   elseif strSpecies = "OF" then
 	 strDigit = "F"
  strDigitLC = "f"
    elseif strSpecies = "MXP" then
 	 strDigit = "M"
  strDigitLC = "m"
    elseif strSpecies = "ROCC" then
 	 strDigit = "R"
  strDigitLC = "r"

 	
  	 elseif strSpecies = "BROKE"  or strSpecies = "Broke" then
  	strDigit = "B"
  		 elseif strSpecies = "WBROK" then
  	strDigit = "B"
		 elseif strSpecies = "HBX" then
  	strDigit = "H"
	 elseif strSpecies = "SHRED" then
  	strDigit = "D"
  	


  end if

	If strDigit = "B" then
 	 strsql = "SELECT tblInbound.* FROM tblInbound where Destination_city = 'MOBILE' and '" & strdate & "' <= date_to"_
	&" and (left(release,1) = '" & strdigit  & "'  or len(Status) > 1) "_
	&" order by date_to"  

	else      
  	strsql = "SELECT tblInbound.* FROM tblInbound where Destination_city = 'MOBILE' and '" & strdate & "' <= date_to"_
	&" and (left(release,1) = '" & strdigit  & "'  or len(Status) > 1) "_
	&" order by date_to"  
    end if

   Set MyRec = Server.CreateObject("ADODB.Recordset")
	 MyRec.Open strSQL, Session("ConnectionString") 	

else
  strsql = "SELECT tblInbound.* FROM tblInbound where Destination_city = 'MOBILE' and '" & strdate & "' <= date_to"_
&" order by left(release,1), date_to" 


   Set MyRec = Server.CreateObject("ADODB.Recordset")
	 MyRec.Open strSQL, Session("ConnectionString") 	

end if

strcount = 1
  
  %>
   <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=96% class="tablecolor1" border=1 align = center>

 
      <tr class="tableheader">
      <td  align="center"><font face="Arial" size = 1>Load</font></td>
    <td  align="center"><font face="Arial" size = 1>Trailer</font></td>
       <td  align="center"><font face="Arial" size = 1>Verify<br>Weight</font></td>

       <td  align="center"><font face="Arial" size = 1>Species</font></td>
  <td  align="center"><font face="Arial" size = 1>Ship_status</font></td>
   
      <td  align="center"><font face="Arial" size = 1>Delivery Date</font></td>
 	<td  align="left"><font face="Arial" size = 1>Vendor Name</font></td>
<td  align="left"><font face="Arial" size = 1>Vendor City</font></td>
<td  align="center"><font face="Arial" size = 1>PO Number</font></td>
<td  align="center"><font face="Arial" size = 1>Release</font></td>
<td  align="center"><font face="Arial" size = 1>Carrier</font></td>
<td  align="center"><font face="Arial" size = 1>Count</font></td>

      
  	<% 
      Dim ii
       ii = 0
       while not MyRec.Eof
       strReleaseNo = MyREc("release")
       
       strsql2 = "Select CID from tblCars where Release_nbr = '" & strReleaseNo & "'"
          Set MyRec2 = Server.CreateObject("ADODB.Recordset")
	 MyRec2.Open strSQL2, Session("ConnectionString") 	
	 if not MyRec2.eof then
	 'skip display
	 else

       
       if MyRec.fields("date_to")  <> strDeldate then
       strcount = 1
       end if
       
 
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr class=tablecolor1>
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
              <td  align="left"><font face="Arial" size = 1><%= MyRec.fields("Load_nbr")%>&nbsp;</font></td>
                  <td  align="left"><font face="Arial" size = 1><%= MyRec.fields("Trailer")%>&nbsp;</font></td>
                  
                  <% strsql3 = "Select V_Weight from tblOrder where Release = '" & strReleaseNo & "'"
                  
                         Set MyRec3 = Server.CreateObject("ADODB.Recordset")
	 MyRec3.Open strSQL3, Session("ConnectionString") 	
	 if not MyRec3.eof then %>
	 

                  
                     <td  align="left"><font face="Arial" size = 1><%= MyRec3.fields("V_weight")%>&nbsp;</font></td>
                     <% else %>
                              <td  align="left"><font face="Arial" size = 1> &nbsp;</font></td>
                     <% end if %>
           <td  align="left"><font face="Arial" size = 1>
          <% 
          
          if left(MyRec.fields("Release"),1) = "C"  or left(MyRec.fields("Release"),1) = "c" then %>
              OCC
              <% elseif left(MyRec.fields("Release"),1) = "K" or  left(MyRec.fields("Release"),1) = "k"then %>  
               KCOP
          <% elseif left(MyRec.fields("Release"),1) = "H" or  left(MyRec.fields("Release"),1) = "h"then %>  
              HBX
<% elseif left(MyRec.fields("Release"),1) = "D" or  left(MyRec.fields("Release"),1) = "d"then %>  
              SHRED

              <% elseif left(MyRec.fields("Release"),1) = "P"  or left(MyRec.fields("Release"),1) = "p" then %>  
              PMX
                 <% elseif left(MyRec.fields("Release"),1) = "S"  or left(MyRec.fields("Release"),1) = "s" then %>  
              SWL

                <% elseif left(MyRec.fields("Release"),1) = "F"  or left(MyRec.fields("Release"),1) = "f" then %>  
             OF

  <% elseif left(MyRec.fields("Release"),1) = "M"  or left(MyRec.fields("Release"),1) = "m" then %>  
              MXP
 <% elseif left(MyRec.fields("Release"),1) = "B" or left(MyRec.fields("Release"),1) = "b"  then 
 strRelease = myrec.fields("release")
      strsql = "SELECT Broke_Description from tblOrder where Release = '" & strRelease & "' "


   Set MyRec2 = Server.CreateObject("ADODB.Recordset")
	 MyRec2.Open strSQL, Session("ConnectionString") 
   if not MyRec2.eof then 
 %>  
           BROKE - <%= MyRec2("Broke_Description") %>
           <% else %>
           BROKE
           <% end if
           MyRec2.close %>

      <% elseif left(MyRec.fields("Release"),1) = "R"  or left(MyRec.fields("Release"),1) = "r" then %>  
           ROCC
  
        <% elseif len(MyRec.fields("Status")) > 1 then
        strStatus = MyRec.fields("Status")
              strsql = "SELECT Type from tblBrokeSAP where SAP = '" & strStatus & "' "


   Set MyRec2 = Server.CreateObject("ADODB.Recordset")
 	 MyRec2.Open strSQL, Session("ConnectionString") 
   if not MyRec2.eof then %>
           BROKE - <%= MyRec2("Type") %>
        
      <% else  %>
      BROKE
      <% end if %>
           <% else %>  
             &nbsp;
             <% end if %></font></td>
       <td  align="center"><font face="Arial" size = 1><%= MyRec.fields("Ship_status") %>&nbsp;</font></td>

     <td  align="center"><font face="Arial" size = 1><%= formatdatetime(MyRec.fields("date_to"),2)%>&nbsp;</font></td>
     <td  align="left"><font face="Arial" size = 1><%= MyRec.fields("ship_from_name")%>&nbsp;</font></td>
     <td  align="left"><font face="Arial" size = 1><%= MyRec.fields("ship_from_city")%>&nbsp;</font></td>
     <td  align="center"><font face="Arial" size = 1><%= MyRec.fields("po")%>&nbsp;</font></td>
   
     <td  align="center"><font face="Arial" size = 1><%= MyRec.fields("release")%>&nbsp;</font></td>
     <td  align="center"><font face="Arial" size = 1><%= MyRec.fields("carrier")%>&nbsp;
       <td  align="center"><font face="Arial" size = 1><%= strcount%>  </font></td> 
   </tr>
    <% 
       ii = ii + 1
       strDelDate = MyRec.fields("date_to")
       strcount = strcount + 1
       
       end if
       MyRec.MoveNext
     Wend
    %>
   </table>
<table>    <tr><td colspan="7" bgcolor="white" align="right">&nbsp;</td></tr></table>

<%
MyRec.close
 
 %>