<html>
<head>
<title>kc style sheet</title>
<style type="text/css">{  }
.tempDept { 
	margin-top: 0px;
	margin-left: 0px;
	margin-right: 0px;
	text-align: center;
	text-indent: 0px;
	text-decoration: none;
	font-size: 18pt;  
	font-weight: bold;
	font-family: Arial, helvetica, sans-serif }
.tempTitle { 
	margin-top: 0px;
	margin-left: 0px;
	margin-right: 0px;        
	text-align: center;
	text-indent: 0px;
	text-decoration: none;
	font-size: 14pt; 
	font-weight: bold;
	font-style: none;
	font-family: Arial, helvetica, sans-serif }
.tempSubtitle { 
	margin-top: 0px;
	margin-left: 0px;
	margin-right: 0px;        
	text-align: center;
	text-indent: 0px;
	text-decoration: none;
	font-size: 12pt; 
	font-weight: bold;
	font-style: none;
	font-family: Arial, helvetica, sans-serif }
.tempBodytext { 
	margin-left: 10px;
	margin-right: 10px;
	font-weight: plain;
	font-style: none;
	font-size: 10pt; 
	font-family: Arial, helvetica, sans-serif }
.tempFootertext { 
	margin-top: 0px;
	margin-left: 0px;
	margin-right: 0px;
	text-align: center;
	text-indent: 0px;
	text-decoration: none;
	font-size: 9pt;
	font-weight: plain;
	font-style: italic;
	font-family: Arial, helvetica, sans-serif }
.tempMenutext {
	margin-top: 0px;
	margin-left: 0px;
	margin-right: 0px; 
	font-size: 9pt; 
	font-weight: plain;
	font-style: none;
	font-family:  Arial, helvetica, sans-serif }
.department { 
	margin-top: 0px;
	margin-left: 0px;
	margin-right: 0px;
	text-align: center;
	text-indent: 0px;
	text-decoration: none;
	font-size: 18pt;  
	font-weight: bold;
	font-family: Arial, helvetica, sans-serif }
.title { 
	margin-top: 0px;
	margin-left: 0px;
	margin-right: 0px;        
	text-align: center;
	text-indent: 0px;
	text-decoration: none;
	font-size: 14pt; 
	font-weight: bold;
	font-style: none;
	font-family: Arial, helvetica, sans-serif }
.subtitle { 
	margin-top: 0px;
	margin-left: 0px;
	margin-right: 0px;        
	text-align: center;
	text-indent: 0px;
	text-decoration: none;
	font-size: 11pt; 
	font-weight: bold;
	font-style: none;
	font-family: Arial, helvetica, sans-serif }
.text { 
	margin-left: 15px;
	margin-right: 10px;
	font-weight: plain;
	font-style: none;
	font-size: 8pt; 
	font-family: Arial, helvetica, sans-serif }
.textblue { 
	margin-left: 15px;
	margin-right: 15px;
	color: 000090;
	font-weight: plain;
	font-style: none;
	font-size: 8pt; 
	font-family: Arial, helvetica, sans-serif }
.textbluebig { 
	margin-left: 15px;
	margin-right: 15px;
	color: 000090;
	font-weight: plain;
	font-style: italics;
	font-size: 11pt; 
	font-family: Arial, helvetica, sans-serif }
.footer { 
	margin-top: 0px;
	margin-left: 0px;
	margin-right: 0px;
	text-align: center;
	text-indent: 0px;
	text-decoration: none;
	font-size: 9pt;
	font-weight: plain;
	font-style: italic;
	font-family: Arial, helvetica, sans-serif }
.menutitle { 
	margin-top: 0px;
	margin-left: 0px;
	margin-right: 0px;
	text-align: left;
	color: black;
	font-size: 9pt; 
	font-weight: bold;
	font-style: none;
	font-family: Arial, helvetica, sans-serif }
.menutext {
	margin-top: 0px;
	margin-left: 0px;
	margin-right: 0px; 
	font-size: 9pt; 
	font-weight: plain;
	font-style: none;
	font-family:  Arial, helvetica, sans-serif }
.menutexts {
	margin-top: 0px;
	margin-left: 0px;
	margin-right: 0px; 
	color: eeeeee;
	font-size: 9pt; 
	font-weight: plain;
	font-style: none;
	font-family:  Arial, helvetica, sans-serif }
.tablecolor1
{
	background-color: #CCCCFF;
        font-family: Arial;
        font-size: 10pt;
}
.tablecolor2
{
	background-color: #FCFBF5;
        font-family: Arial;
        font-size: 10pt;
}
.tableheader
{
	background-color: #FCFBF5;
}
-->


div.menuBar,
div.menuBar a.menuButton,
div.menu,
div.menu a.menuItem {
  font-family: "MS Sans Serif", Arial, sans-serif;
  font-size: 10pt;
  font-style: normal;
  font-weight: bold;
  color: #000000;
}

div.menuBar {

  background-color: #CCCCFF;
  border: 2px outset #e0e0e0;
  padding: 4px 6px 4px 6px;
  text-align: left;
  width:100%;
}



}

div.menuBarvertical {
  background-color: #666699;
  border: 2px outset #e0e0e0;
  padding: 4px 6px 4px 6px;
  text-align: center;
  width:100%;
  font-size: 14pt;
  margin: 45px;
}

div.menuBarvertical a.menuButton {
  background-color: transparent;
  border: 1px solid #666699;
  color: white;
  cursor: default;
  left: 0px;
  margin: 1px;
  padding: 2px 2px 2px 2px;
  position: relative;
  text-decoration: none;
  top: 0px;
  z-index: 100;
}



}

div.menuBar a.menuButton {
  background-color: transparent;
  border: 1px solid #C9CFDA;
  color: #000000;
  cursor: default;
  left: 0px;
  margin: 1px;
  padding: 2px 2px 2px 2px;
  position: relative;
  text-decoration: none;
  top: 0px;
  z-index: 100;
}

div.menuBar a.menuButton:hover {
  background-color: transparent;
  border: 1px outset #e0e0e0;
  color: #000000;
  cursor: hand;
}

div.menuBar a.menuButtonActive,
div.menuBar a.menuButtonActive:hover {
  background-color: #e0e0e0;
  border: 1px inset #ffffff;
  color: #000000;
  left: 0px;
  top: 0px;
}

div.menu {
  background-color: #e0e0e0;
  border: 2px outset #e0e0e0;
  left: 0px;
  padding: 0px 1px 1px 0px;
  position: absolute;
  top: 0px;
  visibility: hidden;
  z-index: 101;
}

div.menu a.menuItem {
  color: #000000;
  cursor: hand;
  display: block;
  padding: 3px 1em;
  text-decoration: none;
  white-space: nowrap;
}

div.menu a.menuItem:hover, div.menu a.menuItemHighlight {
  background-color: #000080;
  color: #ffffff;
}

div.menu a.menuItem span.menuItemText {}

div.menu a.menuItem span.menuItemArrow {
  margin-right: -.75em;
}

div.menu div.menuItemSep {
  border: 1px inset #e0e0e0;
  margin: 4px 2px;
}

</style>

</HEAD>

</HTML>