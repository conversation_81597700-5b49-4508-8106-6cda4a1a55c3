<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 14.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Rejected Load</title>
<style type="text/css">
.style1 {
	border: 1px solid #E4E4E4;
	border-collapse: collapse;
		background-color: #FFFFCC;
}
.style2 {
	border-style: solid;
	border-color: #DFDFF7;
	background-color: #DFDFF7;
}
.style3 {
	font-weight: bold;
	border-style: solid;
	border-color: #FFFFCC;
	background-color: #FFFFCC;
}
.style7 {
	background-color: #FFFFCC;
}
.style8 {
	background-color: #DFDFF7;
}
.style9 {
	font-family: Arial;
	font-size: medium;
	font-weight: bold;
}
.style10 {
	font-family: Arial;
	font-size: x-small;
}
.style12 {
	background-color: #FFFFCC;
	font-family: Arial;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID, objMOC, rstFiber, strCarrier
      
    Dim strTrailer
    Dim strReleaseNbr
    Dim strRECNbr
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strGenerator
    Dim strGenCity
    Dim strGenState
    Dim strOther, strR, strsql3, strpage

       strId  = Trim(Request.QueryString("id")) 
       strpage = request.querystring("p")


 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then




	If Session("EmployeeID") = "B53909" or Session("EmployeeID") = "B48888" or Session("EmployeeID") = "C97338" or  Session("EmployeeID") = "B55548" or Session("EmployeeID") = "C66556" or Session("EmployeeID") = "B55404" or Session("EmployeeID") = "C22521" then

 
		Call SaveData() 

	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to reject a Load.</font></br>")
	MyRec.close
	end if

  
ELSE


strsql = "SELECT tblCars.* from tblCars WHERE tblCars.CID = " & strid

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


    strTrailer = MyRec.fields("Trailer")
    strCarrier = MyRec.fields("Carrier")
    strRECNbr = MyRec.fields("CID")
    
 
     strDateReceived = MyRec.fields("Reject_Date")
     strDateShipped = MyRec.fields("Reject_Shipped")
     strOCCLocation = MyRec.fields("Location")
	
    

MyRec.close

	end if

%>



<body>
<table width = 100%><tr><td width = 33%></td><td align = center width = 34%>
	<span class="style9">Rejected Load</span><b> </b></td><td align = right width = 33%><b><font face = arial size = 2><a href="javascript:history.go(-1);">RETURN</a></font></b></td></tr></table>



<form name="form1" action="Rejected_load.asp?p=<%=strpage%>&id=<%=strid%>" method="post">
<table cellspacing="0" width="60%" align = center class="style1">
<tr>
    <td class="style2" colspan="4">&nbsp;</td>

  </tr>
    <tr>

      <td align = right class="style3" style="height: 41px">
  <font face="Arial" size="2">Receipt&nbsp;&nbsp; Number:&nbsp;</font></td>
<td  align = left colspan="3"> <font face="Arial" size="2"><%= strRecNbr%>

      &nbsp;</td></tr>

  <tr>
    <td  align = right class="style3" style="height: 48px" >
   <font face="Arial" size="2">Trailer:&nbsp;</font></td>
<td  align = left style="height: 48px" colspan="3"><font face="Arial" size="2">

     <%= strTrailer%></td></tr>

  </tr>
<tr>
    <td class="style3" style="height: 54px">  
	<p align="right">  <font face="Arial" size="2">&nbsp;Carrier:&nbsp;</font></td>
    <td class="style7" style="height: 54px" colspan="3"><font face="Arial" size="2"> <%= strCarrier %></td>
  </tr>

<tr>
    <td class="style8" colspan="4">&nbsp;</td>

  </tr>
       <tr>
          <td  align = right class="style3" style="height: 56px" c >
    <font face="Arial" size="2">Date Rejected:&nbsp;</font></td>
<td align = left style="height: 56px" colspan="3">

      <input type="text" name="Date_Received" size="15" value = "<%= strDateReceived%>"></td></tr>

       <tr>
          <td  align = right class="style3" style="height: 56px" >
    <font face="Arial" size="2">Date Left Yard:&nbsp;</font></td>
<td align = left style="height: 56px">

      <input type="text" name="Date_Shipped" size="15" value = "<%= strDateShipped%>"></td>
<td align = left style="height: 56px">

      <font face="Arial" size="2"><strong>New Location:</strong></font></td>
<td align = left style="height: 56px">

	<font face="Arial">	
	<select size="1" name="OCC_Location">
	<option <% if strOCC_Location = "BALDWIN" then%> selected <% end if %>>BALDWIN</option>
	<option <% if strOCC_Location = "BROKE CENTER" then%> selected <% end if %>>BROKE CENTER</option>
	<option <% if strOCC_Location = "DFF" then%> selected <% end if %>>DFF</option>
	<option <% if strOCC_Location = "MERCHANTS" then%> selected <% end if %>>MERCHANTS</option>
	<option <% if strOCC_Location = "MMS" then%> selected <% end if %>>MMS</option>
	<option <% if strOCC_Location = "OCC" then%> selected <% end if %>>OCC</option>
	<option <% if strOCC_Location = "RF" then%> selected <% end if %>>RF</option>
		<option <% if strOCC_Location = "SUPPLIER" then%> selected <% end if %>>SUPPLIER</option
	<option <% if strOCC_Location = "TM BASEMENT" then%> selected <% end if %>>TM BASEMENT</option>
	<option <% if strOCC_Location = "WHSE16" then%> selected <% end if %>>WHSE16</option>
	<option <% if strOCC_Location = "WHSE17" then%> selected <% end if %>>WHSE17</option>
	<option <% if strOCC_Location = "YARD" then%> selected <% end if %>>YARD</option>

	</select></font></td></tr>
<tr>
    <td class="style3" style="height: 54px">  
	<p align="right" class="style10">  N<font size="2">OTE:</font></td>
    <td class="style12" style="height: 54px" colspan="3">If you identified a 
	load as a reject in error, <br>and come back to blank out the Date Rejected, you 
	will need to Edit<br> the load in Access to put in the correct Date Unloaded and 
	Location.</td>
  </tr>

  <tr>

    <td class="style8">&nbsp;</td>

    <td align = left class="style8" colspan="3"><Input name="Update" type="submit" Value="Submit" ></td>
  </tr>
</table>

</form>
</body>
<%



 Function SaveData()
strDateReceived = Request.form("Date_received")
strDateShipped = Request.form("Date_Shipped")
strLocation = Request.form("Location")

If len(strDateReceived) > 5 and strDateShipped = "" then


         strSql = "Update tblCars Set  Reject_date =  '" & strDateReceived & "', Rejected = 'YES', Location = 'YARD', Date_unloaded = null where CID = " & strid & ""
         
         elseif len(strDateReceived) > 5 and len(strDateShipped) > 5 then
         
         strSql = "Update tblCars Set  Reject_date =  '" & strDateReceived & "', Rejected = 'YES', Location = '" & strLocation & "',"_
         &"  Date_unloaded = '" & strDateShipped & "', Reject_Shipped = '" & strDateShipped & "'  where CID = " & strid & ""
         
 		elseif strDateReceived = "" and strDateShipped = ""      then   
         
         strSql = "Update tblCars Set  Reject_date =  Null, Rejected = Null, Reject_Shipped = Null where CID = " & strid & ""

         end if
         
 	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
      

    
if request.querystring("p") = "r" then
response.redirect("Rejected_Load_List.asp")
else

Response.redirect ("Sixty_Day List.asp")

end if

End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->