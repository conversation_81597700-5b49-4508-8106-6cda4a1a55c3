﻿<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->


<%

dim strID, strCarrier
strID = Request.querystring("id")
strCarrier = request.querystring("c")

    set objGeneral = new ASP_CLS_General
  %>


   <form action="Carrier_Delete.asp?id=<%= strid%>&c=<%= strCarrier%>" method=post >

                <table width = 100%> 
<tr><td></td><td colspan=2 align = right><font face="Arial"><a href="Carrier.asp"><b>Return</b></a></font></td></tr>
<% Dim <PERSON>, strsql1, strPO


strsql1 = "SELECT tblCarrier.* FROM tblCarrier WHERE ID = " & strid & ""

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL1, Session("ConnectionString")

strCarrier = MyRec.fields("Carrier") 
MyRec.close
%>
     <tr>
                        <td><Font size = 2 face = Arial> Are you sure you want to delete Carrier <b><%= strCarrier %></b>? <br><br> If so, click the button below.
                           
            </td>
            
        </tr>
        
    </table>

<p>

<Input name="Update" type="submit" Value="Delete Record" >
</form>


  <% if objGeneral.IsSubmit() Then 


Dim strsQL3

    strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
   
    strid = request.querystring("id")
    strCarrier = request.querystring("c")


   
    
	Dim strsql, Myconn
	strSQL = "DELETE FROM tblCarrier where tblCarrier.ID = " &  strID & ""

			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
MyRec.close


Response.redirect ("Carrier.asp") 
else
Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to delete a record.</font></br>")
MyRec.close
end if
  end if
%><!--#include file="Fiberfooter.inc"-->