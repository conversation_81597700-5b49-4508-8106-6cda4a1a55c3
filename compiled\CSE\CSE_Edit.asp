<html>
<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_Procedure.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionString.asp"-->
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 6.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Add Energy Control Procedure</title>
</head>
<% dim strsql, MyRec, strid,  objGeneral, strDescription, strLocation, strComments, strDate
dim objEPS, rstTeam, rstWA, MyConn, strTeam, strSpace_Id, strWA, strFunctional, strAsset

strid = Request.querystring("id")
strnow = dateadd("h", -5, now())
strDate = formatdatetime(strnow,2)
 strsql = "SELECT tblSOP.* from tblSOP where SID = " & strid & ""

Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
  If not MyConn.eof then 

strDescription = MyConn.fields("sDescription")
strLocation = MyConn.fields("Location")
strComments = MyConn.fields("Comment")
strDate = MyConn.fields("sDate")
strTeam = MyConn.fields("TeamName")
strSpace_Id = MyConn.fields("SOP_NO")
strWA = MyConn.fields("WorkArea")
strFunctional = MyConn.fields("Functional Location")
strFunctional = MyConn.fields("AssignedAsset")
end if
MyConn.Close
Call getdata()




set objGeneral = new ASP_CLS_General
  		 if objGeneral.IsSubmit = True Then
  		

 strDate = Request.form("Date")
 strTeam = Request.form("Team")
 strWorkarea = Request.form("Workarea")
 	
 	
  	if isnull(Request.form("Description")) then
strDescription = ""
else
strDescription = Replace(Request.form("Description"), "'", "''") 
end if

if isnull(Request.form("Location")) then
strLocation = ""
else
strLocation= Replace(Request.form("Location"), "'", "''") 
end if

if isnull(Request.form("Comments")) then
strComments = ""
else
strComments = Replace(Request.form("Comments"), "'", "''") 
end if

if isnull(Request.form("Asset")) then
strAsset = ""
else
strAsset = Replace(Request.form("Asset), "'", "''") 
end if

if isnull(Request.form("Functional_location")) then
strFunctional = ""
else
strFunctional= Replace(Request.form("Functional_location"), "'", "''") 
end if

 strsql =  "Update tblSOP set  AssignedAsset = '" & strAsset & "', Sdate = '" & strDate  & "', TeamName  = '" & strTeam  & "', "_
 &" WorkArea  = '" & strWorkarea  & "', Sdescription  = '" & strDescription  & "', Location  = '" & strLocation  & "', "_
 &" Comment  = '" & strComments  & "', [Functional location] = '" & strFunctional & "' where SID = " & strid & ""
 Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 


Response.redirect("CESearch.asp")
end if 

 %><body >
 <p align="left"><b><font face = arial size = 3>Edit Confined Space:</font></b><br>
	<font face="Arial"><br>
 </font>
 </p>
	<form name="form1" action="CSE_Edit.asp?id=<%= strid%>"  method="post" ID="Form1"  >
 
 <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#D9E1F9">
   
  <tr>
    <td   bgcolor="#FFFFDD">
	<p align="center"><font face="Arial" size="2">Space ID #</font></td>
    <td bgcolor="#FFFFDD" align="center">
	<font face="Arial" size="2">Date</font></td>

   
    <td bgcolor="#FFFFDD" align="center">
	<font face="Arial">Team</font></td>

   
    <td bgcolor="#FFFFDD" align="center">
	<font face="Arial" size="2">Work Area</font></td>

   
    <td bgcolor="#FFFFDD" align="center">
	<font face="Arial" size="2">Functional Location</font></td>

   
    <td bgcolor="#FFFFDD" align="center">
	<font face="Arial" size="2">Status</font></td>

   
  </tr>
  <tr>
    <td align = center bgcolor="#FFFFFF" > <font face = arial size = 2>
	<%= strSpace_Id %></td>
    <td  bgcolor="#FFFFFF"  >
	<p align="center"> <font face = arial size = 2>
	<input type="text" name="Date" size="10" value="<%= strDate %>" ></td>
	
    <td  bgcolor="#FFFFFF"  >
	<p align="center">  <font face="Arial">    <select name="Team">
       <option value="">--- All ---</option>
       <%= objGeneral.OptionListAsString(rstTeam, "Team", "Team", strTeam) %>
     </select></font></td>
	
    <td  bgcolor="#FFFFFF"  >
	<p align="center">
		<font face="Arial">  <select name="Workarea">
       <option value="">--- All ---</option>
       <%= objGeneral.OptionListAsString(rstWA, "WorkArea", "WorkArea", strWA) %>
     </select></font></td>
	
    <td  bgcolor="#FFFFFF"  >
	<p align="center"> <font face = arial size = 2>
	<input type="text" name="Functional_location" size="39" value="<%= strFunctional %>" ></td>
	
    <td  bgcolor="#FFFFFF"  align = center >
		<font face="Arial">  <select name="Status" size="1">
       <option selected>In Use</option>
    
     	<option>Removed</option>
    
     </select></font></td>
	
  </table> 
	<p align="center">&nbsp;</p>
 
 <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#D9E1F9" id="table1" align = center>
   
  <tr>
    <td bgcolor="#FFFFDD" align="center">
	<font face="arial" size="2">Description</font></td>

     <td bgcolor="#FFFFDD" align="center">
	<font face="arial" size="2">Asset</font></td>
  </tr>
  <tr>
    <td  bgcolor="#FFFFFF"  >
	<p align="center"><font face="Arial" size="2">
	<input type="text" name="Description" size="73" value="<%= strDescription %>" ></font></td>
	   <td  bgcolor="#FFFFFF"  >
	<p align="center"><font face="Arial" size="2">
	<input type="text" name="Asset" size="33" value="<%= strAsset%>" ></font></td>
  </table>
	<p align="center">&nbsp;</p>
 
 <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="80%" bgcolor="#D9E1F9" id="table2" align = center>
   
  <tr>
    <td bgcolor="#FFFFDD" align="center">
	<font face="arial" size="2">Location</font></td>

   
  </tr>
  <tr>
    <td  bgcolor="#FFFFFF"  >
	<p align="center"><font face="Arial" size="2">
	<input type="text" name="Location" size="93" value="<%= strLocation %>" ></font></td>
	
  </table>
	<p align="center">&nbsp;</p>
 
 <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="80%" bgcolor="#D9E1F9" id="table3" align = center>
   
  <tr>
    <td bgcolor="#FFFFDD" align="center">
	<font face="arial" size="2">Comments</font></td>

   
  </tr>
  <tr>
    <td  bgcolor="#FFFFFF"  >
	<p align="center"><font face="Arial" size="2">
	<input type="text" name="Comments" size="93" value="<%= strComments %>" ></font></td>
	
  </table>
	<p>&nbsp;<INPUT TYPE="submit" value="Submit"></p>
	</form>

 
   <% Function GetData()
        set objEPS = new ASP_CLS_ProcedureESL
        set rstTeam = objEPS.ReadTeamList()
 	set rstWA = objEPS.ReadWorkArea()


    End Function%><!--#include file="footer.inc"-->