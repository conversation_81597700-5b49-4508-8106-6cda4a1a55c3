
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Add AIG User</TITLE>
<!--#include file="classes/asp_cls_General.asp"-->


<!--#include file="classes/asp_cls_headerAIG.asp"-->

<!--#include file="classes/asp_cls_ProcedureMRP.asp"-->
<!--#include file="classes/asp_cls_DataAccessAIG.asp"-->
<%

Dim strSQL, MyRec, strRelease, strDescription, strid
 dim strtoday, strNeeded, strTomorrow
  strNow= DateAdd("h", -5, Now())
 strtoday = formatdatetime(strnow,2)
 strtomorrow = formatdatetime(dateadd("d", 1, strnow),2)
 strid = request.querystring("id")

strsql = "Select release, author, id, status from tblExceptions where id = " & strid
  Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
    If not MyConn.eof then
    
   strAuthor = MyConn.fields("Author")
    strRelease = MyConn.fields("Release")
    end if
    MyConn.close

 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then


	Call SaveData() 

End if
%>
<style type="text/css">
.style1 {
	font-family: Arial;
	font-size: x-small;
}
.style3 {
	text-align: left;
	border-width: 1px;
}
.style4 {
	font-family: Arial;
	font-size: medium;
}
.style5 {
	font-family: Arial;
}
.style6 {
	color: #FF0000;
	font-size: small;
}
</style>
</head>

<body>
<form name="form1" action="Exception_complete.asp?id=<%=strid%>" method="post">
 
<table border="0" cellpadding="0" cellspacing="0" width = 60% style="border-collapse: collapse" bordercolor="000000" height="25" align = center >
  <tr>
    <td  bgcolor="#FFFFFF" bordercolor="#CCCCFF" height="25" >
    <p align="center"><span class="style4">Close</span><font face="Arial" size="4"> Resolution&nbsp;&nbsp;for 
	Release # <%= strRelease %>
    </font>  	</td><td align = center height="25"><font face="Arial"><b><a href="Exception_Search.asp">RETURN</a></b></font></td>

    <td align = center bgcolor="#FFFFFF" height="25">
  &nbsp;</td>
  </tr>
</table><br><br>
<div align="center">
<table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse; height: 198px;" bordercolor="#808080" width="75%">
    <tr>
	 <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="22" align="center" class="style1"  width="50%">
		<strong>Current Resolution</strong></td>
	 <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="22" align="center" class="style1" >
		<strong>Close Exception</strong></td>
   
  </tr>
  
    <tr>
	   <td  bordercolor="#CCCCFF" width = "50%" style="height: 92px; width: 50%" valign="top" class="style3" > <font size="2" face="arial"> <%= strResolution %></font>	
    &nbsp;</td>
	   <td  bordercolor="#CCCCFF" width = "50%" align="center"  > 
	   <% if Session("Ename")  = strAuthor then %> 	
  <Input name="Complete" type="submit" Value="Close" style="float: center" >
  <% else%>
  <font face="arial" size="2"><span class="style6"><strong>You must be the author to close an exception</strong></span>.
  <% end if %></font><br>
	<br>
	<span class="style5"><strong>This will change the Status to Completed</strong></span></td>
    	

  </tr>


  </table>
</div></form>  
  

</body>

 <%
  
  Function SaveData()
 strid = request.querystring("id")
  
  strsql = "Update tblExceptions set status = 'Completed' where id = " & strid

   
  	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
          Response.redirect("Exception_Search.asp")
  End Function
  
   %><!--#include file="AIGfooter.inc"-->