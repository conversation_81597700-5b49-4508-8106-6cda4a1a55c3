																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Edit Release for Order </TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->


<% Dim MyRec, strsql, MyRec2, strsql2,  MyConn3, strsql3,  strRelease, strVendor, strGenerator, strState, strCity, strBD

 

  set objGeneral = new ASP_CLS_General
  strid = Request.querystring("id")
  
  strsql = "Select * from tblOrder where OID = " & strid
  
      Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

  

  if objGeneral.IsSubmit() Then	
  if request.form("Delete") = "ON" then 
  strsql = "Delete from tblOrder where OID = " & strid
  		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

  else

  
  	strRelease = request.form("Release")
 
  If len(request.form("State")) > 0 then	
  strState= Replace(request.Form("State"), "'", "''")
  else
  strState = ""
  end if 
  
   If len(request.form("SAP")) > 0 then	
  strSAP = request.Form("SAP")
  else
  strSAP = ""
  end if 

  If len(Request.form("Vendor")) > 0 then 
  strVendor= Replace(request.Form("Vendor"), "'", "''")
  else
  strVendor = ""
  end if
  
  if len(Request.form("City")) > 0 then 
     strCity = Replace(request.Form("City"), "'", "''") 
     else
     strCity = ""
     end if 
    

	If len(request.form("Generator")) > 0 then  
     strGenerator= Replace(request.Form("Generator"), "'", "''")    
     else
     strGenerator = ""
     end if	
  	
  	If len(request.form("Broke_Description")) > 0 then  
     strBD= Replace(request.Form("Broke_Description"), "'", "''")    
     else
     strBD = ""
     end if	

	strsql =  "Update tblOrder  set PO = '" & request.form("PO") & "', Release = '" & strRelease & "', State = '" & strstate & "', Vendor = '" & strVendor & "', "_
	&" City = '" & strCity & "', Generator = '" & strGenerator & "', Broke_Description = '" & strBD & "', SAP_Nbr = '" & strSAP & "', Species = '" & request.form("Species") & "', "_
	&" Grade = '" & request.form("Grade") & "' where OID = " & strid
	
	
	
	
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
			if request.form("Change") = "ON" then
			strsql = "Update tblCars set PO = '" & request.form("PO") & "', Release_Nbr = '" & strRelease & "', Grade = '" & request.form("Grade") & "', Species = '" & request.form("Species") & "', Vendor = '" & strVendor & "', "_
			&" Generator = '" & strGenerator & "', Gen_City = '" & strCity & "', Broke_Description = '" & strBD & "', Gen_State  =  '" & strstate & "',  SAP_Nbr = '" & strSAP & "' where OID = " & strid
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
			strOID = request.form("OID")
			
			strsq2 = "Select CID from tblCars where OID = " & strid
			 Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    		MyRec2.Open strSQL2, Session("ConnectionString")
			strCID = MyREc2("CID")
			MyREc2.close
			
			strsql = "Update tblCars set OID = " & strOID & " where CID = " & strCid
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

			
			end if


			
end if			

If request.form("web_page") = "p" then
dim strPO
strPO = request.form("po_nbr")	
	
Response.redirect("PO_List.asp?id=" & strPO)	
elseif request.form("web_page") = "i" then
dim strMonth
strmonth = request.form("po_month")
Response.redirect("Import_month_list.asp?id=" & strMonth)		
else
Response.redirect("Maintain_orders.asp")	
end if

end if	
%>

<style type="text/css">
.style3 {
	border: 1px solid #000000;
}
.style4 {
	font-family: arial;
	font-size: x-small;
	text-align: left;
}
.style6 {
	font-family: arial;
	font-size: x-small;
	background-color: #FFFFD7;
}
.style7 {
	text-align: left;
}
.auto-style1 {
	font-family: arial;
	font-size: x-small;
	background-color: #E2F3FE;
}
.auto-style2 {
	background-color: #E2F3FE;
}
</style>
</head>

<body>
<br>
	
<form name="form1" action="Release_Edit.asp?id=<%= strid%>" method="post" ID="Form1">
<input type="hidden" name="web_page" value="<%= request.querystring("p") %>">
<input type="hidden" name="po_month" value="<%= request.querystring("m") %>">
<input type="hidden" name="po_nbr" value="<%= request.querystring("o") %>">

<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Edit PO/Release for PO# <%= MyRec.fields("PO") %></b></font></td>
<td align = right><font face="Arial"><a href="javascript:history.go(-1);">
<strong>RETURN</strong></a><strong>&nbsp;</strong></td>


</tr>
	    </table>



	<p>&nbsp;</p>
	<table id="table1" style="width: 95%" align="center" class="style3">
<tr bgcolor = #CCCCFF>
		<td bgcolor="#FFFFD7" class="auto-style1" style="height: 48">PO #</td>
		<td bgcolor="#FFFFD7" class="auto-style1" style="height: 48">OID</td>
		<td bgcolor="#FFFFD7" class="auto-style1" style="height: 48">Release #</td>
		<td bgcolor="#FFFFD7" class="auto-style1" style="height: 48">Vendor</td>
		<td bgcolor="#FFFFD7" class="auto-style1" style="height: 48">Generator</td>
		<td style="height: 48" class="auto-style2">

<p class="style4">City</td>
		<td bgcolor="#FFFFD7" class="auto-style1" style="height: 48">State</td>
		<td bgcolor="#FFFFD7" class="auto-style1" style="height: 48">Trailer 
		Received</td>
	</tr>
	<tr>
		<td><font face = arial size = 2>
		<input type="text" name="PO" size="20" style="width: 81px" value='<%= MyRec.fields("PO") %>' tabindex="1"></td>
		<td><font face = arial size = 2>
		<input type="text" name="OID" size="20" style="width: 81px" value='<%= MyRec.fields("OID") %>' tabindex="2"></td>
		<td><font face = arial size = 2>
		<input type="text" name="Release" size="20" style="width: 81px" value="<%= MyRec.fields("Release") %>" tabindex="1"></td>
		<td><font face = arial size = 2>
		<input type="text" name="Vendor" size="20" value="<%= MyRec.fields("Vendor") %>" style="width: 235px" tabindex="2"></td>
		<td><font face = arial size = 2>
		<input type="text" name="Generator" size="20" style="width: 281px" value="<%= MyRec.fields("Generator") %>" tabindex="3"></td>
		<td>
		<p align="center" class="style7"><font face = arial size = 2>
<input type="text" name="City" size="39" value="<%= MyRec.fields("City") %>" style="width: 141px" tabindex="4"></td>
		<td>
		<font face = arial size = 2>
<input type="text" name="State" size="39" style="width: 65px" value="<%= MyRec.fields("State") %>" tabindex="5"></td>
		<td><% strOID = MyRec.fields("OID")
		strDelete = "YES"
strsql2 = "Select Date_received from tblCars where OID = " & strid
    Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL2, Session("ConnectionString")
If not MyRec2.eof then
strDelete = "NO" %>
<font size="2" face="Arial"><%= MyRec2.fields("Date_received")%></font>

<% end if 
MyRec2.close%>

		&nbsp;</td>
	</tr>
	<tr bgcolor = #CCCCFF>
		<td bgcolor="#FFFFD7" class="auto-style1" style="height: 46">

SAP #</td>
		<td bgcolor="#FFFFD7" class="auto-style1" style="height: 46">

		&nbsp;</td>
		<td bgcolor="#FFFFD7" class="auto-style1" style="height: 46">

Species</td>
		<td bgcolor="#FFFFD7" class="auto-style1" style="height: 46">

Grade</td>
		<td bgcolor="#FFFFD7" class="auto-style1" style="height: 46">

Broke Type</td>
		<td style="height: 46" class="auto-style2">

<p class="style4">Import Month</td>
		<td bgcolor="#FFFFD7" class="auto-style1" style="height: 46">Weigh Trailer</td>
		<td bgcolor="#FFFFD7" class="auto-style1" style="height: 46"><% if strDelete = "YES" Then %>Delete This 
		Record
		<% else %>
		Change Information on Receipt<% end if %></td>
	</tr>
	<tr>
		<td style="height: 32px"><font face = arial size = 2><input type="text" name="SAP" size="20" style="width: 81px" value='<%= MyRec.fields("SAP_Nbr") %>' tabindex="1"></td>
		<td style="height: 32px">&nbsp;</td>
		<td style="height: 32px"><font face = arial size = 2><input type="text" name="Species" size="20" style="width: 81px" value='<%= MyRec.fields("Species") %>' tabindex="1"></td>
		<td style="height: 32px"><font face = arial size = 2><input type="text" name="Grade" size="20" style="width: 81px" value='<%= MyRec.fields("Grade") %>' tabindex="1"></td>
		<td style="height: 32px"><font face = arial size = 2>
	<input type="text" name="Broke_Description" size="30" style="width: 281px" value="<%= MyRec.fields("Broke_Description") %>" tabindex="3"></td>
		<td style="height: 32px" align="center" class="style7">		<font face = arial size = 2>
<%= MyRec.fields("Import_Month") %></td>
		<td style="height: 32px"><font face = arial size = 2><%= MyRec.fields("Weigh_Trailer") %></td>
		<td style="height: 32px"><% if strDelete = "YES" then %><input type="checkbox" name="Delete" value="ON">
		<% else %>
		<input type=checkbox name="Change" value="ON"><% end if %>&nbsp;</td>
	</tr>

</table>
<br>
		

<p align="center"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></p>
</form>
<% MyRec.close %><!--#include file="Fiberfooter.inc"-->