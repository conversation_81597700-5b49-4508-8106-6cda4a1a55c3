﻿ <!--#include file="classes/asp_cls_SessionStringESL.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<%
 
Dim strID
Dim strUserName2
Dim strUserPassword
Dim strErrorMessage
 
strID = Request.querystring("id")
strUserPassword = Request.querystring("p")
strErrorMessage = "Invalid username or password. Please try again!"

Const ADS_SECURE_AUTHENTICATION       = &H1
 

strUserName2 = "kcus\" & strID
strUserPassword = strUserPassword
 
On Error Resume Next
Err.Clear
 
Set dso = GetObject("LDAP:")
Set domain = dso.OpenDSObject("LDAP://kcc.com", strUserName2, strUserPassword, ADS_SECURE_AUTHENTICATION)
 

If Err.number <> 0 then
'Response.write("This is the error" & err.number)
  Response.Redirect ("Home.asp")
 
Else

Session("Login_ID") = strID
'Response.write("This is the session" & Session("Login_id"))
  Response.Redirect ("CSE_Start.asp")
End If
 

Set dso = Nothing
Set domain = Nothing
 
 
 
%>

