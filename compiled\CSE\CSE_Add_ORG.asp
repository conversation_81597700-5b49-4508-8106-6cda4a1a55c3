<html>
<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_Procedure.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Add Energy Control Procedure</title>
<style>
<!--
table.MsoTableGrid
	{border:1.0pt solid windowtext;
	font-size:10.0pt;
	font-family:"Times New Roman";
	}
.style2 {
	text-align: center;
}
-->
</style>
</head>
<% dim strsql, MyRec, strid, strecp, strTask, objGeneral, strDescription, strLocation, strComments, strDate, strTeam, strAsset
Dim objEPS, rstTeam, rstWA, strArea, strWorkArea,  strSOP, strFunctional, strListone, strListtwo, strListthree, strType

			
Dim strBID, strE_name, strP_Date, strSpaceType
			
strBID = Session("EmployeeID")
strE_name = Session("Ename")
strP_date = formatdatetime(Now(),2)
strSpaceType = "Space Evaluation"

strDate = formatdatetime(now(),2)
Call getdata()

set objGeneral = new ASP_CLS_General
  		 if objGeneral.IsSubmit = True Then
  		
 	
if isnull(Request.form("Description")) then
strDescription = ""
else
strDescription = Replace(Request.form("Description"), "'", "''") 
end IF

if isnull(Request.form("Location")) then
strLocation = ""
else
strLocation= Replace(Request.form("Location"), "'", "''") 
end if

if isnull(Request.form("Comments")) then
strComments = ""
else
strComments = Replace(Request.form("Comments"), "'", "''") 
end if

if isnull(Request.form("Functional_location")) then
strFunctional = ""
else
strFunctional= Replace(Request.form("Functional_location"), "'", "''") 
end if

if isnull(Request.form("Asset")) then
strAsset = ""
else
strAsset = Replace(Request.form("Asset"), "'", "''") 
end if

strWorkArea = Request.form("Workarea")


strType = "CSE"

 strsql =  "INSERT INTO tblSOP (Location_type,  Area,  AssignedAsset, Sdate, TeamName, WorkArea, Sdescription, Location, Comment, [Functional_location]) "_
 &" SELECT '" & strType & "',  '" & strArea & "', '" & strAsset & "',  "_
 &" '" & Request.form("Date") & "', '" & Request.form("Team") & "', '" & strWorkarea & "', '" & strDescription & "', "_
 &" '" & strLocation & "', '" & strComments & "', '" & strFunctional & "'"
 Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 
			
			

 strsql = "SELECT Max(tblSOP.SID) AS MaxOfSID FROM tblSOP "

Set MyRec = Server.CreateObject("ADODB.Recordset") 
   		MyRec.Open strSQL, Session("ConnectionString")
 strid = MyRec.fields("MaxOfSID")
 MyRec.close

strSOP =  Replace(Request.form("SOP_NO"), "'", "''")

strsql = "Update tblSOP set SOP_NO = '" & strSOP & "', SpaceStatus = 'In Use' where SID = " & strid & ""
Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 

		

		
	strsql = "Insert into tblActionLog (SOP_ID, Type, BID, E_name, P_date) Select " & strid & ", "_
	&" '" & strSpaceType & "', '" & strBID & "', '" & strE_name & "', '" & strP_Date & "'"
			Set MyRec = Server.CreateObject("ADODB.Connection")
			MyRec.Open Session("ConnectionString")
			MyRec.Execute strSQL
			MyRec.Close 
 

Response.redirect("CSE_Add_Two.asp?id=" & strid)			

end if 

 %><body>
 <p align="left"><b><font face = arial size = 3>Add New Confined Space:</font></b><font face="Arial">
 </font>
 </p>
	<form name="form1" action="CSE_Add.asp"  method="post" ID="Form1"  >
 
 <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#D9E1F9" height="60">
   
  <tr>
    <td bgcolor="#FFFFDD" align="center" bordercolordark="#000000" bordercolor="#FFFFDD">
	<font face="Arial" size="2">Date</font></td>

   
    <td bgcolor="#FFFFDD" align="center" bordercolordark="#000000" bordercolor="#FFFFDD">
	<font face="Arial" size="2">Team</font></td>

   
    <td bgcolor="#FFFFDD" align="center" bordercolordark="#000000" bordercolor="#FFFFDD">
	<font face="Arial" size="2">Work Area</font></td>

    <td bgcolor="#FFFFDD" bordercolordark="#000000" bordercolor="#FFFFDD" class="style2">
	<font face="Arial" size="2">Space ID</font></td>

    <td bgcolor="#FFFFDD" align="left" width="332" bordercolordark="#000000" bordercolor="#FFFFDD">
	<font face="Arial" size="2">Functional Location</font></td>

   
  </tr>
  <tr>
    <td  bgcolor="#FFFFDD" bordercolor="#FFFFDD"  >
	<p align="center"> <font face = arial size = 2>
	<input type="text" name="Date" size="10" value="<%= strDate %>" ></td>
	
    <td  bgcolor="#FFFFDD" bordercolor="#FFFFDD"  >
	<p align="center">  <font face="Arial">    <select name="Team" tabindex="1">
       <option value="">--- All ---</option>
       <%= objGeneral.OptionListAsString(rstTeam, "Team", "Team", strTeam) %>
     </select></font></td>
	
    <td  bgcolor="#FFFFDD" bordercolor="#FFFFDD"  >
	<p align="center">  
		<font face="Arial">  <select name="Workarea" tabindex="2">
       <option value="">--- All ---</option>
       <%= objGeneral.OptionListAsString(rstWA, "WorkArea", "WorkArea", strWorkArea) %>
     </select></font></td>
       <td  bgcolor="#FFFFDD" width="332" bordercolor="#FFFFDD" class="style2"  >

		<font face = arial size = 2>
	<input name="SOP_NO" size="25" style="float: center" value="" maxlength="25" tabindex="3" ></td>

	
    <td  bgcolor="#FFFFDD" align="left" width="332" bordercolor="#FFFFDD"  >
	<p align="center">
		<font face = arial size = 2>
	<input name="Functional_location" size="42" style="float: left" value="<%= strFunctional %>" tabindex="4" ></td>
	
  </table> 

 

 
 <div align="center">

 

 
 <table border="1" cellpadding="0" style="border-collapse: collapse" bordercolor="#808080" width="100%" bgcolor="#D9E1F9" id="table1">
   
  <tr>
    <td bgcolor="#FFFFDD" align="center" bordercolor="#FFFFDD">
	<p align="left">
	<font face="arial" size="2">Name of New Confined Space</font></td>

   
    <td bgcolor="#FFFFDD" align="center" bordercolor="#FFFFDD">
	<p align="left">
	<font face="arial" size="2">Assigned Asset</font></td>

   
  </tr>
  <tr>
    <td  bgcolor="#FFFFDD" bordercolor="#FFFFDD"  >
	<p align="center"><font face="Arial" size="2">
	<input name="Description" size="87" style="float: left" value="<%= strDescription %>" tabindex="5" ></font></td>
	
    <td  bgcolor="#FFFFDD" bordercolor="#FFFFDD"  >
	<p align="center">
		<font face = arial size = 2>
	<input name="Asset" size="42" style="float: left" value="<%= strAsset %>" tabindex="6" ></td>


  
  <tr>
    <td bgcolor="#FFFFDD" align="left" bordercolor="#FFFFDD" >
	
	<font face="arial" size="2">Space Location</font></td>

   
    <td bgcolor="#FFFFDD" align="left" bordercolor="#FFFFDD">
	
	&nbsp;</td>

   
  </tr>
  <tr>
    <td  bgcolor="#FFFFDD" height="26" bordercolor="#FFFFDD"  >
	<font face="Arial" size="2">
	<input name="Location" size="87" style="float: left" value="<%= strLocation %>" tabindex="7" ></font></td>
	
    <td  bgcolor="#FFFFDD" height="26" bordercolor="#FFFFDD"  >

		&nbsp;</td></tr>
  
  <tr>
    <td bgcolor="#FFFFDD" align="left" colspan = 2 bordercolor="#FFFFDD">
	
	<font face="arial" size="2">Comments</font></td>

   
  </tr>
  <tr>
    <td  bgcolor="#FFFFDD"  bordercolorlight="#D9E1F9" colspan = 2 bordercolor="#FFFFDD"  >
	<font face="Arial" size="2">
	<input name="Comments" size="139" style="float: left" value="<%= strComments %>" tabindex="8" ></font></td>
	
  </table>
  </div>
  <p>&nbsp;</p>

	<p>&nbsp;<INPUT TYPE="submit" value="Continue"></p>
	</form>

   <%  Function GetData()
        set objEPS = new ASP_CLS_ProcedureESL
        set rstTeam = objEPS.ReadTeamList()
 	set rstWA = objEPS.ReadWorkArea()


    End Function %><!--#include file="footer.inc"-->