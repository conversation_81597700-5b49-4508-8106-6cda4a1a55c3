
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Add New Technician</TITLE>
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_Session.asp"-->
<!--#include file="classes/asp_cls_DataAccess.asp"-->

</head>
<style type="text/css">
.style1 {
	font-family: Arial;
}
.style3 {
	border-collapse: collapse;
	border-style: solid;
	border-width: 1px;
}
</style>

<% Dim strName, strBID, strSite, strType


 set objGeneral = new ASP_CLS_General


if objGeneral.IsSubmit() Then


	Call SaveData() 

End if %>

<body><form name="form1" action="Tech_add.asp" method="post">
 
<table border="0" cellpadding="0" cellspacing="0" width = 60% style="border-collapse: collapse" bordercolor="000000" height="25" align = center >
  <tr>
    <td  bgcolor="#FFFFFF" bordercolor="#CCCCFF" height="25" >
    <p align="center"><font face="Arial" size="4">Add New Technician</font></td>
    <td align = center height="25"><font face="Arial"><b><a href="Technicians.asp">RETURN</a></b></font></td>

    <td align = center bgcolor="#FFFFFF" height="25">
  <Input name="Submit" type="submit" Value="Submit" style="float: right" ></td>
  </tr>
</table><br><br>
<div align="center">
<table cellpadding="0" cellspacing="0" width="50%" bordercolor="#808080"  height="10" class="style3">
    <tr>
<td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="22" align="center" ><b>
	<font face="Arial">Name (Last, First) </font></b></td>
   
  <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="22" align="center" class="style1" >
	<strong>Employee ID</strong></td>
   
  <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="22" align="center" class="style1" >
	<strong>Crew</strong></td>
   
  <td  bgcolor="#FFFFD7" bordercolor="#CCCCFF" height="22" align="center" class="style1" style="width: 186px" >
	<strong>Machine</strong></td>
 
  </tr>
  
    <tr>
    <td  bordercolor="#CCCCFF" height="47"  align="center" >   <font face="Arial">	
		<input type="text" name="Name" size="35" value="" style="width: 177px" tabindex="1">
</font></td>
    	

    <td  bordercolor="#CCCCFF" height="47"  align="center" >  	
     <font face="Arial">	
		<input type="text" name="BID" size="35" value="" style="width: 75px" tabindex="2"></font></td>


    <td  bordercolor="#CCCCFF" height="47"  align="center" >  	
     <font face="Arial">
       <select name="Crew" size="1">
           <option  value="">Select</option>
 
     		<option>A</option>
			<option>B</option>
			<option>C</option>
			<option>D</option>
 
     </select></font></td>

	<td  bordercolor="#CCCCFF" height="47"  align="center" class="style6" >  	
     <font face="Arial">
       <select name="Machine" size="1">
     <option selected value="">Select</option>

    <option >Perini 1/3</option>
    <option >Perini 2/4</option>
    <option >Perini 5</option>
      
     </select></font></td>
  </tr>
 
  </table>
</div>



</form>
   
  

</body>
 <%

  
  Function SaveData()



strName = Replace(Request.form("Name"), "'", "''")   
strBID = Request.form("BID")  

strMachine = Request.form("Machine")
strCrew = Request.form("Crew")

 
  strsql = "Insert into tblOT_Employee (Emp_Name, Emp_ID, Machine, Crew) "_
  &" Values('" & strName & "', '" & strBID & "', '" & strMachine & "', '" & strCrew & "')"

   
  	 set MyRec = new ASP_CLS_DataAccess
         MyRec.ExecuteSql strSql 
         
          Response.redirect("Technicians.asp")
  End Function
  
   %><!--#include file="footer.inc"-->