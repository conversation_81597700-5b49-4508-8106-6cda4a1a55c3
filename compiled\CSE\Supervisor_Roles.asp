
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Entry Supervisors</TITLE>

<style type="text/css">
.auto-style1 {
	font-family: Arial, Helvetica, sans-serif;
}
</style>
</head>

<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_Procedure.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<%    Dim   rstTeam, strTeam, rstESL, rstWA, strWA,  strID, objEPS, strName, strName2, strTraining, bdate

    set objGeneral = new ASP_CLS_General %>
	<body>

<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% border=1>
  <tr><td colspan="4" bgcolor="white" class="subheader">&nbsp;</td></tr>

 <TD>
     <p align="left"><font size="3" face="Arial"><b>Entry Supervisors</b>&nbsp;&nbsp;</font></td>
		<td align = right><a href="Supervisor_roles.asp"><font face="Arial"><b>Return</b></font></td></tr>
	    </table><font face="Arial"><br>
	    <%  
	        Dim SCRIPT_NAME
		Dim strSQL, gWorkArea
		Dim BACK_TO_LIST_TEXT

		SCRIPT_NAME = Request.ServerVariables("SCRIPT_NAME")

		BACK_TO_LIST_TEXT = "<p>Click <a href=""" & SCRIPT_NAME & """>" _
    		    & "here</a> to go back to record list.</p>"
    		    
    		    gWorkArea = ""
    		    
    		strSQL = "SELECT * from tblAuthorizers where P_BID = '" & Session("Employeeid") & "'"
		        Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly	
			
If not MyRec.eof then
gWorkArea = MyRec.fields("Work_Area")
end if
MyRec.close
	    
  strPost = ""  		    
    If len(gWorkArea) > 2 then	 
    strPost = "OK"
    end if   
    
		Select Case LCase(Trim(Request.QueryString("action")))
		    Case "add"
		    %>
			</font>
			<form action="<%= SCRIPT_NAME %>?action=addsave" method="post">

		            <font face="Arial">
		            <br><b>Work Area:</b> 
			    <br><font face="Arial"> <select name="Work_area">
       <option value="">--- Select ---</option>
       	 <% strsql = "Select WorkArea from WorkAreas order by WorkArea"
       Set rstWA = Server.CreateObject("ADODB.Recordset")
			rstWA.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
While not rstWA.eof
%>
<option <% if strWA = rstWA.Fields("WorkARea") then %> selected <% end if %>><%= rstWA("Workarea") %></option>
<% rstWA.movenext
wend
rstWA.close %>

     </select></font>
			    <br>
			    <br><b>BID:    </b> 
			    <br><Input name="BID" size="13">
			    			 

			    	</font>
			    		 	   	      <br>
			    	    <br><b><span class="auto-style1">Shift:</span>    </b> 
			    	    <br>
                   <select name="Shift">
                    <option value="">Select Shift</option>
                     <option>A</option>
                         <option>B</option>
 					   <option>C</option>
					   <option>D</option>
 					   <option>Days</option>

                          </select>
			    	   	    </font>

			<p><font face="Arial">
		            <b>Last Training Date:    </b> 
			    <br><Input name="Training" size="13">&nbsp; (mm-dd-yyyy)<br>
			 

			    	</font>

			    <br><Input name="Update" type="submit" Value="Submit" >
 			</p>
 			</form>
                    <%
	            

 		    Case "addsave"
 		   
   		bdate = IsDate(Request.Form("Training"))
       
 
                If bDate = True then 
                    strTraining = Request.form("Training")
                else
                   strTraining = Null
                end if
         strName2 = ReturnEname(Request.Form("BID"))
         
         strName2 = Replace(strName2, "'", "''")
    
	
			strSQL = "Insert into tblAirEntrySups (Work_area, P_BID, P_Name,  P_Training, P_Type, work_shift) Values('" & Request.Form("Work_area") & "', " _
			   & "'" & Request.Form("BID") & "', '" & strName2 & "',  '" & strTraining & "', 'S', '" & Request.form("Shift") & "')"

			
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

			Response.Write("Record has been added successfully.")

			
	

		    Case "edit"
			iRecordId = Request.QueryString("id")
			strSQL = "Select * From tblAirEntrySups Where NID = " & iRecordId

			Set MyRec = Server.CreateObject("ADODB.RecordSet")
			MyRec.Open strSQL, Session("ConnectionString")

			If Not MyRec.EOF Then
			    %>
				<form action="<%= SCRIPT_NAME %>?action=editsave" method="post">
				
				    <input type="hidden" name="id" value="<%= MyRec.Fields("NID").Value %>" />
				    <% strWA = MyRec.fields("Work_Area")%>
		            	    <font face="Arial">
		            	    <br><b>Work Area:</b> 
			    	    <br>
                    <font face="Arial">
       <select name="Work_area">
       <option value="">--- All ---</option>
            	 <% strsql = "Select WorkArea from WorkAreas order by WorkArea"
       Set rstWA = Server.CreateObject("ADODB.Recordset")
			rstWA.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
While not rstWA.eof
%>
<option <% if strWA = rstWA.Fields("WorkARea") then %> selected <% end if %>><%= rstWA("Workarea") %></option>
<% rstWA.movenext
wend
rstWA.close %>
     </select>
    </font></TD>
			    	    <br>
			    	    <br><b>BID:    </b> 
			    	    <br>
                    <input Name="BID" value="<%= MyRec.Fields("P_BID").Value %>" size="10" />
			    	   	    </font>
			    	   	      	   	      <br>
			    	    <br><b><span class="auto-style1">Shift:</span>    </b> 
			    	    <br>
                   <select name="Shift">
                    <option value="">Select Shift</option>
                     <option <% if MyRec.fields("Work_shift") = "A" then %> selected<% end if %>>A</option>
                         <option <% if MyRec.fields("Work_shift") = "B" then %> selected<% end if %>>B</option>
 					   <option <% if MyRec.fields("Work_shift") = "C" then %> selected<% end if %>>C</option>
					   <option <% if MyRec.fields("Work_shift") = "D" then %> selected<% end if %>>D</option>
 					   <option <% if MyRec.fields("Work_shift") = "Days" then %> selected<% end if %>>>Days</option>

                          </select>
			    	   	    </font>

					<p><b><font face="Arial">Last Training Date</font></b><font face="Arial"><b>:    </b> 
			    	    <br>
                    <input Name="Training" value="<%= MyRec.Fields("P_Training").Value %>" size="15" />&nbsp; 
					(mm-dd-yyyy)<br>
			
		
			    	   	    <br>
					<Input name="Update" type="submit" Value="Submit" > 
				</p>
				</form>
			    <%
			End If

		
		

		    Case "editsave"
		       strName2 = ReturnEname(Request.Form("BID"))
			iRecordId = Clng(Request.form("id"))
			   
   		bdate = IsDate(Request.Form("Training"))
       
 
                If bDate = True then 
                    strTraining = Request.form("Training")
                else
                   strTraining = Null
                end if
		
			strSQL = "Update tblAirEntrySups Set Work_area = '" & Request.form("Work_area") & "',  P_BID = '" & Request.Form("BID") & "', "_
			&" P_Name = '" & strName2 & "', P_Training = '" & strTraining & "', work_shift = '" & Request.form("Shift") & "' Where NID = " & iRecordId

			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

			Response.Write("Record has been updated successfully.")

		

		    Case "delete"
	        ' Get the id to delete
			iRecordId = Clng(Request.QueryString("id"))
			

			strSQL = "DELETE FROM tblAirEntrySups WHERE NID = " & iRecordId 

			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

			Response.Write("Record has been deleted successfully.")


		    Case else   'Default view
	strSQL = "SELECT tblAirEntrySups.NID,  tblAirEntrySups.Work_area, tblAirEntrySups.work_shift, tblAirEntrySups.P_BID, tblAirEntrySups.P_Name, "_
			&"	tblAirEntrySups.P_Training FROM tblAirEntrySups  "		
		       
		       
			 Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
			%>
			<table Border=1 bgcolor = #F0F0F0 BORDERCOLOR = #CCCCFF cellpadding = 3 width = 75% align = center>
			<thead>
			    <tr>
		
                	
                		<th bgcolor="#FDFEE9"> <font face="arial" size="2">Work Area</font></th>
	     		
                		<th bgcolor="#FDFEE9"> <font face="arial" size="2">Name</font></th>               		
			
<th bgcolor="#FDFEE9"> <font face="arial" size="2">Shift</font></th>   
	     	
                	
                		<th bgcolor="#FDFEE9"> <font size="2" face="Arial">Last Training Date</font></th>
                		<% if strPost = "OK" Then %>
                      	<th bgcolor="#FDFEE9"> <font size="2" face="Arial">&nbsp;</font></th>
                       	<th bgcolor="#FDFEE9"> <font size="2" face="Arial">&nbsp;</font></th>
                       	<% end if %>
              	    </tr>
			    </thead>
			    <tbody>
	
        		        <% While not MyRec.EOF %>
              			    <tr>
               <td bgcolor="#FFFFFF"><font face="Arial"><%= MyRec.Fields("Work_area").Value %></font></td>
              		   <td bgcolor="#FFFFFF"><font face="Arial"><%= MyRec.Fields("P_Name").Value %></font></td>
              		       		   <td bgcolor="#FFFFFF" align="center"><font face="Arial"><%= MyRec.Fields("work_shift").Value %>&nbsp;</font></td>

              		      <td align = center bgcolor="#FFFFFF"><font face="Arial"><%= MyRec.Fields("P_Training").Value %></font></td>
              		  <% if strPost = "OK" then %>    
					<td align="center" bgcolor="#FFFFFF"><font face="Arial"><a href="<%= SCRIPT_NAME %>?action=edit&id=<%= MyRec.Fields("NID").Value %>">Edit</a></font></td>
              			 <td align="center" bgcolor="#FFFFFF"><font face="Arial"><a href="<%= SCRIPT_NAME %>?action=delete&id=<%= MyRec.Fields("NID").Value %>">Delete</a></font></td>
  					<% end if %>	

 </tr>
                            
              			    <% 
				        MyRec.MoveNext
                 			WEND
                 			MyRec.Close
              			    %>

			    </tbody>
			    <tfoot>
			    <% if strPost = "OK" then %>
				<tr>
				    <td colspan="3" align="right"><font face="Arial"><a href="<%= SCRIPT_NAME %>?action=add">Add a new record</a></font></td>
				</tr>
				<% end if %>
			    </tfoot>

            		</table>
		<font face="Arial">
		<%					
		End Select
		%>                    
      	</font>                    
      	</body>
      	
      	 <!--#include file="footer.inc"-->