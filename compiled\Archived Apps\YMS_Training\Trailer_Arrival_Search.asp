 
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Recovered Paper Arrivals</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

<%  
	Dim strsQL, rstEquip

   	Dim objEquipSearch, rstTotals, strCount, strDed, strNet, strTons, rstTotalReceived, strTR, objNew, rstSpecies, strTT, strTD, strTN, strTL
  	Dim  strVendor, strBegDate, strEndDate, strSpecies, strMonth, strDateReceived, strDateAdded, rstMonth
 	Dim  rstVendor
  	Dim objGeneral, strPO, gcount, strShuttle
   	
   	Dim intDirection
  	Dim intPageNumber
   	Dim strPageNav
   	
   	Dim strTonsTotal, strDeductionTotal, strNetTotal
   	strTonsTotal = 0
   	strDeductionTotal = 0
   	strNetTotal = 0

   intPageNumber = 1

   set objGeneral = new ASP_CLS_General

  if objGeneral.IsSubmit() Then
    Call GetFormData()
    Select Case intDirection
      Case 10: 'goto mocinfo.asp
  
      Case Else:
        Call LoadSearchResults()
    End Select
  else
    intDirection = 0
  end if
Call GetData()
%>

<script language="javascript">
 
  function GotoMOC(MOCID)
  {
    document.forms["form1"].elements["tbl_ID"].value = MOCID;
    document.forms["form1"].elements["Direction"].value = 10;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }

  function Search()
  {
    
    document.forms["form1"].elements["Direction"].value = 1;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }

  function NextPage()
  {
    document.forms["form1"].elements["Direction"].value = 5;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }
  function PrevPage()
  {
    document.forms["form1"].elements["Direction"].value = 6;
    //alert( document.forms["form1"].elements["Direction"].value);
    document.forms["form1"].submit();
  }
</script>


<style type="text/css">
.auto-style1 {
	border: 2px solid #C0C0C0;
}
</style>
</head>


<form name="form1" action="Trailer_arrival_Search.asp" method="post">
 <input type="hidden" name="Direction" value="1" >
 <input type="hidden" name="tbl_ID" value="" >
 <input type="hidden" name=PageNumber value="<%=intPageNumber%>" >
 
 
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=70%  border=1 align = CENTER>  
  <tr><td  align = left><font face = "Arial" size = "3"><b>
	<font face="Arial">Recovered Paper </font></b></font><b><font face="Arial">
	Arrivals</font></b></td>
	<td align = "RIght"><font face="Arial"><b><a href="Loginscreen.asp">HOME</a></b></font></td></tr></table>
	<div align="center">
<TABLE cellSpacing=0 cellPadding=0 width=70% class="auto-style1">  	
	
  <TR>
    <TD bgcolor="#F4F4FF" bordercolor="#808080"><font face="Arial" size="2"><b>Beginning Arrival Date:</b></font></TD>
   <TD bgcolor="#F4F4FF" bordercolor="#808080">
   <font face="Arial"><input name="BegDate" size="10" maxlength="10" value="<%=strBegDate%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633"></td>

    <TD bgcolor="#F4F4FF" bordercolor="#808080"><font face="Arial" size="2"><b>Ending Arrival Date:</b></font></TD>
    <TD bgcolor="#F4F4FF" bordercolor="#808080">
   <font face="Arial"><input name="EndDate" size="10" maxlength="10" value="<%=strEndDate%>" style="padding:0; border:1px solid slategray; bgcolor:#ff6633"></td>
     <TD bgcolor="#F4F4FF" bordercolor="#808080"></td> </tr>
   
  <TR > <TD bgcolor="#F4F4FF" bordercolor="#808080"><font face="Arial" size="2"><b>Species:</b></font></TD>
  <TD bgcolor="#F4F4FF" bordercolor="#808080"> <select size="1" name="Species">

     <option value="">--- All ---</option>
       <%= objGeneral.OptionListAsString(rstSpecies, "Species", "Species", strSpecies) %>
     </select></td><td bgcolor="#F4F4FF" bordercolor="#808080"><b>
	<font face="Arial" size="2">Shuttles / Direct</font></b></td>
	<td bgcolor="#F4F4FF" bordercolor="#808080"><select size="1" name="Shuttle">
	<option selected >ALL</option>
	<option <% if strShuttle = "YES" then %>selected <% end if %> value="YES">SHUTTLES</option>
	<option <% if strShuttle = "NO" then %>selected <% end if %> value="NO">DIRECT</option>
	</select></td>
   
	<td bgcolor="#F4F4FF" bordercolor="#808080">
	<input type="button" onClick="javascript:Search()" value="Search" caption="Search" style="float: right"></TD></TR>
  </TABLE></div>
</form>


  <% if objGeneral.IsSubmit() Then 

%>
   <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=92% border=1 align = center>
    <tr><td colspan="12" bgcolor="white"><font face="Arial" size="2"><b>Search Results</b> - Page <%=intPageNumber%></font></td>
    <td  align="center"><font face="Arial" size = 2>Total Number of Loads:<br>&nbsp;<%=strTL%></font></td>

    </tr>
    <tr><td colspan="14" bgcolor="white" align="right"><%=strPageNav%>&nbsp;</td></tr>
      <tr class="tableheader">
 	<td  align="center" height="30"><font face="Arial" size = 1>Species</font></td>
	<td  align="center" height="30"><font face="Arial" size = 1>SAP Number</font></td>
	<td  align="center" height="30"><font face="Arial" size = 1>PO</font></td>
	<td  align="center" height="30"><font face="Arial" size = 1>Trailer</font></td>
		
	<td  align="center" height="30"><font face="Arial" size = 1>Carrier</font></td>
		<td  align="center" height="30"><font face="Arial" size = 1>Date<br> Received</font></td>
			<td  align="center" height="30"><font face="Arial" size = 1>Release</font></td>
	<td  align="center" height="30"><font face="Arial" size = 1>Date<br>Unloaded</font></td>
	<td  align="center" height="30"><font face="Arial" size = 1>Shuttle<br>Trailer</font></td>
		<td  align="center" height="30"><font face="Arial" size = 1>Shuttle<br>Carrier</font></td>

	<td  align="center" height="30"><font face="Arial" size = 1>Shuttle<br> Date</font></td>

	<td  align="center" height="30"><font face="Arial" size = 1>Shuttle<br>Date<br>Unloaded</font></td>

	
	<td  align="center" height="30"><font face="Arial" size = 1>Tons</font></td>
	<td  align="center" height="30"><font face="Arial" size = 1>Deduction</font></td>
	<td  align="center" height="30"><font face="Arial" size = 1>Net</font></td>

      
  	<% 
      Dim ii
       ii = 0
       while not rstEquip.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr class=tablecolor1>
    <% else %>
       <tr class=tablecolor2>
    <% end if %>

<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Species")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Sap_nbr")%>&nbsp;</td>

<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("PO")%>&nbsp;</td>

<td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("Trailer")%>&nbsp;</td>
<td  align="left"><font face = "arial" size = "1"><%=rstEquip.fields("Carrier")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("date_recejved")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Release")%>&nbsp;</td>
<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Date_unloaded")%>&nbsp;</td>
<td  > <font size="1" face="Arial">&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; <%= rstEquip.fields("Transfer_trailer_nbr")%></font></td>
<td  >   <font size="1" face="Arial">        <%= rstEquip.fields("Trans_Carrier")%>&nbsp;</font></td>

	<td align = center ><font size="1" face="Arial"><%= rstEquip.fields("Transfer_Date")%></font></td>


<td  align="center"><font face = "arial" size = "1"><%=rstEquip.fields("Trans_unload_date")%>&nbsp;</td>

	<td  align="right"><font face="Arial" size = 1>
	<%  if len(rstEquip.fields("Tons_received")) > 0 then
	      strTonsTotal = strTonsTotal + rstEquip.fields("Tons_received")
	    end if %>
	<%= rstEquip.fields("Tons_received")%>&nbsp;</font></td>
	<td  align="right"><font face="Arial" size = 1>
	<% if len(rstEquip.fields("Deduction")) > 0 then
	     strDeductionTotal = strDeductionTotal + rstEquip.fields("Deduction") 
	   end if%>
	<%= rstEquip.fields("Deduction")%>&nbsp;</font></td>
	<td  align="right"><font face="Arial" size = 1>
	<% if len(rstEquip.fields("Net")) > 0 then
	     strNetTotal = strNetTotal + rstEquip.fields("Net") 
	   end if %>
	<%= rstEquip.fields("Net")%>&nbsp;</font></td>
		
  
   </tr>
    <% 
       ii = ii + 1
       rstEquip.MoveNext
     Wend
    %>
   </table>
<table>    <tr><td colspan="7" bgcolor="white" align="right"><%=strPageNav%>&nbsp;</td></tr></table>

<div align="center"><div style="width:92%" align="left"><font face="Arial" size="2"><u>Totals</u><br>
Tons: <%= formatnumber(strTT,0) %><br>
Deduction: <%= formatnumber(strTD,0) %><br>
Net: <%= formatnumber(strTN,0) %><br>
</font></div></div>
<% end if %><% Function GetData()

   set objNew = new ASP_Cls_Fiber
          
	set rstSpecies = objNew.FiberSpecies()
	set rstMonth= objNew.FiberMonth()



End Function
    Function GetFormData()
      intDirection = cint(Request.Form("Direction"))

 	strBegDate = request.form("BegDate")
	strEndDate = request.form("EndDate")
		strSpecies = request.form("Species")
		strShuttle = request.form("Shuttle")
	


      intPageNumber = cint(Request.Form("PageNumber"))

 
    End Function

    Function LoadSearchResults()
      Dim objEquipSearch, objTotals

      if intDirection =1 Then
         intPageNumber = 1
      end if

      if intDirection = 5 Then
         intPageNumber = intPageNumber + 1
      end if
      if intDirection = 6 Then
         intPageNumber = intPageNumber - 1
      end if


	strBegDate= request.form("BegDate")
	strEndDate = request.form("EndDate")


      set objEquipSearch = new ASP_Cls_Fiber
    
 If request.form("Shuttle") = "YES" and len(strSpecies) > 0 then
   set rstEquip = objEquipSearch.RPSearchTA_Shuttles(strBegDate, intPageNumber, strEndDate, strSpecies)
 
   strsqlt = "Select count(tblcars.cid) as CountofOID, Sum(tblCars.Tons_received) AS SumOfTons_received, Sum(tblCars.Net) AS SumOfNet, Sum(tblCars.Deduction) AS SumOfDeduction "_
   &" FROM tblOrder RIGHT JOIN tblCars ON tblOrder.Release = tblCars.Release_nbr where tblcars.cid > 0 "

   if len(strBegDate) > 2 then
     strsqlt = strsqlt & " and transfer_date >= '" & strBegDate & "'"
   end if
   if len(strEndDate) > 2 then
     strsqlt = strsqlt & " and transfer_date <= '" & strEndDate & "'"
   end if
   if len(strSpecies) > 0 then
     strsqlt = strsqlt & " and tblcars.species = '" & strSpecies & "'"
   end if

 elseif request.form("Shuttle") = "YES" then
   set rstEquip = objEquipSearch.RPSearchTA_ShuttlesNull(strBegDate, intPageNumber, strEndDate, strSpecies)
 
   strsqlt = "Select count(tblcars.cid) as CountofOID, Sum(tblCars.Tons_received) AS SumOfTons_received, Sum(tblCars.Net) AS SumOfNet, Sum(tblCars.Deduction) AS SumOfDeduction "_
   &" FROM tblOrder RIGHT JOIN tblCars ON tblOrder.Release = tblCars.Release_nbr where tblcars.cid > 0 "

   if len(strBegDate) > 2 then
     strsqlt = strsqlt & " and transfer_date >= '" & strBegDate & "'"
   end if
   if len(strEndDate) > 2 then
     strsqlt = strsqlt & " and transfer_date <= '" & strEndDate & "'"
   end if
   if len(strSpecies) > 0 then
     strsqlt = strsqlt & " and tblcars.species = '" & strSpecies & "'"
   end if

 elseif request.form("Shuttle") = "NO" and len(strSpecies) > 0 then
   set rstEquip = objEquipSearch.RPSearchTA_Direct(strBegDate, intPageNumber, strEndDate, strSpecies)
 
   strsqlt = "Select count(tblcars.cid) as CountofOID, Sum(tblCars.Tons_received) AS SumOfTons_received, Sum(tblCars.Net) AS SumOfNet, Sum(tblCars.Deduction) AS SumOfDeduction "_
   & " FROM tblOrder RIGHT JOIN tblCars ON tblOrder.Release = tblCars.Release_nbr where Transfer_date is null"

   if len(strBegDate) > 2 then
     strsqlt = strsqlt & " and Date_received >= '" & strBegDate & "'"
   end if
   if len(strEndDate) > 2 then
     strsqlt = strsqlt & " and Date_received <= '" & strEndDate & "'"
   end if
   if len(strSpecies) > 0 then
     strsqlt = strsqlt & " and tblcars.species = '" & strSpecies & "'"
   end if

 elseif request.form("Shuttle") = "NO" then
   set rstEquip = objEquipSearch.RPSearchTA_DirectNull(strBegDate, intPageNumber, strEndDate, strSpecies)
 
   strsqlt = "Select count(tblcars.cid) as CountofOID, Sum(tblCars.Tons_received) AS SumOfTons_received, Sum(tblCars.Net) AS SumOfNet, Sum(tblCars.Deduction) AS SumOfDeduction "_
   & " FROM tblOrder RIGHT JOIN tblCars ON tblOrder.Release = tblCars.Release_nbr where Transfer_date is null"

   if len(strBegDate) > 2 then
     strsqlt = strsqlt & " and Date_received >= '" & strBegDate & "'"
   end if
   if len(strEndDate) > 2 then
     strsqlt = strsqlt & " and Date_received <= '" & strEndDate & "'"
   end if
   if len(strSpecies) > 0 then
     strsqlt = strsqlt & " and tblcars.species = '" & strSpecies & "'"
   end if
 
 elseif len(strSpecies) > 0 then
   set rstEquip = objEquipSearch.RPSearchTA(strBegDate, intPageNumber, strEndDate, strSpecies)

   strsqlt = "Select count(tblcars.cid) as CountofOID, Sum(tblCars.Tons_received) AS SumOfTons_received, Sum(tblCars.Net) AS SumOfNet, Sum(tblCars.Deduction) AS SumOfDeduction "_
   & " FROM tblOrder RIGHT JOIN tblCars ON tblOrder.Release = tblCars.Release_nbr where tblcars.cid > 0"

   if len(strBegDate) > 2 and len(strEndDate) > 2 then
     strsqlt = strsqlt & " and ((Date_received >= '" & strBegDate & "' and Date_received <= '" & strEndDate & "') or (Transfer_date >= '" & strBegDate & "' and Transfer_date <= '" & strEndDate & "'))"
   elseif len(strEndDate) > 2 then
     strsqlt = strsqlt & " and (Date_received <= '" & strEndDate & "' or Transfer_date <= '" & strEndDate & "')"
   elseif len(strBegDate) > 2 then
     strsqlt = strsqlt & " and (Date_received >= '" & strBegDate & "' or Transfer_date >= '" & strBegDate & "')"  
   end if
   if len(strSpecies) > 0 then
     strsqlt = strsqlt & " and tblcars.species = '" & strSpecies & "'"
   end if
 
else 
 
   set rstEquip = objEquipSearch.RPSearchTANull(strBegDate, intPageNumber, strEndDate, strSpecies)

   strsqlt = "Select count(tblcars.cid) as CountofOID, Sum(tblCars.Tons_received) AS SumOfTons_received, Sum(tblCars.Net) AS SumOfNet, Sum(tblCars.Deduction) AS SumOfDeduction "_
   & " FROM tblOrder RIGHT JOIN tblCars ON tblOrder.Release = tblCars.Release_nbr where tblcars.cid > 0"

   if len(strBegDate) > 2 and len(strEndDate) > 2 then
     strsqlt = strsqlt & " and ((Date_received >= '" & strBegDate & "' and Date_received <= '" & strEndDate & "') or (Transfer_date >= '" & strBegDate & "' and Transfer_date <= '" & strEndDate & "'))"
   elseif len(strEndDate) > 2 then
     strsqlt = strsqlt & " and (Date_received <= '" & strEndDate & "' or Transfer_date <= '" & strEndDate & "')"
   elseif len(strBegDate) > 2 then
     strsqlt = strsqlt & " and (Date_received >= '" & strBegDate & "' or Transfer_date >= '" & strBegDate & "')"  
   end if
   if len(strSpecies) > 0 then
     strsqlt = strsqlt & " and tblcars.species = '" & strSpecies & "'"
   end if

end if
	'response.write(strsqlt)
     Set MyRecT = Server.CreateObject("ADODB.Recordset")
    MyRecT.Open strSQLT, Session("ConnectionString")
    if not MyRecT.eof then
    strTT = MyRecT.fields("SumofTons_received")
    strTN = MyRecT.fields("SumOfNet")
    strTD = MyRecT.fields("SumOfDeduction")
    strTL = MyRecT.fields("CountofOID")
    end if
    MyRecT.close
    
    if isnull(strTT) then
    strTT = 0
    end if
    if isnull(strTN) then
    strTN = 0
    end if
    if isnull(strTD) then
    strTD = 0
    end if
    if isnull(strTL) then
    strTL = 0
    end if


     if ( not rstEquip.Eof) Then
      if ( intPageNumber < rstEquip.fields("TotalPage").value ) Then
        strPageNav = "<a href=javascript:NextPage()><B>Next Page</b></a>"
      end if
      if ( intPageNumber > 1 ) Then
         strPageNav = "<a href=javascript:PrevPage()><b>Previous Page</b></a>&nbsp;&nbsp;&nbsp;&nbsp;" & strPageNav
      end if
     end if

    End Function
 %><!--#include file="Fiberfooter.inc"-->