
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Recovered Paper Orders</TITLE>



<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<%  
	Dim strsQL, rstEquip

 
  	Dim  strVendor, strBegDate, strEndDate, strSpecies, strMonth
 	Dim  rstVendor
  	Dim objGeneral, strPO, gcount,  strNetC, strOrderC, strNetSC, strOrderSC, strNetS
 strMonth = request.querystring("id")
 stryear = request.querystring("y")
 
  	
  	strsql = "SELECT CID,  location,  date_unloaded,net, Species, PO, Release_nbr, Vendor, Generator, Gen_City, gen_State, grade "_
  	&" FROM tblCars  where datepart(m, date_unloaded) = " & strmonth & " and datepart(yyyy, date_unloaded) = " & stryear & " "_
  	&" and len(Vendor) > 0  and grade <> 'VF' and grade  <> 'NF' "_
  	&" order by Vendor, Generator, Gen_City, Species, date_unloaded"
  	
  	   Set rstEquip = Server.CreateObject("ADODB.Recordset")
    rstEquip.Open strSQL, Session("ConnectionString")
    If not rstequip.eof then
    strVendor = rstEquip.fields("Vendor")
    strSpecies = rstEquip.fields("Species")
        Response.ContentType = "application/vnd.ms-excel"	
%>
</head>


   <TABLE borderColor=white cellSpacing=0 cellPadding=0 width=90% border=1 align = center>
 
      <tr >
      <td  align="left" ><font face="Arial">Vendor</font></td>
<td  align="left" ><font face="Arial">Generator</font></td>
<td  align="left" ><font face="Arial">City</font></td>
<td  align="left"><font face="Arial">State</font></td>
 	<td  align="center" ><font face="Arial">Species</td>

<td  align="center" ><font face="Arial">Location</td>
 	<td  align="center" ><font face="Arial">PO</td>
 		<td  align="center" ><font face="Arial">Release</td>

	<td  align="center"><font face="Arial"  >Date Unloaded</font></td>
	
	<td  align="center" ><font face="Arial">Net</font></td>


      
  	<% 
      Dim ii
       ii = 0
       while not rstEquip.Eof
   
%><tr>
    <td  align="left"><font face = "arial" ><%=rstEquip.fields("Vendor")%></td>

<td  align="left"><font face = "arial" ><%=rstEquip.fields("Generator")%></td>
<td  align="left"><font face = "arial" ><%=rstEquip.fields("Gen_City")%></td>
<td  align="left"><font face = "arial" ><%=rstEquip.fields("Gen_State")%></td>
<td  align="center"><font face = "arial" ><%=rstEquip.fields("Species")%></td>

<td  align="center"><font face = "arial" ><%=rstEquip.fields("Location")%></td>
<td  align="center"><font face = "arial" ><%=rstEquip.fields("PO")%></td>
<td  align="center"><font face = "arial" ><%=rstEquip.fields("Release_nbr")%><%=rstEquip.fields("PS")%></td>

<td  align="center"><font face = "arial" ><%=rstEquip.fields("date_unloaded")%></td>

	<td  align="right"><font face="Arial" >
	<% if len(rstEquip("Net")) > 0 then %>
	<%= round(rstEquip.fields("Net"),3)%>
	<% else %>
	<%= rstEquip.fields("Net")%>
	<% end if %>
	</font></td>



   </tr>
    <% 
       ii = ii + 1
       
   
       rstEquip.MoveNext
     Wend
 end if %>
</table>