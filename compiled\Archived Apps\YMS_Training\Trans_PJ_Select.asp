
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">


<TITLE>Select Release Number</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->

 <%    
	
	
       set objGeneral = new ASP_CLS_General
       



   
if objGeneral.IsSubmit() Then
	strR = request.form("Release")

	Response.redirect("Transfer_From_PJ_release.asp?id=" & strR)

End if
%>
<SCRIPT LANGUAGE="JavaScript">
function formHandler(Form1){
document.forms["Form1"].submit();
}
</SCRIPT>

<style type="text/css">
.style1 {
	border-width: 1px;
	background-color: #EAF1FF;
}
.style2 {
	text-align: center;
}
.auto-style1 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: small;
	text-align: center;
}
</style>
</head>

<body>
<form name="form1" action="Trans_PJ_Select.asp" method="post" >

<p class="auto-style1"><strong>Transfer from PJ Warehouse to Yard</strong></p>
	<p class="auto-style1">&nbsp;</p>
<table border="1" cellpadding="0" class = "tablecolor1" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="50%" id="AutoNumber2" align = center>

       <TD align = center class="style1">  <p>&nbsp;</p> <font face="Arial" size="3"><b>Select Release Number:&nbsp;&nbsp;</b></font>
	&nbsp;&nbsp;&nbsp;
     <font face="Arial">
     <select name="Release">
 	<option value="" selected>Species - Release</option>
        				<%        
strsql2 = "SELECT CID, Release_nbr, Species, Carrier from tblCars where Release_nbr Is Not Null AND Location = 'PJ WH'  and Tally_Sheet is null  "_
&" order by Species, release_nbr"

 

Set MyRec2 = Server.CreateObject("ADODB.Recordset")
      MyRec2.Open strSQL2, Session("ConnectionString") 
      If not MyRec2.eof then	
      While not MyRec2.eof				 
     If MyRec2("Carrier") = "RAIL" then 			%>
            			
						<option value='<%= MyRec2("CID") %>'> <%= MyRec2("Species")%> - <%= MyREc2("Release_Nbr")%> - <%= MyRec2("Carrier") %> </option>
						<% else %>
		<option value='<%= MyRec2("CID") %>'> <%= MyRec2("Species")%> - <%= MyREc2("Release_Nbr")%></option>
					<% 		 end if
        					MyRec2.MoveNext 
        					wend
        					end if       				
        					MyRec2.close
					%>

     </select><br>
		<br>
		<p>&nbsp;</p> 
<p align="center"><input type="submit" value="Continue" id=submit1 name=submit1><br>&nbsp;</TD></tr>

</table>
</form>


<p align="center">
     <font face="Arial">
     <b><a href="Transfer_From_PJ.asp">If Release Number is not an option, Click 
		Here</a></p>
     <div class="style2">
     <br>

		 <br>

</div>

    
    <!--#include file="Fiberfooter.inc"-->