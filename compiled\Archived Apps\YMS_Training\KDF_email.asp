<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>KDF Email</title>
</head>
<%
dim strbody, strCID, strECarrier, strETrailer, strELoad, strEDoc, strEDC, strEDCName, strEAdd, strECity, strEState
dim strECC, strEBCC, strESpecies, MyRec, strsql, MyConn2, strbody3
dim gTcountFC, gTcountTissue, gTcountTowel, strMoneyFC, strMOneyTissue, strMoneyTowel
Dim gAvgDaysFC, gAvgDaysTissue, gAvgDaysTowel, gTotaldaysFC, gTotalDaysTissue, gTotalDaysTowel
Dim gExcessFC, gExcessTissue, gExcessTowel
Dim strMTDDetentionFC, strMTDDetentionTissue, strMTDDetentionTowel
Dim gMTDExessFC, gMTDExcessTissue, gMTDExcessTowel, gMTDAvgDaysFC, gMTDAvgDaysTissue, gMTDAvgDaysTowel
Dim gMTDTotalCountFC, gMTDTotalCountTissue, gMTDTotalCountTowel, gMTDTotalDaysFC, gMTDTotalDaysTowel, gMTDTotalDaysTissue


dim  strCarrier, strTrailer,   strsql3, MyRec3, gTcount, strMoney, strTMoney, MyConn, strPS, strAT
dim strSpecies, strDateReceived, strVendor, strDetention, strDays, strdate, gcount, gSpecies, strNow, strEmailto, strEmailCC
dim gAvgdays, gExcessDays, gTotaldays, strExcess, strMTDDetention, gMTDAvgDays, gMTDTotalCount, gMTDTotalDays, gMTDExcess

strdate = formatdatetime(now(),2)
gcount = 0
gtcount = 0
gtcountTissue = 0
gtcountTowel = 0
gtcountFC = 0
gSpecies = ""
strMoney = 0
strMoneyFC = 0
strMoneyTissue = 0
strMoneyTowel = 0
strTMoney = 0
gAvgdays = 0
gAvgdaysFC = 0
gAvgdaysTissue = 0
gAvgdaysTowel = 0
gExcessDays = 0
gExcessDaysFC = 0
gExcessDaysTissue = 0
gExcessDaysTowel = 0
gTotaldays = 0
gTotaldaysFC = 0
gTotaldaysTissue = 0
gTotaldaysTowel = 0
strMTDDetention = 0
strMTDDetentionFC = 0
strMTDDetentionTissue = 0
strMTDDetentionTowel = 0
gMTDAvgDays = 0
gMTDAvgDaysFC = 0
gMTDAvgDaysTissue = 0
gMTDAvgDaysTowel = 0
gMTDTotalCount = 0
gMTDTotalCountFC = 0
gMTDTotalCountTissue = 0
gMTDTotalCountTowel = 0
gMTDTotalDays = 0
gMTDTotalDaysFC = 0
gMTDTotalDaysTowel = 0
gMTDTotalDaysTissue = 0
gMTDExcess = 0
gMTDExcessFC = 0
gMTDExcessTissue = 0
gMTDExcessTowel = 0

strsql = "SELECT tblCars.*,  [date_received] AS Sort_Date FROM tblCars WHERE Date_received Is Not Null"_
&"  AND date_unloaded is null  and Species = 'KDF' ORDER BY PS, Asset_team,  Sort_Date "   
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

 Dim ii
       ii = 0
	
         strEmailTo = "<EMAIL>"
         

         	 strEmailCC = "<EMAIL>"          
                 
			Set objMail = Server.CreateObject("CDO.Message")		
			objMail.From = "<EMAIL>"
			objMail.To = strEmailTo
			
			objMail.BCC = strEmailCC
	
			objMail.Subject = "Materials Yard Report "
			strBody2 = "<table border=0  cellpadding=0 cellspacing=0 width=800><tr><td align = left><font face = arial size = 2>PS&nbsp; </td><td align = left><font face = arial size = 2>Asset Team&nbsp; </td><td align = left><font face = arial size = 2>Brand&nbsp; </td><td align = left><font face = arial size = 2>Date Received&nbsp; </td><td align = left><font face = arial size = 2> Trailer </td><td align = left><font face = arial size = 2>Carrier&nbsp;</td><td align = left><font face = arial size = 2>Vendor&nbsp;</td><td align = left><font face = arial size = 2>To-Date<br> Detention</td><td align = left><font face = arial size = 2>Days in<br>Yard</td></tr>"
			
				While not MyRec.EOF
			On error Resume Next

    


        
             strsql3 = "SELECT Free_days, Fee FROM tblCarrier  where Carrier = '" & MyRec.fields("Carrier") & "' "

        
             
             
             
   If isnull(Myrec.fields("Date_unloaded")) then 
                  strDays = Fix(Now()- cdate(MyRec.fields("Date_received")))
                else 
              strdays =  Fix(datediff("d", Myrec.fields("date_received"), Myrec.fields("Date_unloaded")))
				
                  end if 
                  



     strCarrier = MyRec.fields("Carrier") 
     strTrailer = MyRec.fields("Trailer") 
     strSpecies = MyRec.fields("Brand") 
     strDateReceived = MyRec.fields("Date_received")
     If MyRec.fields("Vendor") = "WEYERHAEUSER COMPANY" then
     strVendor = "IP"
     else
     strVendor = MyRec.fields("Vendor")
     end if 
     strPS = Myrec.fields("PS")
     strAT = MyRec.fields("Asset_Team")
        
     
                  strDays = fix(Now()- cdate(MyRec.fields("Date_received")))
          
          
 	Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    
    end if  
    MyRec3.close
    if strDays  > strFree then 
   strDetention =   (int(strDays) * strFee) - (strFee * strFree) 
   strExcess = 1 
   else
   strDetention = 0
strExcess = 0
   end if
	

  
	
if MyRec.fields("PS") = gSpecies or gcount = 0 then	

if strdays < 6 Then	
 strbody2 = strbody2 & " <tr bgcolor=white><td align = left><font face = arial size = 1> " & strPS &  "</td><td align = left><font face = arial size = 1> " & strAT &  "</td><td align = left><font face = arial size = 1> " & strSpecies &  "</td><td align = left><font face = arial size = 1>" & strDateReceived & "</td><td align = left><font face = arial size = 1>"  & strTrailer & "</td><td align = left><font face = arial size = 1>"  & strCarrier & "</td><td align = left><font face = arial size = 1>" & strVendor & "</td><td align = left><font face = arial size = 1>$" & strDetention & " </td><td align = left><font face = arial size = 1>" & strDays & " </td></tr> "
elseif strdays > 5 and strdays < 9 then
strbody2 = strbody2 & " <tr bgcolor=#FFFF99><td align = left><font face = arial size = 1> " & strPS &  "</td><td align = left><font face = arial size = 1> " & strAT &  "</td><td align = left><font face = arial size = 1> " & strSpecies &  "</td><td align = left><font face = arial size = 1>" & strDateReceived & "</td><td align = left><font face = arial size = 1>"  & strTrailer & "</td><td align = left><font face = arial size = 1>"  & strCarrier & "</td><td align = left><font face = arial size = 1>" & strVendor & "</td><td align = left><font face = arial size = 1>$" & strDetention & " </td><td align = left><font face = arial size = 1>" & strDays & " </td></tr> "
	else
strbody2 = strbody2 & " <tr bgcolor=#FCDCE6><td align = left><font face = arial size = 1> " & strPS &  "</td><td align = left><font face = arial size = 1> " & strAT &  "</td><td align = left><font face = arial size = 1> " & strSpecies &  "</td><td align = left><font face = arial size = 1>" & strDateReceived & "</td><td align = left><font face = arial size = 1>"  & strTrailer & "</td><td align = left><font face = arial size = 1>"  & strCarrier & "</td><td align = left><font face = arial size = 1>" & strVendor & "</td><td align = left><font face = arial size = 1>$" & strDetention & " </td><td align = left><font face = arial size = 1>" & strDays & " </td></tr> "

end if 


else

strBody2 = strbody2 & "<font face = arial size = 1> Total " & gSpecies & ": " & gcount
 
 if strdays < 6 Then	
 strbody2 = strbody2 & " <tr bgcolor=white><td align = left><font face = arial size = 1> " & strPS &  "</td><td align = left><font face = arial size = 1> " & strAT &  "</td><td align = left><font face = arial size = 1> " & strSpecies &  "</td><td align = left><font face = arial size = 1>" & strDateReceived & "</td><td align = left><font face = arial size = 1>"  & strTrailer & "</td><td align = left><font face = arial size = 1>"  & strCarrier & "</td><td align = left><font face = arial size = 1>" & strVendor & "</td><td align = left><font face = arial size = 1>$" & strDetention & " </td><td align = left><font face = arial size = 1>" & strDays & " </td></tr> "
elseif strdays > 5 and strdays < 9 then
strbody2 = strbody2 & " <tr bgcolor=#FFFF99><td align = left><font face = arial size = 1> " & strPS &  "</td><td align = left><font face = arial size = 1> " & strAT &  "</td><td align = left><font face = arial size = 1> " & strSpecies &  "</td><td align = left><font face = arial size = 1>" & strDateReceived & "</td><td align = left><font face = arial size = 1>"  & strTrailer & "</td><td align = left><font face = arial size = 1>"  & strCarrier & "</td><td align = left><font face = arial size = 1>" & strVendor & "</td><td align = left><font face = arial size = 1>$" & strDetention & " </td><td align = left><font face = arial size = 1>" & strDays & " </td></tr> "
	else
strbody2 = strbody2 & " <tr bgcolor=#FCDCE6><td align = left><font face = arial size = 1> " & strPS &  "</td><td align = left><font face = arial size = 1> " & strAT &  "</td><td align = left><font face = arial size = 1> " & strSpecies &  "</td><td align = left><font face = arial size = 1>" & strDateReceived & "</td><td align = left><font face = arial size = 1>"  & strTrailer & "</td><td align = left><font face = arial size = 1>"  & strCarrier & "</td><td align = left><font face = arial size = 1>" & strVendor & "</td><td align = left><font face = arial size = 1>$" & strDetention & " </td><td align = left><font face = arial size = 1>" & strDays & " </td></tr> "

end if 


gcount = 0

end if

gSpecies = MyRec.fields("PS")
 	gTotaldays = gTotaldays + strdays
 		if gSpecies = "FC" then
 		gTotaldaysFC =gTotaldaysFC + strdays
 		elseif gSpecies = "KCP Tissue" then
 	gTotaldaysTissue = gTotaldaysTissue + strdays
 		elseif gSpecies = "KCP Towel" then
 		gTotaldaysTowel =gTotaldaysTowel + strdays
 		end if 


    
  gcount = gcount + 1
       gTcount = gTcount + 1
 		
 		if gSpecies = "FC" then
 		gtCountFC = gTcountFC + 1
 		elseif gSpecies = "KCP Tissue" then
 		gTcountTissue = gTcountTissue + 1
 		elseif gSpecies = "KCP Towel" then
 		gTcountTowel = gTcountTowel + 1
 		end if 
 		strMoney = strMoney + cint(strDetention)
 		if gSpecies = "FC" then
 		strMoneyFC = strMoneyFC + cint(strDetention)
 		elseif gSpecies = "KCP Tissue" then
 		strMoneyTissue = strMoneyTissue + cint(strDetention)
 		elseif gSpecies = "KCP Towel" then
 		strMoneyTowel = strMoneyTowel + cint(strDetention)
 		end if 
 		
 		
 		
 		strTMoney = strTmoney + strMoney
 		gExcess = gExcess + strExcess
 			if gSpecies = "FC" then
 		gExcessFC = gExcessFC + strExcess
 		elseif gSpecies = "KCP Tissue" then
 		gExcessTissue = gExcessTissue + strExcess
 		elseif gSpecies = "KCP Towel" then
 	gExcessTowel = gExcessTowel +strExcess
 		end if 
 		
 		
 		
 		
 		
       ii = ii + 1
       MyRec.MoveNext
            
  
		 		WEND
           		MyRec.Close
           		Dim strMdate
           		strMdate = formatdatetime(Now(),2)
           		
    strsql = "SELECT tblCars.* FROM tblCars WHERE (month(Date_unloaded) = month('" & strMdate & "') and year(Date_unloaded) = year('" & strMdate & "') and Species = 'KDF') or (Date_received Is Not Null AND date_unloaded is null AND Species ='KDF') "  
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")    
   				While not MyRec.EOF 
   				
 
             
             
   If isnull(Myrec.fields("Date_unloaded")) then 
                  strDays = Fix(Now()- cdate(MyRec.fields("Date_received")))
                else 
              strdays =  Fix(datediff("d", Myrec.fields("date_received"), Myrec.fields("Date_unloaded")))
				strdays = int(strdays)
                  end if 
          

          
 Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    MyRec3.Open strSQL3, Session("ConnectionString")  
    If Not Myrec3.eof then
    strFee = MyRec3.fields("Fee")
    strFree = MyRec3.fields("Free_days")
    else
    strFee = 0
    strFree = 0
    
    end if  
    MyRec3.close
    if strDays  > strFree then 
   strDetention =   (int(strDays) * strFee) - (strFee * strFree) 
   strExcess = 1 
   else
   strDetention = 0
   strExcess = 0
   end if
   
	strMTDDetention  = strMTDDetention + strDetention 	
	If MyRec.fields("PS") = "FC" then
		strMTDDetentionFC  = strMTDDetentionFC + strDetention 	
	elseif MyRec.fields("PS") = "KCP Towel" then
		strMTDDetentionTowel  = strMTDDetentionTowel + strDetention 
		elseif MyRec.fields("PS") = "KCP Tissue" then
		strMTDDetentionTissue = strMTDDetentionTissue + strDetention 
		end if
	
  	gMTDExcess = gMTDExcess + strExcess
  		If MyRec.fields("PS") = "FC" then
		gMTDExcessFC  = gMTDExcessFC + strExcess
	
	elseif MyRec.fields("PS") = "KCP Towel" then
		gMTDExcessTowel  = gMTDExcessTowel + strExcess

		elseif MyRec.fields("PS") = "KCP Tissue" then
		gMTDExcessTissue = gMTDExcessTissue + strExcess
		end if
  	
  	
  	
  	
 	gMTDTotaldays = gMTDTotaldays + strdays 
 	
 	 		If MyRec.fields("PS") = "FC" then
		gMTDTotaldaysFC  = gMTDTotaldaysFC + strdays 
	
	elseif MyRec.fields("PS") = "KCP Towel" then
		gMTDTotaldaysTowel  = gMTDTotaldaysTowel + strdays 

		elseif MyRec.fields("PS") = "KCP Tissue" then
		gMTDTotaldaysTissue = gMTDTotaldaysTissue + strdays 
		end if
   
 	gMTDTotalCount = gMTDTotalCount + 1 
 		
     	If MyRec.fields("PS") = "FC" then
		gMTDTotalCountFC  = gMTDTotalCountFC + 1	
		elseif MyRec.fields("PS") = "KCP Towel" then
		gMTDTotalCountTowel  = gMTDTotalCountTowel + 1
		elseif MyRec.fields("PS") = "KCP Tissue" then
		gMTDTotalCountTissue = gMTDTotalCountTissue + 1
		end if
       MyRec.MoveNext
            
  
		 		WEND
           		MyRec.Close      		
        		
           		strNow = formatdatetime(now(),0)
           		gAvgDays = round((gTotaldays/gTcount),1)
           		
           		If gTotaldaysFC = 0 or gTcountFC = 0 then
           		gAvgDaysFC = 0
           		else  
           		
           		gAvgDaysFC = round((gTotaldaysFC/gTcountFC),1)
           		end if
           		If gTotaldaysTissue = 0 or gTcountTissue = 0 then
           		gAvgDaysTissue = 0
           		else           		
           		gAvgDaysTissue = round((gTotaldaysTissue/gTcountTissue),1)
           		end if 
           		If gTotaldaysTowel = 0 or gTcountTowel = 0 then
           		gAvgDaysTowel = 0
           		else
           		gAvgDaysTowel = round((gTotaldaysTowel/gTcountTowel),1)
           		end if
           		
           		
           		gMTDAvgDays = round((gMTDTotaldays/gMTDTotalCount),1)
           		
           		If gMTDTotaldaysFC = 0 or gMTDTotalCountFC = 0 then
           		gMTDAvgDaysFC = 0
           		else  
           		
           		gMTDAvgDaysFC = round((gMTDTotaldaysFC/gMTDTotalCountFC),1)
           		end if
           		If gMTDTotaldaysTissue = 0 or gMTDTotalCountTissue = 0 then
           		gMTDAvgDaysTissue = 0
           		else           		
           		gMTDAvgDaysTissue = round((gMTDTotaldaysTissue/gMTDTotalCountTissue),1)
           		end if 
           		If gMTDTotaldaysTowel = 0 or gMTDTotalCountTowel = 0 then
           		gMTDAvgDaysTowel = 0
           		else
           		gMTDAvgDaysTowel = round((gMTDTotaldaysTowel/gMTDTotalCountTowel),1)
           		end if
	
	strBody2 = strbody2 & "<font face = arial size =1> Total " & gSpecies & ": " & gcount 
		
			strBody3 = "Family Care Totals<table border=0  cellpadding=0 cellspacing=0 width=800><tr><font face = arial size = 1><td>Total Count</td><td>Detention Total</td><td>On Avg Days</td><td># Loads in Excess of Free Days</td></tr>"
						
	strBody3 = strbody3 & "<tr><font face = arial size = 1><td>" & gtcountFC & "</td><td>$" & formatNumber(strMoneyFC,0) & "</td><td>" & gAvgDaysFC & "</td><td>" & gExcessFC & "</td></tr>"
	
				strBody3 = strBody3 & "<tr><font face = arial size = 1><td>&nbsp;</td><td>MTD Detention</td><td>MTD Average Days</td><td>MTD # Loads in Excess of Free Days</td></tr>"
				
					strBody3 = strBody3 & "<tr><font face = arial size = 1><td>&nbsp;</td><td> $" & formatNumber(strMTDDetentionFC,0) & "</td><td>" & gMTDAvgDaysFC & "</td><td>" & gMTDExcessFC & "</td></tr></table>"
					
						
			strBody3 = Strbody3 & " KCP Tissue Totals<table border=0  cellpadding=0 cellspacing=0 width=800><tr><font face = arial size = 1><td>Total Count</td><td>Detention Total</td><td>On Avg Days</td><td># Loads in Excess of Free Days</td></tr>"
						
	strBody3 = strbody3 & "<tr><font face = arial size = 1><td>" & gtcountTissue & "</td><td>$" & formatNumber(strMoneyTissue,0) & "</td><td>" & gAvgDaysTissue & "</td><td>" & gExcessTissue & "</td></tr>"
	
				strBody3 = strBody3 & "<tr><font face = arial size = 1><td>&nbsp;</td><td>MTD Detention</td><td>MTD Average Days</td><td>MTD # Loads in Excess of Free Days</td></tr>"
				
					strBody3 = strBody3 & "<tr><font face = arial size = 1><td>&nbsp;</td><td> $" & formatNumber(strMTDDetentionTissue,0) & "</td><td>" & gMTDAvgDaysTissue & " </td><td>" & gMTDExcessTissue & "</td></tr></table>"
					
						
			strBody3 =Strbody3 &  "KCP Towel Totals<table border=0  cellpadding=0 cellspacing=0 width=800><tr><font face = arial size = 1><td>Total Count</td><td>Detention Total</td><td>On Avg Days</td><td># Loads in Excess of Free Days</td></tr>"
						
	strBody3 = strbody3 & "<tr><font face = arial size = 1><td>" & gtcountTowel & "</td><td>$" & formatNumber(strMoneyTowel,0) & "</td><td>" & gAvgDaysTowel & " </td><td>" & gExcessTowel & "</td></tr>"
	
				strBody3 = strBody3 & "<tr><font face = arial size = 1><td>&nbsp;</td><td>MTD Detention</td><td>MTD Average Days</td><td>MTD # Loads in Excess of Free Days</td></tr>"
				
					strBody3 = strBody3 & "<tr><font face = arial size = 1><td>&nbsp;</td><td> $" & formatNumber(strMTDDetentionTowel,0) & "</td><td>" & gMTDAvgDaysTowel & " </td><td>" & gMTDExcessTowel & "</td></tr></table>"
					
					
	
			strBody3 = Strbody3 & "All Product System Totals<table border=0  cellpadding=0 cellspacing=0 width=800><tr><font face = arial size = 1><td>Total Count</td><td>Detention Total</td><td>On Avg Days</td><td># Loads in Excess of Free Days</td></tr>"
						
	strBody3 = strbody3 & "<tr><font face = arial size = 1><td>" & gtcount & "</td><td>$" & formatNumber(strMoney,0) & "</td><td>" & gAvgDays & "</td><td>" & gExcess & "</td></tr>"
	
				strBody3 = strBody3 & "<tr><font face = arial size = 1><td>&nbsp;</td><td>MTD Detention</td><td>MTD Average Days</td><td>MTD # Loads in Excess of Free Days</td></tr>"
				
					strBody3 = strBody3 & "<tr><font face = arial size = 1><td>&nbsp;</td><td> $" & formatNumber(strMTDDetention,0) & "</td><td>" & gMTDAvgDays & "</td><td>" & gMTDExcess & "</td></tr></table>"
	
		objMail.HTMLBody = "<font face = arial size = 2> Materials Yard report for " &  strNow  & " <br><br>" & strbody3 & strbody2



			' objMail.Send
			Set objMail = nothing
	if request.querystring("s") = "i" then
				Response.redirect("Send_yard_email.asp")
			else
			  Response.redirect("Warehouse_Rpt_Email.asp")
			   end if
	



 %>

