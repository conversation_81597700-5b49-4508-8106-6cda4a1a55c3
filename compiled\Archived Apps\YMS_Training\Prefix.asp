																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Edit Last Number for Prefix</TITLE>

<!--#include file="classes/asp_cls_headerOYM.asp"-->
<!--#include file="classes/asp_cls_SessionStringOYM.asp"-->
 

<!--#include file="classes/asp_cls_DataAccessOYM.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureOYM.asp"-->


<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate

strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -7, strtdate)

strsql = "SELECT * from tblPrefix"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=50%  border=1 align = center>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Edit&nbsp; Last Number for BOL # Prefix</b></font></td>
<td align = center>&nbsp;</td>


</tr>
	    </table>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=30% class = "tablecolor1" border=1 align = center>  
	 <tr class="tableheader">
<td>&nbsp;</td>

				<td  align = left>     <font face="Arial" size="1">
				Prefix</font></td>
		<td  ><b><font face="Arial" size="1">Last Number</font></b></td>

	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td> <font size="1" face="Arial">

<a href="Prefix_edit.asp?id=<%= MyRec.fields("PID") %>">

Edit</a></td>

	<td  ><font size="1" face="Arial"><%= MyRec.fields("Load_prefix")%></font></td>
	
<td  ><font size="1" face="Arial"><%= MyRec.fields("Last_number")%></font></td>

</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>


<!--#include file="OYMfooter.inc"-->