																	

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Edit Records</TITLE>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->




<% Dim MyRec, strsql, MyRec2, strsql2, strtdate, strdate, strsql3

strtdate = formatdatetime(Now(),2)
strdate = dateadd("d", -60, strtdate)

    strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "' and User_type = 'A'"


    Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    MyRec2.Open strSQL3, Session("ConnectionString")


If not Myrec2.eof then



strsql = "SELECT tblSAPOpenPO.Item, tblCars.* FROM tblCars LEFT JOIN tblSAPOpenPO ON tblCars.NFID = tblSAPOpenPO.OID "_
&" WHERE NFID > 0 and Date_received > '" & strdate & "' order by Date_Received, Rec_number"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Non-Fiber Loads Received by Product System in Last Sixty Days</b></font></td>
<td align = center>&nbsp;</td>


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% class = "tablecolor1" border=1>  
	 <tr class="tableheader">
<td>&nbsp;</td>
			<td  > <p align="center">       <font face="Arial" size="1">Print<br> Receipt#</font></td>
		<td  > <p align="center">       <font face="Arial" size="1">Product<br> System</font></td>
		<td  > <p align="center">       <font face="Arial" size="1">Asset Team</font></td>
		<td  ><font face="Arial" size="1"><b>Trailer</b></font></td>

		<td  ><font face="Arial" size="1"><b>Carrier</b></font></td>
		<td  ><font face="Arial" size="1">Species</font></td>
    
		<td  ><font face="Arial" size="1">Vendor</font></td>
			<td  ><font face="Arial" size="1">SAP #</font></td>
				<td  ><font face="Arial" size="1">Brand</font></td>
		<td  >       <font face="Arial" size="1">PO #</font></td>
		<td  >       <font face="Arial" size="1">Line #</font></td>
	<td>       <font face="Arial" size="1">REC #</font></td>


		<td  >
        <p align="center">
        <font face="Arial" size="1">QTY<br> (in TH)</font></td>
		<td  ><font face="Arial" size="1">Date<br> Received</font></td>
<td  ><font face="Arial" size="1">Date<br> Unloaded</font></td>
		<td align = center >
        <font size="1" face="Arial">Paper-<br>work</font></td>
	<td  >
        <font size="1" face="Arial">Delete<br>Receipt</font></td>
        <td  >
        <font size="1" face="Arial">Reverse<br> Unload</font></td>

	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td> <font size="1" face="Arial"><a href="Edit_NFReceipt.asp?id=<%= MyRec.fields("CID") %>">Edit</a></td>

	<td align = center> <font size="1" face="Arial"><a href="KDF_receipt.asp?p=e&id=<%= MyRec.fields("CID") %>"><%= MyRec.fields("CID")%></a></td>
<td  ><font size="1" face="Arial"><%= MyRec.fields("PS")%>&nbsp;</font></td>
		<td  ><font size="1" face="Arial"><%= MyRec.fields("Asset_Team")%>&nbsp;</font></td>

	<td  > <font size="1" face="Arial"><b><%= MyRec.fields("Trailer")%></font></b></td>
	

			<td  ><font size="1" face="Arial"><b><%= MyRec.fields("Carrier")%>&nbsp;</font></b></td>


		<td  ><font face="Arial" size="1"> <%= MyRec.fields("Species")%></font></td>
		<% if MyRec.fields("Vendor") = "WEYERHAEUSER COMPANY" then %>
			<td><font size="1" face="Arial">IP</font></td>
			<% else %>
		<td><font size="1" face="Arial"><%= MyRec.fields("Vendor")%></font></td>
		<% end if %>
		<td><font size="1" face="Arial"><%= MyRec.fields("SAP_nbr")%></font></td>
				<td><font size="1" face="Arial"><%= MyRec.fields("Brand")%></font></td>
		<td  >        <font size="1" face="Arial">        <%= MyRec.fields("PO")%></font></td>
			<td  >        <font size="1" face="Arial">        <%= MyRec.fields("Item")%></font></td>
		<% if isnull(MyRec.fields("REC_Number")) then %>
			<td  >  <font size="1" face="Arial">        <%= MyRec.fields("CID")%></font></td>
			<% else %>
	<td  >  <font size="1" face="Arial">        <%= MyRec.fields("REC_Number")%></font></td>
	<% end if %>
	

		<td align = right >
				 <font size="1" face="Arial">        <%= MyRec.fields("Tons_received")%>&nbsp;<%= MyRec.fields("Weigh_required") %></font></td>
		<td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Date_Received")%></font></td>
	<td  >		 <font size="1" face="Arial">        <%= MyRec.fields("Date_Unloaded")%>&nbsp;</font></td>
	<% if Myrec.fields("Paperwork") = 0 then %>
	
		<td  >	 <font size="1" face="Arial"> &nbsp;</font></td>
		<% else %>
				<td  >	 <font size="1" face="Arial"> No Match</font></td>
				<% end if %>
<td align = center >	 <font size="1" face="Arial"><a href="KDF_Edit_Delete_Sys.asp?id=<%= Myrec.fields("CID")%>"> Delete</a></font></td>
<td align = center >	 <font size="1" face="Arial"><a href="KDF_Reverse_unload.asp?id=<%= Myrec.fields("CID")%>"> Reverse</a></font></td>
	</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>

<% Myrec2.close 
end if %><!--#include file="Fiberfooter.inc"-->