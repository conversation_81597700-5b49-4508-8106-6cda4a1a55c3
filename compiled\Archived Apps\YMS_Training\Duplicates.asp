																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Duplicate Release Numbers </TITLE>

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 



<% Dim MyRec, strsql, strstartdate
strStartdate = "8/1/2012"

strsql = "SELECT Release_nbr,CID, OID, Trailer, Date_received, Entry_Page, Grade, Species, Entry_time FROM tblCars "_
&" WHERE (((tblCars.Release_nbr) In (SELECT [Release_nbr] FROM [tblCars] As Tmp GROUP BY [Release_nbr] HAVING Count(*)>1 )) "_
&"  AND Date_received> '" & strStartDate & "' AND (Grade='RF' Or Grade='BROKE')) "_
&" ORDER BY tblCars.Release_nbr "

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<style type="text/css">
.style2 {
	text-align: center;
	font-family: Arial;
}
.style5 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: small;
}
.style6 {
	text-align: left;
}
.style7 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: small;
	text-align: left;
}
</style>
</head>

<body>

<% if Session("EmployeeID") = "C97338" or Session("EmployeeID") = "B55404" or Session("EmployeeID") = "C28802" or Session("EmployeeID") = "B55548" or Session("EmployeeID") = "B38763" or Session("EmployeeID") = "B48943" or Session("EmployeeID") = "B53909"  then %>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>


<td class="style2"><strong>Duplicate Release Numbers entered as Receipts</strong></td>


</tr>
	    </table>
	
	<br>
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=65% class = "tablecolor1" border=1 align = center>  
	 <tr class="tableheader">

				<td class="style7">     Release #</td>
		<td class="style7"  > Receipt #</td>
		<td class="style7">  Trailer</td>
		<td class="style7">  Date Received</td>
		<td class="style7"> Entry Time</td>
		<td class="style7">  Species</td>
		<td class="style7">  Grade</td>
		<td class="style7">  Entry Page</td>
	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>

	<td class="style7"  ><%= MyRec.fields("Release_nbr")%></td>
	
<td class="style7"  ><%= MyRec.fields("CID")%></td>
	<td class="style7"  ><%= MyRec.fields("Trailer")%></td>
		<td class="style7"  ><%= MyRec.fields("Date_received")%></td>
			<td class="style7"  ><%= MyRec.fields("Entry_time")%></td>

		<td class="style6"  ><font size="2" face="Arial"><span class="style5"><%= MyRec.fields("Grade")%></span>&nbsp;</font></td>
		<td class="style6"  ><font size="2" face="Arial"><span class="style5"><%= MyRec.fields("Species")%></span>&nbsp;</font></td>
<td class="style6"  ><font size="2" face="Arial"><span class="style5"><%= MyRec.fields("Entry_page")%></span>&nbsp;</font></td>

</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>
<% else %>
<p align="center"><font face="arial" size="3"><b>You do not have authorization to view this page</b></font></p>
<% end if %><!--#include file="Fiberfooter.inc"-->