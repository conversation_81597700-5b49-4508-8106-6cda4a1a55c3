
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">
<TITLE>RF Inventory List</TITLE>

<!--#include file="classes/asp_cls_headerOYM.asp"-->
<!--#include file="classes/asp_cls_SessionStringOYM.asp"-->
 

<!--#include file="classes/asp_cls_DataAccessOYM.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

    <% Dim MyRec, strsql, strSCNN
   strDate = NOW()
   
     strsql = "SELECT  Count(Master_Table.TRAILER) AS [CountOfTrailer] FROM Master_Table "_
	&" WHERE Master_Table.Date_Time_In Is Not Null AND Master_Table.Carrier='SCNN'"
	Set MyRec = Server.CreateObject("ADODB.Recordset") 
   		MyRec.Open strSQL, Session("ConnectionString")
   		
strSCNN = MyRec.Fields("CountofTrailer")
MyRec.close
 %>

<body bgcolor = "#FCFBF8">

     <br>      

 <table align = center width = 100%>
<tr><td width = 33% align = left><font face="Arial"><a href="OYM_Add_date.asp">Add 
	Missing Date</a></font></td>
<td align = center width = 34%>  <font face="Arial" size = 3><b>RF Inventory List<br><br></font ></td>
<td width = 33% align = right>&nbsp;</td>
</tr></table>


<%
Dim strdate, strYdate
strdate = formatdatetime(Now(),2)
strYdate = dateadd("d", -31, strdate)


   strsql = "SELECT  tblRF_report_list.* from tblRF_report_list where datediff(d, '" & strdate & "', '" & strYdate & "') < 32 order by Report_date desc "

		
		Set MyRec = Server.CreateObject("ADODB.Recordset") 
   		MyRec.Open strSQL, Session("ConnectionString")

do until  MyRec.eof %>
 <table align = center width = 100%>
<tr>

			
		 
    
    <td align = center><font face = Arial size = 2><a href="<%= MyRec.fields("Report_path")%>"><b>
    <%= MyRec.fields("Report_date").value %></a></b></td>



     </tr>
    <%  
       
 	 MyRec.MoveNext
	
loop
MyRec.close

      
    %>

  </table>    
                        


</BODY></font><br>

