
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Security Roles</TITLE>

<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_Procedure.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<%    Dim   rstTeam, strTeam, rstESL, rstWA, strWA,  strID, objEPS, strName, strName2, strType

    set objGeneral = new ASP_CLS_General %>
	<style type="text/css">
.style1 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
.style2 {
	font-family: arial;
	font-size: x-small;
}
</style>
</head>

	<body>

<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100% border=1>
  <tr><td colspan="4" bgcolor="white" class="subheader">&nbsp;</td></tr>

 <TD>
     <p align="left"><font size="3" face="Arial"><b>Security Roles</b>&nbsp;&nbsp;</font></td>
<td align = right><a href="Security_roles.asp"><font face="Arial"><b>Return</b></a></font></td></tr>
	    </table><font face="Arial"><br>
	    <%  
	        Dim SCRIPT_NAME, MyRec1, strAdmin
		Dim strSQL
		Dim BACK_TO_LIST_TEXT

		SCRIPT_NAME = Request.ServerVariables("SCRIPT_NAME")

		BACK_TO_LIST_TEXT = "<p>Click <a href=""" & SCRIPT_NAME & """>" _
    		    & "here</a> to go back to record list.</p>"
    		    
    		    strAdmin = "NO"
    		    
    			strSQL = "Select * From tblSecurity Where BID = '" & SEssion("EmployeeID") & "' and User_Type = 'Admin'"

			Set MyRec1 = Server.CreateObject("ADODB.RecordSet")
			MyRec1.Open strSQL, Session("ConnectionString")

			If Not MyRec1.EOF Then
			strAdmin = "OK"
			end if
			MyRec1.close
    
    		    
    If  session("EmployeeID") = "B41792"  or session("EmployeeID") = "C97338"  or strAdmin = "OK" then
 
	    %>

	    <%
		Select Case LCase(Trim(Request.QueryString("action")))
		    Case "add"
		    %>
			</font>
			<form action="<%= SCRIPT_NAME %>?action=addsave" method="post">
			<font face="Arial">
		            <br>
			    <br><b>BID:    </b> 
			    <br><Input name="BID" size="13">
			    <br>
			      <br><b>Type:    </b> <br>
			 <select name="Type" size="1">
   
       <option>Edit</option>
     	<option>Admin</option>
     </select><br>

			    	</font>

			    <br><Input name="Update" type="submit" Value="Add Name" >
 			</form>
                    <%
	         

 		    Case "addsave"
 		   
         strName2 = ReturnEname(Request.Form("BID"))
   
	
			strSQL = "Insert into tblSecurity (User_tyPE, BID, Employee_Name) Values('" & Request.Form("Type") & "', " _
			   & "'" & Request.Form("BID") & "', '" & strName2 & "')"

			
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

			Response.Write("Record has been added successfully.")

			
	

		    Case "edit"
			iRecordId = Request.QueryString("id")
			strSQL = "Select * From tblSecurity Where ID = " & iRecordId

			Set MyRec = Server.CreateObject("ADODB.RecordSet")
			MyRec.Open strSQL, Session("ConnectionString")

			If Not MyRec.EOF Then
			    %>
				<form action="<%= SCRIPT_NAME %>?action=editsave" method="post">
				    <input type="hidden" name="id" value="<%= MyRec.Fields("ID").Value %>" />
				    <font face="Arial">
			    	    &nbsp;<br>
			    	    <br><b>BID:    </b> 
			    	    <br>
                    <input Name="BID" value="<%= MyRec.Fields("BID").Value %>" size="10" />
			    	   	    <br>
			    	    &nbsp;<br><strong>Type</strong><b>:    </b> <br>
			 <select name="Type" size="1">
    
       <option <% If MyRec.fields("User_Type") = "Edit" then %> selected <% END IF %>>Edit</option>
     	<option <% If MyRec.fields("User_Type") = "Admin" then %> selected <% END IF %>>Admin</option>
     </select></font><br>
		
			    	   	    <br><Input name="Update" type="submit" Value="Update Record" > 
				</form>
			    <%
			End If

		
			

		    Case "editsave"
		       strName2 = ReturnEname(Request.Form("BID"))
			iRecordId = Clng(Request.form("id"))
			 strType = Request.form("Type")
		
			strSQL = "Update tblSecurity Set  BID = '" & Request.Form("BID") & "', "_
			&" Employee_Name = '" & strName2 & "', User_Type = '" & strType & "' Where ID = " & iRecordId

			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

			Response.Write("Record has been updated successfully.")

		

		    Case "delete"
	        ' Get the id to delete
			iRecordId = Clng(Request.QueryString("id"))
			

			strSQL = "DELETE FROM tblSecurity WHERE ID = " & iRecordId 

			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close

			Response.Write("Record has been deleted successfully.")


		    Case else   'Default view
			strSQL = "SELECT * from tblSecurity order by Employee_name "
				        Set MyRec = Server.CreateObject("ADODB.Recordset")
			MyRec.Open strSQL, Session("ConnectionString"), adOpenForwardOnly
			%>
			<table Border=1 bgcolor = #F0F0F0 BORDERCOLOR = #CCCCFF cellpadding = 3 width = 75% align = center>
			<thead>
			    <tr>
		
                	
                		<th bgcolor="#FDFEE9" class="style1"> BID</th>
	     		
                		<th bgcolor="#FDFEE9"> <font face="arial" size="2">Name</font></th>
                	
                			<th bgcolor="#FDFEE9" class="style2"> <font face="arial" size="2">Type 
							of User</font></th>
			
	     	
                	
                		<th colspan="2" bgcolor="#FDFEE9">&nbsp;</th>
                       
              	    </tr>
			    </thead>
			    <tbody>
	
        		        <% While not MyRec.EOF %>
              			    <tr>
               <td bgcolor="#FFFFFF"><font face="Arial"><%= MyRec.Fields("BID").Value %></font></td>
              		   <td bgcolor="#FFFFFF"><font face="Arial"><%= MyRec.Fields("Employee_Name").Value %></font></td>
              		    		   <td bgcolor="#FFFFFF" align = "center"><font face="Arial">
              		 <%=  MyRec.fields("User_type") %>
              		  		   
              		    		 </font></td>
					<td align="center" bgcolor="#FFFFFF"><font face="Arial"><a href="<%= SCRIPT_NAME %>?action=edit&id=<%= MyRec.Fields("ID").Value %>">Edit</a></font></td>
              			 <td align="center" bgcolor="#FFFFFF"><font face="Arial"><a href="<%= SCRIPT_NAME %>?action=delete&id=<%= MyRec.Fields("ID").Value %>">Delete</a></font></td>
  						 </tr>
                            
              			    <% 
				        MyRec.MoveNext
                 			WEND
                 			MyRec.Close
              			    %>

			    </tbody>
			    <tfoot>
				<tr>
				    <td colspan="4" align="right"><font face="Arial"><a href="<%= SCRIPT_NAME %>?action=add">Add a new record</a></font></td>
				</tr>
			    </tfoot>

            		</table>
		<font face="Arial">
		<%					
		End Select
		%>                    
      	</font>                    
      	</body>
      	
      	  <%  
   else
    Response.write ("<font face = arial size = 3>You do not have authorization to view this page")
   
    end if  %><!--#include file="footer.inc"-->