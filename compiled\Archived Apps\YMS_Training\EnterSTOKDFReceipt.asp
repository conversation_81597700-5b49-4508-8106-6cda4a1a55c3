<html>
<script>window.history.go(1);</script>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionPolaris.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Enter Non-Fiber Trailer Receipt</title>
<style type="text/css">
.auto-style1 {
	border-width: 1px;
	background-color: #F2FBFF;
}
.style28 {
	color: #9F112E;
}
.auto-style3 {
	font-family: Arial;
}
.auto-style4 {
	border: 1px solid #000000;
}
.auto-style6 {
	border: 1px solid #EBEBEB;
	font-weight: bold;
	background-color: #F2FBFF;
}
.auto-style7 {
	border: 1px solid #EBEBEB;
	background-color: #F2FBFF;
}
.auto-style8 {
	border: 1px solid #EBEBEB;
	font-weight: bold;
	background-color: #F2FBFF;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID, strPO, MyConn, objMOC, rstFiber, strPS, strAT, strUOM, strNewID, strPaper, strBrand, strCarID
    Dim strTrailer, strCarrier
    Dim strPONbr
    Dim strRECNbr, gVendorName

    Dim strTonsReceived
    Dim strDateReceived

    Dim strOther, strR, strsql3, gDescription, gPO, gSAP_Nbr, gVendor, gOID, strNet, strMultiLoad

    strload= Trim(Request.QueryString("id")) 
 

 	set objGeneral = new ASP_CLS_General

  	if len(strLoad) > 4 then
	strsql = "Select tblSTOInbound.* from tblSTOInbound where Load_nbr = " & strLoad & ""
		 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL, Session("ConnectionString")
  If not MyRec.eof then 
  strTrailer = MyRec.fields("Ship_From_State")
  strCarrier = MyRec.fields("Ship_From_CIty")
  strLoad = MyRec("Load_Nbr")
  strGenerator = MyRec("Ship_From_Name")
  strSAP = MyREc("Sap_NBr")
  strPO = MyREc("PO")
  strBOL = MyREc("BOL")
  end if
  MyRec.close
  else
  strSAP = request.querystring("s")
  end if
  
  strUOM = "EA"

	
	strDateReceived = formatdatetime(Now(),2)
 

if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
MyRec.close

 	strTrailer = Request.form("Trailer")
	strOther = Replace(Request.form("Other_Comments"), "'", "''") 
	strDateReceived = Request.form("Date_received")
	strRECNbr = Request.form("Rec_Number")
	strCarrier = Request.form("Carrier")
	strUOM = Request.form("UOM")
	strSAPWeight = request.form("SAP_Weight")

	strTonsReceived = Request.form("Tons_received")


	
	If isnull(strTrailer) or strTrailer = ""  or isnull(strCarrier) or strCarrier = ""  or isnull(strTonsReceived) or strSAPWeight = "" or isnull(strSAPweight) or strDateReceived = "" then
	
	Response.redirect("EnterSTOKDFReceipt.asp?id=" & strPO & "&n=T")
	else
	Call SaveData() 
		

	end if
else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to save this information.</font></br>")
	
end if

	

	end if  ' submit

 
%>



<body>
<% if Request.querystring("n") = "T" or request.querystring("n") = "T1" then 
strTrailer = Session("Trailer")
strCarrier = Session("Carrier")
strGenerator = Session("Generator")
strSAPWeight = Session("SAP_Weight")
end if
 %>

<% if Request.querystring("n") = "T" then %>
<p align = center><font face = arial size = 3 color = red><b>You must enter the Carrier, Trailer Number, Quantity and Unit of Measure.  Please re-enter your information.</p></b></font>
<% else %>
&nbsp;
<% end if %>
<table width = 100%><tr><td width = 33%>
<font face="Arial" size="2">

</td><td align = center width = 34%><b><font face="Arial" size="4">
Enter STO KDF Trailer Receipt </font> </b></td>
<td align = center><font face="Arial" size="2"><b></td>
<td align = right width = 10%><a href="SelectWeyInbound.asp"><b><font face = arial size = 2>RETURN</a></font></b></td></tr></table>




<form name="form1" action="EnterSTOKDFReceipt.asp?id=<%=strPO%>" method="post">
<input type="hidden" name="Species" value="<%= strSpecies %>">
<input type="hidden" name="SAP_Number" value="<%= strSap %>">
<input type="hidden" name="PO" value="<%= strPO %>">
<input type="hidden" name="BOL" value="<%= strBOL %>">

<div align="center">
<table cellspacing="0" bgcolor="#FFFFE1" style="width: 50%;" cellpadding="0" bordercolorlight="#C0C0C0" class="auto-style4">
<tr>
    <td class="auto-style7" style="width: 25%">&nbsp;</td>

    <td colspan="2" class="auto-style7">&nbsp;</td>
  </tr>
    <tr>
    <td  align = left class="auto-style6" style="width: 25%" >
   <font face="Arial" size="2">PO Number:&nbsp;</font></td>
<td  align = left width="48%" class="auto-style7">

     <font face="Arial" size="2">

      <%= strPO %></font>

  </td>
<td  align = left width="32%" class="auto-style7">

		<font face="Arial"><font size="2"><b>Check to Print Receipt:</b></font><input type="checkbox" name="Print_receipt" value="ON" checked></td></tr>
  <tr>
    <td  align = left class="auto-style8" style="width: 25%" >
    BOL Number:</td>
<td  align = left colspan="2" class="auto-style7"><font face="Arial" size="2"><%= strBOL %>

      &nbsp;</td></tr>
              <tr>
          <td  align = left class="auto-style6" style="width: 25%" >
   <font face="Arial" size="2">SAP #:&nbsp;</font></td>
<td align = left class="auto-style7"> 
   <font face="Arial" size="2">   <%= strSAP %>&nbsp;
 
</TD>
<td align = left class="auto-style1">

		<b><font face="Arial" size="2">&nbsp;</font></b></TD></tr>
   
         <tr>
  <tr>
    <td  align = left class="auto-style6" style="width: 25%" >
    <font face="Arial" size="2">STO Delivery#:</font></td>
<td  align = left colspan="2" class="auto-style7">

      <font face="Arial">

      <input name="Load" size="24" value = "<%= strLoad%>" style="font-weight: 700"></font></td></tr>
  <tr>

      <td align = left class="auto-style6" style="width: 25%">
  <font face="Arial" size="2">Select Carrier:&nbsp;&nbsp;</font></td>
<td  align = left colspan="2" class="auto-style7">

      <font face="Arial">   
	<select name="Carrier" style="font-weight: 700">
 	<option value="" selected>  Select Carrier (Required)</option>
 	<% if UCASE(left(strTrailer,3)) = "MCH" then 
 	strCarrier = "MCH"
 	end if
 	
 	
 	strsql = "Select Carrier,C_Description, Weight from tblCarrier order by Carrier"
 	   	 Set MyRec4 = Server.CreateObject("ADODB.Recordset")
   	 MyRec4.Open strSQL, Session("ConnectionString")
   	 while not MyRec4.eof %>
   	 <option <% if strCarrier = MyRec4("Carrier") then %> selected <% end if %> value="<%= MyRec4("Carrier") %>"><%= MyRec4("Carrier") %>&nbsp;<%= MyRec4("C_Description") %>&nbsp;<%= MyRec4("Weight") %></option>"

 <%MyRec4.Movenext
 wend
 MyRec4.close %>
     </select></font></td></tr>
<tr>
    <td align="left" class="auto-style7" style="width: 25%">  
	<p>  <b><font face="Arial" size="2">Trailer</font></b><font face="Arial" size="2"><b>:&nbsp;&nbsp;</b></font></td>
    <td colspan="2" class="auto-style7">   <font face="Arial">   

      <input name="Trailer" size="24" value = "<%= strTrailer%>" style="font-weight: 700"></font>&nbsp;&nbsp;
      
   
      </td>
  </tr>

       <tr>  <td align = left class="auto-style6" style="width: 25%"> 
		   <font face="Arial" size="2">
			Weight from Shipping Ticket:&nbsp;</font></td>
<td align = left colspan="2" class="auto-style7">    <font face="Arial">  
<input name="SAP_Weight" size="15" value = "<%= strSAPWeight%>" style="font-weight: 700"> </td></tr>

<tr><td align="left" class="auto-style6" style="width: 25%"><font face="Arial" size="2">Unit of 
	Measure:&nbsp;</font></td>
    <td colspan="2" class="auto-style7">    <font face="Arial">  
	<input name="UOM" size="15" value = "<%= strUOM%>" style="font-weight: 700"></td>
  </tr>
       <tr><td  align = left class="auto-style7" style="height: 28" colspan="3" ></td>
	</tr>
       <tr><td  align = left class="auto-style6" style="width: 25%" ><font face="Arial" size="2">Date Received:&nbsp;</font></td>
<td align = left colspan="2" class="auto-style7"> <font face="Arial"> 
<input name="Date_Received" size="15" value = "<%= strDateReceived%>" style="font-weight: 700"></font></td></tr>
              <tr>
          <td  align = left class="auto-style6" style="width: 25%" >
   	   <font face="Arial" size="2">Site Shipped From:&nbsp;</font></td>
<td align = left colspan="2" class="auto-style6">
     <font face = arial size = 3 color = red><span class="style28"><font face="Arial"><input name="Generator" size="25" value = "<%= strGenerator %>" style="font-weight: 700" tabindex="8"></font></TD></tr>
   
              <tr>
          <td  align = left class="auto-style6" style="width: 25%" >
   <font face="Arial" size="2">Commodity:&nbsp;</font></td>
<td align = left colspan="2" class="auto-style7"><font face="Arial" size="2">KDF
      &nbsp;</TD></tr>
   
      
          <td  align = left class="auto-style6" style="width: 25%" >
  <font face="Arial" size="2">Comments/Other:&nbsp;</font></td >
   <td align = left colspan="2" class="auto-style7">   <font face="Arial">   
	<input name="Other" size="60" value = "<%= strOther%>" style="font-weight: 700"></font></td></tr>
<tr>
    <td class="auto-style7" colspan="3">&nbsp;</td>

  </tr>

  <tr>
    <td class="auto-style7" style="width: 25%">&nbsp;</td>

    <td align = left colspan="2" class="auto-style7"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" >&nbsp;&nbsp;&nbsp; 
	</font><span class="auto-style3">&nbsp;</span></td>
  </tr>
</table>

</div>

</form>
</body>
<% Function GetData()
  set objMOC = new ASP_CLS_FIBER
        set rstFiber = objMOC.FiberCarrier()
End Function

Function SaveData()

	strLoad  = Trim(Request.QueryString("id")) 
 	strTrailer = Request.form("Trailer")
	strOther = Replace(Request.form("Other"), "'", "''") 
	strDateReceived = Request.form("Date_received")
	strSAPWeight = request.form("SAP_Weight")
	strPO = request.form("PO")
	strBOL = request.form("BOL")	
	strCarrier = Request.form("Carrier")
	strLocation = "YARD"
	strGenerator = Replace(Request.form("Generator"), "'", "''")


Dim strRightNow
	strRightnow = Now()	
	
	strSAPWeight = round(REquest.form("SAP_Weight")/2000,2)
	strLoad = request.form("Load")
	strSAPNumber = request.form("SAP_Number")
	
	strsql =  "INSERT INTO tblCars (Carrier, Species, Grade, SAP_Nbr, Vendor, Date_received, STO_Number,  Location, "_
	&"  Generator,  Trailer, Other_Comments, Tons_Received, Net,   Entry_Time, Entry_BID, Entry_Page, Status, PO, BOL) "_
	&" SELECT   '" & strCarrier & "', 'KDF', 'NF', '" & strSAPNumber & "', '" & strGenerator & "',  '" & strDateReceived & "', '" & strLoad & "', 'YARD',  '" & strGenerator & "',   "_
	&" '" & strTrailer & "', '" & strOther & "',  " & strSAPWEight & ", " & strSAPWEight & ",   '" & strRightNow & "', "_
	&"  '" & Session("EmployeeID") & "', 'EnterSTOKDFReceipt', '" & strSAPWEight & "', '" & request.form("PO") & "','" & request.form("BOL") & "' "
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
			strsql5 = "SELECT Max(tblCars.CID) AS MaxOfCID FROM tblCars"
			
   	 Set MyRec5 = Server.CreateObject("ADODB.Recordset")
   	 MyRec5.Open strSQL5, Session("ConnectionString")
   	 strCarID = MyRec5.fields("MaxofCID")
   	 MyRec5.close
   	 
   	If Request.form("Print_receipt") =  "ON" then
			
				Response.redirect("STO_Wadding_Truck_receipt.asp?id=" & strCarID)
				
		else
		Response.redirect ("SelectSTO.asp")
		end if 
  
   	 
End Function %>

</html>
<!--#include file="Fiberfooter.inc"-->