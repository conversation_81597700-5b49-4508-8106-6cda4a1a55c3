																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Owensboro Pricing - Purchase Orders to Confirm</TITLE>
<style type="text/css">
.auto-style1 {
	border-width: 0;
}
.auto-style3 {
	border: 1px solid #C0C0C0;
	background-color: #EEEEEE;
}
.auto-style4 {
	border: 1px solid #C0C0C0;
	text-align: center;
	background-color: #EEEEEE;
}
.auto-style5 {
	text-align: center;
}
</style>
</head>

<!--#include file="classes/asp_cls_DataAccessAIG.asp"-->
<!--#include file="classes/asp_cls_headerAIG.asp"-->
<!--#include file="classes/asp_cls_SessionStringAIG.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_SessionOWB.asp"-->


<% Dim MyRec, strsql, MyConn, strUserType

 
    strUserType = ""
 
        If Session("EmployeeID") = "C97338" or Session("EmployeeID") = "B63398" or Session("EmployeeID") = "B17796"  then
    strUserType = "B"
    end if
    If strUserType = "" then

    Response.write ("<br><br><font face = arial size = 3><b>You do not have authorization to view this page</b></font>")
   else
    BuildScreen()
    end if 
 set objGeneral = new ASP_CLS_General

if objGeneral.IsSubmit() Then

	Call SaveData() 

End if

    %>
    <%Sub Buildscreen


strsql = "SELECT tblPricing.* FROM tblPricing "_
&" WHERE Site = 'OWB' AND Price Is Not Null  AND Do_not_invoice Is Null and Confirmed='No'"
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    
    

%>

<body>
<br>
	<form name="form1" action="OWB_Pricing_to_confirm.asp" method="post" >	

<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>
<tr>

<td align = center><b>
<font face="arial" size="4" >Owensboro Recovered Paper Purchase Orders to Confirm</font></b></td>
<td><input type="submit" value="Submit"></td>


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=75% class = "auto-style1" align = center>  
	 <tr class="tableheader">
	<td class="auto-style3">&nbsp;</td>
	<td class="auto-style3"  >  <font face="Arial" size="2"><strong>PO #</strong></font></td>
	<td class="auto-style3"  >  <font face="Arial" size="2"><strong>Vendor Number</strong></font></td>
		<td class="auto-style3"  >  <font face="Arial" size="2"><strong>Vendor Name</strong></font></td>
		<td class="auto-style3"  >  <font face="Arial" size="2"><strong>Generator</strong></font></td>
        <td class="auto-style3"  > <font face="Arial" size="2"><strong>Price</strong></font></td>
             <td class="auto-style4"  > <font face="Arial" size="2"><strong>Confirm</strong></font></td>
	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
                  strAIGPO = MyRec.fields("PO")

    strsql2 = "SELECT distinct PO FROM Master_Table where PO = '" & strAIGPO & "' and len(date_time_In) > 3 UNION SELECT distinct  PO FROM Master_Table_Backup where  PO = '" & strAIGPO & "'"
' Response.write("strsql2:" & strsql2)
	Set rs = Server.CreateObject("ADODB.Recordset")
 
 rs.Open strSQL2, Session("ConnectionOWB")

 
	if not rs.eof then

    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
	<% if strUsertype = "D" then %>
			<td>   <font size="2" face="Arial"> &nbsp;</font></td>
			<% else %>
	<td> <font size="2" face="Arial"><a href="AIG_Delete_pricing.asp?id=<%= MyRec.fields("ID") %>&p=o">Delete</a></td>	
<% end if %>
	<td  >      <font size="2" face="Arial">        <%= MyRec.fields("PO")%>&nbsp;</font></td>
      	<td  >      <font size="2" face="Arial">        <%= MyRec.fields("Vendor_nbr")%>&nbsp;</font></td>
      		<td  >      <font size="2" face="Arial">        <%= MyRec.fields("SAP_Vendor")%>&nbsp;</font></td>
      		<td  >      <font size="2" face="Arial">        <%= MyRec.fields("Generator")%>&nbsp;</font></td>
			<td  >        <font size="2" face="Arial">        <%= MyRec.fields("Price")%>&nbsp;</font></td>
		
					<% if strUsertype = "D" then %>
			<td>   <font size="2" face="Arial"> &nbsp;</font></td>
			<% else %>
	<td class="auto-style5"> <input type="checkbox" name="<%= MyRec("PO") %>" value="ON"></td>	
	<% end if %>
</tr>
 <% rs.close
 end if
       ii = ii + 1
       MyRec.MoveNext
     Wend
     MyRec.close
    %>
</table>
</form>

<% End Sub %>


<%   Function SaveData()

strsql = "SELECT tblPricing.* FROM tblPricing WHERE Site = 'OWB' AND Price Is Not Null  AND Do_not_invoice Is Null and Confirmed='No'"
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
   ' response.write("sql " & strsql)
    while not MyREc.eof
    
    If Request.form(MyREc("PO")) = "ON" then
    strMyPO = MyRec("PO")
    strsql2 = "Update tblPricing set Confirmed = 'Yes' where PO = '" & strMyPO & "'"
  	 set MyRec2 = new ASP_CLS_DataAccess
        MyRec2.ExecuteSql strSql2 

    end if
    MyREc.movenext
    wend
    MyRec.close
  
REsponse.redirect("OWB_pricing_to_Confirm.asp")
end Function %>



<!--#include file="AIGfooter.inc"-->