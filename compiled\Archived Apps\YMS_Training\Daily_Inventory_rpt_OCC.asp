
<html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">
<TITLE>Daily Inventory Report</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->

<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->
	
	
	   <style type="text/css">
.style1 {
	font-family: Arial;
	font-size: x-small;
}
.style4 {
	border: 1px solid #F0F0F0;
}
.style14 {
	border: 1px solid #F0F0F0;
	text-align: center;
	background-color: #F3F3F3;
	font-size: x-small;
}
.style15 {
	font-family: Arial;
}
.style16 {
	font-size: x-small;
}
.style17 {
	border: 1px solid #F0F0F0;
	text-align: center;
		font-family: Arial;
	background-color: #FFD7FF;
}
.style18 {
	border: 1px solid #000000;
}
.style21 {
	border: 1px solid #F0F0F0;
	font-family: Arial;
	font-size: x-small;
	text-align: center;
	background-color: #FFFFCC;
}
.style31 {
	border-width: 1px;
	background-color: #E8E8FF;
}
.style34 {
	border-color: #C0C0C0;
	border-width: 1px;
	text-align: center;
	font-family: Arial;
	font-size: x-small;
	background-color: #FAFBBF;
}
.style38 {
	border-color: #C0C0C0;
	border-width: 1px;
	text-align: center;
	font-family: Arial;
	font-size: x-small;
	background-color: #FFD7FF;
}
.style40 {
	border-left: 1px solid #C0C0C0;
	border-right: 1px solid #000080;
	border-top: 1px solid #C0C0C0;
	border-bottom: 1px solid #000080;
	background-color: #E8E8FF;
}
.style41 {
	border: 1px solid #808080;
	font-size: x-small;
}
.style44 {
	border: 1px solid #C0C0C0;
	text-align: center;
		font-family: Arial;
		font-size: x-small;
	background-color: #F3F3F3;
}
.style47 {
	border: 1px solid #F0F0F0;
	text-align: center;
	font-family: Arial;
	background-color: #F3F3F3;
	font-size: x-small;
}
.style48 {
	border: 1px solid #F0F0F0;
	font-family: Arial;
	font-size: x-small;
	text-align: center;
	background-color: #FFD7FF;
}
.style50 {
	border: 1px solid #F0F0F0;
	background-color: #E8E8FF;
}
.style54 {
	border-width: 1px;
	background-color: #FFECFF;
}
.style56 {
	border-width: 1px;
	background-color: #FCFDDB;
	text-align: center;
}
.style62 {
	border-width: 1px;
	background-color: #FFECFF;
	font-size: x-small;
	text-align: center;
}
.style67 {
	border: 1px solid #FFFFFF;
	background-color: #E8E8FF;
}
.style70 {
	border: 1px solid #FFFFFF;
	font-size: x-small;
	background-color: #FAFBBF;
}
.style71 {
	border-style: solid;
	border-width: 1px;
}
.style72 {
	border: 1px solid #000000;
	font-size: x-small;
	background-color: #FFFFDD;
	text-align: center;
}
.style73 {
	border: 1px solid #808080;
	font-size: x-small;
	background-color: #FFFFDD;
	text-align: center;
}
.style77 {
	border-left: 1px solid #C0C0C0;
	border-right: 1px solid #808080;
	border-top: 1px solid #C0C0C0;
	border-bottom: 1px solid #808080;
	font-size: x-small;
	text-align: center;
}
.style80 {
	border: 1px solid #808080;
	background-color: #FFFFDD;
	font-size: x-small;
}
.style81 {
	color: black;
	font-size: 10.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: 1.0pt solid windowtext;
	border-bottom: .5pt solid windowtext;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #E8E8FF;
}
.style82 {
	color: black;
	font-size: 10.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right: 1.0pt solid windowtext;
	border-top: 1.0pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #E8E8FF;
}
.style83 {
	color: black;
	font-size: 10.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: .5pt solid windowtext;
	border-bottom: 1.0pt solid windowtext;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #FCFBF5;
}
.style84 {
	color: black;
	font-size: 10.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left-style: none;
	border-left-color: inherit;
	border-left-width: medium;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: .5pt solid windowtext;
	border-bottom: 1.0pt solid windowtext;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #FCFBF5;
}
.style85 {
	color: black;
	font-size: 10.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left-style: none;
	border-left-color: inherit;
	border-left-width: medium;
	border-right: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom: 1.0pt solid windowtext;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #FCFBF5;
}
.style86 {
	color: black;
	font-size: 10.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom: 1.0pt solid windowtext;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #FCFBF5;
}
.style87 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top-style: none;
	border-top-color: inherit;
	border-top-width: medium;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #00B0F0;
}
.style88 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-style: none;
	border-color: inherit;
	border-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #00B0F0;
}
.style89 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left-style: none;
	border-left-color: inherit;
	border-left-width: medium;
	border-right: 1.0pt solid windowtext;
	border-top-style: none;
	border-top-color: inherit;
	border-top-width: medium;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #00B0F0;
}
.style90 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right: 1.0pt solid windowtext;
	border-top-style: none;
	border-top-color: inherit;
	border-top-width: medium;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #00B0F0;
}
.style91 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #92D050;
}
.style92 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left-style: none;
	border-left-color: inherit;
	border-left-width: medium;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #92D050;
}
.style93 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left-style: none;
	border-left-color: inherit;
	border-left-width: medium;
	border-right: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #92D050;
}
.style94 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #92D050;
}
.style95 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: yellow;
}
.style96 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left-style: none;
	border-left-color: inherit;
	border-left-width: medium;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: yellow;
}
.style97 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left-style: none;
	border-left-color: inherit;
	border-left-width: medium;
	border-right: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: yellow;
}
.style98 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: yellow;
}
.style99 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: red;
}
.style100 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left-style: none;
	border-left-color: inherit;
	border-left-width: medium;
	border-right-style: none;
	border-right-color: inherit;
	border-right-width: medium;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: red;
}
.style101 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left-style: none;
	border-left-color: inherit;
	border-left-width: medium;
	border-right: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: red;
}
.style102 {
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-right: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: red;
}
.style103 {
	font-size: xx-small;
}
.style104 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	background-color: #E8E8FF;
	text-align: center;
	font-size: x-small;
	font-weight: bold;
}
.style105 {
	border: 1px solid #E1E1E1;
	font-size: x-small;
	text-align: center;
}
.style106 {
	border: 1px solid #E1E1E1;
	font-size: x-small;
	text-align: center;
	font-weight: bold;
}
.style107 {
	font-size: x-small;
	font-weight: bold;
}
.style108 {
	border: 1px solid #F0F0F0;
	font-family: Arial;
	font-size: x-small;
	text-align: center;
	background-color: #FFD7FF;
	font-weight: bold;
}
.style109 {
	border-width: 1px;
	background-color: #FCFDDB;
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	text-align: center;
}
.style112 {
	border-width: 1px;
	background-color: #FCFDDB;
	font-size: x-small;
	text-align: center;
}
.style114 {
	border: 1px solid #F0F0F0;
	text-align: center;
	font-family: Arial;
	background-color: #F3F3F3;
	font-size: x-small;
	font-weight: bold;
}
.style115 {
	text-align: center;
}
.style117 {
	border-right: 1px solid #000000;
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: red;
}
.style118 {
	border-right: 1px solid #000000;
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: yellow;
}
.style119 {
	border-right: 1px solid #000000;
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #92D050;
}
.style120 {
	border-right: 1px solid #000000;
	color: black;
	font-size: 10.0pt;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-top-style: none;
	border-top-color: inherit;
	border-top-width: medium;
	border-bottom-style: none;
	border-bottom-color: inherit;
	border-bottom-width: medium;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #00B0F0;
}
.style121 {
	border-right: 1px solid #000000;
	color: black;
	font-size: 10.0pt;
	font-weight: 700;
	font-style: normal;
	text-decoration: none;
	font-family: Arial, sans-serif;
	text-align: center;
	vertical-align: middle;
	white-space: normal;
	border-left: 1.0pt solid windowtext;
	border-top: .5pt solid windowtext;
	border-bottom: 1.0pt solid windowtext;
	padding-left: 1px;
	padding-right: 1px;
	padding-top: 1px;
	background: #FCFBF5;
}
.style122 {
	text-align: center;
	font-size: x-small;
}
.style123 {
	border: 1px solid #F0F0F0;
	text-align: center;
	background-color: #F3F3F3;
	font-size: x-small;
	font-weight: bold;
}
	   .auto-style2 {
		   border: 1px solid #000000;
		   font-size: x-small;
		   background-color: #E2F3FE;
		   text-align: center;
	   }
	   .auto-style3 {
		   border-left: 1px solid #C0C0C0;
		   border-right: 1px solid #808080;
		   border-top: 1px solid #C0C0C0;
		   border-bottom: 1px solid #808080;
		   font-size: x-small;
		   background-color: #E2F3FE;
		   text-align: center;
	   }
	   .auto-style4 {
		   border-left: 1px solid #C0C0C0;
		   border-right: 1px solid #808080;
		   border-top: 1px solid #C0C0C0;
		   border-bottom: 1px solid #808080;
		   background-color: #E2F3FE;
		   font-size: x-small;
	   }
	   .auto-style6 {
		   color: black;
		   font-size: 10.0pt;
		   font-weight: 700;
		   font-style: normal;
		   text-decoration: none;
		   font-family: Arial, sans-serif;
		   text-align: center;
		   vertical-align: middle;
		   white-space: normal;
		   border-left: 1.0pt solid windowtext;
		   border-right-style: none;
		   border-right-color: inherit;
		   border-right-width: medium;
		   border-top: 1.0pt solid windowtext;
		   border-bottom: .5pt solid windowtext;
		   padding-left: 1px;
		   padding-right: 1px;
		   padding-top: 1px;
		   background: #E2F3FE;
	   }
	   .auto-style7 {
		   color: black;
		   font-size: 10.0pt;
		   font-weight: 700;
		   font-style: normal;
		   text-decoration: none;
		   font-family: Arial, sans-serif;
		   text-align: center;
		   vertical-align: middle;
		   white-space: normal;
		   border-left: 1.0pt solid windowtext;
		   border-right: 1.0pt solid windowtext;
		   border-top: 1.0pt solid windowtext;
		   border-bottom-style: none;
		   border-bottom-color: inherit;
		   border-bottom-width: medium;
		   padding-left: 1px;
		   padding-right: 1px;
		   padding-top: 1px;
		   background: #E2F3FE;
	   }
	   .auto-style17 {
		   border: 1px solid #000000;
		   border-collapse: collapse;
	   }
	   .auto-style18 {
		   color: black;
		   font-size: x-small;
		   font-weight: 400;
		   font-style: normal;
		   text-decoration: none;
		   font-family: Arial, sans-serif;
		   text-align: center;
		   vertical-align: middle;
		   white-space: normal;
		   border-left: 1.0pt solid windowtext;
		   border-right-style: none;
		   border-right-color: inherit;
		   border-right-width: medium;
		   border-top-style: none;
		   border-top-color: inherit;
		   border-top-width: medium;
		   border-bottom-style: none;
		   border-bottom-color: inherit;
		   border-bottom-width: medium;
		   padding-left: 1px;
		   padding-right: 1px;
		   padding-top: 1px;
		   background: #00B0F0;
	   }
	   .auto-style19 {
		   border: 1px solid #C0C0C0;
		   font-size: x-small;
		   text-align: center;
		   	background-color: #F3F3F3;	   }
	   .auto-style20 {
		   background-color: #E7EBFE;
	   }
	   .auto-style21 {
		   border: 1px solid #E1E1E1;
		   font-size: x-small;
		   text-align: center;
		   background-color: #E7EBFE;
	   }
	   .auto-style23 {
		   border: 1px solid #C0C0C0;
		   text-align: center;
		   background-color: #E7EBFE;
		   font-size: x-small;
		   font-weight: bold;
	   }
	   .auto-style24 {
		   border: 1px solid #C0C0C0;
		   text-align: center;
		   font-family: Arial;
		   background-color: #E7EBFE;
		   font-size: x-small;
		   font-weight: bold;
	   }
	   .auto-style25 {
		   border: 1px solid #C0C0C0;
		   text-align: center;
		   font-family: Arial;
		   font-size: x-small;
		   	background-color: #F3F3F3;
	   }
	   .auto-style26 {
		   border: 1px solid #C0C0C0;
		   font-family: Arial;
		   font-size: x-small;
		   text-align: center;
		   background-color: #FFD7FF;
		   font-weight: bold;
	   }
	   .auto-style27 {
		   border: 1px solid #C0C0C0;
		   text-align: center;
		   font-family: Arial;
		   font-size: x-small;
		   background-color: #FFD7FF;
	   }
	   .auto-style28 {
		 	border: 1px solid #C0C0C0;
		 	font-family: Arial;
		 	font-size: x-small;
		   font-weight: bold;
		   text-align: center;
		   background-color: #E2F3FE;
	   }
	   .auto-style29 {
		  border: 1px solid #C0C0C0;
		 	font-family: Arial;
		   font-size: x-small;
			text-align: center;
		   background-color: #E2F3FE;
	   }
	   .auto-style32 {
		   border-left: 1px solid #C0C0C0;
		   border-top: 1px solid #C0C0C0;
		   background-color: #E7EBFE;
		   font-size: x-small;
		   text-align: center;
		   border-right-style: solid;
		   border-right-width: 1px;
		   border-bottom-style: solid;
		   border-bottom-width: 1px;
	   }
	   .auto-style33 {
		   border-left: 1px solid #C0C0C0;
		   border-right: 1px solid #808080;
		   border-top: 1px solid #C0C0C0;
		   border-bottom: 1px solid #808080;
		   font-size: x-small;
	   }
	   .auto-style34 {
		   border-left: 1px solid #C0C0C0;
		   border-right: 1px solid #000000;
		   border-top: 1px solid #C0C0C0;
		   border-bottom: 1px solid #000000;
		   font-size: x-small;
		   background-color: #E2F3FE;
		   text-align: center;
	   }
	   .auto-style35 {
		   border-left: 1px solid #C0C0C0;
		   border-top: 1px solid #C0C0C0;
		   background-color: #E7EBFE;
		   border-right-style: solid;
		   border-right-width: 1px;
		   border-bottom-style: solid;
		   border-bottom-width: 1px;
	   }
	   .auto-style36 {
		   border: 1px solid #C0C0C0;
		   text-align: center;
		   font-family: Arial;
		   font-size: x-small;
		   background-color: #FFECFF;
	   }
	   </style>
</head>
<%  '  1812 ends first section of Previous Consumption
	Dim strsQL, rstDaily, strBegDate, strEndDate, strcount, objPro, strCdate, strKBLD, strOther, strOCC, strMonthName, strMonthDay, strDayofweek
	Dim strdate, objDAC, strOCCRail, strOtherRail, strKBLDRail, strOCCAvg,  strKBLDAvg, strnow, MyConn, strTday7
	Dim strdate1, strYdate
	Dim strOB, MyRec2, strsql2
	Dim KBLD_one, KBLD_Two, KBLD_Three, KBLD_Four, KBLD_Five, KBLD_Six, KBLD_Seven
	DIm OCC_one, OCC_Two, OCC_Three, OCC_Four, OCC_Five, OCC_six, OCC_Seven, MyRec8, strSQL8
	Dim strMXP, MXP_One, MXP_Two, MXP_three, MXP_Four, MXP_Five, MXP_Six, MXP_Seven, strMXPRail, strMXPAvg
	
	strnow = formatdatetime(Now(),2)
	strBegdate = dateadd("d", -7, strnow)
	strEnddate = formatdatetime(now())
	strTomorrow = dateadd("d", 1, strnow)
	strYdate = dateadd("d", -3, now())
		
	%>

<p class="style1"><strong>KC MOBILE Daily OCC REPORT<br><%= Now()%> </strong></p>
<font face="arial" size="1">
<table class="style4" style="width: 70%" cellspacing="0">
<tr><td class="auto-style4" rowspan="3">&nbsp;</td>
	<td class="auto-style4" rowspan="3"><strong>Previous Days Consumption for Reference</strong></td>
	<td class="auto-style2" colspan="2">OCC BROWN</td>
	<td class="auto-style2" colspan="5">OCC WHITE</td>

	</tr>
<tr>
	<td class="auto-style34" colspan="2"><font face="arial" size="1">Trailers/Rail</td>
	<td class="auto-style34" colspan="5"><font face="arial" size="1">Trailers/Rail <span class="style103">&nbsp;&nbsp; </span></td>

	</tr>
<tr><td class="auto-style3">OCC</td>
 
			<td class="auto-style3">MXP</td>
 
			<td class="auto-style3">USBS</td>
 
			<td class="auto-style3">SWL</td>

	 
	<td class="auto-style3">LPSBS</td>
	<td class="auto-style3">HWM</td>
	<td class="auto-style3">OCC SHRED</td>
	</tr>
	<tr><td class="auto-style33"></td>
  <td   class="style77">Last 30 Day Avg (In TL)</td>
  <% str30Date = dateadd("d", -31, date())
    strTodayDate = date()
    str07Date = dateadd("d", -7, date())

   
  
  strsql = "SELECT tblCars.Species, Shred_OCC, Count(tblCars.CID) AS CCID FROM tblCars "_
&" WHERE  Inv_depletion_date >'" & str30Date & "' and  Inv_depletion_date < '" & strTodayDate & "' "_
&" GROUP BY tblCars.Species, Shred_OCC ORDER BY tblCars.Species"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
While not MyRec.eof  
If MyRec("Species") = "OCC" then 
str30OCC = MyRec("CCID")
elseif MyRec("Species") = "MXP" then
str30MXP= MyRec("CCID")
elseif MyRec("Species") = "USBS" then
str30USBS = MyRec("CCID")
elseif MyRec("Species") = "SHRED" and Shred_OCC = "Y" then
str30SHRED = MyRec("CCID")
elseif MyRec("Species") = "SWL" then
str30SWL= MyRec("CCID")
elseif MyRec("Species") = "LPSBS" then
str30LPSBS= MyRec("CCID")
elseif MyRec("Species") = "HWM" then
str30HWM= MyRec("CCID")

end if
MyRec.movenext
wend
MyRec.close


  strsql = "SELECT tblCars.Species, Shred_OCC, Count(tblCars.CID) AS CCID FROM tblCars "_
&" WHERE  Inv_depletion_date >'" & str30Date & "' and  Inv_depletion_date < '" & strTodayDate & "' and carrier = 'RAIL'  "_
&" GROUP BY tblCars.Species, Shred_OCC ORDER BY tblCars.Species"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
While not MyRec.eof  
If MyRec("Species") = "OCC" then 
strR30OCC = MyRec("CCID")
elseif MyRec("Species") = "MXP" then
strR30MXP= MyRec("CCID")
elseif MyRec("Species") = "USBS" then
strR30USBS = MyRec("CCID")
elseif MyRec("Species") = "SHRED" and Shred_OCC = "Y" then
strR30SHRED = MyRec("CCID")
elseif MyRec("Species") = "SWL" then
strR30SWL= MyRec("CCID")
elseif MyRec("Species") = "LPSBS" then
strR30LPSBS= MyRec("CCID")
elseif MyRec("Species") = "HWM" then
strR30HWM= MyRec("CCID")

end if
MyRec.movenext
wend
MyRec.close

  strsql = "SELECT tblCars.Species, Shred_OCC, Count(tblCars.CID) AS CCID FROM tblCars "_
&" WHERE  Inv_depletion_date >'" & str07Date & "' and  Inv_depletion_date < '" & strTodayDate & "'   "_
&" GROUP BY tblCars.Species, Shred_OCC ORDER BY tblCars.Species"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
While not MyRec.eof  
If MyRec("Species") = "OCC" then 
str07OCC = MyRec("CCID")
elseif MyRec("Species") = "MXP" then
str07MXP= MyRec("CCID")
elseif MyRec("Species") = "USBS" then
str07USBS = MyRec("CCID")
elseif MyRec("Species") = "SHRED" and Shred_OCC = "Y" then
str07SHRED = MyRec("CCID")
elseif MyRec("Species") = "SWL" then
str07SWL= MyRec("CCID")
elseif MyRec("Species") = "LPSBS" then
str07LPSBS= MyRec("CCID")
elseif MyRec("Species") = "HWM" then
str07HWM= MyRec("CCID")
end if
MyRec.movenext
wend
MyRec.close


  strsql = "SELECT tblCars.Species, Shred_OCC, Count(tblCars.CID) AS CCID FROM tblCars "_
&" WHERE  Inv_depletion_date >'" & str07Date & "' and  Inv_depletion_date < '" & strTodayDate & "' and carrier = 'RAIL'   "_
&" GROUP BY tblCars.Species, Shred_OCC ORDER BY tblCars.Species"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
While not MyRec.eof  
If MyRec("Species") = "OCC" then 
strR07OCC = MyRec("CCID")
elseif MyRec("Species") = "MXP" then
strR07MXP= MyRec("CCID")
elseif MyRec("Species") = "USBS" then
strR07USBS = MyRec("CCID")
elseif MyRec("Species") = "SHRED" and Shred_OCC = "Y" then
strR07SHRED = MyRec("CCID")
elseif MyRec("Species") = "SWL" then
strR07SWL= MyRec("CCID")
elseif MyRec("Species") = "LPSBS" then
strR07LPSBS= MyRec("CCID")
elseif MyRec("Species") = "HWM" then
strR07HWM= MyRec("CCID")
end if
MyRec.movenext
wend
MyRec.close
 


%>

<td class="style77"> <%= round(( str30OCC + (strR30OCC * 3))/30,1) %> &nbsp;   </td>
 
<% strOCCRail = strR30OCC
strOCCTrailer = str30OCC   %>

<td class="style77"> <%= round(( str30MXP + (strR30MXP * 3))/30,1) %> &nbsp;   </td>
 
<% strMXPRail = strR30MXP
strMXPTrailer = str30MXP   %>
 
<td class="style77"> <%= round(( str30USBS + (strR30USBS * 3))/30,1) %> &nbsp;   </td>
 
<% strUSBSRail = strR30USBS
strUSBSTrailer = str30USBS   %>
 
<td class="style77"> <%= round(( str30SWL + (strR30SWL * 3))/30,1) %> &nbsp;   </td>
 
<% strSWLRail = strR30SWL
strSWLTrailer = str30SWL   %>

<td class="style77"> <%= round(( str30LPSBS + (strR30LPSBS * 3))/30,1) %> &nbsp;   </td>
 
<% strLPSBSRail = strR30LPSBS
strLPSBSTrailer = str30LPSBS   %>

<td class="style77"> <%= round(( str30HWM + (strR30HWM * 3))/30,1) %> &nbsp;   </td>
 
<% strHWMRail = strR30HWM
strHWMTrailer = str30HWM   %>

<td class="style77"> <%= round(( str30SHRED + (strR30SHRED * 3))/30,1) %> &nbsp;   </td>
 
<% strSHREDRail = strR30SHRED
strSHREDTrailer = str30SHRED   %>

</tr>
<tr><td class="auto-style33"></td>
  <td   class="style77">Last 7 Day Avg (In TL)</td>
 <td class="style77"> <%= round(( str07OCC + (strR07OCC * 3))/07,1) %> &nbsp;   </td>
 
<% strOCCRail = strR07OCC
strOCCTrailer = str07OCC   %>

<td class="style77"> <%= round(( str07MXP + (strR07MXP * 3))/07,1) %> &nbsp;   </td>
 
<% strMXPRail = strR07MXP
strMXPTrailer = str07MXP   %>
 
<td class="style77"> <%= round(( str07USBS + (strR07USBS * 3))/07,1) %> &nbsp;   </td>
 
<% strUSBSRail = strR07USBS
strUSBSTrailer = str07USBS   %>
 
<td class="style77"> <%= round(( str07SWL + (strR07SWL * 3))/07,1) %> &nbsp;   </td>
 
<% strSWLRail = strR07SWL
strSWLTrailer = str07SWL   %>

<td class="style77"> <%= round(( str07LPSBS + (strR07LPSBS * 3))/07,1) %> &nbsp;   </td>
 
<% strLPSBSRail = strR07LPSBS
strLPSBSTrailer = str07LPSBS   %>

<td class="style77"> <%= round(( str07HWM + (strR07HWM * 3))/07,1) %> &nbsp;   </td>
 
<% strHWMRail = strR07HWM
strHWMTrailer = str07HWM   %>

<td class="style77"> <%= round(( str07SHRED + (strR07SHRED * 3))/07,1) %> &nbsp;   </td>
 
<% strSHREDRail = strR07SHRED
strSHREDTrailer = str07SHRED   %>

</tr>
 

<%   	strnow = formatdatetime(Now(),2)
	strBegdate7 = dateadd("d", -7, strnow)
	strBegDate6 = dateadd("d", -6, strnow)
	strBegDate5 = dateadd("d", -5, strnow)
	strBegDate4 = dateadd("d", -4, strnow)
	strBegDate3 = dateadd("d", -3, strnow)
	strBegDate2 = dateadd("d", -2, strnow)
	strBegDate1 = dateadd("d", -1, strnow)

	
	strEnddate = formatdatetime(now())
	
	Function getweekday(strD)

 	if weekday(strD)  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif  weekday(strD)   = 2 then
 	 strDayofweek =  "Monday" 
 	elseif   weekday(strD)   = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif  weekday(strD)   = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif   weekday(strD)  = 5 then
 	 strDayofweek =  "Thursday"
 	elseif   weekday(strD)   = 6 then
 	 strDayofweek =  "Friday"
 	elseif  weekday(strD)  = 7 then
 	 strDayofweek =  "Saturday"
 	end if
 	
 	end Function
 	
 	call  getweekday(strBegdate7)
  
 	 call getrow( strBegDate7, strBegDate6)
 	 	call  getweekday(strBegdate6)
 	 call getrow(strBegDate6, strBegDate5)
 	 	call  getweekday(strBegdate5)
 	 call getrow(strBegDate5, strBegDate4)
 	 	call  getweekday(strBegdate4)
 	 call getrow(strBegDate4, strBegDate3)
 	 	call  getweekday(strBegdate3)
 	 call getrow(strBegDate3, strBegDate2)
 	 	call  getweekday(strBegdate2)
 	 call getrow(strBegDate2, strBegDate1)
 	 	call  getweekday(strBegdate1)
 	 call getrow(strBegDate1, strnow)
 	 	call  getweekday(strnow)
 	 call getrow(strnow, strTomorrow)
 	 
 	Function  getRow( strDA, strDA1)
 	
%>
<tr><td class="auto-style33"><font face="arial"><%= strDA %></td>
  <% If datepart("d", strDA) = datepart("d", strnow) then %>
  <td   class="style77"><font face="arial">Consumed since midnight TODAY </td>
  <% else %>
<td class="style77"><font face="arial"><%= strDayofWeek %>  </td>
<% end if %>
 
<td class="style77">
<% strCountOCC = 0
strCountOCCR = 0

strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'OCC'  and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and (carrier <> 'RAIL' or carrier is null)"
 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
strCountOCC = MyREc("CountofCID")
end if

 strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'OCC'  and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and carrier ='RAIL'"

   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then 
strCountOCCR =  MyREc("CountofCID")  
end if

%>
<% if strCOuntOCC = 0 then %>
&nbsp;
<% else %>
<%= strCountOCC %>  
<% end if %>
<% if strCountOCCR > 0 then %>
/  <%= strCountOCCR %>
<% end if %></td>

<td class="style77">
<% strCountMXP = 0
strCountMXPR = 0

strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'MXP'  and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and (carrier <> 'RAIL' or carrier is null)"
 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
strCountMXP = MyREc("CountofCID")
end if

 strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'MXP'  and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and carrier ='RAIL'"

   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then 
strCountMXPR =  MyREc("CountofCID")  
end if

%>
<% if strCOuntMXP = 0 then %>
&nbsp;
<% else %>
<%= strCountMXP %>  
<% end if %>
<% if strCountMXPR > 0 then %>
/  <%= strCountMXPR %>
<% end if %></td>

 
 



<td class="style77">
<% strCountUSBS = 0
strCountUSBSR = 0

strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'USBS'  and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and (carrier <> 'RAIL' or carrier is null)"
 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
strCountUSBS = MyREc("CountofCID")
end if

 strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'USBS'  and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and carrier ='RAIL'"

   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then 
strCountUSBSR =  MyREc("CountofCID")  
end if

%>
<% if strCOuntUSBS = 0 then %>
&nbsp;
<% else %>
<%= strCountUSBS %>  
<% end if %>
<% if strCountUSBSR > 0 then %>
/  <%= strCountUSBSR %>
<% end if %></td>
<td class="style77">
<% strCountSWL = 0
strCountSWLR = 0

strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'SWL'  and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and (carrier <> 'RAIL' or carrier is null)"
 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
strCountSWL = MyREc("CountofCID")
end if

 strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'SWL'  and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and carrier ='RAIL'"

   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then 
strCountSWLR =  MyREc("CountofCID")  
end if

%>
<% if strCOuntSWL = 0 then %>
&nbsp;
<% else %>
<%= strCountSWL %>  
<% end if %>
<% if strCountSWLR > 0 then %>
/  <%= strCountSWLR %>
<% end if %></td>

<td class="style77">
<% strCountLPSBS = 0
strCountLPSBSR = 0

strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'LPSBS'  and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and (carrier <> 'RAIL' or carrier is null)"
 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
strCountLPSBS = MyREc("CountofCID")
end if

 strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'LPSBS'  and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and carrier ='RAIL'"

   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then 
strCountLPSBSR =  MyREc("CountofCID")  
end if

%>
<% if strCOuntLPSBS = 0 then %>
&nbsp;
<% else %>
<%= strCountLPSBS %>  
<% end if %>
<% if strCountLPSBSR > 0 then %>
/  <%= strCountLPSBSR %>
<% end if %></td>

<td class="style77">
<% strCountSHRED = 0
strCountSHREDR = 0

strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'SHRED' and SHRED_OCC = -1 and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and (carrier <> 'RAIL' or carrier is null)"
 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
strCountSHRED = MyREc("CountofCID")
end if

 strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'SHRED' and SHRED_OCC = -1 and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and carrier ='RAIL'"

   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then 
strCountSHREDR =  MyREc("CountofCID")  
end if

%>
<% if strCOuntSHRED = 0 then %>
&nbsp;
<% else %>
<%= strCountSHRED %>  
<% end if %>
<% if strCountSHREDR > 0 then %>
/  <%= strCountSHREDR %>
<% end if %></td>

<td class="style77">
<% strCountHWM = 0
strCountHWMR = 0

strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'HWM'  and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and (carrier <> 'RAIL' or carrier is null)"
 
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
strCountHWM = MyREc("CountofCID")
end if

 strsql = "SELECT Count( CID) AS CountOfCID FROM tblCars where Species = 'HWM'  and Inv_Depletion_Date >= '" & strDA & "' and Inv_Depletion_date <= '" & strDA1 & "' and carrier ='RAIL'"

   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then 
strCountHWMR =  MyREc("CountofCID")  
end if

%>
<% if strCOuntHWM = 0 then %>
&nbsp;
<% else %>
<%= strCountHWM %>  
<% end if %>
<% if strCountHWMR > 0 then %>
/  <%= strCountHWMR %>
<% end if %></td>


</tr>
<% end function %>
</table><p class="style1"><strong>

 <%   Dim strKBLD1, strPMX1, strOCC1, strKBLD2, strPMX2, strOCC2, strKBLD3, strPMX3, strOCC3, strKBLD4, strPMX4, strOCC4, strsql9
  Dim strKBLD5, strPMX5, strOCC5, strKBLD6, strPMX6, strOCC6, strKBLD7, strPMX7, strOCC7, strTday, strTD, strTM, strYear
  Dim strTD1, strTM1, strTday1
  strTday = formatdatetime(now(),2)
  strYear = datepart("yyyy", strTday)
  strTD = datepart("d", strTday)
  strTM = datepart("m", strTDay)
  strTday1 = dateadd("d", 1, strTday)
    strTD1 = datepart("d", strTday1)
  strTM1 = datepart("m", strTDay1)
  strTday2 = dateadd("d", 2, strTday)
    strTD2 = datepart("d", strTday2)
  strTM2 = datepart("m", strTDay2)
  strTday3 = dateadd("d", 3, strTday)
    strTD3 = datepart("d", strTday3)
  strTM3 = datepart("m", strTDay3)
  
    strTday4 = dateadd("d", 4, strTday)
    strTD4 = datepart("d", strTday4)
  strTM4 = datepart("m", strTDay4)


  strTday5 = dateadd("d", 5, strTday)
    strTD5 = datepart("d", strTday5)
  strTM5 = datepart("m", strTDay5)


  strTday6 = dateadd("d", 6, strTday)
    strTD6 = datepart("d", strTday6)
  strTM6 = datepart("m", strTDay6)
  
  
  strConsumptionDate = strTday6
  call  Consumption()
  
   strConsumptionDate = dateadd("d", 7, strTday )
    call  Consumption()
    
      strConsumptionDate = dateadd("d", 8, strTday )
	  call  Consumption()
	 
	 strConsumptionDate =  dateadd("d", 9, strTday )
  call  Consumption()

  
  Function Consumption()
  
      strsql = "Select Total_OCC from tblTempYardTotals where INV_Date = '" & strConsumptionDate & "'"
     Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
if not MyRec.eof then
' do nothing
else

  	
  		strsql8 = "Select tblTempYardTotals.* from tblTempYardTotals where ID = 16 "
  	    Set MyRec8 = Server.CreateObject("ADODB.Recordset")
   MyRec8.Open strSQL8, Session("ConnectionString") 
   RFDefault = MyRec8.fields("Total_RF")
   OCCDefault = MyRec8.fields("Total_OCC")
     MXPDefault = MyRec8.fields("Total_MXP")
   SHREDDefault = MyRec8("SHRED_T1")
 
 	HBXDefault = MyRec8.fields("HBX")
	PMXDefault = MyRec8.fields("PMX")
	
	KBLDDefault = Myrec8("KBLD")
	OF3Default = MyRec8("OF3")
	SWLDefault = MyRec8("SWL")
	USBSDefault = MyRec8("USBS")
	LPSBSDefault = MYrec8("LPSBS")
	HWMDefault = MyRec8("HWM")
	ShredOCCDefault = MyRec8("Shred_OCC")
 
If len(SWLDefault) > 0 then
  	'do nothing
  	else
  	SWLDefault = 0
  	end if
 
 	If len(USBSDefault) > 0 then
  	'do nothing
  	else
  	USBSDefault = 0
  	end if
  	
  	If len(LPSBSDefault) > 0 then
  	' do nothing
  	else
  	LPSBSDefault = 0
  	end if
 
   		If len(HWMDefault) > 0 then
  	' do nothing
  	else
  	HWMDefault = 0
  	end if
 
  
   		If len(ShredOCCDefault) > 0 then
  ' do nothing
  	else
  	ShredOCCDefault = 0
  	end if
 

   
   MyRec8.close
 

  	
  	
	strsql9 =  "INSERT INTO tblTempYardTotals ( INV_Date, total_RF,   total_OCC, total_MXP, KBLD,   OF3, PMX, Shred_t1,  HBX, SWL, USBS, LPSBS, HWM, Shred_OCC) "_
	&" SELECT '" & StrConsumptionDate & "', " & RFDefault & ", " & OCCDefault & ", " & MXPDefault & ", " & KBLDDefault & ",   "_
	&" " & OF3Default & ", " & PMXDefault & ", " & ShredDefault & ",  " & HBXDefault & ", " & SWLDefault & ", " & USBSDefault & ", " & LPSBSDefault & ", "_
	&" " & HWMDefault & ", " & ShredOCCDefault & ""
	
	'Response.write ("strsql9 " & strsql9)
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL9
			MyConn.Close

  
end if
MyRec.close

End Function %>
  
 <br>
<font face="arial" size="2"><b>Legend - Yard Targets</b></font>
<table cellpadding="0" width="550" style="width:340pt" class="auto-style17">
	<colgroup>
		<col width="55" span="8" style="mso-width-source:userset;mso-width-alt:2011;
 width:41pt">
	</colgroup>
	<tr height="20">
		<td height="20"  style="height: 15.0pt; " class="auto-style6" colspan="2">
		&nbsp;OCC BROWN<font face="arial" size="1"><strong>&nbsp;&nbsp; </td>
		<td     class="auto-style7" colspan="5">OCC WHITE<font face="arial" size="1"><strong>&nbsp; </td>
	</tr>
	<tr height="20">
		<td height="20"  style="height: 15.0pt; " class="auto-style6">
		OCC</td>
		<td    class="auto-style7">MXP</td>
		<td     class="auto-style7">USBS</td>
		<td     class="auto-style7">SWL</td>
		<td     class="auto-style7">LPSBS</td>
		<td     class="auto-style7">HWM</td>
		<td     class="auto-style7">OCC SHRED</td>
	</tr>
	<tr height="20">
		<td height="20" width="55" style="height: 15.0pt; width: 41pt" class="style87">
				&gt;10</td>
	
		<td width="55" style="width: 41pt" class="auto-style18">
<font face="arial"><strong>

 	   &gt;4</td>
		<td width="55" style="width: 41pt" class="style90">&gt;2</td>
		<td width="55" style="width: 41pt" class="style90">&gt;6</td>
		<td width="55" style="width: 41pt" class="style90">&gt;6</td>
		<td width="55" style="width: 41pt" class="style90">&gt;2</td>
		<td width="55" style="width: 41pt" class="style90">&gt;6</td>
	</tr>
	<tr height="20">
		<td height="20" width="55" style="height: 15.0pt; width: 41pt" class="style91">
		5-10</td>
 
		<td width="55" style="width: 41pt" class="style91">2-4</td>
		<td width="55" style="width: 41pt" class="style94">2</td>
		<td width="55" style="width: 41pt" class="style94">3-6</td>
		<td width="55" style="width: 41pt" class="style94">3-6</td>
		<td width="55" style="width: 41pt" class="style94">2</td>
		<td width="55" style="width: 41pt" class="style94">3-6</td>
	</tr>
	<tr height="20">
		<td height="20" width="55" style="height: 15.0pt; width: 41pt" class="style95">
		1-4</td>
 
		<td width="55" style="width: 41pt" class="style95">1</td>
		<td width="55" style="width: 41pt" class="style98">1</td>
		<td width="55" style="width: 41pt" class="style98">1-2</td>
		<td width="55" style="width: 41pt" class="style98">1-2</td>
		<td width="55" style="width: 41pt" class="style98">1</td>
		<td width="55" style="width: 41pt" class="style98">1-2</td>
	</tr>
	<tr height="20">
		<td height="20" width="55" style="height: 15.0pt; width: 41pt" class="style99">
		0</td>
	 
		<td width="55" style="width: 41pt" class="style99">0</td>
		<td width="55" style="width: 41pt" class="style102">0</td>
		<td width="55" style="width: 41pt" class="style102">0</td>
		<td width="55" style="width: 41pt" class="style102">0</td>
		<td width="55" style="width: 41pt" class="style102">0</td>
		<td width="55" style="width: 41pt" class="style102">0</td>
	</tr>
</table> 

<br>
Current Yard Inventory
<table style="width: 65%" class="style18" cellspacing="0">
 
		<tr>	<td  class="auto-style20" style="height: 25">&nbsp;</td>

			<td class="auto-style23" colspan="2" style="height: 30px">OCC BROWN</td> 
			<td class="auto-style23" colspan="5" style="height: 30px">OCC WHITE</td>
			<td class="auto-style23" style="height: 30px">OCC Brown</td>
			<td class="auto-style23" style="height: 30px">OCC White</td>
		</tr>
		<tr><td class="auto-style21">&nbsp;</td><td class="auto-style19">OCC</td> 
			<td class="auto-style19">MXP</td>
			<td class="auto-style19">USBS</td>
			<td class="auto-style19">SWL</td><td class="auto-style19">LPSBS</td>
			<td class="auto-style19">HWM</td>
			<td class="auto-style19">OCC SHRED</td>
			<td class="auto-style19">Total </td>
			<td class="auto-style19">Total </td>
		</tr>
		
	

<% strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars WHERE Species = 'USBS' and (Carrier <> 'RAIL' or Carrier is null)   and Rejected is null and Net > 0 and  Location = 'YARD'"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
XUSBS = MyRec("CountofCID")
end if
MyRec.close

 

 strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars WHERE Species = 'USBS' and Carrier= 'RAIL'     and Rejected is null and Net > 0 and  Location = 'YARD'"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
RXUSBS = MyRec("CountofCID")
end if
MyRec.close


strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars WHERE Species = 'SWL' and (Carrier <> 'RAIL' or Carrier is null)   and Rejected is null and Net > 0 and  Location = 'YARD'"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
XSWL = MyRec("CountofCID")
end if
MyRec.close


 strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars WHERE Species = 'SWL' and Carrier= 'RAIL'   and Rejected is null and Net > 0 and  Location = 'YARD'"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
RXSWL = MyRec("CountofCID")
end if
MyRec.close

 strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars WHERE Species = 'SHRED' and (SHRED_OCC = -1) and (Carrier <> 'RAIL' or Carrier is null)   and Rejected is null and Net > 0 and  Location = 'YARD'"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
XSHRED = MyRec("CountofCID")
end if
MyRec.close


 strsql = "SELECT Count(tblCars.CID) AS CountOfCID FROM tblCars WHERE Species = 'SHRED' and (SHRED_OCC = -1) and Carrier= 'RAIL'   and Rejected is null and Net > 0 and  Location = 'YARD'"
  Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
RXSHRED = MyRec("CountofCID")
end if
MyRec.close




strsql = "SELECT    Count(tblCars.CID) AS CountOfCID FROM tblCars   WHERE Species = 'LPSBS'  and (Carrier <> 'RAIL' or Carrier is null)   and Rejected is null and Net > 0 and  Location = 'YARD' "
 Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
XLPSBS = MyRec("CountofCID")
else
XLPSBS = 0
End if

strsql = "SELECT    Count(tblCars.CID) AS CountOfCID FROM tblCars   WHERE (Species = 'HWM') and (Carrier <> 'RAIL' or Carrier is null)   and Rejected is null and Net > 0 and  Location = 'YARD' "
 Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
XHWM = MyRec("CountofCID")
else
XHWM= 0
End if


strsql = "SELECT    Count(tblCars.CID) AS CountOfCID FROM tblCars   WHERE Species = 'LPSBS'  and Carrier= 'RAIL'    and Rejected is null and Net > 0 and  Location = 'YARD' "
 Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
RXLPSBS = MyRec("CountofCID")
else
RXLPSBS = 0
End if

strsql = "SELECT    Count(tblCars.CID) AS CountOfCID FROM tblCars   WHERE Species = 'HWM' and Carrier= 'RAIL'    and Rejected is null and Net > 0 and  Location = 'YARD' "
 Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
RXHWM = MyRec("CountofCID")
else
RXHWM = 0
End if

strsql = "SELECT    Count(tblCars.CID) AS CountOfCID FROM tblCars   WHERE Species = 'OCC' and (Carrier <> 'RAIL' or Carrier is null)   and Rejected is null and Net > 0 and  Location = 'YARD' "
 Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
XOCC = MyRec("CountofCID")
else
XOCC = 0
End if

strsql = "SELECT    Count(tblCars.CID) AS CountOfCID FROM tblCars   WHERE Species = 'OCC'  and Carrier= 'RAIL'   and Rejected is null and Net > 0 and  Location = 'YARD' "
 Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
RXOCC = MyRec("CountofCID")
else
RXOCC = 0
End if

strsql = "SELECT   Count(tblCars.CID) AS CountOfCID FROM tblCars   WHERE Species = 'MXP' and (Carrier <> 'RAIL' or Carrier is null)   and Rejected is null and Net > 0 and  Location = 'YARD'"
 Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
XMXP = MyRec("CountofCID")
else
XMXP = 0
End if

strsql = "SELECT    Count(tblCars.CID) AS CountOfCID FROM tblCars   WHERE Species = 'MXP' and Carrier= 'RAIL'    and Rejected is null and Net > 0 and  Location = 'YARD'"
 Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
RXMXP = MyRec("CountofCID")
else
RXMXP = 0
End if %>	
		
		
		
		<tr><td class="auto-style21">Trailers</td>
			<td class="style105"><%= XOCC %></td>
	 
			<td class="style105"><%= XMXP %>&nbsp; </td>
				<td class="style105"><%= XUSBS %>&nbsp; </td>
			 
				<td class="style105"  ><%=  XSWL%>&nbsp;</td>

		<td class="style105"><%= XLPSBS %>&nbsp;</td>
	
			<td class="style105"><%= XHWM %>&nbsp;</td>
	
				<td class="style105"><%= XSHRED %>&nbsp;</td>
	
			<td class="style105"><%= XOCC + (RXOCC * 3) + XMXP + (RXMXP*3)  %>&nbsp;</td>
	
				<td class="style105"><%= XUSBS + (RXUSBS * 3) + XSWL + (RXSWL*3) + XLPSBS + (RXLPSBS*3)  + XHWM + (RXHWM*3) + XSHRED + (RXSHRED*3) %>&nbsp;</td>
	</tr> 
 
		    <td class="auto-style21">Rail Cars</td>
 
		    <td class="style105"><%= RXOCC %>&nbsp;</td>
	 
			   <td  class="style105"><%= RXMXP  %>&nbsp;</td>
	 
				<td class="style105"><%= RXUSBS %>&nbsp; </td>
			 
				<td class="style105"  ><%=  RXSWL %>&nbsp;</td>
		
			<td class="style105"><%= RXLPSBS %>&nbsp;</td>
		<td class="style105"><%= RXHWM %>&nbsp;</td>
			<td class="style105"><%= RXSHRED %>&nbsp;</td>		 
	 
	<td class="style105" colspan="2"> &nbsp;</td>	
			 
	</tr></table>
<br>
<%  strTday = formatdatetime(now(),2)
  strYear = datepart("yyyy", strTday)
  strTD = datepart("d", strTday)
  strTM = datepart("m", strTDay)
  strTday1 = dateadd("d", 1, strTday)
    strTD1 = datepart("d", strTday1)
  strTM1 = datepart("m", strTDay1)
  strTday2 = dateadd("d", 2, strTday)
    strTD2 = datepart("d", strTday2)
  strTM2 = datepart("m", strTDay2)
  strTday3 = dateadd("d", 3, strTday)
    strTD3 = datepart("d", strTday3)
  strTM3 = datepart("m", strTDay3)
  
    strTday4 = dateadd("d", 4, strTday)
    strTD4 = datepart("d", strTday4)
  strTM4 = datepart("m", strTDay4)


  strTday5 = dateadd("d", 5, strTday)
    strTD5 = datepart("d", strTday5)
  strTM5 = datepart("m", strTDay5)


  strTday6 = dateadd("d", 6, strTday)
    strTD6 = datepart("d", strTday6)
  strTM6 = datepart("m", strTDay6)
%>
<table style="width: 100%" class="style18" cellspacing="0">
	<tr>
		<td colspan="2" class="auto-style35" rowspan="2"></td>
		<font face="arial" size="1">
		<td colspan="7" class="auto-style25" style="height: 40px"><strong>Inventory BOD</strong></td>

		<td colspan="7" class="auto-style28" style="height: 40px"><strong>Inbound</strong></td>
		<td style="height: 40px" colspan="7" class="auto-style27"><strong>Consumption</strong></td>
	</tr>
 
		<tr>

		<td class="auto-style24" style="height: 24px" colspan="2">OCC BROWN</td>

			<td class="auto-style24" style="height: 24px" colspan="5">OCC WHITE</td>


	<td class="auto-style28" colspan="2"  >OCC BROWN</td>
		<td class="auto-style28" colspan="5"  >OCC WHITE</td>

<td class="auto-style26" colspan="2" >OCC BROWN</td>
<td class="auto-style26" colspan="5" >OCC WHITE</td>



	</tr>
 
		<tr>
		<td class="auto-style32"   >Date</td>
		<td class="auto-style32"  >Week day</td>

		<td class="auto-style25" style="height: 24px">OCC</td>

		<td class="auto-style25" style="height: 24px">MXP</td>
			<td class="auto-style25" style="height: 24px">USBS</td>
		<td class="auto-style19" style="height: 24px">SWL</td>
		<td class="auto-style19" style="height: 24px">LPSBS</td>


		<td class="auto-style19" style="height: 24px">HWM</td>


		<td class="auto-style19" style="height: 24px">SHRED</td>


			<strong>


	<td class="auto-style29"  >OCC</td>
	<td class="auto-style29"  >MXP</td>
		<td class="auto-style29"  >USBS</td>
	<td class="auto-style29"  >SWL</td>

	<td class="auto-style29" style="width: 28px"  >LPSBS</td>

	<td class="auto-style29" style="width: 28px"  >HWM</td>

	<td class="auto-style29" style="width: 28px"  >SHRED</td>

<td class="auto-style27" >OCC</td>
<td class="auto-style27" >MXP</td>
<td class="auto-style27" >USBS</td>
<td class="auto-style27" >SWL</td>
		<td class="auto-style27"  >LPSBS</td>



		<td class="auto-style27"  >HWM</td>



		<td class="auto-style27"  >SHRED</td>



	</tr>

<%
''''''''''''''''''''''''''''' This starts the routine to count Inbound Loads  
      
    dim strUse
    strUse = date()
  strDay2 = dateadd("d", -3, strUse)
  strDay0 = dateadd("d", 1, struse)
  
  Dim strTomorrow
  strTomorrow = strDay0
  
   	
 		countUSBS = 0
		countLPSBS = 0
 		countSWL = 0
 		countOCC = 0
 		countMXP = 0
 		countHWM = 0
 		countSHRED = 0
		countLPSBS2 = 0
 		countMXP2 = 0
 		countUSBS2 = 0
 		countSWL2 = 0
 		countOCC2 = 0
 		countHWM2 = 0
 		countSHRED2 = 0



  	     strsql = "SELECT  left(release,1) as Species, Release, dateadd(dd, datediff(dd,0,Date_to),0) as Delivery_date "_
  			&" FROM tblInbound "_
			&" WHERE Destination_City ='MOBILE' and Release <> '0'"_	
			&" and dateadd(dd, datediff(dd,0,Date_to),0) > '" & strDay2 & "' "_
			&" and dateadd(dd, datediff(dd,0,Date_to),0) <= '" & strUse & "' "_
			&" and Ship_Status <>'Tendered' "_
			&" ORDER BY Date_to" 	

 	
   		Set MyRec = Server.CreateObject("ADODB.Recordset")
  		 MyRec.Open strSQL, Session("ConnectionString") 
		While not MyRec.eof 	
		
		
		strOB2 = MyRec.fields("Release")
		strsql2 = "Select [Date_received] from tblCars where [Release_nbr] = '" & strOB2 & "'"
 		Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    	MyRec2.Open strSQL2, Session("ConnectionString")
   	 	if not MyRec2.eof then  
  		else
  		
  		strsql3 = "Select Species, OCC_SHRD from tblOrder where Release = '" & strOB2 & "'"
  			Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    	MyRec3.Open strSQL3, Session("ConnectionString")
   	 	if not MyRec3.eof then
  			if MyRec3("Species") = "SWL"  then
				countSWL = countSWL + 1
			elseif MyRec3("Species") = "OCC"  Then
				countOCC = countOCC + 1
			elseif   MyRec3("Species") = "MXP" Then
				countMXP = countMXP + 1
			elseif   MyRec3("Species") = "HWM" Then
				countHWM = countHWM + 1

			elseif MyRec3("Species") = "USBS" Then				
				countUSBS = countUSBS + 1

			elseif MyRec3("Species") = "LPSBS" Then				
				countLPSBS = countLPSBS + 1
						
			elseif MyRec3("Species") = "SHRED" and  MyRec3("OCC_SHRD") = "Y"   Then			
					
				countShred = countShred + 1
				
				end if ' Species

			end if ' MyRec2.eof

			end if ' MyRec3.EOF
	
   	 	
  		
		MyRec2.close
 
      	 MyRec.MoveNext
    	 Wend
    	 MyRec.close 
    	 

       strsql = "SELECT  left(release,1) as Species, Release, dateadd(dd, datediff(dd,0,Date_to),0) as Delivery_date "_
  			&" FROM tblInbound "_
			&" WHERE Destination_City ='MOBILE' and Release <> '0' and  dateadd(dd, datediff(dd,0,Date_to),0) = '" & strTomorrow & "' "_
				&" and Ship_Status <> 'Tendered' "_

			&" ORDER BY Date_to, left(release,1)" 	
	

 	
   		Set MyRec = Server.CreateObject("ADODB.Recordset")
  		 MyRec.Open strSQL, Session("ConnectionString") 
		While not MyRec.eof 	
		
		
		strOB2 = MyRec.fields("Release")
		strsql2 = "Select [Date_received] from tblCars where [Release_nbr] = '" & strOB2 & "'"
 		Set MyRec2 = Server.CreateObject("ADODB.Recordset")
    	MyRec2.Open strSQL2, Session("ConnectionString")
   	 	if not MyRec2.eof then  
  		else
  		
  		strsql3 = "Select Species, OCC_SHRD from tblOrder where Release = '" & strOB2 & "'"
  			Set MyRec3 = Server.CreateObject("ADODB.Recordset")
    	MyRec3.Open strSQL3, Session("ConnectionString")
   	 	if not MyRec3.eof then
  			if MyRec3("Species") = "SWL"  then
				countSWL2 = countSWL2 + 1
			elseif MyRec3("Species") = "OCC"  Then
				countOCC2 = countOCC2 + 1
			elseif   MyRec3("Species") = "MXP" Then
				countMXP2 = countMXP2 + 1
			elseif   MyRec3("Species") = "HWM" Then
				countHWM2 = countHWM2 + 1

			elseif MyRec3("Species") = "USBS" Then				
				countUSBS2 = countUSBS2 + 1

			elseif MyRec3("Species") = "LPSBS" Then				
				countLPSBS2 = countLPSBS2 + 1
						
			elseif MyRec3("Species") = "SHRED" and  MyRec3("OCC_SHRD") = "Y"   Then			
					
				countShred2 = countShred2 + 1
				
				end if ' Species

			end if ' MyRec2.eof

			end if ' MyRec3.EOF
	
   	 	
  		
		MyRec2.close
 
      	 MyRec.MoveNext
    	 Wend
    	 MyRec.close
    	 
'Rail Cars

 
 RstrOCC0 = 0
 RstrMXP0 = 0
 RstrUSBS0 = 0
  RstrSWL0 = 0 
  RstrLPSBS0 = 0
  RstrHWM0 = 0
 RstrOCC1 = 0
 RstrMXP1 = 0
   RstrUSBS1 = 0 
   RstrSWL1 = 0 
     RstrLPSBS1 = 0
  RstrHWM1 = 0

 RstrOCC2 = 0
 RstrMXP2 = 0
   RstrUSBS2 = 0
     RstrSWL2 = 0 
       RstrLPSBS2 = 0
  RstrHWM2 = 0

 RstrOCC3 = 0
 RstrMXP3 = 0
    RstrUSBS3 = 0
      RstrSWL3 = 0 
        RstrLPSBS3 = 0
  RstrHWM3 = 0

 RstrOCC4 = 0
 RstrMXP4 = 0
    RstrUSBS4 = 0
      RstrSWL4 = 0 
        RstrLPSBS4 = 0
  RstrHWM4 = 0

 RstrOCC5 = 0
 RstrMXP5 = 0
    RstrUSBS5 = 0
      RstrSWL5 = 0 
        RstrLPSBS5 = 0
  RstrHWM5 = 0

 RstrOCC6 = 0
 RstrMXP6 = 0
    RstrUSBS6 = 0
      RstrSWL6 = 0 
        RstrLPSBS6 = 0
  RstrHWM6 = 0

 RstrOCC7 = 0
 RstrMXP7 = 0
   RstrUSBS7 = 0
     RstrSWL7 = 0 
       RstrLPSBS7 = 0
  RstrHWM7 = 0

 
 strTM0 = datepart("m", now())

strETADate = date()




   
        strsql = "SELECT  count(VID) as Countoford_id,  Species,  dateadd(dd, datediff(dd,0,ETA),0) as Delivery_date "_
  			&" FROM tblVirginFiber "_
			&" WHERE Status = 'Inbound' and Vendor = 'Corporate' "_								
			&"GROUP BY dateadd(dd, datediff(dd,0,ETA),0), Species "_			
			&" HAVING '" & strdate & "' <= dateadd(dd, datediff(dd,0,ETA),0) "_
			&" ORDER BY dateadd(dd, datediff(dd,0,ETA),0), Species"



   		Set MyRec = Server.CreateObject("ADODB.Recordset")
   		 MyRec.Open strSQL, Session("ConnectionString") 


While not MyRec.eof


if strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM0  and  (MyRec.fields("Species") = "OCC" ) then 
RstrOCC0 = RstrOCC0 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM0  and  (MyRec.fields("Species") = "MXP" ) then 
RstrMXP0 = RstrMXP0 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM0  and  (MyRec.fields("Species") = "USBS" ) then 
RstrUSBS0 = RstrUSBS0 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM0  and  (MyRec.fields("Species") = "SWL" ) then 
RstrSWL0 = RstrSWL0 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM0  and  (MyRec.fields("Species") = "LPSBS" ) then 
RstrLPSBS0 = RstrLPSBS0 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM0  and  (MyRec.fields("Species") = "HWM" ) then 
RstrHWM0 = RstrHWM0 + MyRec.fields("countoford_id")


elseif strTD1 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM1  and  (MyRec.fields("Species") = "OCC" ) then 
RstrOCC1 = RstrOCC1 + MyRec.fields("countoford_id")
elseif strTD1 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM1  and  (MyRec.fields("Species") = "MXP" ) then 
RstrMXP1 = RstrMXP1 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM1  and  (MyRec.fields("Species") = "USBS" ) then 
RstrUSBS1 = RstrUSBS1 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM1  and  (MyRec.fields("Species") = "SWL" ) then 
RstrSWL1 = RstrSWL1 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM1  and  (MyRec.fields("Species") = "LPSBS" ) then 
RstrLPSBS1 = RstrLPSBS1 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM1  and  (MyRec.fields("Species") = "HWM" ) then 
RstrHWM1 = RstrHWM1 + MyRec.fields("countoford_id")

  
elseif strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "OCC" ) then 
RstrOCC2 = RstrOCC2 + MyRec.fields("countoford_id")
elseif strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "MXP" ) then 
RstrMXP2 = RstrMXP2 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "USBS" ) then 
RstrUSBS2 = RstrUSBS2 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "SWL" ) then 
RstrSWL2 = RstrSWL2 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "LPSBS" ) then 
RstrLPSBS2 = RstrLPSBS2 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "HWM" ) then 
RstrHWM2 = RstrHWM2 + MyRec.fields("countoford_id")
 
 
elseif strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3  and  (MyRec.fields("Species") = "OCC" ) then 
RstrOCC3 = RstrOCC3 + MyRec.fields("countoford_id")
elseif strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3  and  (MyRec.fields("Species") = "MXP" ) then 
RstrMXP3 = RstrMXP3 + MyRec.fields("countoford_id")
 elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3  and  (MyRec.fields("Species") = "USBS" ) then 
RstrUSBS3= RstrUSBS3 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3  and  (MyRec.fields("Species") = "SWL" ) then 
RstrSWL3 = RstrSWL3 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3  and  (MyRec.fields("Species") = "LPSBS" ) then 
RstrLPSBS3 = RstrLPSBS3 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3  and  (MyRec.fields("Species") = "HWM" ) then 
RstrHWM3 = RstrHWM3 + MyRec.fields("countoford_id")
 
 
 
elseif strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4  and  (MyRec.fields("Species") = "OCC" ) then 
RstrOCC4 = RstrOCC4 + MyRec.fields("countoford_id")
elseif strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4  and  (MyRec.fields("Species") = "MXP" ) then 
RstrMXP4 = RstrMXP4 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4  and  (MyRec.fields("Species") = "USBS" ) then 
RstrUSBS4 = RstrUSBS4 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4  and  (MyRec.fields("Species") = "SWL" ) then 
RstrSWL4 = RstrSWL4 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4  and  (MyRec.fields("Species") = "LPSBS" ) then 
RstrLPSBS4 = RstrLPSBS4 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4  and  (MyRec.fields("Species") = "HWM" ) then 
RstrHWM4 = RstrHWM4 + MyRec.fields("countoford_id")

 
 
elseif strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5  and  (MyRec.fields("Species") = "OCC"  ) then 
RstrOCC5 = RstrOCC5 + MyRec.fields("countoford_id")
elseif strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5  and  (MyRec.fields("Species") = "MXP" ) then 
RstrMXP5 = RstrMXP5 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5  and  (MyRec.fields("Species") = "USBS" ) then 
RstrUSBS5 = RstrUSBS5 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5  and  (MyRec.fields("Species") = "SWL" ) then 
RstrSWL5 = RstrSWL5 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5  and  (MyRec.fields("Species") = "LPSBS" ) then 
RstrLPSBS5 = RstrLPSBS5 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5  and  (MyRec.fields("Species") = "HWM" ) then 
RstrHWM5 = RstrHWM5 + MyRec.fields("countoford_id")


  
elseif strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6  and  (MyRec.fields("Species") = "OCC")   then 
RstrOCC6 = RstrOCC6 + MyRec.fields("countoford_id")
elseif strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6  and  (MyRec.fields("Species") = "MXP" ) then 
RstrMXP6 = RstrMXP6 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6  and  (MyRec.fields("Species") = "USBS" ) then 
RstrUSBS6= RstrUSBS6 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6  and  (MyRec.fields("Species") = "SWL" ) then 
RstrSWL6 = RstrSWL6 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6  and  (MyRec.fields("Species") = "LPSBS" ) then 
RstrLPSBS6 = RstrLPSBS6 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6  and  (MyRec.fields("Species") = "HWM" ) then 
RstrHWM6 = RstrHWM6 + MyRec.fields("countoford_id")


 
elseif strTD7 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM7  and  (MyRec.fields("Species") = "OCC")  then 
RstrOCC7 = RstrOCC7 + MyRec.fields("countoford_id")
elseif strTD7 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM7  and  (MyRec.fields("Species") = "MXP" ) then 
RstrMXP7 = RstrMXP7 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM7  and  (MyRec.fields("Species") = "USBS" ) then 
RstrUSBS7 = RstrUSBS7 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM7  and  (MyRec.fields("Species") = "SWL" ) then 
RstrSWL7 = RstrSWL7+ MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM7  and  (MyRec.fields("Species") = "LPSBS" ) then 
RstrLPSBS7 = RstrLPSBS7 + MyRec.fields("countoford_id")
elseif strTD = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM7  and  (MyRec.fields("Species") = "HWM" ) then 
RstrHWM7 = RstrHWM7 + MyRec.fields("countoford_id")

 

end if
   MyRec.MoveNext
     Wend
     MyRec.close %>
		<tr>
			<td class="auto-style25" style="height: 24px"><font face="arial" size="2"><%= strNow %></td>
		<% strDate1 = datepart("w", strNow)
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

	
		<td class="auto-style25" style="height: 24px"><font face="arial" size="2"><%= strDayofWeek %></td>

	<td class="style115"
<% strGroup1 = xOCC + 3*(RXOCC) 
strGroupOCC = strGroup1 %>
		<% if strGroup1 >10 then %>
		
		bgcolor="#00B0F0"  >
				
				<% elseif strGroup1 > 4 and strGroup1 < 11 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 > 0 and strGroup < 5 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>
<td class="style115"
<% strGroup1 = xMXP + 3*(RXMXP)
strGroupMXP = strGroup1  %>
		<% if strGroup1 >4 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 1 and strGroup < 5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

<td class="style115"
<% strGroup1 = xUSBS + 3*(RXUSBS)
strGroupUSBS = strGroup1  %>
		<% if strGroup1 >2 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

<td class="style115"
<% strGroup1 = xSWL + 3*(RXSWL)
strGroupSWL = strGroup1  %>
		<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 2 and strGroup1 < 7 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

<td class="style115"
<% strGroup1 = xLPSBS + 3*(RXLPSBS)
strGroupLPSBS = strGroup1  %>
		<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 2 and strGroup1 < 7 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

<td class="style115"
<% strGroup1 = xHWM + 3*(RXHWM)
strGroupHWM = strGroup1  %>
		<% if strGroup1 >2 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

<td class="style115"
<% strGroup1 = xSHRED  
strGroupSHRED = strGroup1  %>
		<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 2 and strGroup1 < 7 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

 

	<td class="auto-style29"  ><%= countOCC %><% if RstrOCC0 > 0 then %> / <%= RstrOCC0 %> <% end if %></td>
	<td class="auto-style29"  ><%= countMXP%><% if RstrMXP0 > 0 then %> / <%= RstrMXP0 %> <% end if %></td>
		<td class="auto-style29"  ><%= countUSBS %><% if RstrUSBS0 > 0 then %> / <%= RstrUSBS0 %> <% end if %></td>
	<td class="auto-style29"  ><%= countSWL %><% if RstrSWL0 > 0 then %> / <%= RstrSWL0 %> <% end if %></td>

	<td class="auto-style29" style="width: 28px"  ><%= countLPSBS %><% if RstrLPSBS0 > 0 then %> / <%= RstrLPSBS0 %> <% end if %></td>


	<td class="auto-style29" style="width: 28px"  ><%= countHWM %><% if RstrHWM0 > 0 then %> / <%= RstrHWM0 %> <% end if %></td>


	<td class="auto-style29" style="width: 28px"  ><%= countSHRED %> </td>
<%  OCC_One = 0
MXP_One = 0
 SWL_One = 0
USBS_One = 0
 LPSBS_One = 0
HWM_One = 0
 Shred_One = 0


strsql = "Select tblTempYardTotals.* from tblTempYardTotals where INV_Date = '" & strnow & "'"
		
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
 
OCC_One = MyRec.fields("Total_OCC")
MXP_One = MyRec.fields("TOtal_MXP")
SWL_One = MyRec("SWL")
USBS_One = MyRec("USBS")
 LPSBS_One = MyRec("LPSBS")
HWM_One = MyRec("HWM")
SHRED_one = MyRec("Shred_OCC")
 end if
MyRec.close
%>

<td class="auto-style36" ><%= OCC_One %></td>
<td class="auto-style36" ><%= MXP_One %></td>
<td class="auto-style36" ><%= USBS_One %></td>
<td class="auto-style36" ><%= SWL_One %></td>

		<td class="auto-style36"  ><%= LPSBS_One %></td>
		<td class="auto-style36"  ><%= HWM_One %></td>
		<td class="auto-style36"  ><%= Shred_One %></td>



	</tr>

		<tr>
			<td class="auto-style25" style="height: 24px"><font face="arial" size="2"><%= dateadd("d", 1, strnow)%></td>
		<% strDate1 = datepart("w", dateadd("d", 1, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

	
		<td class="auto-style25" style="height: 24px"><font face="arial" size="2"><%= strDayofWeek %></td>

<td  class="style115"
<% strGroup1 = strGroupOCC + countOCC + (3*RstrOCC0) - OCC_One
strGroup2OCC = strGroup1 %>
			<% if strGroup1 > 10 then %>
		
		bgcolor="#00B0F0" >
			
				<% elseif strGroup1 > 4 and strGroup1 < 11 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 > 0 and strGroup < 5 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %>	<span class="style16">
<%= strGroup1 %></span> </td>


	<td class="style115"
<% strGroup1 = strGroupMXP + countMXP + (3*RstrMXP0) - MXP_One
strGroup2MXP = strGroup1  %>
		<% if strGroup1 >4 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 1 and strGroup < 5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

<td class="style115"
<% strGroup1 = strGroupUSBS + countUSBS + (3*RstrUSBS0) -  USBS_One
strGroup2USBS = strGroup1  %>
		<% if strGroup1 >2 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

<td class="style115"
<% strGroup1 = strGroupSWL+ countSWL + (3*RstrSWL0) -  SWL_One
strGroup2SWL = strGroup1  %>
 
		<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 2 and strGroup1 < 7 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

<td class="style115"
<% strGroup1 = strGroupLPSBS + countLPSBS + (3*RstrLPSBS0) -  LPSBS_One
strGroup2LPSBS = strGroup1  %>
		<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 2 and strGroup1 < 7 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>


<td class="style115"
<% strGroup1 = strGroupHWM + countHWM + (3*RstrHWM0) -  HWM_One
strGroup2HWM = strGroup1  %>
		<% if strGroup1 >2 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>


<td class="style115"
<% strGroup1 = strGroupShred + countShred - Shred_One
strGroup2SHRED = strGroup1  %>
		<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 2 and strGroup1 < 7 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>


		<td class="auto-style29"  ><%= countOCC2 %><% if RstrOCC1 > 0 then %> / <%= RstrOCC1 %> <% end if %></td>
	<td class="auto-style29"  ><%= countMXP2%><% if RstrMXP1 > 0 then %> / <%= RstrMXP1 %> <% end if %></td>
		<td class="auto-style29"  ><%= countUSBS2 %><% if RstrUSBS1 > 0 then %> / <%= RstrUSBS1 %> <% end if %></td>
	<td class="auto-style29"  ><%= countSWL2 %><% if RstrSWL1 > 0 then %> / <%= RstrSWL1 %> <% end if %></td>

	<td class="auto-style29" style="width: 28px"  ><%= countLPSBS2 %><% if RstrLPSBS1 > 0 then %> / <%= RstrLPSBS1 %> <% end if %></td>


	<td class="auto-style29" style="width: 28px"  ><%= countHWM2 %><% if RstrHWM1 > 0 then %> / <%= RstrHWM1 %> <% end if %></td>


	<td class="auto-style29" style="width: 28px"  ><%= countSHRED2 %> </td>
<%   
  strSelectdate = dateadd("d", 1, strnow)
				strsql = "Select  tblTempYardTotals.* from tblTempYardTotals where INV_Date = '" & strSelectdate & "'"

	
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
 
OCC_Two = MyRec.fields("Total_OCC")
MXP_Two = MyRec.fields("TOtal_MXP")
SWL_Two = MyRec("SWL")
USBS_Two = MyRec("USBS")
 LPSBS_Two = MyRec("LPSBS")
HWM_Two = MyRec("HWM")
SHRED_Two = MyRec("Shred_OCC")
else
 
OCC_Two = 0
MXP_Two = 0
 SWL_Two = 0
USBS_Two = 0
 LPSBS_Two = 0
HWM_Two = 0
 Shred_Two = 0
 end if
MyRec.close
%>
<td class="auto-style36" ><%= OCC_Two %></td>
<td class="auto-style36" ><%= MXP_Two %></td>
<td class="auto-style36" ><%= USBS_Two %></td>
<td class="auto-style36" ><%= SWL_Two %></td>

		<td class="auto-style36"  ><%= LPSBS_Two %></td>
		<td class="auto-style36"  ><%= HWM_Two %></td>
		<td class="auto-style36"  ><%= Shred_Two %></td>



	</tr>

		<tr>
					<td class="auto-style25" style="height: 24px"><font face="arial" size="2"><%= dateadd("d", 2, strnow)%></td>
		<% strDate1 = datepart("w", dateadd("d", 2, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

	
		<td class="auto-style25" style="height: 24px"><font face="arial" size="2"><%= strDayofWeek %></td>

	
<td  class="style115"
<% strGroup1 = strGroup2OCC + countOCC2 + (3*RstrOCC1) - OCC_two
strGroup3OCC = strGroup1 %>
				<% if strGroup1 >15 then %>
		
		bgcolor="#00B0F0" >
				
				<% elseif strGroup1 > 10 and strGroup1 < 16 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 > 0 and strGroup < 11 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>


	<td class="style115"
<% strGroup1 = strGroup2MXP + countMXP2 + (3*RstrMXP1) - MXP_Two
strGroup3MXP = strGroup1  %>
		<% if strGroup1 >4 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 1 and strGroup < 5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

<td class="style115"
<% strGroup1 = strGroup2USBS + countUSBS2 + (3*RstrUSBS1) -  USBS_Two
strGroup3USBS = strGroup1  %>
		<% if strGroup1 >2 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

<td class="style115"
<% strGroup1 = strGroup2SWL+ countSWL2 + (3*RstrSWL1) -  SWL_Two
strGroup3SWL = strGroup1  %>
 
		<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 2 and strGroup1 < 7 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>
<td class="style115"
<% strGroup1 = strGroup2LPSBS + countLPSBS2 + (3*RstrLPSBS1) -  LPSBS_Two
strGroup3LPSBS = strGroup1  %>
		<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 2 and strGroup1 < 7 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>


<td class="style115"
<% strGroup1 = strGroup2HWM + countHWM2 + (3*RstrHWM1) -  HWM_Two
strGroup3HWM = strGroup1  %>
		<% if strGroup1 >2 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>


<td class="style115"
<% strGroup1 = strGroup2Shred + countShred2 - Shred_Two
strGroup3SHRED = strGroup1  %>
		<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 2 and strGroup1 < 7 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>
		<%
strOCC3 = 0
strUSBS3 = 0
strOCC4 = 0
strUSBS4 = 0
strOCC5 = 0
strUSBS5 = 0
strOCC6 = 0
strUSBS6 = 0
strOCC7 = 0
strUSBS7 = 0

strSWL3 = 0
strSWL4 = 0
strSWL5 = 0
strSWL6 = 0
strSWL7 = 0

strHWM3 = 0
strHWM4 = 0
strHWM5 = 0
strHWM6 = 0
strHWM7 = 0

strSHRED3 = 0
strSHRED4 = 0
strSHRED5 = 0
strSHRED6 = 0
strSHRED7 = 0

strMXP3 = 0
strMXP4 = 0
strMXP5 = 0
strMXP6 = 0
strMXP7 = 0
strLPSBS3 = 0
strLPSBS4 = 0
strLPSBS5 = 0
strLPSBS6 = 0
strLPSBS7 = 0

	
   
        strsql = "SELECT  count(CID) as Countoford_id, left(release,1) as Species, dateadd(dd, datediff(dd,0,Date_to),0) as Delivery_date "_
  			&" FROM tblInbound "_
			&" WHERE Destination_City ='MOBILE' and Release <> '0'  "_								
			&" GROUP BY dateadd(dd, datediff(dd,0,Date_to),0), left(Release,1)"_			
			&" HAVING '" & strdate & "' <= dateadd(dd, datediff(dd,0,Date_to),0) "_
			&" ORDER BY dateadd(dd, datediff(dd,0,Date_to),0), left(Release,1)"



   		Set MyRec = Server.CreateObject("ADODB.Recordset")
   		 MyRec.Open strSQL, Session("ConnectionString") 


While not MyRec.eof

If strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2 and (MyRec.fields("Species") = "L" or MyRec.fields("Species") = "l") then   
strLPSBS3 = MyRec.fields("countoford_id") + strLPSBS3
elseif strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "C"  or MyRec.fields("Species") = "c"   ) then 
strOCC3 = strOCC3 + MyRec.fields("countoford_id")
elseif strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "M"  or MyRec.fields("Species") = "m" ) then 
strMXP3 = strMXP3 + MyRec.fields("countoford_id")
elseif strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "W"  or MyRec.fields("Species") = "w" ) then 
strHWM3 = strHWM3 + MyRec.fields("countoford_id")
elseif strTD2 = datepart("d", MyRec("Delivery_date")) and datepart("m", MyRec("Delivery_Date")) = strTM2  and  (MyRec("Species") = "S"  or MyRec("Species") = "s" )  then 
strSWL3 = strSWL3 + MyRec.fields("countoford_id")
elseif strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "U"  or MyRec.fields("Species") = "u" ) then 
strUSBS3 = strUSBS3 + MyRec.fields("countoford_id")

elseIf strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3 and  (MyRec.fields("Species") = "L" or MyRec.fields("Species") = "l") then 
strLPSBS4 = MyRec.fields("countoford_id") + strLPSBS4
elseif strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3  and (MyRec.fields("Species") = "C"  or MyRec.fields("Species") = "c"  ) then 
strOCC4 = strOCC4 + MyRec.fields("countoford_id")
elseif strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3  and (MyRec.fields("Species") = "M"  or MyRec.fields("Species") = "m" ) then 
strMXP4 = strMXP4 + MyRec.fields("countoford_id")
elseif strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3  and  (MyRec.fields("Species") = "W"  or MyRec.fields("Species") = "w" ) then 
strHWM4 = strHWM4 + MyRec.fields("countoford_id")
elseif strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec("Species") = "S"  or MyRec("Species") = "s" )  then 
strSWL4 = strSWL4 + MyRec.fields("countoford_id")
elseif strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "U"  or MyRec.fields("Species") = "u" ) then 
strUSBS4 = strUSBS4 + MyRec.fields("countoford_id")

elseIf strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4 and  (MyRec.fields("Species") = "L" or MyRec.fields("Species") = "l") then       
strLPSBS5 = MyRec.fields("countoford_id") + strLPSBS5
elseif strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4  and  (MyRec.fields("Species") = "C"  or MyRec.fields("Species") = "c" ) then 
 strOCC5 = strOCC5 +  MyRec.fields("countoford_id")
elseif strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM4  and  (MyRec.fields("Species") = "M"  or MyRec.fields("Species") = "m" ) then 
 strMXP5 = strMXP5 + MyRec.fields("countoford_id")
elseif strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5  and  (MyRec.fields("Species") = "W"  or MyRec.fields("Species") = "w" ) then 
strHWM5 = strHWM5 + MyRec.fields("countoford_id")
elseif strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec("Species") = "S"  or MyRec("Species") = "s" )  then 
strSWL5 = strSWL5 + MyRec.fields("countoford_id")
elseif strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "U"  or MyRec.fields("Species") = "u" ) then 
strUSBS5 = strUSBS5 + MyRec.fields("countoford_id")

elseIf strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5 and  (MyRec.fields("Species") = "L" or MyRec.fields("Species") = "l") then        
strLPSBS6 = MyRec.fields("countoford_id") + strLPSBS6
elseif strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5  and  (MyRec.fields("Species") = "C" or MyRec.fields("Species") = "c" ) then 
strOCC6 = strOCC6 +  MyRec.fields("countoford_id")
elseif strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5  and  (MyRec.fields("Species") = "M" or MyRec.fields("Species") = "m" ) then 
strMXP6 = strMXP6 + MyRec.fields("countoford_id")
elseif strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6  and  (MyRec.fields("Species") = "W"  or MyRec.fields("Species") = "w" ) then 
strHWM6 = strHWM6 + MyRec.fields("countoford_id")
elseif strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec("Species") = "S"  or MyRec("Species") = "s" )  then 
strSWL6 = strSWL6 + MyRec.fields("countoford_id")
elseif strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "U"  or MyRec.fields("Species") = "u" ) then 
strUSBS6 = strUSBS6 + MyRec.fields("countoford_id")

elseIf strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6 and  (MyRec.fields("Species") = "L" or MyRec.fields("Species") = "l") then          
strLPSBS7 = MyRec.fields("countoford_id") + strLPSBS7
elseif strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6  and  (MyRec.fields("Species") = "C" or MyRec.fields("Species") = "c" ) then  
strOCC7 = strOCC7 +  MyRec.fields("countoford_id")
elseif strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6  and  (MyRec.fields("Species") = "M" or MyRec.fields("Species") = "m" ) then  
strMXP7 = strMXP7 + MyRec.fields("countoford_id")
elseif strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM7  and  (MyRec.fields("Species") = "W"  or MyRec.fields("Species") = "w" ) then 
strHWM7 = strHWM7 + MyRec.fields("countoford_id")
elseif strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec("Species") = "S"  or MyRec("Species") = "s" )  then  
strSWL7 = strSWL7 + MyRec.fields("countoford_id")
elseif strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2  and  (MyRec.fields("Species") = "U"  or MyRec.fields("Species") = "u" ) then 
strUSBS7 = strUSBS7 + MyRec.fields("countoford_id")

end if
   MyRec.MoveNext
     Wend
     MyRec.close 
     
      strsql = "SELECT  count(CID) as Countoford_id, left(release,1) as Species,  dateadd(dd, datediff(dd,0,Date_to),0) as Delivery_date "_
  			&" FROM tblInbound "_
			&" WHERE Destination_City ='MOBILE' and Release <> '0' and OCC_SHRED = 'Y' and ( left(release,1) = 'D' or left(release,1) = 'd')  "_								
			&" GROUP BY dateadd(dd, datediff(dd,0,Date_to),0), left(Release,1)"_			
			&" HAVING '" & strdate & "' <= dateadd(dd, datediff(dd,0,Date_to),0) "_
			&" ORDER BY dateadd(dd, datediff(dd,0,Date_to),0)"

   		Set MyRec = Server.CreateObject("ADODB.Recordset")
   		 MyRec.Open strSQL, Session("ConnectionString") 


While not MyRec.eof
 
 if strTD2 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM2   then 
strSHRED3 = strSHRED3 + MyRec.fields("countoford_id")
 
elseif strTD3 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM3    then 
strSHRED4 = strSHRED4 + MyRec.fields("countoford_id")
  elseif strTD4 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM5    then 
strSHRED5 = strSHRED5 + MyRec.fields("countoford_id")
  elseif strTD5 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM6   then 
strSHRED6 = strSHRED6 + MyRec.fields("countoford_id")
  
 elseif strTD6 = datepart("d", MyRec.fields("Delivery_date")) and datepart("m", MyRec.fields("Delivery_Date")) = strTM7 then 
strSHRED7 = strSHRED7 + MyRec.fields("countoford_id")
 end if
   MyRec.MoveNext
     Wend
     MyRec.close%>

	<td class="auto-style29"  ><%= strOCC3 %><% if RstrOCC2 > 0 then %> / <%= RstrOCC2 %> <% end if %></td>
	<td class="auto-style29"  ><%= strMXP3 %><% if RstrMXP2 > 0 then %> / <%= RstrMXP2 %> <% end if %></td>
		<td class="auto-style29"  ><%= strUSBS3 %><% if Rstrusbs2 > 0 then %> / <%= RstrUSBS2 %> <% end if %></td>
	<td class="auto-style29"  ><%= strSWL3 %><% if RstrSWL2 > 0 then %> / <%= RstrSWL2 %> <% end if %></td>

	<td class="auto-style29" style="width: 28px"  ><%= strLPSBS3 %><% if RstrLPSBS2 > 0 then %> / <%= RstrLPSBS2 %> <% end if %></td>

	<td class="auto-style29" style="width: 28px"  ><%= strHWM3 %><% if RstrHWM2 > 0 then %> / <%= RstrHWM2 %> <% end if %></td>

	<td class="auto-style29" style="width: 28px"  ><%= strSHRED3 %></td>
<%  
  strSelectdate = dateadd("d", 2, strnow)
				strsql = "Select  tblTempYardTotals.* from tblTempYardTotals where INV_Date = '" & strSelectdate & "'"

	
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
 
OCC_Three = MyRec.fields("Total_OCC")
MXP_Three = MyRec.fields("TOtal_MXP")
SWL_Three = MyRec("SWL")
USBS_Three = MyRec("USBS")
 LPSBS_Three = MyRec("LPSBS")
HWM_Three = MyRec("HWM")
SHRED_Three = MyRec("Shred_OCC")
else
 
OCC_Three = 0
MXP_Three = 0
 SWL_Three = 0
USBS_Three = 0
 LPSBS_Three = 0
HWM_Three = 0
 Shred_Three = 0
 end if
MyRec.close
%>
<td class="auto-style36" ><%= OCC_Three %></td>
<td class="auto-style36" ><%= MXP_Three %></td>
<td class="auto-style36" ><%= USBS_Three %></td>
<td class="auto-style36" ><%= SWL_Three %></td>

		<td class="auto-style36"  ><%= LPSBS_Three %></td>
		<td class="auto-style36"  ><%= HWM_Three %></td>
		<td class="auto-style36"  ><%= Shred_Three %></td>


	</tr>

		<tr>
							<td class="auto-style25" style="height: 24px"><font face="arial" size="2"><%= dateadd("d", 3, strnow)%></td>
		<% strDate1 = datepart("w", dateadd("d",3, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

	
		<td class="auto-style25" style="height: 24px"><font face="arial" size="2"><%= strDayofWeek %></td>

	<td  class="style115"
<% strGroup1 = strGroup3OCC + strOCC3 + (3 * RstrOCC2) - OCC_three
strGroup4OCC = strGroup1 %>
		<% if strGroup1 >15 then %>
		
		bgcolor="#00B0F0" >
			
				<% elseif strGroup1 > 10 and strGroup1 < 16 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 > 0 and strGroup < 11 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %>	<span class="style16">
<%= strGroup1 %></span> </td>


	<td class="style115"
<% strGroup1 = strGroup3MXP + strMXP3 + (3*RstrMXP2) - MXP_Three
strGroup4MXP = strGroup1  %>
		<% if strGroup1 >4 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 1 and strGroup < 5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

<td class="style115"
<% strGroup1 = strGroup3USBS + strUSBS3 + (3*RstrUSBS2) -  USBS_Three
strGroup4USBS = strGroup1  %>
		<% if strGroup1 >2 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

	<td class="style115"
<% strGroup1 = strGroup3SWL + strSWL3 + (3*RstrSWL2) -  SWL_Three
strGroup4SWL = strGroup1  %>
 
		<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 2 and strGroup1 < 7 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

<td class="style115"
<% strGroup1 = strGroup3LPSBS + strLPSBS3 + (3*RstrLPSBS2) -  LPSBS_Three
strGroup4LPSBS = strGroup1  %>
		<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 2 and strGroup1 < 7 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>


<td class="style115"
<% strGroup1 = strGroup3HWM + strHWM3 + (3*RstrHWM2) -  HWM_Three
strGroup4HWM = strGroup1  %>
		<% if strGroup1 >2 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>


<td class="style115"
<% strGroup1 = strGroup3Shred + strShred3 - Shred_Three
strGroup4SHRED = strGroup1  %>
		<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 2 and strGroup1 < 7 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>


	<td class="auto-style29"  ><%= strOCC4 %><% if RstrOCC3 > 0 then %> / <%= RstrOCC3 %> <% end if %></td>
	<td class="auto-style29"  ><%= strMXP4 %><% if RstrMXP3 > 0 then %> / <%= RstrMXP3 %> <% end if %></td>
		<td class="auto-style29"  ><%= strUSBS4 %><% if RstrUSBS3 > 0 then %> / <%= RstrUSBS3 %> <% end if %></td>
	<td class="auto-style29"  ><%= strSWL4 %><% if RstrSWL3 > 0 then %> / <%= RstrSWL3 %> <% end if %></td>

	<td class="auto-style29" style="width: 28px"  ><%= strLPSBS4 %><% if RstrLPSBS3 > 0 then %> / <%= RstrLPSBS3 %> <% end if %></td>

	<td class="auto-style29" style="width: 28px"  ><%= strHWM4 %><% if RstrHWM3 > 0 then %> / <%= RstrHWM3 %> <% end if %></td>

	<td class="auto-style29" style="width: 28px"  ><%= strSHRED4 %></td>
<%  
  strSelectdate = dateadd("d", 3, strnow)
				strsql = "Select  tblTempYardTotals.* from tblTempYardTotals where INV_Date = '" & strSelectdate & "'"

	
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
 
OCC_Four = MyRec.fields("Total_OCC")
MXP_Four = MyRec.fields("TOtal_MXP")
SWL_Four = MyRec("SWL")
USBS_Four = MyRec("USBS")
 LPSBS_Four = MyRec("LPSBS")
HWM_Four = MyRec("HWM")
SHRED_Four = MyRec("Shred_OCC")

else
 
OCC_Four = 0
MXP_Four = 0
 SWL_Four = 0
USBS_Four = 0
 LPSBS_Four = 0
HWM_Four = 0
 Shred_Four = 0
 end if
MyRec.close
%>


<td class="auto-style36" ><%= OCC_Four %></td>
<td class="auto-style36" ><%= MXP_Four %></td>
<td class="auto-style36" ><%= USBS_Four %></td>
<td class="auto-style36" ><%= SWL_Four %></td>

		<td class="auto-style36"  ><%= LPSBS_Four %></td>
		<td class="auto-style36"  ><%= HWM_Four %></td>
		<td class="auto-style36"  ><%= Shred_Four %></td>


	</tr>

		<tr>
							<td class="auto-style25" style="height: 24px"><font face="arial" size="2"><%= dateadd("d", 4, strnow)%></td>
		<% strDate1 = datepart("w", dateadd("d", 4, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

	
		<td class="auto-style25" style="height: 24px"><font face="arial" size="2"><%= strDayofWeek %></td>
	<td  class="style115"
<% strGroup1 = strGroup4OCC + strOCC4 + (3 * RstrOCC3) - OCC_four
strGroup5OCC = strGroup1 %>
		<% if strGroup1 >15 then %>
		
		bgcolor="#00B0F0" >
			
				<% elseif strGroup1 > 10 and strGroup1 < 16 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 > 0 and strGroup < 11 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %>	<span class="style16">
<%= strGroup1 %></span> </td>


	<td class="style115"
<% strGroup1 = strGroup4MXP + strMXP4 + (3*RstrMXP3) - MXP_Four
strGroup5MXP = strGroup1  %>
		<% if strGroup1 >4 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 1 and strGroup < 5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>
	
<td class="style115"
<% strGroup1 = strGroup4USBS + strUSBS4 + (3*RstrUSBS3) -  USBS_Four
strGroup5USBS = strGroup1  %>
		<% if strGroup1 >2 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

	<td class="style115"
<% strGroup1 = strGroup4SWL + strSWL4 + (3*RstrSWL3) -  SWL_Four
strGroup5SWL = strGroup1  %>
 
		<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 2 and strGroup1 < 7 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>
<td class="style115"
<% strGroup1 = strGroup4LPSBS + strLPSBS4 + (3*RstrLPSBS3) -  LPSBS_four
strGroup5LPSBS = strGroup1  %>
		<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 2 and strGroup1 < 7 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>


<td class="style115"
<% strGroup1 = strGroup4HWM + strHWM4 + (3*RstrHWM3) -  HWM_Four
strGroup5HWM = strGroup1  %>
		<% if strGroup1 >2 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>




<td class="style115"
<% strGroup1 = strGroup4Shred + strShred4 - Shred_Four
strGroup5SHRED = strGroup1  %>
		<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 2 and strGroup1 < 7 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

	

	<td class="auto-style29"  ><%= strOCC5 %><% if RstrOCC4 > 0 then %> / <%= RstrOCC4 %> <% end if %></td>
	<td class="auto-style29"  ><%= strMXP5 %><% if RstrMXP4 > 0 then %> / <%= RstrMXP4 %> <% end if %></td>
		<td class="auto-style29"  ><%= strUSBS5 %><% if RstrUSBS4 > 0 then %> / <%= RstrUSBS4 %> <% end if %></td>
	<td class="auto-style29"  ><%= strSWL5 %><% if RstrSWL4 > 0 then %> / <%= RstrSWL4 %> <% end if %></td>

	<td class="auto-style29" style="width: 28px"  ><%= strLPSBS5 %><% if RstrLPSBS4 > 0 then %> / <%= RstrLPSBS4 %> <% end if %></td>


	<td class="auto-style29" style="width: 28px"  ><%= strHWM5 %><% if RstrHWM4 > 0 then %> / <%= RstrHWM4 %> <% end if %></td>


	<td class="auto-style29" style="width: 28px"  ><%= strSHRED5 %></td>
	<%  
  strSelectdate = dateadd("d", 4, strnow)
				strsql = "Select  tblTempYardTotals.* from tblTempYardTotals where INV_Date = '" & strSelectdate & "'"

	
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
 
OCC_Five = MyRec.fields("Total_OCC")
MXP_Five = MyRec.fields("TOtal_MXP")
SWL_Five = MyRec("SWL")
USBS_Five = MyRec("USBS")
 LPSBS_Five = MyRec("LPSBS")
HWM_Five = MyRec("HWM")
SHRED_Five = MyRec("Shred_OCC")
else
 
OCC_Five = 0
MXP_Five = 0
 SWL_Five = 0
USBS_Five = 0
 LPSBS_Five = 0
HWM_Five = 0
 Shred_Five = 0
 end if
MyRec.close
%>


<td class="auto-style36" ><%= OCC_Five %></td>
<td class="auto-style36" ><%= MXP_Five %></td>
<td class="auto-style36" ><%= USBS_Five %></td>

<td class="auto-style36" ><%= SWL_Five %></td>
		<td class="auto-style36"  ><%= LPSBS_Five %></td>
		<td class="auto-style36"  ><%= HWM_Five %></td>
		<td class="auto-style36"  ><%= Shred_Five %></td>


	</tr>

		<tr>
							<td class="auto-style25" style="height: 24px"><font face="arial" size="2"><%= dateadd("d", 5, strnow)%></td>
		<% strDate1 = datepart("w", dateadd("d", 5, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

	
		<td class="auto-style25" style="height: 24px"><font face="arial" size="2"><%= strDayofWeek %></td>

<td  class="style115"
<% strGroup1 = strGroup5OCC + strOCC5 + (3 * RstrOCC4) - OCC_five
strGroup6OCC = strGroup1 %>
		<% if strGroup1 >15 then %>
		
		bgcolor="#00B0F0" >
			
				<% elseif strGroup1 > 10 and strGroup1 < 16 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 > 0 and strGroup < 11 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %>	<span class="style16">
<%= strGroup1 %></span> </td>


	<td class="style115"
<% strGroup1 = strGroup5MXP + strMXP5 + (3*RstrMXP4) - MXP_Five
strGroup6MXP = strGroup1  %>
		<% if strGroup1 >4 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 1 and strGroup < 5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

<td class="style115"
<% strGroup1 = strGroup5USBS + strUSBS5 + (3*RstrUSBS4) -  USBS_Five
strGroup6USBS = strGroup1  %>
		<% if strGroup1 >2 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

	<td class="style115"
<% strGroup1 = strGroup5SWL + strSWL5 + (3*RstrSWL4) -  SWL_Five
strGroup6SWL = strGroup1  %>
 
		<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 2 and strGroup1 < 7 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

		<td class="style115"
<% strGroup1 = strGroup5LPSBS + strLPSBS5 + (3*RstrLPSBS4) -  LPSBS_five
strGroup6LPSBS = strGroup1  %>
		<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 2 and strGroup1 < 7 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>


<td class="style115"
<% strGroup1 = strGroup5HWM + strHWM5 + (3*RstrHWM4) -  HWM_Five
strGroup6HWM = strGroup1  %>
		<% if strGroup1 >2 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>



<td class="style115"
<% strGroup1 = strGroup5Shred + strShred5 - Shred_Five
strGroup6SHRED = strGroup1  %>
		<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 2 and strGroup1 < 7 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>


	<td class="auto-style29"  ><%= strOCC6 %><% if RstrOCC5 > 0 then %> / <%= RstrOCC5 %> <% end if %></td>
	<td class="auto-style29"  ><%= strMXP6 %><% if RstrMXP5 > 0 then %> / <%= RstrMXP5 %> <% end if %></td>
		<td class="auto-style29"  ><%= strUSBS6 %><% if RstrUSBS5 > 0 then %> / <%= RstrUSBS5 %> <% end if %></td>
	<td class="auto-style29"  ><%= strSWL6 %><% if RstrSWL5 > 0 then %> / <%= RstrSWL5 %> <% end if %></td>

	<td class="auto-style29" style="width: 28px"  ><%= strLPSBS6 %><% if RstrLPSBS5 > 0 then %> / <%= RstrLPSBS5 %> <% end if %></td>

	<td class="auto-style29" style="width: 28px"  ><%= strHWM6 %><% if RstrHWM5 > 0 then %> / <%= RstrHWM5 %> <% end if %></td>

	<td class="auto-style29" style="width: 28px"  ><%= strSHRED6 %></td>
<%  
  strSelectdate = dateadd("d", 5, strnow)
strsql = "Select  tblTempYardTotals.* from tblTempYardTotals where INV_Date = '" & strSelectdate & "'"

	
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
 
OCC_Six = MyRec.fields("Total_OCC")
MXP_Six = MyRec.fields("TOtal_MXP")
SWL_Six = MyRec("SWL")
USBS_Six = MyRec("USBS")
 LPSBS_Six = MyRec("LPSBS")
HWM_Six = MyRec("HWM")
SHRED_Six = MyRec("Shred_OCC")
else
 
OCC_Six = 0
MXP_Six = 0
 SWL_Six = 0
USBS_Six = 0
 LPSBS_Six = 0
HWM_Six = 0
 Shred_Six = 0
 end if
MyRec.close
%>


<td class="auto-style36" ><%= OCC_Six %></td>
<td class="auto-style36" ><%= MXP_Six %></td>
<td class="auto-style36" ><%= USBS_Six %></td>

<td class="auto-style36" ><%= SWL_Six %></td>
		<td class="auto-style36"  ><%= LPSBS_Six %></td>
		<td class="auto-style36"  ><%= HWM_Six %></td>
		<td class="auto-style36"  ><%= Shred_Six %></td>




	</tr>

		<tr>
							<td class="auto-style25" style="height: 24px"><font face="arial" size="2"><%= dateadd("d", 6, strnow)%></td>
		<% strDate1 = datepart("w", dateadd("d", 6, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

	
		<td class="auto-style25" style="height: 24px"><font face="arial" size="2"><%= strDayofWeek %></td>

	<td  class="style115"
<% strGroup1 = strGroup6OCC + countOCC6 + (3 * RstrOCC5) - OCC_six
strGroup7OCC = strGroup1 %>
		<% if strGroup1 >15 then %>
		
		bgcolor="#00B0F0" >
			
				<% elseif strGroup1 > 10 and strGroup1 < 16 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 > 0 and strGroup < 11 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %>	<span class="style16">
<%= strGroup1 %></span> </td>

	<td class="style115"
<% strGroup1 = strGroup6MXP + countMXP6 + (3*RstrMXP5) - MXP_Six
strGroup7MXP = strGroup1  %>
		<% if strGroup1 >4 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 1 and strGroup < 5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>
		<td class="style115"
<% strGroup1 = strGroup6USBS + countUSBS6 + (3*RstrUSBS5) -  USBS_Six
strGroup7USBS = strGroup1  %>
		<% if strGroup1 >2 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>
	
	<td class="style115"
<% strGroup1 = strGroup6SWL + strSWL6 + (3*RstrSWL5) -  SWL_Six
strGroup7SWL = strGroup1  %>
 
		<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 2 and strGroup1 < 7 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

				<td class="style115"
<% strGroup1 = strGroup6LPSBS + strLPSBS6 + (3*RstrLPSBS5) -  LPSBS_six
strGroup7LPSBS = strGroup1  %>
		<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 2 and strGroup1 < 7 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

<td class="style115"
<% strGroup1 = strGroup6HWM + strHWM6 + (3*RstrHWM5) -  HWM_Six
strGroup7HWM = strGroup1  %>
		<% if strGroup1 >2 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>



<td class="style115"
<% strGroup1 = strGroup6Shred + strShred6 - Shred_Six
strGroup7SHRED = strGroup1  %>
		<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 2 and strGroup1 < 7 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

 

	<td class="auto-style29"  ><%= strOCC7 %><% if RstrOCC6 > 0 then %> / <%= RstrOCC6 %> <% end if %></td>
	<td class="auto-style29"  ><%= strMXP7 %><% if RstrMXP6 > 0 then %> / <%= RstrMXP6 %> <% end if %></td>
		<td class="auto-style29"  ><%= strUSBS7 %><% if RstrUSBS6 > 0 then %> / <%= RstrUSBS6 %> <% end if %></td>
	<td class="auto-style29"  ><%= strSWL7 %><% if RstrSWL6 > 0 then %> / <%= RstrSWL6 %> <% end if %></td>

	<td class="auto-style29" style="width: 28px"  ><%= strLPSBS7 %><% if RstrLPSBS6 > 0 then %> / <%= RstrLPSBS6 %> <% end if %></td>

	<td class="auto-style29" style="width: 28px"  ><%= strHWM7 %><% if RstrHWM6 > 0 then %> / <%= RstrHWM6 %> <% end if %></td>

	<td class="auto-style29" style="width: 28px"  ><%= strSHRED7 %></td>
<%  
  strSelectdate = dateadd("d", 6, strnow)
strsql = "Select  tblTempYardTotals.* from tblTempYardTotals where INV_Date = '" & strSelectdate & "'"

	
   Set MyRec = Server.CreateObject("ADODB.Recordset")
   MyRec.Open strSQL, Session("ConnectionString") 
If not MyRec.eof then
 
OCC_Seven = MyRec.fields("Total_OCC")
MXP_Seven = MyRec.fields("TOtal_MXP")
SWL_Seven = MyRec("SWL")
USBS_Seven = MyRec("USBS")
 LPSBS_Seven = MyRec("LPSBS")
HWM_Seven = MyRec("HWM")
SHRED_Seven = MyRec("Shred_OCC")
else
 
OCC_Seven = 0
MXP_Seven = 0
 SWL_Seven = 0
USBS_Seven = 0
 LPSBS_Seven = 0
HWM_Seven = 0
 Shred_Seven = 0
 end if
MyRec.close
%>


<td class="auto-style36" ><%= OCC_Seven %></td>
<td class="auto-style36" ><%= MXP_Seven %></td>
<td class="auto-style36" ><%= USBS_Seven %></td>
<td class="auto-style36" ><%= SWL_Seven %></td>

		<td class="auto-style36"  ><%= LPSBS_Seven %></td>
		<td class="auto-style36"  ><%= HWM_Seven %></td>
		<td class="auto-style36"  ><%= Shred_Seven %></td>

	</tr>

		<tr>
							<td class="auto-style25" style="height: 24px"><font face="arial" size="2"><%= dateadd("d", 7, strnow)%></td>
		<% strDate1 = datepart("w", dateadd("d", 7, strnow))
		 
		 	if strDate1  = 1 then
 	 strDayofweek =  "Sunday" 
 	elseif strDate1    = 2 then
 	 strDayofweek =  "Monday" 
 	elseif strDate1    = 3 then
 	 strDayofweek =  "Tuesday"
 	elseif strDate1    = 4 then
 	 strDayofweek =  "Wednesday"
 	elseif strDate1   = 5 then
 	 strDayofweek =  "Thursday"
 	elseif strDate1   = 6 then
 	 strDayofweek =  "Friday"
 	elseif strDate1    = 7 then
 	 strDayofweek =  "Saturday"
 	end if  %>

	
		<td class="auto-style25" style="height: 24px"><font face="arial" size="2"><%= strDayofWeek %></td>

	<td  class="style115"
<% strGroup1 = strGroup7OCC + countOCC7 + (3 * RstrOCC6) - OCC_seven
strGroup8OCC = strGroup1 %>
		<% if strGroup1 >15 then %>
		
		bgcolor="#00B0F0" >
			
				<% elseif strGroup1 > 10 and strGroup1 < 16 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 > 0 and strGroup < 11 then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %>	<span class="style16">
<%= strGroup1 %></span> </td>


	<td class="style115"
<% strGroup1 = strGroup7MXP + countMXP7 + (3*RstrMXP6) - MXP_Seven
strGroup8MXP = strGroup1  %>
		<% if strGroup1 >4 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 1 and strGroup < 5 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

		<td class="style115"
<% strGroup1 = strGroup7USBS + countUSBS7 + (3*RstrUSBS6) -  USBS_Seven
strGroup8USBS = strGroup1  %>
		<% if strGroup1 >2 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

	
	<td class="style115"
<% strGroup1 = strGroup7SWL + strSWL7 + (3*RstrSWL6) -  SWL_Seven
strGroup8SWL = strGroup1  %>
 
		<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 2 and strGroup1 < 7 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

						<td class="style115"
<% strGroup1 = strGroup7LPSBS + strLPSBS7 + (3*RstrLPSBS6) -  LPSBS_seven
strGroup8LPSBS = strGroup1  %>
		<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 2 and strGroup1 < 7 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>
<td class="style115"
<% strGroup1 = strGroup7HWM + strHWM7 + (3*RstrHWM6) -  HWM_Seven
strGroup8HWM = strGroup1  %>
		<% if strGroup1 >2 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 = 2 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>


		
<td class="style115"
<% strGroup1 = strGroup7Shred + strShred7 - Shred_Seven
strGroup8SHRED = strGroup1  %>
		<% if strGroup1 > 6 then %>
		
		bgcolor="#00B0F0">
				
<% elseif strGroup1 > 2 and strGroup1 < 7 then %>
		
		bgcolor="#92D050">
	<% elseif strGroup1 =1 or strGroup1 = 2  then %>
	bgcolor="yellow">

	<% else %>
		bgcolor="red">

		<% end if %><span class="style16">
<%= strGroup1 %></span> </td>

	<td class="auto-style29"  ><%= strOCC8 %><% if RstrOCC7 > 0 then %> / <%= RstrOCC7 %> <% end if %></td>
	<td class="auto-style29"  ><%= strMXP8 %><% if RstrMXP7 > 0 then %> / <%= RstrMXP7 %> <% end if %></td>
		<td class="auto-style29"  ><%= strUSBS8 %><% if RstrUSBS7 > 0 then %> / <%= RstrUSBS7 %> <% end if %></td>
	<td class="auto-style29"  ><%= strSWL8 %><% if RstrSWL7 > 0 then %> / <%= RstrSWL7 %> <% end if %></td>

	<td class="auto-style29" style="width: 28px"  ><%= strLPSBS8 %><% if RstrLPSBS7 > 0 then %> / <%= RstrLPSBS7 %> <% end if %></td>

	<td class="auto-style29" style="width: 28px"  ><%= strHWM8 %><% if RstrHWM7 > 0 then %> / <%= RstrHWM7 %> <% end if %></td>

	<td class="auto-style29" style="width: 28px"  ><%= strSHRED8 %></td>
 

<td class="auto-style36" >&nbsp;</td>
<td class="auto-style36" >&nbsp;</td>
<td class="auto-style36" >&nbsp;</td>
<td class="auto-style36" >&nbsp;</td>
<td class="auto-style36" >&nbsp;</td>
<td class="auto-style36" >&nbsp;</td>
	<td class="auto-style36" >&nbsp;</td>
	</tr>

	</table>

 

