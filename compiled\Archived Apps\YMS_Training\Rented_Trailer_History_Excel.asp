

<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">
<TITLE>Rented Trailer Movement</TITLE>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
 
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_General.asp"-->


<%  
	Dim strsQL, rstEquip

   	Dim objEquipSearch, rstTotals, strCount, strDed, strNet, strTons, rstTotalReceived, strTR, objNew, rstSpecies
  	Dim  strVendor, strBegDate, strEndDate, strSpecies, strMonth, strDateReceived, strDateAdded, rst<PERSON><PERSON>h
 	Dim  rstVendor, rstNF
  	Dim objGeneral, strPO, gcount, strDays, strBeg, strEnd, what
  	
  	Dim strTrailer, strAT, strStype, objMOC
 
   	Dim strPageNav
 

   set objGeneral = new ASP_CLS_General
 
   Response.ContentType = "application/vnd.ms-excel"	

Function StripOut(From, What) 

    Dim i 
	 
    StripOut = From
    for i = 1 to len(What)
	StripOut = Replace(StripOut, mid(What, i, 1), "")
    next 
	 
	End Function
	
	What = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
%>
 


<style type="text/css">
.style1 {
	text-align: right;
}
.style2 {
	border-style: solid;
	border-width: 1px;
}
</style>
</head>


 
    
 

&nbsp;<TABLE borderColor=white cellSpacing=0 cellPadding=0 class="style2" align = center style="width: 50%">
	 <tr class="tableheader">
	 	<td  ><b><font face="Arial" size="2">Trailer</font></b></td>
	<td  ><b><font face="Arial" size="2">Date_received</font></b></td>
		<td align = left><b><font face="Arial" size="2">Date_unloaded</font></b></td>
 	<td align = left><b><font face="Arial" size="2">Species</font></b></td>
	<td align = left><b><font face="Arial" size="2"> From Rail Car</font></b></td>
<td><b><font face="Arial" size="2">Comments</font></b></td>
<td><b><font face="Arial" size="2">&nbsp;</font></b></td>

	</tr>



      
  	<% 
      Dim ii
       ii = 0
       strTcount = 0
       
           Set MyRec2 = Server.CreateObject("ADODB.Recordset")    
      
	strsql = "select tblRentedTrailer.* from tblRentedTrailer order by Trailer_nbr"
 
    MyRec2.Open strSQL, Session("ConnectionString"), adOpenDynamic

       while not MyRec2.Eof
       if MyRec2("OOS") = "Y" then
       'skip
       else
       strcount = 1
       
        strTrailer = MyRec2("Trailer_nbr")  
   		 strT2 = stripout(strTrailer,What)
   		 
strBeg = Request.querystring("b")
strEnd = Request.querystring("e") %>
   		
    
    

   <tr bgcolor="white">   <td colspan="8"  >  <font size="3" face="Arial"><b>        <%= MyRec2.fields("Trailer_nbr")%></b></font></td>
   
    </tr>
  <tr>  
   		 

   <% strsql = "SELECT Entry_Bid, tblcars.cid, species,  tblcars.location, carrier, Transfer_date, trans_unload_date, tblcars.species, "_
   &"  transfer_trailer_nbr, tblCars.release_nbr,  tblCars.Trailer,  tblCars.Date_Received, tblCars.Date_unloaded, "_
   &"   tblcars.other_comments, tblCars.Tons_received, RC_CID FROM  tblCars  "_
        &"  where datediff(d, '" & strBeg & "', Date_received) >= 0 and datediff(d, Date_received, '" & strEnd & "') >= 0 and"_
      &"  ( Trailer = '" & strTrailer & "' or Trailer = '" & strT2 & "' "_
        &"  or Transfer_Trailer_Nbr = '" & strTrailer & "' or Transfer_Trailer_Nbr = '" & strT2 & "') "
       
 Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    
    If not MyRec.eof then
    while not MyRec.eof

    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr class=tablecolor1>
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
    <% if MyRec("Trailer") = "UNKNOWN" then %>
     
      <td  >  <font size="2" face="Arial">        <%= MyRec.fields("Transfer_trailer_nbr")%></font></td>
<% else %>
   <td  >  <font size="2" face="Arial">        <%= MyRec.fields("Trailer")%></font></td>
<% end if %>
      

      <td  >  <font size="2" face="Arial">  &nbsp;      <%= MyRec.fields("Date_received")%></font></td>
 
    
     <td  >  <font size="2" face="Arial">        <%= MyRec.fields("Date_unloaded")%></font></td>

    	<td><font size="2" face="Arial"><%= MyRec.fields("Species")%>&nbsp;</font></td>
    		<td><font size="2" face="Arial"><% if len(MyRec("RC_CID")) > 1 then %> YES <% end if %>&nbsp;</font></td>
	<td><font size="2" face="Arial"><%= MyRec.fields("Other_comments")%>&nbsp;</font></td>
	<td><font size="2" face="Arial"><%= strCount %>&nbsp;</font></td>
  
   </tr>
    <% 
       ii = ii + 1
 
    
strcount = strcount + 1
strTcount = strTcount + 1

       MyRec.MoveNext
     Wend
     end if 
     
     end if
     MyRec2.movenext
     wend
    %></table>
    <p align="center"><font face="arial" size="2"><b>Total:  <%= strTcount %></b></font></p>
 