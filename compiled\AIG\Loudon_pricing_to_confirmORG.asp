																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Loudon Pricing - Purchase Orders to Confirm</TITLE>
<style type="text/css">
.auto-style1 {
	font-family: Arial, Helvetica, sans-serif;
}
</style>
</head>

<!--#include file="classes/asp_cls_DataAccessAIG.asp"-->
<!--#include file="classes/asp_cls_headerAIG.asp"-->
<!--#include file="classes/asp_cls_SessionStringAIG.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_SessionLDN.asp"-->



<% Dim MyRec, strsql, MyConn, strUserType
strPO = request.querystring("p")
strVendor = Trim(request.querystring("v"))

 
    strUserType = ""
 
       strUserType = ""
 strsql = "SELECT tblAIGUserType.* FROM tblAIGUserType where BID = '" & Session("EmployeeID") & "' and User_type = 'B'" 
    Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
    If not MyConn.eof  then
 strUserType = "B"
    end if
    
         If Session("EmployeeID") = "C97338" then
    strUserType = "B"
    end if

    If strUserType = "" then

    Response.write ("<br><br><font face = arial size = 3><b>You do not have authorization to view this page</b></font>")
   else
    BuildScreen()
    end if 

    %>
    <%Sub Buildscreen

if len(strPO) > 3 then 
 strsql = "SELECT tblPricing.* FROM tblPricing "_
&" WHERE Site = 'LOUDON' AND Price Is Not Null  AND Do_not_invoice Is Null and Confirmed='No' and PO_Nbr >= " & strPO & ""
else
 strsql = "SELECT tblPricing.* FROM tblPricing "_
&" WHERE Site = 'LOUDON' AND Price Is Not Null  AND Do_not_invoice Is Null and Confirmed='No' and SAP_Vendor = '" & strVendor & "'"
end if
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
 
%>

<body>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>


<td align = center><b>
<font face="arial" size="4" >Loudon Recovered Paper Purchase Orders to Confirm</font></b></span></td>
<td align = center class="auto-style1"><a href="Loudon_pricing_to_confirm_Select.asp">
<strong>RETURN</strong></a></td>


</tr>
	    </table><br>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=75% class = "tablecolor1" border=1 align = center>  
	 <tr class="tableheader">
	<td>&nbsp;</td>
	<td  >  <font face="Arial" size="2">PO #</font></td>
	<td  >  <font face="Arial" size="2">Vendor Number</font></td>
		<td  >  <font face="Arial" size="2">Vendor Name</font></td>
        <td  > <font face="Arial" size="2">Price</font></td>
             <td  > <font face="Arial" size="2">Confirmed</font></td>
	<td>&nbsp;</td>
		


	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
           strAIGPO = MyRec.fields("PO")
    strsql2 = "SELECT distinct [PO#] from Master where len(ArrivalDate) > 3 and [PO#] = '" & strAIGPO & "'"
 
	Set rs = Server.CreateObject("ADODB.Recordset")
	   
 rs.Open strSQL2, Session("ConnectionLDN")


 
	if not rs.eof then

    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
	<% if strUsertype = "D" then %>
			<td>   <font size="2" face="Arial"> &nbsp;</font></td>
			<% else %>
	<td> <font size="2" face="Arial"><a href="AIG_Delete_pricing.asp?id=<%= MyRec.fields("ID") %>&p=l&v=<%= strVendor %>&po=<%= strPO %>">Delete</a></td>	
<% end if %>

	<td  >      <font size="2" face="Arial">        <%= MyRec.fields("PO")%>&nbsp;</font></td>
      	<td  >      <font size="2" face="Arial">        <%= MyRec.fields("Vendor_nbr")%>&nbsp;</font></td>
      		<td  >      <font size="2" face="Arial">        <%= MyRec.fields("SAP_Vendor")%>&nbsp;</font></td>
			<td  >        <font size="2" face="Arial">        <%= MyRec.fields("Price")%>&nbsp;</font></td>
			<td  >        <font size="2" face="Arial">        <%= MyRec.fields("Confirmed")%>&nbsp;</font></td>
						<% if strUsertype = "D" then %>
			<td>   <font size="2" face="Arial"> &nbsp;</font></td>
			<% else %>
	<td> <font size="2" face="Arial"><a href="Confirm.asp?id=<%= MyRec.fields("ID") %>&p=LC&c=<%= MyRec.fields("Confirmed")%>&v=<%= strVendor %>&po=<%= strPO %>">Confirm</a></td>	
	<% end if %>
</tr>

 <%  rs.close
 end if

       ii = ii + 1
       MyRec.MoveNext
     Wend
     MyRec.close
    %>
</table>

<% End Sub %><!--#include file="AIGfooter.inc"-->