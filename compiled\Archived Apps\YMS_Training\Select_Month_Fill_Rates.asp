
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">


<TITLE>Maintain Orders</TITLE>


<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

 <%    Dim objMOC,  strID, rstMonth

       Dim objGeneral
	
       set objGeneral = new ASP_CLS_General

   set objNew = new ASP_Cls_Fiber     
	
	set rstMonth= objNew.FiberMonth()

if objGeneral.IsSubmit() Then

strMonth = request.form("Month")

if len(request.form("Month")) > 1  then
strid = request.form("Month")
Response.redirect("rptMobileOrders.asp?id=" & strMonth)

else
Response.write("<font size = 3 color = red face = arial><b>Please Select an Import Month</b></font>")
end if

End if
%>
<SCRIPT LANGUAGE="JavaScript">
function formHandler(Form1){
document.forms["Form1"].submit();
}
</SCRIPT>

<style type="text/css">
.style5 {
	font-family: Arial;
	font-size: medium;
	font-weight: bold;
}
</style>
</head>

<body>
<form name="form1" action="Select_month_fill_rates.asp" method="post" >

<p>&nbsp;</p>
<div align="center">
<table border="1" cellpadding="0" class = "tablecolor1" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="75%" id="AutoNumber2">
<tr>    <TD align = center bgcolor="#FFFFB3">  <p>&nbsp;</p> <b>
		<font face="Arial"><br><br></font></b>&nbsp;
     <font face="Arial">
		
     	<p>&nbsp;</p>
	
<p align="center"><br>&nbsp;</TD>
       <TD align = center bgcolor="#F2F2FF">  &nbsp;<p> 
		<span class="style5">Enter Import Month</span><br><br>
     	<font size="3">     
    <font face="Arial">
   <select size="1" name="Month">

     <option value="">--- Select ---</option>
       <%= objGeneral.OptionListAsString(rstMonth, "Import_month", "Import_month", strMonth) %>
     </select></font></p>
		<p align="center"><input type="submit" value="Continue" id=submit1 name=submit1><br>&nbsp;</TD>
       <TD align = center bgcolor="#FFFFB3">  <p>&nbsp;</p> <b>
		<font face="Arial"><br><br></font></b>&nbsp;
     <font face="Arial">
		
     	<p>&nbsp;</p>
	
<p align="center"><br>&nbsp;</TD></tr>

</table>
</div>
</form>

<!--#include file="Fiberfooter.inc"-->