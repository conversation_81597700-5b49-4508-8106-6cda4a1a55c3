   																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 14.0">

<TITLE>Carriers </TITLE>

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 



<% Dim MyRec, strsql, strstartdate
strStartdate = formatdatetime(now(),2)

strsql = "SELECT * from tblCarrier order by Carrier"

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")

%>

<style type="text/css">
.style1 {
	font-family: Arial;
	font-size: x-small;
}
.style2 {
	color: #FF0000;
}
.style3 {
	text-align: center;
}
.style4 {
	font-family: Arial;
	font-size: x-small;
	text-align: center;
}
</style>
</head>

<body>

<% if Session("EmployeeID") = "C97338" or Session("EmployeeID") = "B55404" or Session("EmployeeID") = "B55548"   or Session("EmployeeID") = "B38763"  or Session("EmployeeID") = "B53909"  then %>
<br>
	
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>


<td align = left><b><font face="Arial">Carrier List&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<span class="style2">&nbsp;Note:&nbsp; Email for empty trailer will not be sent 
to Carriers with email <NAME_EMAIL>.</span></font></b></td>
<td align = right>&nbsp;</td>

<td align = right><font face="Arial"><a href="Carrier_add.asp"><b>Add New</b></a>&nbsp;</td>


</tr>
	    </table>
	
	
	<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=90% class = "tablecolor1" border=1 align = center>  
	 <tr class="tableheader">
<td>&nbsp;</td>

				<td  align = left>     <font face="Arial" size="2">	Carrier</font></td>
		<td class="style1"  > <font face="Arial" size="2">Carrier Description</font></td>
		
<% if Session("EmployeeID") = "C97338" or Session("EmployeeID") = "B55404" or Session("EmployeeID") = "B55548" then %>
<td  align = left class="style1">  <font face="Arial" size="2">Trailer Weight</font></td>
<% end if %>
		<td class="style4">  <font face="Arial" size="2"> Free<br> Days</font></td>
		<td  align = left class="style1">  <font face="Arial" size="2"> Fee</font></td>
		<td  align = left class="style1">  <font face="Arial" size="2"> Email</font></td>
		<td align = right>&nbsp;</td>
	</tr>

 <% Dim ii
       ii = 0
       while not MyRec.Eof
    %>
    <% if ( ii mod 2) = 0 Then %>
       <tr bgcolor="EEF2F6">
    <% else %>
       <tr class=tablecolor2>
    <% end if %>
<td> <font size="2" face="Arial">

<a href="Carrier_Edit.asp?id=<%= MyRec.fields("ID") %>">

Edit</a></td>

	<td  ><font size="2" face="Arial"><%= MyRec.fields("Carrier")%></font></td>
	
<td  ><font size="2" face="Arial"><%= MyRec.fields("C_Description")%></font></td>
<% if Session("EmployeeID") = "C97338" or Session("EmployeeID") = "B55404" or Session("EmployeeID") = "B55548" then %>
	<td  ><font size="2" face="Arial"><%= MyRec.fields("Weight")%></font>&nbsp;</td><% end if %>

	<td class="style3"  ><font size="2" face="Arial"><%= MyRec.fields("Free_days")%></font></td>
		<td  ><font size="2" face="Arial"><%= MyRec.fields("Fee")%>&nbsp;</font></td>
	<td  ><font size="2" face="Arial"><%= MyRec.fields("Email_add")%>&nbsp;</font></td>
<td> <font size="2" face="Arial"><a href="Carrier_Delete.asp?id=<%= MyRec.fields("ID") %>">

Delete</a></td>
	
</tr>

 <%
       ii = ii + 1
       MyRec.MoveNext
     Wend
    %>
</table>
<% else %>
<p align="center"><font face="arial" size="3"><b>You do not have authorization to view this page</b></font></p>
<% end if %><!--#include file="Fiberfooter.inc"-->