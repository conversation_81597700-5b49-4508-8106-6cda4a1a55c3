																	

<head>

<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">

<TITLE>Edit Time Entry </TITLE>


<!--#include file="classes/asp_cls_General.asp"-->
 
<!--#include file="classes/asp_cls_SessionStringFIBERDEV.asp"-->
 



<%


	x = Request.Servervariables("LOGON_USER")
	strEID = ""
	If InStr(x,"\") Then
  		y = SPLIT(x,"\")
  		strEID = y(1)
	Else
  		strEID = x
	End If




 Dim strid
strid = request.querystring("id")
strsql = "Select tblTime.* from tblTime where id = " & strid
Set MyRec = Server.CreateObject("ADODB.Recordset")
    	MyRec.Open strSQL, Session("ConnectionString") 


  set objGeneral = new ASP_CLS_General
  if objGeneral.IsSubmit() Then	

  
  
 strstatus = Replace(request.form("Status"), "'", "''")
  strTask = Replace(request.form("Task"), "'", "''")
        	
  	
	strsql =  "Update tblTime set  Time_start = '" & request.form("Start") & "', Time_stop =  '" & request.form("Stop") & "',   Files_modified = '" & request.form("Files") & "', "_
	&" Status = '" & strstatus & "', Last_update = '" & now() & "', Task = '" & strTask & "' where id = " & strid  
		
		Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
			
Response.redirect("Projects_time.asp")		
end if

	
%>

<style type="text/css">
.style3 {
	border: 1px solid #808080;
}
.style7 {
	font-family: arial;
	font-size: x-small;
	background-color: #E8F5FF;
}
.style8 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	text-align: center;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style9 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: arial;
		font-size: x-small;
		background-color: #E8F5FF;
		text-align: center;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style10 {
	border-left: 1px solid #C0C0C0;
	border-right-style: solid;
	border-right-width: 1px;
	border-top: 1px solid #C0C0C0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style11 {
	border-left: 1px solid #C0C0C0;
	border-top: 1px solid #C0C0C0;
	font-family: arial;
	font-size: x-small;
	background-color: #E8F5FF;
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}
.style12 {
	font-family: Arial, Helvetica, sans-serif;
}
.style13 {
	color: #FF0000;
}
.style14 {
	text-align: center;
}
.style15 {
	font-size: xx-small;
}
</style>
</head>

<body>
<br>
	
<form name="form1" action="Time_edit.asp?id=<%= strid %>" method="post" ID="Form1">
<TABLE borderColor=white cellSpacing=0 cellPadding=0 width=100%  border=1 align = center>

 <TD align = left><font size="2" face = arial>&nbsp;</font></td>
<td align = center><font face="Arial"><b>Modify Entry for <%= MyRec("Project") %><br><br>Start Time: &nbsp;
<input type="text" name="Start" value="<%= MyRec("Time_start") %>"></b></font></td>
<td align = right><font face="Arial"><a href="Projects_time.asp">Return</a>&nbsp;</td>


</tr>
	    </table>



	<p>&nbsp;</p>
	<table id="table1" style="width: 100%" align="center" class="style3">
<tr bgcolor = #CCCCFF>
		<td bgcolor="#FFFFD7" class="style11">

Date/time Stop</td>
		<td bgcolor="#FFFFD7" class="style11">

Task</td>
		<td bgcolor="#FFFFD7" class="style9">

Files Modified&nbsp; (if a lot, list general description</td>
	</tr>
	<tr>
		<td style="height: 64px" class="style10"><font face = arial size = 1>
		<% if len(MyRec("Time_stop")) > 2 then %> 
		<input type="text" name="Stop" size="20" style="width: 142px" value="<%= MyRec("Time_stop") %>"></td>
		<% else %>
		<input type="text" name="Stop" size="20" style="width: 142px" value="<%= now() %>"></td>
		<% end if %>
		<td style="height: 64px" class="style10"><font face = arial size = 1>
		<input type="text" name="Task" size="20" style="width: 473px" value='<%= MyRec("Task") %>'></td>
		

		<td class="style8" style="height: 64px"><font face = arial size = 1>
		<input type="text" name="Files" size="20" style="width: 665px" value="<%= MyRec("Files_modified") %>"></td>
	</tr></table><br>
	<table width="50%" border="0" cellspacing="1" bgcolor = "#DFDFFF" align="center"><tr>
		<td bgcolor="#FFFFD7" class="style7">

Comments/Status</td>
	</tr>

            <tr> 
            
              <td bgcolor="#DFDFFF"><font face="Arial" size="1">
				<textarea name="Status" rows="5"  style="width: 838px;"><%=MyRec("Status")%></textarea></font></td>
            </tr>
          </table>

<div class="style14">

<br>
		

	<br>
	<span class="style12"><span class="style13"><strong><span class="style15">&nbsp;DO NOT SUBMIT THIS PAGE WITHOUT NOTING WHAT YOU 
ACCOMPLISHED DURING THE 
	TIME. <br>
&nbsp;iF THE WORK IS NOT FINISHED, NOTE THE STATUS - WHAT IS LEFT TO BE DONE.</span></strong></span></span></div>
		

<p align="center"><font face="Arial">
	<Input name="Update" type="submit" Value="Submit" style="font-weight: 700" ></font></p>
</form>

<!--#include file="Fiberfooter.inc"-->