<html>
<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<!--#include file="classes/asp_cls_ProcedureFIBER.asp"-->
<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>Resolve Exceptions</title>
<style type="text/css">
.style1 {
	border: 1px solid #E4E4E4;
	border-collapse: collapse;
		background-color: #FFFFCC;
}
.style3 {
	font-weight: bold;
	border-style: solid;
	border-color: #FFFFCC;
	background-color: #FFFFCC;
}

.style4 {
	font-family: Arial;
	border-style: solid;
	border-color: #FFFFCC;
	background-color: #FFFFCC;
}

.style26 {
	font-family: Arial;
	font-size: x-small;
}
.style27 {
	font-weight: bold;
	border-style: solid;
	border-color: #FFFFCC;
	background-color: #FFFFCC;
	font-family: Arial;
	font-size: x-small;
}
.style28 {
	border-color: #D5D5FF;
	background-color: #D5D5FF;
}
.style29 {
	font-weight: bold;
	border-style: solid;
	border-color: #D5D5FF;
	background-color: #D5D5FF;
}
.auto-style1 {
	font-weight: bold;
	border-style: solid;
	border-color: #FFFFFF;
	background-color: #FFFFFF;
}
.auto-style2 {
	background-color: #FFFFFF;
}
.auto-style3 {
	font-family: Arial, Helvetica, sans-serif;
}
.auto-style4 {
	background-color: #E7EBFE;
}
.auto-style5 {
	border-color: #D5D5FF;
	background-color: #E7EBFE;
}
.auto-style6 {
	font-weight: bold;
	border-style: solid;
	border-color: #D5D5FF;
	background-color: #E7EBFE;
}
</style>
</head>
<%
    Dim strSQL, MyRec, strID, objMOC, rstFiber, strCarrier
      
    Dim strTrailer
    Dim strReleaseNbr
    Dim strRECNbr
    Dim strGrossWeight
    Dim strTareWeight
    Dim strTonsReceived
    Dim strDateReceived
    Dim strGenerator
    Dim strGenCity
    Dim strGenState
    Dim strOther, strR, strsql3

       strId  = Trim(Request.QueryString("id")) 


 set objGeneral = new ASP_CLS_General
 

if objGeneral.IsSubmit() Then


   	 strSQL3 = "Select EID From tblAdmin Where EID = '" & Session("EmployeeID") & "' and User_Type = 'A'"


   	 Set MyRec = Server.CreateObject("ADODB.Recordset")
   	 MyRec.Open strSQL3, Session("ConnectionString")


	If not Myrec.eof then
	MyRec.close
	
strsql = "Select tblOrder.* from tblOrder where Release = '" & Request.form("Release")  & "'"
    Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
    If not MyConn.eof then 
    
    strPO = MyConn.fields("PO")

    strGenerator = Replace(MyConn.fields("Generator"), "'", "''")
    strVendor = MyConn.fields("Vendor")
    strRelease = MyConn.fields("Release")
    strState = MyConn.fields("State")
    strSpecies = MyConn.fields("Species")
    strCity = Replace(MyConn.fields("City"), "'", "''")
    strGrade = Myconn("Grade")

    strOID = MyConn.fields("OID")
    
    MyConn.close
    
    Strsql = "Update tblCars set OID = " & strOID & ", Release_nbr = '" & strRelease & "', PO = '" & strPO & "', Generator = '" & strGenerator & "', "_
    &" Vendor = '" & strVendor & "', Gen_State = '" & strState & "', Gen_City = '" & strCity & "', Species = '" & strSpecies & "', "_
    &" Grade = '" & strGrade & "' where CID = " & strid
	
	
	Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close
	
If len(REquest.form("Release_old")) > 0 then	
strOldRelease = Request.form("Release_old")	
else
strOldRelease = ""
end if

strtoday = formatdatetime(now(),0)
			strsql = "Insert into tblMovement (CID, DDate, BID, From_location, To_location, Comment) "_
			&" select " & strid & ", '" & strtoday & "', '" & Session("EmployeeiD") & "', "_
			&" '" & strOldRelease & "', '" & strRelease & "', 'Changed Exception Release Number'"
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close


	end if
 
Response.redirect("Resolve_Exceptions.asp")

	else
	Response.write ("<br><font size = 3 color = Red face = arial><b>You do not have authorization to Change data.</font></br>")
	MyRec.close
	end if

  
ELSE


strsql = "SELECT tblCars.* from tblCars WHERE  CID =  " & strid

    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")


    strTrailer = MyRec.fields("Trailer")
    strRelease = MyRec.fields("Release_nbr")
    strVendor = MyRec.fields("Vendor")
     strGenerator = MyRec.fields("Generator")
     strSpecies = MyRec.fields("Species")
     strGrade = MyRec.fields("Grade")
   
    strComments = MyRec.fields("OTher_comments")
   
    strPO = MyRec.fields("PO")
    strOldRelease = MyRec.fields("Release_nbr")
   
    

MyRec.close

	end if

%>



<body><br>
<table width = 100%><tr><td width = 33%></td><td align = center width = 34%><b><font face="Arial" size="4">
Resolve Exception for Receipt # <%= strid %></font> </b></td><td align = right width = 33%><a href="Resolve_Exception_Detail.asp?id=<%= strid %>"><b><font face = arial size = 2>RETURN</a></font></b></td></tr></table>



<form name="form1" action="Resolve_Exception_Detail_two.asp?id=<%=strid%>" method="post">
<input type="hidden" name="Release" value="<%= request.querystring("r") %>">
<input type="hidden" name="Release_old" value="<%= strOldRelease %>">
<table cellspacing="0" width="100%" align = center class="style1">
    <tr>

      <td align = right class="style3">  &nbsp;</td>
<td  align = left> &nbsp;</td>
<td  align = left> &nbsp;</td>
<td  align = left> &nbsp;</td>
    <td align = right class="style3">  &nbsp;</td>
<td  align = left> &nbsp;</td>
<td  align = left> &nbsp;</td>
<td  align = left> &nbsp;</td>
    <td align = right class="style3">  &nbsp;</td>
<td  align = left> &nbsp;</td>
<td  align = left> &nbsp;</td>
<td  align = left> &nbsp;</td>
    <td align = right class="style3">  &nbsp;</td>
<td  align = left> &nbsp;</td>
</tr>

<tr >

   <td  align = right class="style3" >   <font face="Arial" size="2">Trailer:&nbsp;</font></td>
<td  align = left><font face="Arial" size="2"><%= strTrailer%></td>
    
    <td  align = right class="style3" >   <font face="Arial" size="2">Release Nbr:&nbsp;</font></td>
<td  align = left><font face="Arial" size="2"><%= strRelease%></td>
    <td  align = right class="style3" >   <font face="Arial" size="2">PO:&nbsp;</font></td>
<td  align = left><font face="Arial" size="2"><%= strPO %></td>
  <td  align = right class="style3" >   <font face="Arial" size="2">Vendor:&nbsp;</font></td>
<td  align = left><font face="Arial" size="2"><%= strVendor%></td>
<td  align = right class="style3" >   <font face="Arial" size="2">Generator:&nbsp;</font></td>
<td  align = left><font face="Arial" size="2"><%= strGenerator %></td>  
    <td  align = right class="style3" >   <font face="Arial" size="2">Species:&nbsp;</font></td>
<td  align = left><font face="Arial" size="2"><%= strSpecies %></td> 
    <td  align = right class="style3" >   <font face="Arial" size="2">Grade:&nbsp;</font></td>
<td  align = left><font face="Arial" size="2"><%= strGrade %></td> 

<td  align = right class="style3" >   <font face="Arial" size="2">Comments:&nbsp;</font></td>
<td  align = left><font face="Arial" size="2"><%= strComments%></td> </tr>

 
      <tr>

      <td align = right class="style3">  &nbsp;</td>
<td  align = left> &nbsp;</td>
<td  align = left> &nbsp;</td>
<td  align = left> &nbsp;</td>
    <td align = right class="style3">  &nbsp;</td>
<td  align = left> &nbsp;</td>
<td  align = left> &nbsp;</td>
<td  align = left> &nbsp;</td>
    <td align = right class="style3">  &nbsp;</td>
<td  align = left> &nbsp;</td>
<td  align = left> &nbsp;</td>
<td  align = left> &nbsp;</td>
    <td align = right class="style3">  &nbsp;</td>
<td  align = left> &nbsp;</td>
</tr></table>
<br><br><table width=100% >
      
<% strsql = "SELECT tblCars.* from tblCars WHERE  Release_nbr = '" & request.querystring("r") & "' and CID <> " & strid
    Set MyRec = Server.CreateObject("ADODB.Recordset")
    MyRec.Open strSQL, Session("ConnectionString")
    If not MyRec.eof then
    While not MyRec.eof 
    
    strCID = MyRec.fields("CID")
    strTrailer = MyRec.fields("Trailer")
    strRelease = MyRec.fields("Release_nbr")
    strVendor = MyRec.fields("Vendor")
     strSpecies = MyRec.fields("Species")
     strTonsReceived = MyRec.fields("Tons_Received")
     strDateReceived = MyRec.fields("Date_received")
    strComments = MyRec.fields("OTher_comments")
    strGenerator = MyRec.fields("Generator")
    strPO = MyRec.fields("PO")
%>
    
  
<tr bgcolor="#D5D5FF">
<td  align = left colspan="14" class="auto-style2"> <span class="style26"><strong>Receipt that already has this Release number: <%= strCID %></strong></span> 
	</td>

</tr>

<tr >

   <td  align = right class="auto-style6" >   <font face="Arial" size="2">Trailer:&nbsp;</font></td>
<td  align = left class="auto-style5"><font face="Arial" size="2"><%= strTrailer%></td>
    
    <td  align = right class="auto-style6" >   <font face="Arial" size="2">Release Nbr:&nbsp;</font></td>
<td  align = left class="auto-style5"><font face="Arial" size="2"><%= strRelease%></td>
    <td  align = right class="auto-style6" >   <font face="Arial" size="2">PO:&nbsp;</font></td>
<td  align = left class="auto-style5"><font face="Arial" size="2"><%= strPO %></td>
  <td  align = right class="auto-style6" >   <font face="Arial" size="2">Vendor:&nbsp;</font></td>
<td  align = left class="auto-style5"><font face="Arial" size="2"><%= strVendor%></td>
<td  align = right class="auto-style6" >   <font face="Arial" size="2">Generator:&nbsp;</font></td>
<td  align = left class="auto-style5"><font face="Arial" size="2"><%= strGenerator %></td>  
    <td  align = right class="auto-style6" >   <font face="Arial" size="2">Species:&nbsp;</font></td>
<td  align = left class="auto-style5"><font face="Arial" size="2"><%= strSpecies %></td> 
<td  align = right class="auto-style6" >   <font face="Arial" size="2">Comments:&nbsp;</font></td>
<td  align = left class="auto-style5"><font face="Arial" size="2"><%= strComments%></td> </tr>

 
      <tr>
<% MyRec.movenext
wend
else %>
<tr bgcolor="#D5D5FF">
<td  align = left colspan="14" class="auto-style4"> <span class="style26"><strong>No other Receipts have Release number <%= request.querystring("r") %></strong></span> 
</td>

</tr>
<tr bgcolor="white">
<td  align = left colspan="14">&nbsp;
</td>

</tr>

<% strsql = "Select tblOrder.* from tblOrder where Release = '" & request.querystring("r") & "'"
    Set MyConn = Server.CreateObject("ADODB.Recordset")
    MyConn.Open strSQL, Session("ConnectionString")
    If not MyConn.eof then %>
    
<tr bgcolor="#FFFF99">
<td  align = left colspan="14" class="auto-style1"><span class="style26"><strong>Information from Order file for  Release number <%= request.querystring("r") %></strong></span>
</td>

</tr>

<tr >

   <td  align = right class="style3" >   <font face="Arial" size="2">PO:&nbsp;</font></td>
<td  align = left class="style4"><font face="Arial" size="2"><%= MyConn.fields("PO") %></td>
    <td  align = right class="style3" >   <font face="Arial" size="2">Vendor:&nbsp;</font></td>
<td  align = left class="style4"><font face="Arial" size="2"><%= MyConn.fields("Vendor") %></td>  
    <td  align = right class="style3" >   <font face="Arial" size="2">Generator:&nbsp;</font></td>
<td  align = left class="style4"><font face="Arial" size="2"><%= MyConn.fields("Generator") %></td>
    <td  align = right class="style3" >   <font face="Arial" size="2">City&nbsp;</font></td>
<td  align = left class="style4"><font face="Arial" size="2"><%= MyConn.fields("City") %></td>
  <td  align = right class="style3" >   <font face="Arial" size="2">State&nbsp;</font></td>
<td  align = left class="style4"><font face="Arial" size="2"><%= MyConn.fields("State")%></td>
    <td  align = right class="style3" >   <font face="Arial" size="2">Import Month&nbsp;</font></td>
<td  align = left class="style4"><font face="Arial" size="2"><%= MyConn.fields("Import_month") %></td> 
<td  align = right class="style3" >   <font face="Arial" size="2">Species&nbsp;</font></td>
<td  align = left class="style4"><font face="Arial" size="2"><%= MyConn.fields("Species") %>&nbsp;</td> 
<td  align = right class="style3" >   <font face="Arial" size="2">Grade&nbsp;</font></td>
<td  align = left class="style4"><font face="Arial" size="2"><%= MyConn.fields("Grade") %>&nbsp;</td> 



</tr>
<tr bgcolor="white">
<td  align = left colspan="14">&nbsp;
</td>

</tr>

<tr bgcolor="#FFFF99">
<td  align = right colspan="7" class="auto-style1"><span class="style26"><strong>Do you wish to replace the information on the receipt with this order information?<br>
</strong>Note:&nbsp; Species, PO, Vendor, Generator, City and State will be replaced&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>
&nbsp;</td>
<td  align = left colspan="7" class="auto-style1"><span class="style26"><strong> <Input name="Update" type="submit" Value="YES" ></strong></span>
</td>

</tr>

<% else %>

<tr bgcolor="#FFFF99">
<td  align = left colspan="14" class="auto-style1">
</td>

</tr>
<tr bgcolor="#FFFF99">
<td  align = left colspan="14" class="auto-style2">  <span class="auto-style3">  <strong>This release number is not in the Order file</strong></span> 
</td>

</tr>



<%MyConn.close
end if


 end if 
MyRec.close %>
</table>

</form>
	
</body>

</html>
<!--#include file="Fiberfooter.inc"-->