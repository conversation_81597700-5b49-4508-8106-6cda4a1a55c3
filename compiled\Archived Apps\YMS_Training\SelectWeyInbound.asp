
<head>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="robots" content="index,nofollow">
<META NAME="Generator" CONTENT="Microsoft FrontPage 12.0">


<TITLE>Select Wey Trailer</TITLE>

<style type="text/css">
.auto-style1 {
	border-collapse: collapse;
	border: 1px solid #000000;
	background-color: #F2FBFF;
}
.auto-style2 {
	background-color: #F2FBFF;
}
</style>
</head>

<!--#include file="classes/asp_cls_DataAccessFIBER.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->

<!--#include file="classes/asp_cls_headerFIBER.asp"-->
<!--#include file="classes/asp_cls_SessionStringFIBER.asp"-->
 
<!--#include file="classes/asp_cls_ProcedureFiber.asp"-->

 <%    Dim objDAC, strSQL, MyRec, objMOC, rstFiber, str<PERSON>railer, strR, strID
	Dim gSpecies, gSap_Nbr, gVendor, gPO, gRelease, gOID, gGenerator, gCity, gState
       Dim objGeneral, strDate, MyConn
strDate = formatdatetime(Now(),2)
	
	
  set objGeneral = new ASP_CLS_General
  Call getData()   
if objGeneral.IsSubmit() Then

strR = request.form("Release")


strdate = formatdatetime(Now(),2)
strtdate = dateadd("d", -60, strdate)




strsql = "DELETE  FROM tblSAPOpenPO WHERE Status is null and Delivdate < '" & strtdate & "'"



			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close


	Response.redirect("EnterNFReceipt.asp?id=" & strR)
	


End if
%>
<SCRIPT LANGUAGE="JavaScript">
function formHandler(Form1){
document.forms["Form1"].submit();
}
</SCRIPT>

<body>
<form name="form1" action="SelectWeyInbound.asp" method="post" >

<p>&nbsp;</p>
<table cellpadding="0"  cellspacing="0" width="75%" id="AutoNumber2" align = center class="auto-style1">

       <TD align = center class="auto-style2">  <p>&nbsp;</p> <font face="Arial" size="3"><b>
		Select KDF Trailer to Inbound</b></font><br><br>
     <font face="Arial">
     	<font size="2"> &nbsp;</font><select name="Release">
 	<option value="" selected>-----Select-----</option>
      <%= objGeneral.OptionListAsString(rstFiber, "OID", "TS", strTrailer) %>
     </select><p>&nbsp;</p> 
<p align="center"><input type="submit" value="Continue" id=submit1 name=submit1><p align="center">
	   &nbsp;<p align="center">
		<b>NOTE:&nbsp;</b> If you are receiving a truck with <b>multiple PO-Line Items</b>, 
		and the Truck is noted as &quot;UNKNOWN&quot;, click <b><font color="#0000FF"><a href="ML_KDF_Edit.asp">HERE</a></font></b> 
		to change the trailer from &quot;UNKNOWN&quot; to the right trailer number for 
		each item on the truck.&nbsp; ALSO, if it is not checked as a Multi-Item 
		Load, be sure to enter an &quot;X&quot; under multi-load.<br>&nbsp;</TD>

       </tr>

</table>
</form>


<p align="center">
     &nbsp;</p>



<%

 Function GetData()
        set objMOC = new ASP_CLS_FIBER
        set rstFiber = objMOC.FiberWeyRelease()
         End Function  
%><!--#include file="Fiberfooter.inc"-->