font.hdrTxt
	{
	font-family: arial, tahoma, trebuchet ms, verdana;
	font-size: 13pt;
	font-weight: bold;
	color: 737373;
	text-decoration: none;
	}

.bodyTxt
	{
	font-family: arial, tahoma, trebuchet ms, verdana;
	font-size: 11px;
	color: 000000;
	text-decoration: none;
    text-align: justify;
	}

.QuoteTxt
	{
	font-family: arial, tahoma, trebuchet ms, verdana;
	font-size: 11px;
    line-height:13px;
	color: 000000;
	text-decoration: none;
    text-align: justify;
	}
.QuoteTxtHeader
	{
	font-family: arial, tahoma, trebuchet ms, verdana;
	font-size: 13px;
	color: 000000;
	text-decoration: none;
    text-align: justify;
	}

font.pgHdr {
	font-family: arial, tahoma, trebuchet ms, verdana;
	font-size: 21px;
	font-weight: 900;
	color: FFFFFF;
	text-decoration: none;
	FILTER: progid:DXImageTransform.Microsoft.dropShadow(Color=000000,offX=1,offY=1,positive=true);
	}

font.footer {
	font-family: arial, tahoma, trebuchet ms, verdana;
	font-size: 7pt;
	color: 000000;
	text-decoration: none;
	line-height: 1.4em;
	}
a.footer {
	font-family: arial, tahoma, trebuchet ms, verdana;
	font-size: 10px;
	color: 000000;
	text-decoration: none;
	line-height: 1em;
	}
a:hover.footer {
	font-family: arial, tahoma, trebuchet ms, verdana;
	font-size: 10px;
	color: 000000;
	text-decoration: none;
	line-height: 1em;
	}
a.footer2 {
	font-family: arial, tahoma, trebuchet ms, verdana;
	font-size: 7pt;
	color: 000000;
	text-decoration: none;
	line-height: 1.4em;
    font-weight:bold
	}
a:hover.footer2 {
	font-family: arial, tahoma, trebuchet ms, verdana;
	font-size: 7pt;
	color: 000000;
	text-decoration: underline;
	line-height: 1.4em;
    font-weight:bold
	}
font.topNav
	{
	font-family: arial, tahoma, trebuchet ms, verdana;
	font-size: 11px;
	color: FFFFFF;
	text-decoration: none;
	}

a.topNav
	{
	font-family: arial, tahoma, trebuchet ms, verdana;
	font-size: 11px;
	color: FFFFFF;
	font-weight: bold;
	text-decoration: none;
	}
a:hover.topNav
	{
	font-family: arial, tahoma, trebuchet ms, verdana;
	font-size: 11px;
	color: 4C4C4C;
	font-weight: bold;
	text-decoration: none;
	}

font.deptNav
	{
	font-family: arial, tahoma, trebuchet ms, verdana;
	font-size: 11px;
	color: 000000;
	text-decoration: none;
	}

a.deptNav
	{
	font-family: arial, tahoma, trebuchet ms, verdana;
	font-size: 11px;
	color: 000000;
	text-decoration: none;
	}
a:hover.deptNav
	{
	font-family: arial, tahoma, trebuchet ms, verdana;
	font-size: 11px;
	color: CC0000;
	text-decoration: none;
	}

.vnavdef {
	border-top: solid #FFFFFF 1px;
	border-left: solid #FFFFFF 1px;	
	border-right: solid #4C4C4C 1px;	
	border-bottom: solid #4C4C4C 1px;
	background-color: B8B2A7;
	font-family: arial, tahoma, trebuchet ms, verdana;
	font-size: 11px;
	color: white;
	font-weight: bold;
	cursor: hand; }

.vnavroll {
	border-top: solid #FFFFFF 1px;
	border-left: solid #FFFFFF 1px;	
	border-right: solid #4C4C4C 1px;	
	border-bottom: solid #4C4C4C 1px;
	background-color:004A80;
	font-family: arial, tahoma, trebuchet ms, verdana;
	font-size: 11px;
	color: ffffff;
	font-weight:bold;
	cursor: hand; }

.vnavdefselected {
	border-top: solid #FFFFFF 1px;
	border-left: solid #FFFFFF 1px;	
	border-right: solid #4C4C4C 1px;	
	border-bottom: solid #4C4C4C 1px;
	background-color: B8B2A7;
	font-family: arial, tahoma, trebuchet ms, verdana;
	font-size: 11px;
	color: red;
	font-weight: bold;
	cursor: hand; }

.vnavrollselected {
	border-top: solid #FFFFFF 1px;
	border-left: solid #FFFFFF 1px;	
	border-right: solid #4C4C4C 1px;	
	border-bottom: solid #4C4C4C 1px;
	background-color:004A80;
	font-family: arial, tahoma, trebuchet ms, verdana;
	font-size: 11px;
	color: red;
	font-weight:bold;
	cursor: hand; }


td.raised
	{
	border-top: solid #FFFFFF 1px;
	border-left: solid #FFFFFF 1px;	
	border-right: solid #4C4C4C 1px;	
	border-bottom: solid #4C4C4C 1px;
	background-color: #B8B2A7;
	color: FFFFFF;
	}

div.raised
	{
	border-top: solid #FFFFFF 1px;
	border-left: solid #FFFFFF 1px;	
	border-right: solid #4C4C4C 1px;	
	border-bottom: solid #4C4C4C 1px;
	background-color: #B8B2A7;
	color: 000000;
	}

.menulines{
	border:2px solid #B8B2A7;}

.menulines a{
	text-decoration:none;
	color:FFFFFF;}

font.pims
	{
	font-family: tahoma, trebuchet ms, verdana;
	font-size:7pt;
	color: 000000;
	text-decoration: none;
	}

a.pims
	{
	font-family: tahoma, trebuchet ms, verdana;
	font-size:7pt;
	color: 000000;
	text-decoration: none;
	line-height: 1.5em;
	}

a:hover.pims
	{
	font-family: tahoma, trebuchet ms, verdana;
	font-size:7pt;
	color: 000000;
	text-decoration: underline;
	line-height: 1.5em;
	}
    
.bodyTxtInverse
	{
	font-family: arial, tahoma, trebuchet ms, verdana;
	font-size: 11px;
	color: FFFFFF;
	text-decoration: none;
    text-align: justify
	}
a.bodyTxtInverse
	{
	font-family: arial, tahoma, trebuchet ms, verdana;
	font-size: 11px;
	color: FFFFFF;
	text-decoration: none;
    font-weight: bold
	}
a:hover.bodyTxtInverse
	{
	font-family: arial, tahoma, trebuchet ms, verdana;
	font-size: 11px;
	color: FFFFFF;
	text-decoration: underline;
    font-weight: bold
	}

