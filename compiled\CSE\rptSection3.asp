<html>
<!--#include file="classes/asp_cls_SessionString.asp"-->


<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 6.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>This assessment is intended to document a summary of&nbsp; hazards and 
control measures required to permit safe entry</title>
</head>


<% dim strairTestHere, strATTech,  strEntrantSOS, strAccessEgress, strAEAddInfo, strIssueWithAE, strSelfRescue, strSelfRescueControls
dim strNonEntryRescue, strNoNonEntry, strTeamR<PERSON>e, strSCBA<PERSON>nPortal, strharnessAnd<PERSON>etrieval, strMechanicalDevice, strRescueEquip1
dim strRescueEquip2, strexhazTraffic, strexhazFallFromAbove, strexhazFallBelow, strexhazHoists, strstrhoistOobstructControl, strexhazCatwalk
dim strexhazNearbyProcessIssue, strProcessControl, strPSMArea, strOtherExhaz, strAttendantSOS
Dim strAtt_call,  strExtList, strExt, strNextel, strOther_ae, strBodyHarnesswithLine, strid, strpid, strNameLocPortal



strid = Request.querystring("id")
strpid = Request.querystring("pid")

strsql = "SELECT tblHA.*, tblSOP.LOCATION, tblSOP.SDescription FROM tblHA INNER JOIN tblSOP ON tblHA.SpaceID = tblSOP.SOP_NO "_
  &" where SpaceID = '" & strid & "'"
Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
  If not MyConn.eof then
 strSpaceid = strid
 strLocation = MyConn.fields("Location")
 strSdescription = MyConn.fields("SDescription")
 end if 
 MyConn.close 
  
 strsql = "SELECT tblPortals.* from tblPortals where ID = " & strpid & ""

Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
  If not MyConn.eof then 

strairTestHere = MyConn.fields("airTestHere")
strATTech = MyConn.fields("ATTech")
strAttendantSOS = MyConn.fields("AttendantSOS")
strEntrantSOS = MyConn.fields("EntrantSOS")
strAccessEgress = MyConn.fields("AccessEgress")
strAEAddInfo = MyConn.fields("AEAddInfo")
strIssueWithAE = MyConn.fields("IssueWithAE")
strSelfRescue = MyConn.fields("SelfRescue")
strSelfRescueControls = MyConn.fields("SelfRescueControls")
strNonEntryRescue = MyConn.fields("NonEntryRescue")
strNoNonEntry = MyConn.fields("NoNonEntry")
strTeamRescue = MyConn.fields("TeamRescue")
strSCBAInPortal = MyConn.fields("SCBAInPortal")

strharnessAndRetrieval = MyConn.fields("harnessAndRetrieval")
strMechanicalDevice = MyConn.fields("MechanicalDevice")
strRescueEquip1 = MyConn.fields("RescueEquip1")
strRescueEquip2 = MyConn.fields("RescueEquip2")
strexhazTraffic = MyConn.fields("exhazTraffic")
strexhazFallFromAbove = MyConn.fields("exhazFallFromAbove")
strfallAboveControl = MyConn.fields("fallAboveControl")
strexhazFallBelow = MyConn.fields("exhazFallBelow")
strexhazHoists = MyConn.fields("exhazHoists")
strhoistOobstructControl = MyConn.fields("hoistOObstructControl")
strexhazCatwalk = MyConn.fields("exhazCatwalk")
strcatwalkControls = MyConn.fields("catwalkControls")
strexhazNearbyProcessIssue = MyConn.fields("exhazNearbyProcessIssue")
strProcessControl = MyConn.fields("ProcessControl")
strPSMArea = MyConn.fields("PSMArea")
strOtherExhaz = MyConn.fields("OtherExhaz")
strAtt_call = Myconn.fields("Att_call")
strExtList = Myconn.fields("ExtList")
strExt = Myconn.fields("Ext")
strNextel = Myconn.fields("Nextel")
strOther_ae = Myconn.fields("Other_ae")
strBodyHarnesswithLine = Myconn.fields("BodyHarnesswithLine")
strNameLocPortal = Myconn.fields("NameLocPortal")
end if

	set objGeneral = new ASP_CLS_General
  		 if objGeneral.IsSubmit = True Then
  		 call savedata()
 	
  		
  		 End if
  		
%>
<body >


<table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" height="100">
  <tr>
    <td width="100%" height="16" bgcolor="#FFFFFF">
	<font face="Arial" size="2">Entry Portal Assessment</font></b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <b>
	<font face="Arial">Section 3:&nbsp; Entry Portal</font></b></td>
  </tr>
  </table>
<html>
<body>

  <table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" id="table3">
  <tr>
    <td width="4%" height="19" bgcolor="#FFFFDD">
	<p align="center"><font size="2" face="Arial">
	&nbsp;Space ID</font></td>
    <td width="24%" height="19" bgcolor="#FFFFDD" align="center"><font face = arial size = 2>Space Name</td>
    <td width="27%" height="19" bgcolor="#FFFFDD" align="center"><font face="Arial" size="2">
	Location</font></td>

    <td width="43%" bgcolor="#FFFFDD" height="19">
	<p align="center"><font face="Arial" size="2">Name and/or location of Entry 
	Portal:</font></td>
   
  </tr>
  <tr>
    <td width="4%" align = center height="19" bgcolor="#FFFFFF" > <font face = arial size = 2><%= strSpaceID %></td>
    <td width="24%" height="40" bgcolor="#FFFFFF"  ><font face="Arial" size="2">
	<%= strSdescription %></font></td>
	<td bgcolor="#FFFFFF"><font face="Arial" size="2"><%= strLocation %></td>

    <td width="43%" bgcolor="#FFFFFF" height="19"><font face="Arial" size="2">
	<p align="center">	<%= strNameLocPortal %></td>
  
  </tr>
</table>


  <table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#FFFFDD" id="table5" height="25" >
 

  <tr>
    <td bgcolor="#FFFFDD" height="30" ><font face="Arial"><b>Air test</b></td>
	<td width="234">
	<p align="center">&nbsp;</td>
   </tr>
</table>
 <table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#FFFFFF" id="table6">
 
 
  <tr>
    <td ><font face="Arial"><font size="2">&nbsp; Does an air test need to be done here?.</font>&nbsp;&nbsp;&nbsp;

	 <% if strairTestHere = -1 then%> <font size="2">Yes  <% else %>No <% end if %>

	
	</font>&nbsp;&nbsp;<font size="2">&nbsp; If Yes, select technique: 
&nbsp; 
	 <%= strATTech %><br></font></td>
    
  </tr></table>
 	<table border="0" cellpadding="0" style="border-collapse: collapse" width="100%" bgcolor="#FFFFFF" id="table7">

    <tr>
    <td width="100%" colspan="5" bgcolor="#FFFFDD" height="30"><b>
	<font face="Arial">Access and Egress Information</font></b></td>
  </tr>
  <tr>
   
    <td  ><font face="Arial" size="2">&nbsp;&nbsp;&nbsp;&nbsp; Location of 
	Access &amp; Egress Port&nbsp;&nbsp; 	 <%=  strAccessEgress  %></font></td>  

   
  </tr>
  <tr>

    <td  ><font size="2" face="Arial">&nbsp;&nbsp; &nbsp; 
	List information regarding the entry point and route to take for egress<b> 
	if not obvious</b>:&nbsp; (list information relevant for escape)</font><br>
 
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 	<%= straeaddinfo %></td></font>   

  </tr>
    <tr>

    <td  ><font size="2" face="Arial">&nbsp;&nbsp;&nbsp;&nbsp; </font>
	<span style="font-size: 10.0pt; font-family: Arial">Is there any size, shape 
	or internal or external configuration issues that make access or egress 
	difficult?&nbsp;&nbsp; </span><font face="Arial">
	<font size="2">
	<% if strIssueWithAE = -1 then%>Yes<% end if %>
	 <% if strIssueWithAE = 0 then%> No <% end if %>
	
	&nbsp;</font>&nbsp;<font size="2"> </font><br>&nbsp;&nbsp;&nbsp;&nbsp; <font size="2">If yes, identify 
	issues and controls (if possible):&nbsp; </font></font><%= strOther_ae %></td>
 
  
    
  </tr>
</table>
  <table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#D9E1F9" id="table8">
 

  <tr>
    <td width="100%" bgcolor="#FFFFDD" height="30"><b><font face="Arial">Rescue&nbsp;&nbsp; 
	Identify the types of rescue possible for this space</font></b></td>
  </tr>
  </table>
    <table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#FFFFFF" id="table9">
  <tr>
    <td height="22">&nbsp;</td>
    <td height="40"><font face="Arial" size="2">&nbsp;</font><input type="checkbox" name="SelfRescue"  <% if strSelfRescue = -1 then %> checked <% end if %> value="ON">
	<font face="Arial" size="2">Self Rescue: an unaided emergency exit of a space 
	due to own decision or by command.&nbsp;&nbsp; 
	If obstacles, list controls::&nbsp; </font>
	<%= strselfrescuecontrols %></td>
    <td height="22">&nbsp;</td>
   
  </tr>
  <tr>
    <td>&nbsp;</td>
    <td height="40" ><font face="Arial" size="2">&nbsp;</font><input type="checkbox" name="NonEntryRescue"  <% if strNonEntryRescue = -1 then %> checked <% end if %>value="ON">
	<font face="Arial" size="2">Non-entry Rescue: aided assistance in exiting 
	the confined space not requiring entry&nbsp; (body or wrist harness with a 
	retrieval line attached unless either would endanger the entrant or not 
	assist in a rescue).&nbsp; If not possible, list reason:&nbsp; </font>
	<%= strnononentry %><br><br>
    &nbsp;</font><input type="checkbox" name="TeamRescue"  <% if strTeamRescue = -1 then %> checked <% end if %>value="ON">
	<font face="Arial" size="2">Team rescue -(</font><span style="font-size: 10.0pt; font-family: Arial">aided 
	assistance in exiting the confined space requiring entry by the rescuer(s)</span><font face="Arial" size="2">&nbsp;
	</font><br><br></td>
   
  </tr>
    <tr>
    <td>&nbsp;</td>
    <td height="40" ><font face="Arial" size="2">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </font>&nbsp;<font face="Arial" size="2">Could a person with an Scott 
	Air Pack fit through this 
	portal?&nbsp;&nbsp;&nbsp; </font><font face="Arial">
 <% if strSCBAInPortal= -1 then%> Yes <% end if %>
	 <% if strSCBAInPortal = 0 then%> No<% end if %>
	
	</font><br><font face="Arial" size="2"><br>
    &nbsp;Identify <b>Required</b> Emergency Equipment </font></font><br>&nbsp;&nbsp;
	<input type="checkbox" name="harnessAndRetrieval"  <% if strharnessAndRetrieval = -1 then %> checked <% end if %> value="ON">
	<font face="Arial" size="2">Body or wrist harness&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	</font>
	<input type="checkbox" name="BodyHarnessWithLine"  <% if strBodyHarnessWithLine = -1 then %> checked <% end if %> value="ON">
	<font face="Arial" size="2">Body or wrist harness with retrieval line&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	</font><input type="checkbox" name="MechanicalDevice" <% if strMechanicalDevice = -1 then %> checked <% end if %> value="ON">
	<font face="Arial" size="2">Mechanical device/tripod&nbsp;&nbsp; </font>
	<br>
	&nbsp;&nbsp;
	<font face="Arial" size="2">Other (list)&nbsp; </font>
	<%= strrescueequip1 %>&nbsp;&nbsp;&nbsp;&nbsp;<font face="Arial" size="2">Other (list)&nbsp; </font>
	<%= strrescueequip2 %><br><br></td>
   
  </tr></table><table width = 100% id="table10"><Tr>

    <td width="100%" bgcolor="#FFFFDD" height="30">
	<b><font face="Arial">External Hazards:&nbsp;&nbsp;&nbsp;&nbsp; Identify all 
	situations that could effect the safety on the outside of this space</font></b></td>
  </tr></table>

<table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#FFFFFF" id="table11">
 		<tr><td height="23"><br></td>

	<td height="23">&nbsp;	<font face="Arial" size="2"><b>Required Control</b></font><br></td></tr>
	<tr><td height="23">&nbsp; 
		<input type="checkbox" name="exhazTraffic" <% if strexhazTraffic = -1 then %> checked <% end if %> value="ON">
			<font face="Arial" size="2">Entry point near traffic area</font><br></td>

	<td height="23">&nbsp;	<font face="Arial" size="2">&nbsp;Secure the area: 
	barriers/signs/tape</font><br></td></tr>
	
	<tr><td>
	<font face="Arial" size="2">&nbsp; </font>
		<input type="checkbox" name="exhazFallFromAbove" <% if strexhazFallFromAbove = -1 then %> checked <% end if %> value="ON">
	<font face="Arial" size="2">Falling hazard from above</font><br></td>
	<td>
	<font face="Arial" size="2">&nbsp;&nbsp; List: <b>&nbsp;</b></font><font face="Arial"><%= strfallabovecontrol%></font><br></td></tr>
	<tr><td>	
	<font face="Arial" size="2">&nbsp; </font>
		<input type="checkbox" name="exhazFallBelow" <% if strexhazFallBelow = -1 then %> checked <% end if %> value="ON">
	<font face="Arial" size="2">Falling hazard below</font><br></td>
	<td>	
	<font face="Arial" size="2">&nbsp; </font>&nbsp;<font face="Arial" size="2">Secure 
	the area: barriers/signs/tape</font><br></td></tr>
	<tr><td>
	<font face="Arial" size="2">&nbsp; </font>
		<input type="checkbox" name="exhazHoists" <% if strexhazHoists = -1 then %> checked <% end if %> value="ON">
	<font face="Arial" size="2">Hoists/overhead obstructions</font><br></td>
	<td>
	<font face="Arial" size="2">&nbsp;&nbsp; List: <b>&nbsp;</b></font><font face="Arial"><%= strhoistOobstructControl %></font><br></td></tr>
		<tr><td>
	<font face="Arial" size="2">&nbsp; </font>
		<input type="checkbox" name="exhazCatwalk"  <% if strexhazCatwalk = -1 then %> checked <% end if %> value="ON">
	<font face="Arial" size="2">Reach entry point by catwalks/scaffolding</font><br></td>
	<td>
	<font face="Arial" size="2">&nbsp;&nbsp; List:<b>&nbsp;</b></font>
	<font face="Arial">
<%= strcatwalkcontrols %></font><br></td></tr>
		
			<tr><td>
	<font face="Arial" size="2">&nbsp; </font>
		&nbsp;<font face="Arial" size="2">Other (list)&nbsp; </font><font face="Arial">
		<%= strotherexhaz %></font><br></td>
	<td>
	<font face="Arial" size="2">&nbsp; </font>
		&nbsp;<font face="Arial" size="2">List: <b>&nbsp;</b></font><font face="Arial"><%= strExtList %></font><br></td></tr>

			</table>
</table>


    </body>

</html>

