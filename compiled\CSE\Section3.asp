<html>
<!--#include file="classes/asp_cls_SessionString.asp"-->
<!--#include file="classes/asp_cls_header.asp"-->
<!--#include file="classes/asp_cls_SessionUser.asp"-->
<!--#include file="classes/asp_cls_DataAccess.asp"-->
<!--#include file="classes/asp_cls_General.asp"-->
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<title>This assessment is intended to document a summary of&nbsp; hazards and 
control measures required to permit safe entry</title>
</head>


<% dim strairTestHere, strATTech,   strAccessEgress, strAEAddInfo, strIssueWithAE, strS<PERSON><PERSON><PERSON>e, strSelfRescueControls
dim strNonEntryRescue, str<PERSON>o<PERSON>onEntry, strTeamRescue, strSCBAInPortal, strharnessAndRetrieval, strMechanicalDevice, strRescueEquip1
dim strRescueEquip2, strexhazTraffic, strexhazFallFromAbove, strexhazFallBelow, strexhazHoists, strstrhoistOobstructControl, strexhazCatwalk
dim  strOtherExhaz, strApprover1, strApprover2, strE_name
Dim strExtList,  strOther_ae, strBodyHarnesswithLine, strid, strpid, strNameLocPortal, strHAStatus

strE_name = Session("Ename")

strid = Request.querystring("id")
strpid = Request.querystring("pid")

strsql = "SELECT tblHA.*, tblSOP.LOCATION, tblSOP.SDescription FROM tblHA INNER JOIN tblSOP ON tblHA.SpaceID = tblSOP.SOP_NO "_
  &" where SpaceID = '" & strid & "'"
Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
  If not MyConn.eof then
 strSpaceid = strid
 strLocation = MyConn.fields("Location")
 strSdescription = MyConn.fields("SDescription")
 strApprover1 = MyConn.fields("HA_Approver_1")
 strApprover2 = MyConn.fields("HA_Approver_2")
 strHAStatus = Myconn.fields("HA_Status")
 end if 
 MyConn.close 
  
 strsql = "SELECT tblPortals.* from tblPortals where ID = " & strpid & ""

Set MyConn = Server.CreateObject("ADODB.Recordset") 
   		MyConn.Open strSQL, Session("ConnectionString")
  If not MyConn.eof then 

strairTestHere = MyConn.fields("airTestHere")
strATTech = MyConn.fields("ATTech")

strAccessEgress = MyConn.fields("AccessEgress")
strAEAddInfo = MyConn.fields("AEAddInfo")
strIssueWithAE = MyConn.fields("IssueWithAE")
strSelfRescue = MyConn.fields("SelfRescue")
strSelfRescueControls = MyConn.fields("SelfRescueControls")
strNonEntryRescue = MyConn.fields("NonEntryRescue")
strNoNonEntry = MyConn.fields("NoNonEntry")
strTeamRescue = MyConn.fields("TeamRescue")
strSCBAInPortal = MyConn.fields("SCBAInPortal")

strharnessAndRetrieval = MyConn.fields("harnessAndRetrieval")
strMechanicalDevice = MyConn.fields("MechanicalDevice")
strRescueEquip1 = MyConn.fields("RescueEquip1")
strRescueEquip2 = MyConn.fields("RescueEquip2")
strexhazTraffic = MyConn.fields("exhazTraffic")
strexhazFallFromAbove = MyConn.fields("exhazFallFromAbove")
strfallAboveControl = MyConn.fields("fallAboveControl")
strexhazFallBelow = MyConn.fields("exhazFallBelow")
strexhazHoists = MyConn.fields("exhazHoists")
strhoistOobstructControl = MyConn.fields("hoistOObstructControl")
strexhazCatwalk = MyConn.fields("exhazCatwalk")
strcatwalkControls = MyConn.fields("catwalkControls")


strOtherExhaz = MyConn.fields("OtherExhaz")

strExtList = Myconn.fields("ExtList")

strOther_ae = Myconn.fields("Other_ae")
strBodyHarnesswithLine = Myconn.fields("BodyHarnesswithLine")
strNameLocPortal = Myconn.fields("NameLocPortal")
end if

	set objGeneral = new ASP_CLS_General
  		 if objGeneral.IsSubmit = True Then
  		 call savedata()
 	
  		
  		 End if
  		
%>
<body bgcolor="#FFFFFF">
<form name="form2" action="Section3.asp?pid=<%= strpid%>&id=<%= strid%>"  method="post" ID="Form2"  >
<input type = hidden name = Approver1 value = <%= strApprover1%>>
<input type = hidden name = Approver2 value = <%= strApprover2%>>
<input type = hidden name = HAstatus value = <%= strHAStatus%>>
<table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" height="100">
  <tr>
    <td width="100%" colspan="4" height="16" bgcolor="#FFFF00"><b>
	<font face="Arial" size="2">Entry Portal Assessment</font></b></td>
  </tr>
  <tr>
    <td  height="19" bgcolor="#FFFFDD">


  <table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" id="table1">
  <tr>
    <td width="4%" height="19" bgcolor="#FFFFDD">
	<p align="center"><font size="2" face="Arial">
	&nbsp;Space ID</font></td>
    <td width="24%" height="19" bgcolor="#FFFFDD" align="center"><font face = arial size = 2>Space Name</td>
    <td width="27%" height="19" bgcolor="#FFFFDD" align="center"><font face="Arial" size="2">
	Location</font></td>

    <td width="43%" bgcolor="#FFFFDD" height="19">
	<p align="center"><font face="Arial" size="2">Name and/or location of Entry 
	Portal:</font></td>
   
  </tr>
  <tr>
    <td width="4%" align = center height="19" bgcolor="#FFFFFF" > <font face = arial size = 2><%= strSpaceID %></td>
    <td width="24%" height="40" bgcolor="#FFFFFF"  ><font face="Arial" size="2">
	<%= strSdescription %></font></td>
	<td bgcolor="#FFFFFF"><font face="Arial" size="2"><%= strLocation %></td>

    <td width="43%" bgcolor="#FFFFDD" height="19"><font face="Arial" size="2">
	<p align="center">
	<input type="text" name="NameLocPortal" size="52" value="<%= strNameLocPortal %>" maxlength="100"></td>
  
  </tr>
</table>


<table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#D9E1F9">
  <tr>
    <td width="100%" bgcolor="#FF0000" height="40"><b>
	<font face="Arial" color="#FFFFFF">Section 3:&nbsp; Entry Portal<br>&nbsp;</font></b></td>
  </tr></table>

  <table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#FFFFF2" height="28" >
 

  <tr>
    <td bgcolor="#FFFFDD" height="28" ><font face="Arial"><b>Air test</b></td>
	<td width="234">
	<p align="center"><INPUT TYPE="submit" value="Submit" style="float: right"><font face="Arial"><a href="rptSection3.asp?pid=<%= strpid%>&id=<%= strid%>">Printer Friendly</a></font></td>
   </tr>
</table>
 <table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#EEF2FD">
 
 
  <tr>
    <td  ><br><font face="Arial"><font size="2">&nbsp; Does an air test need to be done here?.</font>&nbsp;&nbsp;&nbsp;
	<select size="1" name="airTestHere">
	<option >Select Value</option>
	<option <% if strairTestHere = -1 then%> selected <% end if %>>Yes</option>
	<option <% if strairTestHere = 0 then%> selected <% end if %>>No</option>
	</select>
	
	&nbsp;&nbsp;<font size="2">&nbsp; If Yes, select technique: </font> </font>
	<font face="Arial" size="2">&nbsp; </font><font face="Arial">
		<select size="1" name="ATTech">
		<option value = "" >Select Value</option>
	<option <% if strATTech = "Vertical" then %>selected <% end if %>>Vertical</option>
	<option <% if strATTech = "Horizontal" then %>selected <% end if %>>Horizontal</option>
	</select></font><br></font></td>
    


  </tr></table>
 	<table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#EEF2FD" >

    <tr>
    <td width="100%" colspan="5" bgcolor="#FFFFDD" height="33"><b>
	<font face="Arial">Access and Egress Information</font></b></td>
  </tr>
  <tr>
   
    <td height="40"  ><font face="Arial" size="2">&nbsp;&nbsp;&nbsp;&nbsp; Location of 
	Access &amp; Egress Port&nbsp;&nbsp; </font><font face="Arial">
		<select size="1" name="AccessEgress">
	<option value = "" >Select Value</option>
	<option <% if strAccessEgress = "Top entry - open vat- retrieval system required" then %>selected <% end if %>>Top entry - open vat- retrieval system required</option>
	<option <% if strAccessEgress = "Top entry - manhole - retrieval system required" then %>selected <% end if %>>Top entry - manhole - retrieval system required</option>
	<option <% if strAccessEgress = "Top entry - less than 5 feet" then %>selected <% end if %>>Top entry - less than 5 feet</option>
	<option <% if strAccessEgress = "Side entry - ground to waist high" then %>selected <% end if %>>Side entry - ground to waist high</option>
	<option <% if strAccessEgress = "Side entry via ladder/platform" then %>selected <% end if %>>Side entry via ladder/platform</option>
	<option <% if strAccessEgress = "Side entry with internal ladder" then %>selected <% end if %>>Side entry with internal ladder</option>
	<option <% if strAccessEgress = "Bottom entry - ground to waist" then %>selected <% end if %>>Bottom entry - ground to waist</option>
	<option <% if strAccessEgress = "Bottm entry via platform" then %>selected <% end if %>>Bottm entry via platform</option>
	</select></font></td>
   

   
  </tr>
  <tr>

    <td height="40" ><font size="2" face="Arial">&nbsp;&nbsp; &nbsp; 
	List information regarding the entry point and route to take for egress<b> 
	if not obvious</b>:&nbsp; (list information relevant for escape)</font></td>
 
  
    
  </tr>
  <tr>
   
    <td height="35" ><font size="2" face="Arial">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	<input type="text" name="AEAddInfo" size="100" value="<%= straeaddinfo %>" maxlength="100"></td>
  


  </tr>
    <tr>

    <td height="50" ><font size="2" face="Arial">&nbsp;&nbsp;&nbsp;&nbsp; </font>
	<span style="font-size: 10.0pt; font-family: Arial">Is there any size, shape 
	or internal or external configuration issues that make access or egress 
	difficult?&nbsp;&nbsp; </span><font face="Arial">
	
<select size="1" name="IssueWithAE">
	<option >Select Value</option>
	<option <% if strIssuewithAE  = "-1" then%> selected <% end if %>>Yes</option>
	<option <% if strIssuewithAE = "0" then%> selected <% end if %>>No</option>
	</select>	
<br>
	
<br>&nbsp;&nbsp;&nbsp;&nbsp; <font size="2">If yes, identify 
	issues and controls </font> <font size="1">(if possible)</font><font size="2">:&nbsp; </font></font>
	<input type="text" name="Other_ae" size="100" value="<%= strOther_ae %>" maxlength="200" style="width: 907px"></td>
 

    
  </tr>
  <tr>
   
    <td height="22" ><font size="2" face="Arial">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </font>
	</td>
  
    <td >&nbsp;</td>

  <td colspan = 2 ><font size="2" face="Arial">&nbsp;</font></td>

  </tr></table>
  <table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#D9E1F9">
 

  <tr>
    <td width="100%" bgcolor="#FFFFDD" height="40"><b><font face="Arial">Rescue&nbsp;&nbsp; 
	Identify the types of rescue possible for this space</font></b></td>
  </tr>
  </table>
    <table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#EEF2FD">
  <tr>
    <td height="22">&nbsp;</td>
    <td height="40"><font face="Arial" size="2">&nbsp;</font><input type="checkbox" name="SelfRescue"  <% if strSelfRescue = -1 then %> checked <% end if %> value="ON">
	<font face="Arial" size="2">Self Rescue: an unaided emergency exit of a space 
	due to own decision or by command.&nbsp;&nbsp; 
	If obstacles, list controls::&nbsp; </font>
	<input type="text" name="SelfRescueControls" size="45" value="<%= strselfrescuecontrols %>" maxlength="50"></td>
    <td height="22">&nbsp;</td>
   
  </tr>
  <tr>
    <td>&nbsp;</td>
    <td height="40" ><font face="Arial" size="2">&nbsp;</font><input type="checkbox" name="NonEntryRescue"  <% if strNonEntryRescue = -1 then %> checked <% end if %>value="ON">
	<font face="Arial" size="2">Non-entry Rescue: aided assistance in exiting 
	the confined space not requiring entry&nbsp; (body or wrist harness with a 
	retrieval line attached unless either would endanger the entrant or not 
	assist in a rescue).&nbsp; If not possible, list reason:&nbsp; </font>
	<input type="text" name="NoNonEntry" size="75" value="<%= strnononentry %>" maxlength="100"><br><br>
    &nbsp;</font><input type="checkbox" name="TeamRescue"  <% if strTeamRescue = -1 then %> checked <% end if %>value="ON">
	<font face="Arial" size="2">Team rescue -(</font><span style="font-size: 10.0pt; font-family: Arial">aided 
	assistance in exiting the confined space requiring entry by the rescuer(s)</span><font face="Arial" size="2">&nbsp;
	</font><br><br></td>
   
  </tr>
    <tr>
    <td>&nbsp;</td>
    <td height="30" ><font face="Arial" size="2">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </font>&nbsp;<font face="Arial" size="2">Could a person with an Scott 
	Air Pack fit through this 
	portal?&nbsp;&nbsp;&nbsp; </font><font face="Arial">
	<select size="1" name="SCBAInPortal">
	<option >Select Value</option>
	<option <% if strSCBAInPortal= -1 then%> selected <% end if %>>Yes</option>
	<option <% if strSCBAInPortal = 0 then%> selected <% end if %>>No</option>
	</select>
	
	
	</font><br><font face="Arial" size="2"><br>
    &nbsp;Identify <b>Required</b> Emergency Equipment </font></font><br>&nbsp;&nbsp;
	<input type="checkbox" name="harnessAndRetrieval"  <% if strharnessAndRetrieval = -1 then %> checked <% end if %> value="ON">
	<font face="Arial" size="2">Body or wrist harness&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	</font>
	<input type="checkbox" name="BodyHarnessWithLine"  <% if strBodyHarnessWithLine = -1 then %> checked <% end if %> value="ON">
	<font face="Arial" size="2">Body or wrist harness with retrieval line&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	</font><input type="checkbox" name="MechanicalDevice" <% if strMechanicalDevice = -1 then %> checked <% end if %> value="ON">
	<font face="Arial" size="2">Mechanical device/tripod&nbsp;&nbsp; </font>
	<br>
	&nbsp;&nbsp;
	<font face="Arial" size="2">Other (list)&nbsp; </font>
	<input type="text" name="RescueEquip1" size="40" value="<%= strrescueequip1 %>" maxlength="100">&nbsp;&nbsp;&nbsp;&nbsp;<font face="Arial" size="2">Other (list)&nbsp; </font>
	<input type="text" name="RescueEquip2" size="40" value="<%= strrescueequip2 %>" maxlength="100"><br><br></td>
   
  </tr></table><table width = 100%><Tr>

    <td width="100%" colspan="14" bgcolor="#FFFFDD" height="40">
	<b><font face="Arial">External Hazards:&nbsp;&nbsp;&nbsp;&nbsp; Identify all 
	situations that could effect the safety on the outside of this space</font></b></td>
  </tr></table>

<table border="0" cellpadding="0" style="border-collapse: collapse" bordercolor="#111111" width="100%" bgcolor="#EEF2FD">
 		<tr><td height="23"><br></td>

	<td height="23">&nbsp;	<font face="Arial" size="2"><b>Required Control</b></font><br></td></tr>
	<tr><td height="23">&nbsp; 
		<input type="checkbox" name="exhazTraffic" <% if strexhazTraffic = -1 then %> checked <% end if %> value="ON">
			<font face="Arial" size="2">Entry point near traffic area</font><br></td>

	<td height="23">&nbsp;	<font face="Arial" size="2">&nbsp;Secure the area: 
	barriers/signs/tape</font><br></td></tr>
	
	<tr><td>
	<font face="Arial" size="2">&nbsp; </font>
		<input type="checkbox" name="exhazFallFromAbove" <% if strexhazFallFromAbove = -1 then %> checked <% end if %> value="ON">
	<font face="Arial" size="2">Falling hazard from above</font><br></td>
	<td>
	<font face="Arial" size="2">&nbsp;&nbsp; List: <b>&nbsp;</b></font><font face="Arial"><input type="text" name="fallAboveControl" size="50" value="<%= strfallabovecontrol%>" maxlength="50"></font><br></td></tr>
	<tr><td>	
	<font face="Arial" size="2">&nbsp; </font>
		<input type="checkbox" name="exhazFallBelow" <% if strexhazFallBelow = -1 then %> checked <% end if %> value="ON">
	<font face="Arial" size="2">Falling hazard below</font><br></td>
	<td>	
	<font face="Arial" size="2">&nbsp; </font>&nbsp;<font face="Arial" size="2">Secure 
	the area: barriers/signs/tape</font><br></td></tr>
	<tr><td>
	<font face="Arial" size="2">&nbsp; </font>
		<input type="checkbox" name="exhazHoists" <% if strexhazHoists = -1 then %> checked <% end if %> value="ON">
	<font face="Arial" size="2">Hoists/overhead obstructions</font><br></td>
	<td>
	<font face="Arial" size="2">&nbsp;&nbsp; List: <b>&nbsp;</b></font><font face="Arial"><input type="text" name="hoistOobstructControl" size="54" value="<%= strhoistOobstructControl %>" maxlength="100"></font><br></td></tr>
		<tr><td>
	<font face="Arial" size="2">&nbsp; </font>
		<input type="checkbox" name="exhazCatwalk"  <% if strexhazCatwalk = -1 then %> checked <% end if %> value="ON">
	<font face="Arial" size="2">Reach entry point by catwalks/scaffolding</font><br></td>
	<td>
	<font face="Arial" size="2">&nbsp;&nbsp; List:<b>&nbsp;</b></font>
	<font face="Arial">
	<input type="text" name="catwalkControls" size="54" value="<%= strcatwalkcontrols %>" maxlength="100"></font><br></td></tr>
		
			<tr><td>
	<font face="Arial" size="2">&nbsp; </font>
		&nbsp;<font face="Arial" size="2">Other (list)&nbsp; </font><font face="Arial">
		<input type="text" name="OtherExhaz" size="45" value="<%= strotherexhaz %>" maxlength="255"></font><br></td>
	<td>
	<font face="Arial" size="2">&nbsp; </font>
		&nbsp;<font face="Arial" size="2">List: <b>&nbsp;</b></font><font face="Arial"><input type="text" name="ExtList" size="54" value="<%= strExtList %>" maxlength="150"></font><br></td></tr>
		<tr><td>
	<font face="Arial" size="2">&nbsp; </font>
		&nbsp;<br></td>
	<td height="40">
	<font face="Arial" size="2">&nbsp; </font>
		&nbsp;<INPUT TYPE="submit" value="Submit" style="float: right"><br></td></tr>
			</table>
    </body>

</html>


</table>
<p></p>
</form>

<%   Function SaveData()





if isnull(Request.form("AEAddInfo")) then
strAEAddInfo = ""
else
strAEAddInfo = Replace(Request.form("AEAddInfo"), "'", "''") 
end if 


if isnull(Request.form("Other_ae")) then
strOther_ae = ""
else
strOther_ae = Replace(Request.form("Other_ae"), "'", "''") 
end if 


if isnull(Request.form("SelfRescueControls")) then
strSelfRescueControls = ""
else
strSelfRescueControls = Replace(Request.form("SelfRescueControls"), "'", "''") 
end if 


if isnull(Request.form("NoNonEntry")) then
strNoNonEntry = ""
else
strNoNonEntry = Replace(Request.form("NoNonEntry"), "'", "''") 
end if 


if isnull(Request.form("RescueEquip1")) then
strRescueEquip1 = ""
else
strRescueEquip1 = Replace(Request.form("RescueEquip1"), "'", "''") 
end if 


if isnull(Request.form("RescueEquip2")) then
strRescueEquip2 = ""
else
strRescueEquip2 = Replace(Request.form("RescueEquip2"), "'", "''") 
end if 


if isnull(Request.form("fallAboveControl")) then
strfallAboveControl = ""
else
strfallAboveControl = Replace(Request.form("fallAboveControl"), "'", "''") 
end if 


if isnull(Request.form("hoistOobstructControl")) then
strhoistOobstructControl = ""
else
strhoistOobstructControl = Replace(Request.form("hoistOobstructControl"), "'", "''") 
end if 



if isnull(Request.form("catwalkControls")) then
strcatwalkControls = ""
else
strcatwalkControls = Replace(Request.form("catwalkControls"), "'", "''") 
end if 






if isnull(Request.form("OtherExhaz")) then
strOtherExhaz = ""
else
strOtherExhaz = Replace(Request.form("OtherExhaz"), "'", "''") 
end if 


if isnull(Request.form("ExtList")) then
strExtList = ""
else
strExtList = Replace(Request.form("ExtList"), "'", "''") 
end if 



if isnull(Request.form("NameLocPortal")) then
strNameLocPortal = ""
else
strNameLocPortal = Replace(Request.form("NameLocPortal"), "'", "''") 
end if 





If Request.form("SelfRescue") = "ON" Then
strSelfRescue= -1
else
strSelfRescue = 0
end if

If Request.form("NonEntryRescue") = "ON" Then
strNonEntryRescue= -1
else
strNonEntryRescue = 0
end if

If Request.form("TeamRescue") = "ON" Then
strTeamRescue= -1
else
strTeamRescue = 0
end if

If Request.form("harnessAndRetrieval") = "ON" Then
strharnessAndRetrieval= -1
else
strharnessAndRetrieval = 0
end if

If Request.form("MechanicalDevice") = "ON" Then
strMechanicalDevice= -1
else
strMechanicalDevice = 0
end if

If Request.form("exhazTraffic") = "ON" Then
strexhazTraffic= -1
else
strexhazTraffic = 0
end if

If Request.form("exhazFallFromAbove") = "ON" Then
strexhazFallFromAbove= -1
else
strexhazFallFromAbove = 0
end if

If Request.form("exhazFallBelow") = "ON" Then
strexhazFallBelow = -1
else
strexhazFallBelow = 0
end if



If Request.form("exhazHoists") = "ON" Then
strexhazHoists= -1
else
strexhazHoists = 0
end if

If Request.form("exhazCatwalk") = "ON" Then
strexhazCatwalk= -1
else
strexhazCatwalk = 0
end if




If Request.form("BodyHarnessWithLine") = "ON" Then
strBodyHarnessWithLine= -1
else
strBodyHarnessWithLine = 0
end if





If Request.form("airTestHere") = "Yes" then
strairTestHere = -1
else
strairTestHere= 0
end if


If Request.form("IssueWithAE") = "Yes" then
strIssueWithAE = -1
else
strIssueWithAE= 0
end if


If Request.form("SCBAInPortal") = "Yes" then
strSCBAInPortal = -1
else
strSCBAInPortal= 0
end if

strAccessEgress = Request.form("AccessEgress")

strATTech = Request.form("ATTech")

strsql = "Update tblPortals set  AEAddInfo  = '" & strAEAddInfo & "', Other_ae  = '" & strOther_ae & "', "_
&" fallAboveControl  = '" & strfallAboveControl  & "', NoNonEntry  = '" & strNoNonEntry & "', RescueEquip1  = '" & strRescueEquip1  & "', RescueEquip2 = '" & strRescueEquip2  & "', "_
&" hoistOobstructControl  = '" & strhoistOobstructControl  & "', catwalkControls  = '" & strcatwalkControls  & "', "_
&" OtherExhaz  = '" & strOtherExhaz  & "', ExtList  = '" & strExtList  & "',"_
&" SelfRescue = " & strSelfRescue  & ",  NonEntryRescue = " & strNonEntryRescue & ", TeamRescue = " & strTeamRescue  & ", harnessAndRetrieval = " & strharnessAndRetrieval  & ","_
&" MechanicalDevice = " & strMechanicalDevice  & ", exhazTraffic = " & strexhazTraffic  & ", exhazFallFromAbove = " & strexhazFallFromAbove  & ","_
&" exhazHoists = " & strexhazHoists  & ", exhazCatwalk = " & strexhazCatwalk  & ",  "_
&" BodyHarnessWithLine = " & strBodyHarnessWithLine  & ", NameLocPortal = '" & strNameLocPortal & "',   "_
&" AccessEgress = '" & strAccessEgress & "',  Attech = '" & strATTech & "',"_
&" exhazFallBelow = " & strexhazFallBelow & ",  SelfRescueControls = '" & strSelfRescueControls & "', "_
&" airTestHere = " & strairTestHere & ", IssueWithAE = " & strIssueWithAE & ", SCBAInPortal = " & strSCBAInPortal & " where id = " & strpid

Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			MyConn.Close 
			
			if strHAStatus = "Approved"   or  strHAStatus = "APPROVED" then
			strsql = "Update tblHA set HA_Status = 'Change Proposal', HA_Approver_1 = Null, HA_Approver_2 = Null where Spaceid = '" & strid & "'"
			Set MyConn = Server.CreateObject("ADODB.Connection")
			MyConn.Open Session("ConnectionString")
			MyConn.Execute strSQL
			Myconn.close
			end if
			
	If strHAStatus = "Approved" or strHAStatus = "APPROVED" or strHAStatus = "CHANGE PROPOSAL" or strHAStatus = "Change Proposal" then
			
			Response.redirect("Change_log_Portal.asp?id=" & strid & "&cn=" & strNameLocPortal & "&pid=" & strpid)
		
			end if
End Function			
			
 %><!--#include file="footer.inc"-->